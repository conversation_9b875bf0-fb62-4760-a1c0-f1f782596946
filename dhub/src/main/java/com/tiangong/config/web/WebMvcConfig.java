package com.tiangong.config.web;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Bean
    public RequestValidateIntercept requestValidateIntercept() {
        return new RequestValidateIntercept();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(requestValidateIntercept())
                .excludePathPatterns("/getSignature")
                .excludePathPatterns("/health")
                .excludePathPatterns("/dhub/orderStatusPush")
                .excludePathPatterns("/test/orderStatusPush")
                .excludePathPatterns("/dhub/checkoutApplyStatusPush")
                .excludePathPatterns("/test/checkoutApplyStatusPush")
                .excludePathPatterns("/dhub/pushInvoiceStatusNotify")
                .excludePathPatterns("/dhub/orderDeductionPush")
                .excludePathPatterns("/encode") //放行加密测试接口
                .excludePathPatterns("/decode") //放行解密测试接口
                .addPathPatterns("/**");
    }

    @Override
    public void configureAsyncSupport(final AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(5000L);
        configurer.registerCallableInterceptors(timeoutInterceptor());
    }


    @Bean
    public TimeoutCallableProcessingInterceptor timeoutInterceptor() {
        return new TimeoutCallableProcessingInterceptor();
    }
}