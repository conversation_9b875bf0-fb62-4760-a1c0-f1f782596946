package com.tiangong.config.exception;

import com.alibaba.fastjson.JSON;
import com.netflix.client.ClientException;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.entity.base.Request;
import com.tiangong.entity.base.ResponseResult;
import com.tiangong.exception.ParameterException;
import com.tiangong.util.SlsLoggerUtil;
import com.tiangong.util.StrUtilX;
import com.tiangong.utils.SystemUtil;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class ModelGlobalExceptionHandler {

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @ExceptionHandler(CustomException.class)
    public ResponseResult customException(CustomException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("request", requestBody);
        ResponseResult responseResult = new ResponseResult(ex.getCode(), ex.getMsg());
        map.put("source", "dhub");
        map.put("response", JSON.toJSONString(responseResult));
        slsLoggerUtil.saveLog(map, "dhub", "dhub");

        return responseResult;
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseResult globalException(RuntimeException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        log.error("runtimeException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        try {
            CustomException exception = (CustomException) ex;
            return new ResponseResult(exception.getCode(), exception.getMsg());
        } catch (Exception e) {
            return new ResponseResult(DhubReturnCodeEnum.OTHER.no, DhubReturnCodeEnum.OTHER.code);
        }
    }

    @ExceptionHandler(ClientException.class)
    public ResponseResult customException(ClientException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("request", requestBody);
        map.put("source", "dhub");
        map.put("response", JSON.toJSONString(DhubReturnCodeEnum.OTHER.no));
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult(DhubReturnCodeEnum.OTHER.no, DhubReturnCodeEnum.OTHER.code);
    }

    @ExceptionHandler(Exception.class)
    public ResponseResult exception(Exception ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        log.error("exception:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult(DhubReturnCodeEnum.OTHER.no, DhubReturnCodeEnum.OTHER.code);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseResult httpMessageNotReadableException(HttpMessageNotReadableException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        log.error("exception:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseResult illegalArgumentException(IllegalArgumentException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        log.error("IllegalArgumentException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult("500", ex.toString() + "---" + StrUtilX.getStackTraceAsString(ex));
    }

    @ExceptionHandler(ParameterException.class)
    public ResponseResult illegalArgumentException(ParameterException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        log.error("IllegalArgumentException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult("500", "数据格式异常----" + ex.toString() + "---" + StrUtilX.getStackTraceAsString(ex));
    }


    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResponseResult validException(MethodArgumentNotValidException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        log.error("MethodArgumentNotValidException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult(DhubReturnCodeEnum.INVALID_INPUTPARAM.no,Objects.requireNonNull(ex.getBindingResult().getFieldError()).getDefaultMessage());
    }

    @ExceptionHandler(value = RetryableException.class)
    public ResponseResult validException(RetryableException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult(DhubReturnCodeEnum.CONNECT_TIME_OUT.no, DhubReturnCodeEnum.CONNECT_TIME_OUT.code);
    }

    @ExceptionHandler(value = TimeoutException.class)
    public ResponseResult validException(TimeoutException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult(DhubReturnCodeEnum.CONNECT_TIME_OUT.no, DhubReturnCodeEnum.CONNECT_TIME_OUT.code);
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    public ResponseResult validException(ConstraintViolationException ex) {
        String requestBody = SystemUtil.getRequestBody(httpServletRequest);
        Request request = JSON.parseObject(requestBody, Request.class);
        String requestType = request.getHeader().getRequestType();

        log.error("MethodArgumentNotValidException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", requestType);
        map.put("errorMsg", StrUtilX.getStackTraceAsString(ex));
        map.put("source", "dhub");
        map.put("request", requestBody);
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "dhub", "dhub");
        return new ResponseResult(DhubReturnCodeEnum.INVALID_INPUTPARAM.no,
                ex.getConstraintViolations().stream()
                        .map(ConstraintViolation::getMessageTemplate)
                        .map(String::toString).distinct()
                        .collect(Collectors.joining(",")));
    }
}
