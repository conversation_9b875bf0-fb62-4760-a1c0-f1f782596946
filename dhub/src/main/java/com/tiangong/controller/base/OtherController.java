package com.tiangong.controller.base;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tiangong.annotations.SlsLog;
import com.tiangong.common.Response;
import com.tiangong.config.agentConfig.AgentConfig;
import com.tiangong.config.annotations.AccessLimitRequestMapping;
import com.tiangong.config.exception.CustomException;
import com.tiangong.controller.common.BaseController;
import com.tiangong.dis.dto.InvoiceNotifyPushRequest;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.entity.base.AgentAccountConfig;
import com.tiangong.entity.base.Header;
import com.tiangong.entity.base.Request;
import com.tiangong.entity.base.ResponseResult;
import com.tiangong.entity.request.order.CreateInvoiceInfoRequest;
import com.tiangong.entity.request.order.QueryCreditBalanceInfoRequest;
import com.tiangong.entity.response.order.CreateInvoiceInfoResponse;
import com.tiangong.entity.response.order.CreditBalanceInfoResponse;
import com.tiangong.enums.ApiNameEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.InvoiceErrorCodeEnum;
import com.tiangong.service.base.OtherService;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.Md5Util;
import com.tiangong.utils.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Service
@RestController
@RequiredArgsConstructor
@Slf4j
public class OtherController extends BaseController {

    @Autowired
    private OtherService otherService;

    /**
     * 信用额度查询接口
     */
    @AccessLimitRequestMapping("queryCreditBalance")
    @SlsLog(level = "info", name = "客户信用额度", message = "queryCreditBalance", topic = "queryCreditBalance", source = "dhub")
    public ResponseResult<CreditBalanceInfoResponse> queryCreditBalance(@RequestBody @Valid Request<QueryCreditBalanceInfoRequest> request) {
        return orderService(otherService::queryCreditBalance, request, ApiNameEnum.queryCreditBalance);
    }

    /**
     * 订单开票接口
     */
    @AccessLimitRequestMapping("createInvoice")
    @SlsLog(level = "info", name = "订单开票", message = "createInvoice", topic = "createInvoice", source = "dhub")
    public ResponseResult<CreateInvoiceInfoResponse> createInvoice(@RequestBody Request<CreateInvoiceInfoRequest> request) {
        //字段校验
        CommonUtil.valid(request.getBusinessRequest(), errorCode -> {
            log.warn("订单发票接口参数校验失败,请求参数:{}", JSONUtil.toJsonStr(request));
            DhubReturnCodeEnum errorEnum = DhubReturnCodeEnum.getEnumByErrorCode(errorCode);
            if (errorEnum == null) {
                throw new CustomException(DhubReturnCodeEnum.SYSTEM_EXCEPTION.getNo(), DhubReturnCodeEnum.SYSTEM_EXCEPTION.getCode(), errorCode);
            }
            throw new CustomException(errorEnum.getNo(), errorEnum.getCode(), errorEnum.getCode());
        });
        return orderService(otherService::createInvoice, request, ApiNameEnum.createInvoice);
    }

    /**
     * 订单状态推送接口
     */
    @PostMapping("dhub/pushInvoiceStatusNotify")
    @SlsLog(level = "info", name = "订单开票状态推送接口", message = "pushInvoiceStatus", topic = "pushInvoiceStatus", source = "dhub")
    public Response<Boolean> pushInvoiceStatusNotify(@RequestBody InvoiceNotifyPushRequest request) {
        return otherService.pushInvoiceStatusNotify(request);
    }

    /**
     * 测试
     */
    @RequestMapping("getSignature")
    public Map getSignature(@RequestBody Header header) throws Exception {
        HashMap<String, Object> returnMap = new HashMap<>();
        AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(header.getPartnerCode());
        if (agentAccountConfig == null) {
            returnMap.put("code", DhubReturnCodeEnum.INVALID_PARTNER.no);
            returnMap.put("timestamp", DhubReturnCodeEnum.INVALID_PARTNER.code);
            return returnMap;
        }
        String secretkey = agentAccountConfig.getSecretkey();
        StringBuffer sb = new StringBuffer();
        long timestamp = System.currentTimeMillis();
        sb.append(timestamp).append(header.getPartnerCode()).append(Md5Util.md5Encode(secretkey).toUpperCase()).append(header.getRequestType());
        String signature = Md5Util.md5Encode(sb.toString()).toUpperCase();

        returnMap.put("signature", signature);
        returnMap.put("timestamp", timestamp);
        return returnMap;
    }
}
