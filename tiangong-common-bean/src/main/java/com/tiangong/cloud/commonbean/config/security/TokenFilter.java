package com.tiangong.cloud.commonbean.config.security;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.utils.WebFrameworkUtilX;
import com.tiangong.keys.RedisKey;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.util.IpUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * token过滤器
 */
@Slf4j
@Component
public class TokenFilter extends GenericFilterBean {

    private final TokenManager tokenManager;

    private final StringRedisTemplate redisTemplate;

    TokenFilter(TokenManager tokenManager) {
        this.tokenManager = tokenManager;
        redisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        String token = resolveToken(httpServletRequest);

        if (StrUtilX.isNotEmpty(token)) {
            // 获取登录用户
            LoginUser loginUser = TokenManager.getUserNotThrow(httpServletRequest);
            if (loginUser == null || Boolean.FALSE.equals(redisTemplate.hasKey(RedisKey.LOGIN_TOKEN + token))) {
                //添加日志
                OperateLogReqDTO newDO = new OperateLogReqDTO();
                newDO.setCreatedDt(LocalDateTime.now());
                newDO.setLogName("超时登出");
                if (loginUser != null) {
                    newDO.setLogName(newDO.getLogName() + loginUser.getUserAccount());
                }
                newDO.setLogType("info");
                newDO.setLogLevel(0);
                newDO.setOperationType(16);
                newDO.setOperationResult(1);
                newDO.setApplicationName("tiangong-common-server");
                newDO.setRequestMethod(httpServletRequest.getMethod());
                newDO.setRequestIp(IpUtil.getIpAddress(httpServletRequest));
                newDO.setRequestHeader(httpServletRequest.getHeader("User-Agent"));
                newDO.setRequestUrl("/common/timeoutLogOut");
                if (loginUser != null) {
                    newDO.setCreatedBy(loginUser.getFullUserName());
                    newDO.setUserAccount(loginUser.getUserAccount());
                }
                redisTemplate.opsForValue().set("loginTimeOut", JSON.toJSONString(newDO));

                //日志结束
                expiredToken(response);
                return;
            }

            Authentication authentication = tokenManager.getAuthentication(token);
            // 将token保存到SecurityContext对象里
            SecurityContext context = SecurityContextHolder.getContext();
            context.setAuthentication(authentication);

            // 将登录用户保存到上下文中
            WebFrameworkUtilX.setLoginUser(httpServletRequest,loginUser);

        }
        filterChain.doFilter(request, response);
    }

    /**
     * token过期或者无效，直接返回 90001
     */
    private void expiredToken(ServletResponse response) throws IOException {
        JSONObject jsonObject = new JSONObject();
        HttpServletResponse resp = (HttpServletResponse) response;
        resp.setContentType("application/json;charset=utf-8" );
        // 添加cors返回请求头,不然会有cors跨域问题
        //resp.setHeader("Access-Control-Allow-Origin" , header);
        PrintWriter writer = response.getWriter();
        jsonObject.put("result" , 0);
        jsonObject.put("failCode" , ResultEnum.E_9001.getCode());
        jsonObject.put("failReason" , ResultEnum.E_9001.getMessage());
        String result = jsonObject.toString();
        writer.write(result);
        writer.close();
    }


    private String resolveToken(HttpServletRequest request) {
        return request.getHeader(HttpConstant.HEADER);
    }
}
