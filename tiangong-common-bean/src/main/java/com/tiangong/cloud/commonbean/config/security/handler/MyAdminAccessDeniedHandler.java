package com.tiangong.cloud.commonbean.config.security.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.common.Response;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 自定义权限校验处理器
 */
@Component
public class MyAdminAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request,
                       HttpServletResponse response,
                       AccessDeniedException accessDeniedException) throws IOException {

        response.setContentType("application/json;charset=UTF-8");
        PrintWriter out = response.getWriter();
        Response<Object> resp = Response.error(ResultEnum.E_9003.getCode(), ResultEnum.E_9003.getMessage());
        out.write(new ObjectMapper().writeValueAsString(resp));
        out.flush();
        out.close();
    }
}
