package com.tiangong.cloud.commonbean.config.security.auth;


import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.commonbean.config.security.SecurityProperties;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.StrUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Service(value = "syyo")
public class SyyoPermissionConfig {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private SecurityProperties securityProperties;

    public Boolean check(String... permissions) {
        // 关闭 Security 的校验,直接放行
        if (!securityProperties.getSecuritySwitch()) {
            return true;
        }

        String token = request.getHeader(HttpConstant.HEADER);
        // 没有传token，或者格式不对，直接返回没有权限
        if (StrUtilX.isEmpty(token)) {
            return false;
        }

        //校验token是否过期
        String result = RedisTemplateX.get(RedisKey.LOGIN_TOKEN + token);
        if (result == null){
            return false;
        }

//        ValueOperations<String, String> redis = redisTemplate.opsForValue();
//        String roles = redis.get(token);
        // redis没有存储role，返回没有权限
//        if (StrUtilX.isEmpty(roles)) {
//            return false;
//        }

        boolean b = true;
//        if (!roles.contains("admin" )) {
//            for (String permission : permissions) {
//                b = roles.contains(permission);
//            }
//        }

//         获取当前用户的所有权限
//        List<String> elPermissions = SecurityUtils.getUserDetails().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
//        boolean b  = true;
//        //判断当前用户是否是admin，admin有所有权限
//        if (!elPermissions.contains("admin")) {
//            //判断当前请求的权限标识是否在当前用户的所有权限中
//            //permissions集合里的值如果在elPermissions集合里只要包含一个，就返回true
//            b = Arrays.stream(permissions).anyMatch(elPermissions::contains);
//        }
        return b;
    }
}
