package com.tiangong.cloud.commonbean.config.security.handler;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.common.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 自定义认证处理器
 */
@Slf4j
@Component
public class MyAdminAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException authException) throws IOException {

        response.setContentType("application/json;charset=UTF-8");
        PrintWriter out = response.getWriter();

        Response<Object> resp = Response.error(ResultEnum.E_9001.getCode(), ResultEnum.E_9001.getMessage());
        log.error("权限校验失败，resp={}", JSONUtil.toJsonStr(resp));
        out.write(new ObjectMapper().writeValueAsString(resp));
        out.flush();
        out.close();
    }
}
