package com.tiangong.cloud.commonbean.config.page;

import com.baomidou.mybatisplus.extension.parsers.DynamicTableNameParser;
import com.baomidou.mybatisplus.extension.parsers.ITableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Collections;
import java.util.HashMap;


@Configuration
@Slf4j
public class MybatisPlusConfig {

    public static ThreadLocal<String> dynamicTableNameSuffix = new ThreadLocal<>();

//
//    @Autowired
//    private DataSource dataSource;
//
//    @Autowired
//    private MybatisProperties properties;
//
//    @Autowired
//    private ResourceLoader resourceLoader = new DefaultResourceLoader();
//
//    @Autowired(required = false)
//    private Interceptor[] interceptors;
//
//    @Autowired(required = false)
//    private DatabaseIdProvider databaseIdProvider;

    /**
     * 注册分页插件
     */
    @Bean
    public PaginationInterceptor mybatisPlusInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setSqlParserList(Collections.singletonList(dynamicTableNameParser()));
        return paginationInterceptor;
    }

    @Bean
    @Primary
    public MySqlInjector mySqlInjector() {
        return new MySqlInjector();
    }

    @Bean
    public DynamicTableNameParser dynamicTableNameParser() {
        DynamicTableNameParser dynamicTableNameParser = new DynamicTableNameParser();
        dynamicTableNameParser.setTableNameHandlerMap(new HashMap<String, ITableNameHandler>(3) {{
            put("f_supplier_import_statement_annex", (metaObject, sql, tableName) -> {
                String suffix = dynamicTableNameSuffix.get();
                if (StrUtilX.isNotEmpty(suffix)) {
                    String trueTableName = tableName + "_" + suffix;
                    dynamicTableNameSuffix.remove();
                    return trueTableName;
                } else {
                    dynamicTableNameSuffix.remove();
                    return tableName;
                }
            });
            put("f_supply_auto_reconciliation", (metaObject, sql, tableName) -> {
                String suffix = dynamicTableNameSuffix.get();
                if (StrUtilX.isNotEmpty(suffix)) {
                    String trueTableName = tableName + "_" + suffix;
                    dynamicTableNameSuffix.remove();
                    return trueTableName;
                } else {
                    dynamicTableNameSuffix.remove();
                    return tableName;
                }
            });
        }});
        return dynamicTableNameParser;
    }

    /**
     * 这里全部使用mybatis-autoconfigure 已经自动加载的资源。不手动指定
     * 配置文件和mybatis-boot的配置文件同步
     * @return
     */
//    @Bean
//    public MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean() {
//        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
//        mybatisPlus.setDataSource(dataSource);
//        mybatisPlus.setVfs(SpringBootVFS.class);
//        if (StringUtils.hasText(this.properties.getConfigLocation())) {
//            mybatisPlus.setConfigLocation(this.resourceLoader.getResource(this.properties.getConfigLocation()));
//        }
//        if (!ObjectUtils.isEmpty(this.interceptors)) {
//            mybatisPlus.setPlugins(this.interceptors);
//        }
//        MybatisConfiguration mc = new MybatisConfiguration();
//        mc.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
//        //数据库字段设计为驼峰命名，默认开启的驼峰转下划线会报错字段找不到
//        mc.setMapUnderscoreToCamelCase(false);
//        mybatisPlus.setConfiguration(mc);
//        if (this.databaseIdProvider != null) {
//            mybatisPlus.setDatabaseIdProvider(this.databaseIdProvider);
//        }
//        if (StringUtils.hasLength(this.properties.getTypeAliasesPackage())) {
//            mybatisPlus.setTypeAliasesPackage(this.properties.getTypeAliasesPackage());
//        }
//        if (StringUtils.hasLength(this.properties.getTypeHandlersPackage())) {
//            mybatisPlus.setTypeHandlersPackage(this.properties.getTypeHandlersPackage());
//        }
//        if (!ObjectUtils.isEmpty(this.properties.resolveMapperLocations())) {
//            mybatisPlus.setMapperLocations(this.properties.resolveMapperLocations());
//        }
//        return mybatisPlus;
//    }
}
