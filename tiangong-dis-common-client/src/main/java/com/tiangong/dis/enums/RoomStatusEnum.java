package com.tiangong.dis.enums;

/**
 * <AUTHOR>
 */
public enum RoomStatusEnum {
    /**
     * 有房
     */
    EXIST(1, "有房"),
    STAY(2, "待查"),
    FULL(3, "满房");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    RoomStatusEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

}
