package com.tiangong.dis.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Document("mb_hotel_info")
public class HotelInfoDB {

    @Id
    private Long id;

    private String hotelName;
    /**
     * 对应的是com.tiangong.dis.entity.vo.hotel.HotelInfo对象
     */
    private String hotelInfo;

    private Date time;
}
