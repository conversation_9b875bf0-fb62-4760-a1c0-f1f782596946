package com.tiangong.dis.entity.vo.hotel;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RoomInfo {

    /**
     * 房型id
     */
    private Long roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 房型英文名称
     */
    private String roomEngName;

    /**
     * 房间面积
     */
    private String roomAcreage;

    /**
     * 房间楼层
     */
    private String roomFloor;

    /**
     * 最多入住人
     */
    private Integer maxPerson;

    /**
     * 最多入住儿童数
     */
    private Integer maxChild;

    /**
     * 宽带
     */
    private Integer broadNet = 4;

    /**
     * 是否可加床
     */
    private Integer addBedflag;

    /**
     * 加床费用
     */
    private String addBedFee;

//    /** 床型列表 */
//    private List<BedType> bedTypes;

    /**
     * 床型
     */
    private String bedTypes;

    /**
     * 房型设施列表 CTRIP 5.0 增加
     */
    private List<RoomFacilityNewResponseDto> roomFacilityNew;

    /**
     * 主图url
     */
    private String mainUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 窗型:0-无窗，1-有窗，2-部分有窗,为空时窗型未知
     **/
    private Integer windowType;

    /**
     * Ctrip5.0新加
     */
    private Integer windowDetail;

    /**
     * 无线宽带 0：没有 1：全部房间有且收费  2：全部房间有且免费   3：部分房间有且收费  4：部分房间有且免费
     */
    private Integer wirelessBroadnet = 0;

    /**
     * 有线宽带 0：没有1：全部房间有且收费  2：全部房间有且免费  3：部分房间有且收费  4：部分房间有且免费
     */
    private Integer wiredBroadnet = 0;

    /**
     * 是否可吸烟 1：是  2：否  3：部分
     */
    private Integer isAllowSmoking;

    /**
     * Ctrip5.0版本床型列表
     */
    private BedTypesDetails bedTypeDetails;

    /**
     * 床型关系:
     * 1表示 床型A与床型B 同时存在
     * 0表示 或者是床型A 或者是床型B
     */
    private String withOr;


    /**
     * 单房/多室
     * 0- 单个房间设置
     * 1- 多室多厅设置
     */
    private Integer oneOrMore;

    /**
     * 房间类型
     * 0-卧室 1-客厅 2-卫生间
     */
    private Integer type;

    /**
     * 卫生间类型
     * 0-私人 1-公共
     */
    private Integer toiletType;

    /**
     * 卫生间位置
     */
    private Integer toiletLocation;
}
