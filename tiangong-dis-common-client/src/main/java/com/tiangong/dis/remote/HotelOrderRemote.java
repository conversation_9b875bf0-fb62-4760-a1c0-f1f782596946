package com.tiangong.dis.remote;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.QueryOrderInvoiceRequest;
import com.tiangong.dis.dto.QueryOrderInvoiceResponse;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.*;
import com.tiangong.dto.order.response.*;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@FeignClient("tiangong-dis-common-server")
public interface HotelOrderRemote {

    /**
     * 试预订(可订检查)接口
     *
     * @param request 检查预订请求，包含预订检查参数
     * @return 返回检查预订响应结果
     */
    @RequestMapping("/discommon/order/checkBooking")
    ResponseResult<CheckBookingResponse> checkBooking(CheckBookingRequest request);

    /**
     * 创建订单
     *
     * @param request 创建订单请求，包含订单创建信息
     * @return 返回创建订单响应结果
     */
    @RequestMapping("/discommon/order/createOrder")
    ResponseResult<CreateOrderResponse> createOrder(CreateOrderRequest request);

    /**
     * 取消订单
     *
     * @param request 取消订单请求，包含订单取消信息
     * @return 返回取消订单响应结果
     */
    @RequestMapping("/discommon/order/cancelOrder")
    ResponseResult<CancelOrderResponse> cancelOrder(CancelOrderRequest request);

    /**
     * 查询订单列表
     *
     * @param request 查询订单列表DTO，包含查询条件
     * @return 返回分页的订单简单DTO数据响应结果
     */
    @PostMapping("/discommon/order/queryOrderList")
    Response<PaginationSupportDTO<OrderSimpleDTO>> queryOrderList(@RequestBody QueryOrderListDTO request);

    /**
     * 订单详情查询-API
     *
     * @param request 订单详情请求，包含订单查询参数
     * @return 返回订单详情响应结果
     */
    @RequestMapping("/discommon/order/queryAPIOrderDetail")
    ResponseResult<OrderDetailResponse> queryAPIOrderDetail(OrderDetailRequest request);

    /**
     * 订单详情查询-B2B
     *
     * @param request 订单详情请求，包含B2B订单查询参数
     * @return 返回订单DTO数据
     */
    @RequestMapping("/discommon/order/queryB2BOrderDetail")
    OrderDTO queryB2BOrderDetail(OrderDetailRequest request);

    /**
     * 订单操作推送接口
     *
     * @param request 订单状态推送请求，包含订单状态变更信息
     * @return 返回推送操作的响应结果
     */
    @RequestMapping("/discommon/order/orderStatusPush")
    Response orderStatusPush(OrderStatusPushRequest request);

    /**
     * 订单支付接口
     *
     * @param request 支付订单请求，包含支付信息
     * @return 返回支付订单响应结果
     */
    @RequestMapping("/discommon/order/payOrder")
    ResponseResult<PayOrderResponse> payOrder(PayOrderRequest request);

    /**
     * 订单退款通知接口
     *
     * @param request 订单退款通知请求，包含退款通知信息
     * @return 返回订单退款通知响应结果
     */
    @RequestMapping("/discommon/order/refundNoticeOrder")
    ResponseResult<OrderRefundNoticeResponse> refundNoticeOrder(OrderRefundNoticeRequest request);

    /**
     * 信用额度查询接口
     *
     * @param request 查询信用余额请求，包含查询参数
     * @return 返回信用余额响应结果
     */
    @RequestMapping("/discommon/order/queryCreditBalance")
    ResponseResult<CreditBalanceResponse> queryCreditBalance(QueryCreditBalanceRequest request);

    /**
     * 订单开票接口
     *
     * @param request 创建发票请求，包含开票信息
     * @return 返回创建发票响应结果
     */
    @RequestMapping("/discommon/order/createInvoice")
    ResponseResult<CreateInvoiceResponse> createInvoice(CreateInvoiceRequest request);

    /**
     * 根据订单号查询发票详情
     *
     * @param request 查询订单发票请求，包含订单号等查询条件
     * @return 返回查询订单发票响应结果
     */
    @PostMapping("/discommon/orderInvoice/query")
    ResponseResult<QueryOrderInvoiceResponse> queryOrderInvoice(@RequestBody QueryOrderInvoiceRequest request);

    /**
     * 订单入住详细情况查询接口
     *
     * @param request 订单入住信息请求，包含入住详情查询参数
     * @return 返回订单入住详细信息响应结果
     */
    @RequestMapping("/discommon/order/queryOrderCheckDetailInfo")
    ResponseResult<OrderCheckDetailInfoResponse> queryOrderCheckDetailInfo(OrderCheckInfoRequest request);


    /**
     * 订单入住推送接口
     *
     * @param orderCheckDetailInfoRequest 订单入住详细信息请求，包含入住推送信息
     * @return 返回推送操作的响应结果
     */
    @RequestMapping("/discommon/order/orderCheckDetailsNotify")
    Response orderCheckDetailsNotify(OrderCheckDetailInfoRequest orderCheckDetailInfoRequest);

    /**
     * 查询订单列表统计-B2B
     *
     * @param request 查询订单统计DTO B2B请求，包含统计查询条件
     * @return 返回订单统计数据的响应结果
     */
    @PostMapping("/discommon/order/queryOrderStatisticsB2B")
    Response queryOrderStatisticsB2B(@RequestBody QueryOrderStatisticsDTOB2BRequest request);

    /**
     * 退房申请接口
     *
     * @param request
     * @return
     */
    @PostMapping("/discommon/order/checkoutApply")
    ResponseResult<CheckoutApplyInfoResponse> checkoutApply(@RequestBody CheckoutApplyInfoRequest request);

    /**
     * 退房详情查询接口
     *
     * @param request
     * @return
     */
    @PostMapping("/discommon/order/queryCheckoutDetail")
    ResponseResult<CheckoutDetailInfoResponse> queryCheckoutDetail(@RequestBody CheckoutDetailInfoRequest request);


}
