package com.tiangong.dis.remote;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.product.request.AvailableHotelRequest;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@FeignClient("tiangong-dis-common-server")
public interface HotelProductRemote {

    /**
     * 查询可售酒店id列表信息
     * @param request
     * @return
     */
    @RequestMapping("/discommon/product/queryAvailableHotelList")
    List<String> queryAvailableHotelList(AvailableHotelRequest request);

    /**
     * 酒店每日起价查询-Dhub
     */
    @RequestMapping("/discommon/product/queryHotelLowestPrice")
    ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店列表查询接口
     */
    @RequestMapping("/discommon/product/queryHotelIdList")
    ResponseResult<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request);

    /**
     * 酒店实时产品查询
     */
    @RequestMapping("/discommon/product/queryProductDetail")
    ResponseResult<ProductDetailResponse> queryProductDetail(ProductDetailRequest request);

    /**
     * 获取酒店详情，按需输出
     */
    @PostMapping(value = "/discommon/product/queryHotelInfo", produces = {"application/json;charset=UTF-8"})
    Response<HotelInfoCollectionDTO> queryHotelInfo(@RequestBody HotelInfoCollectionReq req);

    /**
     * 获取酒店详情，按需输出
     */
    @PostMapping(value = "/discommon/product/queryHotelInfoList", produces = {"application/json;charset=UTF-8"})
    Response<String> queryHotelInfoList(@RequestBody HotelInfoReq req);

    /**
     * 酒店实时起价查询接口_B2B
     */
    @RequestMapping("/discommon/product/hotelLowestPrice")
    ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(@RequestBody HotelLowestPriceRequest request);

    /**
     * 获取酒店标签信息
     */
    @PostMapping(value = "/discommon/product/queryHotelLabelInfo", produces = {"application/json;charset=UTF-8"})
    Response<Set<String>> queryHotelLabelInfo(@RequestBody HotelInfoReq req);

    /**
     * 获取目的地接口
     */
    @PostMapping("/discommon/product/getAddress")
    Response<List<AddressRespDTO>> getAddress(@RequestBody Map<String, String> paramMap);

    /**
     * 获取酒店过滤器
     */
    @PostMapping("/discommon/product/getHotelSearch")
    Response<HotelSearchResp> getHotelSearch(@RequestBody HotelSearchReq hotelSearchReq);

    /**
     * 查询酒店列表
     */
    @PostMapping("/discommon/product/findHotelList")
    Response<PaginationSupportDTO<HotelListResp>> findHotelList(@RequestBody HotelPageReq req);
}
