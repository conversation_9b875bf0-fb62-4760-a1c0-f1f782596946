package com.tiangong.dis.remote;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.product.request.AvailableHotelRequest;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@FeignClient("tiangong-dis-common-server")
public interface HotelProductRemote {

    /**
     * 查询可售酒店id列表信息
     * @param request
     * @return
     */
    @RequestMapping("/discommon/product/queryAvailableHotelList")
    List<String> queryAvailableHotelList(AvailableHotelRequest request);

    /**
     * 酒店每日起价查询-Dhub
     *
     * @param request 酒店最低价格请求，包含酒店起价查询参数
     * @return 返回酒店最低价格响应结果
     */
    @RequestMapping("/discommon/product/queryHotelLowestPrice")
    ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店列表查询接口
     *
     * @param request 酒店ID列表请求，包含酒店ID查询参数
     * @return 返回酒店ID列表响应结果
     */
    @RequestMapping("/discommon/product/queryHotelIdList")
    ResponseResult<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request);

    /**
     * 酒店实时产品查询
     *
     * @param request 产品详情请求，包含酒店产品查询参数
     * @return 返回产品详情响应结果
     */
    @RequestMapping("/discommon/product/queryProductDetail")
    ResponseResult<ProductDetailResponse> queryProductDetail(ProductDetailRequest request);

    /**
     * 获取酒店详情，按需输出
     *
     * @param req 酒店信息收集请求，包含酒店详情查询参数
     * @return 返回酒店信息收集DTO的响应结果
     */
    @PostMapping(value = "/discommon/product/queryHotelInfo", produces = {"application/json;charset=UTF-8"})
    Response<HotelInfoCollectionDTO> queryHotelInfo(@RequestBody HotelInfoCollectionReq req);

    /**
     * 获取酒店详情列表，按需输出
     *
     * @param req 酒店信息请求，包含酒店详情列表查询参数
     * @return 返回酒店详情列表字符串的响应结果
     */
    @PostMapping(value = "/discommon/product/queryHotelInfoList", produces = {"application/json;charset=UTF-8"})
    Response<String> queryHotelInfoList(@RequestBody HotelInfoReq req);

    /**
     * 酒店实时起价查询接口_B2B
     *
     * @param request 酒店最低价格请求，包含B2B酒店起价查询参数
     * @return 返回酒店最低价格响应结果
     */
    @RequestMapping("/discommon/product/hotelLowestPrice")
    ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(@RequestBody HotelLowestPriceRequest request);

    /**
     * 获取酒店标签信息
     *
     * @param req 酒店信息请求，包含酒店标签查询参数
     * @return 返回酒店标签信息集合的响应结果
     */
    @PostMapping(value = "/discommon/product/queryHotelLabelInfo", produces = {"application/json;charset=UTF-8"})
    Response<Set<String>> queryHotelLabelInfo(@RequestBody HotelInfoReq req);

    /**
     * 获取目的地接口
     *
     * @param paramMap 参数映射，包含目的地查询参数
     * @return 返回地址响应DTO列表的响应结果
     */
    @PostMapping("/discommon/product/getAddress")
    Response<List<AddressRespDTO>> getAddress(@RequestBody Map<String, String> paramMap);

    /**
     * 获取酒店过滤器
     *
     * @param hotelSearchReq 酒店搜索请求，包含过滤器查询参数
     * @return 返回酒店搜索响应的响应结果
     */
    @PostMapping("/discommon/product/getHotelSearch")
    Response<HotelSearchResp> getHotelSearch(@RequestBody HotelSearchReq hotelSearchReq);

    /**
     * 查询酒店列表
     *
     * @param req 酒店分页请求，包含酒店列表查询和分页参数
     * @return 返回分页的酒店列表响应数据的响应结果
     */
    @PostMapping("/discommon/product/findHotelList")
    Response<PaginationSupportDTO<HotelListResp>> findHotelList(@RequestBody HotelPageReq req);
}
