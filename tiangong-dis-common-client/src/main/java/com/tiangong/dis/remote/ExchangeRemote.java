package com.tiangong.dis.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.exchange.ExchangeRateDTO;
import com.tiangong.dto.exchange.QueryExchangeReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "tiangong-dis-common-server")
public interface ExchangeRemote {

    /**
     * 查询汇率
     *
     * @param request 查询汇率请求，包含查询条件
     * @return 返回分页的汇率DTO数据响应结果
     */
    @PostMapping("/discommon/exchange/queryExchangeRate")
    Response<PaginationSupportDTO<ExchangeRateDTO>> queryExchangeRate(@RequestBody QueryExchangeReq request);
}
