package com.tiangong.dis.remote;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelLabelConfigReqDTO;
import com.tiangong.dis.dto.HotelLabelConfigRespDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description: 酒店标签远程调用
 * @author: qiu
 * @create: 2024-07-02 18:21
 */
@FeignClient(value = "tiangong-common-server")
public interface HotelLabelConfigRemote {

    /**
     * 酒店标签配置列表
     *
     * @param req 酒店标签配置请求DTO，包含查询条件
     * @return 返回酒店标签配置响应DTO列表的响应结果
     */
    @PostMapping(value = "/common/hotelLabelConfig/hotelLabelConfigList", produces = {"application/json;charset=UTF-8"})
    Response<List<HotelLabelConfigRespDTO>> hotelLabelConfigList(@RequestBody HotelLabelConfigReqDTO req);
}