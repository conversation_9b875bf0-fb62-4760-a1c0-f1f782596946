package com.tiangong.dis.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.SysConfigDTO;
import com.tiangong.dto.common.SysConfigReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "tiangong-dis-common-server")
public interface SysConfigRemote {

    @PostMapping("/discommon/sysConfig/querySysConfigByKey")
    Response<SysConfigDTO> querySysConfigByKey(@RequestBody SysConfigReq req);
}
