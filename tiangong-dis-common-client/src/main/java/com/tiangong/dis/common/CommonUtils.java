package com.tiangong.dis.common;

import cn.hutool.extra.spring.SpringUtil;
import com.github.pagehelper.PageInfo;
import com.tiangong.common.PageDto;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.exception.CustomException;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
public class CommonUtils {

    /**
     * 最大天数限制30天
     */
    private static final int MAX_DAYS_LIMIT_30 = 30;
    private static final int MAX_DAYS_LIMIT_90 = 90;
    private static final int MAX_ADVANCE_DAYS_545 = 545;
    private static final int MAX_ADVANCE_DAYS_575 = 575;
    private static final int MAX_ADVANCE_DAYS_635 = 635;

    /**
     * 异常堆栈信息最大长度
     */
    private static final int MAX_STACK_TRACE_LENGTH = 500;

    private static Executor defaultPool;

    static {
        defaultPool = SpringUtil.getBean("defaultPool", Executor.class);
    }
     private static final String KEY_AES = "AES";
     private static final String DEFAULT_CHARSET = "UTF-8";
     private static final String CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";

    /**
     * 校验日期
     * 入住日期、离店日期是否大于当前时间，离店日期是否大于入住日期
     * 入离日期是否大于30天
     */
    public static void checkDate30(String checkInDateStr, String checkOutDateStr) {
        Date checkInDate = DateUtilX.stringToDate(checkInDateStr);
        Date checkOutDate = DateUtilX.stringToDate(checkOutDateStr);
        checkDateValidity(checkInDate, checkOutDate);
        if (DateUtilX.getDay(checkInDate, checkOutDate) > MAX_DAYS_LIMIT_30) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_30.no, DhubReturnCodeEnum.DAYS_LIMITED_30.code, DhubReturnCodeEnum.DAYS_LIMITED_30.description);
        }
    }

    /**
     * 校验入住日期、离店日期是否大于当前时间，离店日期是否大于入住日期
     */
    public static void checkDateValidity(Date checkInDate, Date checkOutDate) {
//        if (DateUtil.compare(DateUtil.dateFormat(DateUtil.getCurrentDate(), DateUtil.defaultFormat), checkInDate) > 0) {
//            throw new CustomException(ReturnCodeEnum.INVALID_DATE.no, ReturnCodeEnum.INVALID_DATE.description);
//        }
        if (DateUtilX.compare(DateUtilX.dateFormat(DateUtilX.getCurrentDate(), DateUtilX.defaultFormat), checkOutDate) > 0) {
            throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.description);
        }
        if (DateUtilX.compare(checkInDate, checkOutDate) >= 0) {
            throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.description);
        }
    }

    public static void checkDateDefault(String checkInDateStr, String checkOutDateStr) {
        Date checkInDate = DateUtilX.stringToDate(checkInDateStr);
        Date checkOutDate = DateUtilX.stringToDate(checkOutDateStr);
        checkDateValidity(checkInDate, checkOutDate);
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkInDate) >= MAX_ADVANCE_DAYS_545) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_545.no, DhubReturnCodeEnum.DAYS_LIMITED_545.code, DhubReturnCodeEnum.DAYS_LIMITED_545.description);
        }
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkOutDate) >= MAX_ADVANCE_DAYS_575) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_575.no, DhubReturnCodeEnum.DAYS_LIMITED_575.code, DhubReturnCodeEnum.DAYS_LIMITED_575.description);
        }
        if (DateUtilX.getDay(checkInDate, checkOutDate) > MAX_DAYS_LIMIT_30) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_DIFFER_30.no, DhubReturnCodeEnum.DAYS_DIFFER_30.code, DhubReturnCodeEnum.DAYS_DIFFER_30.description);
        }
    }

    public static void checkDate90(String checkInDateStr, String checkOutDateStr) {
        Date checkInDate = DateUtilX.stringToDate(checkInDateStr);
        Date checkOutDate = DateUtilX.stringToDate(checkOutDateStr);
        checkDateValidity(checkInDate, checkOutDate);
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkInDate) >= MAX_ADVANCE_DAYS_545) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_545.no, DhubReturnCodeEnum.DAYS_LIMITED_545.code, DhubReturnCodeEnum.DAYS_LIMITED_545.description);
        }
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkOutDate) >= MAX_ADVANCE_DAYS_635) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_635.no, DhubReturnCodeEnum.DAYS_LIMITED_635.code, DhubReturnCodeEnum.DAYS_LIMITED_635.description);
        }
        if (DateUtilX.getDay(checkInDate, checkOutDate) > MAX_DAYS_LIMIT_90) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_DIFFER_90.no, DhubReturnCodeEnum.DAYS_DIFFER_90.code, DhubReturnCodeEnum.DAYS_DIFFER_90.description);
        }
    }

    /**
     * sha256WithRsa 签名
     *
     * @param content    要加签名的内容
     * @param privateKey 签名的私钥
     * @return 若签名失败则返回null
     */
    public static String rsa256Sign(String content, String privateKey) {
        try {
            String charset = "UTF-8";
            PrivateKey priKey = getPrivateKeyFromPKCS8("RSA", new ByteArrayInputStream(privateKey.getBytes()));

            if (priKey != null) {
                java.security.Signature signature = java.security.Signature.getInstance("SHA256WithRSA");
                signature.initSign(priKey);
                signature.update(content.getBytes(charset));

                byte[] signed = signature.sign();
                return new String(Base64.encodeBase64(signed));
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("sha256WithRsa签名,出现异常", e);
            return null;
        }
    }

    /**
     * sha256WithRsa 验签
     *
     * @param content   要验签的内容
     * @param sign      要验证的签名
     * @param publicKey 验签的公钥
     * @return 验签是否通过
     */
    public static boolean rsa256CheckSign(String content, String sign, String publicKey) {
        try {
            String charset = "UTF-8";
            PublicKey pubKey = getPublicKeyFromX509("RSA", new ByteArrayInputStream(publicKey.getBytes()));
            java.security.Signature signature = java.security.Signature.getInstance("SHA256WithRSA");

            signature.initVerify(pubKey);
            signature.update(content.getBytes(charset));

            boolean result = signature.verify(Base64.decodeBase64(sign.getBytes()));

            return result;
        } catch (Exception e) {
            log.error("sha256WithRsa校验签名,出现异常", e);
            return false;
        }
    }

    /**
     * aes加密，密文是base64编码
     *
     * @param data 明文
     * @param key  加密key
     * @return 若加密失败返回null
     */
    public static String aesEncodeBase64(String data, String key) {
        return CommonUtils.aesBase64(data, key, true);
    }

    /**
     * aes解密，密文是base64编码
     *
     * @param data 密文
     * @param key  解密key
     * @return 若解密失败返回null
     */
    public static String aesDecodeBase64(String data, String key) {
        return CommonUtils.aesBase64(data, key, false);
    }

    /**
     * aes加密或解密，密文是base64编码
     *
     * @param data       明文或密文
     * @param key        加密或解密的key
     * @param encodeMode true-加密  false-解密
     * @return 若加密或解密失败返回null
     */
    private static String aesBase64(String data, String key, boolean encodeMode) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(key)) {
            return null;
        }

        try {
            byte[] content;
            if (encodeMode) {
                content = data.getBytes(DEFAULT_CHARSET);
            } else {
                content = CommonUtils.decodeBase64(data);
            }
            // MD5后取16位
            key = DigestUtils.md5Hex(key.getBytes()).substring(8, 24);

            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(DEFAULT_CHARSET), KEY_AES);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(encodeMode ? Cipher.ENCRYPT_MODE : Cipher.DECRYPT_MODE, keySpec);
            byte[] result = cipher.doFinal(content);
            if (encodeMode) {
                return CommonUtils.encodeBase64(result);
            } else {
                return new String(result, DEFAULT_CHARSET);
            }
        } catch (Exception e) {
            log.error("aes加密或解密, 出现异常", e);
            return null;
        }
    }

    /**
     * 生成Base64字符串
     *
     * @param bytes
     * @return
     */
    private static String encodeBase64(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    /**
     * 解码Base64字符串
     *
     * @param message
     * @return
     */
    private static byte[] decodeBase64(String message) {
        return Base64.decodeBase64(message);
    }

    /**
     * 从PKCS8中转换成私有密钥
     *
     * @param algorithm
     * @param ins
     * @return 若转换失败则返回null
     */
    private static PrivateKey getPrivateKeyFromPKCS8(String algorithm, InputStream ins) {
        if (ins == null || StringUtils.isEmpty(algorithm)) {
            return null;
        }

        try {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
            byte[] encodedKey = IOUtils.toByteArray(ins);
            encodedKey = Base64.decodeBase64(encodedKey);

            return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
        } catch (Exception e) {
            log.error("从PKCS8中转换成私有密钥,出现异常", e);
            return null;
        }
    }

    /**
     * 从X509中转换成公钥
     *
     * @param algorithm
     * @param ins
     * @return 若转换失败则返回null
     */
    private static PublicKey getPublicKeyFromX509(String algorithm, InputStream ins) {
        if (ins == null || StringUtils.isEmpty(algorithm)) {
            return null;
        }

        try {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
            byte[] encodedKey = IOUtils.toByteArray(ins);
            encodedKey = Base64.decodeBase64(encodedKey);

            return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
        } catch (Exception e) {
            log.error("从X509中转换成公钥,出现异常", e);
            return null;
        }
    }


    /**
     * 抓取异常错误信息
     */
    public static String getStackTrace(Exception e) {
        String stackTrace;
        Writer result = new StringWriter();
        PrintWriter printWriter = new PrintWriter(result);
        e.printStackTrace(printWriter);
        stackTrace = result.toString();
        if (stackTrace.length() > MAX_STACK_TRACE_LENGTH) {
            return stackTrace.substring(0, MAX_STACK_TRACE_LENGTH);
        }
        return stackTrace;
    }


    /**
     * 两个线程并行,返回汇总结果
     */
    public static <U> U parallelHandle(U u, Supplier<U> first, Supplier<U> second, BiFunction<U, U, U> biFunction) {
        CompletableFuture<U> rCompletableFuture = CompletableFuture.supplyAsync(first, defaultPool)
                .exceptionally((e) -> {
                    log.error("线程一执行异常", e);
                    return u;
                })
                .thenCombineAsync(CompletableFuture.supplyAsync(second, defaultPool)
                                .exceptionally((e) -> {
                                    log.error("线程二执行异常", e);
                                    return u;
                                }),
                        biFunction, defaultPool);
        return rCompletableFuture.join();
    }

    /**
     * 两个线程并行,返回汇总结果
     * @param u
     * @param v
     * @param first
     * @param second
     * @param triFunction
     * @param executor 自定义执行器
     * @return
     * @param <U>
     */
    public static <U, V> U parallelHandle(U u, V v, Supplier<U> first, Supplier<U> second, TriFunction<U, U, V, U> triFunction,Executor executor) {
        CompletableFuture<U> rCompletableFuture = CompletableFuture.supplyAsync(first, executor)
                .exceptionally((e) -> {
                    log.error("线程一执行异常", e);
                    return u;
                })
                .thenCombineAsync(CompletableFuture.supplyAsync(second, executor)
                                .exceptionally((e) -> {
                                    log.error("线程二执行异常", e);
                                    return u;
                                }),
                        (result1, result2) -> triFunction.apply(result1, result2, v), executor);
        return rCompletableFuture.join();
    }


    /**
     * 分页查询
     */
    public static <T extends PageDto, R> void executePageEach(T request, Function<T, PageInfo<R>> findFunction, Consumer<List<R>> action) {
        Integer pageNo = request.getPageNum();
        PageInfo<R> page = new PageInfo<>();
        do {
            try {
                request.setPageNum(pageNo);
                page = Optional.ofNullable(findFunction.apply(request)).orElseThrow(() -> new IllegalArgumentException("分页批次执行任务 获取分页数据为空"));
                if (CollUtilX.isNotEmpty(page.getList())) {
                    action.accept(page.getList());
                }
            } catch (Exception e) {
                log.error("分页查询系统异常,e:", e);
            } finally {
                pageNo++;
            }
        } while (Objects.equals(page.getList().size(), page.getPageSize()));
    }

    /**
     * 自定义三元函数式接口
     */
    @FunctionalInterface
    public interface TriFunction<T, U, V, R> {
        /**
         * 应用三元函数操作
         *
         * @param t 第一个参数
         * @param u 第二个参数
         * @param v 第三个参数
         * @return 返回函数执行结果
         */
        R apply(T t, U u, V v);
    }

    /**
     * 自定义四元函数式接口
     */
    @FunctionalInterface
    public interface QuaFunction<T, U, V, W, R> {
        /**
         * 应用四元函数操作
         *
         * @param t 第一个参数
         * @param u 第二个参数
         * @param v 第三个参数
         * @param w 第四个参数
         * @return 返回函数执行结果
         */
        R apply(T t, U u, V v, W w);
    }

    /**
     * 三个线程并行处理方法
     * @param u 默认值
     * @param v 额外参数
     * @param first 第一个任务
     * @param second 第二个任务
     * @param third 第三个任务
     * @param quaFunction 四元函数
     * @param executor 执行器
     * @return 处理结果
     */
    public static <U, V> U parallelHandleTriple(U u,
                                             V v,
                                             Supplier<U> first,
                                             Supplier<U> second,
                                             Supplier<U> third,
                                             QuaFunction<U, U, U, V, U> quaFunction,
                                             Executor executor) {

        // 并行执行三个任务，附加异常处理
        CompletableFuture<U> future1 = CompletableFuture.supplyAsync(first, executor)
                .exceptionally(e -> {
                    log.error("线程一执行异常", e);
                    return u;
                });
        CompletableFuture<U> future2 = CompletableFuture.supplyAsync(second, executor)
                .exceptionally(e -> {
                    log.error("线程二执行异常", e);
                    return u;
                });
        CompletableFuture<U> future3 = CompletableFuture.supplyAsync(third, executor)
                .exceptionally(e -> {
                    log.error("线程三执行异常", e);
                    return u;
                });

        // 合并三个任务的结果
        CompletableFuture<U> combinedFuture = CompletableFuture.allOf(future1, future2, future3)
                .thenApplyAsync(w ->
                                quaFunction.apply(future1.join(), future2.join(), future3.join(), v),
                        executor
                );

        return combinedFuture.join();
    }
}
