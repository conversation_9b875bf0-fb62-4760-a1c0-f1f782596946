package com.tiangong.dis.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品售价
 * <AUTHOR>
 */
@Data
public class ProductSaleIncreaseDTO {

    /**
     * 产品id
     */
    private String productId;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * redisKey
     */
    private String redisKey;

    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * B2B调整方式（0加数值 1减数值 2加百分比 3减百分比 4等于）
     */
    private Integer b2bAdjustmentType;

    /**
     * B2B调整金额
     */
    private BigDecimal b2bModifiedAmt;

    /**
     * B2B最小加辐值
     */
    private BigDecimal b2bMiniAddRadiation;
}
