package com.tiangong.dis.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/11/9 16:12
 */
@Data
public class DistrictOrBusinessReq {

    /**
     * 城市编码
     */
    @NotEmpty(message = "EMPTY_PARAM_CITYCODE")
    private String cityCode;

    /**
     * 行政区名
     */
    private String DistrictName;

    /**
     * 商业区名
     */
    private String businessZoneName;

    /**
     * 查询类型 1获取行政区数据 2获取商业区数据 3获取行政区和商业区数据
     */
    private Integer districtOrBusinessType;

    private String language;
}
