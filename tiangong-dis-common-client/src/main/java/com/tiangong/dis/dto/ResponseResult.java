package com.tiangong.dis.dto;

import com.tiangong.dto.common.BusinessResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @param <T>
 */
@Data
@NoArgsConstructor
public class ResponseResult<T extends BusinessResponse> {

    /**
     * 返回编码
     */
    private String returnCode;

    /**
     * 返回信息
     */
    private String returnMsg;

    /**
     * 错误编码
     */
    protected String failCode;

    /**
     * 错误原因
     */
    protected String failReason;

    /**
     * 业务响应参数
     */
    private T bussinessResponse;

    public ResponseResult(String returnCode, String returnMsg) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
    }

    public ResponseResult(String returnCode, String returnMsg, T bussinessResponse) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
        this.bussinessResponse = bussinessResponse;
    }

    public ResponseResult(String returnCode,String failCode, String returnMsg) {
        this.returnCode = returnCode;
        this.failCode = failCode;
        this.returnMsg = returnMsg;
        this.bussinessResponse = bussinessResponse;
    }
}
