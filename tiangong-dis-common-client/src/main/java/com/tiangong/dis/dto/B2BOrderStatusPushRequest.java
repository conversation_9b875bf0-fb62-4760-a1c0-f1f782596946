package com.tiangong.dis.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class B2BOrderStatusPushRequest {

    /**
     * 合作商订单号
     */
    private String coOrderCode;

    /**
     * 房仓订单号
     */
    private String fcOrderCode;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 原因
     */
    private String message;

    /**
     * 酒店订单确认号
     */
    private String hotelConfirmNo;

    /**
     * 分销商编码
     */
    private String agentCode;

    /**
     * 合作商编码
     */
    private String partnerCode;

    /**
     * 取消费用
     */
    private BigDecimal cancellationPrice;

    /**
     * 罚金币种
     */
    private String currency;

    /**
     * 罚金金额
     */
    private BigDecimal penaltiesValue;


    /**
     * 是否重试
     */
    private Boolean retry = true;
}
