package com.tiangong.dis.dto;

import lombok.Data;

import java.util.List;

/**
 * 价格计划条款
 * <AUTHOR>
 */
@Data
public class ProductRestrictDTO {


    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 取消条款类型（0一经预订不能取消 1可以取消）
     */
    private Integer cancellationType;

    /**
     * 取消条款提前天数
     */
    private Integer cancellationAdvanceDays;

    /**
     * 取消条款提前时间点
     */
    private String cancellationDueTime;

    /**
     * 取消条款扣费说明
     */
    private String cancellationDeductionTerm;

    /**
     * 罚金类型
     * 1. *晚
     * 2. 固定金额
     * 3. 百分比
     * 4. 首晚百分比
     * @see com.tiangong.enums.PenaltiesTypeEnum
     */
    private Integer cancelPenaltiesType;

    /**
     * 罚金值
     */
    private Double cancelPenaltiesValue;

    /**
     * 预订天数条款类型（0大于等于 1小于等于 2等于）
     */
    private Integer comparisonType;

    /**
     * 预订天数条款天数
     */
    private Integer reservationLimitNights;

    /**
     * 预订提前天数  -1, 0 正数   -1表示支持凌晨预订
     */
    private Integer reservationAdvanceDays;

    /**
     * 预订提前时间点
     */
    private String reservationDueTime;

    /**
     * 预订间数条款间数
     */
    private Integer reservationLimitRooms;

    /**
     * 担保条款
     */
    private List<GuaranteeDTO> guarantees;

    /**
     * key
     */
    private String redisKey;

    /**
     * 早餐数
     */
    private Integer breakfastQty;

    /**
     * 有效性
     */
    private Integer active;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 日期
     */
    private String saleDate;

    /**
     * 变化时间
     */
    private String changeTime;

    /**
     * 供货类型
     */
    private Integer supplyType;

}
