package com.tiangong.dis.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
/**
 * <AUTHOR>
 */
@Data
public class OrderCheckDetailPriceItem {
    /**
     * 售卖日期
     */
    @NotNull(message = "EMPTY_PARAM_SALEDATE")
    private String saleDate;

    /**
     * 价格
     */
    @NotNull(message = "EMPTY_PARAM_SALEPRICE")
    private BigDecimal salePrice;

    /**
     * 取消费用
     */
    private BigDecimal refundPrice;

}
