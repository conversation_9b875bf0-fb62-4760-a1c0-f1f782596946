package com.tiangong.dis.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品底价和房态
 * <AUTHOR>
 */
@Data
public class ProductBasePriceAndRoomStatusDTO {

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 售价日期
     */
    private String saleDate;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 销售税
     */
    private BigDecimal salesTax;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 到店另付费用币种
     */
    private Integer payAtHotelFeeCurrency;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 其他税费
     */
    private BigDecimal otherTaxFee;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 原始底价 由于basePrice被设置为加浮后的价格，所以添加这个对象存放原底价
     */
    private BigDecimal tmpBasePrice;


    /**
     * 房态(1开房 0关房)
     */
    private Integer roomStatus;

    /**
     * 售罄是否可超(1可超 0不可超)
     */
    private Integer overDraftStatus;

    /**
     * 配额数
     */
    private Integer quota;

    /**
     * 剩余配额数
     */
    private Integer remainingQuota;

    /**
     * redisKey
     */
    private String redisKey;

    /**
     * 早餐数
     */
    private Integer breakfastQty;

    /**
     * 变化时间
     */
    private String changeTime;


    /**
     * 佣金(仅美团)
     */
    private BigDecimal commission;

    /**
     * 餐食类型  (携程代理通)
     */
    private int mealType;

    /**
     * 餐食数量  (携程代理通)
     */
    private int mealCount;

}
