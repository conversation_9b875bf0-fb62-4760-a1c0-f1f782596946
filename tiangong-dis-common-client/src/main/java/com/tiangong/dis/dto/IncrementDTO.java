package com.tiangong.dis.dto;

import lombok.Data;

import java.util.Set;

/**
 * 增量
 * <AUTHOR>
 */
@Data
public class IncrementDTO {

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 美团产品id
     */
    private String disProductId;

    /**
     * 增量list
     */
    private Set<String> saleDate;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 增量类型。详情见com.tiangong.enums.IncrementTypeEnum
     */
    private Integer type;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 对方酒店id
     */
    private String disHotelId;

    /**
     * 早餐数
     */
    private Integer breakfastNum;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 房型名称
     */
    private String roomId;

    /**
     * 查询开始时间
     */
    private String startDate;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 对方房型id
     */
    private String disRoomId;

    /**
     * 是否新建
     */
    private Integer isCreate;

    /**
     * 控制阀 枫林定制化需求
     */
    private Boolean flag;

    /**
     * 发送时间
     */
    private String sendTime;

}
