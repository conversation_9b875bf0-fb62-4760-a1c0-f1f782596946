package com.tiangong.dis.dto;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class HotelLabelConfigReqDTO extends BaseRequest implements Serializable {
    /**
     * id
     */
    private Integer hotelLabelConfigId;
    /**
     * 标签编码
     */
    private String labelCode;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签推荐分值
     */
    private Long recommendScore;

    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改人
     */
    private String updatedBy;
}
