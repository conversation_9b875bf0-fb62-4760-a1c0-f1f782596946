package com.tiangong.dis.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 地区基本信息
 * <AUTHOR>
 */
@Data
public class BaseinfoAreadataDTO {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 地区编码
     */
    private String areaCode;
    /**
     * 地区名称
     */
    private String areaName;
    /**
     * 拼音
     */
    private String pinyin;
    /**
     * 首字母缩写
     */
    private String acronym;
    /**
     * 父类地区id
     */
    private String parentAreaId;
    /**
     * 地区英文名称
     */
    private String areaEnglishName;
    /**
     * 市级城市id
     */
    private String cityId;
    /**
     * 地区类型(0 洲，1 国家，2 省份，3 城市，4 行政区， 5 商业区)
     */
    private Integer areaType;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 是否删除(0 否，1 是)
     */
    private Integer deleted;

    /**
     * 是否热门(0 否，1 是)
     */
    private Integer top;

    /**
     * 地区类型1国内 2海外
     */
    private Integer supplyType;

    /**
     * 时区
     */
    private String timeZero;

}
