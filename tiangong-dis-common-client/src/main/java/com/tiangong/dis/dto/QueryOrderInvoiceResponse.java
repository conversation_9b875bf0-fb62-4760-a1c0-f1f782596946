package com.tiangong.dis.dto;

import com.tiangong.dto.common.BusinessResponse;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 查询订单发票响应
 * <AUTHOR>
 */
@Data
public class QueryOrderInvoiceResponse extends BusinessResponse {
    /**
     * id
     */
    private Integer id;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 开票状态 详情见InvoiceStatusEnum
     */
    private Integer invoiceStatus;

    /**
     * 开票时间
     */
    private String invoiceDate;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 发票类型 详情见InvoiceTypeEnum
     */
    private Integer invoiceType;

    /**
     * 抬头类型 详情见InvoiceTitleTypeEnum
     */
    private Integer invoiceTitleType;

    /**
     * 发票名称 详情见InvoiceNameEnum
     */
    private Integer invoiceName;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 公司电话
     */
    private String companyPhone;

    /**
     * 注册地址
     */
    private String registerAddr;

    /**
     * 银行账号
     */
    private String accountNo;

    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 申请日期
     */
    private String applyDate;

    /**
     * 取票方式 详情见TicketTypeEnum
     */
    private Integer ticketType;

    /**
     * 发送方式(0 未发送, 1 已发送)
     */
    private Integer sendStatus;

    /**
     * 收件人
     */
    private String receivedName;

    /**
     * 收件人电话
     */
    private String receivedPhone;

    /**
     * 收件人地址
     */
    private String receivedAddr;

    /**
     * 快递单号
     */
    private String trackingNo;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 操作人员
     */
    private String operator;
    /**
     * 票单/发票号
     */
    private String invoiceNo;
    private String applyId;
}
