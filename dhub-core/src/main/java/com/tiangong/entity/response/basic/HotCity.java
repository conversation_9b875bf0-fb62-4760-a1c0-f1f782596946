package com.tiangong.entity.response.basic;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class HotCity {

    //城市名
    private String cityName;

    //城市编码
    private String cityCode;

    //区域类型
    private Integer areaType;

    //父级id
    private String parentAreaId;

    //维度
    private BigDecimal latitude;

    //经度
    private BigDecimal longitude;

    //时区
    private String timeZone;

//    private String countryCode;
//
//    private String provinceCode;

}
