package com.tiangong.entity.response.hotel;

import com.tiangong.entity.base.BusinessResponse;
import com.tiangong.product.resp.HotelPriceDTO;
import lombok.Data;

import java.util.List;

@Data
////@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class HotelListResponse extends BusinessResponse {


    private List<HotelPriceDTO> list;

    /**
     * 总记录数
     */
    private long totalCount;

    /**
     * 总页数
     */
    private int totalPage;

    /**
     * 当前页 默认1
     */
    private int currentPage;

    /**
     * 页面记录数
     */
    private int pageSize;
}
