package com.tiangong.entity.response.basic;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddressList {

    private String groupName;

    private String cityName;

    private String countryCode;

    private String provinceCode;

    private String cityCode;

    private String name;

    private String language;

    private Long hotelId;

    private String countryName;

    private String provinceName;

    private String groupCode;

    private BigDecimal latitude;

    private BigDecimal longitude;
}
