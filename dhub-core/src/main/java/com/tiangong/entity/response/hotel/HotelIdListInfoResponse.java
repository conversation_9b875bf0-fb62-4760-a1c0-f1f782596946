package com.tiangong.entity.response.hotel;

import com.tiangong.entity.base.BusinessResponse;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 11．酒店列表查询接口-queryHotelList 接口返回分页对象
 */
@Data
@Accessors(chain = true)
////@JsonInclude(JsonInclude.Include.NON_NULL)
public class HotelIdListInfoResponse extends BusinessResponse {

    /**
     * 酒店列表
     */
    private List<Long> hotelIds;

    /**
     * maxId标记值，用于下一页查询。当maxId为-1时，表示已经查询到最后一页
     */
    private Long maxId;

    /**
     * 总页数
     */
    private Integer totalPage;

    /**
     * 当前页
     */
    private Integer currentPage;

    /**
     * 总记录数
     */
    private Integer total;

    /**
     * 页大小
     */
    private Integer pageSize;


}
