package com.tiangong.entity.response.order;

import com.tiangong.entity.base.BusinessResponse;
import lombok.Data;

@Data
public class OrderStatusPushResponse extends BusinessResponse {

    /**
     * 合作商的订单号
     */
    private String coOrderCode;

    /**
     * 接收状态
     */
    private Integer receiveStatus;

    /**
     * 不确认原因
     */
    private String message;

    /**
     * 返回码
     */
    private String returnCode;

    /**
     * 返回描述信息
     */
    private String returnMsg;
}
