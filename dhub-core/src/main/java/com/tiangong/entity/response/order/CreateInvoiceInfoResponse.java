package com.tiangong.entity.response.order;

import com.tiangong.entity.base.BusinessResponse;
import lombok.Data;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CreateInvoiceInfoResponse extends BusinessResponse {

    private int result;

    private String message;
    /**
     * 开票申请单号id
     */
    private String applyId;
    /**
     * 发票id
     */
    private String invoiceId;
    /**
     * 序号 默认为1
     */
    private Integer sortNum;
}
