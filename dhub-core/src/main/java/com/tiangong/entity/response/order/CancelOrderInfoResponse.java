package com.tiangong.entity.response.order;

import com.tiangong.entity.base.BusinessResponse;
import lombok.Data;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CancelOrderInfoResponse extends BusinessResponse {

    /** 合作商订单号 */
    private String coOrderCode;

    /** 房仓订单号 */
    private String fcOrderCode;

    /** 取消结果 */
    private Integer cancelResult;

    /** 失败原因 */
    private String message;

}
