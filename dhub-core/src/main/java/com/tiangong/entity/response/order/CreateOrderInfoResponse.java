package com.tiangong.entity.response.order;

import com.tiangong.entity.base.BusinessResponse;
import lombok.Data;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CreateOrderInfoResponse extends BusinessResponse {

    /** 合作商订单号 */
    private String coOrderCode;

    /** 房仓订单号 */
    private String fcOrderCode;

    /** 结果  1-预订成功; 2-预订失败; */
    private Integer result;

    /** 失败原因 */
    private String message;

}
