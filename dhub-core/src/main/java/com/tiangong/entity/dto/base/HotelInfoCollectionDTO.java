package com.tiangong.entity.dto.base;

import com.tiangong.dto.hotel.HotFacility;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class HotelInfoCollectionDTO {
    /**
     * 酒店id
     */
    private Long hotelId;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 酒店英文名称   国内酒店需要存该字段，国外酒店无需存该数据
     */
    private String hotelEngName;
    /**
     * 酒店层数
     */
    private String floorCount;
    /**
     * 客房总数
     */
    private String roomCount;
    /**
     * 酒店排名
     */
    private Integer ranking;
    /**
     * 酒店一级分类
     */
    private String hotelCategory;
    /**
     * 酒店二级分类
     */
    private String hotelSubCategory;
    /**
     * 开业日期
     */
    private Date praciceDate;
    /**
     * 装修日期
     */
    private Date fitmentDate;
    /**
     * 传真
     */
    private String fax;
    /**
     * 规定入住时间
     */
    private String checkInTime;
    /**
     * 规定退房时间
     */
    private String checkOutTime;
    /**
     * 简介
     */
    private String introduce;
    /**
     * 集团
     */
    private GroupDTO group;
    /**
     * 品牌
     */
    private BrandDTO brand;
    /**
     * 经纬度（百度、谷歌、高德）
     */
    private GeoInfoDTO geoInfo;
    /**
     * 位置（国家、省份、城市、地址）
     */
    private LocationDTO location;
    /**
     * 行政区
     */
    private District district;
    /**
     * 商业区
     */
    private Business businesszone;
    /**
     * 星级
     */
    private HotelStar hotelStar;
    /**
     * 入离政策
     */
    private InOutTimeDTO inOutPolicy;
    /**
     * 收费标准
     */
    private List<HotelChargeInfoDTO> addBedCharge;
    /**
     * 电话
     */
    private String tel;
    /**
     * 通知
     */
    private InformDTO inform;
    /**
     * 设施集合
     */
    private List<FacilityPublicDTO> facilities;
    /**
     * 图片集合
     */
    private List<ImageDTO> images;
    /**
     * 停车场和充电车位
     */
    private ParkingDTO parkings;
    /**
     * 房型集合
     */
    private List<RoomtypeDTO> rooms;
    /**
     * 会议室集合
     */
    private List<MeetingDTO> meetings;
    /**
     * 资质
     */
    private List<HotelCertificationDTO> certificates;
    /**
     * 视频
     */
    private List<HotelTypeVideoDTO> videoInfos;
    /**
     * 评分
     */
    private HotelRatingsDTO rating;
    /**
     * 早餐
     */
    private BreakFastDTO breakfast;
    /**
     * 加床政策
     */
    private AddBedPolicyDTO addBedPolicy;
    /**
     * 儿童政策
     */
    private ChildPolicyDTO childPolicy;
    /**
     * 宠物政策
     */
    private PetDTO petPolicy;
    /**
     * 酒店文本政策
     */
    private List<HotelTextPolicyDTO> hotelTextPolicyList;
    /**
     * 酒店设施
     */
    private HotFacility hotFacility;
}
