package com.tiangong.entity.dto.base;

import lombok.Data;

@Data
public class HotelBreakfastOpenTimeDTO {
    /**
     * 酒店ID
     */
    private Long hotelId;
    /**
     * 每周开放指数 0-不开放 1-开放
     * 以一周为单位
     */
    private String weeklyIndex;
    /**
     * 是否营业 0-否 1-是
     */
    private String isOpen;
    /**
     * 营业开始时间
     */
    private String openStartTime;
    /**
     * 营业结束时间
     */
    private String openEndTime;
}
