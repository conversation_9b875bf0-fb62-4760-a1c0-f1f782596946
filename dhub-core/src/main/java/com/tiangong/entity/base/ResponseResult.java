package com.tiangong.entity.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class ResponseResult<T extends BusinessResponse> {

    /** 返回编码 */
    private String returnCode;

    /** 返回信息 */
    private String returnMsg;

    /** 业务响应参数 */
    private T bussinessResponse;
    
    public ResponseResult(String returnCode, String returnMsg) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return isSuccess(this.returnCode);
    }

    @JsonIgnore
    public boolean isError() {
        return !isSuccess(this.returnCode);
    }

    public static boolean isSuccess(String returnCode) {
        return returnCode.equals("000") || returnCode.equals("SUCCESS");
    }
}
