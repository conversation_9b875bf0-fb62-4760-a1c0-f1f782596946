package com.tiangong.entity.request.order;

import com.tiangong.dto.product.response.BedInfoDto;
import com.tiangong.entity.base.BusinessRequest;
import com.tiangong.entity.vo.order.CreateOrderPriceItemDto;
import com.tiangong.entity.vo.order.GuestInfo;
import com.tiangong.entity.vo.order.OrderSignatureInfo;
import com.tiangong.entity.vo.order.RoomGuestNumberDto;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CreateOrderInfoRequest extends BusinessRequest {

    /**
     * 酒店id
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 房型id
     */
    @NotNull(message = "EMPTY_PARAM_ROOMID")
    private Long roomId;

    /**
     * 价格计划id
     */
    @NotEmpty(message = "EMPTY_PARAM_RATEPLANID")
    private String ratePlanId;

    /**
     * 入住日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKINDATE(yyyy-MM-dd)")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKOUTDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKOUTDATE(yyyy-MM-dd)")
    private String checkOutDate;

    /**
     * 预订间数
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private Integer roomNum;

    /**
     * 床型明细
     */
    private List<BedInfoDto> bedInfos;

    /**
     * 床型
     */
    private String bedType;

    /**
     * 总金额
     */
    @NotNull(message = "EMPTY_PARAM_TOTALAMOUNT")
    private BigDecimal totalAmount;

    /**
     * 合作商订单号
     */
    @NotEmpty(message = "EMPTY_PARAM_COORDERCODE")
    private String coOrderCode;

    /**
     * 订单每日价格详情
     */
    @Valid
    @NotEmpty(message = "EMPTY_PARAM_ORDERPRICEITEMS")
    private List<CreateOrderPriceItemDto> priceItems;

    /**
     * 入住人列表
     */
    @Valid
    @Size(min = 1, message = "EMPTY_PARAM_GUESTINFOS")
    private List<GuestInfo> guestInfos;

    /**
     * 订单联系人
     */
    @NotEmpty(message = "EMPTY_PARAM_LINKMAN")
    private String linkMan;

    /**
     * 联系人电话
     */
    @NotEmpty(message = "EMPTY_PARAM_LINKPHONE")
    private String linkPhone;

    /**
     * 电话区号
     */
    private String linkCountryCode;

    /**
     * 邮箱
     */
    @Email(message = "PARAM_FORMAT_EMAIL_ERROR")
    private String email;

    /**
     * 到店时间
     */
    @NotEmpty(message = "EMPTY_PARAM_ARRIVETIME")
    @Pattern(regexp = "^\\d{2}:\\d{2}$", message = "PARAM_FORMAT_ARRIVETIME(HH:mm)")
    private String arriveTime;

    /**
     * 最晚到店时间
     */
    @NotEmpty(message = "EMPTY_PARAM_LATESTARRIVETIME")
    @Pattern(regexp = "^\\d{2}:\\d{2}$", message = "PARAM_FORMAT_LATESTARRIVETIME(HH:mm)")
    private String latestArriveTime;

    /**
     * 特殊要求
     */
    private String specialDemand;

    /**
     * 备注信息
     */
    private String remark;

    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplyCode;
    /**
     * 是否到店付(1-是，0或其它-否)
     */
    private Integer payAtHotelFlag;

    /**
     * 是否担保标志 ,1担保 0未担保
     */
    private Integer guaranteeFlag;

    /**
     * 优惠金额
     */
    private Double benefitAmount;

    /**
     * 入住类型， 1为全日房，2为钟点房
     */
    private Integer checkInType;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 出行类型，1-因公，2-因私 非必填
     */
    private Integer travelType;

    /**
     * 客户下属单位名称
     */
    private String sdistributorName;

    /**
     * 订单数字签章
     */
    private OrderSignatureInfo orderSignatureInfo;

    /**
     * 房间客人数量信息，海外酒店必传
     */
    private List<RoomGuestNumberDto> roomGuestNumbers;

    /**
     * 房间客人数量信息，海外酒店必传
     */
    private RoomGuestNumberDto roomGuestNumber;

    /**
     * 订单支付状态
     */
    private Integer payStatus;

    /**
     * 快速处理标签开关
     */
    //private Integer quickProcessingSwitch;
    private Integer rapidProcessing;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 服务费
     */
    private BigDecimal serviceCharge;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 0：国内，1：国外
     */
    private Integer domesticOrOverseas;
}
