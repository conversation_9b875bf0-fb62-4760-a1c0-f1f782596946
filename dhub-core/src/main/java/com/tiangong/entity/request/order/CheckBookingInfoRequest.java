package com.tiangong.entity.request.order;

import com.tiangong.entity.base.BusinessRequest;
import com.tiangong.entity.vo.order.RoomGuestNumberDto;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class CheckBookingInfoRequest extends BusinessRequest {

    /**
     * 酒店id
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 房型id
     */
    @NotNull(message = "EMPTY_PARAM_ROOMID")
    private Long roomId;

    /**
     * 价格计划id
     */
    @NotEmpty(message = "EMPTY_PARAM_RATEPLANID")
    private String ratePlanId;

    /**
     * 入住类型
     */
    private Integer checkInType;

    /**
     * 入住日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKINDATE(yyyy-MM-dd)")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKOUTDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKOUTDATE(yyyy-MM-dd)")
    private String checkOutDate;

    /**
     * 间数
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private Integer roomNum;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplyCode;


    /**
     * 入住人数
     */
    private List<RoomGuestNumberDto> roomGuestNumbers;

    /**
     * 房间客人数量信息，海外酒店必传
     */
    private RoomGuestNumberDto roomGuestNumber;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 0：国内，1：国外
     */
    private Integer domesticOrOverseas;
}
