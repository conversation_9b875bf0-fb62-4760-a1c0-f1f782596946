package com.tiangong.entity.request.order;

import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderStatusPushRequest extends BusinessRequest {


    /**
     * 合作商订单号
     */
    private String coOrderCode;

    /**
     * 房仓订单号
     */
    private String fcOrderCode;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 原因
     */
    private String message;

    /**
     * 酒店订单确认号
     */
    private String hotelConfirmNo;

    /**
     * 取消费用
     */
    private BigDecimal cancellationPrice;


    /**
     * 分销商编码
     */
    private String agentCode;

    /**
     * 合作商编码
     */
    private String partnerCode;

    /**
     * 币种
     */
    private String currency;

    /**
     * 罚金金额
     */
    private BigDecimal penaltiesValue;

    /**
     * 是否重试
     */
    private Boolean retry = true;
}

