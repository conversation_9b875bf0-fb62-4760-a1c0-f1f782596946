package com.tiangong.entity.request.basic;

import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class CityListRequest extends BusinessRequest {

    /** 国家编码
     *  CN：中国
     *  ABROAD：海外
     *  ALL：全部
     * */
    @NotEmpty(message = "EMPTY_PARAM_COUNTRYCODE")
    private String countryCode;

    /**
     * 返回城市名称的语言类型，针对海外城市有效，参考语言字典项，不传默认查询中文
     */
    private String language;
}
