package com.tiangong.entity.request.order;

import com.tiangong.entity.base.BusinessRequest;
import com.tiangong.entity.response.order.CheckoutInfo;
import com.tiangong.entity.response.order.CheckoutRefundItemInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CheckoutApplyStatusPushInfoRequest extends BusinessRequest {
    /**
     * 任务单号
     */
    private String taskOrderCode;

    /**
     * 退款状态，T0-待处理,T1-跟进中待退款,T2-已退款完成,T3-已取消
     */
    private String refundStatus;

    /**
     * 订单号
     */
    private String fcOrderCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 币种
     */
    private String currency;

    /**
     * 我司应退客户金额
     */
    private BigDecimal agentReceivableRefund;

    /**
     * 我司实退客户金额
     */
    private BigDecimal agentReceivedRefund;

    /**
     * 退款方式
     */
    private Integer refundMethod;

    /**
     * 退款账号
     */
    private String refundAccount;

    /**
     * 退款流水号
     */
    private String serialNumber;

    /**
     * 实际退房手续费
     * 退款间夜房费总和-实际退款我司退客户金额
     */
    private BigDecimal receivedServiceCharge;

    /**
     * 退房信息
     */
    private List<CheckoutInfo> cheOutList;

    /**
     * 退房信息
     */
    private List<CheckoutRefundItemInfo> refundItemInfoList;
}
