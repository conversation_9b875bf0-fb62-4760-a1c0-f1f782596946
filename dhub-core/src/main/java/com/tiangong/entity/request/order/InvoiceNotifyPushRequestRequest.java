package com.tiangong.entity.request.order;

import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class InvoiceNotifyPushRequestRequest extends BusinessRequest {
    private Integer id;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 开票申请单号id
     */
    private String applyId;


    /**
     * 票单/发票号
     */
    private String invoiceNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票url 多个逗号隔开
     */
    private String invoiceUrls;
    /**
     * 开票时间
     */
    private String invoiceDate;
    /**
     * 开票状态 详情见InvoiceStatusEnum
     */
    private Integer invoiceStatus;
    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;
}
