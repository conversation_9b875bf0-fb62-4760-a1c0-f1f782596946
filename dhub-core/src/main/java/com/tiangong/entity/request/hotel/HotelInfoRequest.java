package com.tiangong.entity.request.hotel;

import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Validated
public class HotelInfoRequest extends BusinessRequest {

    /** 酒店id列表 */
    @Size(min =1 ,max = 10,message = "PARAM_MAX_10_HOTELIDS")
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private List<Long> hotelIds;

    /** 指定参数列表 */
    private List<String> settings;

    /**
     * 返回城市名称的语言类型，针对海外城市有效，参考语言字典项，不传默认查询中文
     */
    private String language;
}
