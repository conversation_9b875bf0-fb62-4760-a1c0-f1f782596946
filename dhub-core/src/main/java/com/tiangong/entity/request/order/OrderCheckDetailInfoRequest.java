
package com.tiangong.entity.request.order;

import com.tiangong.entity.base.BusinessRequest;
import com.tiangong.entity.vo.order.OrderCheckDetails;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class OrderCheckDetailInfoRequest  extends BusinessRequest {

	/**
	 * 泰坦云订单号
	 */
	private String fcOrderCode;

	/**
	 * coOrderCode
	 */
	private String coOrderCode;

	/**
	 *
	 订单级别入住状
	 态
	 */
	private String orderCheckInState;

	/**
	 * 订单入住总金额
	 */
	private BigDecimal orderSalePrice;


	/**
	 * 订单入住明细
	 */
	private List<OrderCheckDetails> checkDetails;

	/**
	 * 分销商编码
	 */
	private String agentCode;

	/**
	 * 订单总附加费
	 */
	private BigDecimal orderAdditionalCharges;

	/**
	 * 合作商编码
	 */
	private String partnerCode;

}
