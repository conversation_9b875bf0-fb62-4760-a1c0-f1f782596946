package com.tiangong.entity.request.basic;

import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AddressRequest extends BusinessRequest {

    /**
     * 关键字
     */
    @NotEmpty(message = "EMPTY_PARAM_KEYWORD")
    public String keyWord;

    /**
     * 0605版本，优化目的地接口需求 新增字段【热度值】、【目的地ID】
     *  by. 雷燕军
     */

    //城市编码
    private String cityCode;

    //目的地数据类型（1:酒店  2:城市）
    private Integer dataType;
}
