package com.tiangong.entity.request.hotel;

import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class HotelListPageRequest extends BusinessRequest {

    /**
     * 搜索关键词
     */
    private String keyWord;

    /**
     * 入住日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKINDATE(yyyy-MM-dd)")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKINDATE(yyyy-MM-dd)")
    private String checkOutDate;

    @NotEmpty(message = "ADDRESS_BY_ID_NOT_EMPTY")
    private String destinationId;

    /**
     * 价格开始
     */
    private Double priceBegin;

    /**
     * 价格结束
     */
    private Double priceEnd;

    /**
     * 酒店标签id
     */
    private List<Long> hotelLabelIds;

    /**
     * 集团编码
     */
    private List<String> hotelBrandCodes;

    /**
     * 集团编码
     */
    private List<String> plateCodes;

    /**
     * 住宿类型
     */
    private List<String> hotelSubCategoryIds;

    /**
     * 星级
     */
    private List<Long> hotelStars;

    private String agentCode;

    private List<Long> facilitiesIds;

    //热门酒店设施
    private List<String> hotelFacilityCodes;

    //热门房型设施
    private List<String> roomFacilityCodes;

    private Long currentPage = 1L; // 当前

    private Long pageSize = 10L; // 每页条数

    //1默认排序 2价格由低到高 3价格由高到低 4星级从高到底 5星级从低到高 6距离由近到远 7评分由高到低，默认传1
    private Integer sortBy = 1;

    private String language = "zh-CN";
}
