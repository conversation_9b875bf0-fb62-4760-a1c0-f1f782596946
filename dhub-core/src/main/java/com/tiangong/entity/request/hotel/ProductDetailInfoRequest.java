package com.tiangong.entity.request.hotel;

import com.tiangong.entity.base.BusinessRequest;
import com.tiangong.entity.vo.hotel.RoomGuestNumberDto;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class ProductDetailInfoRequest extends BusinessRequest {

    /** 酒店id */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /** 入住日期 */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKINDATE(yyyy-MM-dd)")
    private String checkInDate;

    /** 离店日期 */
    @NotEmpty(message = "EMPTY_PARAM_CHECKOUTDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKOUTDATE(yyyy-MM-dd)")
    private String checkOutDate;

    /**
     * 间数
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private Integer roomNum;

    /** 房间客人数量信息，海外酒店必传 */
   private List<RoomGuestNumberDto> roomGuestNumbers;

   /** 是否查出钟点房产品，空或false或连住多天时不查出，true查出。*/
   private Boolean needClockRoomProduct;

    /**
     * 房间客人数量信息，海外酒店必传
     */
    private RoomGuestNumberDto roomGuestNumber;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 0：国内，1：国外
     */
    private Integer domesticOrOverseas;
}
