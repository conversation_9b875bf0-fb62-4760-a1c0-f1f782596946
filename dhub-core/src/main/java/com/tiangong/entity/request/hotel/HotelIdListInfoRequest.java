package com.tiangong.entity.request.hotel;

import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

@Data
@Validated
public class HotelIdListInfoRequest extends BusinessRequest {

    /**
     * cityCode	城市编码
     */
    private String cityCode;

    /**
     * 页码，默认为1
     */
    private Integer pageNo;

    /**
     * 每页数量，默认为1000
     */
    private Integer pageSize=1000;

    /**
     * 查询上一页时返回的maxId标记值, 查询第一页时该值为0。
     * 使用说明：首次请求必须为0，之后再根据返回值修改后续MaxId值。
     */
    private Long maxId;

    /**
     * 入住类型：1或空-全日房，2-钟点房【即将上线】
     */
    private Integer checkInType;
}
