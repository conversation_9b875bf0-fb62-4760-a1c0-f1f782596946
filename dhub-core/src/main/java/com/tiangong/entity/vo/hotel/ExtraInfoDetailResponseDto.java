package com.tiangong.entity.vo.hotel;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by ji<PERSON><PERSON><PERSON> on 2017/6/23.
 * 扩展信息详情
 */
@Data
public class ExtraInfoDetailResponseDto implements Serializable {

    private String infoName;

    private String infoIcon;

    private Integer display;


    //ctrip5.0增加

    //入离时间  重新查询
    private String checkIn;
    private String checkOut;

    //宠物政策  重新查询
    private Integer isAllowsPet;
    private String petText;

    //信用卡&第三方支付  重新查询
    private Integer paymentType;
    private String paymentName;


    //加床及儿童政策  已有，不需要在查
    private String allowedUnderage;
    private String allowedAge;
    private String childrenBedNum;
    private String isOfferBed;
    private String isOfferInfanette;
    private String childrenBreakType;
    private String remark;
    private String isShowWarn;
    private String adultsBedExtra;
    private String chargeMinage;
    private String chargeMaxage;
    private String chargeManner;
    private String changeAmount;
    private String changeNumber;
    private String isChildrenBreak;
    private String chargeType;

    //早餐政策   已有
    private String isBreakfast;
    private String breakfastForm;
    private String fixedAmount;
    private String operateAmount;
    private String buffetAmount;
    private String breakfastType;
    private String breakfastVariety;
    private String breakfastOpenType;
    private String openStarttime;
    private String openEndtime;
    private String weeklyIndex;
    private String isOpen;

    //订房必读  已有
    private String informType;
    private String informStarttime;
    private String informEndtime;
    private String informText;

    //停车场  已有
    private String parkingLocation;
    private String parkingType;
    private String isReserved;
    private String isChargeable;
    private String chargeAmount;
    private String isLimitParking;
    private String chargingLocation;
    private String chargingPile;
    private String chargingType;
    private String isLimitCharging;

}
