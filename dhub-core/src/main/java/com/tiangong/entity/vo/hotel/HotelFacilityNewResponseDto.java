package com.tiangong.entity.vo.hotel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 *  酒店新设施信息
 *  CTRIP 5.0 改版数据
 */
@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class HotelFacilityNewResponseDto  implements Serializable {

    //设施类型编码
    private String typeCode;

    //设施类型名称  设施大类(比如：浴室)
    private String typeName;

    //设施类别(0-房型设施  1-酒店设施)
    private String categoryType;

    //设施状态(0-无  1-全部有  2-部分有)
    private Integer status = 0;

    //设施ID
    private String featureId;

    //设施名称
    private String name;

    //描述(酒店和房型描述)
    private String describe;

    //房型ID集合(针对设施类别为房型,设施状态为部分有的情况才有这个集合)
    private String roomIdList;

    //适用房型
    private List<ApplicableRoomTypeID> applicableRoomTypeIDs;
}
