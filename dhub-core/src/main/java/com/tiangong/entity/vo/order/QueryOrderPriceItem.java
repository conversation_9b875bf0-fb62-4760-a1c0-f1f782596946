package com.tiangong.entity.vo.order;

import lombok.Data;

import java.math.BigDecimal;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class QueryOrderPriceItem {

    /** 售卖日期 */
    private String saleDate;

    /** 售卖价格 */
    private BigDecimal salePrice;

    /** 早餐类型 */
    private Integer breakfastType;

    /** 早餐数量 */
    private Integer breakfastNum;

    /** 售价币种 */
    private String currency;

}
