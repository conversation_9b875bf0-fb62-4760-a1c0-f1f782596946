package com.tiangong.entity.vo.hotel;

import com.tiangong.entity.response.order.PriceTaxDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: qiu
 * @create: 2024-07-05 14:06
 */
@Data
public class RoomItemDetailDto {

    /**
     * 房间序号，多间房多个入住人使用
     */
    private Integer roomIndex;

    /**
     * 成人数
     */
    private Integer adultNum;

    /**
     * 儿童年龄 多个儿童年龄用逗号分隔 例如：1,2,3
     */
    private String childAges;

    /**
     * 每间 税费明细
     */
    private PriceTaxDetail taxDetail;

    /**
     * 到店另付币种(酒店当地币种)
     */
    private String payInStoreCurrencyCode;

    /**
     * 到店另付费用(酒店当地币种金额)
     */
    private BigDecimal payInStorePrice;

    /**
     * 到店另付币种(供应商币种)
     */
    private String supplyPayInStoreCurrencyCode;

    /**
     * 到店另付费用(供应商币种金额)
     */
    private BigDecimal supplyPayInStorePrice;

    /**
     * 售卖信息列表
     */
    private List<PriceDetailItemDto> priceItems;
}