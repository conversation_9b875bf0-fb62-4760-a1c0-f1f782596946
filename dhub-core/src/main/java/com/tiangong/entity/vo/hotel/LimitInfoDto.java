package com.tiangong.entity.vo.hotel;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/16 15:58
 */
@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class LimitInfoDto {

    //最大重量(单位：千克)
    private BigDecimal maxWeightKg;

    //最大重量（单位：磅）
    private BigDecimal maxWeightLb;

    //最多可携带宠物数
    private Integer quantity;

    //宠物类型（1：狗；2-猫；0：其他）
    private String animalType;
}
