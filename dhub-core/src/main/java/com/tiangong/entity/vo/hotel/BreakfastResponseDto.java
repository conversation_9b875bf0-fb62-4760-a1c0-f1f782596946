package com.tiangong.entity.vo.hotel;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class BreakfastResponseDto   implements Serializable {
    /**
     * 是否提供早餐 0-否 1-是
     */
    private String isBreakfast;

    /**
     * 早餐形式
     */
    private String breakfastForm;

    /**
     * 固定套餐金额
     */
    private BigDecimal fixedAmount;

    /**
     * 盒装视频/包装食品金额
     */
    private BigDecimal operateAmount;

    /**
     * 自助餐金额
     */
    private BigDecimal buffetAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 早餐类型(以,分割)
     */
    private String breakfastType;

    /**
     * 早餐种类(以,分割)
     */
    private String breakfastVariety;

    /**
     * 0-每日开放 1-指定日期开放
     */
    private String breakfastOpenType;

    /**
     * 每日开放开始时间
     */
    private String breakfastOpenContent;

    private List<BreakfastOpenTimeDto> breakfastOpenTimes;

}
