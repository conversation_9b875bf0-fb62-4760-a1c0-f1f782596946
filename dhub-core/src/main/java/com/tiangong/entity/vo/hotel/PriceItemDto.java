package com.tiangong.entity.vo.hotel;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class PriceItemDto {

    /** 售卖日期 */
    private String saleDate;

    /** 售卖价格 */
    private BigDecimal salePrice;

    /** 早餐类型 */
    private Integer breakfastType;

    /** 早餐数量 */
    private Integer breakfastNum;

    /** 房态 */
    private Integer roomStatus;

    /** 预订条款类型 */
    private Integer bookRestrictionType;

    /** 提前预定的天数 */
    private Integer bookRestrictionDay;

    /** 提前预定天数 Day 天之中的 time 点 */
    private String bookRestrictionTime;

    /** 入住条款类型 */
    private Integer occupancyRestrictionType;

    /** 需要入住的天数 */
    private Integer occupancyRestrictionDay;

    /** 最少预订房间数 */
    private Integer minRoomCount;

    /** 最多预订房间数 */
    private Integer maxRoomCount;

    /** 取消条款类型 */
    private Integer cancelRestrictionType;

    /** 需提前取消的天数 */
    private Integer cancelRestrictionDay;

    /** 提前取消的天数的时间点 */
    private String cancelRestrictionTime;

    /**
     * 取消罚金
     */
    private List<CancelPenalties> cancelPenalties;

    //担保条款类型
    private Integer guaranteeType;

    //担保条件
    private String guaranteeCondition;

    //担保费用类型
    private Integer guaranteeFeeType;

    //剩余房量
    private Integer quotaNum;

    //售价币种
    private String currency;

    // 基础协议价
    private BigDecimal basePrice;
}
