package com.tiangong.entity.vo.hotel;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/16 15:46
 */

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class PetPolicyDto {

    //是否可携带宠物 （1：允许；0：不允许）
    private Integer allowPet;

    //是否可携带服务型动物（1：允许；0：不允许）
    private Integer allowServiceAnimal;

    //服务型动物豁免费用/限制（1：豁免；0：不豁免；）
    private Integer exemptionChargeLimit;

    //是否需要提前联系酒店（1：需要；0：不需要；）
    private Integer needContact;

    //是否收费 (0：免费；1-收费)
    private Integer isCharge;

    //收费
    private List<PetPolicyChargeDto> charge;

    private LimitInfoDto limitInfos;
}
