package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NationalityEnum {

    ZERO(1, "所有宾客：所有客人"),
    ONE(2, "内宾：持中国大陆身份证的客人"),
    TWO(3, "港澳台客人：港澳台地区，持回乡证/台胞证等的客人"),
    THREE(4, "外宾：除内宾和港澳台以外的客人"),
    FOUE(5, "其它：可自定义输入特殊类型");

    public final Integer key;
    public final String desc;

    public static Integer nationalityCheck(Integer key) {
        if (key == null) {
            return 0;
        }
        for (NationalityEnum nationalityEnum : NationalityEnum.values()) {
            if (nationalityEnum.key.intValue() == key.intValue()) {
                return nationalityEnum.key;
            }
        }
        return null;
    }
}
