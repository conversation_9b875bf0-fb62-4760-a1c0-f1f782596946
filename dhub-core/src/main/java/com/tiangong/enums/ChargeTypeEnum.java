package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChargeTypeEnum {

    ONE(1, 0, "每日"),
    TWO(2, 1, "每小时"),
    THREE(3, 2, "每周"),
    FOUR(4, 3, "每次入住"),
    FIVE(5, 4, "每次进出"),
    ;

    private final Integer type;
    private final Integer basicType;
    private final String name;

    public static Integer chargeTypeBasic(int basicType) {
        for (ChargeTypeEnum chargeTypeEnum : ChargeTypeEnum.values()) {
            if (chargeTypeEnum.basicType == basicType) {
                return chargeTypeEnum.type;
            }
        }
        return null;
    }

}
