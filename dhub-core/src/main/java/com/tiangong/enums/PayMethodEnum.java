package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum PayMethodEnum {

    COMPANY(1, "企业支付"),
    PERSON(2, "个人支付");

    private final Integer type;
    private final String name;

    public static Integer payMethodCheck(Integer type) {
        if (type == null) {
            return null;
        }
        for (PayMethodEnum payMethodEnum : PayMethodEnum.values()) {
            if (Objects.equals(payMethodEnum.type, type)) {
                return payMethodEnum.type;
            }
        }
        return null;
    }
}
