package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GuaranteeFlagEnum {

    ZERO(0, "未担保"),
    ONE(1, "担保");

    public final Integer key;
    public final String desc;

    public static Integer guaranteeFlag(Integer key) {
        if (key == null) {
            return 0;
        }
        for (GuaranteeFlagEnum guaranteeFlagEnum : GuaranteeFlagEnum.values()) {
            if (guaranteeFlagEnum.key.intValue() == key.intValue()) {
                return guaranteeFlagEnum.key;
            }
        }
        return null;
    }
}
