package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IdCardTypeEnum {

    ZERO(1, "身份证"),
    ONE(2, "驾驶证"),
    TWO(3, "护照"),
    THREE(4, "回乡证"),
    FOUE(5, "台胞证"),
    FIVE(6, "港澳台居民居住证"),
    ;

    public final Integer key;
    public final String desc;

    public static Integer idCardTypeCheck(Integer key) {
        if (key == null) {
            return 0;
        }
        for (IdCardTypeEnum idCardTypeEnum : IdCardTypeEnum.values()) {
            if (idCardTypeEnum.key.intValue() == key.intValue()) {
                return idCardTypeEnum.key;
            }
        }
        return null;
    }
}
