package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BreakfastTypeEnum {

    CHINESE(1, 28, "中早"),
    WEST(2, 27, "西早"),
    SELF(3, 0, "自助早"),//产品级别
    FOUR(13, 109, "素食"),
    FIVE(9, 110, "清真"),
    <PERSON><PERSON>(12, 111, "全素食"),
    SEVEN(3, 114, "日式"),//酒店政策级别
    EIGHT(4, 115, "美式"),
    NINE(5, 120, "亚洲风味"),
    TEN(6, 166, "欧陆式"),
    ELEVEN(8, 167, "无麸质"),
    TWELVE(7, 169, "全套英式/爱尔兰式"),
    THIRTEEN(11, 170, "犹太洁食"),
    FOURTEEN(10, 121, "意式"),
    ;

    private final Integer type;
    private final Integer basicType;
    private final String name;

    public static Integer breakfastTypeCheck(Integer key) {
        if (key == null) {
            return 0;
        }
        for (BreakfastTypeEnum breakfastTypeEnum : BreakfastTypeEnum.values()) {
            if (breakfastTypeEnum.type == key.intValue()) {
                return breakfastTypeEnum.type;
            }
        }
        return null;
    }

    public static Integer breakfastTypeToBasic(Integer basicType) {
        if (basicType == null) {
            return null;
        }
        //西式--1，中式--2,上面的是产品级别的枚举数据
        for (BreakfastTypeEnum breakfastTypeEnum : BreakfastTypeEnum.values()) {
            if (breakfastTypeEnum.basicType == basicType.intValue()) {
                if (breakfastTypeEnum.type == 1) {
                    return 2;
                }
                if (breakfastTypeEnum.type == 2) {
                    return 1;
                }
                return breakfastTypeEnum.type;
            }
        }
        return null;
    }

}
