package com.tiangong.enums;

import com.tiangong.dis.dto.InvoiceNotifyPushRequest;
import com.tiangong.entity.base.Header;
import com.tiangong.entity.request.basic.*;
import com.tiangong.entity.request.hotel.*;
import com.tiangong.entity.request.order.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApiNameEnum {

    HEADER("header", Header.class, "非API接口，只用于定义jsonSchema用到"),

    queryHotelIdList("queryHotelIdList", HotelIdListInfoRequest.class, "查询酒店ID列表"),
    queryHotelInfo("queryHotelInfo", HotelInfoRequest.class, "查询酒店信息"),
    queryHotelIncrement("queryHotelIncrement", HotelIncrementRequest.class, "查询酒店信息变化增量"),
    queryHotelImage("queryHotelImage", HotelImageRequest.class, "查询酒店图片"),

    queryHotelLowestPrice("queryHotelLowestPrice", HotelLowestPriceInfoRequest.class, "查询酒店起价信息"),
//    queryHotelAllList("queryHotelAllList", HotelPriceRequest.class, "酒店列表查询接口"),
//    queryHotelMainInfoByHotelIds("queryHotelMainInfoByHotelIds", HotelMainInfoRequest.class, "酒店列表查询接口"),
    queryProductDetail("queryProductDetail", ProductDetailInfoRequest.class, "酒店实时产品查询接口"),

    orderStatusPush("orderStatusPush", OrderStatusPushRequest.class, "订单状态推送"),
    orderCheckDetailInfoPush("orderCheckDetailInfoPush", OrderCheckDetailInfoRequest.class, "订单入住详细情况推送"),

    checkBooking("checkBooking", CheckBookingInfoRequest.class, "试预订(可订检查)接口"),
    createOrder("createOrder", CreateOrderInfoRequest.class, "创建订单接口"),
    cancelOrder("cancelOrder", CancelOrderInfoRequest.class, "取消订单接口"),
    queryOrderDetail("queryOrderDetail", OrderDetailInfoRequest.class, "订单详情查询接口"),
    payOrder("payOrder", PayOrderInfoRequest.class, "订单支付接口"),
    refundNoticeOrder("refundNoticeOrder", PayOrderInfoRequest.class, "订单退款通知接口"),
    queryCreditBalance("queryCreditBalance", QueryCreditBalanceInfoRequest.class, "信用额度查询接口"),
    createInvoice("createInvoice", CreateInvoiceInfoRequest.class, "订单开票接口"),
    pushInvoiceStatus("pushInvoiceStatus", InvoiceNotifyPushRequest.class, "订单开票状态推送接口"),
    queryOrderCheckDetailInfo("queryOrderCheckDetailInfo", OrderCheckRequest.class, "订单入住详细情况查询接口"),

    checkoutApply("checkoutApply", CheckoutApplyRequest.class, "退房申请接口"),

    queryCheckoutDetail("queryCheckoutDetail", CheckoutDetailRequest.class, "查询退房详情接口"),

    checkoutApplyStatusPush("checkoutApplyStatusPush", CheckoutDetailRequest.class, "订单退房申请状态通知推送接口"),

    orderDeductionPush("orderDeductionPush", OrderDeductionPushRequest.class, "订单担保扣款通知推送接口"),


    queryCountryList("queryCountryList", QueryCountryListRequest.class, "查询国家信息"),
    queryCityList("queryCityList", CityListRequest.class, "查询城市信息"),
    queryDistrictList("queryDistrictList", DistrictListRequest.class, "查询行政区信息"),
    queryBusinessList("queryBusinessList", BusinessListRequest.class, "查询商业区信息"),
    queryHotelPlateList("queryHotelPlateList", BusinessListRequest.class, "品牌信息查询接口"),
//    queryCityAndHotelByKeyword("queryCityAndHotelByKeyword", BusinessListRequest.class, "城市和酒店查询接口"),
    queryDistrictAndBusiness("queryDistrictAndBusiness", BusinessListRequest.class, "查询行政区和商业区"),
    queryHotCityList("queryHotCityList", CityRequest.class, "查询热门城市"),
    getAddress("getDestination", AddressRequest.class, "查询目的地"),
    getHotelSearch("getHotelSearch", HotelSearchRequest.class, "获取酒店筛选条件"),
    findHotelList("findHotelList", HotelSearchRequest.class, "获取酒店列表"),//新接口
    ;

    public final String apiName;
    public final Class<?> clazz;
    public final String desc;

    public static Class<?> getClazzByApiName(String apiName) {
        Class<?> clazz = null;
        for (ApiNameEnum apiNameEnum : ApiNameEnum.values()) {
            if (apiNameEnum.apiName.equals(apiName)) {
                clazz = apiNameEnum.clazz;
                break;
            }
        }
        return clazz;
    }
}
