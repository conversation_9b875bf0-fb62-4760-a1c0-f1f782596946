package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FacilityEnum {

    FC1001(1, "FC1001"),
    FC1002(1, "FC1002"),
    FC1003(1, "FC1003"),
    FC1004(1, "FC1004"),
    FC1005(1, "FC1005"),
    FC1006(1, "FC1006"),
    FC1007(1, "FC1007"),
    FC1008(1, "FC1008"),
    FC1009(1, "FC1009"),
    FC1010(1, "FC1010"),
    FC1011(1, "FC1011"),
    FC1012(1, "FC1012"),
    FC1013(1, "FC1013"),
    FC1014(1, "FC1014"),
    FC1015(1, "FC1015"),
    FC1016(1, "FC1016"),
    FC2001(2, "FC2001"),
    FC2002(2, "FC2002"),
    FC2003(2, "FC2003"),
    FC2004(2, "FC2004"),
    FC2005(2, "FC2005"),
    FC2006(2, "FC2006"),
    FC2007(2, "FC2007"),
    FC2008(2, "FC2008"),
    FC2009(2, "FC2009"),
    FC2010(2, "FC2010"),
    FC2011(2, "FC2011"),
    FC2012(2, "FC2012"),
    FC2013(2, "FC2013"),
    FC2014(2, "FC2014"),
    FC2015(2, "FC2015"),
    FC2016(2, "FC2016"),
    FC2017(2, "FC2017"),
    FC2018(2, "FC2018"),
    FC2019(2, "FC2019"),
    FC2020(2, "FC2020"),
    FC2021(2, "FC2021"),
    FC2022(2, "FC2022"),

    ;

    public final Integer type;
    public final String code;

    public static String checkHotelFacility(String code) {
        for (FacilityEnum facilityEnum : FacilityEnum.values()) {
            if (facilityEnum.type == 1 && facilityEnum.code.equals(code)) {
                return facilityEnum.code;
            }
        }
        return null;
    }

    public static String checkRoomFacility(String code) {
        for (FacilityEnum facilityEnum : FacilityEnum.values()) {
            if (facilityEnum.type == 2 && facilityEnum.code.equals(code)) {
                return facilityEnum.code;
            }
        }
        return null;
    }
}
