package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DhubSettingEnum {

    HOTELFACILITYNEW("hotelFacilityNew", "酒店设施"),
    BREAKFAST("breakfast", "早餐政策"),
    IMPORTANTNOTICES("importantNotices", "重要通知"),
    PARKING("parking", "停车场"),
    CHARGINGPARKING("chargingParking", "充电车位"),
    HOTELCERTIFICATES("hotelCertificates", "酒店资质"),
    COMMENT("comment", "酒店评分"),
    HOTELMEETINGINFOS("hotelMeetingInfos", "酒店会议室信息"),
    HOTELVIDEOINFOS("hotelVideoInfos", "酒店视频信息"),
    HOTELTEXTPOLICIES("hotelTextPolicies", "酒店文本政策"),
    CHILDPOLICY("hotelStructuredPolicies.childPolicy", "儿童政策"),
    EXTRABEDPOLICY("hotelStructuredPolicies.extraBedPolicy", "加床政策"),
    PETPOLICY("hotelStructuredPolicies.petPolicy", "宠物政策"),
    LOCATION("location", "位置");

    public final String key;
    public final String value;
}
