package com.tiangong.service.base;

import com.tiangong.entity.request.hotel.HotelLowestPriceInfoRequest;
import com.tiangong.entity.request.hotel.ProductDetailInfoRequest;
import com.tiangong.entity.response.hotel.HotelLowestPriceInfoResponse;
import com.tiangong.entity.response.hotel.ProductDetailInfoResponse;

public interface ProductService {

    /**
     * 酒店每日起价查询
     */
    HotelLowestPriceInfoResponse queryHotelLowestPrice(HotelLowestPriceInfoRequest request);

    /**
     * 酒店实时产品查询
     */
    ProductDetailInfoResponse queryProductDetail(ProductDetailInfoRequest request);

}
