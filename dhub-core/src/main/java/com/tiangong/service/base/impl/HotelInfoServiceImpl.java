package com.tiangong.service.base.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.common.Response;
import com.tiangong.config.DhubConfig;
import com.tiangong.config.agentConfig.AgentConfig;
import com.tiangong.config.exception.CustomException;
import com.tiangong.constant.Constant;
import com.tiangong.convert.DTOConvert;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.AreaRemote;
import com.tiangong.dis.remote.HotelLabelConfigRemote;
import com.tiangong.dis.remote.HotelProductRemote;
import com.tiangong.dis.remote.ProductRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.DistrictOrBusinessResp;
import com.tiangong.dto.hotel.HotelListResp;
import com.tiangong.dto.hotel.HotelPageReq;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.entity.base.AgentAccountConfig;
import com.tiangong.entity.dto.base.*;
import com.tiangong.entity.dto.base.BrandDTO;
import com.tiangong.entity.dto.base.GroupDTO;
import com.tiangong.entity.request.basic.DistrictOrBusinessReq;
import com.tiangong.entity.request.basic.SearchCityAndHotelReq;
import com.tiangong.entity.request.hotel.*;
import com.tiangong.entity.response.basic.DistrictAndBusinessResponse;
import com.tiangong.entity.response.basic.HotelAndCityResponse;
import com.tiangong.entity.response.basic.HotelLabel;
import com.tiangong.entity.response.hotel.*;
import com.tiangong.entity.vo.hotel.*;
import com.tiangong.enums.*;
import com.tiangong.exception.GlobalErrorCodeConstants;
import com.tiangong.product.dto.HotelPriceReq;
import com.tiangong.product.resp.HotelLabelDTO;
import com.tiangong.product.resp.HotelPriceDTO;
import com.tiangong.service.base.HotelInfoService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.HttpUtilX;
import com.tiangong.util.StrUtilX;
import com.tiangong.utils.CommonUtil;
import com.tiangong.utils.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class HotelInfoServiceImpl implements HotelInfoService {

    @Autowired
    private DhubConfig dhubConfig;

    @Autowired
    private ProductRemote productRemote;

    @Autowired
    private AreaRemote areaRemote;

    @Autowired
    private HotelLabelConfigRemote hotelLabelConfigRemote;

    @Autowired
    private HotelProductRemote hotelProductRemote;

    /**
     * 酒店列表查询接口
     */
    @Override
    public HotelIdListInfoResponse queryHotelList(HotelIdListInfoRequest request) {
        HotelIdListInfoResponse hotelIdListInfoResponse = new HotelIdListInfoResponse();
        if (CheckInTypeEnum.checkType(request.getCheckInType()) == null) {
            throw new CustomException(DhubReturnCodeEnum.CHECK_IN_TYPE_ERROR.no, DhubReturnCodeEnum.CHECK_IN_TYPE_ERROR.code, DhubReturnCodeEnum.CHECK_IN_TYPE_ERROR.code);
        }
        HotelIdListRequest hotelIdListRequest = new HotelIdListRequest();
        hotelIdListRequest.setCityCode(request.getCityCode());
        if (request.getMaxId() == null) {
            if (request.getPageNo() == null) {
                hotelIdListRequest.setPageNo(1);
            } else {
                hotelIdListRequest.setPageNo(request.getPageNo());
            }
        }
        hotelIdListRequest.setPageSize(request.getPageSize());
        hotelIdListRequest.setMaxId(request.getMaxId());
        hotelIdListRequest.setAgentCode(request.getAgentCode());
        ResponseResult<HotelIdListResponse> hotelProductResponseResponseResult = hotelProductRemote.queryHotelIdList(hotelIdListRequest);
        CommonUtil.checkResponseResult(hotelProductResponseResponseResult);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(hotelProductResponseResponseResult.getReturnCode())) {
            DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(hotelProductResponseResponseResult.getReturnCode());
            throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
        }
        if (hotelProductResponseResponseResult.getBussinessResponse() != null) {
            HotelIdListResponse businessResponse = hotelProductResponseResponseResult.getBussinessResponse();
            hotelIdListInfoResponse = DTOConvert.INSTANCE.convert(businessResponse);
        }
        return hotelIdListInfoResponse;
    }

    /**
     * 校验指定参数列表,并返回基础信息参数
     */
    public List<String> checkSetting(List<String> settings) {
        List<String> list = new ArrayList<>();
        for (String setting : settings) {
            String key = HotelSettingEnum.getKey(setting);
            if (null == key) {
                throw new CustomException(DhubReturnCodeEnum.SETTINGS_PARAM_ERROR.no, DhubReturnCodeEnum.SETTINGS_PARAM_ERROR.code, DhubReturnCodeEnum.SETTINGS_PARAM_ERROR.code + ":" + setting);
            }
            list.add(key);
        }
        return list;
    }

    @Override
    public HotelInfoResponse queryHotelInfo(HotelInfoRequest request) {
        HotelInfoResponse hotelInfoResponse = new HotelInfoResponse();
        CommonReqDTO commonReqDTO = new CommonReqDTO();
        commonReqDTO.setLanguageType(request.getLanguage());
        commonReqDTO.setHotelIds(request.getHotelIds());
        //获取基础key值
        commonReqDTO.setSettings(HotelSettingEnum.getBaseKey());

        List<String> settings = request.getSettings();
        if (CollUtilX.isEmpty(settings)) {
            settings = new ArrayList<>();
        } else {
            //校验key值
            List<String> list = checkSetting(settings);
            //获取基础信息key值
            //用户按需新增key值
            commonReqDTO.getSettings().addAll(list);
        }


        HotelInfoReq req = new HotelInfoReq();
        //修改为按需获取数据
        req.setSettings(new HashSet<>(commonReqDTO.getSettings()));
        req.setHotelIds(request.getHotelIds());
        req.setLanguageType(request.getLanguage());
        Response<String> stringResponse = hotelProductRemote.queryHotelInfoList(req);
        if (stringResponse == null) {
            log.error("酒店信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        if (stringResponse.isError()) {
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        String s = stringResponse.getModel();

        HotelPriceReq hotelPriceReq = new HotelPriceReq();
        hotelPriceReq.setHotelIdList(request.getHotelIds());
        hotelPriceReq.setAgentCode(request.getAgentCode());
        Response<List<HotelLabelDTO>> listResponse = productRemote.selectAgentHotelLabel(hotelPriceReq);
        Map<Long, Set<String>> hotelLabelMap = new HashMap<>();
        if (listResponse != null && listResponse.isSuccess()) {
            if (listResponse.getModel() != null) {
                for (HotelLabelDTO hotelLabelDTO : listResponse.getModel()) {
                    hotelLabelMap.put(hotelLabelDTO.getHotelId(), hotelLabelDTO.getHotelLabelNameList());
                }
            }
        } else {
            log.error("客户酒店标签失败,失败原因:" + JSON.toJSONString(listResponse));
            throw new CustomException(DhubReturnCodeEnum.getKeyByValue(listResponse.getFailCode()), listResponse.getFailCode(), listResponse.getFailCode());
        }
        List<HotelInfoCollectionDTO> hotelInfoCollectionList = JSON.parseObject(s, new TypeReference<List<HotelInfoCollectionDTO>>() {
        });


        if (hotelInfoCollectionList == null || hotelInfoCollectionList.size() == 0) {
            return hotelInfoResponse;
        }
        List<HotelInfo> hotelInfos = new ArrayList<>();
        HotelInfo hotelInfo;
        for (HotelInfoCollectionDTO hotelInfoCollection : hotelInfoCollectionList) {
            Long hotelId = hotelInfoCollection.getHotelId();
            try {
                hotelInfo = new HotelInfo();
                hotelInfo.setHotelId(hotelId);
                Set<String> labelNames = hotelLabelMap.get(hotelId);
                if (CollUtilX.isNotEmpty(labelNames)) {
                    hotelInfo.setHotelLabelNameList(new ArrayList<>(labelNames));
                }
                //酒店一级分类
                if (hotelInfoCollection.getHotelCategory() != null) {
                    hotelInfo.setHotelCategory(hotelInfoCollection.getHotelCategory());
                }
                //酒店二级分类
                if (hotelInfoCollection.getHotelSubCategory() != null) {
                    hotelInfo.setHotelSubCategory(hotelInfoCollection.getHotelSubCategory());
                }

                hotelInfo.setHotFacility(hotelInfoCollection.getHotFacility());

                hotelInfo.setHotelName(hotelInfoCollection.getHotelName());
                hotelInfo.setHotelEngName(hotelInfoCollection.getHotelEngName());
                LocationDTO location = hotelInfoCollection.getLocation();
                if (location != null) {
                    hotelInfo.setAddress(location.getAddress());
                    hotelInfo.setCountryCode(location.getCountry());
                    hotelInfo.setCountryName(location.getCountryName());
                    hotelInfo.setCity(location.getCity());
                    hotelInfo.setCityName(location.getCityName());
                }

                //酒店主图
                List<ImageDTO> images = hotelInfoCollection.getImages();
                if (CollUtilX.isNotEmpty(images)) {

                    String hotelMainUrl = null;
                    //备用酒店图片
                    String backHotelMainUrl = null;
                    //备用房型图片
                    String backRoomMainUrl = null;

                    for (ImageDTO imageRespDTO : images) {
                        //获取酒店临时主图 在主图不存在时进行替换
                        if (imageRespDTO.getImgType() != null && imageRespDTO.getImgType() != 1 && backHotelMainUrl == null) {
                            backHotelMainUrl = imageRespDTO.getImgUrl();
                        }
                        //
                        if (imageRespDTO.getImgType() != null && imageRespDTO.getImgType() == 1 && backRoomMainUrl == null) {
                            if (backRoomMainUrl == null) {
                                backRoomMainUrl = imageRespDTO.getImgUrl();
                            }
                        }

                        String isMainImg = imageRespDTO.getIsMainImg();
                        if (StrUtilX.isNotEmpty(isMainImg) && String.valueOf(Constant.ACTIVE_1).equals(isMainImg)) {
                            hotelMainUrl = imageRespDTO.getImgUrl();
                            break;
                        }
                    }
                    //如果主图不存在则使用临时主图 酒店图片优先于房型图片
                    if (StrUtil.isBlank(hotelMainUrl)) {
                        hotelMainUrl = backHotelMainUrl;
                    }
                    if (StrUtil.isBlank(hotelMainUrl)) {
                        hotelMainUrl = backRoomMainUrl;
                    }
                    hotelInfo.setAppearancePicUrl(hotelMainUrl);
                }
                hotelInfo.setTelephone(hotelInfoCollection.getTel());
                hotelInfo.setHotelIntroduce(hotelInfoCollection.getIntroduce());
                hotelInfo.setFax(hotelInfoCollection.getFax());
                HotelStar hotelStar = hotelInfoCollection.getHotelStar();
                if (hotelStar != null) {
                    hotelInfo.setHotelStar(Integer.valueOf(hotelStar.getHotelStar()));
                }
                if (hotelInfoCollection.getPraciceDate() != null) {
                    hotelInfo.setPraciceDate(DateUtilX.dateToString(hotelInfoCollection.getPraciceDate()));
                }
                if (hotelInfoCollection.getFitmentDate() != null) {
                    hotelInfo.setFitmentDate(DateUtilX.dateToString(hotelInfoCollection.getFitmentDate()));
                }
                GroupDTO groupDTO = hotelInfoCollection.getGroup();
                //集团
                if (groupDTO != null) {
                    hotelInfo.setParentHotelGroup(groupDTO.getGroupId());
                    hotelInfo.setParentHotelGroupName(groupDTO.getGroupName());
                }
                //品牌
                BrandDTO brandDTO = hotelInfoCollection.getBrand();
                if (brandDTO != null) {
                    hotelInfo.setPlateID(brandDTO.getBrandId());
                    hotelInfo.setPlateName(brandDTO.getBrandName());
                }
                //行政区
                District district = hotelInfoCollection.getDistrict();
                if (district != null) {
                    hotelInfo.setDistinct(district.getDistrict());
                    hotelInfo.setDistinctName(district.getDistrictName());
                }
                //商业区
                Business businesszone = hotelInfoCollection.getBusinesszone();
                if (businesszone != null) {
                    hotelInfo.setBusiness(businesszone.getBusinesszone());
                    hotelInfo.setBusinessName(businesszone.getBusinesszoneName());
                }
                //百度经纬度
                GeoInfoDTO geoInfo = hotelInfoCollection.getGeoInfo();
                if (geoInfo != null) {
                    if (geoInfo.getLngBaidu() != null) {
                        hotelInfo.setLongitude(geoInfo.getLngBaidu().doubleValue());
                    }
                    if (geoInfo.getLatBaidu() != null) {
                        hotelInfo.setLatitude(geoInfo.getLatBaidu().doubleValue());
                    }
                    if (geoInfo.getLngGoogle() != null) {
                        hotelInfo.setLngGoogle(geoInfo.getLngGoogle().doubleValue());
                    }
                    if (geoInfo.getLatGoogle() != null) {
                        hotelInfo.setLatGoogle(geoInfo.getLatGoogle().doubleValue());
                    }
                }

                hotelInfo.setRoomNum(hotelInfoCollection.getRoomCount());

                //可接待人群
                InOutTimeDTO inOutPolicy = hotelInfoCollection.getInOutPolicy();
                if (inOutPolicy != null) {
                    hotelInfo.setApplicableGuest(inOutPolicy.getAccepCustom());
                    hotelInfo.setCheckInTime(inOutPolicy.getCheckInTime());
                    hotelInfo.setCheckInLateTime(inOutPolicy.getCheckInLateTime());
                    hotelInfo.setCheckOutTime(inOutPolicy.getCheckOutTime());
                    hotelInfo.setCheckOutEarlyTime(inOutPolicy.getCheckOutEarlyTime());
                }

                //酒店设施
                if (settings.contains(DhubSettingEnum.HOTELFACILITYNEW.key)) {
                    List<HotelFacilityNewResponseDto> hotelFacilityNews = convertHotelFacility(hotelInfoCollection.getFacilities());
                    hotelInfo.setHotelFacilityNew(hotelFacilityNews);
                }

                //儿童政策，加床政策，宠物政策
                HotelStructuredPoliciesDto hotelStructuredPoliciesDto = new HotelStructuredPoliciesDto();

                //加床政策
                if (settings.contains(DhubSettingEnum.EXTRABEDPOLICY.key)) {
                    ExtraBedPolicyDto extraBedPolicyDto = convertExtraBedPolicy(hotelInfoCollection.getAddBedPolicy());
                    hotelStructuredPoliciesDto.setExtraBedPolicy(extraBedPolicyDto);
                }
                //儿童政策
                if (settings.contains(DhubSettingEnum.CHILDPOLICY.key)) {
                    ChildPolicyDto childPolicy = convertChildPolicy(hotelInfoCollection.getChildPolicy());
                    hotelStructuredPoliciesDto.setChildPolicy(childPolicy);
                }
                //宠物政策
                if (settings.contains(DhubSettingEnum.PETPOLICY.key)) {
                    PetPolicyDto petPolicyDto = convertpetPolicy(hotelInfoCollection.getPetPolicy());
                    hotelStructuredPoliciesDto.setPetPolicy(petPolicyDto);
                }
                if (hotelStructuredPoliciesDto.getChildPolicy() != null
                        || hotelStructuredPoliciesDto.getExtraBedPolicy() != null
                        || hotelStructuredPoliciesDto.getPetPolicy() != null) {
                    hotelInfo.setHotelStructuredPolicies(hotelStructuredPoliciesDto);
                }

                //早餐
                if (settings.contains(DhubSettingEnum.BREAKFAST.key)) {
                    BreakFastDTO breakfast = hotelInfoCollection.getBreakfast();
                    if (breakfast != null) {
                        List<BreakfastResponseDto> breakfasts = new ArrayList<>();
                        BreakfastResponseDto breakfastResponseDto = DTOConvert.INSTANCE.breakfastConvert(breakfast);
                        // BeanUtils.copyProperties(breakfast, breakfastResponseDto);
                        if (breakfastResponseDto != null) {
                            try {
                                if (!StringUtils.isEmpty(breakfastResponseDto.getBreakfastType())) {
                                    String[] breakfastTypeStr = breakfastResponseDto.getBreakfastType().split(",");
                                    StringBuilder breakfastType = new StringBuilder();
                                    for (String type : breakfastTypeStr) {
                                        Integer breakfastTypeToBasic = BreakfastTypeEnum.breakfastTypeToBasic(Integer.parseInt(type));
                                        breakfastType.append(breakfastTypeToBasic).append(",");
                                    }
                                    breakfastResponseDto.setBreakfastType(breakfastType.substring(0, breakfastType.length() - 1));
                                    if (!StringUtils.isEmpty(breakfastResponseDto.getBreakfastVariety())) {
                                        String[] breakfastVarietyStr = breakfastResponseDto.getBreakfastVariety().split(",");
                                        StringBuilder breakfastVariety = new StringBuilder();
                                        for (String type : breakfastVarietyStr) {
                                            Integer breakfastTypeToBasic = BreakfastVarietyEnum.BreakfastVarietyBasic(Integer.parseInt(type));
                                            breakfastVariety.append(breakfastTypeToBasic).append(",");
                                        }
                                        breakfastResponseDto.setBreakfastVariety(breakfastVariety.substring(0, breakfastVariety.length() - 1));
                                    }
                                }
                            } catch (Exception e) {
                                log.error("早餐类型解析异常", e);
                            }
                        }
                        breakfasts.add(breakfastResponseDto);
                        hotelInfo.setBreakfast(breakfasts);
                    }
                }

                ParkingDTO parkings = hotelInfoCollection.getParkings();
                if (parkings != null) {
                    hotelInfo.setIsPark(parkings.getIsPark());
                    hotelInfo.setIsChargePark(parkings.getIsChargePark());
                }
                //停车场信息
                if (settings.contains(DhubSettingEnum.PARKING.key)) {
                    List<HotelParkingDto> parking = convertParking(parkings);
                    try {
                        if (CollUtilX.isNotEmpty(parking)) {
                            for (HotelParkingDto dto : parking) {
                                if (dto == null) continue;
                                if (!StringUtils.isEmpty(dto.getChargeType())) {
                                    dto.setChargeType(ChargeTypeEnum.chargeTypeBasic(Integer.parseInt(dto.getChargeType())) + "");
                                }
                            }
                        }

                    } catch (Exception e) {
                        log.error("解析停车场信息异常", e);
                    }
                    hotelInfo.setParking(parking);
                }
                //充电桩信息
                if (settings.contains(DhubSettingEnum.CHARGINGPARKING.key)) {
                    List<HotelChargingParkingDto> hotelChargingParkingDtos = convertChargingParking(parkings);
                    hotelInfo.setChargingParking(hotelChargingParkingDtos);
                }
                //重要通知
                if (settings.contains(DhubSettingEnum.IMPORTANTNOTICES.key)) {
                    InformDTO inform = hotelInfoCollection.getInform();
                    if (inform != null) {
                        List<HotelInformDTO> informList = inform.getInformList();
                        if (CollUtilX.isNotEmpty(informList)) {
                            List<HotelInformDto> hotelInformDtos = DTOConvert.INSTANCE.hotelInformListConvert(informList);
                            hotelInfo.setImportantNotices(hotelInformDtos);
                        }
                    }
                }
                //酒店资质
                if (settings.contains(DhubSettingEnum.HOTELCERTIFICATES.key)) {
                    List<HotelCertificationDTO> certificates = hotelInfoCollection.getCertificates();
                    if (CollUtilX.isNotEmpty(certificates)) {
                        List<HotelCertificationDto> hotelCertificates = new ArrayList<>();
                        HotelCertificationDto hotelCertificationDto;
                        for (HotelCertificationDTO certificate : certificates) {
                            hotelCertificationDto = new HotelCertificationDto();
                            hotelCertificationDto.setCertificationName(certificate.getCertificationName());
                            hotelCertificationDto.setFileUrl(certificate.getFileUrl());
                            hotelCertificationDto.setUnifyCode(certificate.getUnifyCode());
                            hotelCertificates.add(hotelCertificationDto);
                        }
                        hotelInfo.setHotelCertificates(hotelCertificates);
                    }
                }

                //评分
                if (settings.contains(DhubSettingEnum.COMMENT.key)) {
                    HotelRatingsDTO rating = hotelInfoCollection.getRating();
                    if (rating != null) {
                        List<CommentDto> comments = new ArrayList<>();
                        CommentDto commentDto = new CommentDto();
                        commentDto.setAverage_score(rating.getOverall() != null ? String.valueOf(rating.getOverall()) : null);
                        commentDto.setChannel(rating.getSource());
                        comments.add(commentDto);
                        hotelInfo.setComment(comments);
                    } else {
                        //评分不存在的时候返回设置的默认评分
                        AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(request.getPartnerCode());
                        List<CommentDto> comments = new ArrayList<>();
                        CommentDto commentDto = new CommentDto();
                        commentDto.setAverage_score(Objects.nonNull(partnerInfo.getRating()) ? partnerInfo.getRating() + "" : null);
                        commentDto.setChannel("");
                        comments.add(commentDto);
                        hotelInfo.setComment(comments);
                    }
                }
                //房型信息
                List<RoomInfo> roomInfos = convertRoomInfo(hotelInfoCollection.getRooms());
                hotelInfo.setRoomInfos(roomInfos);

                //酒店视频信息
                if (settings.contains(DhubSettingEnum.HOTELVIDEOINFOS.key)) {
                    List<HotelVideoInfo> hotelVideoInfos = convertHotelVideo(hotelInfoCollection.getVideoInfos());
                    hotelInfo.setHotelVideoInfos(hotelVideoInfos);
                }

                //会议室数据
                if (settings.contains(DhubSettingEnum.HOTELMEETINGINFOS.key)) {
                    List<MeetingTypeInfoDTO> hotelMeetingInfos = convertBedTypeMeeting(hotelInfoCollection.getMeetings());
                    hotelInfo.setHotelMeetingInfos(hotelMeetingInfos);
                }
                //酒店文本政策
                if (settings.contains(DhubSettingEnum.HOTELTEXTPOLICIES.key)) {
                    hotelInfo.setHotelTextPolicies(hotelInfoCollection.getHotelTextPolicyList());
                }

                hotelInfos.add(hotelInfo);
            } catch (Exception e) {
                log.error("转换酒店信息异常，酒店id：{} ,", hotelId, e);
                CommonUtils.saveErrorSlsLog(new Date(), "queryHotelInfo", String.valueOf(hotelId), StrUtilX.getStackTraceAsString(e), "999", request.getPartnerCode());
            }
        }

        hotelInfoResponse.setHotelInfos(hotelInfos);
        return hotelInfoResponse;
    }

    private PetPolicyDto convertpetPolicy(PetDTO petPolicy) {
        if (petPolicy == null) {
            return null;
        }
        PetPolicyDto petPolicyDto = new PetPolicyDto();
        if (StrUtilX.isNotEmpty(petPolicy.getAllowPet())) {
            petPolicyDto.setAllowPet(Integer.valueOf(petPolicy.getAllowPet()));
        }
        petPolicyDto.setAllowServiceAnimal(petPolicy.getAllowServiceAnimal());
        petPolicyDto.setExemptionChargeLimit(petPolicy.getServeTypeCharge());
        petPolicyDto.setNeedContact(petPolicy.getContactHotel());
        petPolicyDto.setIsCharge(petPolicy.getCharge());
        List<PetPolicyChargeDto> charge = new ArrayList<>();
        PetPolicyChargeDto petPolicyChargeDto = new PetPolicyChargeDto();
        petPolicyChargeDto.setAmount(petPolicy.getAmount());
        petPolicyChargeDto.setCurrency(petPolicy.getCurrency());
        charge.add(petPolicyChargeDto);
        petPolicyDto.setCharge(charge);
        LimitInfoDto limitInfos = new LimitInfoDto();
        limitInfos.setMaxWeightKg(petPolicy.getWeightKg());
        limitInfos.setMaxWeightLb(petPolicy.getWeightLb());
        limitInfos.setQuantity(petPolicy.getCount());
        List<String> allowKinds = petPolicy.getAllowKinds();
        if (CollUtilX.isNotEmpty(allowKinds)) {
            limitInfos.setAnimalType(StringUtils.join(allowKinds, ","));
        }
        petPolicyDto.setLimitInfos(limitInfos);
        return petPolicyDto;
    }

    private ChildPolicyDto convertChildPolicy(ChildPolicyDTO childPolicyDTO) {
        if (childPolicyDTO == null) {
            return null;
        }
        ChildPolicyDto childPolicy = new ChildPolicyDto();
        childPolicy.setAllowChildrenToStay(childPolicyDTO.getAllowedUnderage());
        childPolicy.setLimitOfChildAge(childPolicyDTO.getAllowedAge());

        //儿童使用现有床政策
        List<HotelChargeInfoDTO> childUseExistingBedList = childPolicyDTO.getChildUseExistingBedList();
        if (CollUtilX.isNotEmpty(childUseExistingBedList)) {
            ExistingBedDto existingBed = new ExistingBedDto();
            List<ChargeDto> chargeDtos = new ArrayList<>();
            ChargeDto chargeDto;
            for (HotelChargeInfoDTO hotelChargeInfoDTO : childUseExistingBedList) {
                chargeDto = new ChargeDto();
                chargeDto.setRangeFrom(hotelChargeInfoDTO.getChargeMinAge());
                chargeDto.setRangeTo(hotelChargeInfoDTO.getChargeMaxAge());
                chargeDto.setChargeManner(hotelChargeInfoDTO.getChargeManner());
                chargeDto.setChargeValue(hotelChargeInfoDTO.getChargeAmount());
                chargeDto.setCurrency(hotelChargeInfoDTO.getCurrency());
                chargeDto.setChargeFrequency(hotelChargeInfoDTO.getChargeNumber());
                chargeDtos.add(chargeDto);
            }
            existingBed.setCharge(chargeDtos);
            childPolicy.setExistingBed(existingBed);
        }

        //儿童早餐政策
        List<HotelChildBreakfastDTO> hotelChildBreakfastDTOS = childPolicyDTO.getHotelChildBreakfastList();
        if (CollUtilX.isNotEmpty(hotelChildBreakfastDTOS)) {
            ChildBreakfastDto childBreakfastDto = new ChildBreakfastDto();
            childBreakfastDto.setRangeType(childBreakfastDto.getRangeType());
            List<BreakfastChargeDto> breakfastChargeDtos = new ArrayList<>();
            BreakfastChargeDto breakfastChargeDto;
            for (HotelChildBreakfastDTO hotelChildBreakfastDTO : hotelChildBreakfastDTOS) {
                breakfastChargeDto = new BreakfastChargeDto();
                if (hotelChildBreakfastDTO.getChargeMin() != null) {
                    breakfastChargeDto.setRangeFrom(hotelChildBreakfastDTO.getChargeMin().toString());
                }
                if (hotelChildBreakfastDTO.getChargeMax() != null) {
                    breakfastChargeDto.setRangeTo(hotelChildBreakfastDTO.getChargeMax().toString());
                }
                if (hotelChildBreakfastDTO.getChargeManner() != null && hotelChildBreakfastDTO.getChargeManner().equals("0")) {
                    //免费
                    breakfastChargeDto.setAmount(new BigDecimal(0));
                } else {
                    breakfastChargeDto.setAmount(hotelChildBreakfastDTO.getChargeAmount());
                }
                breakfastChargeDto.setCurrency(hotelChildBreakfastDTO.getCurrency());
                breakfastChargeDtos.add(breakfastChargeDto);
            }
            childBreakfastDto.setCharge(breakfastChargeDtos);
            childPolicy.setChildBreakfast(childBreakfastDto);
        }
        return childPolicy;
    }

    private ExtraBedPolicyDto convertExtraBedPolicy(AddBedPolicyDTO addBedPolicy) {
        if (addBedPolicy == null) {
            return null;
        }
        List<HotelChargeInfoDTO> addBedList = addBedPolicy.getAddBedList();
        List<HotelChargeInfoDTO> addChildBedList = addBedPolicy.getAddChildBedList();
        List<ChargeDto> chargeDtos = new ArrayList<>();
        ChargeDto chargeDto;
        if (CollUtilX.isNotEmpty(addBedList)) {
            for (HotelChargeInfoDTO hotelChargeInfoDTO : addBedList) {
                chargeDto = new ChargeDto();
                chargeDto.setRangeFrom(hotelChargeInfoDTO.getChargeMinAge());
                chargeDto.setRangeTo(hotelChargeInfoDTO.getChargeMaxAge());
                chargeDto.setAddBedType(hotelChargeInfoDTO.getChargeType());
                chargeDto.setChargeManner(hotelChargeInfoDTO.getChargeManner());
                chargeDto.setChargeValue(hotelChargeInfoDTO.getChargeAmount());
                chargeDto.setCurrency(hotelChargeInfoDTO.getCurrency());
                chargeDto.setChargeFrequency(hotelChargeInfoDTO.getChargeNumber());
                chargeDtos.add(chargeDto);
            }
        }
        if (CollUtilX.isNotEmpty(addChildBedList)) {
            for (HotelChargeInfoDTO hotelChargeInfoDTO : addChildBedList) {
                chargeDto = new ChargeDto();
                chargeDto.setRangeFrom(hotelChargeInfoDTO.getChargeMinAge());
                chargeDto.setRangeTo(hotelChargeInfoDTO.getChargeMaxAge());
                chargeDto.setAddBedType(hotelChargeInfoDTO.getChargeType());
                chargeDto.setChargeManner(hotelChargeInfoDTO.getChargeManner());
                chargeDto.setChargeValue(hotelChargeInfoDTO.getChargeAmount());
                chargeDto.setCurrency(hotelChargeInfoDTO.getCurrency());
                chargeDto.setChargeFrequency(hotelChargeInfoDTO.getChargeNumber());
                chargeDtos.add(chargeDto);
            }
        }
        if (chargeDtos.size() > 0) {
            ExtraBedPolicyDto extraBedPolicyDto = new ExtraBedPolicyDto();
            extraBedPolicyDto.setCharge(chargeDtos);
            return extraBedPolicyDto;
        } else {
            return null;
        }
    }

    private List<HotelFacilityNewResponseDto> convertHotelFacility(List<FacilityPublicDTO> facilities) {
        if (CollUtilX.isEmpty(facilities)) {
            return null;
        }
        List<HotelFacilityNewResponseDto> hotelFacilityNews = new ArrayList<>();
        HotelFacilityNewResponseDto hotelFacilityNewResponseDto;
        for (FacilityPublicDTO facility : facilities) {
            hotelFacilityNewResponseDto = new HotelFacilityNewResponseDto();
            hotelFacilityNewResponseDto.setFeatureId(facility.getFacilityId().toString());
            hotelFacilityNewResponseDto.setName(facility.getFacilityName());
            hotelFacilityNewResponseDto.setTypeCode(String.valueOf(facility.getDataId()));
            hotelFacilityNewResponseDto.setTypeName(facility.getDataName());
            hotelFacilityNewResponseDto.setCategoryType(facility.getFacilityType() == null ? null : facility.getFacilityType().toString());
            if (StrUtilX.isNotEmpty(facility.getStatus())) {
                hotelFacilityNewResponseDto.setStatus(Integer.valueOf(facility.getStatus()));
            }
            hotelFacilityNewResponseDto.setDescribe(facility.getFacilityDes());

            //房型设施
            List<RoomFacilityDTO> roomFacilityList = facility.getRoomFacilityList();
            if (CollUtilX.isNotEmpty(roomFacilityList)) {
                List<ApplicableRoomTypeID> applicableRoomTypeIDs = new ArrayList<>();
                ApplicableRoomTypeID applicableRoomTypeID;
                for (RoomFacilityDTO roomFacilityDTO : roomFacilityList) {
                    applicableRoomTypeID = new ApplicableRoomTypeID();
                    applicableRoomTypeID.setRoomTypeId(roomFacilityDTO.getRoomtypeId());
                    applicableRoomTypeID.setFacilityDesc(roomFacilityDTO.getDescription());
                    applicableRoomTypeIDs.add(applicableRoomTypeID);
                }
                hotelFacilityNewResponseDto.setApplicableRoomTypeIDs(applicableRoomTypeIDs);
            }
            hotelFacilityNews.add(hotelFacilityNewResponseDto);
        }
        return hotelFacilityNews;
    }

    private List<HotelChargingParkingDto> convertChargingParking(ParkingDTO parkings) throws Exception {
        if (parkings == null) {
            return null;
        }
        List<HotelChargingParkingDTO> chargingParkings = parkings.getChargingParkings();
        if (CollUtilX.isEmpty(chargingParkings)) {
            return null;
        }
        return DTOConvert.INSTANCE.hotelChargingParkingConvert(chargingParkings);
    }


    private List<HotelParkingDto> convertParking(ParkingDTO parkings) {
        if (parkings == null) {
            return null;
        }
        List<HotelParkingDTO> hotelParkingDTOs = parkings.getParkings();
        if (CollUtilX.isEmpty(hotelParkingDTOs)) {
            return null;
        }
        List<HotelParkingDto> parking = new ArrayList<>();
        HotelParkingDto hotelParkingDto;
        for (HotelParkingDTO hotelParkingDTOInfo : hotelParkingDTOs) {
            hotelParkingDto = DTOConvert.INSTANCE.hotelParkConvert(hotelParkingDTOInfo);
            hotelParkingDto.setChargeType(hotelParkingDTOInfo.getChargeManner());
            parking.add(hotelParkingDto);
        }
        return parking;
    }

    private List<MeetingTypeInfoDTO> convertBedTypeMeeting(List<MeetingDTO> meetings) {
        if (CollUtilX.isEmpty(meetings)) {
            return null;
        }
        List<MeetingTypeInfoDTO> hotelMeetingInfos = new ArrayList<>();
        MeetingTypeInfoDTO meetingTypeInfoDTO;
        for (MeetingDTO meeting : meetings) {
            meetingTypeInfoDTO = DTOConvert.INSTANCE.meetingConvert(meeting);
            if (meeting.getFramein() != null) {
                meetingTypeInfoDTO.setFrameIn(meeting.getFramein());
            }
            meetingTypeInfoDTO.setMeetingTypeId(meeting.getMeetingtypeId());
            meetingTypeInfoDTO.setMeetingTypeName(meeting.getMeetingtypeName());
            String imgUrls = meeting.getImgUrls();
            if (StrUtilX.isNotEmpty(imgUrls)) {
                List<ImageInfoResponseDto> meetingImgDTOList = new ArrayList<>();
                ImageInfoResponseDto imageInfoResponseDto;
                for (Map map : JSON.parseArray(imgUrls, Map.class)) {
                    imageInfoResponseDto = new ImageInfoResponseDto();
                    if (map.containsKey("hotelId") && map.get("hotelId") != null) {
                        imageInfoResponseDto.setHotelId(Long.valueOf(map.get("hotelId").toString()));
                    }
                    if (map.containsKey("imgId") && map.get("imgId") != null) {
                        imageInfoResponseDto.setImageId(Long.valueOf(map.get("imgId").toString()));
                    }
                    if (map.containsKey("imgType") && map.get("imgType") != null) {
                        imageInfoResponseDto.setImageType(Integer.valueOf(map.get("imgType").toString()));
                    }
                    if (map.containsKey("imgUrl") && map.get("imgUrl") != null) {
                        imageInfoResponseDto.setOrgImageUrl(map.get("imgUrl").toString());
                    }
                    if (map.containsKey("isRoomMainImg") && map.get("isRoomMainImg") != null) {
                        imageInfoResponseDto.setIsMain(Integer.valueOf(map.get("isRoomMainImg").toString()));
                    }
                    if (map.containsKey("roomtypeId") && map.get("roomtypeId") != null) {
                        imageInfoResponseDto.setRoomId(Long.valueOf(map.get("roomtypeId").toString()));
                    }
                    meetingImgDTOList.add(imageInfoResponseDto);
                }
                meetingTypeInfoDTO.setMeetingImgDTOList(meetingImgDTOList);
            }
            hotelMeetingInfos.add(meetingTypeInfoDTO);
        }
        return hotelMeetingInfos;
    }

    private List<HotelVideoInfo> convertHotelVideo(List<HotelTypeVideoDTO> hotelTypeVideoDTOs) {
        if (CollUtilX.isEmpty(hotelTypeVideoDTOs)) {
            return null;
        }
        List<HotelVideoInfo> hotelVideoInfos = new ArrayList<>();
        HotelVideoInfo hotelVideoInfo;
        for (HotelTypeVideoDTO hotelTypeVideoDTO : hotelTypeVideoDTOs) {
            hotelVideoInfo = new HotelVideoInfo();
            hotelVideoInfo.setBigPosterUrl(hotelTypeVideoDTO.getBigPosterUrl());
            hotelVideoInfo.setPosterUrl(hotelTypeVideoDTO.getPosterUrl());
            String videoType = hotelTypeVideoDTO.getVideoType();
            if (StrUtilX.isNotEmpty(videoType)) {
                hotelVideoInfo.setVideoType(Integer.valueOf(videoType));
            }
            hotelVideoInfo.setVideoUrl(hotelTypeVideoDTO.getVideoUrl());
            hotelVideoInfos.add(hotelVideoInfo);
        }
        return hotelVideoInfos;
    }

    private List<RoomInfo> convertRoomInfo(List<RoomtypeDTO> rooms) {
        if (CollUtilX.isEmpty(rooms)) {
            return null;
        }
        List<RoomInfo> roomInfos = new ArrayList<>();
        RoomInfo roomInfo;
        for (RoomtypeDTO roomtypeDTO : rooms) {
            roomInfo = new RoomInfo();
            roomInfo.setRoomId(roomtypeDTO.getRoomtypeId());
            roomInfo.setRoomName(roomtypeDTO.getRoomtypeName());
            roomInfo.setRoomEngName(roomtypeDTO.getRoomtypeEngName());
            roomInfo.setRoomAcreage(roomtypeDTO.getRoomArea());
            roomInfo.setRoomFloor(roomtypeDTO.getRoomFloor());
            roomInfo.setMaxPerson(roomtypeDTO.getMaxPerson());
            roomInfo.setWiredBroadnet(roomtypeDTO.getWiredBroadNet());
            roomInfo.setWirelessBroadnet(roomtypeDTO.getWirelessBroadNet());
            roomInfo.setWindowDetail(roomtypeDTO.getWindowDetail());
            roomInfo.setIsAllowSmoking(roomtypeDTO.getIsAllowSmoking());
            if (StrUtilX.isNotEmpty(roomtypeDTO.getAllowAddBed())) {
                roomInfo.setAddBedflag(Integer.valueOf(roomtypeDTO.getAllowAddBed()));
            }
            if (StrUtilX.isNotEmpty(roomtypeDTO.getAllowAddBabyBed())) {
                roomInfo.setAddCribFlag(Integer.valueOf(roomtypeDTO.getAllowAddBabyBed()));
            }
            // 最大成人数量
            roomInfo.setMaxAdults(roomtypeDTO.getMaxGrown());
            roomInfo.setMaxChild(roomtypeDTO.getMaxChild());
            BedTypesDetails roomBedTypeInfos = new BedTypesDetails();
            //卧室床
            List<BedInfoResponseDto> roomBeds = convertBedType(roomtypeDTO.getRoomBed(), roomtypeDTO.getRoomtypeName(), 0);
            //客厅床
            int roomCount = 0;
            if (CollUtilX.isNotEmpty(roomBeds)) {
                roomCount = roomBeds.size();
            }
            List<BedInfoResponseDto> livingRoomBeds = convertBedType(roomtypeDTO.getLivingBed(), roomtypeDTO.getRoomtypeName(), roomCount);
            if (CollUtilX.isNotEmpty(roomBeds) || CollUtilX.isNotEmpty(livingRoomBeds)) {
                roomBedTypeInfos.setRoomBeds(roomBeds);
                roomBedTypeInfos.setLivingRoomBeds(livingRoomBeds);
                roomInfo.setRoomBedTypeInfos(roomBedTypeInfos);
            }
            roomInfos.add(roomInfo);
        }
        return roomInfos;
    }

    /**
     * 酒店增量查询
     */
    @Override
    public IncrementResponse queryHotelIncrement(HotelIncrementRequest request) {
        IncrementResponse incrementResponse = new IncrementResponse();

        String partnerCode = request.getPartnerCode();
        AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(partnerCode);
        if (partnerInfo == null) {
            throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
        }
        // 1国内 2海外
        Integer domesticOrOverseas = partnerInfo.getDomesticOrOverseas();
        if (domesticOrOverseas == null) {
            throw new CustomException(DhubReturnCodeEnum.INVALID_PARTNER.no, DhubReturnCodeEnum.INVALID_PARTNER.code, DhubReturnCodeEnum.INVALID_PARTNER.code);
        }
        //如果合作商是国内合作商，只能查询国内数据
        String areaType = AreaTypeEnum.INLAND.getValue();
        if (domesticOrOverseas == 1) {
            areaType = AreaTypeEnum.INLAND.getValue();
        } else if (domesticOrOverseas == 2) {
            areaType = AreaTypeEnum.OUTLAND.getValue();
        }
        HotelIncrementReqDTO reqDTO = new HotelIncrementReqDTO();
        reqDTO.setAreaType(areaType);
        if (request.getPageNo() == null) {
            reqDTO.setCurrentPage(1L);
        } else {
            reqDTO.setCurrentPage(Long.valueOf(request.getPageNo()));
        }
        reqDTO.setPageSize(1000L);
        reqDTO.setBeginTime(request.getStartTime());
        reqDTO.setEndTime(request.getEndTime());
        reqDTO.setLanguageType(request.getLanguage());
        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/external/queryHotelIncrement", JSON.toJSONString(reqDTO), Constant.REQCONFIG);
        if (StrUtilX.isEmpty(s)) {
            log.error("酒店增量列表查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<PageResult<Long>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<PageResult<Long>>>() {
        });
        if (!GlobalErrorCodeConstants.SUCCESS.getCodeNo().equals(listResultX.getCode())) {
            log.error("酒店增量列表查询失败,失败原因:" + s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        PageResult<Long> longPageResult = listResultX.getData();
        if (longPageResult == null) {
            return incrementResponse;
        }
        incrementResponse.setCurrentPage(longPageResult.getCurrentPage() != null ? longPageResult.getCurrentPage().intValue() : 0);
        incrementResponse.setTotal(longPageResult.getTotalCount() != null ? longPageResult.getTotalCount() : 0L);
        incrementResponse.setTotalPage(longPageResult.getTotalPage() != null ? longPageResult.getTotalPage().intValue() : 0);
        incrementResponse.setPageSize(longPageResult.getPageSize() != null ? longPageResult.getPageSize().intValue() : 0);
        incrementResponse.setIncrements(longPageResult.getList());
        return incrementResponse;
    }

    /**
     * 酒店图片查询
     */
    @Override
    public HotelImageResponse queryHotelImage(HotelImageRequest request) {
        HotelImageResponse hotelImageResponse = new HotelImageResponse();
        CommonReqDTO commonReqDTO = new CommonReqDTO();
        commonReqDTO.setHotelIds(request.getHotelIds());
        commonReqDTO.setLanguageType(request.getLanguage());
        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/external/queryHotelImage", JSON.toJSONString(commonReqDTO), Constant.REQCONFIG);
        if (StrUtilX.isEmpty(s)) {
            log.error("酒店图片信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<List<HotelImageDTO>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<HotelImageDTO>>>() {
        });
        if (listResultX.isError()) {
            log.error("酒店图片信息查询失败,失败原因:{}", s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        List<HotelImageDTO> hotelImageDTOS = listResultX.getData();
        if (CollUtilX.isEmpty(hotelImageDTOS)) {
            return hotelImageResponse;
        }
        List<HotelImage> hotelImages = new ArrayList<>();
        HotelImage hotelImage;
        for (HotelImageDTO hotelImageDTO : hotelImageDTOS) {
            hotelImage = new HotelImage();
            hotelImage.setHotelId(hotelImageDTO.getHotelId());
            convertHotelImage(hotelImage, hotelImageDTO);
            hotelImages.add(hotelImage);
        }
        hotelImageResponse.setHotelImages(hotelImages);
        return hotelImageResponse;
    }

    private void convertHotelImage(HotelImage hotelImage, HotelImageDTO hotelImageDTO) {
        if (CollUtilX.isNotEmpty(hotelImageDTO.getImages())) {
            List<Image> images = new ArrayList<>();
            Image image;
            for (ImageDTO imageDTO : hotelImageDTO.getImages()) {
                image = new Image();
                convertImage(image, imageDTO, 1);
                images.add(image);
            }
            hotelImage.setImages(images);
        }
        //房型图片
        if (CollUtilX.isNotEmpty(hotelImageDTO.getRoomImages())) {
            List<RoomImage> roomImages = new ArrayList<>();
            RoomImage roomImage;
            for (RoomImages roomImageInfo : hotelImageDTO.getRoomImages()) {
                roomImage = new RoomImage();
                roomImage.setRoomId(roomImageInfo.getRoomId());
                if (CollUtilX.isEmpty(roomImageInfo.getImages())) {
                    continue;
                }
                List<Image> images = new ArrayList<>();
                Image image;
                for (ImageDTO imageDTO : roomImageInfo.getImages()) {
                    image = new Image();
                    convertImage(image, imageDTO, null);
                    images.add(image);
                }
                roomImage.setImages(images);
                roomImages.add(roomImage);
            }
            hotelImage.setRoomImages(roomImages);
        }
        //视频图片
        if (CollUtilX.isNotEmpty(hotelImageDTO.getMeetingImages())) {
            List<MeetingImage> meetingImages = new ArrayList<>();
            MeetingImage meetingImage;
            for (MeetingImages meetingImagesInfo : hotelImageDTO.getMeetingImages()) {
                meetingImage = new MeetingImage();
                meetingImage.setMeetingId(meetingImagesInfo.getMeetingId());
                List<Image> images = new ArrayList<>();
                Image image;
                for (ImageDTO imageDTO : meetingImagesInfo.getImages()) {
                    image = new Image();
                    convertImage(image, imageDTO, null);
                    images.add(image);
                }
                meetingImage.setImages(images);
                meetingImages.add(meetingImage);
            }
            hotelImage.setMeetingImages(meetingImages);
        }
    }

    private void convertImage(Image image, ImageDTO imageDTO, Integer hotelMainType) {
        image.setImageId(imageDTO.getImgId());
        image.setImageType(imageDTO.getImgType());
        //酒店主图
        if (hotelMainType != null && hotelMainType == 1) {
            if (StrUtilX.isNotEmpty(imageDTO.getIsMainImg())) {
                image.setIsMain(Integer.valueOf(imageDTO.getIsMainImg()));
            }
        } else {
            if (StrUtilX.isNotEmpty(imageDTO.getIsRoomMainImg())) {
                image.setIsMain(Integer.valueOf(imageDTO.getIsRoomMainImg()));
            }
        }
        image.setUrl(imageDTO.getImgUrl());
    }

    private List<BedInfoResponseDto> convertBedType(List<RoomDetailBedDTO> roomBed, String roomName, int roomCount) {
        List<BedInfoResponseDto> bedInfoResponseDtos;
        if (CollUtilX.isEmpty(roomBed)) {
            return null;
        }
        bedInfoResponseDtos = new ArrayList<>();
        BedInfoResponseDto bedInfoResponseDto;
        for (RoomDetailBedDTO roomDetailBedDTO : roomBed) {
            roomCount++;
            bedInfoResponseDto = new BedInfoResponseDto();
            bedInfoResponseDto.setRoomName(roomName + roomCount);
            List<BedGroupDto> bedGroups;
            List<BedGroupsDTO> bedGroupsDTOS = roomDetailBedDTO.getBedGroups();
            if (CollUtilX.isNotEmpty(bedGroupsDTOS)) {
                bedGroups = new ArrayList<>();
                BedGroupDto bedGroupDto;
                for (BedGroupsDTO bedGroupsDTO : bedGroupsDTOS) {
                    bedGroupDto = new BedGroupDto();
                    List<BedInfoDto> bedInfos;
                    List<BedInfosDTO> bedInfosDTOs = bedGroupsDTO.getBedInfos();
                    if (CollUtilX.isNotEmpty(bedInfosDTOs)) {
                        bedInfos = new ArrayList<>();
                        BedInfoDto bedInfoDto;
                        for (BedInfosDTO bedInfosDTO : bedInfosDTOs) {
                            bedInfoDto = new BedInfoDto();
                            bedInfoDto.setBedTypeCode(bedInfosDTO.getBedCode());
                            bedInfoDto.setBedName(bedInfosDTO.getBedName());
                            bedInfoDto.setBedWidth(bedInfosDTO.getBedWidth());
                            if (bedInfosDTO.getBedNum() != null) {
                                bedInfoDto.setBedNum(bedInfosDTO.getBedNum().toString());
                            }
                            bedInfos.add(bedInfoDto);
                        }
                        bedGroupDto.setBedInfos(bedInfos);
                    }

                    bedGroups.add(bedGroupDto);
                }
                bedInfoResponseDto.setBedGroups(bedGroups);
            }
            bedInfoResponseDtos.add(bedInfoResponseDto);
        }
        return bedInfoResponseDtos;
    }

//    @Override
//    public HotelListResponse queryHotelAllList(HotelPriceRequest request) {
//        HotelListResponse response = new HotelListResponse();
//        if (request.getDefaultSort() == null) {
//            request.setDefaultSort(6);
//        }
//        HotelPriceReq req = DTOConvert.INSTANCE.hotelPriceConvert(request);
//        //校验价格区间
//        if (!StringUtils.isEmpty(request.getPriceBetween())) {
//            if (!request.getPriceBetween().contains("-")) {
//                throw new CustomException(DhubReturnCodeEnum.BETWEEN_PRICE_FORMAT_ERROR.no, DhubReturnCodeEnum.BETWEEN_PRICE_FORMAT_ERROR.code, DhubReturnCodeEnum.BETWEEN_PRICE_FORMAT_ERROR.code);
//            }
//            String[] priceStr = request.getPriceBetween().split("-");
//            if (priceStr.length != 2) {
//                throw new CustomException(DhubReturnCodeEnum.BETWEEN_PRICE_FORMAT_ERROR.no, DhubReturnCodeEnum.BETWEEN_PRICE_FORMAT_ERROR.code, DhubReturnCodeEnum.BETWEEN_PRICE_FORMAT_ERROR.code);
//            }
//        }
//        AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(request.getPartnerCode());
//        if (partnerInfo == null) {
//            throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
//        }
//        req.setAgentCode(partnerInfo.getAgentCode());
//        req.setHotelName(request.getHotelName());
//
//        Response<PaginationSupportDTO<HotelPriceDTO>> paginationSupportDTOResponse = productRemote.queryHotelBaseToDhubList(req);
//        if (paginationSupportDTOResponse.isSuccess()) {
//            PaginationSupportDTO<HotelPriceDTO> model = paginationSupportDTOResponse.getModel();
//            response.setList(model.getItemList());
//            response.setPageSize(model.getPageSize());
//            response.setCurrentPage(model.getCurrentPage());
//            response.setTotalPage(model.getTotalPage());
//            response.setTotalCount(model.getTotalCount());
//        } else {
//            log.error("查询酒店列表异常");
//            log.error(paginationSupportDTOResponse.toString());
//            throw new CustomException(DhubReturnCodeEnum.getKeyByValue(paginationSupportDTOResponse.getFailCode()), paginationSupportDTOResponse.getFailCode(), paginationSupportDTOResponse.getFailCode());
//        }
//        return response;
//    }

//    @Override
//    public DistrictAndBusinessResponse queryDistrictAndBusiness(DistrictOrBusinessReq req) {
//        DistrictAndBusinessResponse response = new DistrictAndBusinessResponse();
//        req.setLanguage(req.getLanguage());
//        if (StrUtilX.isEmpty(req.getCityCode())) {
//            throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, "城市编码必填");
//        }
//        com.tiangong.dis.dto.DistrictOrBusinessReq req1 = DTOConvert.INSTANCE.districtOrBusinessConvert(req);
//        //BeanUtils.copyProperties(req, req1);
//        Response<DistrictOrBusinessResp> resp = areaRemote.queryBusinessAndDistrictList(req1);
//
//        // 默认查询有效的酒店标签
//        HotelLabelConfigReqDTO hotelLabelConfigReq = new HotelLabelConfigReqDTO();
//        Response<List<HotelLabelConfigRespDTO>> listResponse = hotelLabelConfigRemote.hotelLabelConfigList(hotelLabelConfigReq);
//
//        if (resp.isSuccess()) {
//            if (resp.getModel() != null) {
//                response.setDistrictOrBusinessResp(resp.getModel());
//            }
//        } else {
//            log.error("行政区和商业区查询失败");
//            throw new CustomException(DhubReturnCodeEnum.getKeyByValue(resp.getFailCode()), resp.getFailCode(), resp.getFailCode());
//
//        }
//        if (listResponse.isSuccess()) {
//            List<HotelLabel> hotelLabelList = DTOConvert.INSTANCE.HotelLabelListConvert(listResponse.getModel());
//            response.setHotelLabels(hotelLabelList);
//        } else {
//            log.error("查询酒店标签");
//            throw new CustomException(DhubReturnCodeEnum.getKeyByValue(listResponse.getFailCode()), listResponse.getFailCode(), listResponse.getFailCode());
//        }
//
//        List<Map<String, Object>> list = new ArrayList<>();
//        for (HotelRankEnum hotelRankEnum : HotelRankEnum.values()) {
//            Map<String, Object> map = new HashMap<>();
//            map.put("rank", hotelRankEnum.getSourceNo());
//            map.put("rankName", hotelRankEnum.getDesc());
//            list.add(map);
//        }
//        response.setHotelRankEnum(list);
//
//        Map<String, String> brandMap = new HashMap<>();
//
//
//        response.setHotelBrands(brandMap);
//
//        return response;
//    }

//    @Override
//    public HotelAndCityResponse queryCityAndHotelByKeyword(SearchCityAndHotelReq request) {
//        HotelAndCityResponse hotelAndCityResponse = new HotelAndCityResponse();
//        request.setLanguageType(request.getLanguage());
//        if (StrUtilX.isEmpty(request.getKeyWord())) {
//            throw new CustomException(DhubReturnCodeEnum.KEY_WORD_NOT_EMPTY.no, DhubReturnCodeEnum.KEY_WORD_NOT_EMPTY.code, DhubReturnCodeEnum.KEY_WORD_NOT_EMPTY.code);
//        }
//        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/external/queryHotelAndCity", JSON.toJSONString(request), Constant.REQCONFIG);
//        if (StrUtilX.isEmpty(s)) {
//            log.error("查询酒店和城市失败,未返回数据");
//            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
//        }
//        ResultX<List<HotelAndCityDto>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<HotelAndCityDto>>>() {
//        });
//        if (!GlobalErrorCodeConstants.SUCCESS.getCode().equals(listResultX.getCode())) {
//            log.error("查询酒店和城市失败,失败原因:" + s);
//            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
//        }
//        JSONObject object = JSON.parseObject(s, JSONObject.class);
//        if (object == null) {
//            return hotelAndCityResponse;
//        }
//        JSONObject data = object.getJSONObject("data");
//        if (data == null) {
//            return hotelAndCityResponse;
//        }
//        JSONArray list = data.getJSONArray("list");
//        if (list == null || list.size() == 0) {
//            return hotelAndCityResponse;
//        }
//        List<HotelAndCityDto> hotelAndCityDtos = JSON.parseObject(list.toString(), new TypeReference<List<HotelAndCityDto>>() {
//        });
//        hotelAndCityResponse.setList(hotelAndCityDtos);
//        return hotelAndCityResponse;
//    }

//    @Override
//    public HotelMainInfoResponse queryHotelMainInfoByHotelIds(HotelMainInfoRequest request) {
//        HotelMainInfoResponse hotelMainInfoResponse = new HotelMainInfoResponse();
//        HotelPriceReq req = new HotelPriceReq();
//        AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(request.getPartnerCode());
//        if (partnerInfo == null) {
//            throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
//        }
//        req.setLanguage(request.getLanguage());
//        req.setHotelIdList(request.getHotelIds());
//        req.setAgentCode(partnerInfo.getAgentCode());
//
//
//        Response<PaginationSupportDTO<HotelPriceDTO>> paginationSupportDTOResponse = productRemote.queryHotelBaseToDhubList(req);
//        List<HotelPriceDTO> itemList = paginationSupportDTOResponse.getModel().getItemList();
//        if (paginationSupportDTOResponse.isSuccess() && paginationSupportDTOResponse.getModel() != null && CollUtilX.isNotEmpty(paginationSupportDTOResponse.getModel().getItemList())) {
//            List<HotelMainInfo> list = new ArrayList<>();
//            for (HotelPriceDTO hotelPriceDTO : itemList) {
//                HotelMainInfo hotelMainInfo = new HotelMainInfo();
//                hotelMainInfo.setHotelName(hotelPriceDTO.getCityCode());
//                hotelMainInfo.setCityName(hotelPriceDTO.getCityName());
//                hotelMainInfo.setHotelId(hotelPriceDTO.getHotelId());
//                hotelMainInfo.setHotelName(hotelPriceDTO.getHotelName());
//                hotelMainInfo.setHotelStar(hotelPriceDTO.getHotelStar());
//                hotelMainInfo.setHotelAddress(hotelPriceDTO.getHotelAddress());
//                hotelMainInfo.setLowestPrice(hotelPriceDTO.getLowestPrice());
//                hotelMainInfo.setSaleCurrencyCode(hotelPriceDTO.getSaleCurrencyCode());
//                hotelMainInfo.setMainUrl(hotelPriceDTO.getMainUrl());
//                hotelMainInfo.setBusinessName(hotelPriceDTO.getBusinessName());
//                hotelMainInfo.setHotelScore(hotelPriceDTO.getHotelScore());
//                hotelMainInfo.setCountryCode(hotelPriceDTO.getCountryCode());
//                hotelMainInfo.setPayAtHotelFee(hotelPriceDTO.getPayAtHotelFee());
//                hotelMainInfo.setPayAtHotelCurrencyCode(hotelPriceDTO.getPayAtHotelCurrencyCode());
//                hotelMainInfo.setLongitude(hotelPriceDTO.getLongitude());
//                hotelMainInfo.setLatitude(hotelPriceDTO.getLatitude());
//                hotelMainInfo.setLngGoogle(hotelPriceDTO.getLngGoogle());
//                hotelMainInfo.setLatGoogle(hotelPriceDTO.getLatGoogle());
//                hotelMainInfo.setHotelRecommendId(hotelPriceDTO.getHotelRecommendId());
//                hotelMainInfo.setHotelLabelNameList(hotelPriceDTO.getHotelLabelNameList());
//                list.add(hotelMainInfo);
//            }
//            hotelMainInfoResponse.setList(list);
//        }
//        return hotelMainInfoResponse;
//    }

    @Override
    public HotelListPageResponse findHotelList(HotelListPageRequest request) {

        HotelListPageResponse response = new HotelListPageResponse();

        //价格校验
        if (request.getPriceBegin() != null && request.getPriceEnd() == null) {
            throw new CustomException(DhubReturnCodeEnum.PRICE_END_IS_NOT_EMPTY.no, DhubReturnCodeEnum.PRICE_END_IS_NOT_EMPTY.code, DhubReturnCodeEnum.PRICE_END_IS_NOT_EMPTY.code);
        } else if (request.getPriceEnd() != null && request.getPriceBegin() == null) {
            throw new CustomException(DhubReturnCodeEnum.PRICE_BEGIN_IS_NOT_EMPTY.no, DhubReturnCodeEnum.PRICE_BEGIN_IS_NOT_EMPTY.code, DhubReturnCodeEnum.PRICE_BEGIN_IS_NOT_EMPTY.code);
        } else if (request.getPriceBegin() != null && request.getPriceBegin() >= request.getPriceEnd()) {
            throw new CustomException(DhubReturnCodeEnum.PRICE_BEGIN_LOWEST_PRICE_END.no, DhubReturnCodeEnum.PRICE_BEGIN_LOWEST_PRICE_END.code, DhubReturnCodeEnum.PRICE_BEGIN_LOWEST_PRICE_END.code);
        } else if (request.getPriceBegin() != null && request.getPriceBegin() <= 0) {
            throw new CustomException(DhubReturnCodeEnum.PRICE_BEGIN_GREATER_ZERO.no, DhubReturnCodeEnum.PRICE_BEGIN_GREATER_ZERO.code, DhubReturnCodeEnum.PRICE_BEGIN_GREATER_ZERO.code);
        }
        if (request.getSortBy() < 1 || request.getSortBy() > 7) {
            throw new CustomException(DhubReturnCodeEnum.SORT_BY_NOT_EXIST.no, DhubReturnCodeEnum.SORT_BY_NOT_EXIST.code, DhubReturnCodeEnum.SORT_BY_NOT_EXIST.code);
        }

        //校验设施
        if (CollUtilX.isNotEmpty(request.getHotelFacilityCodes())) {
            for (String code : request.getHotelFacilityCodes()) {
                String facility = FacilityEnum.checkHotelFacility(code);
                if (StringUtils.isEmpty(facility)) {
                    throw new CustomException(DhubReturnCodeEnum.ILLEGAL_HOTEL_FACILITY_CODE.no, DhubReturnCodeEnum.ILLEGAL_HOTEL_FACILITY_CODE.code, DhubReturnCodeEnum.ILLEGAL_HOTEL_FACILITY_CODE.code);
                }
            }
        }

        if (CollUtilX.isNotEmpty(request.getRoomFacilityCodes())) {
            for (String code : request.getRoomFacilityCodes()) {
                String facility = FacilityEnum.checkRoomFacility(code);
                if (StringUtils.isEmpty(facility)) {
                    throw new CustomException(DhubReturnCodeEnum.ILLEGAL_ROOM_FACILITY_CODE.no, DhubReturnCodeEnum.ILLEGAL_ROOM_FACILITY_CODE.code, DhubReturnCodeEnum.ILLEGAL_ROOM_FACILITY_CODE.code);
                }
            }
        }

        HotelPageReq req = DTOConvert.INSTANCE.hotelListPageConvert(request);
        if (StrUtilX.isEmpty(req.getAgentCode())) {
            AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(request.getPartnerCode());
            if (partnerInfo == null) {
                throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
            }
            req.setAgentCode(partnerInfo.getAgentCode());
        }
        Response<PaginationSupportDTO<HotelListResp>> result = hotelProductRemote.findHotelList(req);
        if (result.isSuccess()) {
            PaginationSupportDTO<HotelListResp> model = result.getModel();
            // 组装酒店主图信息
            convertHotelMainUrl(model.getItemList(), request.getLanguage());
            response.setList(model.getItemList());
            response.setPageSize(model.getPageSize());
            response.setCurrentPage(model.getCurrentPage());
            response.setTotalPage(model.getTotalPage());
            response.setTotalCount(model.getTotalCount());
        } else {
            log.error("获取酒店列表异常--- request: {} ,response：{}", JSON.toJSONString(req), JSON.toJSONString(result));
            throw new CustomException(DhubReturnCodeEnum.getKeyByValue(result.getFailCode()), result.getFailCode(), result.getFailCode());
        }
        return response;
    }

    /**
     * 组装酒店主图信息
     */
    private void convertHotelMainUrl(List<HotelListResp> itemList, String language) {
        // 获取主图为空的酒店
        List<Long> hotelIds = itemList.stream()
                .filter(hotelListResp -> StrUtilX.isEmpty(hotelListResp.getMainUrl()))
                .map(HotelListResp::getHotelId)
                .collect(Collectors.toList());
        if (CollUtilX.isEmpty(hotelIds)){
            return;
        }

        // 查询酒店图片
        HotelImageRequest hotelImageRequest = new HotelImageRequest();
        hotelImageRequest.setHotelIds(hotelIds);
        hotelImageRequest.setLanguage(language);
        HotelImageResponse hotelImageResponse = queryHotelImage(hotelImageRequest);
        if(hotelImageResponse == null || CollUtilX.isEmpty(hotelImageResponse.getHotelImages())){
            return;
        }
        // 获取酒店图片
        Map<Long, HotelImage> hotelImageMap = hotelImageResponse.getHotelImages().stream().collect(Collectors.toMap(HotelImage::getHotelId, hotelImage -> hotelImage));
        for (HotelListResp hotelListResp : itemList) {
            // 设置酒店主图 优先级 外景图>大堂图>其他类型图片
            if (hotelImageMap.containsKey(hotelListResp.getHotelId())){
                HotelImage hotelImage = hotelImageMap.get(hotelListResp.getHotelId());
                String mainUrl = ""; // 主图
                String otherUrl = "";
                if (CollUtilX.isNotEmpty(hotelImage.getImages())){
                    for (Image image : hotelImage.getImages()) {
                        if (StrUtilX.isEmpty(image.getUrl())){
                            continue;
                        }
                        // 外景图优先
                        if (image.getImageType() == 2){
                            mainUrl = image.getUrl();
                            break;
                        }
                        // 大堂图
                        if (image.getImageType() == 3){
                            otherUrl = image.getUrl();
                            mainUrl = image.getUrl();
                        } else if (StrUtilX.isEmpty(otherUrl)) {
                            // 其他类型图片
                            mainUrl = image.getUrl();
                        }
                    }
                }
                // 主图为空时，取房型图作为酒店主图
                if(StrUtilX.isEmpty(mainUrl) && CollUtilX.isNotEmpty(hotelImage.getRoomImages())){
                    for (RoomImage roomImage : hotelImage.getRoomImages()) {
                        if (CollUtilX.isEmpty(roomImage.getImages())){
                            continue;
                        }
                        mainUrl = roomImage.getImages().get(0).getUrl();
                        break;
                    }
                }
                //主图为空时，取会议室图作为酒店主图
                if(StrUtilX.isEmpty(mainUrl) && CollUtilX.isNotEmpty(hotelImage.getMeetingImages())){
                    for (MeetingImage meetingImage : hotelImage.getMeetingImages()) {
                        if (CollUtilX.isEmpty(meetingImage.getImages())){
                            continue;
                        }
                        mainUrl = meetingImage.getImages().get(0).getUrl();
                        break;
                    }
                }
                hotelListResp.setMainUrl(mainUrl);
            }
        }
    }
}
