package com.tiangong.service.base.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.tiangong.common.Response;
import com.tiangong.config.agentConfig.AgentConfig;
import com.tiangong.config.exception.CustomException;
import com.tiangong.convert.DTOConvert;
import com.tiangong.dis.dto.InvoiceNotifyPushRequest;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.HotelOrderRemote;
import com.tiangong.dto.order.request.CreateInvoiceRequest;
import com.tiangong.dto.order.request.QueryCreditBalanceRequest;
import com.tiangong.dto.order.response.CreateInvoiceResponse;
import com.tiangong.dto.order.response.CreditBalanceResponse;
import com.tiangong.entity.base.AgentAccountConfig;
import com.tiangong.entity.base.Header;
import com.tiangong.entity.base.Request;
import com.tiangong.entity.base.ResponseResult;
import com.tiangong.entity.request.order.CreateInvoiceInfoRequest;
import com.tiangong.entity.request.order.InvoiceNotifyPushRequestRequest;
import com.tiangong.entity.request.order.InvoiceNotifyPushRequestResponse;
import com.tiangong.entity.request.order.QueryCreditBalanceInfoRequest;
import com.tiangong.entity.response.order.CreateInvoiceInfoResponse;
import com.tiangong.entity.response.order.CreditBalanceInfoResponse;
import com.tiangong.enums.*;
import com.tiangong.order.remote.OrderQueryRemote;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.OrderInvoiceDTO;
import com.tiangong.service.base.OtherService;
import com.tiangong.util.HttpUtilX;
import com.tiangong.util.Md5Util;
import com.tiangong.util.StrUtilX;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OtherServiceImpl implements OtherService {

    private final HotelOrderRemote hotelOrderRemote;
    private final OrderQueryRemote orderQueryRemote;

    /**
     * 信用额度查询接口
     */
    @Override
    public ResponseResult<CreditBalanceInfoResponse> queryCreditBalance(QueryCreditBalanceInfoRequest request) {
        ResponseResult<CreditBalanceInfoResponse> creditBalanceResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);
        CreditBalanceInfoResponse creditBalanceInfoResponse = new CreditBalanceInfoResponse();
        QueryCreditBalanceRequest queryCreditBalanceRequest = new QueryCreditBalanceRequest();
        queryCreditBalanceRequest.setAgentCode(request.getAgentCode());
        com.tiangong.dis.dto.ResponseResult<CreditBalanceResponse> creditBalanceRemoteResponse = hotelOrderRemote.queryCreditBalance(queryCreditBalanceRequest);
        if (creditBalanceRemoteResponse == null) {
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(creditBalanceRemoteResponse.getReturnCode())) {
            DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(creditBalanceRemoteResponse.getReturnCode());
            throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
        }
        if (creditBalanceRemoteResponse.getBussinessResponse() != null) {
            creditBalanceInfoResponse.setCreditBalance(creditBalanceRemoteResponse.getBussinessResponse().getCreditBalance().setScale(2, RoundingMode.DOWN));
            creditBalanceResponseResponseResult.setBussinessResponse(creditBalanceInfoResponse);
        }
//        creditBalanceResponseResponseResult.setReturnCode(creditBalanceRemoteResponse.getReturnCode());
//        creditBalanceResponseResponseResult.setReturnMsg(creditBalanceRemoteResponse.getReturnMsg());
        return creditBalanceResponseResponseResult;
    }

    /**
     * 订单开票接口
     */
    @Override
    public ResponseResult<CreateInvoiceInfoResponse> createInvoice(CreateInvoiceInfoRequest request) {
        if (StrUtil.isEmpty(request.getRequestId())) {
            request.setRequestId(IdWorker.getIdStr());
        }
        log.info("订单发票接口参数请求参数:{},requestId:{}", JSONUtil.toJsonStr(request), request.getRequestId());
        ResponseResult<CreateInvoiceInfoResponse> createInvoiceResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);
        // 部分字段已经使用Bean Validation注解进行校验 这里进行额外的业务校验
        validateInvoiceRequest(request, request.getRequestId());
        //默认*旅游服务*代订房费
        request.setCostNameInteger(12);
        CreateInvoiceRequest createInvoiceRequest = DTOConvert.INSTANCE.convert(request);
        com.tiangong.dis.dto.ResponseResult<CreateInvoiceResponse> createInvoiceRemoteResponse = hotelOrderRemote.createInvoice(createInvoiceRequest);
        if (createInvoiceRemoteResponse == null) {
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(createInvoiceRemoteResponse.getReturnCode())) {
            DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(createInvoiceRemoteResponse.getReturnCode());
            throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
        }
        if (createInvoiceRemoteResponse.getBussinessResponse() != null) {
            createInvoiceResponseResponseResult.setBussinessResponse(DTOConvert.INSTANCE.createInvoiceConvert(createInvoiceRemoteResponse.getBussinessResponse()));
        }
        createInvoiceResponseResponseResult.setReturnCode(createInvoiceRemoteResponse.getReturnCode());
        createInvoiceResponseResponseResult.setReturnMsg(createInvoiceRemoteResponse.getReturnMsg());
        return createInvoiceResponseResponseResult;
    }

    @Override
    public Response<Boolean> pushInvoiceStatusNotify(InvoiceNotifyPushRequest request) {
        AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(request.getPartnerCode());
        // 检查是否配置了通知地址
        if (ObjectUtil.isEmpty(agentAccountConfig) || StringUtils.isEmpty(agentAccountConfig.getInvoiceNotifyUrl())) {
            log.info("客户未配置电子发票推送接口地址，partnerCode: {}requestId:{}", request.getPartnerCode(), request.getRequestId());
            return Response.error(DhubReturnCodeEnum.INVOICE_URL_NO_EXIST.getNo(), DhubReturnCodeEnum.INVOICE_URL_NO_EXIST.getCode());
        }
        try {
            InvoiceNotifyPushRequestRequest invoiceNotifyPushRequestRequest = DTOConvert.INSTANCE.invoiceNotifyPushConvert(request);
            Request<InvoiceNotifyPushRequestRequest> requestRequest = new Request<>();
            requestRequest.setBusinessRequest(invoiceNotifyPushRequestRequest);
            Header header = getHeader(agentAccountConfig, ApiNameEnum.orderStatusPush);
            requestRequest.setHeader(header);
            String req = JSONUtil.toJsonStr(requestRequest);
            log.info("dhub推送发票状态请求参数: {},请求头:{}requestId:{}", req, header, request.getRequestId());
            String resp = HttpUtilX.post(agentAccountConfig.getInvoiceNotifyUrl(), req);
            log.info("dhub推送发票状态返回，result: {}requestId:{}", resp, request.getRequestId());
            if (StrUtilX.isNotEmpty(resp)) {
                ResponseResult<InvoiceNotifyPushRequestResponse> result = JSONObject.parseObject(resp, new TypeReference<ResponseResult<InvoiceNotifyPushRequestResponse>>() {
                });
                if (Objects.nonNull(result) && Objects.nonNull(result.getBussinessResponse()) && Objects.equals(result.getReturnCode(), DhubReturnCodeEnum.SUCCESS.no)
                        && Objects.equals(result.getBussinessResponse().getResult(), 0)) {
                    return Response.success(true);
                } else {
                    return Response.error(DhubReturnCodeEnum.OUTER_IF_EXCEPTION.getNo(), DhubReturnCodeEnum.OUTER_IF_EXCEPTION.getCode());
                }
            }
            return Response.error(DhubReturnCodeEnum.OUTER_IF_EXCEPTION.getNo(), DhubReturnCodeEnum.OUTER_IF_EXCEPTION.getCode());
        } catch (Exception e) {
            log.error(StrUtil.format("dhub推送发票请求异常,requestId:", request.getRequestId()), e);
            return Response.error(DhubReturnCodeEnum.OUTER_IF_EXCEPTION.getNo(), DhubReturnCodeEnum.OUTER_IF_EXCEPTION.getCode());
        }
    }

    /**
     * 获取响应头
     */
    private Header getHeader(AgentAccountConfig agentAccountConfig, ApiNameEnum apiNameEnum) {
        Header header = new Header();
        header.setTimestamp(System.currentTimeMillis());
        header.setPartnerCode(agentAccountConfig.getPartnerCode());
        header.setRequestType(apiNameEnum.apiName);
        header.setVersion("1.0.0");
        String str = header.getTimestamp() + header.getPartnerCode() +
                Md5Util.md5Encode(agentAccountConfig.getSecretkey()).toUpperCase() + header.getRequestType();
        header.setSignature(str);
        return header;
    }

    /**
     * 校验开票请求参数
     *
     * @param request 开票请求
     */
    private void validateInvoiceRequest(CreateInvoiceInfoRequest request, String requestId) {
        if(StrUtil.isNotEmpty(request.getInvoiceTaxRate())){
            rateValid(request.getInvoiceTaxRate());
        }
        // 校验开票金额
        int scale = request.getAmount().stripTrailingZeros().scale();
        if (scale > 2) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_AMOUNT_DECIMAL_PLACES.getNo(), DhubReturnCodeEnum.INVOICE_AMOUNT_DECIMAL_PLACES.getCode(), DhubReturnCodeEnum.INVOICE_AMOUNT_DECIMAL_PLACES.getCode());
        }
        // 校验发票类型
        List<Integer> collect = Arrays.stream(InvoiceTypeEnum.values()).map(InvoiceTypeEnum::getNo).collect(Collectors.toList());
        if (!collect.contains(request.getInvoiceTypeInteger())) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_TYPE_NOT_EXIST.getNo(), DhubReturnCodeEnum.INVOICE_TYPE_NOT_EXIST.getCode(), DhubReturnCodeEnum.INVOICE_TYPE_NOT_EXIST.getCode());
        }
        // 校验发票抬头
        List<Integer> collect2 = Arrays.stream(InvoiceTitleTypeEnum.values()).map(InvoiceTitleTypeEnum::getNo).collect(Collectors.toList());
        if (!collect2.contains(request.getInvoiceTitleType())) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_TITLE_TYPE_NOT_EXIST.getNo(), DhubReturnCodeEnum.INVOICE_TITLE_TYPE_NOT_EXIST.getCode(), DhubReturnCodeEnum.INVOICE_TITLE_TYPE_NOT_EXIST.getCode());
        }
        //企业情况
        if (request.getInvoiceTitleType().equals(InvoiceTitleTypeEnum.COMPANY.getNo())) {
            //税号不能为空
            if (StrUtil.isEmpty(request.getCompanyTax())) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_TAX_NUMBER_EMPTY.getNo(), DhubReturnCodeEnum.INVOICE_TAX_NUMBER_EMPTY.getCode(), DhubReturnCodeEnum.INVOICE_TAX_NUMBER_EMPTY.getCode());
            }
            if (StrUtil.isEmpty(request.getBank())) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_BANK_NOT_EXIST.getNo(), DhubReturnCodeEnum.INVOICE_BANK_NOT_EXIST.getCode(), DhubReturnCodeEnum.INVOICE_BANK_NOT_EXIST.getCode());
            }
        }
        // 校验税号
        // 校验税号只能是数字和字母
        if (StrUtil.isNotEmpty(request.getCompanyTax()) && !request.getCompanyTax().matches("^[a-zA-Z0-9]+$")) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_TAX_NUMBER_FORMAT.getNo(), DhubReturnCodeEnum.INVOICE_TAX_NUMBER_FORMAT.getCode(), DhubReturnCodeEnum.INVOICE_TAX_NUMBER_FORMAT.getCode());
        }
        if (StrUtil.isNotEmpty(request.getBankAccount()) && !request.getBankAccount().matches("^[a-zA-Z0-9]+$")) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_BANK_ACCOUNT_FORMAT.getNo(), DhubReturnCodeEnum.INVOICE_BANK_ACCOUNT_FORMAT.getCode(), DhubReturnCodeEnum.INVOICE_BANK_ACCOUNT_FORMAT.getCode());
        }
        // 校验公司电话区号和电话
        // 公司电话非必填
        if (StringUtils.isNotEmpty(request.getCompanyTel())) {
            // 校验公司电话只能是数字
            if (!request.getCompanyTel().matches("^\\d+$")) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_COMPANY_PHONE_FORMAT.getNo(), DhubReturnCodeEnum.INVOICE_COMPANY_PHONE_FORMAT.getCode(), DhubReturnCodeEnum.INVOICE_COMPANY_PHONE_FORMAT.getCode());
            }
        }

        // 校验取票方式
        // 校验取票方式是否存在
        boolean isValidGetMethod = false;

        // 电子发票（电子普通发票、电子专用发票）取票方式只能是电子邮箱
        if (request.getInvoiceTypeInteger().equals(InvoiceTypeEnum.ELECTRONIC_COMMON_INVOICE.no) ||
                request.getInvoiceTypeInteger().equals(InvoiceTypeEnum.ELECTRONIC_SPECIAL_INVOICE.no)) {
            if (StrUtil.isEmpty(request.getEmail())) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_EMAIL_EMPTY.getNo(), DhubReturnCodeEnum.INVOICE_EMAIL_EMPTY.getCode(), DhubReturnCodeEnum.INVOICE_EMAIL_EMPTY.getCode());
            }
            isValidGetMethod = (request.getGetMethod().equals(TicketTypeEnum.EMAIL.changeNo));
        }
        // 纸质发票（纸质普通发票、纸质专用发票）取票方式只能是邮寄或自取
        else if (request.getInvoiceTypeInteger().equals(InvoiceTypeEnum.PAPER_COMMON_INVOICE.no) ||
                request.getInvoiceTypeInteger().equals(InvoiceTypeEnum.PAPER_SPECIAL_INVOICE.no)) {
            isValidGetMethod = (request.getGetMethod().equals(TicketTypeEnum.POSTER.changeNo) ||
                    request.getGetMethod().equals(TicketTypeEnum.SELF_TAKE.changeNo));
            if (StrUtil.isEmpty(request.getReceiveName())) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_CONTACT_NAME_EMPTY.getNo(), DhubReturnCodeEnum.INVOICE_CONTACT_NAME_EMPTY.getCode(), DhubReturnCodeEnum.INVOICE_CONTACT_NAME_EMPTY.getCode());
            }
            if (StrUtil.isEmpty(request.getTelePhone())) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_CONTACT_PHONE_EMPTY.getNo(), DhubReturnCodeEnum.INVOICE_CONTACT_PHONE_EMPTY.getCode(), DhubReturnCodeEnum.INVOICE_CONTACT_PHONE_EMPTY.getCode());
            }
        }

        if (!isValidGetMethod) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_GET_METHOD_NOT_EXIST.getNo(), DhubReturnCodeEnum.INVOICE_GET_METHOD_NOT_EXIST.getCode(), DhubReturnCodeEnum.INVOICE_GET_METHOD_NOT_EXIST.getCode());
        }

        // 校验邮箱格式
        if (StringUtils.isNotEmpty(request.getEmail()) && !isValidEmail(request.getEmail())) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_EMAIL_FORMAT.getNo(), DhubReturnCodeEnum.INVOICE_EMAIL_FORMAT.getCode(), DhubReturnCodeEnum.INVOICE_GET_METHOD_NOT_EXIST.getCode());
        }

        // 校验联系人电话长度和格式
        if (StringUtils.isNotEmpty(request.getTelePhone())) {
            // 校验联系人电话只能是数字
            if (!request.getTelePhone().matches("^\\d+$")) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_CONTACT_PHONE_FORMAT.getNo(), DhubReturnCodeEnum.INVOICE_CONTACT_PHONE_FORMAT.getCode(), DhubReturnCodeEnum.INVOICE_CONTACT_PHONE_FORMAT.getCode());
            }
        }
        // 取票方式为邮寄时收货地址必填
        if (request.getGetMethod().equals(TicketTypeEnum.POSTER.changeNo) && StringUtils.isEmpty(request.getAddress())) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_ADDRESS_EMPTY.getNo(), DhubReturnCodeEnum.INVOICE_ADDRESS_EMPTY.getCode(), DhubReturnCodeEnum.INVOICE_ADDRESS_EMPTY.getCode());
        }
        Response<OrderDTO> orderDTOResponse = orderQueryRemote.queryOrderDetailByOrderCode(request.getOrderCode());
        if (!orderDTOResponse.isSuccess()) {
            log.error("dhub查询订单详情失败:{}请求id:{}", orderDTOResponse, requestId);
            throw new CustomException(DhubReturnCodeEnum.SYSTEM_EXCEPTION.getNo(), DhubReturnCodeEnum.SYSTEM_EXCEPTION.getCode(), DhubReturnCodeEnum.SYSTEM_EXCEPTION.getCode());
        }
        OrderDTO model = orderDTOResponse.getModel();
        if (ObjectUtil.isEmpty(model) || !ChannelEnum.DHUB.getKey().equals(model.getChannelCode())) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_ORDER_CODE_NOT_EXIST.getNo(), DhubReturnCodeEnum.INVOICE_ORDER_CODE_NOT_EXIST.getCode(), DhubReturnCodeEnum.INVOICE_ORDER_CODE_NOT_EXIST.getCode());
        }
        final Integer CNY=0;
        if(!CNY.equals(model.getSaleCurrency())){
            throw new CustomException(DhubReturnCodeEnum.INVOICE_CURRENCY_NOT_SUPPORT.getNo(),DhubReturnCodeEnum.INVOICE_CURRENCY_NOT_SUPPORT.getCode(),DhubReturnCodeEnum.INVOICE_CURRENCY_NOT_SUPPORT.getCode());
        }
        Response<OrderInvoiceDTO> orderInvoiceDTOResponse = orderQueryRemote.queryOrderInvoiceByOrderCode(request.getOrderCode());
        if (!orderInvoiceDTOResponse.isSuccess()) {
            log.error("dhub查询发票详情失败{}请求id:{}", orderDTOResponse, request);
            throw new CustomException(DhubReturnCodeEnum.SYSTEM_EXCEPTION.getNo(), DhubReturnCodeEnum.SYSTEM_EXCEPTION.getCode(), DhubReturnCodeEnum.SYSTEM_EXCEPTION.getCode());
        }
        OrderInvoiceDTO invoiceDTO = orderInvoiceDTOResponse.getModel();

        if (ObjectUtil.isNotEmpty(invoiceDTO)) {
            throw new CustomException(DhubReturnCodeEnum.INVOICE_INFO_ALREADY_EXISTS.getNo(), DhubReturnCodeEnum.INVOICE_INFO_ALREADY_EXISTS.getCode(), DhubReturnCodeEnum.INVOICE_INFO_ALREADY_EXISTS.getCode());
        }
    }
    private static void rateValid(String invoiceTaxRate) {
        // 校验1：检查是否为数值
        try {
            BigDecimal taxRate = new BigDecimal(invoiceTaxRate);
            // 校验2：检查范围是否在0-100之间
            if (taxRate.compareTo(BigDecimal.ZERO) < 0 || taxRate.compareTo(new BigDecimal(100)) > 0) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_RATE_RANGE_ERROR.getNo(),
                        DhubReturnCodeEnum.INVOICE_RATE_RANGE_ERROR.getCode(),
                        DhubReturnCodeEnum.INVOICE_RATE_RANGE_ERROR.getCode());
            }
            // 校验3：检查小数位是否超过2位
            int scale2 = taxRate.stripTrailingZeros().scale();
            if (scale2 > 2) {
                throw new CustomException(DhubReturnCodeEnum.INVOICE_RATE_SCALE_ERROR.getNo(),
                        DhubReturnCodeEnum.INVOICE_RATE_SCALE_ERROR.getCode(),
                        DhubReturnCodeEnum.INVOICE_RATE_SCALE_ERROR.getCode());
            }
        } catch (NumberFormatException e) {
            // 非数值类型异常处理
            throw new CustomException(DhubReturnCodeEnum.INVOICE_RATE_FORMAT_ERROR.getNo(),
                    DhubReturnCodeEnum.INVOICE_RATE_FORMAT_ERROR.getCode(),
                    DhubReturnCodeEnum.INVOICE_RATE_FORMAT_ERROR.getCode());
        }
    }
    /**
     * 校验邮箱格式是否有效
     */
    private boolean isValidEmail(String email) {
        // 邮箱正则表达式
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        Pattern pattern = Pattern.compile(emailRegex);
        return pattern.matcher(email).matches();
    }

}
