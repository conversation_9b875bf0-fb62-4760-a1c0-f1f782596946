package com.tiangong.service.base.impl;

import com.alibaba.fastjson.JSON;
import com.tiangong.config.DhubConfig;
import com.tiangong.config.exception.CustomException;
import com.tiangong.constant.Constant;
import com.tiangong.convert.DTOConvert;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.HotelProductRemote;
import com.tiangong.dis.remote.ProductRemote;
import com.tiangong.dto.hotel.CountryCodeReq;
import com.tiangong.dto.product.request.ChildrenInfo;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.request.RoomGuestNumber;
import com.tiangong.dto.product.response.DhubProductDetailInfoResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.entity.request.hotel.HotelLowestPriceInfoRequest;
import com.tiangong.entity.request.hotel.ProductDetailInfoRequest;
import com.tiangong.entity.response.hotel.HotelLowestPriceInfoResponse;
import com.tiangong.entity.response.hotel.ProductDetailInfoResponse;
import com.tiangong.entity.vo.hotel.RoomGuestNumberDto;
import com.tiangong.enums.CheckInTypeEnum;
import com.tiangong.enums.NationalEnum;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.service.base.ProductService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import com.tiangong.utils.CommonUtil;
import com.tiangong.utils.CommonUtils;
import com.tiangong.utils.CreateTokenUtil;
import feign.Request;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;


@Slf4j
@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

    private final HotelProductRemote hotelProductRemote;

    private final ProductRemote productRemote;

    private final CreateTokenUtil createTokenUtil;

    private final HttpServletRequest httpServletRequest;

    private final DhubConfig dhubConfig;

    /**
     * 请求上下文中获取国内海外标识
     * 在 RequestValidateIntercept 设置
     * @return
     */
    private Integer getDomesticOrOverseas() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return (Integer) request.getAttribute(Constant.DOMESTICOROVERSEAS);
        }
        return null;
    }

    /**
     * 请求上下文中获取 90天报价输出开关
     * 在 RequestValidateIntercept 设置
     * @return
     */
    private Integer getQuoteswitch() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return (Integer) request.getAttribute(Constant.QUOTESWITCH);
        }
        return null;
    }

    /**
     * 请求上下文中获取 原始协议价
     * 在 RequestValidateIntercept 设置
     * @return
     */
    private Integer getOriginalProtocolPriceSwitch() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return (Integer) request.getAttribute(Constant.ORIGINALPROTOCOLPRICESWITCH);
        }
        return null;
    }

    /**
     * 酒店每日起价查询
     */
    @Override
    public HotelLowestPriceInfoResponse queryHotelLowestPrice(HotelLowestPriceInfoRequest request) {
        HotelLowestPriceInfoResponse hotelLowestPriceInfoResponse = new HotelLowestPriceInfoResponse();

        HotelLowestPriceRequest hotelLowestPriceRequest = DTOConvert.INSTANCE.convert(request);

        //全日房
        if (request.getCheckInType() == null || request.getCheckInType() == 1) {
            hotelLowestPriceRequest.setOnlyHourRoom(0);
        } else {
            //钟点房
            hotelLowestPriceRequest.setOnlyHourRoom(1);
        }

        if(dhubConfig.getExpedia()){
            createTokenUtil.expediaTokenAndIp(httpServletRequest,request.getPartnerCode());
        }


        hotelLowestPriceRequest.setUserAccount(request.getPartnerCode());
        ResponseResult<HotelLowestPriceResponse> hotelLowestPriceResponseResponseResult = hotelProductRemote.queryHotelLowestPrice(hotelLowestPriceRequest);
        CommonUtil.checkResponseResult(hotelLowestPriceResponseResponseResult);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(hotelLowestPriceResponseResponseResult.getReturnCode())) {
            DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(hotelLowestPriceResponseResponseResult.getReturnCode());
            throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
        }
        if (hotelLowestPriceResponseResponseResult.getBussinessResponse() != null) {
            hotelLowestPriceInfoResponse = DTOConvert.INSTANCE.convert(hotelLowestPriceResponseResponseResult.getBussinessResponse());
            hotelLowestPriceInfoResponse.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(hotelLowestPriceResponseResponseResult.getBussinessResponse().getCurrency())));
        }
        return hotelLowestPriceInfoResponse;
    }

    /**
     * 酒店实时产品查询
     */
    @Override
    public ProductDetailInfoResponse queryProductDetail(ProductDetailInfoRequest request) {

        ProductDetailInfoResponse productDetailInfoResponse = new ProductDetailInfoResponse();
        // 房间数量校验
        CommonUtil.checkRoomNum(request.getRoomNum());
        //校验国籍
        if (StringUtils.isNotEmpty(request.getNationality())){
            String nameByCode = NationalEnum.getNameByCode(request.getNationality());
            if (StringUtils.isEmpty(nameByCode)){
                throw new CustomException(DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.no, DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.code, DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.code);
            }
        }

        // 校验入离时间(90天报价输出开关校验)
        Integer quoteswitch = getQuoteswitch();
        if (quoteswitch != null && quoteswitch == 1) {
            CommonUtils.checkDate90(request.getCheckInDate(), request.getCheckOutDate());
        } else {
            CommonUtils.checkDateDefault(request.getCheckInDate(), request.getCheckOutDate());
        }
        // 1国内 2海外
        CountryCodeReq req = new CountryCodeReq();
        req.setHotelId(request.getHotelId());

        // 获取客户类型
        Integer domesticOrOverseas = request.getDomesticOrOverseas() != null ? request.getDomesticOrOverseas() : getDomesticOrOverseas();
        // 如果是海外，房间客人数量信息必传
        if (domesticOrOverseas == 2) {
            RoomGuestNumberDto roomGuestNumberDto = request.getRoomGuestNumber();
            // 校验房间人数信息
            checkRoom(roomGuestNumberDto);

            // 组装天宫格式
            List<RoomGuestNumberDto> roomGuestNumbers = new ArrayList<>();
            for (int i = 0; i < request.getRoomNum(); i++) {
                RoomGuestNumberDto dto = new RoomGuestNumberDto();
                dto.setRoomNumber(i + 1);
                dto.setChildrenAges(roomGuestNumberDto.getChildrenAges());
                dto.setNumberOfAdults(roomGuestNumberDto.getNumberOfAdults());
                dto.setNumberOfChildren(roomGuestNumberDto.getNumberOfChildren());
                roomGuestNumbers.add(dto);
            }
            request.setRoomGuestNumbers(roomGuestNumbers);
        }
        ProductDetailRequest productDetailRequest = DTOConvert.INSTANCE.convert(request);
        productDetailRequest.setSupplyType(String.valueOf(domesticOrOverseas));
        productDetailRequest.setLanguage(request.getLanguage());
        if (request.getNeedClockRoomProduct() == null || !request.getNeedClockRoomProduct()) {
            productDetailRequest.setOnlyHourRoom(CheckInTypeEnum.ALL_DAY_ROOM.tiangongkey);
        } else {
            productDetailRequest.setOnlyHourRoom(CheckInTypeEnum.CLOCK_ROOM.tiangongkey);
        }
        if (DateUtilX.getDay(DateUtilX.stringToDate(request.getCheckInDate()),
                DateUtilX.stringToDate(request.getCheckOutDate())) > 1) {
            // 连住不查钟点房，那就只查日历房
            productDetailRequest.setOnlyHourRoom(CheckInTypeEnum.ALL_DAY_ROOM.tiangongkey);
        }
        // 请求来源 1 Dhub
        productDetailRequest.setSource(Constant.ACTIVE_1);
        List<RoomGuestNumberDto> roomGuestNumberDtos = request.getRoomGuestNumbers();
        if (CollUtilX.isNotEmpty(roomGuestNumberDtos)) {
            List<RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
            RoomGuestNumber roomGuestNumber;
            int guestCount = 0;
            for (RoomGuestNumberDto roomGuestNumberDto : roomGuestNumberDtos) {
                roomGuestNumber = new RoomGuestNumber();
                roomGuestNumber.setRoomIndex(roomGuestNumberDto.getRoomNumber());
                roomGuestNumber.setAdultNum(roomGuestNumberDto.getNumberOfAdults());
                guestCount += roomGuestNumberDto.getNumberOfAdults();
                if (StrUtilX.isNotEmpty(roomGuestNumberDto.getChildrenAges())) {
                    List<ChildrenInfo> childrenInfos = new ArrayList<>();
                    ChildrenInfo childrenInfo;
                    // 校验儿童年龄数量和儿童数量是否一致
                    String[] ageSplit = roomGuestNumberDto.getChildrenAges().split(",");
                    if (null == roomGuestNumberDto.getNumberOfChildren()) {
                        throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code);
                    }
                    if (ageSplit.length != roomGuestNumberDto.getNumberOfChildren()) {
                        throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code);
                    }
                    for (String age : ageSplit) {
                        childrenInfo = new ChildrenInfo();
                        // 校验儿童年龄范围
                        int childAge = Integer.parseInt(age);
                        if (childAge < 0 || childAge > 17) {
                            throw new CustomException(DhubReturnCodeEnum.CHILDREN_AGE_ERROR.no, DhubReturnCodeEnum.CHILDREN_AGE_ERROR.code, DhubReturnCodeEnum.CHILDREN_AGE_ERROR.code);
                        }
                        childrenInfo.setChildrenAge(childAge);
                        childrenInfos.add(childrenInfo);
                    }
                    if (roomGuestNumberDto.getNumberOfChildren() != null && roomGuestNumberDto.getNumberOfChildren() > 0) {
                        guestCount += roomGuestNumberDto.getNumberOfChildren();
                    }
                    roomGuestNumber.setChildrenInfos(childrenInfos);
                }
                roomGuestNumbers.add(roomGuestNumber);
            }
            productDetailRequest.setGuestQuantity(guestCount);
            productDetailRequest.setRoomGuestNumbers(roomGuestNumbers);
        } else {
            if (domesticOrOverseas == 1) {
                List<RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
                //默认设置为1间
                if (request.getRoomNum() == null) {
                    request.setRoomNum(1);
                }
                for (int i = 0; i < request.getRoomNum(); i++) {
                    RoomGuestNumber roomGuestNumber = new RoomGuestNumber();
                    roomGuestNumber.setRoomIndex(i + 1);
                    if (request.getRoomGuestNumber() != null && request.getRoomGuestNumber().getNumberOfAdults() != null) {
                        roomGuestNumber.setAdultNum(request.getRoomGuestNumber().getNumberOfAdults());
                    } else {
                        roomGuestNumber.setAdultNum(2);
                    }
                    roomGuestNumbers.add(roomGuestNumber);
                }
                productDetailRequest.setRoomGuestNumbers(roomGuestNumbers);
            }
        }
        productDetailRequest.setLanguage(request.getLanguage());
        Date date = new Date();

        if(dhubConfig.getExpedia()){
            createTokenUtil.expediaTokenAndIp(httpServletRequest,request.getPartnerCode());
        }

        productDetailRequest.setUserAccount(request.getPartnerCode());
        // 设置国内全球标识
        productDetailRequest.setSupplyType(String.valueOf(domesticOrOverseas));
        String uuid = UUID.randomUUID().toString();
        request.setRequestId(uuid);
        productDetailRequest.setRequestId(request.getRequestId());
        productDetailRequest.setOriginalProtocolPriceSwitch(getOriginalProtocolPriceSwitch());
        productDetailRequest.setDomesticOrOverseas(domesticOrOverseas);
        productDetailRequest.setNationality(request.getNationality());
        ResponseResult<DhubProductDetailInfoResponse> productDetailResponseResponseResult = productRemote.queryDhubHotelProductDetail(productDetailRequest, new Request.Options(15, TimeUnit.SECONDS, 15, TimeUnit.SECONDS, true));
        CommonUtils.saveInfoSlsLog(date, Constant.TIANGONG + "-queryProductDetail", JSON.toJSONString(productDetailRequest), JSON.toJSONString(productDetailResponseResponseResult), "1", request.getPartnerCode());
        CommonUtil.checkResponseResult(productDetailResponseResponseResult);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(productDetailResponseResponseResult.getReturnCode())) {
            DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(productDetailResponseResponseResult.getReturnCode());
            throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
        }
        if (productDetailResponseResponseResult.getBussinessResponse() != null) {
            productDetailInfoResponse = DTOConvert.INSTANCE.convert(productDetailResponseResponseResult.getBussinessResponse());
        }
        productDetailInfoResponse.setHotelId(request.getHotelId());
        return productDetailInfoResponse;
    }

    public void checkRoom(RoomGuestNumberDto roomGuestNumberDto) {
        if (Objects.isNull(roomGuestNumberDto)) {
            throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_ROOM_GUEST_NUMBER.no, DhubReturnCodeEnum.EMPTY_PARAM_ROOM_GUEST_NUMBER.code, DhubReturnCodeEnum.EMPTY_PARAM_ROOM_GUEST_NUMBER.code);
        }

        if (roomGuestNumberDto.getNumberOfAdults() == null) {
            throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_NUMBER_OF_ADULTS.no, DhubReturnCodeEnum.EMPTY_PARAM_NUMBER_OF_ADULTS.code, DhubReturnCodeEnum.EMPTY_PARAM_NUMBER_OF_ADULTS.code);
        }
        if (roomGuestNumberDto.getNumberOfChildren() != null && roomGuestNumberDto.getNumberOfChildren() > 0) {
            String childrenAges = roomGuestNumberDto.getChildrenAges();
            if (StrUtilX.isEmpty(childrenAges)) {
                throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES.code);
            }
            if (!Pattern.compile("^\\d+(,\\d+)*$").matcher(childrenAges).matches()) {
                throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_TYPE_ERROR.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_TYPE_ERROR.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_TYPE_ERROR.code);
            }
            //校验儿童数量和年龄数量是否一致
            String[] agesNumber = childrenAges.split(",");
            if (agesNumber.length != roomGuestNumberDto.getNumberOfChildren()) {
                throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code);
            }
        }
    }
}
