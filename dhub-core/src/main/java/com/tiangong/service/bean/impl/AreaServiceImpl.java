package com.tiangong.service.bean.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.common.Response;
import com.tiangong.config.DhubConfig;
import com.tiangong.config.agentConfig.AgentConfig;
import com.tiangong.config.exception.CustomException;
import com.tiangong.constant.Constant;
import com.tiangong.convert.DTOConvert;
import com.tiangong.dis.dto.CityReq;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.AreaRemote;
import com.tiangong.dis.remote.HotelProductRemote;
import com.tiangong.dto.hotel.AddressRespDTO;
import com.tiangong.dto.hotel.HotCityResp;
import com.tiangong.dto.hotel.HotelSearchReq;
import com.tiangong.dto.hotel.HotelSearchResp;
import com.tiangong.entity.base.AgentAccountConfig;
import com.tiangong.entity.dto.base.*;
import com.tiangong.entity.request.basic.*;
import com.tiangong.entity.response.basic.*;
import com.tiangong.entity.vo.basic.Business;
import com.tiangong.entity.vo.basic.District;
import com.tiangong.enums.*;
import com.tiangong.service.bean.AreaService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.HttpUtilX;
import com.tiangong.util.StrUtilX;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AreaServiceImpl implements AreaService {

    @Autowired
    private DhubConfig dhubConfig;

    @Autowired
    private AreaRemote areaRemote;

    @Autowired
    private HotelProductRemote hotelProductRemote;

    /**
     * 查询国家信息
     */
    @Override
    public CountryListResponse queryCountryList(QueryCountryListRequest request) {
        CountryListResponse countryListResponse = new CountryListResponse();
        CommonReqDTO commonReqDTO = new CommonReqDTO();
        String language = request.getLanguage();
        if (StrUtilX.isEmpty(language)) {
            language = LanguageEnum.zh_CN.getValue();
        }
        commonReqDTO.setLanguageType(language);
        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/external/queryCountryList", JSON.toJSONString(commonReqDTO), Constant.REQCONFIG);
        if (StrUtilX.isEmpty(s)) {
            log.error("国家信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<List<CountryDTO>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<CountryDTO>>>() {
        });
        if (!GlobalErrorCodeConstants.SUCCESS.getCodeNo().equals(listResultX.getCode())) {
            log.error("国家信息查询失败,失败原因:" + s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        List<CountryDTO> countryDTOS = listResultX.getData();
        countryListResponse.setCountries(countryDTOS);
        return countryListResponse;
    }

    /**
     * 查询城市信息
     */
    @Override
    public CityListResponse queryCityList(CityListRequest request) {
        CityListResponse cityListResponse = new CityListResponse();

        String partnerCode = request.getPartnerCode();
        AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(partnerCode);
        if (partnerInfo == null) {
            throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
        }
        // 1国内 2海外
        Integer domesticOrOverseas = partnerInfo.getDomesticOrOverseas();
        if (domesticOrOverseas == null) {
            throw new CustomException(DhubReturnCodeEnum.INVALID_PARTNER.no, DhubReturnCodeEnum.INVALID_PARTNER.code, DhubReturnCodeEnum.INVALID_PARTNER.code);
        }
        //如果合作商是国内合作商，只能查询国内数据
        String countryCode = request.getCountryCode();
        if (StrUtilX.isEmpty(countryCode)) {
            countryCode = AreaTypeEnum.INLAND.getValue();
        }
        if (domesticOrOverseas == 1 && !AreaTypeEnum.INLAND.getValue().equals(countryCode)) {
            throw new CustomException(DhubReturnCodeEnum.DOMESTIC_OR_OVERSEAS_ERROR.no, DhubReturnCodeEnum.DOMESTIC_OR_OVERSEAS_ERROR.code, DhubReturnCodeEnum.DOMESTIC_OR_OVERSEAS_ERROR.code);
        } //else if (domesticOrOverseas == 2 && AreaTypeEnum.INLAND.getValue().equals(countryCode)) {
//            throw new CustomException(ReturnCodeEnum.DOMESTIC_OR_OVERSEAS_ERROR.no, ReturnCodeEnum.DOMESTIC_OR_OVERSEAS_ERROR.code, ReturnCodeEnum.DOMESTIC_OR_OVERSEAS_ERROR.code);
//        }
        CommonReqDTO commonReqDTO = new CommonReqDTO();
        commonReqDTO.setCountryCode(countryCode);
        commonReqDTO.setLanguageType(request.getLanguage());
        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/external/queryCityList", JSON.toJSONString(commonReqDTO), Constant.REQCONFIG);
        if (StrUtilX.isEmpty(s)) {
            log.error("城市信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<List<CityRespDTO>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<CityRespDTO>>>() {
        });
        if (!GlobalErrorCodeConstants.SUCCESS.getCodeNo().equals(listResultX.getCode())) {
            log.error("城市信息查询失败,失败原因:" + s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        List<CityRespDTO> cityRespDTOS = listResultX.getData();
        if (CollUtilX.isEmpty(cityRespDTOS)) {
            return cityListResponse;
        }
        cityListResponse.setProvinces(cityRespDTOS);
        return cityListResponse;
    }

    @Override
    public DistrictListResponse queryDistrictList(DistrictListRequest request) {
        DistrictListResponse districtListResponse = new DistrictListResponse();
        if (StringUtils.isEmpty(request.getCityCode())){
            throw new CustomException(ErrorCodeEnum.CITY_CODE_IS_NOT_EMPTY.errorNo, ErrorCodeEnum.CITY_CODE_IS_NOT_EMPTY.errorCode, ErrorCodeEnum.CITY_CODE_IS_NOT_EMPTY.errorCode);
        }
        JSONObject hotelReq = new JSONObject();
        hotelReq.put("languageType", request.getLanguage());
        hotelReq.put("parentCode", request.getCityCode());
        hotelReq.put("dataType", 4);//4行政区 5商业区
        hotelReq.put("currentPage", "1");
        hotelReq.put("pageSize", "500");
        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/es/selectAreaList", hotelReq.toJSONString(), Constant.REQCONFIG);
        if (StrUtilX.isEmpty(s)) {
            log.error("城市信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<List<District>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<District>>>() {
        });
        if (!GlobalErrorCodeConstants.SUCCESS.getCodeNo().equals(listResultX.getCode())) {
            log.error("品牌信息查询失败,失败原因:" + s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        districtListResponse.setDistricts(listResultX.getData());
        return districtListResponse;
    }

    @Override
    public BusinessListResponse queryBusinessList(BusinessListRequest request) {
        BusinessListResponse districtListResponse = new BusinessListResponse();
        if (StringUtils.isEmpty(request.getCityCode())){
            throw new CustomException(ErrorCodeEnum.CITY_CODE_IS_NOT_EMPTY.errorNo, ErrorCodeEnum.CITY_CODE_IS_NOT_EMPTY.errorCode, ErrorCodeEnum.CITY_CODE_IS_NOT_EMPTY.errorCode);
        }
        JSONObject hotelReq = new JSONObject();
        hotelReq.put("languageType", request.getLanguage());
        hotelReq.put("parentCode", request.getCityCode());
        hotelReq.put("dataType", 5);//4行政区 5商业区
        hotelReq.put("currentPage", "1");
        hotelReq.put("pageSize", "500");
        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/es/selectAreaList", hotelReq.toJSONString(), Constant.REQCONFIG);
        if (StrUtilX.isEmpty(s)) {
            log.error("城市信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<List<Business>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<Business>>>() {
        });
        if (!GlobalErrorCodeConstants.SUCCESS.getCodeNo().equals(listResultX.getCode())) {
            log.error("品牌信息查询失败,失败原因:" + s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        districtListResponse.setBusinesses(listResultX.getData());
        return districtListResponse;
    }

    /**
     * 品牌信息查询接口
     */
    @Override
    public HotelPlateListResponse queryHotelPlateList(HotelPlateListRequest request) {
        HotelPlateListResponse businessListResponse = new HotelPlateListResponse();
        CommonReqDTO commonReqDTO = new CommonReqDTO();
        commonReqDTO.setLanguageType(request.getLanguage());
        String s = HttpUtilX.post(dhubConfig.getBaseHotelUrl() + "/manage/external/queryGroupBrandList", JSON.toJSONString(commonReqDTO), Constant.REQCONFIG);
        if (StrUtilX.isEmpty(s)) {
            log.error("城市信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<List<BrandDTO>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<BrandDTO>>>() {
        });
        if (!GlobalErrorCodeConstants.SUCCESS.getCodeNo().equals(listResultX.getCode())) {
            log.error("品牌信息查询失败,失败原因:" + s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        List<BrandDTO> brandDTOS = listResultX.getData();
        businessListResponse.setHotelPlateList(brandDTOS);
        return businessListResponse;
    }



    @Override
    public HotCityResponse queryHotCityList(CityRequest req) {
        HotCityResponse response = new HotCityResponse();
        CityReq cityReq = DTOConvert.INSTANCE.cityConvert(req);
        Response<List<HotCityResp>> resp = areaRemote.queryHotCityList(cityReq);
        if (resp.isSuccess()){
            List<HotCityResp> model = resp.getModel();
            List<HotCity> list = new ArrayList<>();
            for (HotCityResp hotCityResp : model) {
                HotCity city = DTOConvert.INSTANCE.hotCityConvert(hotCityResp);
                list.add(city);
            }
            response.setList(list);
        }else {
            log.error("查询热门城市异常");
            return response;
        }
        return response;
    }

    @Override
    public AddressResponse getAddress(AddressRequest request) {
        AddressResponse response = new AddressResponse();
        Map<String, String> map = new HashMap<>();

        // 判断数据类型不为空，并且只能为 1 或 2
        if (request.getDataType() != null && (request.getDataType() < AddressEnum.HOTEL.getKey() || request.getDataType() > AddressEnum.CITY.getKey())) {
            throw new CustomException(
                    DhubReturnCodeEnum.INVALID_INPUTPARAM.no,
                    DhubReturnCodeEnum.INVALID_INPUTPARAM.code,
                    DhubReturnCodeEnum.INVALID_INPUTPARAM.description
            );
        }
        map.put("keyWord", request.getKeyWord());



         //0605版本，优化目的地接口需求 新增字段【热度值】、【目的地ID】
         //by. 雷燕军
        map.put("cityCode", request.getCityCode());
        map.put("dataType", String.valueOf(request.getDataType()));
        Response<List<AddressRespDTO>> result = hotelProductRemote.getAddress(map);
        if (result.isSuccess()){
            response.setDestinationDTOList(result.getModel());
        }else {
            log.error("查询目的地异常---返回的数据是：{}", result);
            throw new CustomException("999", result.getFailCode(), result.getFailCode());
        }
        return response;
    }

    @Override
    public HotelSearchResponse getHotelSearch(HotelSearchRequest request) {
        HotelSearchReq req = DTOConvert.INSTANCE.hotelSearchConvert(request);
        AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(request.getPartnerCode());
        if (partnerInfo == null) {
            throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
        }
        req.setAgentCode(partnerInfo.getAgentCode());
        Response<HotelSearchResp> hotelSearch = hotelProductRemote.getHotelSearch(req);
        if (hotelSearch.isSuccess()){
            HotelSearchResp model = hotelSearch.getModel();
            return DTOConvert.INSTANCE.hotelSearchRespConvert(model);
        }else {
            log.error("获取酒店过滤器异常----返回的数据是：{}", hotelSearch);
            throw new CustomException("999", hotelSearch.getFailCode(), hotelSearch.getFailCode());
        }
    }

}
