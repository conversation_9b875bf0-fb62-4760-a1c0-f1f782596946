package com.tiangong.utils;

import com.tiangong.config.DhubConfig;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.IpUtil;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.security.Key;
import java.util.Date;

@Component
public class CreateTokenUtil {

    private static final String AUTHORITIES_KEY = "auth";//权限的key值

    private static Key key;

    @Autowired
    private DhubConfig dhubConfig;

    /**
     * 创建token
     */
    private String createToken(String user) {
        this.key();

        //过期时间
        long now = (new Date()).getTime();
        // 12 * 60 *60 * 1000
        long hours = 12 * 60 * 60 * 1000;

        Date validity = new Date(now + hours);

        return Jwts.builder()
                .setSubject(user)//用户名
                .claim(AUTHORITIES_KEY, "1,2")//权限字符串
                .signWith(key, SignatureAlgorithm.HS256)//使用加密
                .setExpiration(validity)//过期时间
                .compact();
    }

    /**
     * 创建key
     */
    public void key() {
        byte[] keyBytes = Decoders.BASE64.decode(dhubConfig.getBase64Secret());
        key = Keys.hmacShaKeyFor(keyBytes);
    }

    public void expediaTokenAndIp(HttpServletRequest httpServletRequest, String partnerCode) {
        // 获取ip地址存入缓存，需要传给eps
        String ipAddress = IpUtil.getIpAddress(httpServletRequest);
        RedisTemplateX.set(RedisKey.USER_IPADDRESS + partnerCode, ipAddress);
        // 生成token存入缓存，需要传给eps
        String token = createToken(partnerCode);
        RedisTemplateX.set(RedisKey.TOKEN_RANDOM_ID + partnerCode, token.substring(token.length() - 32));
    }

}
