package com.tiangong.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.entity.base.BusinessRequest;
import com.tiangong.enums.ApiNameEnum;
import com.tiangong.util.StrUtilX;

/**
 * @author: zhiling
 * @date: 2024/3/7 10:56
 * @description: 加密解密测试
 */
public class DhubTest {

    //验签公钥(房仓)
    private static String customerPublicKey ="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDZ7MB4VS3Yk5FVdSYhXsHtZ33fHN8duDuWsTgL7BzcYg92f5GdOxoIyhBx8TWcnB/W7NGgJEeeSp/Q+7AdPCE6Kpvu2K6b94vnFxP2/M7ffr9+yh8X1onLqx2QYb/j9HcwEzF6uBGc3Uyya8EMuYqCIXhYwJNFEBZOqwMrO1iUCQIDAQAB";

    //加签私钥(房仓)
    private static String privateKey="MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANnswHhVLdiTkVV1JiFewe1nfd8c3x24O5axOAvsHNxiD3Z/kZ07GgjKEHHxNZycH9bs0aAkR55Kn9D7sB08IToqm+7Yrpv3i+cXE/b8zt9+v37KHxfWicurHZBhv+P0dzATMXq4EZzdTLJrwQy5ioIheFjAk0UQFk6rAys7WJQJAgMBAAECgYBGL3TRjIVmcihlgIRWTQFiJ2mnmNHW7YXrg4oMdO/ano5iWLWaEPXSjNl9CPV+Sd4LR1xAGk8ikOAl21meIMaGL7lYk8J/q5jLNY49nJ76ENw6vE621ymvy4b5318VKN5U2Exyxhv2/yPbIY2G+HBzLT9gZFxUKMySDJbk/V7WAQJBAP2pE6xOOSris0xIPUSt538q4SDJaBqXL9HN88kAVM1iLAPIIV0BFKpw5389qKRn2XFYcRV8rHiZeURLLNnkXEECQQDb70x9hDV2F8tAOF9D55d+ESI+41LIDs73+Z1R90KAAOwP4Yf+8DUzaxPcXKt8B61irSK53Ugg6KjNWi73BuXJAkEAnll+PoJWjb8XzvcJyGYHrgtGnwEcS/01jPPUzBW42r3+XB4DNdwG2p/A5F3JeVibFXbwoUlDIcYtPcTwKtW0AQJASLH6GGrAuP34You/gVRlUHgdV6gPzqyGYJSTGWqfAmxLmpwzNWL0UsCL2gSPcfmfp8QOfFzV4HrGgMnUSDZxcQJAGQVTBWqpr7NniDCHuadowR2HkCRIiGZ7nNXwCihFBRKlJ9bwJ1l58wG8CvY80hvisTtApJ3PHJ+l0nOn7BnUGg==";

    //加密解密密钥(房仓)
    private static String secretkey="I8YxbODxQmGOXmY0EHExe92U";

//    private static String customerPublicKey ="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHZkHpxRooM95heOEz0BtyhhepbYJLiuOAah57Su8JOkrMJs2MML/B5doeLj5XldRlQpmSgLCy7dRDnAIu2RSRy6Ac1ii7vuUc9FPOiU9qFA04ZCOSR+cxobRMc0nVcBR6ezISDw27CPaqLPsm81dCu/+FD28zfhoYD0djWvvD1wIDAQAB";
//    private static String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIdmQenFGigz3mF44TPQG3KGF6ltgkuK44BqHntK7wk6SswmzYwwv8Hl2h4uPleV1GVCmZKAsLLt1EOcAi7ZFJHLoBzWKLu+5Rz0U86JT2oUDThkI5JH5zGhtExzSdVwFHp7MhIPDbsI9qos+ybzV0K7/4UPbzN+GhgPR2Na+8PXAgMBAAECgYBwx2AHk8Fh0zJcBg+u1JzIG6EB4tEwkxNEFyAkmhnYmdFLktaK+t/+ZbH0iZgr5ATyAzNfsAl+QFMZrYyz8OUMA4+sZsJzYa7bZDPS6muUZoWv8JnJ2+G92TfXAWG/iQGUzeEz6zR7GOJpp0tl3gUe0H1jExsvT5m5/hBbnH7rIQJBAM+MB/d6zjOuDOGjxZCljjB7gzasd5Aj47lm94htvayg4uJI3j6GC+ov71YKUl6E+EHd/ii/f9MAuo4FP/9iP0MCQQCnAlrPQ9/7C0GU5T0lxDhvPJaD4ORUGYx4+uLb2WfxqvDMJnqIcD5fv9VgCwyzYlXH8OzaM4IFD5pvJSq1DE3dAkBLl9c2/lWoYRZ0ALButL4LTpFGEp8hstC4sJYr7LyO6bJ5dMztvOkODROBJRVHM+swImj18nneIn/QRn0+yUv9AkAqSB5VewmptR/VYnQoq5TeFKjGmpkz8YPu3RWyM0htlqmABf3frQsdkqOivSlqAOtWw1gzpfa08U5X9UDqtULBAkEAl+O6tATp23g407KoM3lewWl9eNoOXegXve2S+R97c0cBFdKCxcNTvUN+0YNwn8seXne1IznMB3MMbLLN8fozLQ==";



//    private static Long timestamp=1709780014106L;
//
//    /** 合作商编码 */
//    private static String partnerCode="testzhengyh01";
//
//    /** 请求接口名 */
//    private static String requestType="queryProductDetail";
//
//    /** 版本号 */
//    private static String version="1.0.0";
//
//    private static String body= "";

    /**
     * 测加密解密
     *
     */
    public static void main(String[] args) {
        //明文
        String body ="{\n" +
                "    \"businessRequest\": {\n" +
                "        \"endTime\": \"2024-03-07 15:00:35\",\n" +
                "        \"countryCode\": \"\",\n" +
                "        \"pageNo\": \"6\",\n" +
                "        \"startTime\": \"2024-03-02 00:00:35\"\n" +
                "    },\n" +
                "    \"header\": {\n" +
                "        \"partnerCode\": \"testzhengyh01\",\n" +
                "        \"requestType\": \"queryHotelIncrement\",\n" +
                "        \"signature\": \"3F2BFF0620AF9D64DDDAB5E0EDFE5A49\",\n" +
                "        \"timestamp\": 1709794836970,\n" +
                "        \"version\": \"1.0.0\"\n" +
                "    },\n" +
                "    \"privateKey\": \"MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANnswHhVLdiTkVV1JiFewe1nfd8c3x24O5axOAvsHNxiD3Z/kZ07GgjKEHHxNZycH9bs0aAkR55Kn9D7sB08IToqm+7Yrpv3i+cXE/b8zt9+v37KHxfWicurHZBhv+P0dzATMXq4EZzdTLJrwQy5ioIheFjAk0UQFk6rAys7WJQJAgMBAAECgYBGL3TRjIVmcihlgIRWTQFiJ2mnmNHW7YXrg4oMdO/ano5iWLWaEPXSjNl9CPV+Sd4LR1xAGk8ikOAl21meIMaGL7lYk8J/q5jLNY49nJ76ENw6vE621ymvy4b5318VKN5U2Exyxhv2/yPbIY2G+HBzLT9gZFxUKMySDJbk/V7WAQJBAP2pE6xOOSris0xIPUSt538q4SDJaBqXL9HN88kAVM1iLAPIIV0BFKpw5389qKRn2XFYcRV8rHiZeURLLNnkXEECQQDb70x9hDV2F8tAOF9D55d+ESI+41LIDs73+Z1R90KAAOwP4Yf+8DUzaxPcXKt8B61irSK53Ugg6KjNWi73BuXJAkEAnll+PoJWjb8XzvcJyGYHrgtGnwEcS/01jPPUzBW42r3+XB4DNdwG2p/A5F3JeVibFXbwoUlDIcYtPcTwKtW0AQJASLH6GGrAuP34You/gVRlUHgdV6gPzqyGYJSTGWqfAmxLmpwzNWL0UsCL2gSPcfmfp8QOfFzV4HrGgMnUSDZxcQJAGQVTBWqpr7NniDCHuadowR2HkCRIiGZ7nNXwCihFBRKlJ9bwJ1l58wG8CvY80hvisTtApJ3PHJ+l0nOn7BnUGg==\",\n" +
                "    \"sekretKey\": \"I8YxbODxQmGOXmY0EHExe92U\"\n" +
                "}";

        JSONObject jsonObject = JSONObject.parseObject(body, JSONObject.class);
        //请求体内容
        JSONObject businessRequest = jsonObject.getJSONObject("businessRequest");
        JSONObject header = jsonObject.getJSONObject("header");
        if (null == businessRequest){
            System.out.println("businessRequest对象不允许为空,"+businessRequest);
        }
        if (null == header){
            System.out.println("header对象不允许为空,"+header);
        }


        //加密
//        String data = dhubEncode2(body);
        String data="rlvYyPp7KvkI7fxFkNow4Tz82DrVbcl5H5bgu+vxY58HRS3wZ1IBEEBlJBW2elHqxOmsZNqsXNH4oxVmKlLbcjVRGt9rF6QldRPtkHT2ZNIJDFGuO51EbjTzMmBpLm8WQZVi/xPoYcAG4DI8krnA+w93pf+SOy3bQ2Tw+yKtmeOFu5YPfXquxQ2lxE2K6Ujuc39HJpJ37DyhZbJMP50iKM/sNzV9Hi3Num53RV7wc3EGnwjdYqnQuhvuYJ5qD5R9McY/jn5PKUXa8+BXsoYURgThUv3cxk4Eu99nlLlYmoz1Qyihp1xJ9PFSGz9JaeXHNX5hpUi5vTXZ72mm0xZ43HX34u/GHlphbTT5N63+qojMXHq87YHXIGgQemoIExeoDrTpOx3o64knS6l19cSylw==";
        System.out.println();
        System.out.println("====================================================================================================");
        System.out.println();
        //解密
        dhubDncode(header,data);

        //1709795633440testzhengyh01createOrder1.0.0{"arriveTime":"17:00","bedType":"1000000","checkInDate":"2024-03-13","checkOutDate":"2024-03-14","coOrderCode":"TMCHubtest202403071513","email":"<EMAIL>","guestInfos":[{"guestName":"测试","nationality":0}],"hotelId":398961,"latestArriveTime":"18:00","linkMan":"郑尧辉","linkPhone":"13632586371","priceItems":[{"breakfastNum":0,"breakfastType":0,"saleDate":"2024-03-13","salePrice":12.0}],"ratePlanId":"249071397","remark":"TMC-Hub的API测试","roomId":1785368,"roomNum":1,"specialDemand":"测试数据","supplyCode":"S10025411","totalAmount":12.0}
        //1709795633440testzhengyh01createOrder1.0.0{"arriveTime":"17:00","bedType":"1000000","checkInDate":"2024-03-13","checkOutDate":"2024-03-14","coOrderCode":"TMCHubtest202403071513","email":"<EMAIL>","guestInfos":[{"guestName":"测试","nationality":0}],"hotelId":398961,"latestArriveTime":"18:00","linkMan":"郑尧辉","linkPhone":"13632586371","priceItems":[{"breakfastNum":0,"breakfastType":0,"saleDate":"2024-03-13","salePrice":12}],"ratePlanId":"249071397","remark":"TMC-Hub的API测试","roomId":1785368,"roomNum":1,"specialDemand":"测试数据","supplyCode":"S10025411","totalAmount":12}

    }

    /**
     * 请求内容加密
     *
     * @param body
     * @return
     */
    public static String dhubEncode2(String body) {
        JSONObject jsonObject = JSONObject.parseObject(body, JSONObject.class);
        //请求体内容
        JSONObject businessRequest = jsonObject.getJSONObject("businessRequest");
        JSONObject header = jsonObject.getJSONObject("header");
        if (null == businessRequest){
            System.out.println("businessRequest对象不允许为空,"+businessRequest);
        }
        if (null == header){
            System.out.println("header对象不允许为空,"+header);
        }

        String requestType = header.getString("requestType");
        System.out.println("请求接口："+requestType+",请求业务对象:"+businessRequest);

        // 必须用接口对应的请求类型的class强转，否则拿不到各接口对应的业务参数
        // 处理json串收尾引号和转义符，形成合法的json字符串
        Class<?> className = ApiNameEnum.getClazzByApiName(requestType);
        if (null == className) {
            System.out.println("对应的接口不存在");
        }
        BusinessRequest originalRequest = (BusinessRequest) JSON.parseObject(businessRequest.toJSONString(), className);
        if (null == businessRequest) {
            System.out.println("转换成实际对象失败");
        }
        System.out.println("实际对象请求业务对象："+JSON.toJSONString(originalRequest));

        //3.构造待加签的字符串
        String signOriginalText = getSignOriginalText(header, JSON.toJSONString(originalRequest));

        // 4. 开始加签
        String sign = CommonUtils.rsa256Sign(signOriginalText, privateKey);
        System.out.println("生成的签名：" + sign);

        // 5.将签名设置到BusinessRequest中，以便一起加密
        originalRequest.setSign(sign);

        // 6. 得到待加密的字符串，这里两次toJSONString是为了得到转义符，跟加签时的区别之处
        String cryptoOriginalText = JSONObject.toJSONString(JSONObject.toJSONString(originalRequest));
        System.out.println("待加密明文：" + cryptoOriginalText);

        // 7. 开始加密
        String cryptoText = CommonUtils.aesEncodeBase64(cryptoOriginalText, secretkey);
        System.out.println("加密结果：" + cryptoText);
        return cryptoText;
    }


    /**
     * 请求内容解密
     *
     * @return
     */
    private static void dhubDncode(JSONObject header, String data) {
        // 解密请求内容
        String decodedText = CommonUtils.aesDecodeBase64(data, secretkey);
        if (StrUtilX.isEmpty(decodedText)) {

            System.out.println("解密失败");
        }
        String requestType = header.getString("requestType");
        // 消除最外面的双引号，以便可以正常转对象，如果字符串正常可省略
        decodedText = JSON.parseObject(decodedText, String.class);

        System.out.println("请求接口："+requestType+",解密后字符串:"+decodedText);

        // 必须用接口对应的请求类型的class强转，否则拿不到各接口对应的业务参数
        // 处理json串收尾引号和转义符，形成合法的json字符串
        Class<?> className = ApiNameEnum.getClazzByApiName(requestType);
        if (null == className) {
            System.out.println("对应的接口不存在");
        }

        BusinessRequest businessRequest = (BusinessRequest) JSON.parseObject(decodedText, className);
        if (null == businessRequest) {
            System.out.println("解密后数据转换失败");
        }
        System.out.println("请求业务对象："+JSON.toJSONString(businessRequest));

        /** 开始验签 **/
        String sign = businessRequest.getSign();
        if (StrUtilX.isEmpty(sign)) {
            System.out.println("请求签名不能为空");
        } else {
            // 加签时的对象是不含签名的，故要先清空
            businessRequest.setSign(null);
            // 解密后的内容转成对象
            String decodeText = JSON.toJSONString(businessRequest);
            if (!checkSign(header, sign, decodeText)) {
                System.out.println("验签时请求签名不正确ERROR");
            }else {
                System.out.println("验签通过OK");
            }
        }
    }


    private static boolean checkSign(JSONObject header, String sign, String data) {
        StringBuffer sb = new StringBuffer();
        sb.append(header.getString("timestamp"));
        sb.append(header.getString("partnerCode"));
        sb.append(header.getString("requestType"));
        sb.append(header.getString("version"));
        sb.append(data);
        System.out.println("验签请求data:"+sb.toString());
                 return CommonUtils.rsa256CheckSign(sb.toString(), sign, customerPublicKey);
    }

    /**
     * 签名原始数据
     * mer_no+method+nonce_str+timestamp+ver_no+body
     *
     * @return
     */
    private static String getSignOriginalText(JSONObject header, String data) {
        StringBuffer sb = new StringBuffer();
        sb.append(header.getString("timestamp"));
        sb.append(header.getString("partnerCode"));
        sb.append(header.getString("requestType"));
        sb.append(header.getString("version"));
        sb.append(data);
        System.out.println("签名明文数据："+sb.toString());
        return sb.toString();
    }



}
