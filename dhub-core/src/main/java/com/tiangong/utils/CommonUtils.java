package com.tiangong.utils;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.tiangong.config.exception.CustomException;
import com.tiangong.constant.Constant;
import com.tiangong.dis.dto.CurrencyCoinDTO;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.enums.SlsEnum;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.SlsLoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

@Slf4j
public class CommonUtils {

    private static final SlsLoggerUtil slsLoggerUtil;

    static {
        slsLoggerUtil = SpringUtil.getBean("slsLoggerUtil", SlsLoggerUtil.class);
    }

    /**
     * 校验日期
     * 入住日期、离店日期是否大于当前时间，离店日期是否大于入住日期
     * 入离日期是否大于30天
     */
    public static void checkDate30(String checkInDateStr, String checkOutDateStr) {
        Date checkInDate = DateUtilX.stringToDate(checkInDateStr);
        Date checkOutDate = DateUtilX.stringToDate(checkOutDateStr);
        checkDateValidity(checkInDate, checkOutDate);
        if (DateUtilX.getDay(checkInDate, checkOutDate) > 30) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_DIFFER_30.no, DhubReturnCodeEnum.DAYS_DIFFER_30.code, DhubReturnCodeEnum.DAYS_DIFFER_30.code);
        }
    }

    /**
     * 校验日期
     * 入住日期、离店日期是否大于当前时间
     */
    public static void checkDateNow(String checkInDateStr, String checkOutDateStr) {
        Date checkInDate = DateUtilX.stringToDate(checkInDateStr);
        Date checkOutDate = DateUtilX.stringToDate(checkOutDateStr);
        if (DateUtilX.compare(DateUtilX.getCurrentDate(), checkInDate) > 0) {
            throw new CustomException(DhubReturnCodeEnum.CHECK_IN_DATE_COMPANY_TO_NOW_ERROR.no, DhubReturnCodeEnum.CHECK_IN_DATE_COMPANY_TO_NOW_ERROR.code, DhubReturnCodeEnum.CHECK_IN_DATE_COMPANY_TO_NOW_ERROR.code);
        }
        if (DateUtilX.compare(DateUtilX.getCurrentDate(), checkOutDate) > 0) {
            throw new CustomException(DhubReturnCodeEnum.CHECK_OUT_DATE_COMPANY_TO_NOW_ERROR.no, DhubReturnCodeEnum.CHECK_OUT_DATE_COMPANY_TO_NOW_ERROR.code, DhubReturnCodeEnum.CHECK_OUT_DATE_COMPANY_TO_NOW_ERROR.code);
        }
    }

    /**
     * 校验入住日期、离店日期是否大于当前时间，离店日期是否大于入住日期
     */
    public static void checkDateValidity(Date checkInDate, Date checkOutDate) {
//        if (DateUtil.compare(DateUtil.getCurrentDate(), checkInDate) > 0) {
//            throw new CustomException(ReturnCodeEnum.CHECK_IN_DATE_COMPANY_TO_NOW_ERROR.no, ReturnCodeEnum.CHECK_IN_DATE_COMPANY_TO_NOW_ERROR.code, ReturnCodeEnum.CHECK_IN_DATE_COMPANY_TO_NOW_ERROR.description);
//        }
//        if (DateUtil.compare(DateUtil.getCurrentDate(), checkOutDate) > 0) {
//            throw new CustomException(ReturnCodeEnum.CHECK_OUT_DATE_COMPANY_TO_NOW_ERROR.no, ReturnCodeEnum.CHECK_OUT_DATE_COMPANY_TO_NOW_ERROR.code, ReturnCodeEnum.CHECK_OUT_DATE_COMPANY_TO_NOW_ERROR.description);
//        }
        if (DateUtilX.compare(checkInDate, checkOutDate) >= 0) {
            throw new CustomException(DhubReturnCodeEnum.CHECK_IN_DATE_MUST_GREATER_THAN_CHECK_OUT_DATE.no, DhubReturnCodeEnum.CHECK_IN_DATE_MUST_GREATER_THAN_CHECK_OUT_DATE.code, DhubReturnCodeEnum.CHECK_IN_DATE_MUST_GREATER_THAN_CHECK_OUT_DATE.code);
        }
    }

    public static void checkDateDefault(String checkInDateStr, String checkOutDateStr) {
        Date checkInDate = DateUtilX.stringToDate(checkInDateStr);
        Date checkOutDate = DateUtilX.stringToDate(checkOutDateStr);
        checkDateValidity(checkInDate, checkOutDate);
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkInDate) >= 545) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_545.no, DhubReturnCodeEnum.DAYS_LIMITED_545.code, DhubReturnCodeEnum.DAYS_LIMITED_545.code);
        }
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkOutDate) >= 575) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_575.no, DhubReturnCodeEnum.DAYS_LIMITED_575.code, DhubReturnCodeEnum.DAYS_LIMITED_575.code);
        }
        if (DateUtilX.getDay(checkInDate, checkOutDate) > 30) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_DIFFER_30.no, DhubReturnCodeEnum.DAYS_DIFFER_30.code, DhubReturnCodeEnum.DAYS_DIFFER_30.code);
        }
    }

    public static void checkDate90(String checkInDateStr, String checkOutDateStr) {
        Date checkInDate = DateUtilX.stringToDate(checkInDateStr);
        Date checkOutDate = DateUtilX.stringToDate(checkOutDateStr);
        checkDateValidity(checkInDate, checkOutDate);
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkInDate) >= 545) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_545.no, DhubReturnCodeEnum.DAYS_LIMITED_545.code, DhubReturnCodeEnum.DAYS_LIMITED_545.code);
        }
        if (DateUtilX.getDay(DateUtilX.getCurrentDate(), checkOutDate) >= 635) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_LIMITED_635.no, DhubReturnCodeEnum.DAYS_LIMITED_635.code, DhubReturnCodeEnum.DAYS_LIMITED_635.code);
        }
        if (DateUtilX.getDay(checkInDate, checkOutDate) > 90) {
            throw new CustomException(DhubReturnCodeEnum.DAYS_DIFFER_90.no, DhubReturnCodeEnum.DAYS_DIFFER_90.code, DhubReturnCodeEnum.DAYS_DIFFER_90.code);
        }
    }


    public static void checkTime(Date startTime, Date endTime) {
        if (startTime.getTime() - System.currentTimeMillis() > 0) {
            throw new CustomException(DhubReturnCodeEnum.CHECK_IN_DATE_MUST_LESS_THAN_NOW.no, DhubReturnCodeEnum.CHECK_IN_DATE_MUST_LESS_THAN_NOW.code, DhubReturnCodeEnum.CHECK_IN_DATE_MUST_LESS_THAN_NOW.code);
        }
        if (endTime.getTime() - System.currentTimeMillis() > 0) {
            throw new CustomException(DhubReturnCodeEnum.CHECK_OUT_DATE_MUST_LESS_THAN_NOW.no, DhubReturnCodeEnum.CHECK_OUT_DATE_MUST_LESS_THAN_NOW.code, DhubReturnCodeEnum.CHECK_OUT_DATE_MUST_LESS_THAN_NOW.code);
        }
        if ((endTime.getTime() - startTime.getTime()) / 1000 > 3600 * 24 * 7) {
            throw new CustomException(DhubReturnCodeEnum.CHECK_IN_DATE_MUST_DIFFERENCE_CHECK_OUT_DATE_7_DAYS.no, DhubReturnCodeEnum.CHECK_IN_DATE_MUST_DIFFERENCE_CHECK_OUT_DATE_7_DAYS.code, DhubReturnCodeEnum.CHECK_IN_DATE_MUST_DIFFERENCE_CHECK_OUT_DATE_7_DAYS.code);
        }
    }

    /**
     * sha256WithRsa 签名
     *
     * @param content    要加签名的内容
     * @param privateKey 签名的私钥
     * @return 若签名失败则返回null
     */
    public static String rsa256Sign(String content, String privateKey) {
        try {
            String charset = "UTF-8";
            PrivateKey priKey = getPrivateKeyFromPKCS8("RSA", new ByteArrayInputStream(privateKey.getBytes()));

            if (priKey != null) {
                java.security.Signature signature = java.security.Signature.getInstance("SHA256WithRSA");
                signature.initSign(priKey);
                signature.update(content.getBytes(charset));

                byte[] signed = signature.sign();
                return new String(Base64.encodeBase64(signed));
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("sha256WithRsa签名,出现异常", e);
            return null;
        }
    }

    /**
     * sha256WithRsa 验签
     *
     * @param content   要验签的内容
     * @param sign      要验证的签名
     * @param publicKey 验签的公钥
     * @return 验签是否通过
     */
    public static boolean rsa256CheckSign(String content, String sign, String publicKey) {
        try {
            String charset = "UTF-8";
            PublicKey pubKey = getPublicKeyFromX509("RSA", new ByteArrayInputStream(publicKey.getBytes()));
            java.security.Signature signature = java.security.Signature.getInstance("SHA256WithRSA");
            signature.initVerify(pubKey);
            signature.update(content.getBytes(charset));
            return signature.verify(Base64.decodeBase64(sign.getBytes()));
        } catch (Exception e) {
            log.error("sha256WithRsa校验签名,出现异常", e);
            return false;
        }
    }

    /**
     * aes加密，密文是base64编码
     *
     * @param data 明文
     * @param key  加密key
     * @return 若加密失败返回null
     */
    public static String aesEncodeBase64(String data, String key) {
        return CommonUtils.aesBase64(data, key, true);
    }

    /**
     * aes解密，密文是base64编码
     *
     * @param data 密文
     * @param key  解密key
     * @return 若解密失败返回null
     */
    public static String aesDecodeBase64(String data, String key) {
        return CommonUtils.aesBase64(data, key, false);
    }

    /**
     * aes加密或解密，密文是base64编码
     *
     * @param data       明文或密文
     * @param key        加密或解密的key
     * @param encodeMode true-加密  false-解密
     * @return 若加密或解密失败返回null
     */
    private static String aesBase64(String data, String key, boolean encodeMode) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(key)) {
            return null;
        }

        try {
            String KEY_AES = "AES", defaultCharset = "UTF-8", CIPHER_IALGORITHM = "AES/ECB/PKCS5Padding";
            byte[] content;
            if (encodeMode) {
                content = data.getBytes(defaultCharset);
            } else {
                content = CommonUtils.decodeBase64(data);
            }
            // MD5后取16位
            key = DigestUtils.md5Hex(key.getBytes()).substring(8, 24);

            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(defaultCharset), KEY_AES);
            Cipher cipher = Cipher.getInstance(CIPHER_IALGORITHM);
            cipher.init(encodeMode ? Cipher.ENCRYPT_MODE : Cipher.DECRYPT_MODE, keySpec);
            byte[] result = cipher.doFinal(content);
            if (encodeMode) {
                return CommonUtils.encodeBase64(result);
            } else {
                return new String(result, defaultCharset);
            }
        } catch (Exception e) {
            log.error("aes加密或解密, 出现异常", e);
            return null;
        }
    }

    /**
     * 生成Base64字符串
     */
    private static String encodeBase64(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    /**
     * 解码Base64字符串
     */
    private static byte[] decodeBase64(String message) {
        return Base64.decodeBase64(message);
    }

    /**
     * 从PKCS8中转换成私有密钥
     *
     * @param algorithm 算法
     * @param ins       流数据
     * @return 若转换失败则返回null
     */
    private static PrivateKey getPrivateKeyFromPKCS8(String algorithm, InputStream ins) {
        if (ins == null || StringUtils.isEmpty(algorithm)) {
            return null;
        }

        try {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
            byte[] encodedKey = IOUtils.toByteArray(ins);
            encodedKey = Base64.decodeBase64(encodedKey);

            return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
        } catch (Exception e) {
            log.error("从PKCS8中转换成私有密钥,出现异常", e);
            return null;
        }
    }

    /**
     * 从X509中转换成公钥
     *
     * @param algorithm 算法
     * @param ins       流数据
     * @return 若转换失败则返回null
     */
    private static PublicKey getPublicKeyFromX509(String algorithm, InputStream ins) {
        if (ins == null || StringUtils.isEmpty(algorithm)) {
            return null;
        }

        try {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
            byte[] encodedKey = IOUtils.toByteArray(ins);
            encodedKey = Base64.decodeBase64(encodedKey);

            return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
        } catch (Exception e) {
            log.error("从X509中转换成公钥,出现异常", e);
            return null;
        }
    }


    private static Map<String, CurrencyCoinDTO> convertToNewStringMap(Map<Object, Object> oldCacheMap) {
        if (oldCacheMap == null || oldCacheMap.size() == 0) {
            return new HashMap<>();
        }
        Map<String, CurrencyCoinDTO> resultMap = new HashMap<>();
        for (Map.Entry<Object, Object> entry : oldCacheMap.entrySet()) {
            String key = entry.getKey().toString();
            String value = entry.getValue().toString();
            resultMap.put(key, JSONUtil.toBean(value, CurrencyCoinDTO.class));
        }
        return resultMap;
    }


    public static void saveInfoSlsLog(Date start, String message, String request,
                                      String response, String status, String additional) {
        try {
            Date endDate = new Date();
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), Constant.SLSLOGLEVEL);
            map.put(SlsEnum.NAME.getType(), Constant.APPLICATIONNAME);
            map.put(SlsEnum.MESSAGE.getType(), message);
            map.put("source", Constant.APPLICATIONNAME);
            map.put("request", request);
            map.put("response", response);
            map.put("requestTime", DateUtilX.dateToString(start, Constant.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_MS));
            map.put("responseTime", DateUtilX.dateToString(endDate, Constant.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_MS));
            map.put("status", status);
            map.put("costTime", String.valueOf(endDate.getTime() - start.getTime()));
            map.put("additional", additional);
            slsLoggerUtil.saveLog(map, message, Constant.APPLICATIONNAME);
        } catch (Exception e) {
            log.error("保存日志异常", e);
        }
    }

    public static void saveErrorSlsLog(Date start, String message, String request,
                                       String response, String status, String additional) {
        try {
            Date endDate = new Date();
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), Constant.SLSLOGLEVEL_ERROR);
            map.put(SlsEnum.NAME.getType(), Constant.APPLICATIONNAME);
            map.put(SlsEnum.MESSAGE.getType(), message);
            map.put("request", request);
            map.put("response", response);
            map.put("requestTime", DateUtilX.dateToString(start, Constant.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_MS));
            map.put("responseTime", DateUtilX.dateToString(endDate, Constant.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_MS));
            map.put("status", status);
            map.put("costTime", String.valueOf(endDate.getTime() - start.getTime()));
            map.put("additional", additional);
            slsLoggerUtil.saveLog(map, message, Constant.APPLICATIONNAME);
        } catch (Exception e) {
            log.error("保存日志异常", e);
        }
    }

    public static boolean validateIncrement(List<Integer> itemList) {
        if (itemList.isEmpty()) {
            return true; // 空集合，无需校验
        }
        Collections.sort(itemList);
        int expectedId = 1;
        for (Integer item : itemList) {
            if (item != expectedId) {
                return false; // 校验失败，id 值不是按预期自增
            }
            expectedId++;
        }
        return true; // 校验通过，id 值按预期自增
    }

    /**
     * 校验字符串中是否包含数字
     */
    public static boolean containsDigit(String str) {
        return str != null && str.matches(".*\\d.*");
    }
}
