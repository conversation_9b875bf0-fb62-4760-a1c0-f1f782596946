<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.settle.collectPayment.mapper.SettleServiceJobMapper">

    <resultMap id="SettleServiceJobPageMap" type="com.tiangong.settle.collectPayment.domain.resp.SettleServiceJobResp">
        <result property="jobId" column="job_id"/>
        <result property="hotelId" column="hotel_id"/>
        <result property="hotelName" column="hotel_name"/>
        <result property="taskCode" column="task_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="searialNo" column="searial_no"/>
        <result property="bizNo" column="biz_no"/>
        <result property="orderType" column="order_type"/>
        <result property="ddtStatus" column="ddt_status"/>
        <result property="tgBillCode" column="tg_bill_code"/>
        <result property="tgStatus" column="tg_status"/>
        <result property="progressStatus" column="progress_status"/>
        <result property="paymentAmt" column="payment_amt"/>
        <result property="paymentDt" column="payment_dt"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>

        <collection property="settleServiceJobLogList" select="com.tiangong.settle.collectPayment.mapper.SettleServiceJobMapper.selectSettleServiceJobLogList"
                    column="job_id" javaType="ArrayList" ofType="com.tiangong.settle.settleServiceJob.domain.resp.SettleServiceJobLogResp">
            <result property="logType" column="logType"/>
            <result property="content" column="content"/>
            <result property="createdBy" column="createdBy"/>
            <result property="createdDt" column="createdDt"/>
            <result property="updatedBy" column="updatedBy"/>
            <result property="updatedDt" column="updatedDt"/>
        </collection>
    </resultMap>

    <select id="selectSettleServiceJobPage" resultMap="SettleServiceJobPageMap">
        select
            t1.job_id,
            t1.hotel_id,
            t1.hotel_name,
            t1.task_code,
            t1.business_code,
            t1.transfer_status,
            t1.searial_no,
            t1.biz_no,
            t1.order_type,
            t1.ddt_status,
            t1.tg_bill_code,
            t1.tg_status,
            t1.progress_status,
            t1.payment_amt,
            t1.payment_dt,
            t1.created_by,
            t1.created_dt,
            t1.updated_by,
            t1.updated_dt
        from f_settle_service_job t1
        where 1 = 1
        <if test="req.orderType != null">
            and t1.order_type = #{req.orderType}
        </if>
        <if test="req.businessCode != null and req.businessCode != ''">
            and t1.business_code = #{req.businessCode}
        </if>
        <if test="req.progressStatus != null">
            and t1.progress_status = #{req.progressStatus}
        </if>
        <if test="req.transferStatus != null">
            and t1.transfer_status = #{req.transferStatus}
        </if>
        <if test="req.tgStatus != null">
            and t1.tg_status = #{req.tgStatus}
        </if>
        <if test="req.ddtStatus != null">
            and t1.ddt_status = #{req.ddtStatus}
        </if>
        <if test="req.hotelId != null">
            and t1.hotel_id = #{req.hotelId}
        </if>
        order by t1.updated_dt desc
    </select>

    <select id="selectSettleServiceJobLogList" resultType="com.tiangong.settle.settleServiceJob.domain.resp.SettleServiceJobLogResp">
        SELECT
            t1.job_id,
            t1.log_type,
            t1.content,
            t1.created_dt,
            t1.created_by,
            t1.updated_dt,
            t1.updated_by
        FROM f_settle_service_job_log t1
        INNER JOIN (
                SELECT
                    log_type,
                    MAX(created_dt) AS max_created_dt
                FROM
                    f_settle_service_job_log
                WHERE job_id = #{jobId}
                GROUP BY log_type
                ) t2
        ON t1.log_type = t2.log_type AND t1.created_dt = t2.max_created_dt
    </select>
</mapper>