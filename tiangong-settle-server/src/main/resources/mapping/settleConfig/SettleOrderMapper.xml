<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.settle.settleOrder.mapper.SettleOrderMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.settle.settleOrder.domain.entity.SettleOrderEntity">
        <result property="id" column="id"/>
        <result property="merchantCode" column="merchant_code"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="checkInDate" column="check_in_date"/>
        <result property="checkOutDate" column="check_out_date"/>
        <result property="orderCode" column="order_code"/>
        <result property="orderDt" column="order_dt"/>
        <result property="supplyOrderCode" column="supply_order_code"/>
        <result property="tgSupplyOrderDt" column="tg_supply_order_dt"/>
        <result property="ddtMerchantCode" column="ddt_merchant_code"/>
        <result property="ddtCustomerCode" column="ddt_customer_code"/>
        <result property="ddtOrderCode" column="ddt_order_code"/>
        <result property="ddtOrderDt" column="ddt_order_dt"/>
        <result property="settleDt" column="settle_dt"/>
        <result property="settleType" column="settle_type"/>
        <result property="payerAccountId" column="payer_account_id"/>
        <result property="sendStatus" column="send_status"/>
        <result property="sendDt" column="send_dt"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <select id="queryNotFailSettleServiceJob" resultType="com.tiangong.settle.resp.SettleServiceJobDto">
        select
            t2.payment_dt
        from f_settle_order t1
        left join f_settle_service_job t2 on t2.business_code = t1.ddt_order_code
        where t1.supply_order_code = #{supplyOrderCode} and t2.transfer_status not in (2)
    </select>


</mapper>