<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <style>
        /*body {*/
            /*background: #E8E8ED;*/
        /*}*/

        * {
            margin: 0;
            padding: 0;
        }

        li {
            list-style-type: none;
        }

        .content {
            width: 606px;
            border-radius: 15px;
            background: #F8F8FA;
            margin: 20px auto;
        }

        .headInfo {
            width: 606px;
            height: 167px;
            background: linear-gradient(45deg, #008489 0%, #35B699 100%);
            border-radius: 15px 15px 0px 0px;
            padding: 25px 20px 0 25px;
            box-sizing: border-box;
        }

        .headInfo .tit1 {
            font-size: 12px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #CDE0E6;
        }

        .headInfo .tit1 span {
            color: #FAFA00;
        }

        .headInfo .tit2 {
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #FFFFFF;
            margin-top: 10px;
        }

        .headInfo .tit3 {
            font-size: 12px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #BBE2DE;
            line-height: 17px;
        }

        .orderInfo {
            background: #FFFFFF;
            padding: 25px 30px 30px;
            box-sizing: border-box;
            border-radius: 0px 0px 15px 15px;
        }

        .orderInfo .first,
        .orderInfo .second,
        .orderInfo .third {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .orderInfo .first .code {
            height: 22px;
            line-height: 22px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #A1A1A1;
        }

        .orderInfo .first .name {
            font-size: 14px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 400;
            color: #A1A1A1;
        }

        .orderInfo .second .hotelname {
            width: 270px;
            font-size: 20px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #4A4A4A;
            line-height: 28px;
        }

        .orderInfo .second .customername {
            width: 180px;
            font-size: 21px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #6C8CB4;
            line-height: 28px;
        }

        .orderInfo .third {
            margin-top: 15px;
        }

        .orderInfo .third .left .tit1 {
            height: 22px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #4A4A4A;
            line-height: 22px;
        }

        .orderInfo .third .left .tit2 {
            height: 18px;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #4A4A4A;
            line-height: 18px;
            margin-bottom: 15px;
        }

        .orderInfo .third .left .list .name {
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #A1A1A1;
        }

        .orderInfo .third .left .list .con {
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #4A4A4A;
        }

        .orderInfo .third .right {
            width: 185px;
            background: #FFFFEA;
        }

        .orderInfo .third .tit {
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 600;
            color: #4A4A4A;
            margin-left: 15px;
            margin-top: 10px;
        }

        .orderInfo .third ul {
            margin-top: 10px;
            padding: 0 15px;
            box-sizing: border-box;
        }

        .orderInfo .third ul li {
            height: 18px;
            line-height: 18px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #A1A1A1;
        }

        .orderInfo .third ul li .num {
            color: #4A4A4A;
        }

        .orderInfo .third ul li .num span {
            color: #FA6400;
        }

        .orderInfo .third .subtotal {
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #9B9B9B;
            line-height: 20px;
            text-align: right;
            margin-right: 15px;
            margin-bottom: 10px;
        }

        .orderInfo .third .subtotal span {
            font-weight: 600;
            color: #FA6400;
        }

        .orderInfo .third .total {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #0D2341;
            text-align: right;
            margin-right: 15px;
            border-top: 1px solid #DFE3E6;
            padding: 10px 0;
            box-sizing: border-box;
        }

        .orderInfo .third .total span {
            font-weight: 600;
            color: #FA6400;
        }

        .contactInfo {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 30px 15px 20px;
            box-sizing: border-box;
        }

        .contactInfo .left .tit1 {
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #0D2341;
            line-height: 20px;
        }

        .contactInfo .left .tit2 {
            height: 18px;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #1373E6;
            line-height: 18px;
            margin-top: 10px;
        }

        .contactInfo .con,
        .contactInfo .right {
            display: flex;
            align-items: center;
            font-size: 12px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #4A4A4A;
        }

        .contactInfo .con img {
            margin-right: 6px;
        }

        .contactInfo .con .txt,
        .contactInfo .right .txt {
            height: unset !important;
            border: 0 !important;
            background: transparent !important;
        }

        .footer {
            width: 606px;
            height: 140px;
            background: #FFFFFF;
            border-radius: 15px;
            margin: 0 auto;
            padding: 30px 40px 35px 30px;
            box-sizing: border-box;
        }

        .footer .tit {
            height: 22px;
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #4A4A4A;
            line-height: 22px;
        }

        .footer ul {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .footer ul li {
            font-size: 12px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #6C8CB4;
            line-height: 17px;
        }

        .footer ul li .tit1 {
            color: #8C8C91;
        }

        .title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        .title span {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8C8C91;
            line-height: 17px;
            letter-spacing: 5px;
            margin-left: 10px;
        }
    </style>
</head>

<body>
<!--<div  style="background: #f3f3f3;width: 100% ;padding: 10px;height: 800px">-->
    <div class="content" >
        <div class="headInfo">
            <div class="tit1">
                尊敬的<span>tempHotelName</span>,
            </div>
            <div class="tit2">此订单为公司支付订单</div>
            <div class="tit3">
                公司将为入住的客人支付房费，请勿向客人直接收取。客人在酒店产生的其他费用，由客人在前台支付，该费用包括且不限于额外的早餐费，迷你吧消费，餐费洗衣费等。房费发票由酒店每月给武汉市天下房仓科技有限公司开具相应增值税专用发票，客人有权要求前台开具公司月结之外其它费用发票。订单实际支付金额将根据客人的实际离店日期进行结算。
            </div>
        </div>
        <div class="orderInfo">
            <div class="first">
                <div class="code">tempOrderCode</div>
                <div class="name">下单客户</div>
            </div>
            <div class="second">
                <div class="hotelname">tempHotelName</div>
                <div class="customername">tempCustomerName</div>
            </div>
            <div class="third">
                <div class="left">
                    <div class="tit1">tempRoomName</div>
                    <div class="tit2">tempPriceName</div>
                    <div class="list">
                        <span class="name">入离日期:</span>
                        <span class="con">tempDateTime</span>
                    </div>
                    <div class="list">
                        <span class="name">客户姓名:</span>
                        <span class="con">tempGuestName</span>
                    </div>
                    <div class="list">
                        <span class="name">到店时间:</span>
                        <span class="con">tempArriveTime</span>
                    </div>
                    <div class="list">
                        <span class="name">确认号:</span>
                        <span class="con">tempConfirmNo</span>
                    </div>
                    <div class="list">
                        <span class="name">联系人:</span>
                        <span class="con">tempContactInfo</span>
                    </div>
                </div>
                <div class="right">
                    <div class="tit">房费</div>
                    tempDailyInfo
                    <div class="total">
                        合计<span>tempTotalAmount</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="contactInfo">
            <div class="left">
                <div class="tit1">web网页端收款登录地址</div>
                <a href="tempFangCangUrl" class="tit2" style="font-size: 11px">tempsameFangCangUrl</a>
            </div>
            <div class="con">
                <img src="https://titantmc.fangcang.com/erweima1.png" alt="">
                <div class="txt">
                    <div>跟单/收款</div>
                    <div>关房/调价</div>
                    <div style="color: #859AB4;">小程序全搞定</div>
                </div>
            </div>
            <div class="right">
                <img src="https://titantmc.fangcang.com/erweima2.png" alt="">
                <div class="txt">
                    <div style="color: #859AB4;">扫码关注</div>
                    <div class="tit2">来单提醒</div>
                    <div style="color: #859AB4;">公众号</div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer">
        <div class="tit">红色加力酒店供应链服务商</div>
        <ul>
            <li>
                <div class="tit1">7*24小时客户服务热线</div>
                <div class="tit2">027-59308500</div>
            </li>
            <li>
                <div class="tit1">客服邮箱地址</div>
                <div class="tit2"><EMAIL></div>
            </li>
            <li>
                <div class="tit1">对账结算联系电话</div>
                <div class="tit2">027-86510366</div>
            </li>
        </ul>
    </div>
    <div class="title">
        <img src="https://titantmc.fangcang.com/ireveimg.png" alt="">
        <span>让酒店分销更简单</span>
    </div>
<!--</div>-->
</body>

</html>