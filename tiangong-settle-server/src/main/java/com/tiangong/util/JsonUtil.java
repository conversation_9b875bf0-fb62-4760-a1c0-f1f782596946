package com.tiangong.util;

import com.google.gson.Gson;

import java.util.Map;

/**
 * JSON相关处理
 * <AUTHOR>
 * @ClassName JsonUtil
 * @description: TODO
 * @datetime 2022年09月02日 11:26
 * @version: 1.0
 */
public class JsonUtil {
    /**
     * 单字符串转json字符串
     * @param key1
     * @param value1
     * @param key2
     * @param value2
     * @return
     */
    public static String stringConvertJson(String key1, String value1,String key2, String value2){
        String res = "{\""+ key1 + "\": \""+ value1 +"\",\""+ key2 + "\": \""+ value2 +"\"}";
        return res;
    }

    /**
     * 参数化转JSON数据
     * @param keyValuePairs
     * @return
     */
    public static String stringConvertJson(Map<String, String> keyValuePairs) {
        Gson gson = new Gson();
        // 将接收到的键值对 Map 对象转换为 JSON 字符串
        String json = gson.toJson(keyValuePairs);
        return json;
    }
}
