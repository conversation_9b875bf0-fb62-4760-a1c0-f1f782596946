package com.tiangong.util;


import com.tiangong.settle.dto.DdtSettleOrderDto;
import com.tiangong.settle.settleOrder.domain.entity.SettleOrderEntity;
import java.text.SimpleDateFormat;

/**
 * 对象转换工具类
 * <AUTHOR>
 */
public class ObjectConvertUtil {
    public static DdtSettleOrderDto oldSettleConvertNewSettle(SettleOrderEntity settleOrder){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DdtSettleOrderDto newSettleOrderDto = new DdtSettleOrderDto();
        newSettleOrderDto.setIreveAgentCode(settleOrder.getDdtCustomerCode());
        newSettleOrderDto.setIreveMerchantCode(settleOrder.getDdtMerchantCode());
        newSettleOrderDto.setIreveOrderCode(settleOrder.getDdtOrderCode());
        newSettleOrderDto.setIreveOrderCreatedDt(format.format(settleOrder.getDdtOrderDt()));
        newSettleOrderDto.setPayAccountId(settleOrder.getPayerAccountId());
        newSettleOrderDto.setSaasMerchantCode(settleOrder.getMerchantCode());
        newSettleOrderDto.setSaasOrderCode(settleOrder.getOrderCode());
        newSettleOrderDto.setSaasOrderCreatedDt(format.format(settleOrder.getOrderDt()));
        newSettleOrderDto.setSaasSupplierCode(settleOrder.getSupplyOrderCode());
        newSettleOrderDto.setSaasSupplierCodeCreatedDt(format.format(settleOrder.getTgSupplyOrderDt()));
        newSettleOrderDto.setSaasSupplyCode(settleOrder.getSupplierCode());
        newSettleOrderDto.setSettleDate(format.format(settleOrder.getSettleDt()));
        newSettleOrderDto.setSettleType(settleOrder.getSettleType());
        newSettleOrderDto.setCreatedBy(settleOrder.getCreatedBy());
        return newSettleOrderDto;
    }
}
