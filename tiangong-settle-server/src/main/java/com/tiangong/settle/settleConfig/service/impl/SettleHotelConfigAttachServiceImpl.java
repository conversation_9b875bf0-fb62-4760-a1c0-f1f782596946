package com.tiangong.settle.settleConfig.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.exception.SysException;
import com.tiangong.settle.req.SettleHotelConfigAttachReq;
import com.tiangong.settle.resp.SettleHotelConfigAttachResp;
import com.tiangong.settle.settleConfig.domain.entity.SettleHotelConfigAttachEntity;
import com.tiangong.settle.settleConfig.mapper.SettleHotelConfigAttachMapper;
import com.tiangong.settle.settleConfig.service.SettleHotelConfigAttachService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自助结算酒店配置附加项表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
@Service
public class SettleHotelConfigAttachServiceImpl extends ServiceImpl<SettleHotelConfigAttachMapper, SettleHotelConfigAttachEntity> implements SettleHotelConfigAttachService {

    @Autowired
    private SettleHotelConfigAttachMapper settleHotelConfigAttachMapper;

    @Autowired
    HttpServletRequest request;

    /**
     * 自助结算酒店配置附加项表新增
     */
    @Override
    @Transactional
    public void settleHotelConfigAttachAdd(SettleHotelConfigAttachReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        SettleHotelConfigAttachEntity entity = new SettleHotelConfigAttachEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreatedDt(new Date());
        entity.setCreatedBy(loginUser.getUserName());
        // ===========开始业务==============

        // ===========结束业务==============
        int insert = settleHotelConfigAttachMapper.insert(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
        }
    }

    /**
     * 自助结算酒店配置附加项表删除
     */
    @Override
    @Transactional
    public void settleHotelConfigAttachDel(SettleHotelConfigAttachReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        // ===========开始业务==============

        // ===========结束业务==============
        int insert = settleHotelConfigAttachMapper.deleteById(req.getId());
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1002.getCode(), ResultEnum.E_1002.getMessage());
        }
    }

    /**
     * 自助结算酒店配置附加项表编辑
     */
    @Override
    @Transactional
    public void settleHotelConfigAttachEdit(SettleHotelConfigAttachReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        SettleHotelConfigAttachEntity entity = new SettleHotelConfigAttachEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy(loginUser.getUserName());
        // ===========开始业务==============

        // ===========结束业务==============
        int insert = settleHotelConfigAttachMapper.updateById(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1003.getCode(), ResultEnum.E_1003.getMessage());
        }
    }

    /**
     * 自助结算酒店配置附加项表详情
     */
    @Override
    @AnonymousAccess
    public SettleHotelConfigAttachResp settleHotelConfigAttachDetail(SettleHotelConfigAttachReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        SettleHotelConfigAttachEntity entity = settleHotelConfigAttachMapper.selectById(req.getId());
        SettleHotelConfigAttachResp resp = new SettleHotelConfigAttachResp();
        BeanUtils.copyProperties(entity, resp);
        // ===========开始业务==============

        // ===========结束业务==============
        return resp;
    }

    /**
     * 自助结算酒店配置附加项表列表（全部）
     */
    @Override
    public List<SettleHotelConfigAttachResp> settleHotelConfigAttachList(SettleHotelConfigAttachReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        List<SettleHotelConfigAttachEntity> list = settleHotelConfigAttachMapper.selectList(null);
        List<SettleHotelConfigAttachResp> collect = list.stream().map((item) -> {
            SettleHotelConfigAttachResp resp = new SettleHotelConfigAttachResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());

        return collect;
    }

    /**
     * 自助结算酒店配置附加项表列表（分页）
     */
    @Override
    public PageVo settleHotelConfigAttachPage(SettleHotelConfigAttachReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        Page<SettleHotelConfigAttachEntity> teacherPage = new Page<SettleHotelConfigAttachEntity>(req.getPage(), req.getPageSize());
        QueryWrapper<SettleHotelConfigAttachEntity> wrapper = new QueryWrapper<>();
        // ===========开始业务==============

        // ===========结束业务==============
        IPage<SettleHotelConfigAttachEntity> ipage = settleHotelConfigAttachMapper.selectPage(teacherPage, wrapper);

        List<SettleHotelConfigAttachResp> collect = ipage.getRecords().stream().map((item) -> {
            SettleHotelConfigAttachResp resp = new SettleHotelConfigAttachResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }


}