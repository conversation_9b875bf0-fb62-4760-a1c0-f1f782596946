package com.tiangong.settle.settleConfig.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.settle.req.SettleHotelConfigAttachReq;
import com.tiangong.settle.resp.SettleHotelConfigAttachResp;
import com.tiangong.settle.settleConfig.domain.entity.SettleHotelConfigAttachEntity;

import java.util.List;

/**
 * 自助结算酒店配置附加项表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
public interface SettleHotelConfigAttachService extends IService<SettleHotelConfigAttachEntity> {

    /**
     * 自助结算酒店配置附加项表新增
     */
    void settleHotelConfigAttachAdd(SettleHotelConfigAttachReq req);

    /**
     * 自助结算酒店配置附加项表删除
     */
    void settleHotelConfigAttachDel(SettleHotelConfigAttachReq req);

    /**
     * 自助结算酒店配置附加项表编辑
     */
    void settleHotelConfigAttachEdit(SettleHotelConfigAttachReq req);

    /**
     * 自助结算酒店配置附加项表详情
     */
    SettleHotelConfigAttachResp settleHotelConfigAttachDetail(SettleHotelConfigAttachReq req);

    /**
     * 自助结算酒店配置附加项表列表（全部）
     */
    List<SettleHotelConfigAttachResp> settleHotelConfigAttachList(SettleHotelConfigAttachReq req);

    /**
     * 自助结算酒店配置附加项表列表（分页）
     */
    PageVo settleHotelConfigAttachPage(SettleHotelConfigAttachReq req);

}

