package com.tiangong.settle.settleConfig.service;

import com.tiangong.common.Response;
import com.tiangong.settle.collectPayment.domain.resp.ddt.DdtOrderInfoDetailResp;
import com.tiangong.settle.dto.AliPayBankTransfer;
import com.tiangong.settle.dto.DdtOrderDto;
import com.tiangong.settle.dto.DdtCollectionAccount;
import com.tiangong.settle.collectPayment.domain.resp.DdtBillInfoResp;
import com.tiangong.settle.collectPayment.domain.req.DdtCollectPaymentReq;
import com.tiangong.settle.req.SettleHotelConfigReq;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单通接口服务类
 * <AUTHOR>
 */
public interface DdtService {
    /**
     * 验证酒店客户信息
     * @param req 自助结算配置参数
     * @return 返回结果
     */
    String checkAgentBeHotelUrl(SettleHotelConfigReq req);

    /**
     * 标记自助结算
     * @param req       自助结算配置参数
     * @param isSettle  IsSettleEnum枚举值 是否为自助结算
     * @return 返回结果
     */
    Integer signSettleAgentUrl(SettleHotelConfigReq req,String isSettle);


    /**
     * 查询订单通客户信息
     * @param ddtCustomerCodeList
     * @return
     */
    Map<String, String> queryDdtCustomerCodeList(List<String> ddtCustomerCodeList);

    /**
     * 查询订单通收款账号
     * @param ddtMerchantCode 订单通商家编码
     * @return 返回账号信息
     */
    DdtCollectionAccount queryCollectionAccount(String ddtMerchantCode);

    /**
     * 通过订单编码查询订单通订单明细
     * @param orderCode
     * @return
     */
    DdtCollectPaymentReq queryDdtOrderInfo(String orderCode);


    /**
     * 通过账单编码查询订单通账单明细
     */
    Response<DdtBillInfoResp> querySupplyOrderCodeInfo(String billCode);

    /**
     * 创建订单通订单
     * @param orderDto
     * @return
     */
    String createDdtOrder(DdtOrderDto orderDto);


    /**
     * 通过订单编码查询订单通订单明细
     * @param orderCode
     * @return
     */
    DdtOrderInfoDetailResp queryDdtOrderInfoDetail(String orderCode);



    /**
     * [银企平台]查询付款余额
     * @param payAccount 付款账号
     * @return 返回余额
     */
    Map<String,Object> queryAvailableBalance(String payAccount);

    /**
     * [银企平台]付款
     * @param aliPayBankTransfer
     */
    Map<String,Object> singlePay(AliPayBankTransfer aliPayBankTransfer);


    /**
     * 查询支付状态
     */
    Map<String,Object>  querySinglePayResultUrl(String businessCode);


    /**
     *  [银企平台]查询凭证
     * @param bizNo 支付业务流水号
     * @return 返回凭证地址
     */
    Map<String, Object> queryPaymentVoucher(String bizNo);

}
