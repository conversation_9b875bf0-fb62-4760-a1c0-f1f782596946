package com.tiangong.settle.settleConfig.convert;

import com.tiangong.settle.req.ImportSettleHotelConfigReq;
import com.tiangong.settle.req.SettleHotelConfigReq;
import com.tiangong.settle.resp.ImportSettleHotelConfigFailResp;
import com.tiangong.settle.resp.SettleHotelConfigResp;
import com.tiangong.settle.settleConfig.domain.entity.SettleHotelConfigEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SettleConvert {

    SettleConvert INSTANCE = Mappers.getMapper(SettleConvert.class);

    ImportSettleHotelConfigFailResp ImportSettleHotelConfigFailRespConvert(ImportSettleHotelConfigReq importReq);

    SettleHotelConfigEntity settleHotelConfigEntity(SettleHotelConfigReq req);

    SettleHotelConfigEntity SettleHotelConfigEntityConvert(SettleHotelConfigReq req);

    SettleHotelConfigResp respConvert(SettleHotelConfigEntity entity);

    SettleHotelConfigResp settleHotelConfigRespConvert(SettleHotelConfigEntity item);
}
