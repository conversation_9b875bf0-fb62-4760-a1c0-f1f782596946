package com.tiangong.settle.settleConfig.server;

import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.Response;
import com.tiangong.settle.req.SettleHotelConfigAttachReq;
import com.tiangong.settle.resp.SettleHotelConfigAttachResp;
import com.tiangong.settle.settleConfig.service.SettleHotelConfigAttachService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 自助结算酒店配置附加项表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
@RestController
@RequestMapping("/settleConfig")
@Slf4j
public class SettleHotelConfigAttachController {

    @Autowired
    private SettleHotelConfigAttachService settleHotelConfigAttachService;

    /**
     * 自助结算酒店配置附加项表新增
     */
    @PostMapping("/settleHotelConfigAttachAdd")
    @PreAuthorize("@syyo.check('order:settleHotelConfigAttach')")
    public Response<Object> add(@RequestBody SettleHotelConfigAttachReq req) {
        settleHotelConfigAttachService.settleHotelConfigAttachAdd(req);
        return Response.success();
    }

    /**
     * 自助结算酒店配置附加项表删除
     */
    @PostMapping("/settleHotelConfigAttachDel")
    @PreAuthorize("@syyo.check('order:settleHotelConfigAttach')")
    public Response<Object> del(@RequestBody SettleHotelConfigAttachReq req) {
        settleHotelConfigAttachService.settleHotelConfigAttachDel(req);
        return Response.success();
    }

    /**
     * 自助结算酒店配置附加项表编辑
     */
    @PostMapping("/settleHotelConfigAttachEdit")
    @PreAuthorize("@syyo.check('order:settleHotelConfigAttach')")
    public Response<Object> edit(@RequestBody SettleHotelConfigAttachReq req) {
        settleHotelConfigAttachService.settleHotelConfigAttachEdit(req);
        return Response.success();
    }

    /**
     * 自助结算酒店配置附加项表详情
     */
    @PostMapping("/settleHotelConfigAttachDetail")
    public Response<SettleHotelConfigAttachResp> findOne(@RequestBody SettleHotelConfigAttachReq req) {
        return Response.success(settleHotelConfigAttachService.settleHotelConfigAttachDetail(req));
    }

    /**
     * 自助结算酒店配置附加项表列表（全部）
     */
    @PostMapping("/settleHotelConfigAttachList")
    public Response<List<SettleHotelConfigAttachResp>> findAll(@RequestBody SettleHotelConfigAttachReq req) {
        return Response.success(settleHotelConfigAttachService.settleHotelConfigAttachList(req));
    }

    /**
     * 自助结算酒店配置附加项表列表（分页）
     */
    @PostMapping("/settleHotelConfigAttachPage")
    public Response<PageVo> findPage(@RequestBody SettleHotelConfigAttachReq req) {
        return Response.success(settleHotelConfigAttachService.settleHotelConfigAttachPage(req));
    }
}
