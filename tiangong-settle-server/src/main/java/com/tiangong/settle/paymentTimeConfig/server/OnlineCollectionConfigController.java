package com.tiangong.settle.paymentTimeConfig.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.common.Response;
import com.tiangong.settle.paymentTimeConfig.service.OnlineCollectionConfigService;
import com.tiangong.settle.req.OnlineCollectionConfigReq;
import com.tiangong.settle.resp.OnlineCollectionConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 延时付款配置表
 *
 * <AUTHOR>
 * @date 2023-12-19 15:38:33
 */
@RestController
@RequestMapping("/settle/onlineCollectionConfig")
@Slf4j
public class OnlineCollectionConfigController {

    @Autowired
    private OnlineCollectionConfigService onlineCollectionConfigService;

    /**
     * 延时付款配置表编辑
     */
    @PostMapping("/onlineCollectionConfigEdit")
    @PreAuthorize("@syyo.check('settle:onlineCollectionConfig')")
    public Response<Integer> edit(@RequestBody List<OnlineCollectionConfigReq> req) {
        if (null != req && !req.isEmpty() && null != req.get(0).getConfigId()) {
            return Response.success(onlineCollectionConfigService.onlineCollectionConfigEdit(req));
        } else {
            return Response.success(onlineCollectionConfigService.onlineCollectionConfigAdd(req));
        }
    }


    /**
     * 延时付款配置表列表（全部）
     */
    @PostMapping("/onlineCollectionConfigList")
    public Response<List<OnlineCollectionConfigResp>> findAll(@RequestBody OnlineCollectionConfigReq req) {
        return Response.success(onlineCollectionConfigService.onlineCollectionConfigList(req));
    }
}
