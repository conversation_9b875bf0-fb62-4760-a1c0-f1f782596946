package com.tiangong.settle.sysDdtTask.domain.resp;

import lombok.Data;

                                                                                                                                                                                                                                                            import java.util.Date;
                                                                                        import java.util.Date;
                    
import java.io.Serializable;

/**
 * 同步任务表
 * 返回参数
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:59
 */
@Data
public class SysDdtTaskResp implements Serializable {

    private static final long serialVersionUID = 1L;

            /**
         * id
         */
        private Long id;
            /**
         * 业务订单号 （订单编码/账单编码）
         */
        private String businessCode;
            /**
         * 任务类型 1、创建结算单, 2、取消订单 3、工单处理  4、支付失败同步
         */
        private Integer taskType;
            /**
         * 完成类型 0 未开始,1 已完成, 2 失败
         */
        private Integer resultType;
            /**
         * 请求参数
         */
        private String request;
            /**
         * 创建人
         */
        private String createdBy;
            /**
         * 创建时间
         */
        private Date createdDt;
            /**
         * 更新人
         */
        private String updatedBy;
            /**
         * 更新时间
         */
        private Date updatedDt;
    }