package com.tiangong.settle.sysDdtTask.server;

import com.tiangong.common.Response;
import com.tiangong.settle.sysDdtTask.service.SysDdtTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算单任务表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
@RestController
@RequestMapping("/settle/sysDdtTask")
public class SysDdtTaskController {

    @Autowired
    private SysDdtTaskService sysDdtTaskService;


    /**
     * 同步订单通处理
     */
    @PostMapping("/sysDdtHandle")
    @PreAuthorize("@syyo.check('settle:sysDdtHandle')")
    public Response<Object> sysDdtHandle() {
        sysDdtTaskService.sysDdtHandle();
        return Response.success();
    }

}
