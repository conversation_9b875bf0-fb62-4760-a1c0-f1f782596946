package com.tiangong.settle.task;


import com.tiangong.settle.settleOrder.service.SettleOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 查询需要发送的邮件
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QueryOrderBySendStatusTask {

    @Autowired
    private SettleOrderService settleOrderService;

    @XxlJob("queryOrderBySendStatusTask")
    public void execute() {
        try{
            XxlJobHelper.log("查询需要发送的邮件---开始");
            settleOrderService.querySettleOrderBySendStatus();
            XxlJobHelper.log("查询需要发送的邮件---结束");
        }catch (Exception e){
            XxlJobHelper.log("查询需要发送的邮件---异常");
            log.error("查询需要发送的邮件异常",e);
        }
    }
}
