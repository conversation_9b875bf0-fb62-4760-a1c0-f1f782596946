package com.tiangong.settle.task;


import com.tiangong.settle.settleTask.service.SettleTaskService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 结算单任务表生成
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SettleTaskFormRedisTask{

    @Autowired
    private SettleTaskService settleTaskService;

    @XxlJob(value = "settleTaskFormRedisTask")
    public void execute() {
        try{
            XxlJobHelper.log("读取【供货单确认/取消任务】队列到结算单任务表--开始");
            settleTaskService.readSettleTaskFormRedis();
            XxlJobHelper.log("读取【供货单确认/取消任务】队列到结算单任务表--结束");
        }catch (Exception e){
            XxlJobHelper.log("读取【供货单确认/取消任务】队列到结算单任务表---异常");
            log.error("读取【供货单确认/取消任务】队列到结算单任务表",e);
        }
    }
}
