package com.tiangong.settle.collectPayment.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.settle.collectPayment.domain.entity.SettleServiceJobEntity;
import com.tiangong.settle.collectPayment.domain.req.SettleServiceJobReq;
import com.tiangong.settle.collectPayment.domain.resp.SettleServiceJobResp;

/**
 * 付款任务表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:59
 */
public interface SettleServiceJobMapper extends BaseMapper<SettleServiceJobEntity> {

    /**
     * 付款任务表列表（分页）
     */
    IPage<SettleServiceJobResp> selectSettleServiceJobPage(IPage<?> page, SettleServiceJobReq req);
}
