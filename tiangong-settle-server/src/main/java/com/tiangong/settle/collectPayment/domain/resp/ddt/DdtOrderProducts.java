package com.tiangong.settle.collectPayment.domain.resp.ddt;


import lombok.Data;

import java.util.List;

@Data
public class DdtOrderProducts {

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 房间数量
     */
    private String roomQty;

    /**
     * 产品每日明细
     */
    private List<DdtProductPrices> productPrices;
}
