package com.tiangong.settle.collectPayment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.SettleConstantKey;
import com.tiangong.settle.collectPayment.domain.entity.SettleServiceJobEntity;
import com.tiangong.settle.collectPayment.domain.req.SettleServiceJobReq;
import com.tiangong.settle.collectPayment.domain.resp.SettleServiceJobResp;
import com.tiangong.settle.collectPayment.mapper.SettleServiceJobMapper;
import com.tiangong.settle.collectPayment.service.SettleServiceJobService;
import com.tiangong.settle.enums.ProgressStatusEnum;
import com.tiangong.settle.enums.SettleLogTypeEnum;
import com.tiangong.settle.enums.WorkOrderEnum;
import com.tiangong.settle.req.SettleServiceJobVo;
import com.tiangong.settle.req.TgWorkOrderReq;
import com.tiangong.settle.resp.SettleServiceJobDto;
import com.tiangong.settle.settleOrder.mapper.SettleOrderMapper;
import com.tiangong.settle.settleServiceJob.domain.entity.SettleServiceJobLogEntity;
import com.tiangong.settle.settleServiceJob.domain.req.SettleServiceJobLogReq;
import com.tiangong.settle.settleServiceJob.domain.resp.SettleServiceJobLogResp;
import com.tiangong.settle.settleServiceJob.mapper.SettleServiceJobLogMapper;
import com.tiangong.util.StrUtilX;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 付款任务表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:59
 */
@Service
public class SettleServiceJobServiceImpl extends ServiceImpl<SettleServiceJobMapper, SettleServiceJobEntity> implements SettleServiceJobService {

    @Autowired
    private SettleServiceJobMapper settleServiceJobMapper;

    @Autowired
    private SettleServiceJobLogMapper settleServiceJobLogMapper;

    @Autowired
    private SettleOrderMapper settleOrderMapper;

    @Autowired
    HttpServletRequest request;


    /**
     * 付款任务表列表（分页）
     */
    @Override
    public PageVo settleServiceJobPage(SettleServiceJobReq req) {
        Page<SettleServiceJobEntity> teacherPage = new Page<>(req.getCurrentPage(), req.getPageSize());

        // 查询付款任务表列表（分页）
        IPage<SettleServiceJobResp> iPage = settleServiceJobMapper.selectSettleServiceJobPage(teacherPage, req);

        List<SettleServiceJobResp> collect = iPage.getRecords().stream().peek(item -> {
            // 酒店名称
            if (null != req.getHotelName()) {
                item.setHotelName(req.getHotelName());
            }
            for (SettleServiceJobLogResp jobLog : item.getSettleServiceJobLogList()) {
                //存在付款日志，并且是付款中，付款失败情况
                if (jobLog.getLogType() == 1 && item.getTransferStatus() == 2) {
                    item.setTransferReason(jobLog.getContent());
                }
                //存在订单通工单日志，并且是处理中情况
                if (jobLog.getLogType() == 3 && (item.getDdtStatus() == 1)) {
                    item.setDdtStatusReason(jobLog.getContent());
                }
                //存在天宫工单日志，并且是处理中情况
                if (jobLog.getLogType() == 2 && (item.getTgStatus() == 1)) {
                    item.setTgStatusReason(jobLog.getContent());
                }
            }
        }).collect(Collectors.toList());

        return PageVo.result(iPage, collect);
    }


    @Override
    public SettleServiceJobResp settleServiceJobDetail(SettleServiceJobReq req) {
        //查询任务详情
        SettleServiceJobEntity entity = settleServiceJobMapper.selectById(req.getJobId());
        SettleServiceJobResp resp = new SettleServiceJobResp();
        BeanUtils.copyProperties(entity, resp);

        //查询任务日志详情
        QueryWrapper<SettleServiceJobLogEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("job_id", req.getJobId());
        queryWrapper.inSql("created_dt", "select max(created_dt) from f_settle_service_job_log where job_id = " + req.getJobId() + "  group by log_type");


        List<SettleServiceJobLogEntity> jobLogList = settleServiceJobLogMapper.selectList(queryWrapper);

        for (SettleServiceJobLogEntity jobLog : jobLogList) {
            //存在付款日志，并且是付款中，付款失败情况
            if (jobLog.getLogType() == 1 && resp.getTransferStatus() == 2) {
                resp.setTransferReason(jobLog.getContent());
            }
            //存在订单通工单日志，并且是处理中情况
            if (jobLog.getLogType() == 3 && (resp.getDdtStatus() == 1)) {
                resp.setDdtStatusReason(jobLog.getContent());
            }
            //存在天宫工单日志，并且是处理中情况
            if (jobLog.getLogType() == 2 && (resp.getTgStatus() == 1)) {
                resp.setTgStatusReason(jobLog.getContent());
            }
        }
        return resp;
    }

    @Override
    public PageVo settleServiceJobLogDetail(SettleServiceJobLogReq req) {
        QueryWrapper<SettleServiceJobLogEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("job_id", req.getJobId())
                .orderByDesc("created_dt");
        ;

        Page<SettleServiceJobLogEntity> teacherPage = new Page<SettleServiceJobLogEntity>(req.getCurrentPage(), req.getPageSize());

        IPage<SettleServiceJobLogEntity> ipage = settleServiceJobLogMapper.selectPage(teacherPage, queryWrapper);

        List<SettleServiceJobLogResp> collect = ipage.getRecords().stream().map((item) -> {
            SettleServiceJobLogResp resp = new SettleServiceJobLogResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());
        return PageVo.result(ipage, collect);
    }


    /**
     * 根据指定条件查询付款任务详情
     */
    @Override
    public SettleServiceJobResp querySettleServiceJobInfo(SettleServiceJobReq req) {
        QueryWrapper<SettleServiceJobEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_code", req.getBusinessCode()); //业务单号【账单编码、订单编码】
        queryWrapper.eq("transfer_status", 1); //支付成功
        SettleServiceJobEntity entity = settleServiceJobMapper.selectOne(queryWrapper);
        SettleServiceJobResp resp = new SettleServiceJobResp();
        BeanUtils.copyProperties(entity, resp);
        return resp;
    }

    /**
     * 修改自助结算任务信息
     *
     * @param req           请求参数
     * @param settleLogType 自助结算处理日志类型
     * @param flag          处理结果 true、false
     * @param msg           处理失败日志
     * @return 返回成功或者失败
     */
    @Override
    public Integer updateSettleServiceJob(SettleServiceJobReq req, Integer settleLogType, boolean flag, String msg) {
        try {

            QueryWrapper<SettleServiceJobEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("task_code", req.getTaskCode());
            SettleServiceJobEntity settleServiceJob = settleServiceJobMapper.selectOne(queryWrapper);

            //工单确认处理成功
            //则去修改付款任务工单状态
            //否则只更新工单日志
            if (flag) {
                //判断订单通
                if (settleLogType.equals(SettleLogTypeEnum.DDT_LOG_TYPE.key)) {
                    settleServiceJob.setDdtStatus(req.getDdtStatus());
                }
                //判断天宫
                if (settleLogType.equals(SettleLogTypeEnum.TG_LOG_TYPE.key)) {
                    settleServiceJob.setTgStatus(req.getTgStatus());
                    settleServiceJob.setTgBillCode(req.getTgBillCode());
                }

                //天宫工单 和 订单通工单 都变成已完成
                if (settleServiceJob.getDdtStatus() == WorkOrderEnum.WORK_ORDER_2.key &&
                        settleServiceJob.getTgStatus() == WorkOrderEnum.WORK_ORDER_2.key) {
                    //标记为整体任务完成
                    settleServiceJob.setProgressStatus(ProgressStatusEnum.FINISHED.key);
                }
                settleServiceJob.setUpdatedDt(new Date());
                /*
                 * 更新任务
                 */
                settleServiceJobMapper.updateById(settleServiceJob);
            }

            /*
             * 更新工单处理日志
             * 【天宫工单状态】 + 【订单通工单状态】
             */
            SettleServiceJobLogEntity jobLogReq = new SettleServiceJobLogEntity();
            //日志类型 ['1: 网商银行支付','2: 天宫工单处理','3:订单通工单处理]
            jobLogReq.setLogType(settleLogType);

            //判断是否为订单通日志类型，并且是订单通工单处理成功情况
            if (settleLogType.equals(SettleLogTypeEnum.DDT_LOG_TYPE.key) && flag) {
                jobLogReq.setContent("订单通工单处理成功");
            } else if (settleLogType.equals(SettleLogTypeEnum.TG_LOG_TYPE.key) && flag) {
                jobLogReq.setContent("天宫工单处理成功");
            } else {
                jobLogReq.setContent(msg);
            }
            jobLogReq.setJobId(settleServiceJob.getJobId());
            jobLogReq.setCreatedBy(SettleConstantKey.SETTLE_OPERATOR);
            jobLogReq.setCreatedDt(new Date());
            jobLogReq.setUpdatedBy(SettleConstantKey.SETTLE_OPERATOR);
            jobLogReq.setUpdatedDt(new Date());
            return settleServiceJobLogMapper.insert(jobLogReq);
        } catch (Exception e) {
            log.error("更新异常", e);
        }
        return 0;
    }

    @Override
    public Integer processTgJobOrderStatus(TgWorkOrderReq req) {
        // 更新自助结算收款任务（订单通工单状态）
        SettleServiceJobReq settleServiceJobReq = new SettleServiceJobReq();
        settleServiceJobReq.setTaskCode(req.getTaskCode());  //任务编码
        settleServiceJobReq.setTgStatus(WorkOrderEnum.WORK_ORDER_2.key);   //天宫工单已完成
        settleServiceJobReq.setTgBillCode(req.getBillCode());

        return updateSettleServiceJob(settleServiceJobReq, SettleLogTypeEnum.TG_LOG_TYPE.key, req.isFlag(), req.getMsg());
    }

    @Override
    public SettleServiceJobDto queryNotFailSettleServiceJob(SettleServiceJobVo req) {
        if (StrUtilX.isEmpty(req.getSupplyOrderCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERCODE);
        }
        SettleServiceJobDto settleServiceJobDto = settleOrderMapper.queryNotFailSettleServiceJob(req);
        return settleServiceJobDto;
    }
}