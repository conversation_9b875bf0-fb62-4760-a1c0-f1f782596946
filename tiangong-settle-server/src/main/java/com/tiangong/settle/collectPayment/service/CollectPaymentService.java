package com.tiangong.settle.collectPayment.service;


import com.tiangong.settle.collectPayment.domain.req.DdtCollectPaymentReq;
import java.util.Map;

/**
 * 自助结算收款服务
 * <AUTHOR>
 */
public interface CollectPaymentService {

    /**
     * 【订单通接口】订单在线收款接口
     * @param ddtCollectPaymentRequest 入参参数
     * @return map
     */
    public Map<String, Object> orderOnlineCollection(DdtCollectPaymentReq ddtCollectPaymentRequest);


    /**
     * 【定时任务接口】查询可付款数据加入到订单付款任务队列
     */
    public void orderPayment();



    /**
     * 【定时任务接口】从订单付款任务队列中取出订单,进行付款处理
     */
    public Map<String,Object> orderHandlePayment();



    /**
     * 【订单通】账单在线收款接口
     * @param billCode 账单编码
     * @return map
     */
    public Map<String, Object> billOnlineCollection(String billCode);


    /**
     * 查询支付凭证
     * @param businessCode
     * @return
     */
    public Map<String,Object> queryPaymentVoucher(String businessCode);




    /**
     * 【订单通】加入到账单付款任务
     */
    public void billPayment();


    /**
     * 【订单通】账单付款处理
     */
    public Map<String, Object> billHandlePayment();


    /**
     * 【定时任务】查询需要处理支付状态的任务
     */
    public void queryPaymentStatus();


    /**
     * 【订单通】处理支付状态
     */
    public void handlePaymentStatus();

}
