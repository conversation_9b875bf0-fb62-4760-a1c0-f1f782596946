package com.tiangong.settle.settleTask.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dto.order.CancelRestriction;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.keys.SettleRedisKey;
import com.tiangong.order.remote.OrderQueryRemote;
import com.tiangong.order.remote.SupplyOrderFinanceRemote;
import com.tiangong.order.remote.SupplyProductPriceRemote;
import com.tiangong.order.remote.request.SupplyOrderInfoListDTO;
import com.tiangong.order.remote.response.GuestDTO;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.SupplyOrderFinanceDTO;
import com.tiangong.order.remote.response.SupplyProductDailyPriceDTO;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.organization.remote.dto.AgentAddDTO;
import com.tiangong.organization.remote.dto.AgentSelectDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.settle.dto.DdtOrderDto;
import com.tiangong.settle.dto.DdtProductDto;
import com.tiangong.settle.dto.DdtProductPriceDto;
import com.tiangong.settle.dto.DdtSettleOrderDto;
import com.tiangong.settle.enums.DdtTaskTypeEnum;
import com.tiangong.settle.enums.ResultTypeEnum;
import com.tiangong.settle.enums.SettleTaskTypeEnum;
import com.tiangong.settle.enums.SupplyResultEnum;
import com.tiangong.settle.req.SettleSupplyResultReq;
import com.tiangong.settle.settleConfig.domain.entity.SettleHotelConfigAttachEntity;
import com.tiangong.settle.settleConfig.domain.entity.SettleHotelConfigEntity;
import com.tiangong.settle.settleConfig.mapper.SettleHotelConfigAttachMapper;
import com.tiangong.settle.settleConfig.mapper.SettleHotelConfigMapper;
import com.tiangong.settle.settleConfig.service.DdtService;
import com.tiangong.settle.settleOrder.domain.entity.SettleOrderEntity;
import com.tiangong.settle.settleOrder.mapper.SettleOrderMapper;
import com.tiangong.settle.settleTask.domain.entity.SettleTaskEntity;
import com.tiangong.settle.settleTask.domain.req.SettleTaskReq;
import com.tiangong.settle.settleTask.domain.resp.SettleTaskResp;
import com.tiangong.settle.settleTask.mapper.SettleTaskMapper;
import com.tiangong.settle.settleTask.service.SettleTaskService;
import com.tiangong.settle.sysDdtTask.domain.entity.SysDdtTaskEntity;
import com.tiangong.settle.sysDdtTask.mapper.SysDdtTaskMapper;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算任务实现类
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class SettleTaskServiceImpl extends ServiceImpl<SettleTaskMapper, SettleTaskEntity> implements SettleTaskService {


    @Autowired
    private SupplyProductPriceRemote supplyProductPriceRemote;


    @Autowired
    private SupplyOrderFinanceRemote supplyOrderFinanceRemote;

    @Autowired
    private SettleHotelConfigMapper settleHotelConfigMapper;

    @Autowired
    private SettleHotelConfigAttachMapper settleHotelConfigAttachMapper;

    @Autowired
    private SettleTaskMapper settleTaskMapper;

    @Autowired
    private SettleOrderMapper settleOrderMapper;


    @Autowired
    private SysDdtTaskMapper sysDdtTaskMapper;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private DdtService ddtService;

    @Autowired
    private OrderQueryRemote orderQueryRemote;

    @Autowired
    private AgentRemote agentRemote;

    /**
     * 写入结算单任务到redis
     *
     * @param req 供应商编码
     */
    @Override
    @Transactional
    public void writeSettleTaskToRedis(SettleSupplyResultReq req) {
        log.info("[写入结算单任务到redis]" + JSON.toJSON(req));
        if (StrUtilX.isEmpty(req.getSupplierCode())) {
            // 供应商编码不能为空
            throw new SysException(ErrorCodeEnum.SETTLE_SUPPLIER_CODE_IS_NULL);
        }

        //查询供应商编码 + 酒店id 是否存在自助结算配置
        QueryWrapper<SettleHotelConfigEntity> query = new QueryWrapper<>();
        query.eq("supplier_code", req.getSupplierCode());
        query.eq("hotel_id", req.getHotelId());
        SettleHotelConfigEntity settleHotelConfig = settleHotelConfigMapper.selectOne(query);

        //已确认
        //生成创建结算单任务
        if (req.getSupplyResult().equals(SupplyResultEnum.CONFIRMED.key)) {
            //任务类型 {加力天宫供货单结果【是】(下结算单到订单通)}
            req.setTaskType(0);
            //订单类型 {saas订单任务类型}
            req.setOrderType(0);
        }
        //已取消
        //生成取消结算单任务
        if (req.getSupplyResult().equals(SupplyResultEnum.CANCELLED.key)) {
            //任务类型 {加力天宫供货单结果【否】(取消订单通订单)}
            req.setTaskType(1);
            //订单类型 {ireve订单任务类型}
            req.setOrderType(1);
        }

        if (null != settleHotelConfig) {
            //写入自助结算配置id
            req.setHid(settleHotelConfig.getHid());
            List<String> supplyResultList = new ArrayList<>();
            supplyResultList.add(JSON.toJSONString(req));
            //写入队列（从左边写入队列，需要在右边消费队列，使得先进先出的结果）
            //示例：
            RedisTemplateX.lLeftPushAll(SettleRedisKey.SETTLE_SUPPLYORDERCODE_RESULT_KEY, supplyResultList);
        }
    }

    /**
     * 读取结算单任务redis队列
     */
    @Override
    public void readSettleTaskFormRedis() {
        //读取redis数据
        String supplyOrderCodeResult = RedisTemplateX.lRightPop(SettleRedisKey.SETTLE_SUPPLYORDERCODE_RESULT_KEY);

        /*
         * 结算单任务生成逻辑
         */
        if (null != supplyOrderCodeResult) {
            try {
                //转化成SettleSupplyResultReq对象
                SettleSupplyResultReq req = JSONUtil.toBean(supplyOrderCodeResult, SettleSupplyResultReq.class);
                log.info("[结算单任务表生成]供货单号：" + req.getSupplierCode() + ",处理数据:" + JSON.toJSON(req));

                QueryWrapper<SettleTaskEntity> query = new QueryWrapper<>();
                query.eq("order_id", req.getOrderId()); //订单id
                query.eq("supply_order_id", req.getSupplyOrderId()); //供货单id
                query.eq("supply_result", req.getSupplyResult()); //确认结果
                //写入到结算单任务表
                SettleTaskEntity entity = settleTaskMapper.selectOne(query);
                if (null != entity) {
                    log.info("[结算单任务表生成]供货单号：" + req.getSupplierCode() + ",已经存在结算单任务");
                    return;
                }

                entity = new SettleTaskEntity();
                entity.setHid(req.getHid());            //自助结算配置id
                entity.setOrderId(req.getOrderId());    //自助结算订单id
                entity.setSupplyOrderId(req.getSupplyOrderId()); //供货订单id
                entity.setConfirmNo(req.getConfirmNo()); //确认号
                entity.setTaskType(req.getTaskType());   //任务类型
                entity.setResultType(ResultTypeEnum.RESULT_TYPE_0.key);  //任务结果 0, "未开始"  1, "进行中"  2, "已完成"  3, "失败"
                entity.setOrderType(req.getOrderType()); //订单类型
                entity.setErrorCount(0);                 //默认错误数
                entity.setSupplyResult(req.getSupplyResult()); //供货单确认结果 [1:已确认 2:不确认]

                entity.setCreatedDt(new Date());
                entity.setCreatedBy(RedisTemplateX.get(RedisKey.LOG_CODE + "O000026"));
                entity.setUpdatedDt(new Date());
                entity.setUpdatedBy(RedisTemplateX.get(RedisKey.LOG_CODE + "O000026"));
                //插入结算单任务中
                settleTaskMapper.insert(entity);
            } catch (Exception e) {
                log.info("[结算单任务表生成]供货单号异常：",e);
                //异常则重新放到队列中
                List<String> supplyResultList = new ArrayList<>();
                supplyResultList.add(supplyOrderCodeResult);
                RedisTemplateX.lLeftPushAll(SettleRedisKey.SETTLE_SUPPLYORDERCODE_RESULT_KEY, supplyResultList);
                throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
            }
        }


        /*
         *
         * 查询结算单任务表未完成、未处理数据
         *
         */
        Long size = RedisTemplateX.sSize(SettleRedisKey.SETTLE_SUPPLYORDERCODE_CREATE_CANCEL_KEY);
        if (size != 0) {
            //存在未处理完的缓存，直接退出，不做任何处理
            return;
        }

        //查询结算单任务表
        QueryWrapper<SettleTaskEntity> queryWrapper = new QueryWrapper<>();
        //查询[未开始]和[失败]的结算单任务
        queryWrapper.in("result_type", ResultTypeEnum.RESULT_TYPE_0.key, ResultTypeEnum.RESULT_TYPE_3.key);
        //按创建时间排序
        queryWrapper.orderBy(true, true, "created_dt");
        //查询结算单任务
        List<SettleTaskEntity> settleTaskEntityList = settleTaskMapper.selectList(queryWrapper);

        if (null != settleTaskEntityList && settleTaskEntityList.size() > 0) {
            //加入到【供货单】创建/取消结算单队列中
            String[] settleTaskId = settleTaskEntityList.stream()
                    .map(entity -> String.valueOf(entity.getId()))
                    .toArray(String[]::new);
            RedisTemplateX.setAdd(SettleRedisKey.SETTLE_SUPPLYORDERCODE_CREATE_CANCEL_KEY, settleTaskId);
        }
    }

    /**
     * 处理结算单任务
     */
    @Override
    public void processSettleTask() {
        Long size = RedisTemplateX.sSize(SettleRedisKey.SETTLE_SUPPLYORDERCODE_CREATE_CANCEL_KEY);

        //查询结算单任务是否大于0
        if (null != size && size > 0) {
            //判断最大值,最大值取10条记录,后续写入到nacos配置文件中
            if (size > 10) {
                size = 10L;
            }

            // 从队列中获取结算单任务的 ID
            List<String> taskIdStrings = RedisTemplateX.setPopCount(SettleRedisKey.SETTLE_SUPPLYORDERCODE_CREATE_CANCEL_KEY, size.intValue());

            // 将字符串类型的 ID 转换为 Long 类型
            List<Long> settleIds = taskIdStrings.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            //查询缓存取出的结算单任务id数据 和 未开始、已失败的结算单任务
            QueryWrapper<SettleTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("result_type", ResultTypeEnum.RESULT_TYPE_0.key, ResultTypeEnum.RESULT_TYPE_3.key);
            queryWrapper.in("id", settleIds);
            //批量查询结算单任务
            List<SettleTaskEntity> list = settleTaskMapper.selectList(queryWrapper);

            if (null != list && list.size() > 0) {
                //自助结算单配置id集合条件
                List<Long> hidList = new ArrayList<>();
                //供货单id集合条件
                SupplyOrderInfoListDTO supplyOrderListDTO = new SupplyOrderInfoListDTO();
                //订单id集合条件
                List<Integer> orderIdList = new ArrayList<>();
                //封装条件
                extracted(list, hidList, supplyOrderListDTO, orderIdList);


                //查询自助结算配置
                QueryWrapper<SettleHotelConfigEntity> settleConfigQuery = new QueryWrapper<>();
                settleConfigQuery.in("hid", hidList);
                List<SettleHotelConfigEntity> settleConfigList = settleHotelConfigMapper.selectList(settleConfigQuery);
                //根据自助结算配置id查询详情数据【settleConfigList 转化成 settleConfigMap】
                Map<Long, SettleHotelConfigEntity> settleConfigMap = settleConfigList.stream()
                        .collect(Collectors.toMap(SettleHotelConfigEntity::getHid, config -> config));


                //自助结算附加配置表
                QueryWrapper<SettleHotelConfigAttachEntity> settleHotelConfigAttachQueryWrapper = new QueryWrapper<>();
                settleConfigQuery.in("hid", hidList);
                List<SettleHotelConfigAttachEntity> settleHotelConfigAttachList = settleHotelConfigAttachMapper.selectList(settleHotelConfigAttachQueryWrapper);
                //根据自助结算配置id查询详情数据【settleHotelConfigAttachList 转化成 settleConfigAttachMap】
                Map<Long, SettleHotelConfigAttachEntity> settleConfigAttachMap = settleHotelConfigAttachList.stream()
                        .collect(Collectors.toMap(SettleHotelConfigAttachEntity::getHid, configAttach -> configAttach));

                //根据供货单id查询每日产品明细
                Response<List<SupplyProductDailyPriceDTO>> response = supplyProductPriceRemote.querySupplyOrderPriceList(supplyOrderListDTO);
                //根据供货单id查询每日产品明细【supplyProductPriceDTOList 转化成 supplyProductPriceMap】
                Map<Integer, List<SupplyProductDailyPriceDTO>> supplyProductPriceMap = response.getModel().stream()
                        .collect(Collectors.groupingBy(SupplyProductDailyPriceDTO::getSupplyOrderId));


                //根据供货单id查询供货单财务明细数据
                Response<List<SupplyOrderFinanceDTO>> supplyOrderFinanceInfoResp = supplyOrderFinanceRemote.querySupplyOrderFinanceInfo(supplyOrderListDTO);
                //根据供货单id查询供货单财务明细数据【supplyOrderFinanceDTOList 转化成 supplyOrderFinanceMap】
                Map<Long, SupplyOrderFinanceDTO> supplyOrderFinanceMap = supplyOrderFinanceInfoResp.getModel().stream()
                        .collect(Collectors.toMap(SupplyOrderFinanceDTO::getSupplyOrderId, dto -> dto));


                //根据订单id查询入住人信息
                Response<List<GuestDTO>> guestNameResp = supplyOrderFinanceRemote.queryGuestNameList(orderIdList);
                //根据订单id查询订单入住人数据【supplyOrderFinanceDTOList 转化成 supplyOrderFinanceMap】
                Map<Long, List<GuestDTO>> geustMap = guestNameResp.getModel().stream()
                        .collect(Collectors.groupingBy(dto -> dto.getOrderId().longValue()));


                for (SettleTaskEntity settleTaskEntity :
                        list) {
                    //自助结算id：取出自助结算配置
                    SettleHotelConfigEntity settleHotelConfig = settleConfigMap.get(settleTaskEntity.getHid());

                    //自助结算id：取出自助结算附加配置
                    SettleHotelConfigAttachEntity settleHotelConfigAttach = settleConfigAttachMap.get(settleTaskEntity.getHid());

                    //订单id：订单入住人信息
                    List<GuestDTO> guestDTO = geustMap.get(settleTaskEntity.getOrderId());

                    //完成类型：标记为处理中
                    //0, "未开始"  1, "进行中"  2, "已完成"  3, "失败"
                    settleTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_1.key);
                    settleTaskEntity.setUpdatedDt(new Date());
                    settleTaskMapper.updateById(settleTaskEntity);


                    //判断任务类型
                    if (settleTaskEntity.getTaskType().equals(SettleTaskTypeEnum.CONFIRMED.key)) {
                        /*
                         * 确认订单逻辑
                         */
                        try {
                            createSettleOrder(settleTaskEntity, supplyOrderFinanceMap, supplyProductPriceMap, settleHotelConfig, settleHotelConfigAttach, guestDTO);
                        } catch (Exception e) {
                            //完成类型：标记为未开始
                            //0, "未开始"  1, "进行中"  2, "已完成"  3, "失败"
                            settleTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_0.key);
                            settleTaskEntity.setUpdatedDt(new Date());

                            log.error("确认订单逻辑异常", e);
                        }
                    }

                    if (settleTaskEntity.getTaskType().equals(SettleTaskTypeEnum.CANCELLED.key)) {
                        try {
                            /*
                             * 取消订单逻辑
                             */
                            cancelledSettleOrder(settleTaskEntity, supplyOrderFinanceMap, settleHotelConfig);
                        } catch (Exception e) {
                            //完成类型：标记为未开始
                            //0, "未开始"  1, "进行中"  2, "已完成"  3, "失败"
                            settleTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_0.key);
                            settleTaskEntity.setUpdatedDt(new Date());

                            log.error("取消订单逻辑异常", e);
                        }
                    }
                }

                //更新最终任务状态
                for (SettleTaskEntity settleTask :
                        list) {
                    //标记需要处理的结算单任务，标记为处理中
                    UpdateWrapper<SettleTaskEntity> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("id", settleTask.getId()) //     // 结算单任务id
                            .set("result_type", settleTask.getResultType())  // 更新处理结果
                            .set("updated_dt", settleTask.getUpdatedDt());    // 更新时间

                    settleTaskMapper.update(null, updateWrapper);      // 执行批量更新操作
                }
            }
        }
    }


    /**
     * 更新结算任务数据
     */
    @Override
    public void updateSettleTask(SettleTaskEntity settleTask) {
        settleTaskMapper.updateById(settleTask);
    }

    /**
     * 条件封装
     *
     * @param list               结算单任务集合条件
     * @param hidList            自助结算配置集合条件
     * @param supplyOrderListDTO 供货单财务相关条件
     * @param orderIdList        订单id集合
     */
    private void extracted(List<SettleTaskEntity> list, List<Long> hidList, SupplyOrderInfoListDTO supplyOrderListDTO, List<Integer> orderIdList) {
        List<Integer> supplyOrderIdList = new ArrayList<>();

        for (SettleTaskEntity settleTask :
                list) {
            //标记需要处理的结算单任务，标记为处理中
            UpdateWrapper<SettleTaskEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", settleTask.getId()) // 任务id
                    .set("result_type", ResultTypeEnum.RESULT_TYPE_1.key); // 标记为进行中
            settleTaskMapper.update(null, updateWrapper);

            //自助结算单配置id集合
            Long hid = settleTask.getHid();
            if (!hidList.contains(hid)) {
                hidList.add(settleTask.getHid());
            }
            //供货单id集合
            Integer supplyOrderId = Integer.valueOf(settleTask.getSupplyOrderId().toString());
            if (!supplyOrderIdList.contains(supplyOrderId)) {
                supplyOrderIdList.add(Integer.valueOf(settleTask.getSupplyOrderId().toString()));
            }

            //订单id集合
            Integer orderId = settleTask.getOrderId().intValue();
            if (!orderIdList.contains(orderId)) {
                orderIdList.add(settleTask.getOrderId().intValue());
            }

        }
        supplyOrderListDTO.setSupplyOrderIdList(supplyOrderIdList);
    }


    /**
     * 结算单创建
     *
     * @param settleTaskEntity      结算单任务
     * @param supplyOrderFinanceMap 供应商财务信息集合
     * @param supplyProductPriceMap 供应商每日产品明细集合
     * @param settleHotelConfig     自助结算配置
     */
    private void createSettleOrder(SettleTaskEntity settleTaskEntity,
                                   Map<Long, SupplyOrderFinanceDTO> supplyOrderFinanceMap,
                                   Map<Integer, List<SupplyProductDailyPriceDTO>> supplyProductPriceMap,
                                   SettleHotelConfigEntity settleHotelConfig,
                                   SettleHotelConfigAttachEntity settleHotelConfigAttach,
                                   List<GuestDTO> guestDTO) throws ParseException {

        /*
         * 确认订单逻辑
         */
        //处理入住人相关
        //同一个房间号的入住人需要拼接起来
        Map<Integer, String> guestMap = guestDTO.stream()
                .collect(Collectors.groupingBy(
                        GuestDTO::getRoomNumber,
                        Collectors.mapping(GuestDTO::getName, Collectors.joining(", "))
                ));

        //订单通对象
        DdtOrderDto orderDto = new DdtOrderDto();

        //相同供货单id：供货单明细
        SupplyOrderFinanceDTO supplyOrderFinance = supplyOrderFinanceMap.get(settleTaskEntity.getSupplyOrderId());
        //房间号是通过","号分开
        //同一个供货单，不同房间号的情况下，需要把入住人拼接起来
        if (null != supplyOrderFinance.getRoomNumbers()) {
            List<String> roomNuberList = StrUtilX.stringToList(supplyOrderFinance.getRoomNumbers(), ",");
            StringBuilder guests = null;
            for (String s : roomNuberList) {
                if (null == guests) {
                    guests = new StringBuilder(guestMap.get(Integer.valueOf(s)));
                } else {
                    guests.append(",").append(guestMap.get(Integer.valueOf(s)));
                }
            }
            if (guests != null) {
                orderDto.setGuests(guests.toString());
            }
        }


        //相同供货单id：每日产品明细
        List<SupplyProductDailyPriceDTO> supplyProductDailyPrice = supplyProductPriceMap.get(Integer.valueOf(settleTaskEntity.getSupplyOrderId().toString()));
        //针对房间号进行分组
        Map<Integer, List<SupplyProductDailyPriceDTO>> groupedMap = supplyProductDailyPrice.stream()
                .collect(Collectors.groupingBy(SupplyProductDailyPriceDTO::getRoomNumber));

        //取第一个房间分组数据即可
        // 取第一个房间分组数据即可，如果只有一个分组，则使用原数据
        List<SupplyProductDailyPriceDTO> supplyProductPrice = new ArrayList<>();

        if (groupedMap.size() == 1) {
            supplyProductPrice = supplyProductDailyPrice;
        } else {
            supplyProductPrice = groupedMap.values().stream().findFirst().orElse(new ArrayList<>());
        }

        //订单通客户编码
        orderDto.setCustomerNo(settleHotelConfig.getDdtCustomerCode());
        //订单通商家编码
        orderDto.setMerchantCode(settleHotelConfig.getDdtMerchantCode());
        //酒店id
        orderDto.setHotelId(settleHotelConfig.getHotelId());
        //酒店名称
        orderDto.setHotelName(supplyOrderFinance.getHotelName());
        //创建人
        orderDto.setCreatedBy(RedisTemplateX.get(RedisKey.LOG_CODE + "O000026"));
        //币种
        orderDto.setCurrency(supplyOrderFinance.getBaseCurrency());
        //供货单编码
        orderDto.setSupplyOrderCode(supplyOrderFinance.getSupplyOrderCode());
        //客户单号
        orderDto.setAgentCode(settleHotelConfig.getDdtCustomerCode());
        //标记为自助结算订单
        orderDto.setSettleAgentSource(2);

        //根据订单id查询订单数据
        OrderDTO orderDTO = new OrderDTO();
        Response<OrderDTO> response = orderQueryRemote.queryContactOrder(Integer.valueOf(supplyOrderFinance.getOrderId().toString()));
        if (response.isSuccess() && response.getModel() != null) {
            orderDTO = response.getModel();
        }

        //订单联系人
        //是否同步订单联系人信息到ireve [0:不同步 1:同步]
        //同步：则取订单联系人
        //不同步，则取客户联系人
        if (settleHotelConfig.getIsSync() == 1) {
            //取订单联系人
            orderDto.setLinkManPhone(orderDTO.getContactPhone());
            orderDto.setLinkMan(orderDTO.getContactName());
        } else {
            AgentAddDTO request = new AgentAddDTO();
            request.setAgentCode(orderDTO.getAgentCode());
            //取客户联系人
            Response<AgentSelectDTO> response1 = agentRemote.queryAgentBaseInfo(request);

            if (response1.isSuccess()) {
                AgentSelectDTO agentSelectDTO = response1.getModel();
                orderDto.setLinkManPhone(agentSelectDTO.getAdminName());
                orderDto.setLinkMan(agentSelectDTO.getAgentTel());
            }
        }
        // 确认号
        orderDto.setConfirmNo(orderDTO.getConfirmationCode());

        //产品信息
        DdtProductDto productDto = new DdtProductDto();
        List<DdtProductDto> productList = new ArrayList<>();
        productList.add(productDto);
        orderDto.setProductList(productList);

        //房型id
        productDto.setRoomId(0L);
        //房型名称
        productDto.setRoomName(supplyOrderFinance.getRoomName());
        //产品类型
        productDto.setProductType(0);
        //产品id
        productDto.setProductId("0");
        //产品名称
        productDto.setProductName(supplyOrderFinance.getProductName());
        //床型
        productDto.setBedTypes(supplyOrderFinance.getBedType());
        //早餐数量
        productDto.setBreakfastQty(supplyOrderFinance.getBreakfastQty());
        //入住时间
        productDto.setStartDate(supplyOrderFinance.getStartDate());
        //离店时间
        productDto.setEndDate(supplyOrderFinance.getEndDate());
        //房间数量
        productDto.setRoomQty(supplyOrderFinance.getRoomQty());
        //备注
        productDto.setRemark(supplyOrderFinance.getConfirmationRemark());

        //售卖信息
        List<DdtProductPriceDto> ddtProductPriceDtoList = new ArrayList<>();
        productDto.setProductPriceList(ddtProductPriceDtoList);

        // 取消条款
        if (StrUtilX.isNotEmpty(supplyOrderFinance.getCancellationTerm())) {
            try {
                List<CancelRestriction> cancelRestrictions = JSON.parseObject(supplyOrderFinance.getCancellationTerm(), new TypeReference<List<CancelRestriction>>() {
                });
                if (CollUtilX.isNotEmpty(cancelRestrictions)) {
                    CancelRestriction cancelRestriction = cancelRestrictions.get(0);
//                    productDto.setCancelRestrict();// 取消条款文字描述
                    productDto.setCancelRestrictRemark(cancelRestriction.getCancelRestrictionRemark());// 取消条款备注
                    if (cancelRestriction.getCancelRestrictionType() != null) {
                        if (cancelRestriction.getCancelRestrictionType() == 1) {
                            productDto.setCancellationType(0);// 取消条款类型
                        } else if (cancelRestriction.getCancelRestrictionType() == 2) {
                            productDto.setCancellationType(2);// 取消条款类型
                        } else if (cancelRestriction.getCancelRestrictionType() == 3) {
                            productDto.setCancellationType(1);// 取消条款类型
                        }
                    }
                    productDto.setCancellationAdvancedDays(cancelRestriction.getCancelRestrictionDay());// 取消条款提前天数
                    productDto.setCancellationAdvancedTime(cancelRestriction.getCancelRestrictionTime());// 取消条款提前时间点
                }
            } catch (Exception e) {
                log.error("结算单创建转换取消条款异常");
            }
        }

        SimpleDateFormat sdfTmp = new SimpleDateFormat("yyyy-MM-dd");
        for (SupplyProductDailyPriceDTO supplyProductDailyPriceDTO :
                supplyProductPrice) {
            DdtProductPriceDto ddtProductPriceDto = new DdtProductPriceDto();
            //售卖日期
            ddtProductPriceDto.setDate(sdfTmp.format(supplyProductDailyPriceDTO.getSaleDate()));
            //售卖价格
            ddtProductPriceDto.setPrice(supplyProductDailyPriceDTO.getBasePrice());
            ddtProductPriceDtoList.add(ddtProductPriceDto);
        }
        log.info("调用订单通创建订单,订单明细：" + JSON.toJSON(orderDto));
        String ddtOrderCode = ddtService.createDdtOrder(orderDto);
        log.info("调用订单通创建订单，返回结果：" + ddtOrderCode);

        //返回创建订单不为空
        if (null != ddtOrderCode) {
            Date ireveCreateTime = new Date();
            /*
             * 创建结算单逻辑
             */
            QueryWrapper<SettleOrderEntity> query1 = new QueryWrapper<>();
            query1.eq("ddt_order_code", ddtOrderCode);
            SettleOrderEntity settleOrder = settleOrderMapper.selectOne(query1);
            if (null == settleOrder) {
                settleOrder = new SettleOrderEntity();
                settleOrder.setMerchantCode(settleHotelConfig.getMerchantCode());
                settleOrder.setOrderCode(supplyOrderFinance.getOrderCode());
                settleOrder.setOrderDt(supplyOrderFinance.getOrderDt());

                settleOrder.setSupplyOrderCode(supplyOrderFinance.getSupplyOrderCode());
                settleOrder.setTgSupplyOrderDt(supplyOrderFinance.getSupplyOrderDt());
                settleOrder.setSupplierCode(settleHotelConfig.getSupplierCode());

                settleOrder.setDdtMerchantCode(settleHotelConfig.getDdtMerchantCode());
                settleOrder.setDdtCustomerCode(settleHotelConfig.getDdtCustomerCode());
                settleOrder.setDdtOrderCode(ddtOrderCode);
                settleOrder.setDdtOrderDt(ireveCreateTime);

                settleOrder.setPayerAccountId(settleHotelConfigAttach.getPayerAccountId());
                settleOrder.setCheckInDate(sdfTmp.parse(supplyOrderFinance.getStartDate()));
                settleOrder.setCheckOutDate(sdfTmp.parse(supplyOrderFinance.getEndDate()));

                settleOrder.setCreatedBy(RedisTemplateX.get(RedisKey.LOG_CODE+"O000026"));
                settleOrder.setCreatedDt(new Date());
                settleOrder.setUpdatedBy(RedisTemplateX.get(RedisKey.LOG_CODE+"O000026"));
                settleOrder.setUpdatedDt(new Date());

                settleOrder.setSettleType(settleHotelConfig.getSettleTimeType());

                //结算时间类型不能为空
                if (settleHotelConfig.getSettleTimeType() != null) {
                    calculateSettleTime(supplyOrderFinance, settleHotelConfig, settleOrder, ireveCreateTime);
                }
                //判断邮箱是否为空
                if (StrUtilX.isNotEmpty(settleHotelConfig.getPubPaymentOrderEmailUrl())) {
                    //需要发送
                    settleOrder.setSendStatus(1);
                    settleOrder.setPubPaymentOrderEmailUrl(settleHotelConfig.getPubPaymentOrderEmailUrl());
                } else {
                    settleOrder.setSendStatus(0);
                }

                //结算单新增酒店id和确认号
                settleOrder.setHotelId(settleHotelConfig.getHotelId());
                settleOrder.setConfirmNo(settleTaskEntity.getConfirmNo());

                settleOrderMapper.insert(settleOrder);


                /*
                 * 订单通创建结算单任务处理
                 */

                //转化成订单通创建结算单对象
                DdtSettleOrderDto ddtSettleOrderDto = ObjectConvertUtil.oldSettleConvertNewSettle(settleOrder);

                SysDdtTaskEntity sysDdtTaskEntity = new SysDdtTaskEntity();
                sysDdtTaskEntity.setBusinessCode(ddtOrderCode);
                //任务类型 1、创建结算单, 2、取消订单 3、工单处理 4、支付失败同步
                sysDdtTaskEntity.setTaskType(DdtTaskTypeEnum.SETTLE_ORDER_SYNC.key);
                sysDdtTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_0.key);
                sysDdtTaskEntity.setRequest(JSON.toJSONString(ddtSettleOrderDto));

                sysDdtTaskEntity.setCreatedBy(RedisTemplateX.get(RedisKey.LOG_CODE+"O000026"));
                sysDdtTaskEntity.setCreatedDt(new Date());
                sysDdtTaskEntity.setUpdatedBy(RedisTemplateX.get(RedisKey.LOG_CODE+"O000026"));
                sysDdtTaskEntity.setUpdatedDt(new Date());

                sysDdtTaskMapper.insert(sysDdtTaskEntity);

                //标记为已经完成
                settleTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_2.key);
                settleTaskEntity.setUpdatedDt(new Date());
            }
        }else{
            //标记为未开始
            //由于订单通未返回订单号，则任务直接初始化。
            settleTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_0.key);
            settleTaskEntity.setUpdatedDt(new Date());
        }
    }

    /**
     * 取消订单
     *
     * @param settleTaskEntity      自助结算单任务
     * @param supplyOrderFinanceMap 供应商财务信息
     * @param settleHotelConfig     自助结算配置
     */
    private void cancelledSettleOrder(SettleTaskEntity settleTaskEntity,
                                      Map<Long, SupplyOrderFinanceDTO> supplyOrderFinanceMap,
                                      SettleHotelConfigEntity settleHotelConfig) {

        String supplyOrderCode = supplyOrderFinanceMap.get(settleTaskEntity.getSupplyOrderId()).getSupplyOrderCode();
        //根据供货单编码查询供货单结算单
        QueryWrapper<SettleOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("supply_order_code", supplyOrderCode);
        SettleOrderEntity settleOrder = settleOrderMapper.selectOne(queryWrapper);

        //判断结算单不为空
        if (null != settleOrder) {
            String jsonStr = JsonUtil.stringConvertJson("orderCode", settleOrder.getDdtOrderCode(), "merchantCode", settleHotelConfig.getDdtMerchantCode());
            //创建取消订单同步订单通任务
            SysDdtTaskEntity sysDdtTaskEntity = new SysDdtTaskEntity();
            sysDdtTaskEntity.setBusinessCode(settleOrder.getDdtOrderCode());    //订单通编码

            //任务类型 1、创建结算单, 2、取消订单 3、工单处理 4、支付失败同步
            sysDdtTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_0.key);   //未开始
            sysDdtTaskEntity.setTaskType(DdtTaskTypeEnum.CANCEL_ORDER.key);     //取消订单通订单任务
            sysDdtTaskEntity.setRequest(jsonStr);                               //处理的内容
            sysDdtTaskEntity.setCreatedBy(RedisTemplateX.get(RedisKey.LOG_CODE+"O000026"));
            sysDdtTaskEntity.setCreatedDt(new Date());
            sysDdtTaskEntity.setUpdatedBy(RedisTemplateX.get(RedisKey.LOG_CODE+"O000026"));
            sysDdtTaskEntity.setUpdatedDt(new Date());
            sysDdtTaskMapper.insert(sysDdtTaskEntity);
            //标记为已经完成
            settleTaskEntity.setResultType(ResultTypeEnum.RESULT_TYPE_2.key);
            settleTaskEntity.setUpdatedDt(new Date());
        }
    }

    /**
     * 计算结算时间
     *
     * @param supplyOrderFinance 供货单财务信息
     * @param settleHotelConfig  结算酒店配置
     * @param settleOrder        结算订单
     * @param ireveCreateTime    订单通创建时间
     */
    private void calculateSettleTime(SupplyOrderFinanceDTO supplyOrderFinance,
                                     SettleHotelConfigEntity settleHotelConfig,
                                     SettleOrderEntity settleOrder,
                                     Date ireveCreateTime) throws ParseException {

        SimpleDateFormat sdfTmp = new SimpleDateFormat("yyyy-MM-dd");
        //入住日期
        Date checkInDate = sdfTmp.parse(supplyOrderFinance.getStartDate());
        //离店日期
        Date checkOutDate = sdfTmp.parse(supplyOrderFinance.getEndDate());


        /*
         * 计算结算时间
         */
        if (settleHotelConfig.getSettleTimeType() == 1) {
            //下单后
            settleOrder.setSettleDt(DateUtilX.dateFormat(ireveCreateTime, "yyyy-MM-dd"));
        } else if (settleHotelConfig.getSettleTimeType() == 2) {
            //入住当日后
            settleOrder.setSettleDt(checkInDate);
        } else if (settleHotelConfig.getSettleTimeType() == 3) {
            //离店当日后
            settleOrder.setSettleDt(checkOutDate);
        } else if (settleHotelConfig.getSettleTimeType() == 4) {
            //离店周结
            int weekOfDate = DateUtilX.getWeekOfDate(checkOutDate);
            int addDay = 8 - weekOfDate;
            Date date = DateUtilX.getAddDay(checkOutDate, addDay);
            settleOrder.setSettleDt(date);
        } else if (settleHotelConfig.getSettleTimeType() == 5) {
            //离店半月结
            Calendar cal = Calendar.getInstance();
            cal.setTime(checkOutDate);
            int day = cal.get(Calendar.DATE);
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;

            if (day <= 15) { //在1-15号的订单，16后结算
                cal.set(year, month - 1, 16);
            } else {
                if (month > 12) { // 12月跨年
                    year = year + 1;
                    month = month + 1 - 12;
                }
                cal.set(year, month, 1);
            }
            settleOrder.setSettleDt(cal.getTime());
        } else if (settleHotelConfig.getSettleTimeType() == 6) {
            //离店月结
            Calendar cal = Calendar.getInstance();
            cal.setTime(checkOutDate);

            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;

            if (month > 12) { // 12月跨年
                year = year + 1;
                month = month + 1 - 12;
            }
            cal.set(year, month, 1);
            settleOrder.setSettleDt(cal.getTime());
        }
    }


    /**
     * 结算单任务表编辑
     */
    @Override
    @Transactional
    public void settleTaskEdit(SettleTaskReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        SettleTaskEntity entity = new SettleTaskEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy(loginUser.getFullUserName());
        // ===========开始业务==============

        // ===========结束业务==============
        int insert = settleTaskMapper.updateById(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1003.getCode(), ResultEnum.E_1003.getMessage());
        }
    }

    /**
     * 结算单任务表详情
     */
    @Override
    public SettleTaskResp settleTaskDetail(SettleTaskReq req) {
        SettleTaskEntity entity = settleTaskMapper.selectById(req.getId());
        SettleTaskResp resp = new SettleTaskResp();
        BeanUtils.copyProperties(entity, resp);
        return resp;
    }

}

