package com.tiangong.settle.settleTask.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import lombok.Data;

/**
 * 结算单任务表
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
@Data
@TableName("f_settle_task")
public class SettleTaskEntity extends Model<SettleTaskEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 供货单ID
     */
    private Long supplyOrderId;
    /**
     * 确认订单号
     */
    private String confirmNo;
    /**
     * 任务类型 0:saas供货单结果【是】(下结算单到ireve)  1:ireve供货单结果【否】(取消ireve订单)]
     */
    private Integer taskType;
    /**
     * 完成类型 [0:未开始 1:进行中 2:已完成 3:失败]
     */
    private Integer resultType;
    /**
     * 订单类型 [0:saas订单 1:ireve订单]
     */
    private Integer orderType;
    /**
     * 失败次数
     */
    private Integer errorCount;
    /**
     * 供货结果 [1:已确认 2:不确认]
     */
    private Integer supplyResult;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 自助结算配置id
     */
    private Long hid;

}
