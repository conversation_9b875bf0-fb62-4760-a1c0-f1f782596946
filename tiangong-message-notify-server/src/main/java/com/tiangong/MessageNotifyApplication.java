package com.tiangong;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @Date 2024/7/12 下午4:59
 * @Description:
 */

@SpringBootApplication
@EnableEncryptableProperties
@EnableFeignClients
@EnableDiscoveryClient
@MapperScan(basePackages = {"com.tiangong.mapper"})
@EntityScan(basePackages = {"com.tiangong.domain", "com.tiangong.domain.entity", "com.tiangong.domain.po"})
public class MessageNotifyApplication {

    public static void main(String[] args) {
        SpringApplication.run(MessageNotifyApplication.class, args);
    }

}
