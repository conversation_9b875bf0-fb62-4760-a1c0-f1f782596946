package com.tiangong.organization.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.TipsDTO;
import com.tiangong.dto.common.TipsResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/1 21:12
 **/
@FeignClient(value = "tiangong-common-server")
public interface TipsRemote {

    /**
     * 查询提示说明
     */
    @PostMapping("/common/tips/findTips")
    Response<List<TipsResp>> findTips(@RequestBody TipsDTO po);

    /**
     * 查询提示信息
     */
    @PostMapping("/common/tips/findTipList")
    Response<List<TipsResp>> findTipList(@RequestBody TipsDTO po);
}
