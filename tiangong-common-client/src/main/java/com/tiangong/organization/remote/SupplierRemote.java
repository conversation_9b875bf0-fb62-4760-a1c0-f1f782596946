package com.tiangong.organization.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.AddSupplierForeignReq;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.organization.remote.dto.SupplierSelectDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/7/2 15:18
 **/
@FeignClient(value = "tiangong-common-server")
public interface SupplierRemote {

    /**
     * 供应商详情
     */
    @PostMapping("/common/supplier/querySupplierDetail")
    Response<SupplierSelectDTO> querySupplierDetail(@RequestBody SupplierAddDTO request);

    /**
     * 批量新增供应商
     */
    @PostMapping(value = "common/supplier/addSupplierForeign", produces = {"application/json;charset=UTF-8"})
    Response<Map<String, Object>> addSupplierForeign(@RequestBody List<AddSupplierForeignReq> request);
}
