package com.tiangong.organization.remote.dto;

import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/12/16 17:27
 */
@Data
@SensitiveClass
public class BankAgentListDTO {

    private Integer agentId;

    /**
     * 银行卡Id
     */
    private Integer bankId;
    /**
     * 银行卡名称
     */
    private String bankName;
    /**
     * 开户名
     */
    private String accountName;

    /**
     * 余额
     */
    private BigDecimal balance;
    /**
     * 账号
     */
    @EncryptField
    private String accountNumber;
    /**
     * 行号
     */
    @EncryptField
    private String bankCode;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 币种
     */
    private Integer bankCurrency;

    /**
     * 开票主体公司名称
     */
    private String companyName;
}
