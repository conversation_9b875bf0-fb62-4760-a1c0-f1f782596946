package com.tiangong.organization.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.BankBalanceChangeReq;
import com.tiangong.dto.common.BankListReq;
import com.tiangong.organization.remote.dto.BankDetailResp;
import com.tiangong.organization.remote.dto.BankSupplierDTO;
import com.tiangong.organization.remote.dto.QueryBankDetailReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/2 11:34
 **/
@FeignClient(value = "tiangong-common-server")
public interface BankRemote {

    /**
     * 银行卡金额变动
     *
     * @param req 银行余额变动请求参数，包含银行卡ID和变动金额
     * @return 返回金额变动操作的响应结果
     */
    @PostMapping("/common/bank/bankBalanceChange")
    Response<Object> bankBalanceChange(BankBalanceChangeReq req);

    /**
     * 根据商户编码查询银行卡列表
     *
     * @param req 银行列表查询请求参数，包含商户编码等查询条件
     * @return 返回银行供应商DTO列表的响应结果
     */
    @PostMapping("/common/bank/queryBankList")
    Response<List<BankSupplierDTO>> queryBankList(@RequestBody BankListReq req);

    /**
     * 根据银行卡id查询银行卡信息
     *
     * @param req 查询银行详情请求参数，包含银行卡ID
     * @return 返回银行详情响应数据
     */
    @PostMapping("/common/bank/bankDetail")
    Response<BankDetailResp> bankDetail(@RequestBody QueryBankDetailReq req);

}
