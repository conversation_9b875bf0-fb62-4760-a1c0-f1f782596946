package com.tiangong.organization.remote.dto;

import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/27 19:11
 **/
@Data
@SensitiveClass
public class ContactSupplierDTO {
    /**
     * 联系人Id
     */
    private Integer contactId;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    @EncryptField
    private String contactTel;
    /**
     * 联系人角色
     */
    private String contactRole;

    /**
     * 联系人Email
     */
    private String contactEmail;

    /**
     * 联系人备注
     */
    private String contactRemark;


}
