package com.tiangong.organization.remote.dto;

import com.tiangong.enums.SupplyOneLevelChannelTypeEnum;
import com.tiangong.enums.SupplyTwoLevelChannelTypeEnum;
import com.tiangong.order.remote.request.AddSupplierAutoChannelDTO;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/27 15:54
 **/
@Data
@SensitiveClass
public class SupplierSelectDTO {
    /**
     * 供应商Id
     */
    private Integer supplierId;
    /**
     * 供应商类型 0-自签 1-酒店 2-API
     */
    private Integer supplierType;
    /**
     * 供应商企业名称
     */
    private String supplierName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 总管理员姓名
     */
    private String adminName;
    /**
     * 总管理员账号
     */
    private String adminAccount;
    /**
     * 总管理员手机号
     */
    @EncryptField
    private String adminTel;
    /**
     * 结算方式
     */
    private Integer settlementType;
    /**
     * 我司采购经理Id
     */
    private Integer purchaseManagerId;
    /**
     * 我司采购经理姓名
     */
    private String purchaseManagerName;
    /**
     * 联系人信息
     */
    private List<ContactSupplierDTO> contactList;

    /**
     * 我方联系人
     */
    private List<ContactSupplierDTO> OwncontactList;
    /**
     * 银行卡信息
     */
    private List<BankSupplierDTO> bankCardList;

    /**
     * 发单签约企业
     */
    private String companySignature;

    /**
     * 公章url
     */
    private String officialSealUrl;

    /**
     * 公章id
     */
    private Integer officialSealId;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 是否支持下凌晨房订单
     * 0:否
     * 1:是
     */
    private Integer isSupportEarlyMorningRoomOrder;


    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    private BigDecimal modifiedAmt;

    /**
     * 最低加幅金额
     */
    private BigDecimal lowestIncreaseAmt;

    /**
     * 发票类型：1普票 2专票
     */
    private Integer invoiceType;

    /**
     * 发票模式: 1商家开票 2酒店前台开票
     */
    private Integer invoiceModel;

    /**
     * 是否不落地 1不落地 2落地
     */
    private Integer isCached;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 供应商类型
     * @see com.tiangong.enums.SupplierFormEnum
     */
    private Integer supplierForm;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 集团id
     */
    private Integer groupId;

    /**
     * 集团名称
     */
    private String groupName;

    /**
     * 是否同步入住明细：0否 1是
     */
    private Integer isSyncCheckDetail;

    /**
     * 我方合作主题
     */
    private Integer invoiceId;

    /**
     * 我方主体名称
     */
    private String invoiceName;

    /**
     * 是否协议托管：0否 1是
     */
    private Integer enableProtocol;

    /**
     * 是否待结算
     */
    private Integer enableSettled;

    /**
     * 描述
     */
    private String remark;

    /**
     * 业务联系人
     */
    private String businessContactName;

    /**
     * 业务联系人电话
     */
    private String businessContactTel;

    /**
     * 商务经理
     */
    private String businessManager;

    /**
     * 运营经理
     */
    private String operationManager;

    /**
     * 自动发单渠道
     */
    private AddSupplierAutoChannelDTO addSupplierAutoChannelDTO;

    /**
     * 一级渠道类型
     * @see SupplyOneLevelChannelTypeEnum
     */
    private Integer oneLevelChannelType;

    /**
     * 二级渠道类型
     * @see SupplyTwoLevelChannelTypeEnum
     */
    private Integer twoLevelChannelType;

    /**
     * 三级渠道类型
     */
    private String threeLevelChannelType;
}
