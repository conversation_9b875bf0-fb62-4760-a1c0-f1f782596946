package com.tiangong.organization.remote.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/28 20:54
 **/
@Data
@SensitiveClass
public class AgentSelectDTO {

    private Integer id;
    /**
     * 客户Id
     */
    private Integer agentId;
    /**
     * 客户类型
     */
    private Integer agentType;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 总管理员姓名
     */
    private String adminName;
    /**
     * 总管理员账号
     */
    private String adminAccount;
    /**
     * 总管理员手机号
     */
    @EncryptField
    private String adminTel;
    /**
     * 个人手机号
     */
    @EncryptField
    private String agentTel;
    /**
     * 结算方式
     */
    private Integer settlementType;

    /**
     * 结算币种类型
     */
    private Integer settlementCurrency;
    /**
     * 信用额度
     */
    private String creditLine;
    /**
     * 剩余额度
     */
    private String Balance;
    /**
     * 我司销售经理ID
     */
    private Integer saleManagerId;
    /**
     * 我司销售经理
     */
    private String saleManagerName;
    /**
     * 联系人信息
     */
    private List<ContactSupplierDTO> contactList;

    /**
     * 我方联系人信息
     */
    private List<ContactSupplierDTO> ownContactAddDTOS;
    /**
     * 银行卡信息
     */
    private List<BankSupplierDTO> bankCardList;

    /**
     * 分销商相关信息
     */
    private List<OrgAgentApiConfigListResp> orgAgentApiConfigList;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 保留的小数位
     */
    private Integer decimalPlaces;

    /**
     * 取整方式 1向上取整 2向下取整 3四舍五入
     */
    private Integer roundingType;

    /**
     * 合作商编码
     */
    private String partnerCode;

    /**
     * 企业唯一编码
     */
    private String orgUniqueCode;

    /**
     * 国家名
     */
    private String countryName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 省份编码
     */
    private String provinceCode;


    /**
     * 城市名
     */
    private String cityName;

    /**
     * 城市编码
     */
    private String cityCode;


    /**
     * 企业域名
     */
    private String orgDomain;

    /**
     * 企业地址/客户地址
     */
    private String orgAddress;

    /**
     * 企业成立日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date establishedDate;

    /**
     * 个人付款账户信息
     */
    private List<BankAgentListDTO> bankAgentListDTOList;

    /**
     * 我方合作主题
     */
    private Integer invoiceId;

    /**
     * 我方开票主体名称
     */
    private String invoiceName;

    /**
     * 结算模式：0挂账 1预存
     */
    private Integer settledType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 业务经理
     */
    private String businessContactName;

    /**
     * 业务经理电话
     */
    private String businessContactTel;

    /**
     * 客户经理
     */
    private String agentManagerName;

    /**
     * 运营经理
     */
    private String operationManager;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 结算策略(0按下单 1按结算成本)
     */
    private Integer settlementStrategy;

    /**
     * 推送结算成本(0关 1开)
     */
    private Integer pushSettleCostSwitch;

    /**
     * 推送结算成本地址
     */
    private String pushSettleCostUrl;

    /**
     * 客户优选产品状态
     *
     * 0关闭
     * 1开启
     */
    private Integer preferredProductsStatus;

    /**
     * 客户确认函配置
     */
    private AgentConfirmationLetterConfigResp agentConfirmationLetterConfig;

    /**
     * 支付超时自动取消开关
     * 0-关闭 1-开启
     */
    private Integer paymentOvertimeCancelSwitch;

    /**
     * 支付超时等待时间
     */
    private Double paymentOvertimeCancelTime;
    /**
     * 是否开启额度预警：0否 1是
     */
    private Integer creditWarningEnabled;

    /**
     * 预警额度
     */
    private BigDecimal warningAmount;

    /**
     * 额度预警邮箱，多个邮箱用逗号分隔
     */
    private String warningEmails;

    /**
     * 额度账户类型 0：自己 1：其他客户 默认自己
     */
    private Integer lineAccountType;

    /**
     * 额度账户
     */
    private String lineAccount;

    /**
     * 额度账户名称
     */
    private String lineAccountName;

    /**
     * 额度账户剩余额度
     */
    private BigDecimal lineAccountBalance;
}
