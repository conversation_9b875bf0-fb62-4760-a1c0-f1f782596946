package com.tiangong.organization.remote.dto;

import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/29 15:11
 **/
@Data
@SensitiveClass
public class QuerySupplierListDTO {
    /**
     * 供应商Id
     */
    private Integer supplierId;
    /**
     * 供应商类型 0-自签 1-酒店 2-API
     */
    private Integer supplierType;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 管理员手机号
     */
    @EncryptField
    private String adminTel;
    /**
     * 我司采购经理Id
     */
    private Integer purchaseManagerId;
    /**
     * 我司采购经理
     */
    private String purchaseManagerName;
    /**
     * 启用状态
     */
    private Integer availableStatus;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 业务联系人
     */
    private String businessContactName;

    /**
     * 业务联系人电话
     */
    private String businessContactTel;

    /**
     * 商务经理
     */
    private String businessManager;

    /**
     * 运营经理
     */
    private String operationManager;

    /**
     * 创建时间
     */
    private String createdDt;
}
