package com.tiangong.organization.remote.dto;

import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/29 9:46
 **/
@Data
@SensitiveClass
public class CompanySelectDTO {
    /**
     * 企业Id
     */
    private Integer companyId;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 企业编码
     */
    private String companyCode;
    /**
     * 企业域名
     */
    private String companyDomain;
    /**
     * 总管理员姓名
     */
    private String adminName;
    /**
     * 总管理员账号
     */
    private String adminAccount;
    /**
     * 总管理员手机号
     */
    @EncryptField
    private String adminTel;
    /**
     * 企业营业执照List
     */
    private List<CompanyBusinessLicenseUrlDTO> pictureList;
    /**
     * 企业LOGO
     */
    private String companyLogoUrl;

    /**
     * 企业logo Id
     */
    private Integer companyLogoId;

    /**
     * 企业电话
     */
    private String companyTel;

    /**
     * 商家币种
     */
    private String orgCurrency;
    /**
     * 企业地址
     */
    private String companyAddress;
    /**
     * 企业简介
     */
    private String companySummary;
    /**
     * 企业创建时间
     */
    private String createdDt;

    /**
     * 落款主体
     */
    private List<SendEntityDTO> sendEntityList;

    /**
     * 客服电话
     */
    private String customerTel;

    /**
     * 是否开启hr对接配置 0-否 1-是
     */
    private Integer hrIsOpen;

    /**
     * hr对接地址
     */
    private String hrUrl;

    /**
     * hr系统编码
     */
    private String hrSystemCode;

    /**
     * hr系统私钥
     */
    private String hrPrivateKey;

    /**
     * hr系统公钥
     */
    private String hrPublicKey;

    /**
     * hr回调地址
     */
    private String hrCallbackUrl;

}
