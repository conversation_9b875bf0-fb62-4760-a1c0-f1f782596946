package com.tiangong.organization.remote.dto;

import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/6/27 19:12
 **/
@Data
@SensitiveClass
public class BankSupplierDTO {
    /**
     * 银行卡Id
     */
    private Integer bankId;
    /**
     * 银行卡名称
     */
    private String bankName;
    /**
     * 开户名
     */
    private String accountName;

    /**
     * 余额
     */
    private BigDecimal balance;
    /**
     * 账号
     */
    @EncryptField
    private String accountNumber;
    /**
     * 行号
     */
    @EncryptField
    private String bankCode;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 币种
     */
    private Integer bankCurrency;

    private Integer active;

    /**
     * 银行数据组合
     */
    private String bankMsg;

    /**
     * 币种名
     */
    private String bankCurrencyStr;

    /**
     * 开户行类型：0网商银行 1其他
     */
    private Integer bankType;

}
