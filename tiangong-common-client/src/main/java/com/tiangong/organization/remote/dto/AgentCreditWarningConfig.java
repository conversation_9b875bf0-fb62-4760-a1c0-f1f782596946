package com.tiangong.organization.remote.dto;

import lombok.Data;

import java.math.BigDecimal;
/**
 * 客户额度预警配置DTO
 * <AUTHOR>
 * @date 2025/6/9
 * @Description 客户额度预警配置DTO
 */
@Data
public class AgentCreditWarningConfig {

    /**
     * 是否开启额度预警：0否 1是
     */
    private Integer creditWarningEnabled;

    /**
     * 预警额度
     */
    private BigDecimal warningAmount;

    /**
     * 额度预警邮箱，多个邮箱用逗号分隔
     */
    private String warningEmails;
    /**
     * 额度
     */
    private BigDecimal balance;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 客户编码
     */
    private String agentCode;
}
