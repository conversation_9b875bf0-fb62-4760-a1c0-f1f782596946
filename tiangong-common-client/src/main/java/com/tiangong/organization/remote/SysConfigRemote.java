package com.tiangong.organization.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.SysConfigDTO;
import com.tiangong.dto.common.SysConfigReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2019/7/1 21:12
 **/
@FeignClient(value = "tiangong-common-server")
public interface SysConfigRemote {

    @PostMapping("/common/sysConfig/querySysConfigByKey")
    Response<SysConfigDTO> querySysConfigByKey(@RequestBody SysConfigReq req);
}
