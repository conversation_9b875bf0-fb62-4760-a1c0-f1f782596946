package com.tiangong.organization.remote.dto;

import com.tiangong.enums.SupplyOneLevelChannelTypeEnum;
import com.tiangong.enums.SupplyTwoLevelChannelTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/6/19 20:47
 **/
@Data
public class SupplierAddDTO {
    /**
     * 供应商Id
     */
    private Integer supplierId;
    /**
     * 供应商类型 0-自签 1-酒店 2-API
     */
    private Integer supplierType;
    /**
     * 供应商企业名称
     */
    private String supplierName;
    /**
     * 总管理员姓名
     */
    private String adminName;
    /**
     * 总管理员账号
     */
    private String adminAccount;
    /**
     * 总管理员手机号
     */
    private String adminTel;
    /**
     * 结算方式
     */
    private Integer settlementType;
    /**
     * 结算币种类型
     */
    private Integer settlementCurrency;

    /**
     * 我司采购经理Id
     */
    private Integer purchaseManagerId;
    /**
     * 我司采购经理姓名
     */
    private String purchaseManagerName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 数据创建时间
     */
    private Date creatDt;
    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据修改人
     */
    private String updatedBy;
    /**
     * 数据修改时间
     */
    private Date updatedDt;
    /**
     * 供应商启用状态
     */
    private Integer availableStatus;

    /**
     * 域名
     */
    private String orgDomain;

    /**
     * 公章id
     */
    private Integer officialSealId;

    /**
     * 酒店Id
     */
    private Long hotelId;

    private String hotelName;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 是否支持下凌晨房订单
     * 0:否
     * 1:是
     */
    private Integer isSupportEarlyMorningRoomOrder;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    private BigDecimal modifiedAmt;

    /**
     * 最低加幅金额
     */
    private BigDecimal lowestIncreaseAmt;

    /**
     * 保留的小数位
     */
    private Integer decimalPlaces;

    /**
     * 发票类型：1普票 2专票
     */
    private Integer invoiceType;

    /**
     * 发票模式: 1商家开票 2酒店前台开票
     */
    private Integer invoiceModel;

    /**
     * 是否不落地 1不落地 2落地
     */
    private Integer isCached;

    /**
     * 供应商类型
     * @see com.tiangong.enums.SupplierFormEnum
     */
    private Integer supplierForm;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 一级渠道类型
     * @see SupplyOneLevelChannelTypeEnum
     */
    private Integer oneLevelChannelType;

    /**
     * 二级渠道类型
     * @see SupplyTwoLevelChannelTypeEnum
     */
    private Integer twoLevelChannelType;

    /**
     * 三级渠道类型
     */
    private String threeLevelChannelType;
}
