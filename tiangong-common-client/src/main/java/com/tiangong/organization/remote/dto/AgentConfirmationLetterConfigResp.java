package com.tiangong.organization.remote.dto;

import lombok.Data;

/**
 * @program: glink_tiangong
 * @ClassName AgentConfirmationLetterConfigResp
 * @description:
 * @author: 湫
 * @create: 2025/04/15/ 16:21
 * @Version 1.0
 **/

@Data
public class AgentConfirmationLetterConfigResp {
    private Long id;
    private Integer auotoFlag;
    private String agentCode;
    private String blindCarbonCopy;
    private String carbonCopy;
    private Integer priceShow;
    private String language;
    private Integer annexCarry;
    private String logoUrl;
    private String createdBy;
    private String createdDt;
    private String updatedBy;
    private String updatedDt;
    /**
     * 客户专属客服电话
     */
    private String agentPhone;
}
