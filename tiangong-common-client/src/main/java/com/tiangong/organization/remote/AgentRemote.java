package com.tiangong.organization.remote;

import com.tiangong.common.Response;
import com.tiangong.organization.remote.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/1 21:12
 **/
@FeignClient(value = "tiangong-common-server")
public interface AgentRemote {

    /**
     * 查询客户详情
     * 根据客户编码查询客户的完整详细信息，包括基本信息、财务信息、配置信息等
     * @param request 客户查询请求对象，包含客户编码等查询条件
     * @return 客户详情响应对象，包含客户的完整信息
     */
    @PostMapping("/common/agent/queryAgentDetail")
    Response<AgentSelectDTO> queryAgentDetail(@RequestBody AgentAddDTO request);

    /**
     * 查询客户基本信息
     * 根据客户编码查询客户的基本信息，不包含敏感的财务配置信息
     * @param request 客户查询请求对象，包含客户编码等查询条件
     * @return 客户基本信息响应对象
     */
    @PostMapping("/common/agent/queryAgentBaseInfo")
    Response<AgentSelectDTO> queryAgentBaseInfo(@RequestBody AgentAddDTO request);

    /**
     * 修改客户额度（扣减或退还）
     * 批量处理客户额度的扣减或退还操作，用于订单相关的额度变更
     * @param agentCreditLineList 客户额度变更列表，包含客户编码、变更金额等信息
     * @return 额度变更操作结果，包含成功更新的记录数等信息
     */
    @PostMapping("/common/agent/modifyDeductRefundCreditLine")
    Response<AgentCreditLineResultDTO> modifyDeductRefundCreditLine(@RequestBody List<AgentCreditLineDTO> agentCreditLineList);

    /**
     * 初始化推送协议订单结算成本客户编码到缓存任务
     * 将需要推送结算成本的客户编码列表加载到Redis缓存中，用于后续的成本推送任务
     * @return 操作结果
     */
    @PostMapping("/common/agent/initPushProtocolOrderSettlementCostAgentCodeToRedisTask")
    Response<Object> initPushProtocolOrderSettlementCostAgentCodeToRedisTask();

    /**
     * 获取供应商列表信息 根据运营商编号
     * @param orgBaseInfoReq 请求参数
     * @return 供应商列表信息
     */
    @PostMapping("/common/agent/listSupplierBaseInfoByAgentCode")
    Response<List<SupplierBaseInfoResp>> listSupplierBaseInfoByAgentCode(SupplierBaseInfoReq orgBaseInfoReq);
}
