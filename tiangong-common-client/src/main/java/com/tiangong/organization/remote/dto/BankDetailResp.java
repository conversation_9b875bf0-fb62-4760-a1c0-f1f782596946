package com.tiangong.organization.remote.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/12/21 10:03
 */
@Data
public class BankDetailResp {

    private Integer bankId;
    /**
     * 银行卡名称
     */
    private String bankName;
    /**
     * 开户名
     */
    private String accountName;
    /**
     * 账号
     */
    private String accountNumber;
    /**
     * 行号
     */
    private String bankCode;

    /**
     * 机构Id
     */
    private String orgCode;
    /**
     * 机构类型
     */
    private Integer orgType;

    /**
     * 账号类型：1企业账号 2个人账号
     */
    private Integer accountType;

    /**
     * 银行币种
     */
    private Integer bankCurrency;

    /**
     * 公司发票主体id（t_invoice表）
     */
    private Integer companyInvoiceId;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 纳税人识别号
     */
    private String taxpayerIdentificationNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 开户账号
     */
    private String accountNo;


    /**
     * 发票类型：1普票2专票
     */
    private Integer invoiceType;

    /**
     * 发票税点
     */
    private BigDecimal invoiceRatio;


    /**
     * 开户行类型：0网商银行 1其他
     */
    private Integer bankType;

    /**
     * 是否有效 0无效 1有效
     */
    private Integer active;
}
