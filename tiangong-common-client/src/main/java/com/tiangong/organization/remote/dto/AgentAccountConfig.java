package com.tiangong.organization.remote.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AgentAccountConfig {

    private Integer id;

    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * parentCode
     */
    private String partnerCode;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系电话
     */
    private String linkmanTel;

    /**
     * 联系邮箱
     */
    private String linkmanEmail;

    /**
     * 秘钥
     */
    private String secretkey;

    /**
     * 订单推送地址
     */
    private String orderStatusPushUrl;

    /**
     * 发票通知接口
     */
    private String invoiceNotifyUrl;

    /**
     * 入住明细推送接口
     */
    private String orderCheckDetailNotifyUrl;


    /**
     * 订单退款接口
     */
    private String orderRefundNotifyUrl;

    /**
     * 订单推送接口
     */
    private String orderPushNotifyUrl;

    /**
     * 担保条款通知接口
     */
    private String warrantiesNotifyUrl;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 客户启用状态
     */
    private Integer availableStatus;

    /**
     * 剩余额度
     */
    private BigDecimal balance;

    /**
     * 结算方式
     */
    private Integer settlementType;

    /**
     * 我司销售经理ID
     */
    private Integer saleManagerId;

    /**
     * 保留的小数位
     */
    private Integer decimalPlaces;

    /**
     * 取整方式 1向上取整 2向下取整 3四舍五入
     */
    private Integer roundingType;

    /**
     * 结算币种类型
     */
    private Integer settlementCurrency;

    /**
     * 原始协议价输出开关
     */
    private Integer originalProtocolPriceSwitch;

    /**
     * 30天报价输出开关
     */
    private Integer quoteSwitch;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 数据加密开关
     */
    private Integer dataEncryptionSwitch;

    /**
     * 私钥
     */
    private String opsSecretKey;

    /**
     * 客户公钥
     */
    private String customerPublicKey;

    /**
     * 天宫公钥
     */
    private String tiangongPublicKey;

    /**
     * 客户Id
     */
    private Integer agentId;

    /**
     * 我方合作主题
     */
    private Integer invoiceId;

    /**
     * 结算模式：0挂账 1预存
     */
    private Integer settledType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 结算策略(0按下单 1按结算成本)
     */
    private Integer settlementStrategy;

    /**
     * 推送结算成本(0关 1开)
     */
    private Integer pushSettleCostSwitch;

    /**
     * 推送结算成本地址
     */
    private String pushSettleCostUrl;

    /**
     * 额度账户类型 0：自己 1：其他客户 默认自己
     */
    private Integer lineAccountType;

    /**
     * 额度账户
     */
    private String lineAccount;
}
