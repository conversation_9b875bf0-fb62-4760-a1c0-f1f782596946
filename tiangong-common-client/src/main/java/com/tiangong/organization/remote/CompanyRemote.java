package com.tiangong.organization.remote;

import com.tiangong.common.Response;
import com.tiangong.organization.remote.dto.CompanyAddDTO;
import com.tiangong.organization.remote.dto.CompanySelectDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2019/7/2 16:01
 **/
@FeignClient(value = "tiangong-common-server")
public interface CompanyRemote {

    /**
     * 查询运营商详情
     *
     * @param request 公司新增DTO请求，包含查询条件
     * @return 返回公司选择DTO的响应结果
     */
    @PostMapping("/common/company/queryCompanyDetail")
    Response<CompanySelectDTO> queryCompanyDetail(@RequestBody CompanyAddDTO request);
}
