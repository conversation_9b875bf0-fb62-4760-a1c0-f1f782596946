package com.tiangong.organization.remote.dto;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/7/15 17:01
 **/

@Data
public class AgentCreditLineDTO extends BaseDTO {
    /**
     * 额度明细Id
     */
    private Integer agentCreditLineId;
    /**
     * 分销商编码
     */
    private String agentCode;
    /**
     * 订单编码
     */
    private String orderCode;
    /**
     * 扣退额度
     */
    private BigDecimal deductRefundCreditLine;
}
