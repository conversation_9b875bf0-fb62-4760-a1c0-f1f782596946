package com.tiangong.organization.remote.dto;

import com.tiangong.dto.common.BasePO;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2024/04/17 10:42
 */
@Data
public class InvoiceDTO extends BasePO {

    private Integer id;

    /**
     * 类型 1客户 2供应商 3系统设置（公司）
     */
    private Integer type;

    /**
     * 客户或供应商编码
     */
    private String orgCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 纳税人识别号
     */
    private String taxpayerIdentificationNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 开户账号
     */
    private String accountNo;

    /**
     * 普票开票比例
     */
    private BigDecimal generalInvoiceRatio;

    /**
     * 专票开票比例
     */
    private BigDecimal specialInvoiceRatio;
}
