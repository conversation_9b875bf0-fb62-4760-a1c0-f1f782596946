package com.tiangong.fuzzyquery.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.fuzzyquery.dto.FuzzyAgentDTO;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 11:39
 * @Description: 模糊查询
 */
@FeignClient(value = "tiangong-common-server")
public interface FuzzyQueryRemote {

    /**
     * 模糊查询供应商
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含供应商查询条件
     * @return 返回分页的模糊供应商DTO数据响应结果
     */
    @PostMapping("/common/fuzzy/querySupplier")
    Response<PaginationSupportDTO<FuzzySupplierDTO>> querySupplier(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 查询供应商列表
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含供应商查询条件
     * @return 返回模糊供应商DTO列表的响应结果
     */
    @PostMapping("/common/fuzzy/querySupplierList")
    Response<List<FuzzySupplierDTO>> querySupplierList(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 模糊查询供应商(全部)
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含供应商查询条件
     * @return 返回分页的所有模糊供应商DTO数据响应结果
     */
    @PostMapping("/common/fuzzy/querySupplierAll")
    Response<PaginationSupportDTO<FuzzySupplierDTO>> querySupplierAll(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 模糊查询分销商
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含分销商查询条件
     * @return 返回分页的模糊分销商DTO数据响应结果
     */
    @PostMapping("/common/fuzzy/queryAgent")
    Response<PaginationSupportDTO<FuzzyAgentDTO>> queryAgent(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);
}
