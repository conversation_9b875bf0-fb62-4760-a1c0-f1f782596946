package com.tiangong.fuzzyquery.dto;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 12:01
 * @Description:模糊查询查询条件
 */
@Data
public class FuzzyQueryDTO extends BaseRequest {

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商类型 0-自签 1-酒店 2-API
     */
    private Integer supplierType;

    /**
     * 分销商名称
     */
    private String agentName;

    /**
     * 分销商编码
     */
    private String agentCode;

    /**
     * 房型Id
     */
    private Integer roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 客户类型
     */
    private String agentType;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 集团名称
     */
    private String groupName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 语言
     */
    private String language;

    /**
     * 区域表名
     */
    private String tXTableArea;

    /**
     * 供应商Id
     */
    private Integer supplierId;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer availableStatus;

    /**
     * 编码是否模糊查询
     */
    private Integer codeLike;

    /**
     * 结算币种类型
     */
    private Integer settlementCurrency;

    /**
     * 排除分销商编码
     */
    private String excludeAgentCode;
}
