package com.tiangong.fuzzyquery.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 11:44
 * @Description:
 */
@Data
public class FuzzyAgentDTO implements Serializable {

    /**
     * 客户id
     */
    private Integer agentId;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 结算币种类型
     */
    private Integer settlementCurrency;

    /**
     * 0：国内，1：国外
     */
    private Integer domesticOrOverseas;

    /**
     * 剩余额度
     */
    private BigDecimal balance;
}
