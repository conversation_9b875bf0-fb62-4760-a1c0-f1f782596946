package com.tiangong.file.remote;

import com.tiangong.common.Response;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@FeignClient(value = "tiangong-common-server")
public interface FileRemote {

    /**
     * 获取文件列表
     *
     * @param req 文件请求，包含查询条件
     * @return 返回文件响应列表的响应结果
     */
    @PostMapping(value = "/common/file/list", produces = "application/json;charset=UTF-8")
    Response<List<FileResp>> list(@RequestBody FileReq req);

    /**
     * 更新文件信息
     *
     * @param req 文件请求，包含要更新的文件信息
     * @return 返回更新操作的响应结果
     */
    @PostMapping(value = "/common/file/update", produces = "application/json;charset=UTF-8")
    Response<Object> update(@RequestBody FileReq req);

    /**
     * 上传文件
     *
     * @param file 多部分文件，要上传的文件对象
     * @return 返回文件响应信息的响应结果
     */
    @PostMapping(value = "/common/file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Response<FileResp> upload(@RequestPart("file") MultipartFile file);

    /**
     * 删除文件
     *
     * @param req 文件请求，包含要删除的文件信息
     * @return 返回删除操作的响应结果
     */
    @PostMapping(value = "/common/file/del", produces = "application/json;charset=UTF-8")
    Response<Object> del(@RequestBody FileReq req);

}
