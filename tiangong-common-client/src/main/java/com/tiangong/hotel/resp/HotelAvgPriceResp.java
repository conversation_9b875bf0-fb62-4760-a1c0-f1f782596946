package com.tiangong.hotel.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店平均价表
 */
@Data
public class HotelAvgPriceResp {

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 平均价
     */
    private BigDecimal avgPrice;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
