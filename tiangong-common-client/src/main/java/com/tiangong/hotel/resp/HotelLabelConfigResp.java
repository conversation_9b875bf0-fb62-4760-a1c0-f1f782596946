package com.tiangong.hotel.resp;

import lombok.Data;

@Data
public class HotelLabelConfigResp {
    /**
     * id
     */
    private Integer hotelLabelConfigId;
    /**
     * 标签编码
     */
    private String labelCode;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签推荐分值
     */
    private Long recommendScore;

    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private String updatedDt;
}
