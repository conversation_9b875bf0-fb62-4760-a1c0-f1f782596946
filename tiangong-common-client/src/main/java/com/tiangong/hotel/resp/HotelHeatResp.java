package com.tiangong.hotel.resp;

import lombok.Data;

import java.util.Date;

/**
 * 酒店热度值
 */
@Data
public class HotelHeatResp {

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 热度分数
     */
    private Long heatScore;

    /**
     * 可订分数
     */
    private Long bookableScore;

    /**
     * 评分分数
     */
    private Long gradeScore;

    /**
     * 所属集团分数
     */
    private Long groupScore;

    /**
     * 城市平均房价分数
     */
    private Long cityAvgPriceScore;

    /**
     * 预订间夜分数
     */
    private Long roomNightScore;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
