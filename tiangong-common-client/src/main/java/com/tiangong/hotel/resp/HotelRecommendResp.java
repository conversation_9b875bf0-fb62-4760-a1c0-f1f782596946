package com.tiangong.hotel.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 酒店推荐排序
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
@Data
public class HotelRecommendResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 酒店Id
     */
    private Long hotelId;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 推荐类型 1-推荐 2-协议
     */
    private Integer recommendType;
    /**
     * 推荐分值
     */
    private Integer recommendScore;
    /**
     * 标签id列表
     */
    private List<Integer> hotelLabelConfigIds;
    /**
     * 标签名称Str
     */
    private String hotelLabelNames;
    /**
     * 酒店标签配置信息
     */
    private List<HotelLabelConfigResp> hotelLabelConfigList;
    /**
     * 推荐总分值
     */
    private Integer sumRecommendScore;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private String createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private String updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;
}