package com.tiangong.hotel.remote;

import com.tiangong.common.Response;
import com.tiangong.hotel.req.CityAvgPriceReq;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelAvgPriceResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "tiangong-common-server")
public interface HotelHeatRemote {

    /**
     * 查询酒店热度表中所有酒店id
     *
     * @return 返回酒店ID列表的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/queryAllHotelHeatHotelIdList", produces = {"application/json;charset=UTF-8"})
    Response<List<Long>> queryAllHotelHeatHotelIdList();

    /**
     * 修改热度分数
     *
     * @param reqList 酒店热度请求列表，包含要更新的热度分数信息
     * @return 返回更新操作的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/updateHotelHeatScore", produces = {"application/json;charset=UTF-8"})
    Response<Object> updateHotelHeatScore(@RequestBody List<HotelHeatReq> reqList);

    /**
     * 批量保存酒店平均价
     *
     * @param reqList 酒店平均价请求列表，包含要保存的酒店平均价数据
     * @return 返回保存操作的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/batchSaveHotelAvgPrice", produces = {"application/json;charset=UTF-8"})
    Response<Object> batchSaveHotelAvgPrice(@RequestBody List<HotelAvgPriceReq> reqList);

    /**
     * 删除酒店平均价
     *
     * @param req 酒店平均价请求，包含要删除的酒店平均价标识
     * @return 返回删除操作的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/delHotelAvgPrice", produces = {"application/json;charset=UTF-8"})
    Response<Object> delHotelAvgPrice(@RequestBody HotelAvgPriceReq req);

    /**
     * 批量保存城市平均价
     *
     * @param reqList 城市平均价请求列表，包含要保存的城市平均价数据
     * @return 返回保存操作的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/batchSaveCityAvgPrice", produces = {"application/json;charset=UTF-8"})
    Response<Object> batchSaveCityAvgPrice(@RequestBody List<CityAvgPriceReq> reqList);

    /**
     * 查询酒店平均价城市编码列表
     *
     * @return 返回酒店平均价城市编码列表的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/queryHotelAvgPriceCityCodes", produces = {"application/json;charset=UTF-8"})
    Response<List<String>> queryHotelAvgPriceCityCodes();

    /**
     * 查询酒店平均价列表
     *
     * @param req 酒店平均价请求，包含查询条件
     * @return 返回酒店平均价响应列表的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/queryHotelAvgPriceList", produces = {"application/json;charset=UTF-8"})
    Response<List<HotelAvgPriceResp>> queryHotelAvgPriceList(@RequestBody HotelAvgPriceReq req);

    /**
     * 查询酒店热度表中所有酒店id列表（分页）
     *
     * @param req 酒店热度请求，包含分页查询条件
     * @return 返回酒店ID列表的响应结果
     */
    @PostMapping(value = "/common/hotelHeat/queryAllHotelHeatHotelIdPage", produces = {"application/json;charset=UTF-8"})
    Response<List<String>> queryAllHotelHeatHotelIdPage(@RequestBody HotelHeatReq req);
}
