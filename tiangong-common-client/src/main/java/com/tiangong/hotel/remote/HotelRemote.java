package com.tiangong.hotel.remote;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.CityReq;
import com.tiangong.dis.dto.CountryReq;
import com.tiangong.dis.dto.DistrictOrBusinessReq;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dto.common.HotelPublicDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.hotel.dto.BaseinfoAreadataDTO;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Set;

@FeignClient(value = "tiangong-common-server")
public interface HotelRemote {

    /**
     * 获取国家列表
     *
     * @param req 国家请求，包含国家查询条件
     * @return 返回基础信息区域数据DTO列表的响应结果
     */
    @PostMapping(value = "/common/hotel/queryCountryList",produces = {"application/json;charset=UTF-8"})
    Response<List<BaseinfoAreadataDTO>> queryCountryList(@RequestBody CountryReq req);

    /**
     * 获取行政区商业区
     *
     * @param req 行政区或商业区请求，包含查询条件
     * @return 返回行政区或商业区响应的响应结果
     */
    @PostMapping(value = "/common/hotel/queryBusinessAndDistrictList",produces = {"application/json;charset=UTF-8"})
    Response<DistrictOrBusinessResp> queryBusinessAndDistrictList(@RequestBody DistrictOrBusinessReq req);

    /**
     * 获取热门城市
     *
     * @param req 城市请求，包含热门城市查询条件
     * @return 返回热门城市响应列表的响应结果
     */
    @PostMapping(value = "/common/hotel/queryHotCityList",produces = {"application/json;charset=UTF-8"})
    Response<List<HotCityResp>> queryHotCityList(@RequestBody CityReq req);

    /**
     * 获取国际区号
     *
     * @return 返回基础信息区域DTO列表的响应结果
     */
    @PostMapping(value = "/common/hotel/getRegion",produces = {"application/json;charset=UTF-8"})
    Response<List<BaseinfoRegionDTO>> getRegion();

    /**
     * 获取酒店详细信息--按需输出
     *
     * @param req 酒店信息收集请求，包含酒店详情查询参数
     * @return 返回酒店信息收集DTO的响应结果
     */
    @PostMapping(value = "/common/hotel/queryHotelInfo",produces = {"application/json;charset=UTF-8"})
    Response<HotelInfoCollectionDTO> queryHotelInfo(@RequestBody HotelInfoCollectionReq req);

    /**
     * 查询酒店列表--按需输出
     *
     * @param req 酒店信息收集请求，包含酒店列表查询参数
     * @return 返回酒店信息收集DTO列表的响应结果
     */
    @PostMapping(value = "/common/hotel/queryHotelInfoList",produces = {"application/json;charset=UTF-8"})
    Response<List<HotelInfoCollectionDTO>> queryHotelInfoList(@RequestBody HotelInfoCollectionReq req);

    /**
     * 获取酒店详细信息字符串--按需输出
     *
     * @param req 酒店信息请求，包含酒店详情查询参数
     * @return 返回酒店详情字符串的响应结果
     */
    @PostMapping(value = "/common/hotel/queryHotelInfoStrList",produces = {"application/json;charset=UTF-8"})
    Response<String> queryHotelInfoStrList(@RequestBody HotelInfoReq req);

    /**
     * 根据关键字获取城市和酒店
     *
     * @param req 搜索城市和酒店请求，包含关键字等查询条件
     * @return 返回热门城市响应列表的响应结果
     */
    @PostMapping("/common/hotel/queryCityAndHotelByKeyword")
    Response<List<HotCityResp>> queryCityAndHotelByKeyword(@RequestBody SearchCityAndHotelReq req);

    /**
     * 更新时区，转发给基础信息云化
     *
     * @param updateTimeZone 时区更新DTO，包含时区更新信息
     * @return 返回更新操作的响应结果
     */
    @PostMapping("/common/hotel/updateTimeZone")
    Response<Object> updateTimeZone(@RequestBody UpdateTimeZoneDTO updateTimeZone);

    /**
     * 根据城市编码查询城市时区
     *
     * @param req 查询城市时区请求，包含城市编码
     * @return 返回查询城市时区响应的响应结果
     */
    @PostMapping("/common/hotel/queryCityTimeZone")
    Response<QueryCityTimeZoneResp> queryCityTimeZone(@RequestBody QueryCityTimeZoneReq req);

    /**
     * 查询酒店是否是黑名单
     *
     * @param req 新增酒店可见性请求，包含酒店和客户信息
     * @return 返回酒店可见性DTO的响应结果
     */
    @PostMapping("/common/hotelAvailable/queryHotelAvailable")
    Response<HotelAvailableDTO> queryHotelAvailable(@RequestBody AddHotelAvailableReq req);

    /**
     * 获取设施
     *
     * @return 返回设施DTO的响应结果
     */
    @PostMapping("/common/facility/queryFacility")
    Response<FacilityDto> queryFacility();

    /**
     * 获取酒店标签信息
     *
     * @param req 酒店信息请求，包含酒店标签查询参数
     * @return 返回酒店标签信息集合的响应结果
     */
    @PostMapping("/common/hotelRecommend/queryHotelLabelInfo")
    Response<Set<String>> queryHotelLabelInfo(@RequestBody HotelInfoReq req);

    /**
     * 更新mongodb公共表里面的数据
     *
     * @param hotelPublicDTO 酒店公共DTO，包含要更新的酒店数据
     */
    @PostMapping("/common/hotel/updateHotelMongodbByHotelId")
    void updateHotelMongodbByHotelId(HotelPublicDTO hotelPublicDTO);

    /**
     * 获取目的地数据
     *
     * @param paramMap 参数映射，包含目的地查询参数
     * @return 返回地址响应DTO列表的响应结果
     */
    @PostMapping("/common/hotel/getAddress")
    Response<List<AddressRespDTO>> getAddress(@RequestBody Map<String, String> paramMap);

    /**
     * 获取酒店过滤器
     *
     * @param hotelSearchReq 酒店搜索请求，包含过滤器查询参数
     * @return 返回酒店搜索响应的响应结果
     */
    @PostMapping("/common/hotel/getHotelSearch")
    Response<HotelSearchResp> getHotelSearch(@RequestBody HotelSearchReq hotelSearchReq);

    /**
     * 批量更新酒店热度值和推荐值到公共表
     *
     * @param req 更新酒店分数请求，包含批量更新的酒店分数信息
     */
    @PostMapping("/common/hotel/updateHotelScoreToMongodbBatch")
    void updateHotelScoreToMongodbBatch(@RequestBody UpdateHotelScoreReq req);

    /**
     * 查询酒店列表
     *
     * @param req 酒店分页请求，包含酒店列表查询和分页参数
     * @return 返回分页的酒店列表响应数据的响应结果
     */
    @PostMapping("/common/hotel/findHotelList")
    Response<PaginationSupportDTO<HotelListResp>> findHotelList(@RequestBody HotelPageReq req);

    /**
     * 查询目的地酒店
     *
     * @param req 目的地请求，包含目的地酒店查询参数
     * @return 返回ES酒店DTO列表的响应结果
     */
    @PostMapping("/common/hotel/searchDestinationHotel")
    Response<List<EsHotelDto>> searchDestinationHotel(@RequestBody DestinationReq req);

    /**
     * 查询目的地城市
     *
     * @param req 目的地请求，包含目的地城市查询参数
     * @return 返回ES城市DTO列表的响应结果
     */
    @PostMapping("/common/hotel/searchDestinationCity")
    Response<List<EsCityDto>> searchDestinationCity(@RequestBody DestinationReq req);


    /**
     * 查询目的地酒店, 不校验权限
     *
     * @param req 目的地请求，包含目的地酒店查询参数（无权限校验）
     * @return 返回ES酒店DTO列表的响应结果
     */
    @PostMapping("/common/hotel/searchDestinationHotel2")
    Response<List<EsHotelDto>> searchDestinationHotel2(@RequestBody DestinationReq req);

    /**
     * 热门城市列表
     *
     * @param vo 酒店热门城市VO，包含热门城市查询条件
     * @return 返回酒店热门城市DTO列表的响应结果
     */
    @PostMapping("/common/hotelPopularCity/queryHotelPopularCityList")
    Response<List<HotelPopularCityDTO>> queryHotelPopularCityList(@RequestBody HotelPopularCityVO vo);
}
