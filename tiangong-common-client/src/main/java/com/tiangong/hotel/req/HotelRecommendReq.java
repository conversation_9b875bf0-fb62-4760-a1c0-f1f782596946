package com.tiangong.hotel.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 酒店推荐排序
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
@Data
public class HotelRecommendReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 客户编码
     */
    private String agentName;
    /**
     * 客户编码列表
     */
    private List<String> agentCodeList;
    /**
     * 酒店Id
     */
    private Long hotelId;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 推荐类型 1-推荐 2-协议
     */
    private Integer recommendType;
    /**
     * 酒店标签配置id列表
     */
    private List<Integer> hotelLabelConfigIds;
    /**
     * 推荐分值
     */
    private Integer recommendScore;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 语言
     */
    private String language;

    /**
     * 酒店推荐id列表
     */
    private List<Long> hotelRecommendIdList;

    /**
     * 酒店标签配置id
     */
    private Integer hotelLabelConfigId;

    /**
     * 每次获取的记录数
     */
    private Integer batchSize;

    /**
     * 从哪一条记录开始获取
     */
    private Integer offset;
}