package com.tiangong.hotel.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 酒店热度值
 */
@Data
public class HotelHeatReq extends BaseRequest {

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 热度分数
     */
    private Long heatScore;

    /**
     * 可订分数
     */
    private Long bookableScore;

    /**
     * 评分分数
     */
    private Long gradeScore;

    /**
     * 所属集团分数
     */
    private Long groupScore;

    /**
     * 城市平均房价分数
     */
    private Long cityAvgPriceScore;

    /**
     * 预订间夜分数
     */
    private Long roomNightScore;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 酒店id列表
     */
    private List<Long> hotelIds;

    /**
     * 语言
     */
    private String language;

    /**
     * 查询的酒店表名
     */
    private String tXTable;

    /**
     * 每次获取的记录数
     */
    private Integer batchSize;

    /**
     * 从哪一条记录开始获取
     */
    private Integer offset;

    /**
     * 最后一条记录的id
     */
    private Long lastId;
}
