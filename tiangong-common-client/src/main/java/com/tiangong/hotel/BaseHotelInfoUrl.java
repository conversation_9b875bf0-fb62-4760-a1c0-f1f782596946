package com.tiangong.hotel;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/10/30 11:19
 */
@Data
public class BaseHotelInfoUrl {

    /**
     * 查询国家列表
     */
    public static String COUNTRY_LIST_URL = "manage/external/queryCountryList";

    /**
     * 查询城市列表
     */
    public static String CITY_LIST_RUL = "manage/external/queryCityList";

    /**
     * 查询所有城市列表--临时方案
     */
    public static String CITY_LIST_RUL_BAK = "manage/external/queryCityCodeList";

    /**
     * 更加城市id查询酒店id列表
     */
    public static String HOTEL_IDS_URL = "manage/external/queryHotelIdList";

    /**
     * 查询酒店id列表
     */
    public static String PUBLIC_HOTEL_IDS_URL = "manage/external/queryPublicHotelIdList";

    /**
     * 更加酒店id查询酒店详细信息
     */
    public static String HOTEL_BASE_INFO = "manage/external/querySummaryInfoHotelList";

    /**
     * 查询酒店和城市列表
     */
    public static String QUERY_HOTEL_AND_CITY = "manage/external/queryHotelAndCity";

    /**
     * 查询酒店增量
     */
    public static String QUERY_HOTEL_INCREMENT = "manage/external/queryHotelIncrement";

    /**
     * 更新城市时区
     */
    public static String UPDATE_CITY_TIME_ZONE = "manage/external/b2bUpdateCityTimeZone";

    /**
     * 获取城市，省份，国家数据接口
     */
    public static String SELECT_CITY_LIST = "manage/es/selectCityList";

    /**
     * 查询城市列表
     */
    public static String SELECT_CITY_LIST_BY_CITY_NAME = "manage/es/selectCityListByCityName";

    /**
     * 获取集团品牌数据接口
     */
    public static String GROUP_AND_BRAND = "manage/external/queryGroupBrandList";

    /**
     * 获取集团品牌数据接口
     */
    public static String QUERY_FACILITY_LIST = "manage/external/queryFacilityList";

    /**
     * 查询行政区商业区
     */
    public static String BUSINESS_AND_DISTRICT_LIST = "manage/es/selectAreaList";

    /**
     * 查询目的地
     */
    public static String GET_ADDRESS = "manage/es/findDestination";

    /**
     * 通过目的地id获取目的地数据
     */
    public static String GET_ADDRESS_BY_ID = "manage/es/findDestinationById";

    /**
     * 根据城市编码和关键字查询酒店列表
     */
    public static String FIND_HOTELIDS_BYCITYCODE_AND_KEYWORD="manage/es/findHotelIdsByCityCodeAndKeyword";

    /**
     * 查询目的地
     */
    public static String SEARCH_DESTINATION = "manage/es/searchDestination";

    /**
     * 查询国省市
     */
    public static String SEARCH_COUNTRY_PROVINCE_CITY = "/manage/es/selectCountryProvinceCity";

    /**
     * 酒店名
     */
    public static String hotelName = "hotelName";
    /**
     * 酒店简介
     */
    public static String introduce = "introduce";
    /**
     * 集团
     */
    public static String group = "group";
    /**
     * 品牌
     */
    public static String brand = "brand";
    /**
     * 经纬度
     */
    public static String geoInfo = "geoInfo";
    /**
     * 位置
     */
    public static String location = "location";
    /**
     * 行政区
     */
    public static String district = "district";
    /**
     * 商业区
     */
    public static String businesszone = "businesszone";
    /**
     * 酒店星级
     */
    public static String hotelStar = "hotelStar";
    /**
     * 入离政策
     */
    public static String inOutPolicy = "inOutPolicy";
    /**
     * 收费标准
     */
    public static String addBedCharge = "addBedCharge";
    /**
     * 酒店电话
     */
    public static String tel = "tel";
    /**
     * 酒店通知
     */
    public static String inform = "inform";
    /**
     * 设施facilityType（1-酒店设施 2-房型设施）
     */
    public static String facilities = "facilities";
    /**
     * 酒店图片集合
     */
    public static String images = "images";
    /**
     * 停车场
     */
    public static String parkings = "parkings";
    /**
     * 房型
     */
    public static String rooms = "rooms";
    /**
     * 会议室
     */
    public static String meetings = "meetings";
    /**
     * 酒店评分
     */
    public static String rating = "rating";
    /**
     * 酒店资质
     */
    public static String certificates = "certificates";
    /**
     * 酒店视频
     */
    public static String videoInfos = "videoInfos";
    /**
     * 早餐
     */
    public static String breakFast = "breakFast";
    /**
     * 加床政策
     */
    public static String addBedPolicy = "facilities";
    /**
     * 儿童政策
     */
    public static String childPolicy = "childPolicy";
    /**
     * 宠物政策
     */
    public static String petPolicy = "petPolicy";
    /**
     * 其他政策
     */
    public static String otherPolicy = "otherPolicy";

    /**
     * 文本政策
     */
    public static String hotelTextPolicy = "hotelTextPolicy";

}
