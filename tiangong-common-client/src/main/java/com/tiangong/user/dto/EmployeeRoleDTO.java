package com.tiangong.user.dto;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

@Data
public class EmployeeRoleDTO extends BaseDTO {

    /**
     * 企业成员角色id
     */
    private Integer employeeRoleId;

    /**
     * 企业成员角色名称
     */
    private String employeeRoleName;

    /**
     * 企业成员角色描述
     */
    private String employeeRoleDescription;


    private Integer userRoleId;

    private Integer employeeId;

    public EmployeeRoleDTO() {
    }

    public EmployeeRoleDTO(Integer employeeRoleId, String employeeRoleName) {
        this.employeeRoleId = employeeRoleId;
        this.employeeRoleName = employeeRoleName;
    }
}
