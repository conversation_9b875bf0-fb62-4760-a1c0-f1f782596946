package com.tiangong.user.dto;

import com.tiangong.dto.common.BaseDTO;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 企业员工
 */
@Data
@SensitiveClass
public class EmployeeUserDTO extends BaseDTO {

    /**
     * 企业成员id
     */

    private Integer employeeId;

    /**
     * 企业成员姓名
     */

    private String employeeName;

    /**
     * 企业成员姓名
     */

    private String employeeAccountPwd;

    /**
     * 企业成员密码1
     */

    private String employeeAccountPwd1;

    /**
     * 企业成员手机号
     */
    @EncryptField
    private String employeeTel;

    /**
     * 企业成员账号
     */
    private String employeeAccount;

    /**
     * 身份证
     */
    @EncryptField
    private String employeeIDNumber;

    /**
     * 账号类型 0-长期 1-短期
     */
    private Integer employeeAccountType;

    /**
     * 账号有效期开始时间
     */
    private String accountAvlStartTime;

    /**
     * 账号有效期结束时间
     */
    private String accountAvlEndTime;

    /**
     * 账号访问开始时间
     */
    private String visitStartTime;

    /**
     * 账号访问结束时间
     */
    private String visitEndTime;

    /**
     * 访问IP
     */
    private String visitIp;

    /**
     * 员工状态 0-离线 1-在线
     */
    private Integer employeeStatus;

    /**
     * 账号状态 0-激活 1-休眠 2-注销 3-锁定
     */
    private Integer accountStatus;

    /**
     * 企业成员角色
     */
    private List<EmployeeRoleDTO> employeeRoleList;

    /**
     * 解锁时间
     */
    private Date unlockTime;

    /**
     * 是否启用
     */
    private Integer availableStatus;

    /**
     * 是否能删除
     */
    private Integer deletableStatus;

    /**
     * 企业编码
     */
    private String orgCode;

    /**
     * 能否重置密码
     */
    private Integer resetable;

    /**
     * 能否修改
     */
    private Integer modifiable;

    /**
     * 用于判断是否是本人账号
     */
    private String modifiedAccount;

    /**
     * 用户角色
     */
    private String userRoles;

    /**
     * 是否有教程编辑权限 0-否 1-是
     */
    private Integer isNotePermission;

    /**
     * 当前用户Id
     */
    private Integer userId;

    /**
     * 企业成员密码
     */
    private String password;

    /**
     * 有效性 0-无效 1-有效
     */
    private Integer active;

    /**
     * 审核状态 0-无需审核 1-提交审核失败 2-审核中 3-审核通过 4-审核不通过
     */
    private Integer approvalStatus;

    /**
     * ip
     */
    private String ip;
}
