package com.tiangong.user.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户登录dto
 */
@Data
public class UserDTO {

    /**
     * 登录id
     */
    private Integer loginId;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 登录账号
     */
    private String loginAccount;

    /**
     * 所在公司编码
     */
    private String companyCode;

    /**
     * 所在公司名称
     */
    private String companyName;

    /**
     * 所在公司域名
     */
    private String companyDomain;

    /**
     * 企业LOGO
     */
    private String companyLogoUrl;

    /**
     * 酒店权限
     */
    private Integer hotelPermissions;

    /**
     * 登录密码
     */
    private String loginPwd;

    /**
     * 加签密码
     */
    private String signaturePwd;

    /**
     * 第一层目录
     */
    private List<FirstMenuDTO> menus;

    /**
     * token
     */
    private String token;

    /**
     * 盐值
     */
    private String salt;

    /**
     * 更新密码时间
     */
    private Date updatePwdTime;

    /**
     * 访问有效期开始时间
     */
    private String visitStartTime;

    /**
     * 访问有效期结束时间
     */
    private String visitEndTime;

    /**
     * 可访问IP
     */
    private String visitIp;

    /**
     * 账号状态 0-激活 1-休眠 2-注销 3-锁定
     */
    private Integer accountStatus;

    /**
     * 解锁时间
     */
    private Date unlockTime;

    /**
     * 是否超级管理员 0-否 1-是
     */
    private Integer isSuperAdmin;

    /**
     * 角色列表
     */
    private List<EmployeeRoleDTO> roleList;

    /**
     * 账号有效性
     */
    private Integer availableStatus;

    /**
     * 结算币种
     */
    private Integer settlementCurrency;

    /**
     * 企业类型
     */
    private String orgType;

    /**
     * 是否有教程编辑权限 0-否 1-是
     */
    private Integer isNotePermission;

    /**
     * 审核状态 0-无需审核 1-提交审核失败 2-审核中 3-审核通过 4-审核不通过
     */
    private Integer approvalStatus;

    /**
     * 是否有效 0-否 1-有
     */
    private Integer active;

}
