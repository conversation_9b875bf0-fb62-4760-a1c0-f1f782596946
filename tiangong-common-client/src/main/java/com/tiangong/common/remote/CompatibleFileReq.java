package com.tiangong.common.remote;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/8/20 17:38
 * @Description:
 */
@Data
public class CompatibleFileReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对象id
     */
    private String objId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * url地址数组
     */
    private String fileUrl;

    /**
     * 业务类型
     */
    private Integer type;

    /**
     * 备用id
     */
    private String spareId;

    /**
     * 图片类型
     * 0:外景图
     * 1:内景图
     * 2:房间图
     * 3:其它图
     */
    private Integer photoType;

    /**
     * 图片类型
     */
    private String fileFormat;

    private String fileId;

    private Integer fileSize;

    private Integer level;

}
