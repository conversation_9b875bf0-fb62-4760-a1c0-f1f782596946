package com.tiangong.operatelog.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/3/25 11:52
 */
@Data
@Slf4j
public class OperateLogReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long logId;

    /**
     * 描述
     */
    private String logName;

    /**
     * 操作人
     */
    private String logType;

    /**
     * 服务名
     */
    private String applicationName;

    /**
     * 操作内容
     */
    private String requestBrowser;

    /**
     * 方法
     */
    private String requestMethod;

    /**
     * 操作内容
     */
    private String requestIp;

    private String requestUrl;

    /**
     * ip
     */
    private String requestHeader;

    /**
     * 耗时
     */
    private String requestParam;

    /**
     * 浏览器
     */
    private String errorMsg;

    /**
     * 耗时
     */
    private Long elapsedTime;

    /**
     * 操作日期
     */
    private LocalDateTime createdDt;

    private String createdBy;

    /**
     * 日志级别 0：系统级事件 1：业务级事件
     */
    private Integer logLevel;

    /**
     * 操作类型
     * 0:查询
     * 1:增加
     * 2:删除
     * 3:修改
     * 4:导出
     * 5:加锁
     * 6:确认
     * 7:发短信
     * 8:登录
     * 9:处理
     * 10:取消
     * 11:发单
     * 12:重置
     * 13:推送
     * 14:刷新
     * 15:解锁
     * 16:退出登录
     * 17:移动
     * 18:越权操作
     * 19:ip异常操作
     * 20:连续登录失败
     * 21:获取
     * 22:请求错误
     * 23:初始化
     * 24:通知
     * 25:校验
     * 26:导入
     */
    private Integer operationType;

    private Integer operationResult;

    /**
     * 操作账号
     */
    private String userAccount;

}
