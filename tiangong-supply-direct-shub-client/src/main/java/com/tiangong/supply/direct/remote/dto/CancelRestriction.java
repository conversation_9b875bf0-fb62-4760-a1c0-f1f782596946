package com.tiangong.supply.direct.remote.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CancelRestriction {

    /**
     * 取消条款类型
     * 0 未设置取消条款；
     * 1 表示不可取消，不可更改；
     * 2 表示入住前day天time 点之后取消，取消需支付违约金（违约金根据cancellationPrice）
     * 3 表示入住前day天time点之后不可取消，不可更改。
     * 4 可免费取消
     * @see com.tiangong.enums.CancelRestrictionTypeEnum
     */
    private Integer cancelRestrictionType = 0;

    /**
     * 需提前取消的天数
     */
    private Integer cancelRestrictionDay;

    /**
     * 提前取消的天数的时间点
     * 如1700，表示17点
     */
    private String cancelRestrictionTime;

    /**
     * 取消费用
     */
    private BigDecimal cancellationPrice;

    /**
     * 取消备注
     */
    private String cancelRestrictionRemark;

    /**
     * 取消罚金
     */
    private List<CancelPenalties> cancelPenalties;
}
