package com.tiangong.supply.direct.remote.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CreateSupplyOrderResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 供货单号
     */
    private String supplyOrderCode;


    /**
     * 供应商编码
     */
    private String supplierCode;


    /**
     * 供应商(返回)订单号
     */
    private String supplierOrderId;
    

    /** 确认号 **/
    private String confirmNos;

    /**
     * 供货结果：（0-未确认，1-已确认，2表示-不确认，3-已取消）
     */
    private Integer supplyResult;

    /**
     * 供货结果为不确认时，用来表示供应商返回的不确认原因
     */
    private String supplierMessage;


    /**
     * 供应商确认人
     */
    private String confirmUser;

    /**
     * 供应奖励
     */
    private BigDecimal supplyReward;

    /**
     * 供应商返佣
     */
    private BigDecimal supplyShouldRackBack;
}