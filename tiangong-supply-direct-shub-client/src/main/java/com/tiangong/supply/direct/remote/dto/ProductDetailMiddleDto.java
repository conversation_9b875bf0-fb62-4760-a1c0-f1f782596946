package com.tiangong.supply.direct.remote.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019-12-20 20:34
 * @Description:
 */
@Data
public class ProductDetailMiddleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 售价
     */
    private BigDecimal basePrice;

    /**
     * 价格明细
     * 主要用于海外展示 售价包含那些东西
     */
    private TaxDetailDto taxDetail;

    /**
     * 早餐类型 1:中早 2:西早 3:自助早
     */
    private Integer breakfastType;

    /**
     * 早餐数量
     */
    private Integer breakfastNum;

    /**
     * 阶段取消条款
     */
    private List<CancelRestriction> cancelRestrictions;

    /**
     * 房量
     */
    private Integer quotaNum;

    /**
     * 房态(1:有房,2:待查,3:满房)
     */
    private Integer roomStatus;

    /**
     * 是否可超
     */
    private Boolean overDraft;

    /**
     * 预定条款类型
     * 0 未设置预订条款；
     * 1 预订需在day天前的time点之前
     * 2 表示预订需在day天前的time点之后。
     */
    private Integer bookRestrictType;

    /**
     * 提前预定的天数
     */
    private Integer bookRestrictDays;

    /**
     * 提前预定天数 Day 天之中的 time 点
     */
    private String bookRestrictTime;

    /**
     *  入住条款类型
     * 0 未设置入住条款；
     * 1 入住天数需>=day天；
     * 2 必须入住day天；
     * 3 入住天数需<=Day天。
     */
    private Integer occupancyRestrictType;

    /**
     * 需要入住的天数
     */
    private Integer occupancyRestrictDays;

    /**
     * 最少预订房间数
     */
    private Integer minBookRoomsRestrict;

    /**
     * 最多预订房间数
     */
    private Integer maxBookRoomsRestrict;


    private Integer currency;

    /**
     * 担保条款类型
     * 为空 未设置担保条款
     * 0 未设置担保条款
     * 1 按到店时间
     * 2 按预订间数
     * 3 预订即保（预订就必须担保）
     *
     */
    private Integer guaranteeType;

    /**
     * 担保条件
     * guaranteeType
     * 为1时：时间点，如1700，表示17点
     * 为2时：间数，如：2
     * 为3时：无
     */
    private String guaranteeCondition;

    /**
     * 担保费用类型
     * 1 全额
     * 2 首晚
     */
    private Integer guaranteeFeeType;

    /**
     * 标记位 是否促销
     */
    private Boolean isPromotion;

}
