package com.tiangong.supply.direct.remote.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020-1-9 17:09
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SupplyDetailsDTO {

    /**
     * 供货明细
     */
    private Long id;

    /**
     * 供货单ID
     */
    private Long supplyOrderId;

    /**
     * 日期
     */
    private Date roomDate;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 单价
     */
    @NotNull(message = "EMPTY_PARAM_PRICE")
    private BigDecimal roomPrice;

    /**
     * 早餐类型
     */
    private Integer breakFastType;

    /**
     * 早餐数量
     */
    private Integer breakFastNum;



}
