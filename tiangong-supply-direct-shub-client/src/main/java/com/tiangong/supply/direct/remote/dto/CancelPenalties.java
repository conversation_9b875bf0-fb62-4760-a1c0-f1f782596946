package com.tiangong.supply.direct.remote.dto;

import lombok.Data;

/**
 * @description: 取消罚金
 * @author: qiu
 * @create: 2024-06-13 10:33
 */
@Data
public class CancelPenalties {
    /**
     * 罚金类型
     * 1. *晚
     * 2. 固定金额
     * 3. 百分比
     * 4. 首晚百分比
     * @see com.tiangong.enums.PenaltiesTypeEnum
     */
    private Integer penaltiesType;

    /**
     * 开始时间 yyyy-MM-dd'T'HH:mm:ss.SSSXXX
     * 2022-09-04T23:59:00.000+07:00
     * 带有时区偏移量的日期时间字符串 酒店当地时间
     */
    private String startDate;

    /**
     * 结束时间 yyyy-MM-dd'T'HH:mm:ss.SSSXXX
     * 2022-09-04T23:59:00.000+07:00
     * 带有时区偏移量的日期时间字符串 酒店当地时间
     */
    private String endData;

    /**
     * 罚金值
     */
    private String penaltiesValue;

    /**
     * 罚金币种
     */
    private Integer currency;
    
}