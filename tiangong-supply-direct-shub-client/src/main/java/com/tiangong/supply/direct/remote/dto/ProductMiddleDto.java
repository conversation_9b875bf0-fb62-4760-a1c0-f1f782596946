package com.tiangong.supply.direct.remote.dto;

import com.tiangong.dto.product.HourlyRoomInfo;
import com.tiangong.dto.product.response.TipInfosDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductMiddleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商价格计划Id 供应商封装设置为sp_hotel_id+_+sp_room_id+_+sp_ratePlanId/productId+_+supplyCode 进行拼接
     * 后面通过通用封装会拼接为sp_hotel_id+_+sp_room_id+_+sp_ratePlanId/productId+_+supplyCode+_+protocolFlag(1/0)#SpPricePlanName
     * 示例：561794_1702770_85232030_S10267587_1#单早
     */
    private String spPricePlanId;

    /**
     * 供应商价格计划名称
     */
    private String spPricePlanName;

    /**
     * 供应商酒店id
     */
    private String spHotelId;

    /**
     * 供应商房型id
     */
    private String spRoomTypeId;

    /**
     * 币种 SettlementCurrencyEnum
     */
    private Integer currency;


    /**
     * 床型详细信息
     */
    private List<BedTypesDetailDto> bedTypeDetails;

    /**
     * 供应商编码
     */
    private String supplyCode;

    /**
     * 供应商酒店名称
     */
    private String spHotelName;

    /**
     * 供应商房型名称
     */
    private String spRoomName;

    /**
     * 宽带信息 1 收费 2免费 3无（默认）
     */
    private Integer broadBand;

    /**
     * 预订状态
     * 0：订完，
     * 1可订，
     * 2部分可订，
     * 3查看
     */
    private Integer bookType;


    /**
     * 窗型编码 枚举值见【窗型表】没有就不要设置 防止出现问题
     */
    private Integer windowType;

    /**
     * 最大入住人 如果为空 会统一在通用供应商模块 自动设置会2
     */
    private Integer maxGuestPerson;

    /**
     * 最小入住人
     */
    private Integer minGuestPerson;

    /**
     * 是否立即确认 是否立即确认
     * 0 否
     * 1 是
     * 空 不确定
     */
    private Integer immediateConfirm;

    /**
     * 是否钟点房
     */
    //private Integer hourRoom;

    /**
     * 是否可加床
     * 0 否
     * 1 是
     * 空 不确定
     */
    private Integer addBed;

    /**
     * 加床费用
     */
    private BigDecimal addBedPrice;

    /**
     * 加床费用描述
     */
    private String addBedPriceDesc;

    /**
     * 均价
     */
    private BigDecimal avgPrice;

    /**
     * 订单是否需要证件信息
     * 0 不需要
     * 1 需要
     * 空 默认为不需要
     */
    private Integer certificateFlag;


//    /**
//     * 每日详情
//     */
//    private List<ProductDetailMiddleDto> productDetails = new ArrayList<ProductDetailMiddleDto>();

    /**
     * 房间售卖列表
     * 多间房使用
     */
    private List<RoomItemDetailDto> roomItemDetails;


    /**
     * 发票模式 0:不开发票  1:商家开发票  2:酒店前台自取
     */
    private Integer invoiceModel;

    /**
     * 发票类型 0:或空 表示未确定 1:普票 2：专票
     */
    private Integer invoiceType;

    /**
     * 提示信息，带有 html 标签，展示时需支持按html 标签
     */
//    private String reminder;

    /**
     * 是否到店付标志
     * 1-是，0或其它-否
     */
    private Integer payAtHotelFlag;

    /**
     * 产品标签
     * 0或空-无标签
     * 1-协议价格标签
     * 2-平台自签标签
     * 3-团房标签
     * 必须存在值 默认非协议
     */
    private int productLabel;


    /**
     * 产品小类标签
     */
    private Integer smallProductLabel;


    /**
     * ==============开始==================
     * 下单查询产品返回参数,供应商对接过程中不需要下面信息
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 房型名称
     */
    private String roomName;
    /**
     * ==============结束===================
     */


    /**
     * 是否钟点房	Integer	1是0否 空代表否
     */
    private int hourlyRoom;

    /**
     * 钟点房对象
     */
    private HourlyRoomInfo hourlyRoomInfo;

    /**
     * 产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    private Integer labelType;

    /**
     * 礼包信息
     */
//    private List<String> giftPacks;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;
}
