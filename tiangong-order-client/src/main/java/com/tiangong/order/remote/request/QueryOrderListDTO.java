package com.tiangong.order.remote.request;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

@Data
public class QueryOrderListDTO extends BaseRequest {

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 入住人
     */
    private String guest;

    /**
     * 渠道订单号
     */
    private String channelOrderCode;

    /**
     * 订单确认状态
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer orderConfirmationStatus;

    /**
     * 日期查询类型：0下单日期，1入住日期，2离店日期
     */
    private Integer dateQueryType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 联系热人电话
     */
    private String contactPhone;

    /**
     * 联系人手机区号
     */
    private String contactCountryCode;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 我方供应商负责人
     */
    private Integer purchaseManagerId;

    /**
     * 供货单状态：0待确认，1已确认，2已取消
     */
    private Integer supplyOrderConfirmationStatus;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 是否中国订单1是0否
     */
    private Integer cnType;

    /**
     * 订单结算方式：1月结2半月结3周结4单结5日结
     */
    private Integer orderSettlementType;

    /**
     * 订单结算状态：0未结算，1已结算
     */
    private Integer orderSettlementStatus;

    /**
     * 供货单结算方式：1月结2半月结3周结4单结5日结
     */
    private Integer supplyOrderSettlementType;

    /**
     * 供货单结算状态：0未结算，1已结算
     */
    private Integer supplyOrderSettlementStatus;

    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 确认号
     */
    private String confirmationCode;

    /**
     * 供货方单号
     */
    private String supplierOrderCode;

    /**
     * 标星状态
     */
    private Integer markedStatus;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 是否我的订单
     */
    private Integer isMyOrder;

    /**
     * 特殊订单状态 0及时确认，1二次确认
     */
    private Integer specialOrderStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 订单号、酒店名、入住人复合字段
     */
    private String searchValue;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 是否问题单 0-否 1-是
     */
    private Integer isAbnormal;

    /**
     * 客户名称
     */
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 开票状态 （0 未开票， 1 已开票）
     */
    private Integer invoiceStatus;

    /**
     * 支付方式
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 异常单 5待确认异常，6取消异常
     */
    private Integer abnormalOrder;

    /**
     * 产品标签（0：无标签  1：协议价格标签）
     */
    private Integer productLabel;


    /**
     * 产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    private Integer productLabelType;

    /**
     * 产品类型
     * 0:全日房
     * 1:钟点房
     */
    private Integer productType;

    /**
     * 订单创建者
     */
    private String createdBy;

    /**
     * 订单类型 1当前客户下的所有订单 2下单人的所有订单
     */
    private Integer orderTypeList;

    private Integer userId;

    /**
     * 用户账号
     */
    private String userAccount;

    private String language;

    /**
     * 订单支付状态
     */
    private Integer payStatus;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 出行类型，1-因公，2-因私 非必填
     */
    private Integer travelType;

    private OrderUser orderUser;

    /**
     * 渠道名称
     */
    private String channelName;

    @Data
    public class OrderUser{
        private String userAccount;
        private String userName;
    }

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 是否异常取消供货单 0否 1是
     */
    private Integer eCancelSupplyOrder;

    /**
     * 同步入住明细是否异常：0否 1是
     */
    private Integer isCheckInDetailAbnormal;

    /**
     * 发票异常（0非 1是）查询发票异常的订单
     */
    private Integer isOrderInvoiceError;

    /**
     * 0--天宫，dhub，1----b2b
     */
    private Integer channel = 0;

    /**
     * 退房任务
     */
    private Integer isCheckout;

    /**
     * 是否通知VCC支付失败
     */
    private Integer isVccInformPayError;

    /**
     * 订单来源
     * @see com.tiangong.enums.OrderSourceEnum
     */
    private Integer orderSource;

    /**
     * 是否手工订单
     */
    private Integer isManualOrder;

    /**
     * 供货单付款方式
     * 0-URL付款
     * 1-银行转账
     * 2-WorldFirst账户收款
     */
    private Integer supplyOrderPaymentMethod;
}
