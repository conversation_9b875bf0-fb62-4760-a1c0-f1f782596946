package com.tiangong.order.remote.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/1/14 14:54
 * @Description:
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePayStatusAndDeductRefundCreditLineDTO {

    private String orderCode;
    private String fcOrderCode;
    private BigDecimal payAmount;
    private String agentCode;
    private String channelCode;

}
