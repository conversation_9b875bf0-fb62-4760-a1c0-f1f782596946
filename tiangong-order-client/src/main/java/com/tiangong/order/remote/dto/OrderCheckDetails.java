package com.tiangong.order.remote.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class OrderCheckDetails {

    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;

    /**
     * 房间号
     */
    private String roomNumber;

    /**
     * 入住状态
     * @see com.tiangong.enums.CheckInstateEnum
     */
    @NotNull(message = "入住状态不能为空!")
    private String checkInState;

    /**
     * 入住人姓名
     */
    @Valid
    @Size(min = 1, message = "EMPTY_PARAM_GUESTINFOS")
    private List<String> guestInfos;

    /**
     * 价格明细
     */
    @Valid
    @Size(min = 1, message = "EMPTY_PARAM_PRICEITEMS")
    private List<OrderCheckDetailPriceItem> priceItems;
}
