package com.tiangong.order.remote.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/11/10 18:30
 */
@Data
public class OrderCodeReq {

    /**
     * 订单号
     */
    @NotEmpty(message = "EMPTY_PARAM_ORDERCODE")
    private String orderCode;

    /**
     * 订单id
     */
    private Integer orderId;

    private String language;

    private Long hotelId;
}
