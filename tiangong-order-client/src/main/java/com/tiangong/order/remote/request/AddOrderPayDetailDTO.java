package com.tiangong.order.remote.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AddOrderPayDetailDTO implements Serializable {

    /**
     * 订单id
     */
    private String orderCode;

    /**
     * 付款类型 0-收 1-付
     */
    private Integer payType;

    /**
     * 合作商企业支付金额
     */
    private BigDecimal companyPayAmount;

    /**
     * 个人支付金额
     */
    private BigDecimal personPayAmount;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 支付账号
     */
    private String payAccount;

    /**
     * 合作商支付方式 0-企业支付 1-个人支付
     */
    private Integer partnerPayMethod;

    /**
     * 合作商编码
     */
    private String partnerCode;

}
