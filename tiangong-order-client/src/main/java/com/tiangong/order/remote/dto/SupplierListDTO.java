package com.tiangong.order.remote.dto;

import lombok.Data;

/**
 * 供应商-渠道列表
 *
 * <AUTHOR>
 * @date 2019/9/30 11:49
 **/
@Data
public class SupplierListDTO {

    /**
     * 自增长id
     */
    private Integer id;
    /**
     * 企业类型（0包房商/1酒店/0企业/1个人）
     */

    private Integer supplierType;
    /**
     * 时间段订单(0.所有时间 1.自定义时间)
     */
    private Integer suppliertimeType;
    /**
     * 企业名称
     */
    private String supplierName;
    /**
     * 自动发单状态(是否自动发单 0:是 1:否)
     */
    private Integer available;

    /**
     * 渠道编码列表
     */
    private String channelCodeList;

    /**
     * 渠道名称列表
     */
    private String channelNameList;

    /**
     * 企业ID
     */
    private String supplierCode;

    /**
     * 供应商自定义起始时间
     */
    private String startTime;

    /**
     * 供应商自定义结束时间
     */
    private String endTime;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 发单方式（1:直连   2:电邮   3:线下）
     */
    private Integer orderMethod;
}
