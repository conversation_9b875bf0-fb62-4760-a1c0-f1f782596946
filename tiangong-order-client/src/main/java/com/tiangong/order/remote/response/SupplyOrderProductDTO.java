package com.tiangong.order.remote.response;

import lombok.Data;

@Data
public class SupplyOrderProductDTO {

    private Long hotelId;

    private Long roomId;

    private Integer productId;

    private String productName;

    private String bedType;

    private String supplyCode;

    private Integer supplyType;

    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 供应商产品id
     */
    private String spProductId;

    /**
     * 供应商类型 0:国内,1:海外
     */
    private Integer supplierType;


}
