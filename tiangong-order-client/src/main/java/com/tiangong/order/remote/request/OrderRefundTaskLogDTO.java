package com.tiangong.order.remote.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 订单退款任务日志
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class OrderRefundTaskLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private String operatedTime;

}
