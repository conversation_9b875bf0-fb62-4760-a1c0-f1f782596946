package com.tiangong.order.remote.response;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供货单每日售价
 * <AUTHOR>
 */
@Data
public class SupplyProductDailyPriceDTO implements Serializable {


    /**
     * 供货单ID
     */
    private Integer supplyOrderId;

    /**
     * 房间号
     */
    private Integer roomNumber;

    /**
     * 售卖日期
     */
    private Date saleDate;

    /**
     * 底价
     */
    private BigDecimal basePrice;

}
