package com.tiangong.order.remote.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProtocolOrderSettleInfoDTO {

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 刷卡金额币种
     */
    private String swipingCardAmountCurrency;

    /**
     * 刷卡金额
     */
    private BigDecimal swipingCardAmount;

    /**
     * 刷卡汇率
     */
    private BigDecimal swipingCardRate;

    /**
     * 刷卡时间
     */
    private String swipingCardDt;

    /**
     * 结算金额币种
     */
    private String settleAmountCurrency;

    /**
     * 结算金额
     */
    private BigDecimal settleAmount;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 客户单号
     */
    private String channelOrderCode;

    /**
     * 导入人
     */
    private String importBy;

    /**
     * 导入时间
     */
    private String importDt;

    /**
     * 匹配状态(0新建 1匹配成功 2匹配失败)
     */
    private Integer matchingStatus;
}
