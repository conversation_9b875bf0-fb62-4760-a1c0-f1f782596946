package com.tiangong.order.remote;

import com.tiangong.common.Response;
import com.tiangong.order.remote.dto.InBlankCheckInfoDTO;
import com.tiangong.order.remote.dto.InvoiceNotifyRequest;
import com.tiangong.order.remote.dto.OrderCheckDetailInfoDTO;
import com.tiangong.order.remote.dto.OrderCodeInfoDTO;
import com.tiangong.order.remote.request.DeleteSupplyOrderAttachmentByLogicDTO;
import com.tiangong.order.remote.request.SaveSupplyAttachmentDTO;
import com.tiangong.order.remote.request.SupplyOrderIdDTO;
import com.tiangong.order.remote.request.UrgeOrderRequestDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@FeignClient(value = "tiangong-order-server")
public interface SupplyOrderRemote {

    /**
     * 上传供货单附件
     */
    @PostMapping("/order/saveSupplyAttachment")
    Response<Object> saveSupplyAttachment(@RequestBody SaveSupplyAttachmentDTO request);

    /**
     * 催单(仅美团)
     */
    @PostMapping("/order/urgeOrder")
    Response<Object> urgeOrder(@RequestBody UrgeOrderRequestDTO urgeOrderRequestDTO);

    /**
     * 订单入住明细
     */
    @PostMapping("order/check/detail")
    Response<Object> pushOrderCheckDetail(@RequestBody OrderCheckDetailInfoDTO orderCheckDetailInfoDTO);

    /**
     * 获取需要没有填写入住明细的供货单
     */
    @PostMapping("/order/queryInBlankCheckInfoSupplyOrderCodeList")
    Response<List<SupplyOrderDTO>> queryInBlankCheckInfoSupplyOrderCodeList(@RequestBody InBlankCheckInfoDTO inBlankCheckInfoDTO);

    /**
     * 发票开票状态
     */
    @PostMapping("order/invoice/status")
    Response<Object> pushInvoiceStatus(InvoiceNotifyRequest request);

    /**
     * 删除供货单附件(逻辑删除)
     */
    @RequestMapping(value = "/order/deleteSupplyOrderAttachmentByLogic")
    Response<Object> deleteSupplyOrderAttachmentByLogic(@RequestBody DeleteSupplyOrderAttachmentByLogicDTO deleteSupplyOrderAttachmentByLogicDTO);

    /**
     * 根据供货单编码查询订单编码
     */
    @PostMapping("/order/queryOrderCodeBySupplyOrderCode")
    Response<List<OrderCodeInfoDTO>> queryOrderCodeBySupplyOrderCode(@RequestBody SupplyOrderIdDTO request);

    /**
     * 查询供货单详情
     */
    @PostMapping("/order/querySupplyOrderInfo")
    Response<SupplyOrderDTO> querySupplyOrderInfo(@RequestBody SupplyOrderIdDTO request);
}
