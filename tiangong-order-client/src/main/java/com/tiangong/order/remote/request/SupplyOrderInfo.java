package com.tiangong.order.remote.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplyOrderInfo {
    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 确认状态：0待确认，1已确认，2已取消
     */
    private Integer confirmationStatus;

    /**
     * 底价币种
     */
    private Integer baseCurrency;

    /**
     * 供货单价格
     */
    private BigDecimal supplyOrderAmt;
}
