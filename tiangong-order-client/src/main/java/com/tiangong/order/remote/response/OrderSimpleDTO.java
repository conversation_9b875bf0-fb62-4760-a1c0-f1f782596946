package com.tiangong.order.remote.response;

import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.order.remote.request.SupplyOrderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderSimpleDTO implements Serializable {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 是否终点房1是0否
     */
    private Integer hourly;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 下单时间
     */
    private String createdDt;

    /**
     * 预定人，这个是根据create_by这个属性设置的
     */
    private String bookingPerson;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 分销商名称
     */
    private String agentName;
    /**
     * 分销商编码
     */
    private String agentCode;
    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 入住日期
     */
    private String startDate;

    /**
     * 离店日期
     */
    private String endDate;

    /**
     * 入住时间
     */
    private String startTime;

    /**
     * 离店时间
     */
    private String endTime;

    /**
     * 入住人
     */
    private String guest;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmt;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 售价汇率
     */
    private BigDecimal saleRate;

    /**
     * 订单结算状态：0未结算，1已结算
     */
    private Integer orderSettlementStatus;


    /**
     * 订单结算方式：0月结 1半月结 2周结 3单结 4日结
     */
    private Integer orderSettlementType;

    /**
     * 结算币种
     */
    private Integer saleCurrency;

    /**
     * 结算币种名
     */
    private String currency;

    /**
     * 供应商低价币种
     */
    private Integer baseCurrency;

    /**
     * 供货单金额
     */
    private BigDecimal supplyOrderAmt;

    /**
     * 订单确认状态
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer orderConfirmationStatus;

    /**
     * 供货单确认状态：0待确认，1已确认，2已取消
     */
    private Integer supplyOrderConfirmationStatus;

    /**
     * 归属人
     */
    private String orderOwnerName;

    /**
     * 订单标签
     */
    private List<String> orderTagList;

    /**
     * 确认号
     */
    private String confirmationCode;

    /**
     * 锁单人名称
     */
    private String lockName;

    /**
     * 标星：0未标，1已标
     */
    private Integer markedStatus;

    /**
     * 客人特殊要求
     */
    private Integer isShownOnSupplyOrder;



    /**
     * 修改状态：0无，1取消中，2修改中，3二次确认
     */
    private Integer modificationRequestStatus;


    /**
     * 是否即时确认, 0否 ,1是
     */
    private Integer instantConfimationStatus;

    /**
     * 是否手工单
     */
    private Integer isManualOrder;

    /**
     * 锁定状态
     */
    private Integer lockStatus;

    /**
     * 是否问题单 0-否 1-是
     */
    private Integer isAbnormal;

    /**
     * 夜数
     */
    private String nightQty;

    /**
     * 支付方式（0:预付 1:面付无佣金）
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 最早到店时间
     */
    private String earliestArriveTime;

    /**
     * 最晚到店时间
     */
    private String latestArriveTime;

    /**
     * 1补单 2帮客户下单
     */
    private Integer isSubstituted;

    /**
     * 供货单信息
     */
    private List<SupplyOrderInfo> supplyOrderInfos;

    /**
     * 产品标签（0：无标签  1：协议价格标签）
     */
    private Integer productLabel;

    /**
     * 产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    private Integer productLabelType;

    /**
     * 是否是凌晨房订单(0:否,1:是)
     */
    @Column(name = "is_early_morning_room_order")
    private Integer isEarlyMorningRoomOrder;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 是否中国订单1是0否
     */
    private Integer cnType;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种
     */
    private Integer payAtHotelCurrency;

    /**
     * 到店另付费用币种
     */
    private String payAtHotelCurrencyCode;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 快速处理标签开关 0-关 1-开
     */
    private Integer quickProcessingSwitch;

    /**
     * 是否混合支付 0-否 1-是
     */
    private Integer isMixedPay;

    /**
     * 是否退改 0-否 1-是
     */
    private Integer isRefund;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 退改任务单号
     */
    private String taskOrderCode;

    /**
     * 支付状态 0-未支付 1-已支付
     */
    private Integer payStatus;

    /**
     * 入住人列表
     */
    private List<OrderGuestDTO> guestList;

    /**
     * 订单来源（手工单、GDP、API）
     * @see com.tiangong.enums.OrderSourceEnum
     */
    private Integer orderSource;
}
