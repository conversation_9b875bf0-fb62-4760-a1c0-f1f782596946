package com.tiangong.order.remote.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单退款信息
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderRefundDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 退款明细
     */
    private List<OrderRefundDetailDTO> refundDetailList;

}
