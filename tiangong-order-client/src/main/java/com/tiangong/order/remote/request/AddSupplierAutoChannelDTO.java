package com.tiangong.order.remote.request;

import com.tiangong.annotations.Compared;
import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/9 16:40
 **/
@Data
public class AddSupplierAutoChannelDTO extends BaseDTO {
    /**
     * id
     */
    private Integer id;
    /**
     * 是否自动发单 0:是 1:否
     */
    private Integer available;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 时间段订单(0.所有时间 1.自定义时间)
     */
    private Integer suppliertimeType;
    /**
     * 供应商类型
     */
    private Integer supplierType;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 渠道编码
     */
    @Compared(variableName = "渠道编码", enumsName = "com.tiangong.enums.ChannelEnum", split = ",")
    private String channelCodeList;
    /**
     * 供应商自定义起始时间
     */
    private String startTime;
    /**
     * 供应商自定义结束时间
     */
    private String endTime;

    private String companyCode;

    //以下是2024年09月版本新增字段

    /**
     * 发单方式（1:直连   2:电邮   3:线下）
     */
    private Integer orderMethod;

    /**
     * 姓名（发单方式：电邮）
     */
    private String recipientName;

    /**
     * 邮箱（发单方式：电邮）
     */
    private String preorderEmail;

    /**
     * VCC支付
     */
    private Integer vccPay;

    /**
     * VCC邮箱（发单方式：电邮）
     */
    private String vccEmail;

    /**
     * 公司名称（发单方式：电邮）
     */
    private String companyName;

    /**
     * 备注（发单方式：电邮）
     */
    private String remarks;

    /**
     * 文件集合
     */
    private List<FileAttachDTO> fileAttachDTOList;

    /**
     * 入口 1订单入口 2其他入口
     */
    private Integer entrance;
}
