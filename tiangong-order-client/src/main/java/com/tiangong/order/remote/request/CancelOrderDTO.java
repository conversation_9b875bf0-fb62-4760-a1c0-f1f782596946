package com.tiangong.order.remote.request;

import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CancelOrderDTO implements Serializable {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 退改费
     */
    private BigDecimal refundFee;

    /**
     * 取消原因
     */
    private String cancelledReason;

    /**
     * 取消内容
     */
    private String cancelledContent;

    /**
     * 申请id
     */
    private Integer orderRequestId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 订单归属人
     */
    private String orderOwnerName;

    /**
     * 供货结果
     *
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer supplyResult;

    /**
     * 排查问题
     */
    private String requestId;

    /**
     * 不校验取消状态（标识如果是取消状态不抛异常）
     */
    private Boolean notCheckCancelStatus;

    /**
     * 渠道编码
     */
    private String channelCode;
}
