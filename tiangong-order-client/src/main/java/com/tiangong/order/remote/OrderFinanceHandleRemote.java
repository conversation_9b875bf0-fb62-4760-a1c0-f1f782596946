package com.tiangong.order.remote;

import com.tiangong.common.Response;
import com.tiangong.order.remote.request.CancelOrderWorkOrderDTO;
import com.tiangong.order.remote.request.ConfirmOrderWorkOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Objects;

@FeignClient(value = "tiangong-order-server")
public interface OrderFinanceHandleRemote {

    /**
     * 确认工单
     */
    @PostMapping("/order/finance/handle/confirmOrderWorkOrder")
    Response<Object> confirmOrderWorkOrder(@RequestBody ConfirmOrderWorkOrderDTO request);

    /**
     * 取消工单
     */
    @PostMapping("/order/finance/handle/cancelOrderWorkOrder")
    Response<Object> cancelOrderWorkOrder(@RequestBody CancelOrderWorkOrderDTO request);
}
