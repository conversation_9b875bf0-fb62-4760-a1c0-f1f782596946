package com.tiangong.order.remote.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tiangong.dto.common.BaseDTO;
import com.tiangong.dto.common.TipsResp;
import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.response.OrderFeeDTO;
import com.tiangong.dto.order.response.TaxDTO;
import com.tiangong.dto.product.response.TipInfosDTO;
import com.tiangong.order.remote.dto.OrderRestrictDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderDTO extends BaseDTO implements Serializable {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 订单确认状态
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer orderConfirmationStatus;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 联系人电话区号
     */
    private String contactCountryCode;

    /**
     * 早餐数
     */
    private Integer breakfastQty;

    /**
     * 入住日期
     */
    private String startDate;

    /**
     * 离店日期
     */
    private String endDate;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 离店时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    /**
     * 晚数
     */
    private String nightQty;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 支付方式（0:预付 1:到店付）
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 入住人（多个入住人逗号分隔）
     */
    private String guest;

    /**
     * 入住人列表
     */
    private List<OrderGuestDTO> guestList;

    /**
     * 每间房入住人信息
     */
    private List<OrderRoomGuestDTO> roomGuestList;

    /**
     * 特殊要求
     */
    private String specialRequest;

    /**
     * 确认号
     */
    private String confirmationCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 分销商编码
     */
    private String agentCode;

    /**
     * 分销商名称
     */
    private String agentName;

    /**
     * 联系电话
     */
    private String contactTel;

    /**
     * 渠道订单号
     */
    private String channelOrderCode;

    /**
     * 订单金额
     */
    private BigDecimal orderAmt;

    /**
     * 订单金额（加了到店另付金额）
     */
    private BigDecimal orderFinallyAmt;

    /**
     * 订单金额(商家币种)
     */
    private BigDecimal orderOrgCurrencyAmt;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 退改费
     */
    private BigDecimal refundFee;

    /**
     * 售价币种
     */
    private Integer saleCurrency;

    /**
     * 底价币种
     */
    private Integer baseCurrency;

    /**
     * 售价币种名称
     */
    private String saleCurrencyCode;

    /**
     * 结算方式：0月结 1半月结 2周结 3单结 4日结
     */
    private Integer settlementType;

    /**
     * 结算状态：0未结算，1已结算
     */
    private Integer settlementStatus;

    /**
     * 供货单总金额
     */
    private BigDecimal supplyOrderTotalAmt;

    /**
     * 供货单总金额(商家币种)
     */
    private BigDecimal supplyOrderTotalOrgCurrencyAmt;

    /**
     * 供货单奖励
     */
    private BigDecimal rewardAmt;

    /**
     * 供货单奖励(商家币种)
     */
    private BigDecimal rewardOrgCurrencyAmt;

    /**
     * 供货单返佣
     */
    private BigDecimal rebateAmt;

    /**
     * 供货单返佣(商家币种)
     */
    private BigDecimal rebateOrgCurrencyAmt;

    /**
     * 利润
     */
    private BigDecimal profit;

    /**
     * 利润(商家币种)
     */
    private BigDecimal profitOrgCurrencyAmt;

    /**
     * 售价明细
     */
    private List<PriceResponseDTO> salePriceList;

    /**
     * 费用明细
     */
    private List<OrderFeeDTO> feeList;

    /**
     * 总税费
     */
    private TaxDTO totalTax;

    /**
     * 供货单列表
     */
    private List<SupplyOrderDTO> supplyOrderList;

    /**
     * 订单附件
     */
    private List<OrderAttachmentDTO> orderAttachmentList;

    /**
     * 是否手工单
     */
    private Integer isManualOrder;

    /**
     * 是否代下单
     */
    private Integer isSubstituted;

    /**
     * 已收金额
     */
    private BigDecimal receivedAmt;

    /**
     * 未收金额
     */
    private BigDecimal unreceivedAmt;

    /**
     * 收款未确认
     */
    private BigDecimal unconfirmedReceivedAmt;

    /**
     * 付款未确认
     */
    private BigDecimal unconfirmedPaidAmt;

    /**
     * 订单归属人
     */
    private String orderOwnerName;

    /**
     * 修改状态：0无，1取消中，2修改中
     */
    private Integer modificationRequestStatus;

    /**
     * 订单确认人
     */
    private String confirmer;

    /**
     * 订单确认时间
     */
    private String confirmTime;

    /**
     * 客人特殊要求
     */
    private Integer isShownOnSupplyOrder;

    /**
     * 财务锁单
     */
    private Integer financeLockStatus;

    /**
     * 订单锁
     */
    private Integer lockStatus;

    /**
     * 是否我的订单 0:不是 1:是的
     */
    private Integer isMyOrder;

    /**
     * 是否即时确认： 0是，1否
     */
    private Integer instantConfimationStatus;


    /**
     * 修改申请内容
     */
    private String modificationRequestContent;

    /**
     * 取消条款
     */
    private String cancellationTerm;

    /**
     * 其它条款
     */
    private String otherTerm;

    /**
     * 是否拒单
     */
    private Integer isRefused;

    /**
     * 锁单人
     */
    private String lockName;

    /**
     * 锁单人账号
     */
    private String lockUser;

    /**
     * 修改结果选择项： 1 同意。 0 同意 Or 拒绝 2 已处理
     */
    private Integer modificationResponseType;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 供应商类型
     */
    private Integer supplyType;

    /**
     * 床型要求
     */
    private String bedType;

    /**
     * 床型描述
     * 不涉及多语言
     * 2024-07-29 author:湫
     * qc 973 【天宫&B2B-EPS】产品的床型信息传递
     */
    private String bedInfoDesc;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 供应商产品id
     */
    private String spProductId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 订单入住明细
     */
    private List<OrderCheckInfoDTOS> orderCheckInfos;

    /**
     * 订单担保条款
     */
    private OrderRestrictDTO orderRestrictDTO;

    /**
     * 产品标签（0或者null：无标签  1：协议价格标签）
     */
    private Integer productLabel;

    /**
     * 产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    private Integer productLabelType;

    /**
     * 是否终点房1是0否
     */
    private Integer hourly;

    /**
     * 提示信息
     */
//    private String reminder;

    /**
     * 最早到店时间
     */
    private String earliestArriveTime;

    /**
     * 最晚到店时间
     */
    private String latestArriveTime;

    /**
     * 附加项费用
     */
    private BigDecimal additionalCharges;

    /**
     * 服务费
     */
    private BigDecimal serviceCharge;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付价格（客户币种金额）
     */
    private BigDecimal payAtHotelAgentCurrencyFee;

    /**
     * 到店另付费用币种
     */
    private Integer payAtHotelCurrency;

    /**
     * 到店另付费用币种
     */
    private String payAtHotelCurrencyCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 入住成人数
     */
    private Integer adultQty;

    /**
     * 入住儿童年龄
     */
    private String childrenAge;

    /**
     * 开票模式 1商家开票 2酒店前台开票
     */
    private Integer invoiceModel;

    /**
     * 是否含窗：0-无窗1-部分有窗2-有窗4-内窗5-天窗
     */
    private Integer windowDetail;

    /**
     * 输出给api的产品Id
     */
    private String conversionProductId;

    /**
     * 售价汇率
     */
    private BigDecimal saleRate;

    /**
     * 商家币种转客户币种汇率
     */
    private BigDecimal orgToAgentRate;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    private String latitude;

    private String longitude;

    private String timeZone;

    private BigDecimal baseRate;

    /**
     * 语言
     * zh_CN("zh_CN","zh-CN", "简体中文")
     * en_US("en_US","en-US","英语")
     */
    private String language;

    /**
     * 订单支付状态
     */
    private Integer payStatus;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 订单发票异常列表
     */
    private List<OrderInvoiceErrorDTO> orderInvoiceErrorList;

    /**
     * 出行类型，1-因公，2-因私 非必填
     */
    private Integer travelType;

    /**
     * 客户下属单位名称
     */
    private String sdistributorName;

    /**
     * 是否担保
     * 订单是否担保
     * 1 担保
     * 0 未担保
     * 默认为未担保
     */
    private Integer guaranteeFlag;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 全部床型
     */
    private String allBedType;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 价格礼包
     */
    private String giftPacks;

    /**
     * 剩余额度
     */
    private BigDecimal remainingBalance;

    /**
     * 结算模式：0挂账 1预存
     */
    private Integer settledType;

    /**
     * 提示说明
     */
    private List<TipsResp> tipsList;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    /**
     * 入住明细不符列表
     */
    private List<SupplyOrderAmtLogDTO> supplyOrderAmtLogDTOList;

    /**
     * 订单交通信息
     */
    private String orderTrafficJson;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;

    /**
     * 退改任务信息
     */
    private OrderChangeWordOrderTaskDTO changeWordOrderTaskDTO;

    /**
     * 协议订单结算信息
     */
    private List<ProtocolOrderSettleInfoDTO> protocolOrderSettleInfos;

    /**
     * 供货单取消异常页面提示
     */
    private List<SupplyOrderExceptionDTO> supplyOrderExceptionDTOList;

    /**
     * 订单状态推送异常描述
     */
    private String pushOrderStatusContent;

    /**
     * 订单状态推送异常状态 0 正常 1 异常
     */
    private Integer pushOrderStatus;

    /**
     * 订单创建时间
     */
    private String createTime;
}
