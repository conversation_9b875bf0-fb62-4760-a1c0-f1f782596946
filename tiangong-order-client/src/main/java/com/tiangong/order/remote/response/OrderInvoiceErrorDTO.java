package com.tiangong.order.remote.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 订单发票异常信息DTO
 */
@Data
public class OrderInvoiceErrorDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 异常类型
     */
    private Integer eType;
    
    /**
     * 异常描述
     */
    private String desc;
    
    /**
     * 订单发票ID
     */
    private Integer orderInvoiceId;
}
