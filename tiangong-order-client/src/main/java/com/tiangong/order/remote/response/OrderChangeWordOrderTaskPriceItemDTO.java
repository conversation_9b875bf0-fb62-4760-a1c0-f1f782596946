package com.tiangong.order.remote.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 退改工单任务明细表
 */
@Data
public class OrderChangeWordOrderTaskPriceItemDTO {

    /**
     * 明细id
     */
    private Integer itemId;

    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 退订费
     */
    private BigDecimal refundPrice;
}
