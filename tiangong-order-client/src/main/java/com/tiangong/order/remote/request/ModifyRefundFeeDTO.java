package com.tiangong.order.remote.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ModifyRefundFeeDTO implements Serializable {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 供货单ID
     */
    private Integer supplyOrderId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 退订费
     */
    private BigDecimal refundFee;

    /**
     * 订单归属人
     */
    private String orderOwnerName;

}
