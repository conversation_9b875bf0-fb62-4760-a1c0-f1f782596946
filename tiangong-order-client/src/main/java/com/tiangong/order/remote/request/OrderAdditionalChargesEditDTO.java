package com.tiangong.order.remote.request;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/18 21:13
 * @Description:
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderAdditionalChargesEditDTO implements Serializable {

    /**
     * id号
     */
    private Integer id;
    /**
     * 附加费类型
     * 0:其他
     */
    private Integer additionalChargesType;
    /**
     * 附加费名称
     */
    private String additionalChargesName;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 日期
     */
    private Date additionalChargesDate;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 附加费
     */
    private BigDecimal additionalCharges;

}
