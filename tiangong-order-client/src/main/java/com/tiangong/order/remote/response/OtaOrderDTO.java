package com.tiangong.order.remote.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OtaOrderDTO {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 是否终点房1是0否
     */
    private Integer hourly;
    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 确认号
     */
    private List<String> confirmNums;

    /**
     * 渠道订单号
     */
    private String channelOrderCode;

    /**
     * 入住时间
     */
    private String checkInDate;

    /**
     * 离店时间
     */
    private String checkOutDate;

    /**
     * 入住时间
     */
    private String checkInTime;

    /**
     * 离店时间
     */
    private String checkOutTime;

    /**
     * 最早到店时间
     */
    private String earliestArriveTime;

    /**
     * 最晚到店时间
     */
    private String latestArriveTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 取消状态
     * 1, "取消成功"
     * 0, "取消失败"
     * 2, "异步取消"
     */
    private Integer cancelStatus;

    /**
     * 修改状态：0无，1取消中，2修改中，3再次确认，4退款申请，5待确认异常，6取消异常
     */
    private Integer modificationStatus;

    /**
     * 取消原因
     */
    private String cancelledReason;

    /**
     * 拒绝取消原因
     */
    private String refuseCancelledReason;

    /**
     * 是否取消成功
     */
    private Integer cancelledStatus;

    /**
     * 取消条款
     */
    private String cancellationTerm;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型id
     */
    private Long roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 床型
     */
    private String bedType;

    /**
     * 房间数
     */
    private Integer roomQty;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 早餐数量
     */
    private Integer breakfastQty;


    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;


    /**
     * 转换后的产品id
     */
    private String conversionProductId;

    /**
     * 订单创建时间
     */
    private Date createdDate;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 售价币种
     */
    private Integer saleCurrency;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 入住人
     */
    private String guest;
}
