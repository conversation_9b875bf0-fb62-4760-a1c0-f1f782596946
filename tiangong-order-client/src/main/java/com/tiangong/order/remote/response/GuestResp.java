package com.tiangong.order.remote.response;

import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/11/13 10:46
 */
@Data
@SensitiveClass
public class GuestResp {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 入住人名称
     */
    private String name;

    /**
     * 手机号码
     */
    @EncryptField
    private String mobileNo;

    /**
     * 房间号
     */
    private String roomNumber;

    /**
     * 入住成人数
     */
    private Integer adultQty;

    /**
     * 入住儿童数
     */
    private Integer childrenQty;

    /**
     * 入住儿童年龄
     */
    private String childrenAge;
}
