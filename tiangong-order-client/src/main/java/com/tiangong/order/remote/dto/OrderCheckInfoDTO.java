package com.tiangong.order.remote.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单入住信息
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:26:35
 */
@Data
public class OrderCheckInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 订单编码
     */
    private String orderCode;

    private String coOrderCode;
    /**
     * 入住日期
     */
    private Date startDate;
    /**
     * 离店日期
     */
    private Date endDate;
    /**
     * 入住状态
     * @see com.tiangong.enums.CheckInstateEnum
     */
    private String checkInState;
    /**
     * 入住人名单，多个以逗号隔开
     */
    private String guestName;
    /**
     * 房间号
     */
    private String roomNumber;
    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 入住明细
     */
    private List<OrderCheckDetailDTO> checkInDetailList;
    /**
     * 币种
     */
    private Integer currency;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 房间附加费
     */
    private BigDecimal roomAdditionalCharges;
}