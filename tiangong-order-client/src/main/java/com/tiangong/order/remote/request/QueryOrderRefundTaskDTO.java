package com.tiangong.order.remote.request;

import com.tiangong.common.PageDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单退款任务
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryOrderRefundTaskDTO extends PageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 任务类型 0-在线退款通知 1-平安担保扣款通知
     */
    @NotNull(message = "EMPTY_PARAM_TASKTYPE")
    private Integer taskType;

    /**
     * 退款任务类型 0-人工 1-自动
     */
    private Integer refundTaskType;

    /**
     * 退款类型 0-普通退款（平安）1-罚金退款（平安） 3-退款
     */
    private Integer refundType;

    /**
     * 退款状态 0-新建 1-通知成功 2-已取消 3-退款中
     */
    private Integer refundState;

    /**
     * 客户单号
     */
    private String channelOrderCode;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 创建人
     */
    private String createdBy;

}
