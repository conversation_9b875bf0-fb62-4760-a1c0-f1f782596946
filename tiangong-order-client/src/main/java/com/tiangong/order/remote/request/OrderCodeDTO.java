package com.tiangong.order.remote.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class OrderCodeDTO implements Serializable {

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 合作商订单编码
     */
    private String coOrderCode;

    /**
     * 订单归属人
     */
    private String orderOwnerName;

    /**
     * 页面请求标识 1页面请求 0其他请求
     */
    private Integer pageFlag;

    /**
     * 操作人全名
     */
    private String operator;

    /**
     * 语言
     */
    private String language;

    /**
     * 排查问题
     */
    private String requestId;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 渠道编码
     */
    private String channelCode;
}
