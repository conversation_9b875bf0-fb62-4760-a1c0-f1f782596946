package com.tiangong.order.remote.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class OrderCheckDetailInfoDTO {

    /**
     * 供应商订单编码
     */
    private String supplyOrderCode;

    /**
     * 企业单号
     */
    private String supplierOrderCode;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否是同步操作
     */
    private Boolean isSync = false;

    /**
     * 入住明细
     */
    @Valid
    @Size(min = 1, message = "EMPTY_PARAM_CHECKDETAILS")
    private List<OrderCheckDetails> checkDetails;
}
