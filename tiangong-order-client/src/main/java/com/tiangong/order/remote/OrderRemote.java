package com.tiangong.order.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.order.request.OrderCheckInfoRequest;
import com.tiangong.order.remote.dto.AdditionalChargesDTO;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.OrderInvoiceDTO;
import com.tiangong.order.remote.response.OrderInvoiceWithCompanyDO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "tiangong-order-server")
public interface OrderRemote {

    /**
     * 上传附件
     */
    @PostMapping("/order/saveOrderAttachment")
    Response<Object> saveOrderAttachment(@RequestBody SaveOrderAttachmentDTO request);

    /**
     * 添加自动发单渠道及时间
     */
    @PostMapping("/order/addSupplierAutoChannel")
    Response<Object> addSupplierAutoChannel(@RequestBody AddSupplierAutoChannelDTO request);

    /**
     * 修改订单发票
     */
    @PostMapping("/order/modifyOrderInvoice")
    Response<OrderInvoiceDTO> modifyOrderInvoice(@RequestBody OrderInvoiceDTO orderInvoiceDTO);

    /**
     * 申请订单发票
     *
     * @param orderInvoiceDTO
     * @return
     */
    @PostMapping("/order/applyOrderInvoice")
    Response<OrderInvoiceDTO> applyOrderInvoice(@RequestBody OrderInvoiceDTO orderInvoiceDTO);

    /**
     * 查询入住明细详情
     */
    @PostMapping("/order/queryOrderCheckDetailInfoDhub")
    Response<List<OrderCheckInfoDTO>> queryOrderCheckDetailInfoDhub(@RequestBody OrderCheckInfoRequest orderInvoiceDTO);

    /**
     * 删除订单附件(逻辑删除)
     */
    @RequestMapping(value = "/order/deleteOrderAttachmentByLogic")
    Response<Object> deleteOrderAttachmentByLogic(@RequestBody DeleteOrderAttachmentByLogicDTO deleteOrderAttachmentByLogicDTO);

    /**
     * 根据订单号查找订单的基本信息
     */
    @PostMapping("/order/queryOrderBasicInfoByOrderCode")
    Response<OrderDTO> queryOrderBasicInfoByOrderCode(@RequestParam String orderCode);

    /**
     * 新增订单支付明细
     */
    @PostMapping("/order/addOrderPayDetailList")
    Response<Object> addOrderPayDetailList(@RequestBody AddOrderPayDTO addOrderPayDTO);

    /**
     * 修改附加费
     */
    @PostMapping("/order/modifyAdditionalCharges")
    Response<Object> modifyAdditionalCharges(@RequestBody List<AdditionalChargesDTO> dtoList);

    /**
     * 查询符合自动开票条件的订单发票列表
     *
     * @param req 查询条件
     * @return 符合条件的订单发票列表（包含商家编码）
     */
    @PostMapping("/order/queryAutoInvoiceList")
    Response<List<OrderInvoiceWithCompanyDO>> queryAutoInvoiceList(@RequestBody QueryAutoInvoiceListDTO req);

    /**
     * 保存订单日志
     *
     * @param req 订单日志信息
     * @return 操作结果
     */
    @PostMapping("/order/saveOrderLog")
    Response<Object> saveOrderLog(@RequestBody List<SaveOrderLogDTO> req);
}
