package com.tiangong.order.remote.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SaveSupplyResultDTO implements Serializable {

    /**
     * 供货单id
     */
    private Integer supplyOrderId;

    /**
     * 确认状态：0未确认，1确认，2已取消，3已完成
     * @see com.tiangong.order.enums.ConfirmationStatusEnum
     */
    private Integer confirmationStatus;

    /**
     * 供应商确认人
     */
    private String supplierConfirmer;

    /**
     * 确认号
     */
    private String confirmationCode;

    /**
     * 拒绝原因
     */
    private String refusedReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 退改费
     */
    private BigDecimal refundFee;

    /**
     * 供应商返回的退改费（方便计算订单退订费）
     */
    private BigDecimal supplyRefundFee;

    /**
     * 退改费币种
     */
    private Integer refundFeeCurrency;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 订单归属人
     */
    private String orderOwnerName;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 供应商订单号
     */
    private String supplierOrderCode;

    /**
     * 任务标识（处理供货单状态）
     */
    private int isTask;

    /**
     * 供应奖励
     */
    private BigDecimal supplyReward;

    /**
     * 供应商返佣
     */
    private BigDecimal supplyShouldRackBack;

    /**
     * 手动标识
     */
    private Integer manualFlag;
}
