package com.tiangong.order.remote.response;

import com.tiangong.dto.order.response.OrderFeeDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplyOrderDTO implements Serializable {

    /**
     * 供货单id
     */
    private Integer supplyOrderId;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 发单状态：0未发单，1已发预订单，2已重发预订单，3已发修改单，4已发取消单
     * @see com.tiangong.order.enums.SendingStatusEnum
     */
    private Integer sendingStatus;

    /**
     * 确认状态：0未确认，1确认，2已取消，3已完成
     * @see com.tiangong.order.enums.ConfirmationStatusEnum
     */
    private Integer confirmationStatus;

    /**
     * 酒店id
     */
    private Integer hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 入住日期
     */
    private String startDate;

    /**
     * 离店日期
     */
    private String endDate;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供货单金额
     */
    private BigDecimal supplyOrderAmt;

    /**
     * 供货单金额
     */
    private BigDecimal supplyOrderOrgCurrencyAmt;

    /**
     * 底价
     */
    private BigDecimal basePrice;


    /**
     * 佣金
     */
    private BigDecimal commission;


    /**
     * 退改费
     */
    private BigDecimal refundFee;

    /**
     * 底价币种
     */
    private Integer baseCurrency;

    /**
     * 底价列表
     */
    private List<PriceResponseDTO> basePriceList;

    /**
     * 费用明细
     */
    private List<OrderFeeDTO> feeList;

    /**
     * 结算方式：0月结 1半月结 2周结 3单结 4日结
     */
    private Integer settlementType;

    /**
     * 结算状态：0未结算，1已结算
     */
    private Integer settlementStatus;

    /**
     * 产品列表
     */
    private List<SupplyProductDTO> productList;

    /**
     * 已付金额
     */
    private BigDecimal paidAmt;

    /**
     * 未付金额
     */
    private BigDecimal unpaidAmt;

    /**
     * 收款未确认金额
     */
    private BigDecimal unconfirmedReceivedAmt;

    /**
     * 付款未确认金额
     */
    private BigDecimal unconfirmedPaidAmt;

    /**
     * 供应商订单号
     */
    private String supplierOrderCode;

    /**
     * 财务锁单
     */
    private Integer financeLockStatus;

    /**
     * 取消条款
     */
    private String cancellationTerm;


    /**
     * 其它条款
     */
    private String otherTerm;

    /**
     * 附加项费用
     */
    private BigDecimal additionalCharges;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种
     */
    private Integer payAtHotelCurrency;

    /**
     * 到店另付币种
     */
    private String payAtHotelCurrencyCode;

    /**
     * 供应商转本币汇率
     */
    private BigDecimal baseRate;

    /**
     * 供应商转分销商汇率
     */
    private BigDecimal supplierToAgentRate;

    /**
     * 奖励财务对象
     */
    private SupplyOrderRewardDTO supplyOrderRewardDTO;

    /**
     * 返佣财务对象
     */
    private SupplyOrderRebateDTO supplyOrderRebateDTO;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmt;

    /**
     * 返佣金额
     */
    private BigDecimal rebateAmt;

    /**
     * 是否自助结算订单
     */
    private Integer isSettle;

    /**
     * 付款时间
     */
    private String settleDt;

    /**
     * 是否异常取消供货单 0否 1是
     */
    private Integer eCancelSupplyOrder;

    /**
     * 异常取消供货单描述
     */
    private String eCancelSupplyOrderContext;

    /**
     * 房间号（多个用逗号隔开）
     */
    private String roomNumbers;

    /**
     * 房间号列表
     */
    private List<String> roomNumberList;

    /**
     * 同步入住明细是否异常：0否 1是
     */
    private Integer isCheckInDetailAbnormal;

    /**
     * 供应商确认号码
     */
    private String confirmationCode;

    /**
     * 发单方式（1:直连   2:电邮   3:线下）
     */
    private Integer orderMethod;

    /**
     * 是否带附件
     */
    private Integer isAttachment;

    /**
     * 邮箱（发单方式：电邮）
     */
    private String preorderEmail;

    /**
     * vcc发送状态(0:未发送 1:已发送 2:发送失败)
     */
    private Integer vccSendStatus;

    /**
     * 采购类型：0自签协议房，1自签包房，2手工录入
     * @see com.tiangong.enums.PurchaseTypeEnum
     */
    private Integer purchaseType;

    /**
     * 入住人列表
     */
    private List<SupplyGuestDTO> guestList;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 订单币种
     */
    private Integer saleCurrency;

    /**
     * 订单币种CODE
     */
    private String orderCurrencyCode;

    /**
     * 客户单号
     */
    private String channelOrderCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 床型
     */
    private String bedType;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmt;

    /**
     * 订单下单日期
     */
    private String orderCreatedDt;

    /**
     * 最迟免费取消日
     */
    private String latestCancelDate;

    /**
     * 供应商币种CODE
     */
    private String supplyCurrencyCode;

    /**
     * 供货单标签详情
     */
    private List<SupplyOrderLabelDetails> supplyOrderLabelDetails;
}
