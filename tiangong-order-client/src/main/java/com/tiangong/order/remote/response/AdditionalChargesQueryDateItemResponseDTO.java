package com.tiangong.order.remote.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/21 11:43
 * @Description:
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AdditionalChargesQueryDateItemResponseDTO implements Serializable {

    /**
     * 订单类型:
     * 0:订单
     * 1:供货单
     */
    private Integer additionalChargesOrderType;

    /**
     * id号
     */
    private Integer id;
    /**
     * 附加费类型
     * 0:其他
     */
    private Integer additionalChargesType;
    /**
     * 附加费名称
     */
    private String additionalChargesName;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 日期
     */
    private Date additionalChargesDate;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 附加费
     */
    private BigDecimal additionalCharges;

}
