package com.tiangong.order.remote.request;

import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.order.CancelRestriction;
import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.OrderRoomPriceDTO;
import com.tiangong.dto.order.request.OrderTrafficJson;
import com.tiangong.dto.order.request.PriceRequestDTO;
import com.tiangong.dto.order.response.OrderRoomDetailDto;
import com.tiangong.dto.product.PriceItem;
import com.tiangong.dto.product.response.BedInfoDto;
import com.tiangong.order.remote.dto.OrderRestrictDTO;
import com.tiangong.order.remote.response.OrderInvoiceDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class AddManualOrderDTO implements Serializable {

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 分销商编码
     */
    private String agentCode;

    /**
     * 合作商编码
     */
    private String partnerCode;

    /**
     * 分销商名称
     */
    private String agentName;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机
     */
    private String contactPhone;

    /**
     * 联系人手机区号
     */
    private String contactCountryCode;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 入住人
     */
    private List<OrderGuestDTO> guestList;

    /**
     * 入住人，按每间房传入
     */
    private List<OrderRoomGuestDTO> roomGuestList;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 支付方式
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 不落地产品供应商产品Id
     */
    private String spProductId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 早餐数量
     */
    private Integer breakfastQty;

    /**
     * 采购类型：0自签协议房，1自签包房，2手工录入
     * @see com.tiangong.enums.PurchaseTypeEnum
     */
    private Integer purchaseType;

    /**
     * 入住日期
     */
    private String startDate;

    /**
     * 小时房选择的时间段位
     */
    private String hourlyTime;

    /**
     * 离店日期
     */
    private String endDate;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 订单金额
     */
    private BigDecimal orderAmt;

    /**
     * 采购金额
     */
    private BigDecimal supplyOrderAmt;

    /**
     * 售价币种
     */
    private Integer saleCurrency;

    /**
     * 底价币种
     */
    private Integer baseCurrency;

    /**
     * 商家编码
     */
    private String companyCode = CompanyDTO.COMPANY_CODE;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 订单归属人
     */
    private String orderOwnerName;

    /**
     * 渠道订单号
     */
    private String channelOrderCode;

    /**
     * 价格列表
     */
    private List<PriceRequestDTO> priceList;

    /**
     * 房间价格列表
     */
    private List<OrderRoomPriceDTO> orderRoomPriceList;

    /**
     * 是否即时确认： 0是，1否
     */
    private Integer instantConfirmationStatus;

    /**
     * 特殊要求
     */
    private String specialRequest;

    /**
     * 床型要求
     */
    private String bedTypeRequirement;

    /**
     * 入住成人数
     */
    private Integer adultQty;

    /**
     * 入住儿童数
     */
    private Integer childrenQty;

    /**
     * 入住儿童年龄
     */
    private String childrenAge;

    /**
     * 取消条款类型（0一经预订不能取消 1可以取消）
     */
    private Integer cancellationType;

    /**
     * 最迟取消时间
     */
    private String cancelAdvanceTime;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount = BigDecimal.ZERO;

    /**
     * 发票信息
     */
    private OrderInvoiceDTO orderInvoiceDTO;

    /**
     * 是否担保
     * 订单是否担保
     * 1 担保
     * 0 未担保
     * 默认为未担保
     */
    private Integer guaranteeFlag;

    /**
     * 订单担保条款
     */
    private OrderRestrictDTO orderRestrictDTO;

    /**
     * 转换后的产品id
     */
    private String conversionProductId;

    /**
     * 供应商单号
     */
    private String supplierOrderCode;

    /**
     * 床型明细
     */
    private List<BedInfoDto> bedInfos;

    /**
     * 床型描述
     * 不涉及多语言
     * 2024-07-29 author:湫
     * qc 973 【天宫&B2B-EPS】产品的床型信息传递
     */
    private String bedInfoDesc;

    /**
     * 下单类型（1、帮客户下单）
     */
    private String orderType;

    /**
     * 到店时间
     */
    private String arrivalTime;

    /**
     * 协议产品
     */
    private Integer productLabel;

    /**
     * 是否需要填写身份证	Integer	1是0否 空代表否
     */
    private int hasIdCard;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 语言
     * zh_CN("zh_CN","zh-CN", "简体中文")
     * en_US("en_US","en-US","英语")
     */
    private String language;

    private String supplyType;

    /**
     * 下单类型1补单 2帮客户下单
     */
    private Integer isSubstituted;

    /**
     * 下单人
     */
    private String userAccount;

    /**
     * 渠道名称
     */
    private String channelName;


    /**
     * 订单支付状态
     */
    private Integer payStatus;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 出行类型，1-因公，2-因私 非必填
     */
    private Integer travelType;

    /**
     * 客户下属单位名称
     */
    private String sdistributorName;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 全部床型
     */
    private String allBedType;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 价格礼包
     */
    private String giftPacks;
    /**
     * 入住人数
     */
    private Integer guestQuantity;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 总价格
     */
    private BigDecimal totalSalePrice;
    /**
     * 每日售卖信息
     */
    private List<PriceItem> priceItems;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    //交通方式
    private OrderTrafficJson orderTrafficJson;

    /**
     * 排查问题
     */
    private String requestId;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 服务费
     */
    private BigDecimal serviceCharge;

    /**
     * 阶段取消条款
     */
    private List<CancelRestriction> cancelRestrictions;

    /**
     * 按间价格明细
     */
    private List<OrderRoomDetailDto> roomItemDetails;

    /**
     * 产品币种
     */
    private Integer productCurrency;
}
