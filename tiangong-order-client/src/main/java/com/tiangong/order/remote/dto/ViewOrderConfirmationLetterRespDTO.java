package com.tiangong.order.remote.dto;

import lombok.Data;

/**
 * @program: glink_tiangong
 * @ClassName ViewOrderConfirmationLetterRespDTO
 * @description:
 * @author: 湫
 * @create: 2025/04/16/ 14:48
 * @Version 1.0
 **/

@Data
public class ViewOrderConfirmationLetterRespDTO {

    private Integer orderId;

    private String orderCode;

    private String agentCode;

    /**
     * 确认函语言
     */
    private String language;

    /**
     * 价格显示
     */
    private Integer priceShow;

    /**
     * 接收确认函邮箱
     */
    private String email;

    /**
     * 是否携带附件
     */
    private Integer annexCarry;
}
