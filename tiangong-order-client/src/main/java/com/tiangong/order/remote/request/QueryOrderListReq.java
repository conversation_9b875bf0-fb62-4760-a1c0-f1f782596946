package com.tiangong.order.remote.request;

import com.tiangong.common.PageDto;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/11/10 18:25
 */
@Data
public class QueryOrderListReq extends PageDto {

    /**
     * 订单列表类型 1全部订单 2我下的订单
     */
    @NotNull(message = "EMPTY_PARAM_ORDERTYPELIST")
    private Integer orderTypeList;

    /**
     * 分销商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 入住人
     */
    private String contactName;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 下单人
     */
    private String userName;

    /**
     * 下单开始时间
     */
    private String startDate;

    /**
     * 下单结束时间
     */
    private String endDate;

    /**
     * 订单状态 0待确认，1已确认，2已取消, 3已完成， 不传或者空为全部
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer orderConfirmationStatus;
}
