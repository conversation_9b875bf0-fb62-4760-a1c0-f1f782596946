package com.tiangong.order.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.order.CreditLineOrderDTO;
import com.tiangong.order.remote.dto.PreBookDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.OtaOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "tiangong-order-server")
public interface BookingRemote {

    /**
     * 渠道下单
     */
    @PostMapping("/order/addOTAOrder")
    Response<String> addOTAOrder(@RequestBody AddManualOrderDTO request);

    /**
     * 渠道试预定
     */
    @PostMapping("/order/checkPreBook")
    Response<Object> checkPreBook(@RequestBody PreBookDTO prebookDTO);

    /**
     * 取消ota订单
     */
    @PostMapping("/order/cancelOtaOrder")
    Response<OtaOrderDTO> cancelOtaOrder(@RequestBody CancelOtaOrderDTO cancelOtaOrderDTO);

    /**
     * 修改订单状态以及扣除额度
     */
    @PostMapping("/order/updatePayStatusAndDeductRefundCreditLineByOrderCode")
    Response<CreditLineOrderDTO> updatePayStatusAndDeductRefundCreditLineByOrderCode(@RequestBody UpdatePayStatusAndDeductRefundCreditLineDTO request);

}
