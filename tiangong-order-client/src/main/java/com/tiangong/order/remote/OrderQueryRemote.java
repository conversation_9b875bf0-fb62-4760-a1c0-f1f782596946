package com.tiangong.order.remote;

import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.QueryOrderStatisticsDTOB2BRequest;
import com.tiangong.dto.order.response.OrderStatisticsDTOB2BResponse;
import com.tiangong.order.remote.dto.FuzzySupplierDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(value = "tiangong-order-server")
public interface OrderQueryRemote {

    /**
     * 查询订单列表
     */
    @PostMapping("/order/queryOrderList")
    Response<PaginationSupportDTO<OrderSimpleDTO>> queryOrderList(@RequestBody QueryOrderListDTO request);

    /**
     * 查询订单列表统计
     */
    @PostMapping("/order/queryOrderStatistics")
    Response<OrderStatisticsDTO> queryOrderStatistics(@RequestBody QueryOrderStatisticsDTO request);

    /**
     * 查询订单列表统计-B2B
     */
    @PostMapping("/order/queryOrderStatisticsB2B")
    Response<OrderStatisticsDTOB2BResponse> queryOrderStatisticsB2B(@RequestBody QueryOrderStatisticsDTOB2BRequest request);

    /**
     * 查询订单详情
     */
    @PostMapping("/order/queryOrderDetail")
    Response<OrderDTO> queryOrderDetail(@RequestBody OrderCodeDTO request);

    /**
     * 查询订单备注
     */
    @PostMapping("/order/queryOrderRemark")
    Response<List<OrderRemarkDTO>> queryOrderRemark(@RequestBody QueryOrderRemarkDTO request);

    /**
     * 查询订单日志
     */
    @PostMapping("/order/queryOrderLog")
    Response<List<OrderLogDTO>> queryOrderLog(@RequestBody OrderIdDTO request);

    /**
     * 查询订单申请
     */
    @PostMapping("/order/queryOrderRequest")
    Response<List<OrderRequestDTO>> queryOrderRequest(@RequestBody OrderIdDTO request);

    /**
     * 查询订单确认信息
     */
    @PostMapping("/order/queryConfirmOrderInfo")
    Response<String> queryConfirmOrderInfo(@RequestBody QueryConfirmOrderInfoDTO request);

    /**
     * 查询订单价格明细
     */
    @PostMapping("/order/queryOrderPriceItem")
    Response<List<PriceResponseDTO>> queryOrderPriceItem(@RequestBody OrderIdDTO request);

    /**
     * 供货单预览
     */
    @PostMapping("/order/previewSupplyOrder")
    Response<SupplyOrderPreviewDTO> previewSupplyOrder(@RequestBody SupplyOrderIdDTO request);

    /**
     * 查询供货单结果
     */
    @PostMapping("/order/querySupplyOrderResult")
    Response<SupplyResultDTO> querySupplyOrderResult(@RequestBody SupplyOrderIdDTO request);

    /**
     * 查询供货单产品详情
     */
    @PostMapping("/order/querySupplyProduct")
    Response<SupplyProductDetailDTO> querySupplyProduct(@RequestBody SupplyProductIdDTO request);

    /**
     * 查询供货单的供应商
     */
    @PostMapping("/order/querySupplier")
    Response<List<FuzzySupplierDTO>> querySupplier(@RequestBody Map<String, String> requestMap);

    /**
     * 查询订单发票
     */
    @PostMapping("/order/queryOrderInvoice")
    Response<OrderInvoiceDTO> queryOrderInvoice(@RequestBody QueryOrderInvoiceDTO queryOrderInvoiceDTO);

    /**
     * 根据订单编号查询订单发票信息
     *
     * @param orderCode
     * @return
     */
    @PostMapping("/order/queryOrderInvoiceByOrderCode")
    Response<OrderInvoiceDTO> queryOrderInvoiceByOrderCode(@RequestParam String orderCode);

    /**
     * 查询订单退款任务
     */
    @PostMapping("/order/queryOrderRefundTaskPage")
    Response<PaginationSupportDTO<OrderRefundTaskDetailDTO>> queryOrderRefundTaskPage(@RequestBody QueryOrderRefundTaskDTO queryOrderRefundTaskDTO);

    /**
     * （对外接口）根据订单id查询订单信息
     */
    @PostMapping("/order/queryContactOrder")
    Response<OrderDTO> queryContactOrder(@RequestParam Integer orderId);

    /**
     * 根据订单编号查询订单详情
     */
    @PostMapping("/order/queryOrderDetailByOrderCode")
    Response<OrderDTO> queryOrderDetailByOrderCode(@RequestParam String orderCode);
}
