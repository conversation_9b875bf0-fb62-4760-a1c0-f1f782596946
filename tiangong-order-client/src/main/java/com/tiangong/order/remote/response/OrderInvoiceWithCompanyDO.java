package com.tiangong.order.remote.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tiangong.annotations.Compared;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 订单发票信息（包含商家编码）
 *
 * <AUTHOR>
 * @date 2024/05/13
 */
@Data
public class OrderInvoiceWithCompanyDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 订单编码
     */
    @Compared(variableName = "订单编码")
    private String orderCode;

    /**
     * 开票状态(0 待开票, 1 已开票 2开票中 3开票成功待下载 3开票失败 4下载附件失败 5下载成功待创建票单 6创建票单失败 7发票绑定失败)
     */
    private Integer invoiceStatus;

    /**
     * 开票时间
     */
    private String invoiceDate;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 税额
     */
    private BigDecimal taxAmt;

    /**
     * 发票类型(0 普通发票, 1 专用发票)
     */
    private Integer invoiceType;

    /**
     * 发票名称
     */
    private String invoiceName;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 公司电话
     */
    private String companyPhone;

    /**
     * 注册地址
     */
    private String registerAddr;

    /**
     * 银行账号
     */
    private String accountNo;

    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 申请日期
     */
    private String applyDate;

    /**
     * 取票方式(0 电子发票邮箱，1 邮寄, 2 自取)
     */
    private Integer ticketType;

    /**
     * 发送方式(0 未发送, 1 已发送 2发送失败)
     */
    private Integer sendStatus;

    /**
     * 收件人
     */
    private String receivedName;

    /**
     * 收件人电话
     */
    private String receivedPhone;

    /**
     * 收件人地址
     */
    private String receivedAddr;

    /**
     * 快递单号
     */
    private String trackingNo;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 发票抬头类型
     */
    private Integer invoiceTitleType;

    /**
     * 发票url 多个逗号隔开
     */
    private String invoiceUrls;
    /**
     * 票单/发票号
     */
    private String invoiceNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 开票申请单号id
     */
    private String applyId;
    /**
     * 序号
     */
    private String sortNum;
    /**
     * 规格
     */
    private String specs;
    /**
     * 单位
     */
    private String perUnit;
    /**
     * 数量
     */
    private String countNum;
    /**
     * 税率
     */
    private String invoiceTaxRate;

//    /**
//     * 客户未配置电子发票推送地址异常状态 0非 1是 默认0
//     */
//    private Integer ePushAddressConfigStatus;
//
//    /**
//     * 客户未配置电子发票推送地址异常内容
//     */
//    private String ePushAddressConfigMessage;
    /**
     * 创建开票异常状态 0非 1是 默认0
     */
    private Integer eCreateBillStatus;

    /**
     * 创建开票异常内容
     */
    private String eCreateBillMessage;
    /**
     * 客户配置地址错误状态 0非 1是 默认0
     */
    private Integer ePushAddressInvalidStatus;

    /**
     * 客户配置地址错误内容
     */
    private String ePushAddressInvalidMessage;

    /**
     * 手工推送发票失败 0 非 1是 默认0
     */
    private Integer eInvoiceFileMissingStatus;

    /**
     * 手工推送发票失败内容
     */
    private String eInvoiceFileMissingMessage;

    /**
     * 申请开票失败异常状态0非 1是 默认0
     */
    private Integer eInvoiceApplyFailedStatus;

    /**
     * 申请开票失败异常内容
     */
    private String eInvoiceApplyFailedMessage;

    /**
     * 下载开票文件失败异常状态0非 1是 默认0
     */
    private Integer eFileDownloadFailedStatus;

    /**
     * 下载开票文件失败异常内容
     */
    private String eFileDownloadFailedMessage;

    /**
     * 查询全版接口调用状态 0非 1是 默认1
     */
    private Integer fileUrlStatus;

    /**
     * 自动开票接口调用状态 0非 1是 默认 1
     */
    private Integer autoOpenInvoiceStatus;

    /**
     * 发票文件名
     */
    private String invoiceFileName;

    /**
     * 发票发送客户邮箱失败异常状态 0 非 1是
     */
    private Integer eEmailSendFailedStatus;

    /**
     * 发票发送客户邮箱失败内容
     */
    private String eEmailSendFailedMessage;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 销售方税号
     */
    private String saleTaxNo;
    /**
     * 销售方机构名称
     */
    private String saleTaxOrgName;

    /**
     * 商家编码
     */
    private String companyCode;
    /**
     * 分销商编码
     */
    private String agentCode;
    /**
     * 分销商名称
     */
    private String agentName;
    /**
     * 订单确认状态
     */
    private Integer confirmationStatus;
    /**
     * 离店时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;
    /**
     * 售价币种
     */
    private Integer saleCurrency;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 订单金额
     */
    private BigDecimal orderAmt;
}
