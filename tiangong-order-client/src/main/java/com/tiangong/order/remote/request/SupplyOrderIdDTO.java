package com.tiangong.order.remote.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
public class SupplyOrderIdDTO implements Serializable {

    /**
     * 供货单id
     */
    private Integer supplyOrderId;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 供货单编码集合
     */
    private Set<String> supplyOrderCodeList;

    /**
     * 语言
     */
    private String languageType;

    /**
     * 供应商编码
     */
    private String supplyCode;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 支付类型
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;
}
