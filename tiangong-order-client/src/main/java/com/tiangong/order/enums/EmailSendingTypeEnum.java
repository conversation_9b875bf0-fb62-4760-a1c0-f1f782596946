package com.tiangong.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供货单邮件发送类型
 */
@Getter
@AllArgsConstructor
public enum EmailSendingTypeEnum {

    BOOKING_ORDER(0, "预定单"),
    CONFIRMATION(2, "确认单"),
    CANCELLATION(3, "取消单");

    public final int key;
    public final String value;

    public static String getValueByKey(int key) {
        String value = null;
        for (EmailSendingTypeEnum statusEnum : EmailSendingTypeEnum.values()) {
            if (key == statusEnum.key) {
                value = statusEnum.value;
                break;
            }
        }
        return value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for (EmailSendingTypeEnum statusEnum : EmailSendingTypeEnum.values()) {
            if (statusEnum.value.equals(value)) {
                key = statusEnum.key;
                break;
            }
        }
        return key;
    }

    public static EmailSendingTypeEnum getEnumByKey(int key) {
        EmailSendingTypeEnum returnEnum = null;
        for (EmailSendingTypeEnum statusEnum : EmailSendingTypeEnum.values()) {
            if (key == statusEnum.key) {
                returnEnum = statusEnum;
                break;
            }
        }
        return returnEnum;
    }
}
