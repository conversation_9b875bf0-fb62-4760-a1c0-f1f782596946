package com.tiangong.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 百望云错误码枚举
 *
 * <AUTHOR>
 * @date 2023/5/7
 */
@Getter
@AllArgsConstructor
public enum BaiWangErrorCodeEnum {

    /**
     * 缺少参数
     */
    MISSING_PARAMETER("100000", "缺少参数，请检测输入参数的正确性"),

    /**
     * 参数取值范围错误
     */
    PARAMETER_RANGE_ERROR("100001", "参数取值范围错误，检查参数的值"),

    /**
     * token失效或者无效
     */
    TOKEN_INVALID("100002", "token失效或者无效，重新登录获取token"),

    /**
     * appKey错误或无访问权限
     */
    APP_KEY_ERROR("100003", "appKey错误或无访问权限，联系运营"),

    /**
     * 请求过于频繁
     */
    REQUEST_TOO_FREQUENT("100005", "请求过于频繁，默认一个app最大并发为10，请控制并发或者联系运营"),

    /**
     * 远程访问错误
     */
    REMOTE_ACCESS_ERROR("100006", "远程访问错误，业务错误，一般是业务参数写错了，请检查"),

    /**
     * 无效的签名
     */
    INVALID_SIGNATURE("100007", "无效的签名，注意sign参数的正确性"),

    /**
     * 系统内部超时
     */
    SYSTEM_INTERNAL_TIMEOUT("100008", "系统内部超时，请稍后重试"),

    /**
     * 超过系统最大容流量
     */
    SYSTEM_MAX_CAPACITY("100009", "超过系统最大容流量，请控制并发或者过段时间重试"),

    /**
     * 请求超时
     */
    REQUEST_TIMEOUT("100010", "请求超时，请稍后重试"),

    /**
     * 不支持GET方法
     */
    GET_METHOD_NOT_SUPPORTED("100011", "不支持GET方法，请使用POST方法"),

    /**
     * 非法请求
     */
    ILLEGAL_REQUEST("100021", "非法请求，请发送合法请求"),

    /**
     * 服务升级中
     */
    SERVICE_UPGRADING("100022", "服务升级中，请稍后再试"),

    /**
     * 该appkey已被冻结
     */
    APP_KEY_FROZEN("100403", "该appkey已被冻结，联系运营人员"),

    /**
     * 请求报文超长
     */
    REQUEST_MESSAGE_TOO_LONG("100413", "请求报文超长，请求报文缩减（目前服务端设置报文大小限制为100M）"),

    /**
     * 未知错误
     */
    UNKNOWN_ERROR("60", "未知错误，具体分析");

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述和处理建议，用逗号拼接
     */
    private final String errorDesc;

    /**
     * 根据错误码获取枚举
     *
     * @param code 错误码
     * @return 对应的枚举，如果没有找到则返回null
     */
    public static BaiWangErrorCodeEnum getByCode(String code) {
        for (BaiWangErrorCodeEnum errorCode : BaiWangErrorCodeEnum.values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return null;
    }
    /**
     * 根据错误码获取枚举
     *
     * @param code 错误码
     * @return 对应的枚举，如果没有找到则返回null
     */
    public static String getDescByCode(String code) {
        for (BaiWangErrorCodeEnum errorCode : BaiWangErrorCodeEnum.values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode.getErrorDesc();
            }
        }
        return null;
    }
}
