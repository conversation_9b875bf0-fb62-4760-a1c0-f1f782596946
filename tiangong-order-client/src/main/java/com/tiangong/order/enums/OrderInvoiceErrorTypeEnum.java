package com.tiangong.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单发票异常类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderInvoiceErrorTypeEnum {
    
    /**
     * 客户未配置电子发票推送地址
     */
    PUSH_ADDRESS_CONFIG_ERROR(1, "客户未配置电子发票推送地址"),
    
    /**
     * 客户配置地址错误
     */
    PUSH_ADDRESS_INVALID_ERROR(2, "客户配置地址错误"),
    
    /**
     * 手工推送发票失败
     */
    INVOICE_FILE_MISSING_ERROR(3, "手工推送发票失败"),
    
    /**
     * 申请开票失败
     */
    INVOICE_APPLY_FAILED_ERROR(4, "申请开票失败"),
    
    /**
     * 下载开票文件失败
     */
    FILE_DOWNLOAD_FAILED_ERROR(5, "下载开票文件失败"),
    
    /**
     * 发票发送客户邮箱失败
     */
    EMAIL_SEND_FAILED_ERROR(6, "发票发送客户邮箱失败"),
    /**
     * 创建开票失败
     */
    CREATE_BILL_ERROR(7, "创建开票失败");

    public final Integer code;
    public final String desc;
    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static OrderInvoiceErrorTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderInvoiceErrorTypeEnum typeEnum : OrderInvoiceErrorTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
