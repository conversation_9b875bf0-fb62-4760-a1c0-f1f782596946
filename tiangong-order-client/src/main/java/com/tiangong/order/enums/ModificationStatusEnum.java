package com.tiangong.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 修改状态枚举
 * 0无，1取消中，2修改中，3再次确认，4退款申请，5待确认异常，6取消异常
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModificationStatusEnum {

    MODIFICATION_STATUS_0(0, "无"),
    MODIFICATION_STATUS_1(1, "取消申请"),
    MODIFICATION_STATUS_2(2, "修改申请"),
    MODIFICATION_STATUS_3(3, "再次确认"),
    MODIFICATION_STATUS_4(4, "退款申请"),
    MODIFICATION_STATUS_5(5, "待确认异常"),
    MODIFICATION_STATUS_6(6, "取消异常");

    public final int key;
    public final String value;

    public static String getValueByKey(int key) {
        String value = null;
        for (ModificationStatusEnum statusEnum : ModificationStatusEnum.values()) {
            if (key == statusEnum.key) {
                value = statusEnum.value;
                break;
            }
        }
        return value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for (ModificationStatusEnum statusEnum : ModificationStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                key = statusEnum.key;
                break;
            }
        }
        return key;
    }

    public static ModificationStatusEnum getEnumByKey(int key) {
        ModificationStatusEnum returnEnum = null;
        for (ModificationStatusEnum statusEnum : ModificationStatusEnum.values()) {
            if (key == statusEnum.key) {
                returnEnum = statusEnum;
                break;
            }
        }
        return returnEnum;
    }
}
