package com.tiangong.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供货单发送类型
 */
@Getter
@AllArgsConstructor
public enum SendingTypeEnum {

    EBK(1, "EBK"),
    EMAIL(2, "EMAIL"),
    FAX(3, "FAX"),
    <PERSON><PERSON><PERSON><PERSON>(4, "PHONE"),
    WECHAT(5, "WECHAT"),
    QQ(6, "QQ"),
    DIRECT_LINK(7, "直连");

    public final int key;
    public final String value;

    public static String getValueByKey(int key) {
        String value = null;
        for (SendingTypeEnum statusEnum : SendingTypeEnum.values()) {
            if (key == statusEnum.key) {
                value = statusEnum.value;
                break;
            }
        }
        return value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for (SendingTypeEnum statusEnum : SendingTypeEnum.values()) {
            if (statusEnum.value.equals(value)) {
                key = statusEnum.key;
                break;
            }
        }
        return key;
    }

    public static SendingTypeEnum getEnumByKey(int key) {
        SendingTypeEnum returnEnum = null;
        for (SendingTypeEnum statusEnum : SendingTypeEnum.values()) {
            if (key == statusEnum.key) {
                returnEnum = statusEnum;
                break;
            }
        }
        return returnEnum;
    }
}
