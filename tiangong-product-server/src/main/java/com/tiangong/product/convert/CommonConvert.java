package com.tiangong.product.convert;

import com.tiangong.dis.dto.ProductSaleStatusDTO;
import com.tiangong.dto.product.PriceItem;
import com.tiangong.dto.product.request.RoomGuestNumber;
import com.tiangong.dto.product.response.*;
import com.tiangong.product.domain.*;
import com.tiangong.product.domain.dto.*;
import com.tiangong.product.domain.entity.*;
import com.tiangong.product.domain.req.*;
import com.tiangong.product.domain.resp.ProOrgAvailableResp;
import com.tiangong.product.domain.resp.ProOrgIncreaseResp;
import com.tiangong.product.domain.resp.ProProductFilterStrategyResp;
import com.tiangong.product.domain.vo.*;
import com.tiangong.product.dto.*;
import com.tiangong.supply.direct.remote.dto.CancelRestriction;
import com.tiangong.supply.direct.remote.dto.TaxDetailDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import javax.validation.constraints.NotNull;
import java.util.List;

@Mapper
public interface CommonConvert {

    public static CommonConvert INSTANCE = Mappers.getMapper(CommonConvert.class);


    DhubPriceItemDto convert(DhubPriceDetailItemDto dhubPriceDetailItemDto);

    DhubPriceTaxDetail convert(TaxDetailDto taxDetailDto);

    com.tiangong.dto.product.TaxDetailDto convertTaxDetail(TaxDetailDto taxDetailDto);

    TaxDetailDto convertTaxDetail(com.tiangong.dto.product.TaxDetailDto taxDetailDto);

    PriceItem convert(PriceItem priceItem);

    List<DhubPriceItemDto> convert(List<DhubPriceDetailItemDto> priceItems);

    ImportProAgentAvailableExportReq importProAgentConvert(ImportProAgentAvailableReq importReq);

    ProOrgAvailableEntity proOrgAvailableConvert(ProOrgAvailableReq req);

    ProOrgAvailableResp proOrgConvert(ProOrgAvailableEntity entity);

    ProTaxRuleConfigEntity proTaxRuleConfigConvert(ProTaxRuleConfigAditReq req);

    List<ProTaxDetailEntity> proTaxDetailList(@NotNull(message = "税费规则明细不能为空") List<ProTaxDetailBase> proTaxDetails);

    ProTaxDetailBase reqProTaxDetail(ProTaxDetailEntity reqProTaxDetail);

    ProTaxRuleConfigEntity proTaxRuleConfigAditReqConvert(ProTaxRuleConfigAditReq req);

    ProductSalePriceDetailDTO productSalePriceDetailDTOConvert(ProductDTO productDTO);

    ProductSaleStatusDTO productSaleStatusDTOConvert(ProductSaleStatusPO t);

    ProductSaleIncreaseDTO productSaleIncreaseDTOConvert(ProductDayIncreasePO i);

    ProductForShowDTO productForShowDTOConvert(ProductTempDTO productTempDTO);

    com.tiangong.dto.product.TaxDetailDto taxDetailConvert(com.tiangong.dto.product.TaxDetailDto taxDetail);

    ProductPO productPOConvert(ProductDTO productDTO);

    ProductRestrictPO ProductRestrictPOConvert(ProductDTO productDTO);

    List<GuaranteePO> guaranteePOListConvert(List<GuaranteeDTO> guarantees);

    ProductPO productDTOConvert(ProductDTO productDTO);

    GuaranteePO guaranteePOConvert(GuaranteeDTO guarantee);

    ProductDTO productConvert(ProductDayQuotationDTO productDayQuotationDTO);

    List<ProTaxDetailDTO> proTaxDetailDTOConvert(List<ProTaxDetailBase> proTaxDetails);

    BatchQuotationDTO batchQuotationDTOConvert(BatchModifyHotelProductStatusDTO batchModifyHotelProductStatusDTO);

    ProductSaleItemDTO productSaleItemDTOConvert(ProductTempDTO productTempDTO);

    ProductDayQuotationDTO productDayQuotationDTOConvert(BatchQuotationDTO batchQuotationDTO);

    ProductDTO productRestrictDTOConvert(com.tiangong.dis.dto.ProductRestrictDTO productRestrictDTO);

    List<TipInfosDTO> tipInfosListConvert(List<TipInfosDTO> tips);

    List<DebitedQuotaDTO> debitedQuotaDTOConvert(List<DebitedQuotaPO> poList);


    ProProductFilterStrategyEntity proProductFilterStrategyConvert(ProProductFilterStrategyAddReq req);

    ProProductFilterStrategyEntity proProductFilterStrategyEditConvert(ProProductFilterStrategyEditReq req);

    ProProductFilterStrategyResp convert(ProProductFilterStrategyEntity bean);

    List<ProProductFilterStrategyResp> convertList(List<ProProductFilterStrategyEntity> bean);

    ProOrgIncreaseEntity proOrgIncreaseConvert(ProOrgIncreaseReq req);

    ProOrgIncreaseResp convert(ProOrgIncreaseEntity bean);

    List<ProOrgIncreaseResp> convertProOrgIncreaseResp(List<ProOrgIncreaseEntity> bean);

    List<com.tiangong.supply.direct.remote.request.RoomGuestNumber> convertRoomGuestNumber(List<RoomGuestNumber> roomGuestNumbers);

    List<com.tiangong.dto.order.CancelRestriction> convertCancelRestrictions(List<CancelRestriction> cancelRestrictions);

    /**
     * 供应商酒店黑白名单分页VO转DTO
     */
    ProOrgHotelAvailablePageDTO convertVoToDto(ProOrgHotelAvailablePageVO vo);

    /**
     * 供应商酒店黑白名单新增VO转DTO
     */
    ProOrgHotelAvailableAddDTO convertAddVoToDto(ProOrgHotelAvailableAddVO vo);

    /**
     * 供应商酒店黑白名单DTO转VO
     */
    ProOrgHotelAvailableRespVO convertDtoToVo(ProOrgHotelAvailableRespDTO dto);

    /**
     * 供应商酒店黑白名单DTO列表转VO列表
     */
    List<ProOrgHotelAvailableRespVO> convertDtoListToVoList(List<ProOrgHotelAvailableRespDTO> dtoList);

    ProOrgHotelAvailableEntity convertToPO(ProOrgHotelAvailableAddDTO dto);
    ProOrgHotelAvailableExcelErrorVO convert(ProOrgHotelAvailableExcelVO vo);

    ProOrgHotelAvailableBaseDTO convertToDTO(ProOrgHotelAvailableEntity e);

    BatchSaleItemDTO batchSaleItemDTOConvert(BatchQuotationDTO batchQuotationDTO);
}
