package com.tiangong.product.util;

import cn.hutool.core.util.IdUtil;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.product.SupplyHotelIncrease;
import com.tiangong.dto.product.SupplyIncrease;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.SaleAdjustmentTypeEnum;
import com.tiangong.enums.SupplyClassEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.product.dto.mongo.SupplyHotelLowestPrice;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询工具类
 */
@Slf4j
@Service("queryUtil")
public class QueryUtil {

    /**
     * 根据分销商编码查询供应商白名单
     */
    public List<String> getAgentSupplyAvailable(String agentCode) {
        String supplyCodes = (String) RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_KEY, agentCode);
        return Optional.ofNullable(supplyCodes).map(e -> e.split(",")).map(Arrays::asList).orElse(new LinkedList<>());
    }

    /**
     * 获取供应商配置信息
     */
    public Map<String, SupplierAddDTO> getSupplierConfig(List<String> supplyCodes) {
        Map<String, SupplierAddDTO> supplierMap = new HashMap<>();
        try {
            if (CollUtilX.isEmpty(supplyCodes)) {
                return supplierMap;
            }
            List<Object> supplierInfos = RedisTemplateX.hMultiGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplyCodes);
            if (CollUtilX.isEmpty(supplierInfos)) {
                return supplierMap;
            }
            for (Object supplierInfo : supplierInfos) {
                SupplierAddDTO supplierAddDTO = StrUtilX.parseObject(supplierInfo, SupplierAddDTO.class);
                if (supplierAddDTO == null || StrUtilX.isEmpty(supplierAddDTO.getSupplierCode())) {
                    continue;
                }
                supplierMap.put(supplierAddDTO.getSupplierCode(), supplierAddDTO);
            }
        } catch (Exception e) {
            log.error("getSupplierConfig error!", e);
        }
        return supplierMap;
    }

    /**
     * 根据agentCode+supplyCode查询供应商加幅
     */
    public Map<String, SupplyIncrease> getSupplyIncrease(String agentCode, List<String> supplyCodes) {
        Map<String, SupplyIncrease> supplyIncreaseMap = new HashMap<>();
        try {
            if (StrUtilX.isEmpty(agentCode) || CollUtilX.isEmpty(supplyCodes)) {
                return supplyIncreaseMap;
            }
            List<String> keys = supplyCodes.stream().map(item -> agentCode.concat(StrUtilX.SPLIT_CODE).concat(item)).collect(Collectors.toList());
            List<Object> supplyIncreases = RedisTemplateX.hMultiGet(RedisKey.AGENT_SUPPLY_INCREASE_KEY, keys);
            if (CollUtilX.isEmpty(supplyIncreases)) {
                return supplyIncreaseMap;
            }
            for (Object obj : supplyIncreases) {
                if (obj == null) {
                    continue;
                }
                SupplyIncrease supplyIncrease = StrUtilX.parseObject(obj, SupplyIncrease.class);
                if (supplyIncrease == null || StrUtilX.isEmpty(supplyIncrease.getSupplyCode())) {
                    continue;
                }
                supplyIncreaseMap.put(supplyIncrease.getSupplyCode(), supplyIncrease);
            }
        } catch (Exception e) {
            log.error("getSupplyIncrease error!", e);
        }
        return supplyIncreaseMap;
    }

    /**
     * 根据agentCode+supplyCode+hotelId查询供应商酒店加幅
     *
     * @param agentCode
     * @param supplyCodes
     * @param hotelIds
     * @return
     */
    public Map<String, SupplyHotelIncrease> getSupplyHotelIncrease(String agentCode, List<String> supplyCodes, List<Long> hotelIds) {
        Map<String, SupplyHotelIncrease> supplyHotelIncreaseMap = new HashMap<>();
        try {
            if (StrUtilX.isEmpty(agentCode) || CollUtilX.isEmpty(supplyCodes) || CollUtilX.isEmpty(hotelIds)) {
                return supplyHotelIncreaseMap;
            }

            //组装key
            Set<String> keySet = new HashSet<>();
            for (String supplyCode : supplyCodes) {
                for (Long hotelId : hotelIds) {
                    String key = agentCode.concat(StrUtilX.SPLIT_CODE).concat(supplyCode).concat(StrUtilX.SPLIT_CODE).concat(String.valueOf(hotelId));
                    keySet.add(key);
                }
            }
            List<String> keys = new ArrayList<>(keySet);
            List<Object> supplyHotelIncreases = RedisTemplateX.hMultiGet(RedisKey.AGENT_SUPPLY_HOTEL_INCREASE_KEY, keys);
            if (CollUtilX.isEmpty(supplyHotelIncreases)) {
                return supplyHotelIncreaseMap;
            }

            for (Object obj : supplyHotelIncreases) {
                if (obj == null) {
                    continue;
                }
                SupplyHotelIncrease supplyHotelIncrease = StrUtilX.parseObject(obj, SupplyHotelIncrease.class);
                if (Objects.isNull(supplyHotelIncrease)
                        || StrUtilX.isEmpty(supplyHotelIncrease.getSupplyCode())
                        || Objects.isNull(supplyHotelIncrease.getHotelId())) {
                    continue;
                }
                supplyHotelIncreaseMap.put(supplyHotelIncrease.getSupplyCode().concat(StrUtilX.SPLIT_CODE).concat(String.valueOf(supplyHotelIncrease.getHotelId())), supplyHotelIncrease);
            }
        } catch (Exception e) {
            log.error("getSupplyHotelIncrease error!", e);
        }
        return supplyHotelIncreaseMap;
    }

    /**
     * 计算预付售价
     */
    public BigDecimal calculateSalePrice(SupplyHotelLowestPrice supplyHotelLowestPrice, AgentAccountConfig agentAccountConfig, SupplierAddDTO supplierAddDTO,
                                         SupplyIncrease supplyIncrease, SupplyHotelIncrease supplyHotelIncrease, BigDecimal rate, BigDecimal orgToAgentRate, int orgCurrency, Integer baseCurrency) {
        try {
            BigDecimal basePrice = supplyHotelLowestPrice.getLowestPrice();
            BigDecimal roomPrice = supplyHotelLowestPrice.getRoomPrice();
            if (Objects.isNull(basePrice)) {
                return null;
            }

            // 加幅只对房费
            if (roomPrice != null) {
                basePrice = roomPrice;
            }

            // 自签数据不需要进行加幅，币种是商家币种
            if (StrUtilX.isEmpty(supplyHotelLowestPrice.getSupplyClass()) || !supplyHotelLowestPrice.getSupplyClass().equals(SupplyClassEnum.ZIQIAN.getCode())) {
                // 币种不一样
                if (baseCurrency != null && !baseCurrency.equals(orgCurrency)) {
                    // 计算汇率 供应商转商家
                    basePrice = basePrice.multiply(rate);

                    // 税费
                    supplyHotelLowestPrice.setTaxFee(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getTaxFee()).multiply(rate));
                    supplyHotelLowestPrice.setSalesTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSalesTax()).multiply(rate));
                    supplyHotelLowestPrice.setOtherTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getOtherTax()).multiply(rate));
                    // 按次税费
                    supplyHotelLowestPrice.setSecondTaxFee(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSecondTaxFee()).multiply(rate));
                    supplyHotelLowestPrice.setSecondSalesTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSecondSalesTax()).multiply(rate));
                    supplyHotelLowestPrice.setSecondOtherTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSecondOtherTax()).multiply(rate));
                }

                // 供应商加幅值
                BigDecimal baseSupplierAdjustment = BigDecimal.ZERO;
                if (Objects.nonNull(supplierAddDTO)) {
                    baseSupplierAdjustment = CommonTgUtils.adjustmentAmt(basePrice, supplierAddDTO.getAdjustmentType(), supplierAddDTO.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (supplierAddDTO.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, supplierAddDTO.getAdjustmentType())
                            && supplierAddDTO.getLowestIncreaseAmt() != null && baseSupplierAdjustment.compareTo(supplierAddDTO.getLowestIncreaseAmt()) < 0) {
                        baseSupplierAdjustment = supplierAddDTO.getLowestIncreaseAmt();
                    }
                }

                // 分销商-供应商-酒店加幅值
                BigDecimal baseAgentHotelAdjustment = BigDecimal.ZERO;
                if(supplyHotelIncrease != null){
                    // 优先酒店级别加幅
                    // 供应商加幅后的价格
                    BigDecimal supplierPrice = baseSupplierAdjustment.add(basePrice);
                    baseAgentHotelAdjustment = CommonTgUtils.adjustmentAmt(supplierPrice, supplyHotelIncrease.getAdjustmentType(), supplyHotelIncrease.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (supplyHotelIncrease.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, supplyHotelIncrease.getAdjustmentType())
                            && supplyHotelIncrease.getLowestIncreaseAmt() != null && baseAgentHotelAdjustment.compareTo(supplyHotelIncrease.getLowestIncreaseAmt()) < 0) {
                        baseAgentHotelAdjustment = supplyHotelIncrease.getLowestIncreaseAmt();
                    }
                } else if (supplyIncrease != null) {
                    // 分销商级别加幅
                    // 供应商加幅后的价格
                    BigDecimal supplierPrice = baseSupplierAdjustment.add(basePrice);
                    baseAgentHotelAdjustment = CommonTgUtils.adjustmentAmt(supplierPrice, supplyIncrease.getAdjustmentType(), supplyIncrease.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (supplyIncrease.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, supplyIncrease.getAdjustmentType())
                            && supplyIncrease.getLowestIncreaseAmt() != null && baseAgentHotelAdjustment.compareTo(supplyIncrease.getLowestIncreaseAmt()) < 0) {
                        baseAgentHotelAdjustment = supplyIncrease.getLowestIncreaseAmt();
                    }
                }

                // 折扣
                BigDecimal discountTemp = BigDecimal.ZERO;
                // 其他税
                BigDecimal otherTaxTemp = CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getOtherTax());
                if (baseSupplierAdjustment.compareTo(BigDecimal.ZERO) < 0) {
                    discountTemp = discountTemp.add(baseSupplierAdjustment);
                } else {
                    otherTaxTemp = otherTaxTemp.add(baseSupplierAdjustment);
                }
                if (baseAgentHotelAdjustment.compareTo(BigDecimal.ZERO) < 0) {
                    discountTemp = discountTemp.add(baseAgentHotelAdjustment);
                } else {
                    otherTaxTemp = otherTaxTemp.add(baseAgentHotelAdjustment);
                }
                // 其他税 = 其他税 + 供应商加幅值 + 分销商加幅值 (加幅金额算到其他税里面)
                supplyHotelLowestPrice.setOtherTax(otherTaxTemp);
                // 折扣
                supplyHotelLowestPrice.setDiscount(discountTemp);
            }

            // 币种不一样
            if (!agentAccountConfig.getSettlementCurrency().equals(orgCurrency)) {
                // 计算汇率 商家转客户
                basePrice = basePrice.multiply(orgToAgentRate);

                // 税费
                supplyHotelLowestPrice.setTaxFee(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getTaxFee()).multiply(orgToAgentRate));
                supplyHotelLowestPrice.setSalesTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSalesTax()).multiply(orgToAgentRate));
                supplyHotelLowestPrice.setOtherTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getOtherTax()).multiply(orgToAgentRate));
                supplyHotelLowestPrice.setDiscount(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getDiscount()).multiply(orgToAgentRate));
                // 按次税费
                supplyHotelLowestPrice.setSecondTaxFee(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSecondTaxFee()).multiply(orgToAgentRate));
                supplyHotelLowestPrice.setSecondSalesTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSecondSalesTax()).multiply(orgToAgentRate));
                supplyHotelLowestPrice.setSecondOtherTax(CommonTgUtils.formatBigDecimal(supplyHotelLowestPrice.getSecondOtherTax()).multiply(orgToAgentRate));
            }

            // 只对房费加幅时需要加上税费
            if (roomPrice != null) {
                // 统一进位
                BigDecimal newBasePrice = CommonTgUtils.setScale(basePrice, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                BigDecimal taxFee = CommonTgUtils.setScale(supplyHotelLowestPrice.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                BigDecimal salesTax = CommonTgUtils.setScale(supplyHotelLowestPrice.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                BigDecimal otherTax = CommonTgUtils.setScale(supplyHotelLowestPrice.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                BigDecimal discount = CommonTgUtils.setScale(supplyHotelLowestPrice.getDiscount(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                BigDecimal secondTaxFee = CommonTgUtils.setScale(supplyHotelLowestPrice.getSecondTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                BigDecimal secondSalesTax = CommonTgUtils.setScale(supplyHotelLowestPrice.getSecondSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                BigDecimal secondOtherTax = CommonTgUtils.setScale(supplyHotelLowestPrice.getSecondOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                basePrice = newBasePrice.add(taxFee).add(salesTax).add(otherTax).add(discount).add(secondTaxFee).add(secondSalesTax).add(secondOtherTax);
            } else {
                // 单独进位
                BigDecimal otherTax = CommonTgUtils.setScale(supplyHotelLowestPrice.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                basePrice = CommonTgUtils.setScale(basePrice, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                basePrice = basePrice.add(otherTax);
            }
            return basePrice;
        } catch (Exception e) {
            log.error("计算预付售价错误！", e);
            return null;
        }
    }

    /**
     * 计算现付售价
     */
    public BigDecimal calculateCashSalePrice(BigDecimal cashPrice, Integer cashCurrency, String supplyClass, AgentAccountConfig agentAccountConfig, BigDecimal rate, BigDecimal orgToAgentRate, int orgCurrency) {
        try {
            if (Objects.isNull(cashPrice)) {
                return null;
            }

            // 自签数据不需要进行加幅，币种是商家币种
            if (StrUtilX.isEmpty(supplyClass) || !supplyClass.equals(SupplyClassEnum.ZIQIAN.getCode())) {
                // 币种不一样
                if (cashCurrency != null && !cashCurrency.equals(orgCurrency)) {
                    // 计算汇率 供应商转商家
                    cashPrice = cashPrice.multiply(rate);
                }
            }

            // 币种不一样
            if (agentAccountConfig.getSettlementCurrency() != null && !agentAccountConfig.getSettlementCurrency().equals(orgCurrency)) {
                // 计算汇率 商家转客户
                cashPrice = cashPrice.multiply(orgToAgentRate);
            }

            cashPrice = CommonTgUtils.setScale(cashPrice, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
        } catch (Exception e) {
            log.error("计算现付售价错误！", e);
            return null;
        }
        return cashPrice;
    }

    /**
     * 获取产品id
     */
    public Map<String, String> getCacheProductIdsWithUUID(List<String> productIds, Long hotelId) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            // 1. 生成所有缓存键
            List<String> cacheKeys = productIds.stream()
                    .map(item -> buildCacheKey(hotelId, item))
                    .collect(Collectors.toList());

            // 2. 批量查询缓存
            List<String> cachedValues = RedisTemplateX.multiGet(cacheKeys);

            // 处理可能的null情况，确保列表大小一致
            if (cachedValues == null) {
                cachedValues = Collections.nCopies(cacheKeys.size(), null);
            } else if (cachedValues.size() != cacheKeys.size()) {
                throw new IllegalStateException("Redis返回的缓存值数量与请求的键数量不一致");
            }

            // 3. 构建结果Map并准备写入缓存的数据
            List<String> valuesToCache = new ArrayList<>(cacheKeys.size());
            for (int i = 0; i < productIds.size(); i++) {
                String productId = productIds.get(i);
                String cachedValue = cachedValues.get(i);

                // 生成或使用缓存值
                String value = (cachedValue != null) ? cachedValue : IdUtil.fastSimpleUUID();
                resultMap.put(productId, value);
                valuesToCache.add(value);
            }

            // 4. 批量原子写入缓存
            if (resultMap.size() > 0) {
                RedisTemplateX.getStringRedisTemplate().executePipelined((RedisCallback<Void>) conn -> {
                    for (int i = 0; i < cacheKeys.size(); i++) {
                        String cacheKey = cacheKeys.get(i);
                        String value = valuesToCache.get(i);

                        byte[] keyBytes = cacheKey.getBytes(StandardCharsets.UTF_8);
                        byte[] valueBytes = value.getBytes(StandardCharsets.UTF_8);

                        // 使用SETEX设置键值对及过期时间（正向映射）
                        conn.setEx(keyBytes, 3600, valueBytes);
                        // 反向映射：value -> cacheKey
                        conn.setEx(buildCacheKey(value).getBytes(StandardCharsets.UTF_8), 3600, productIds.get(i).getBytes(StandardCharsets.UTF_8));
                    }
                    return null;
                });
            }

            return resultMap;
        } catch (Exception e) {
            log.error("获取产品id异常", e);
            throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(Long hotelId, String productId) {
        return String.format("%s%d_%s", RedisKey.AGENT_PRODUCT_ID_MAPPING_CACHE, hotelId, productId);
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String uuId) {
        return String.format("%s%s", RedisKey.AGENT_PRODUCT_ID_MAPPING_CACHE, uuId);
    }
}
