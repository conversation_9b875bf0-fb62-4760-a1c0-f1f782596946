package com.tiangong.product.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.tiangong.common.Response;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.domain.areatadatimezone.AreaDataTimeZone;
import com.tiangong.product.domain.areatadatimezone.AreaDataTimeZoneReqDTO;
import com.tiangong.product.domain.areatadatimezone.AreaDataTimeZoneRespDTO;
import com.tiangong.redis.core.RedisTemplateX;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class TimeZoneUtil {

    /**
     * 获取转换后的时间和酒店所处的时区
     * @param req
     * @return
     */
    public static Response<AreaDataTimeZoneRespDTO> convertDateTimeZone(AreaDataTimeZoneReqDTO req) {
        AreaDataTimeZoneRespDTO respDTO = new AreaDataTimeZoneRespDTO();

        // 根据区域获取时区
        String timeZone = getTimeZone(req);

        // 根据查询的时区转换
        if (timeZone == null){
            throw new SysException(ErrorCodeEnum.EXCEPTION_DATETIME);
        }

        // 获取转换后的时间
        respDTO.setDateTime(convertDateTime(req.getDateTime(), req.getDateTimeZone(), timeZone));
        respDTO.setTimeZone(timeZone);
        return Response.success(respDTO);
    }

    /**
     * 获取时区
     * @param req
     * countryCode：国家编码
     * provinceCode：省份编码
     * @return
     */
    public static Response<String> getDateTimeZone(AreaDataTimeZoneReqDTO req) {
        return Response.success(getTimeZone(req));
    }

    /**
     * 转换时间
     * dateTime：需要转换的时间
     * dateTimeZone：需要转换时间的时区
     * convertTimeZone：需要转换到的时区
     * @param req
     * @return 转换后的时间
     */
    public static Response<String> convertTimeByTimeZone(AreaDataTimeZoneReqDTO req) {
        return Response.success(convertDateTime(req.getDateTime(), req.getDateTimeZone(), req.getConvertTimeZone()));
    }

    /**
     * 查询时区
     *
     * @param req
     * @return
     */
    private static String getTimeZone(AreaDataTimeZoneReqDTO req) {
        String key = RedisKey.AREA_DATA_TIME_ZONE;

        // 查询缓存时区
        AreaDataTimeZone areaDataTimeZone = null;
        if (RedisTemplateX.hashGet(key, req.getCountryCode()) != null) {
            areaDataTimeZone = JSON.parseObject((String) RedisTemplateX.hashGet(key, req.getCountryCode()), AreaDataTimeZone.class);
        } else if (RedisTemplateX.hashGet(key, req.getCountryCode() + "_" + req.getProvinceCode()) != null) {
            areaDataTimeZone = JSON.parseObject((String) RedisTemplateX.hashGet(key, req.getCountryCode() + "_" + req.getProvinceCode()), AreaDataTimeZone.class);
        }

        // 未查询到时区，放入失败队列
        if (areaDataTimeZone == null) {
            RedisTemplateX.setAdd(RedisKey.AREA_DATA_TIME_ZONE_NOT_EXISTS, req.getCountryCode() + "_" + req.getProvinceCode());
            return null;
        }

        // 判断取标准时区还是夏令时
        String timeZone = areaDataTimeZone.getTimeZone();
        if (StringUtils.isNotEmpty(areaDataTimeZone.getSummerStartDate())) {
            String summerStartDate = areaDataTimeZone.getSummerStartDate();
            String summerEndDate = areaDataTimeZone.getSummerEndDate();
            String dateTimeStr = req.getDateTime()+" 00:00:00";
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            try {
                // 解析给定的时间字符串
                LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, dateTimeFormatter);
                LocalDate dateTimeDate = dateTime.toLocalDate();

                // 解析起始日期字符串，并设置年份
                LocalDate startDate = LocalDate.of(dateTimeDate.getYear(),
                        Integer.parseInt(summerStartDate.split("-")[0]),
                        Integer.parseInt(summerStartDate.split("-")[1]));

                // 解析结束日期字符串，并设置年份
                LocalDate endDate;
                if (Integer.parseInt(summerEndDate.split("-")[0]) < startDate.getMonthValue()) {
                    // 如果结束月份小于开始月份，则是明年的日期
                    endDate = LocalDate.of(dateTimeDate.getYear() + 1,
                            Integer.parseInt(summerEndDate.split("-")[0]),
                            Integer.parseInt(summerEndDate.split("-")[1]));
                } else {
                    endDate = LocalDate.of(dateTimeDate.getYear(),
                            Integer.parseInt(summerEndDate.split("-")[0]),
                            Integer.parseInt(summerEndDate.split("-")[1]));
                }

                // 检查日期是否在范围内
                if (!dateTimeDate.isBefore(startDate) && !dateTimeDate.isAfter(endDate)) {
                    timeZone = areaDataTimeZone.getSummerTimeZone();
                }
            } catch (Exception e) {
                throw new SysException(ErrorCodeEnum.EXCEPTION_DATETIME);
            }
        }
        return timeZone;
    }

    /**
     * 转换时间时区
     * @param dateTime 时间
     * @param dateTimeZone 原始时区
     * @param timeZone 需转换时区
     * @return
     */
    private static String convertDateTime(String dateTime, String dateTimeZone, String timeZone){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将字符串解析为LocalDateTime（没有时区信息）
        LocalDateTime localDateTime = LocalDateTime.parse(dateTime, formatter);

        // 将入参时间设置时区
        // 去掉"UTC"前缀 例：UTC-5:30 = -5:30
        String time = dateTimeZone.replace("UTC", "");
        ZonedDateTime dateTimeZoneDateTime;
        if (dateTimeZone.contains(":")){
            // 有5:30的时区，需特殊转换
            String[] times = time.split(":");
            if (dateTimeZone.contains("-")){
                // -时区
                dateTimeZoneDateTime = localDateTime.atZone(ZoneOffset.ofHoursMinutes(Integer.parseInt(times[0]), -Integer.parseInt(times[1])));
            } else {
                // +时区
                dateTimeZoneDateTime = localDateTime.atZone(ZoneOffset.ofHoursMinutes(Integer.parseInt(times[0]), Integer.parseInt(times[1])));
            }
        } else {
            dateTimeZoneDateTime = localDateTime.atZone(ZoneOffset.ofHours(Integer.parseInt(time)));
        }

        // 将原始时间按timeZone时区转换
        // 去掉"UTC"前缀，例：UTC+5:30
        String timeZ = timeZone.replace("UTC", "");
        ZonedDateTime timeZoneDateTime;
        if (timeZone.contains(":")){
            // 有5:30的时区，需特殊转换
            String[] times = timeZ.split(":");
            if (timeZone.contains("-")){
                // -时区
                timeZoneDateTime = dateTimeZoneDateTime.withZoneSameInstant(ZoneOffset.ofHoursMinutes(Integer.parseInt(times[0]), -Integer.parseInt(times[1])));
            } else {
                // +时区
                timeZoneDateTime = dateTimeZoneDateTime.withZoneSameInstant(ZoneOffset.ofHoursMinutes(Integer.parseInt(times[0]), Integer.parseInt(times[1])));
            }
        } else {
            timeZoneDateTime = dateTimeZoneDateTime.withZoneSameInstant(ZoneOffset.ofHours(Integer.parseInt(timeZ)));
        }

        // 输出转换后时间
        String convertedDateStr = timeZoneDateTime.format(formatter);
        return convertedDateStr;
    }

    /**
     * 获取标准时区（不考虑夏令时）
     * @param req
     * countryCode：国家编码
     * provinceCode：省份编码
     * @return 标准时区
     */
    public static Response<String> getStandardTimeZone(AreaDataTimeZoneReqDTO req) {
        String key = RedisKey.AREA_DATA_TIME_ZONE;

        // 查询缓存时区
        AreaDataTimeZone areaDataTimeZone = null;
        if (RedisTemplateX.hashGet(key, req.getCountryCode()) != null) {
            areaDataTimeZone = JSON.parseObject((String) RedisTemplateX.hashGet(key, req.getCountryCode()), AreaDataTimeZone.class);
        } else if (RedisTemplateX.hashGet(key, req.getCountryCode() + "_" + req.getProvinceCode()) != null) {
            areaDataTimeZone = JSON.parseObject((String) RedisTemplateX.hashGet(key, req.getCountryCode() + "_" + req.getProvinceCode()), AreaDataTimeZone.class);
        }

        // 未查询到时区，放入失败队列
        if (areaDataTimeZone == null) {
            RedisTemplateX.setAdd(RedisKey.AREA_DATA_TIME_ZONE_NOT_EXISTS, req.getCountryCode() + "_" + req.getProvinceCode());
            return Response.success(null);
        }

        // 直接返回标准时区，不判断夏令时
        return Response.success(areaDataTimeZone.getTimeZone());
    }
}
