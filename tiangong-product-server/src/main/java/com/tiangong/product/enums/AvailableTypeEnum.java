package com.tiangong.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@AllArgsConstructor
@Getter
public enum AvailableTypeEnum {
    WHITE(1, "白名单"),
    BLACK(2, "黑名单");
    /**
     * 枚举
     */
    private final Integer key;
    /**
     * 描述
     */
    private final String desc;
    public static String getDesc(Integer key) {
        for (AvailableTypeEnum availableTypeEnum : AvailableTypeEnum.values()) {
            if (availableTypeEnum.key.equals(key)) {
                return availableTypeEnum.desc;
            }
        }
        return null;
    }
    public static Integer getKey(String desc) {
        for (AvailableTypeEnum availableTypeEnum : AvailableTypeEnum.values()) {
            if (availableTypeEnum.desc.equals(desc)) {
                return availableTypeEnum.key;
            }
        }
        return null;
    }
}
