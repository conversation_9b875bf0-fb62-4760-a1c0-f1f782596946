package com.tiangong.product.task;

import com.alibaba.fastjson.JSON;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.product.service.ProductSaleService;
import com.tiangong.product.util.QueryUtil;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 用途：计算客户可售酒店列表预处理任务
 */
@Slf4j
@Component
public class AgentHotelAvailablePreHandlerTask {

    @Autowired
    private ProductSaleService productSaleService;

    @Autowired
    private QueryUtil queryUtil;

    @XxlJob("agentHotelAvailablePreHandlerTask")
    public void agentHotelAvailablePreHandlerTask() {
        try {
            XxlJobHelper.log("执行计算客户可售酒店列表预处理任务开始");
            String param = XxlJobHelper.getJobParam();
            if (StrUtilX.isNotEmpty(param)) {
                // 处理客户可售酒店
                disposeAgentAvailableHotel(param);
            } else {
                if (RedisTemplateX.hasKey(RedisKey.AVAILABLE_AGENT_CODE_LIST)) {
                    String agentCode = RedisTemplateX.setPop(RedisKey.AVAILABLE_AGENT_CODE_LIST);
                    if (StrUtilX.isNotEmpty(agentCode)) {
                        // 处理客户可售酒店
                        disposeAgentAvailableHotel(agentCode);
                    }
                }
            }
            XxlJobHelper.log("执行计算客户可售酒店列表预处理任务结束");
        } catch (Exception e) {
            log.error("执行计算客户可售酒店列表预处理任务异常", e);
            XxlJobHelper.log("执行计算客户可售酒店列表预处理任务异常", e);
        }
    }

    /**
     * 处理客户可售酒店
     */
    private void disposeAgentAvailableHotel(String agentCode) {
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode), AgentAccountConfig.class);
        if (Objects.nonNull(agentAccountConfig) && agentAccountConfig.getAvailableStatus().equals(1)) {
            // 查询客户白名单供应商
            List<String> supplyCodes = queryUtil.getAgentSupplyAvailable(agentCode);
            if (CollUtilX.isEmpty(supplyCodes)) {
                log.info("分销商关联供应商白名单配置不存在:{}", agentCode);
            } else {
                // 处理客户可售酒店
                productSaleService.disposeAgentAvailableHotel(agentCode, supplyCodes);
            }
        }
    }

}
