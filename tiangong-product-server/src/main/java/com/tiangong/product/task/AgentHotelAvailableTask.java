package com.tiangong.product.task;

import com.alibaba.fastjson.JSON;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.service.ProductSaleService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 用途：计算客户可售酒店列表
 *
 * @author：Owen
 */
@Slf4j
@Component
public class AgentHotelAvailableTask {

    @Autowired
    private ProductSaleService productSaleService;

    @XxlJob("agentHotelAvailableTask")
    public void agentHotelAvailableTask() {
        try {
            XxlJobHelper.log("执行计算客户可售酒店列表任务开始");
            // param：执行酒店数，Integer
            String param = XxlJobHelper.getJobParam();
            if (StrUtilX.isEmpty(param)) {
                XxlJobHelper.log("执行计算客户可售酒店列表任务结束，未传入入参，不执行");
                return;
            }

            // 消费待消费计算起价分销商列表
            String agentCode = RedisTemplateX.lRightPop(RedisKey.AVAILABLE_AGENT_LIST);
            if (Objects.nonNull(agentCode)) {
                // 代码优化 一次取多个 并删除
                List<String> hotelIdList = RedisTemplateX.lRightPopMultiple(RedisKey.AVAILABLE_AGENT_HOTEL_LIST + agentCode, Integer.parseInt(param));

                // 酒店未跑完，将客户塞回待计算列表
                if (hotelIdList.size() == Integer.parseInt(param)) {
                    RedisTemplateX.lRightPushAll(RedisKey.AVAILABLE_AGENT_LIST, new ArrayList<>(Collections.singletonList(agentCode)));
                } else {
                    // 防止查询异常
                    String hotelId = RedisTemplateX.lRightPop(RedisKey.AVAILABLE_AGENT_HOTEL_LIST + agentCode);
                    if (Objects.nonNull(hotelId)) {
                        RedisTemplateX.lRightPushAll(RedisKey.AVAILABLE_AGENT_HOTEL_LIST + agentCode, new ArrayList<>(Collections.singletonList(hotelId)));
                        RedisTemplateX.lRightPushAll(RedisKey.AVAILABLE_AGENT_LIST, new ArrayList<>(Collections.singletonList(agentCode)));
                    }
                }

                // 计算起价并入库
                if (hotelIdList.size() > 0) {
                    productSaleService.calculateAgentAvailableHotel(agentCode, hotelIdList);
                }
            }
            XxlJobHelper.log("执行计算客户可售酒店列表任务结束");
        } catch (Exception e) {
            log.error("执行计算客户可售酒店列表任务异常", e);
            XxlJobHelper.log("执行计算客户可售酒店列表任务异常", e);
        }
    }
}
