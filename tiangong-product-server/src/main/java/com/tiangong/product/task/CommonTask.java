package com.tiangong.product.task;

import cn.hutool.core.util.StrUtil;
import com.tiangong.product.domain.ProSaleDatePO;
import com.tiangong.product.mapper.ProSaleDataMapper;
import com.tiangong.product.service.ProTaxRuleConfigService;
import com.tiangong.util.DateUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @program: tiangong
 * @ClassName InitSaleDate
 * @description: 初始化售卖日期到mysql ，至于为啥这么设计。不太清楚了
 * @author: 湫
 * @create: 2024/09/20/ 19:35
 * @Version 1.0
 **/

@Slf4j
@Component
public class CommonTask {

    @Autowired
    private ProSaleDataMapper proSaleDataMapper;

    @Autowired
    private ProTaxRuleConfigService proTaxRuleConfigService;

    /**
     * 初始化售卖日期任务
     */
    @XxlJob("initSaleDate")
    public void initSaleDate() {
        try {
            XxlJobHelper.log("执行初始化售卖日期任务开始");
            String jobParam = XxlJobHelper.getJobParam();
            Date startDate = new Date();
            Date endDate = DateUtilX.getDate(startDate, 365, 0);
            // 默认 新增365 天
            if (StrUtil.isNotEmpty(jobParam)) {
                endDate = DateUtilX.getDate(startDate, Integer.parseInt(jobParam), 0);
            }
            List<Date> dateList = DateUtilX.getDateList(startDate, endDate);

            for (Date date : dateList) {
                ProSaleDatePO proSaleDatePO = new ProSaleDatePO();
                proSaleDatePO.setSaleDate(DateUtilX.dateToString(date));
                ProSaleDatePO temp = proSaleDataMapper.selectOne(proSaleDatePO);
                if (temp == null) {
                    proSaleDataMapper.insert(proSaleDatePO);
                }
            }
            XxlJobHelper.log("执行初始化售卖日期任务结束");
        } catch (Exception e) {
            log.error("执行初始化售卖日期任务异常", e);
            XxlJobHelper.log("执行初始化售卖日期任务异常", e);
        }
    }

    /**
     * 初始化税费信息到缓存任务
     */
    @XxlJob("inItTaxRuleConfigToCache")
    public void inItTaxRuleConfigCacheInit() {
        try {
            XxlJobHelper.log("执行初始化税费信息到缓存任务开始");
            proTaxRuleConfigService.proTaxRuleConfigCacheInit();
            XxlJobHelper.log("执行初始化税费信息到缓存任务结束");
        } catch (Exception e) {
            log.error("执行初始化税费信息到缓存任务异常", e);
            XxlJobHelper.log("执行初始化税费信息到缓存任务异常", e);
        }
    }

}
