package com.tiangong.product.task;

import com.tiangong.product.service.ProductSaleService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 初始化计算自签酒店起价任务
 */
@Slf4j
@Component
public class InitCalculateSelfHotelLowestPriceTask {

    @Autowired
    private ProductSaleService productSaleService;

    @XxlJob("initCalculateSelfHotelLowestPriceTask")
    public void initCalculateSelfHotelLowestPriceTask() {
        try {
            XxlJobHelper.log("初始化计算自签酒店起价任务开始");
            String param = XxlJobHelper.getJobParam();
            productSaleService.initCalculateSelfHotelLowestPriceTask(param);
            XxlJobHelper.log("初始化计算自签酒店起价任务结束");
        } catch (Exception e) {
            log.error("初始化计算自签酒店起价任务异常", e);
            XxlJobHelper.log("初始化计算自签酒店起价任务异常", e);
        }
    }
}
