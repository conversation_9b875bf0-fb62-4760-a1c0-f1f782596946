package com.tiangong.product.task;

import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.tiangong.common.Response;
import com.tiangong.hotel.remote.HotelHeatRemote;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 初始化需要计算酒店热度可订分数的酒店id到缓存任务
 */
@Slf4j
@Component
public class InitCalculateBookableScoreHotelIdToRedisTask {

    @Autowired
    private HotelHeatRemote hotelHeatRemote;

    @XxlJob("initCalculateBookableScoreHotelIdToRedisTask")
    public void initCalculateBookableScoreHotelIdToRedisTask() {
        ExecutorService executor = null;
        try {
            XxlJobHelper.log("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务开始");

            // 配置参数
            final int pageSize = 2000;
            final int maxConcurrency = 8;
            final AtomicLong lastId = new AtomicLong(0);
            final RateLimiter rateLimiter = RateLimiter.create(5);
            executor = Executors.newFixedThreadPool(maxConcurrency);

            // 分页循环
            while (true) {
                rateLimiter.acquire();

                // 分页查询
                HotelHeatReq req = new HotelHeatReq();
                req.setLastId(lastId.get());
                req.setBatchSize(pageSize);

                Response<List<String>> response = hotelHeatRemote.queryAllHotelHeatHotelIdPage(req);

                // 错误处理
                if (response.isError()) {
                    log.error("分页查询失败 lastId={}, error={}", lastId, response.getFailReason());
                    XxlJobHelper.log("分页查询失败: " + response.getFailReason());
                    break;
                }

                List<String> hotelIds = response.getModel();
                if (CollUtilX.isEmpty(hotelIds)) break;

                // 提交处理
                executor.submit(() -> processWithRetry(hotelIds));

                // 更新游标
                updateLastIdAtomically(hotelIds, lastId);
            }

            // 等待完成
            executor.shutdown();
            while (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
                XxlJobHelper.log("等待线程池关闭...");
            }
            XxlJobHelper.log("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务结束，最终lastId=" + lastId.get());
        } catch (Exception e) {
            log.error("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务异常", e);
            XxlJobHelper.log("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务异常", e);
        } finally {
            if (executor != null) executor.shutdownNow();
        }
    }

    /**
     * 更新lastId
     */
    private void updateLastIdAtomically(List<String> hotelIds, AtomicLong lastId) {
        hotelIds.stream()
                .mapToLong(Long::parseLong)
                .max()
                .ifPresent(max -> {
                    long current;
                    do {
                        current = lastId.get();
                    } while (max > current && !lastId.compareAndSet(current, max));
                });
    }

    /**
     * 处理数据
     */
    private void processWithRetry(List<String> hotelIds) {
        try (RedisConnection conn = RedisTemplateX.getConnectionFactory()) {
            conn.openPipeline();
            byte[] key = RedisKey.CALCULATE_BOOKABLE_SCORE_HOTEL_ID_KEY.getBytes();

            hotelIds.stream()
                    .map(String::getBytes)
                    .forEach(bytes -> conn.sAdd(key, bytes));

            conn.closePipeline();
        } catch (Exception e) {
            log.error("Redis批量写入失败", e);
        }
    }
}
