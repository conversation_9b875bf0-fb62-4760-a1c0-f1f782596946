package com.tiangong.product.task;

import com.alibaba.fastjson.JSON;
import com.tiangong.dto.product.SupplyIncrease;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.domain.entity.ProOrgIncreaseEntity;
import com.tiangong.product.mapper.ProOrgIncreaseMapper;
import com.tiangong.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化分销商加幅任务
 */
@Slf4j
@Component
public class InitAgentIncreaseTask {

    @Autowired
    private ProOrgIncreaseMapper proOrgIncreaseMapper;

    @XxlJob("InitAgentIncreaseTask")
    public void initAgentIncreaseTask() {
        try {
            XxlJobHelper.log("执行初始化分销商加幅任务开始");
            List<ProOrgIncreaseEntity> proOrgIncreaseEntities = proOrgIncreaseMapper.selectList(null);
            for (ProOrgIncreaseEntity entity : proOrgIncreaseEntities) {
                Map<String, String> map = new HashMap<>();
                SupplyIncrease supplyIncrease = new SupplyIncrease();
                supplyIncrease.setAdjustmentType(entity.getAdjustmentType());
                supplyIncrease.setModifiedAmt(entity.getModifiedAmt());
                supplyIncrease.setLowestIncreaseAmt(entity.getLowestIncreaseAmt());
                supplyIncrease.setSupplyCode(entity.getSupplierCode());
                map.put(entity.getAgentCode().concat("_").concat(entity.getSupplierCode()), JSON.toJSONString(supplyIncrease));
                RedisTemplateX.hPutAll(RedisKey.AGENT_SUPPLY_INCREASE_KEY, map);
            }
            XxlJobHelper.log("执行初始化分销商加幅任务结束");
        } catch (Exception e) {
            log.error("执行初始化分销商加幅任务异常", e);
            XxlJobHelper.log("执行初始化分销商加幅任务异常", e);
        }
    }
}
