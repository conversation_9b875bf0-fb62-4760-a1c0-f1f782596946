//package com.tiangong.product.task;
//
//import com.alibaba.fastjson.JSON;
//import com.tiangong.keys.RedisKey;
//import com.tiangong.redis.core.RedisTemplateX;
//import com.tiangong.util.DateUtil;
//import com.tiangong.util.StringUtil;
//import com.xxl.job.core.context.XxlJobHelper;
//import com.xxl.job.core.handler.IJobHandler;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import io.lettuce.core.RedisClient;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import redis.clients.jedis.ScanResult;
//
//import java.util.*;
//
////import com.xxl.job.core.log.XxlJobLogger;
//
//@Slf4j
//@Component
//public class CleanPriceAndRoomStatusTask extends IJobHandler {
//
//    @Autowired
//    private RedisClient redisClient;
//
//    @XxlJob("cleanPriceAndRoomStatusTask")
//    public void execute() {
//        try {
//            String param = XxlJobHelper.getJobParam();
//            int forCount = 0;
//
//            if (StrUtilX.isNotEmpty(param)) {
//                forCount = Integer.valueOf(param);
//            } else {
//                log.error("cleanPriceAndRoomStatusTask:cleanPriceAndRoomStatusTask未传入执行次数，不执行！");
//                //return ReturnT.FAIL;
//            }
//            Date currentDate = DateUtil.stringToDate(DateUtil.getCurrentDateStr(2));
//            ScanResult<Map.Entry<String, String>> result = null;
//            String cursor = "0";
//            for (int i = 0; i < forCount; i++) {
//                //每次执行200条数据
//                result = RedisTemplateX.hscan(redisClient, RedisKey.productBasePriceAndRoomStatusKey, cursor, 200);
//                if (null == result) {
//                    log.info("执行完成，执行次数：{}", i);
//                    break;
//                }
//                cleanData(result.getResult(), currentDate);
//                cursor = result.getStringCursor();
//                //每次执行200条休眠2秒
//                Thread.sleep(2000L);
//            }
//
//        } catch (Exception e) {
//            log.error("cleanPriceAndRoomStatusTask:清理价格和房态任务异常", e);
//            //XxlJobLogger.log("清理价格和房态任务异常");
//            //return ReturnT.FAIL;
//        }
//
//        log.info("---------cleanPriceAndRoomStatusTask:清理价格和房态成功-----------");
//    }
//
//    private void cleanData(List<Map.Entry<String, String>> result, Date currentDate) {
//        try {
//            if (CollUtilX.isNotEmpty(result)) {
//                Set<String> needDeleteKeySet = new HashSet<>();
//                for (Map.Entry<String, String> value : result) {
//                    String key = value.getKey();
//                    String[] idAndDateArray = key.split("_");
//                    if (idAndDateArray.length > 1) {
//                        if (DateUtil.compare(DateUtil.stringToDate(idAndDateArray[1]), currentDate) < 0) {
//                            needDeleteKeySet.add(value.getKey());
//                        }
//                    }
//                }
//                if (needDeleteKeySet.size() > 0) {
//                    RedisTemplateX.hDelete(RedisKey.productBasePriceAndRoomStatusKey, needDeleteKeySet.toArray(new String[needDeleteKeySet.size()]));
//                    log.warn("删除过期价格和房态：key = {}", JSON.toJSONString(needDeleteKeySet));
//                }
//            }
//        } catch (Exception e) {
//            log.error("删除过期价格和房态异常！", e);
//        }
//    }
//
//}
