package com.tiangong.product.task;

import com.tiangong.product.service.ProductSaleService;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 删除客户不可售酒店任务
 */
@Slf4j
@Component
public class AgentHotelNotAvailableTask {

    @Autowired
    private ProductSaleService productSaleService;

    @XxlJob("agentHotelNotAvailableTask")
    public void agentHotelNotAvailableTask() {
        try {
            XxlJobHelper.log("执行删除客户不可售酒店任务开始");
            //param：未更新天数，Integer
            String param = XxlJobHelper.getJobParam();
            if (StrUtilX.isEmpty(param)) {
                XxlJobHelper.log("执行删除客户不可售酒店任务未传入入参，不执行");
            } else {
                String date = DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.getCurrentDate(), -Integer.parseInt(param)));

                for (int i = 0; i < 10; i++) {
                    productSaleService.deleteAgentNotAvailHotel("t_agent_hotel_lowest_price_" + i, date);
                }
            }
            XxlJobHelper.log("执行删除客户不可售酒店任务结束");
        } catch (Exception e) {
            log.error("执行删除客户不可售酒店任务异常", e);
            XxlJobHelper.log("执行删除客户不可售酒店任务异常", e);
        }
    }
}
