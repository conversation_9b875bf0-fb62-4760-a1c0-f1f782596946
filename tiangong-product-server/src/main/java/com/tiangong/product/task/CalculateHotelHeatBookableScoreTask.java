package com.tiangong.product.task;

import com.tiangong.product.service.ProductSaleService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 计算酒店热度可订分数任务
 */
@Slf4j
@Component
public class CalculateHotelHeatBookableScoreTask {

    @Autowired
    private ProductSaleService productSaleService;

    @XxlJob("calculateHotelHeatBookableScoreTask")
    public void calculateHotelHeatBookableScoreTask() {
        try {
            XxlJobHelper.log("执行计算酒店热度可订分数任务开始");
            String param = XxlJobHelper.getJobParam();
            productSaleService.calculateHotelHeatBookableScoreTask(param);
            XxlJobHelper.log("执行计算酒店热度可订分数任务结束");
        } catch (Exception e) {
            log.error("执行计算酒店热度可订分数任务异常", e);
            XxlJobHelper.log("执行计算酒店热度可订分数任务异常", e);
        }
    }
}
