package com.tiangong.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.dto.common.MyMapper;
import com.tiangong.product.domain.dto.ProOrgHotelAvailablePageDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableRespDTO;
import com.tiangong.product.domain.entity.ProOrgHotelAvailableEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商酒店黑白名单表
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Mapper
public interface ProOrgHotelAvailableMapper extends BaseMapper<ProOrgHotelAvailableEntity>, MyMapper<ProOrgHotelAvailableEntity> {

    /**
     * 分页查询供应商酒店黑白名单列表
     */
    IPage<ProOrgHotelAvailableRespDTO> queryProOrgHotelAvailablePage(IPage<ProOrgHotelAvailableRespDTO> page, @Param("dto") ProOrgHotelAvailablePageDTO dto);

    /**
     * 根据酒店ID查询所有黑白名单配置
     */
    List<ProOrgHotelAvailableEntity> selectByHotelId(@Param("hotelId") Long hotelId);

}
