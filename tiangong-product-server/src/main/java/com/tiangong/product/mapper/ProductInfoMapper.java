package com.tiangong.product.mapper;

import com.tiangong.product.domain.resp.HotelProductIdsResponse;
import com.tiangong.product.domain.resp.HotelSaleInfoItemResp;
import com.tiangong.product.domain.resp.QueryProductIdsRequest;

import java.util.List;

/**
 * @program: tiangong
 * @ClassName ProductInfoMapper
 * @description:
 * @author: 湫
 * @create: 2024/09/20/ 14:57
 * @Version 1.0
 **/

public interface ProductInfoMapper {

    /**
     * 根据酒店ids查询出产品ids
     */
    List<HotelProductIdsResponse> queryProductIdsByHotelId(QueryProductIdsRequest queryProductIdsRequest);

    /**
     * 查询酒店信息
     */
    List<HotelSaleInfoItemResp> selectProductHotel(QueryProductIdsRequest queryProductIdsRequest);
}