package com.tiangong.product.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.dto.product.AgentApiConfigDTO;
import com.tiangong.product.domain.ProductDayIncreasePO;
import com.tiangong.product.domain.ProductPO;
import com.tiangong.product.domain.ProductRestrictPO;
import com.tiangong.product.domain.ProductSaleStatusPO;
import com.tiangong.product.domain.req.HotelSaleInfoReq;
import com.tiangong.product.domain.resp.HotelSaleInfoItemResp;
import com.tiangong.product.dto.*;
import com.tiangong.product.resp.HotelLabelDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Auther: Owens
 * @Date: 2019/4/24 11:24
 * @Description:产品
 */
@Component(value = "productMapper")
public interface ProductMapper extends MyMapper<ProductPO> {

    /**
     * 根据产品id查询税费配置
     */
    List<ProductTaxRuleDTO> queryProductTaxRuleConfig(@Param("productIdList") Set<Integer> productIdList);

    /**
     * 检查税规则配置是否存在
     */
    int checkTaxRuleConfigExists(@Param("taxRuleConfigId") Long taxRuleConfigId);

    /**
     * 查询产品信息
     */
    List<ProductHotelDTO> queryHotelListByStatistics(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 查询酒店售卖产品信息
     */
    List<ProductTempDTO> queryHotelProducts(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 根据酒店id查询售卖酒店id
     */
    List<Long> querySaleHotelIdsByStatistics(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 根据酒店id批量添加酒店售卖信息
     */
    int addHotelSaleInfoGroupByHotelId(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 根据酒店id删除酒店售卖信息
     */
    int deleteHotelSaleInfoByHotelId(@Param("hotelId") Long hotelId);

    /**
     * 查询产品售卖信息
     */
    List<ProductTempDTO> querySaleProducts(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 查询产品信息
     */
    ProductDTO queryProduct(@Param("productId") String productId);

    /**
     * 按照日期查询产品低价
     */
    List<TotalAmtDTO> queryTotalAmt(ProductOrderQueryRequestDTO productOrderQueryRequestDTO);

    /**
     * 查询产品操作日志
     */
    List<ProductLogDTO> queryProductLogList(Map<String, String> requestMap);

    /**
     * 查询产品售卖日志
     */
    List<ProductSaleLogDTO> queryProductSaleLogList(Map<String, String> requestMap);

    /**
     * 根据供应商code，查询companyCode
     */
    List<String> getCompanyCode(@Param("supplierCode")String supplierCode);

    /**
     * 查询将要修改的产品
     */
    List<Integer> queryWillModifyProductStatus(BatchModifyHotelProductStatusDTO batchModifyHotelProductStatusDTO);

    /**
     * 查询产品条款list
     */
    List<ProductDTO> queryProductRestrictList(QueryProductRestrictDTO queryProductRestrictDTO);

    /**
     * 查询自签酒店数据量
     */
    Integer querySelfSignedProductCount(Map<String, String> requestMap);

    /**
     * 查询自签产品基本信息
     */
    List<ProductDTO> querySelfSignedProductInfo(Map<String, String> requestMap);

    /**
     * 查询自签产品的条款
     */
    List<ProductRestrictPO> querySelfSignedProductRestrict(@Param("list") List<ProductDTO> productDTOS);

    /**
     * 查询自签产品价格房态
     */
    List<ProductDayQuotationDTO> querySelfSignedBasePriceAndRoomStatus(@Param("list") List<ProductDTO> productDTOS);

    /**
     * 查询自签产品的售价
     */
    List<ProductDayIncreasePO> querySelfSignedSalePrice(@Param("list") List<ProductDTO> productDTOS);

    /**
     * 查询自签的售价
     */
    List<ProductSaleStatusPO> querySelfSignedSaleStatus(@Param("list") List<ProductDTO> productDTOS);

    /**
     * 删除分销商无效可售酒店
     */
    int deleteAgentNotAvailHotel(@Param("xTable") String xTable, @Param("date") String date);

    /**
     * 查询是否需要输出原始协议价：0否 1是
     */
    AgentApiConfigDTO agentApiConfig(@Param("agentCode")String agentCode, @Param("supplyType") String supplyType);

    /**
     * 查询适用所有客户酒店标签列表
     */
    List<HotelLabelDTO> selectApplyAllAgentHotelLabel(HotelPriceReq req);

    /**
     * 查询指定客户|所有客户酒店标签列表
     */
    List<HotelLabelDTO> selectAgentHotelLabel(HotelPriceReq req);

    /**
     * 查询酒店售卖信息
     */
    List<HotelSaleInfoItemResp> selectHotelSaleInfoList(HotelSaleInfoReq req);
}
