package com.tiangong.product.domain.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class HotelPriceResp {

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店英文名称
     */
    private String hotelEnglishName;

    /**
     * 供应商酒店ID
     */
    private String spHotelId;

    /**
     * 酒店星级
     */
    private Integer sortRank;

    /**
     * 酒店地址
     */
    private String hotelAddress;

    /**
     * 联系电话
     */
    private String hotelTel;

    /**
     * 售卖价格
     */
    private BigDecimal salePrice;

    /**
     * 协议酒店
     */
    private Integer protocolFlag;

    /**
     * 钟点房
     */
    private Integer duration;

    /**
     * 酒店主图
     */
    private String mainUrl;

    /**
     * 最早到店时间，分钟数
     */
    private Integer earliestArriveTime;

    /**
     * 最晚离店时间，分钟数
     */
    private Integer latestLeaveTime;

    /**
     * 酒店星级
     */
    private String hotelRank;

    /**
     * 装修日期
     */
    private Date decorationDate;
    /**
     * 装修年份
     */
    private String decoratedYear;


}
