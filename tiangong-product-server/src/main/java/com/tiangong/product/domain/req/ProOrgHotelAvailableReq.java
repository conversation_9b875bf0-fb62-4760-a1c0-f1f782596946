package com.tiangong.product.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商酒店黑白名单表
 * 请求参数
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProOrgHotelAvailableReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 可见模式（1-白名单 2-黑名单）
     */
    private Integer availableType;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;
}
