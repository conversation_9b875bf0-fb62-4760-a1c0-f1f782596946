package com.tiangong.product.domain.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: tiangong
 * @ClassName PriceDto
 * @description:
 * @author: 湫
 * @create: 2024/09/24/ 20:12
 * @Version 1.0
 **/

@Data
public class PriceDto {

    /**
     * 总卖价
     */
    private BigDecimal totalSalePrice = BigDecimal.ZERO;

    /**
     * 总底价 含税
     */
    private BigDecimal totalBasePrice = BigDecimal.ZERO;


    /**
     * 总房费 不含税
     */
    private BigDecimal totalRoomPrice = BigDecimal.ZERO;
}
