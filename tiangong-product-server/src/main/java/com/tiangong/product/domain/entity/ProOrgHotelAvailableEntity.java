package com.tiangong.product.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tiangong.dto.common.BaseDTO;
import com.tiangong.dto.common.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * 供应商酒店黑白名单表
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name="t_pro_org_hotel_available")
@TableName("t_pro_org_hotel_available")
public class ProOrgHotelAvailableEntity extends BasePO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 可见模式（1-白名单 2-黑名单）
     */
    private Integer availableType;
    /**
     * 酒店ID
     */
    private Long hotelId;
    /**
     * 城市id
     */
    private String cityCode;
}
