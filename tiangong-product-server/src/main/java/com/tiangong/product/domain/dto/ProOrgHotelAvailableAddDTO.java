package com.tiangong.product.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 供应商酒店黑白名单新增DTO
 * 服务层传输对象
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProOrgHotelAvailableAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 黑白名单类型（1-白名单 2-黑名单）
     */
    private Integer availableType;

    /**
     * 创建人
     */
    private String createdBy;
}
