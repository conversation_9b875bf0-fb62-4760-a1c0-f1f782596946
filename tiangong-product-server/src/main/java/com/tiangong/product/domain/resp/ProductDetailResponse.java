package com.tiangong.product.domain.resp;


import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProductDetailResponse {

    /**
     * 酒店id
     */
    private Long hotelId;
    /**
     * 房型列表
     */
    private List<RoomItem> roomItems;
    /**
     * 钟点房房型列表
     */
    private List<RoomItem> hourlyRoomItems;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 酒店英文名称
     */
    private String hotelEnglishName;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 行政区名称
     */
    private String districtName;
    /**
     * 酒店地址
     */
    private String hotelAddress;
    /**
     * 装修日期
     */
    private Date decorationDate;
    /**
     * 装修日期年
     */
    private String decorationYear;
    /**
     * 联系电话
     */
    private String hotelTel;
    /**
     * 酒店星级
     */
    private Integer hotelRank;
    /**
     * 生成简介
     */
    private String summary;
    /**
     * 酒店主图片
     */
    private String mainUrl;


}
