package com.tiangong.product.domain.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/7/7 15:06
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DrainageSettingQueryResp implements Serializable {

    /**
     * 引流设置
     * 0:不做资源引流
     * 1:过滤携程数据，引流给加力供应商
     */
    private Integer drainageSetting;

}
