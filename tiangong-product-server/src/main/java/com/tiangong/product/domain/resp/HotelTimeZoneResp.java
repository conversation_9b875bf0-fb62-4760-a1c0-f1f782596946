package com.tiangong.product.domain.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/6 19:29
 * @Description:
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
public class HotelTimeZoneResp implements Serializable {

    /**
     * 酒店当地时区 (例如: UTC+7)
     */
    private String hotelLocalTimezone;

}
