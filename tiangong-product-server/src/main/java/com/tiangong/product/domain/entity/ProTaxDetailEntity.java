package com.tiangong.product.domain.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 税费配置 DO
 *
 * <AUTHOR>
 */
@TableName("t_pro_tax_detail")
@KeySequence("t_pro_tax_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProTaxDetailEntity extends Model<ProTaxDetailEntity> {

    /** 自增长主键id */
    @TableId
    private Long id;

    /** 税费规则id */
    private Long taxRuleConfigId;

    /** 税费类型 com.tiangong.enums.TaxTypeEnum */
    private Integer taxType;

    /** 税费说明 */
    private String taxDesc;

    /** 税费标准 [1:/间、2:/晚]
     * com.tiangong.enums.TaxStandardsEnum
     * */
    private Integer taxStandard;

    /**
     *  税费加辐规则 [1:加固定值、2:加百分比]
     * com.tiangong.enums.TaxStandardsEnum
     * */
    private Integer taxIncreaseType;

    /** 税费加辐值 */
    private BigDecimal taxIncreaseValue;

    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

}
