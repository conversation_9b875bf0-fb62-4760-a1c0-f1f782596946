package com.tiangong.product.domain.req;


import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 09:23:04
 */
@Data
public class ProProductFilterStrategyQueryPageReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

}