package com.tiangong.product.domain.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 09:23:04
 */
@Data
public class ProProductFilterStrategyQueryPageResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 适用范围(0所有酒店 1指定酒店集团 2指定酒店)
     */
    private Integer scopeOfApplication;
    /**
     * 酒店Id或酒店集团Id
     */
    private String targetCode;
    /**
     * 酒店名称或酒店集团名称
     */
    private String targetName;
    /**
     * 过滤策略(0【取消规则】不优于其他供应商产品 1【确认时效】不优于其他供应商产品 2协议标签与其他供应商产品不一致)
     */
    private List<Integer> filterStrategyList;
    /**
     * 价格策略类型（0加数值 1减数值 2加百分比 3减百分比 4等于）
     */
    private Integer priceStrategyAdjustmentType;
    /**
     * 价格策略
     */
    private BigDecimal priceStrategy;

}