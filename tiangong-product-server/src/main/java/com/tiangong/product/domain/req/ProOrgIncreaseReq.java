package com.tiangong.product.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 差异化售价表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-14 14:24:57
 */
@Data
public class ProOrgIncreaseReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户编码
     */
    private List<String> list;

    /**
     * 差异化加幅Id
     */
    private Integer id;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 调整方式（0加数值 1减数值 2加百分比 3减百分比 4等于）
     */
    private Integer adjustmentType;
    /**
     * 调整金额
     */
    private BigDecimal modifiedAmt;
    /**
     * 最低加幅金额
     */
    private BigDecimal lowestIncreaseAmt;
    /**
     * 运营商编码
     */
    private String companyCode;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 供应商加幅（0加数值 1减数值 2加百分比 3减百分比 4等于）
     */
    private Integer supplierAdjustmentType;

    /**
     * 供应商调整金额
     */
    private BigDecimal supplerModifiedAmt;
}