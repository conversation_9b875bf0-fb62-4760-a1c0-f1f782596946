package com.tiangong.product.domain.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HotelSaleInfoItemResp {
    /**
     * 销售信息id
     */
    private Integer saleInfoId;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 销售状态 0停售 1在售
     */
    private Integer saleStatus;
}
