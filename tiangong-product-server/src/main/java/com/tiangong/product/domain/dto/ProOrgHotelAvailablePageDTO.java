package com.tiangong.product.domain.dto;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商酒店黑白名单分页查询DTO
 * 服务层传输对象
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProOrgHotelAvailablePageDTO extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 启用状态
     */
    private Integer active;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店名称
     */
    private String hotelName;

}
