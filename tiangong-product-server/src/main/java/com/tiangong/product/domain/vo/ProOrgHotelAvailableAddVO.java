package com.tiangong.product.domain.vo;

import lombok.Data;


import java.io.Serializable;

/**
 * 供应商酒店黑白名单新增VO
 * 控制层接收参数
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProOrgHotelAvailableAddVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 黑白名单类型（1-白名单 2-黑名单）
     */
    private Integer availableType;
}
