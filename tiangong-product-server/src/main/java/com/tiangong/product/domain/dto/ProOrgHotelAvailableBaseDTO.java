package com.tiangong.product.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 供应商酒店黑白名单分页响应DTO
 * 服务层返回对象
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProOrgHotelAvailableBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 酒店ID
     */
    private Long hotelId;


    /**
     * 类型（1-白名单 2-黑名单）
     */
    private Integer availableType;
}
