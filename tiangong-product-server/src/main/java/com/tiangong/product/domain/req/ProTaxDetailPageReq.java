package com.tiangong.product.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 税费配置 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProTaxDetailPageReq extends BasePage {

    /** 税费规则id */
    private Long taxRuleConfigId;

    /** 税费说明 */
    private String taxDesc;

    /** 税费标准 [1:/间、2:/晚] */
    private Integer taxStandard;

    /** 税费加辐规则 [1:加固定值、2:加百分比] */

    /** 税费加辐值 */
    private BigDecimal taxIncreaseValue;


}
