package com.tiangong.product.domain.resp;

import lombok.Data;

import java.util.List;


@Data
public class QueryProductIdsRequest  {

    /**
     * 酒店Id
     */
    private String hotelId;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 供应商白名单
     */
    private List<String> supplyCodes;

    /**
     * 价格类型 （0含税价 1不含税价）
     *   对应 0国内 1 海外
     */
    private Integer priceType;

    /**
     * 支付类型
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 税费规则id
     */
    private Long taxRuleConfigId;

    /**
     * 产品Id
     */
    private Integer productId;
}
