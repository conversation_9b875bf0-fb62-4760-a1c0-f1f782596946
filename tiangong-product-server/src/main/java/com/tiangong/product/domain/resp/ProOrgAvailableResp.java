package com.tiangong.product.domain.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 可见性表
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-14 14:24:58
 */
@Data
public class ProOrgAvailableResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 可见性Id
     */
    private Integer id;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 分销商名称
     */
    private String agentName;
    /**
     * /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 可见模式（1-白名单 2-黑名单）
     */
    private Integer availableType;
    /**
     * 维度（1-分销商 2-供应商）
     */
    private Integer source;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;
}