package com.tiangong.product.domain.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SelfHotelLowestReq {
    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 价格
     */
    private BigDecimal totalPrice;

    /**
     * 支付类型 0:预付 1:到店付
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 产品币种
     */
    private Integer productCurrency;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 销售税
     */
    private BigDecimal salesTax;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 其他税费
     */
    private BigDecimal otherTaxFee;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 到店另付费用币种
     */
    private Integer payAtHotelFeeCurrency;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 次税费 税费
     */
    private BigDecimal secondTaxFee;

    /**
     * 次税 销售税
     */
    private BigDecimal secondSalesTax;

    /**
     * 次税 其他税费
     */
    private BigDecimal secondOtherTax;
}
