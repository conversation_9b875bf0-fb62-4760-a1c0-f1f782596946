package com.tiangong.product.domain.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 删除起价对象
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SupplyHotelLowestPriceDelReq {

    /**
     * 商家来源
     */
    private String merchantSource;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 供应商类型
     */
    private String supplyClass;

    /**
     * 供应商编码
     */
    private List<String> supplyCodes;

    /**
     * 酒店id
     */
    @NotNull(message = "酒店id不能为空")
    private String hotelId;

    /**
     * 售卖日期
     */
    private String saleDate;

}
