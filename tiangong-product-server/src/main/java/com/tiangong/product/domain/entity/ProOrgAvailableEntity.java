package com.tiangong.product.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;
import java.util.Date;

import lombok.Data;

/**
 * 可见性表
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-14 14:24:58
 */
@Data
@TableName("t_pro_org_available")
public class ProOrgAvailableEntity extends Model<ProOrgAvailableEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 可见性Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 可见模式（1-白名单 2-黑名单）
     */
    private Integer availableType;
    /**
     * 维度（1-分销商 2-供应商）
     */
    private Integer source;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;

}
