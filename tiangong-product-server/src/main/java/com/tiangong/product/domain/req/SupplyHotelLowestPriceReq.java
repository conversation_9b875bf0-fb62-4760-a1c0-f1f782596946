package com.tiangong.product.domain.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 供应商每日起价
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SupplyHotelLowestPriceReq {

    //NOTE 预付金额和现付金额不可同时为空 !!!!

    /** 最低售价 预付 */
    private BigDecimal lowestPrice;

    /**
     * 房费
     */
    private BigDecimal roomPrice;
    /**
     * 每日税费
     *  税费
     */
    private BigDecimal taxFee;

    /**
     * 销售税
     */
    private BigDecimal salesTax;

    /**
     * 其他税费
     */
    private BigDecimal otherTax;

    /**
     * 次税费
     * 税费
     */
    private BigDecimal secondTaxFee;

    /**
     * 次税 销售税
     */
    private BigDecimal secondSalesTax;

    /**
     * 次税 其他税费
     */
    private BigDecimal secondOtherTax;

    /** 底价币种 预付 */
    private Integer lowestPriceCurreny;

    /**
     * 到店另付金额
     */
    private BigDecimal payInStorePrice;

    /**
     * 到店另付币种
     */
    private Integer payInStoreCurrency;

    /**
     * 现付和到店另付的区别：
     * EPS数据存在预付产品，到店另付金额。
     */

    /**
     * 现付金额
     */
    private BigDecimal cashPrice;

    /**
     * 现付币种
     */
    private Integer cashCurrency;

    /** 日期 */
    @NotNull(message = "日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate saleDate;

    /** 酒店id */
    @NotEmpty(message = "酒店id不能为空")
    private String hotelId;

    /** 国家编码 */
    private String countryCode;

    /** 城市编码 */
    private String cityCode;

    /** 供应类型 */
    @NotEmpty(message = "供应类型不能为空")
    private String supplyClass;

    /** 供应商编码 */
    @NotEmpty(message = "供应商编码不能为空")
    private String supplyCode;

    /** 商家来源 */
    @NotEmpty(message = "商家来源不能为空")
    private String merchantSource;

    /** 商家编码 */
    @NotEmpty(message = "商家编码不能为空")
    private String merchantCode;

    /** 类型：1-国内 2-海外 ["国内","海外"] */
    @NotEmpty(message = "类型：1-国内 2-海外 [’国内‘,’海外‘]不能为空")
    private String supplyType;

    /** 状态 [有效,无效] */
    @NotNull(message = "状态 [有效,无效]不能为空")
    private Integer status;

    /**
     * 折扣
     * 直连产品忽略 自签产品使用
     */
    private BigDecimal discount;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedDt;
}