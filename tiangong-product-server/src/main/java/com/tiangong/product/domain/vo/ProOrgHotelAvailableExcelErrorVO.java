package com.tiangong.product.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/7
 * @Description:
 */
@Data
public class ProOrgHotelAvailableExcelErrorVO implements Serializable {
    /**
     * 供应商编码
     */
    @ExcelProperty("供应商编码")
    @ColumnWidth(20)
    private String supplierCode;
    /**
     * 酒店ID
     */
    @ExcelProperty("酒店ID")
    @ColumnWidth(20)
    private String hotelId;
    /**
     * 可见模式（1-白名单 2-黑名单）
     */
    @ExcelProperty("类型")
    @ColumnWidth(20)
    private String availableType;
    /**
     * 错误描述
     */
    @ExcelProperty("错误描述")
    @ColumnWidth(40)
    private String errorDesc;
}
