package com.tiangong.product.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 供应商酒店黑白名单分页响应DTO
 * 服务层返回对象
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProOrgHotelAvailableRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 类型（1-白名单 2-黑名单）
     */
    private Integer availableType;
}
