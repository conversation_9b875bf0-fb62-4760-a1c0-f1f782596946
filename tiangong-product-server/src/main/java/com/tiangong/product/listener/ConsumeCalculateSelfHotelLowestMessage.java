package com.tiangong.product.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.product.domain.resp.HotelSaleInfoResp;
import com.tiangong.product.service.ProductSaleService;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

/**
 *【消费类】计算自签酒店起价
 */
@Component
@Slf4j
public class ConsumeCalculateSelfHotelLowestMessage implements MessageListener {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ProductSaleService productSaleService;

    @Override
    public void onMessage(Message message, byte[] bytes) {
        RedisSerializer<String> valueSerializer = stringRedisTemplate.getStringSerializer();
        String data = valueSerializer.deserialize(message.getBody());
        // 收到刷新消息时重新加载数据
        if (StrUtilX.isNotEmpty(data)) {
            HotelSaleInfoResp resp = JSON.parseObject(data, new TypeReference<HotelSaleInfoResp>() {
            });

            String lockKey = "CALCULATE_SELF_HOTEL_LOWEST_PRICE_LOCK:" + resp.getLogId();
            try {
                // 获取分布式锁
                Boolean isLocked = stringRedisTemplate.execute((RedisCallback<Boolean>) connection ->
                        connection.setNX(lockKey.getBytes(), "true".getBytes()));

                if (isLocked != null && isLocked) {
                    productSaleService.calculateSelfHotelLowestPrice(resp.getRespList());
                }
            } catch (Exception e) {
                log.error("【消费类】计算自签酒店起价异常：", e);
            } finally {
                // 释放分布式锁
                stringRedisTemplate.delete(lockKey);
            }
        }
    }
}
