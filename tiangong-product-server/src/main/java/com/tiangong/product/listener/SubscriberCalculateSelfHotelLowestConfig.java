package com.tiangong.product.listener;

import com.tiangong.keys.RedisKey;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * 队列监听配置
 */
@Configuration
@AutoConfigureAfter({ConsumeCalculateSelfHotelLowestMessage.class})
public class SubscriberCalculateSelfHotelLowestConfig {

    @Bean
    public MessageListenerAdapter getCalculateSelfHotelLowestMessageListenerAdapter(ConsumeCalculateSelfHotelLowestMessage automaticReportStatistics) {
        return new MessageListenerAdapter(automaticReportStatistics);
    }

    @Bean
    public RedisMessageListenerContainer getCalculateSelfHotelLowestMessageListenerContainer(RedisConnectionFactory redisConnectionFactory, @Qualifier("getCalculateSelfHotelLowestMessageListenerAdapter") MessageListenerAdapter messageListenerAdapter) {
        RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
        redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
        redisMessageListenerContainer.addMessageListener(messageListenerAdapter, new PatternTopic(RedisKey.CALCULATE_SELF_HOTEL_LOWEST));
        return redisMessageListenerContainer;
    }

}
