package com.tiangong.product.listener;

import com.tiangong.keys.RedisKey;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * 队列监听配置
 */
@Configuration
@AutoConfigureAfter({ConsumeRateMessage.class})
public class SubscriberRateConfig {

    @Bean
    public MessageListenerAdapter getRateMessageListenerAdapter(ConsumeRateMessage automaticReportStatistics) {
        return new MessageListenerAdapter(automaticReportStatistics);
    }

    @Bean
    public RedisMessageListenerContainer getRateMessageListenerContainer(RedisConnectionFactory redisConnectionFactory, @Qualifier("getRateMessageListenerAdapter") MessageListenerAdapter messageListenerAdapter) {
        RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
        redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
        redisMessageListenerContainer.addMessageListener(messageListenerAdapter, new PatternTopic(RedisKey.REFRESH_RATE_INFO));
        return redisMessageListenerContainer;
    }

}
