package com.tiangong.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableAddDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailablePageDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableRespDTO;
import com.tiangong.product.domain.entity.ProOrgHotelAvailableEntity;
import com.tiangong.product.dto.UploadRespVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 供应商酒店黑白名单表
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ProOrgHotelAvailableService {

    /**
     * 分页查询供应商酒店黑白名单列表
     */
    PaginationSupportDTO<ProOrgHotelAvailableRespDTO> queryProOrgHotelAvailablePage(ProOrgHotelAvailablePageDTO dto);

    /**
     * 新增供应商酒店黑白名单
     */
    void addProOrgHotelAvailable(ProOrgHotelAvailableAddDTO dto);

    /**
     * 删除供应商酒店黑白名单
     */
    void deleteProOrgHotelAvailable(Integer id);

    /**
     * 文件导入
     *
     * @param file 文件流
     * @return
     */
    UploadRespVO uploadFile(MultipartFile file,String createBy);

    /**
     * 根据酒店黑白名单规则过滤供应商列表
     *
     * @param hotelId 酒店ID
     * @param supplierCodes 待过滤的供应商编码列表
     * @return 过滤后的供应商编码列表
     */
    List<String> filterSuppliersByHotelAvailableRule(Long hotelId, List<String> supplierCodes);

    /**
     * 重置酒店黑白名单缓存
     * 清空所有酒店黑白名单相关缓存并重新加载
     */
    void resetHotelAvailableCache();

}
