package com.tiangong.product.service.impl;

import com.tiangong.product.domain.resp.HotelProductIdsResponse;
import com.tiangong.product.domain.resp.HotelSaleInfoItemResp;
import com.tiangong.product.domain.resp.QueryProductIdsRequest;
import com.tiangong.product.mapper.ProductInfoMapper;
import com.tiangong.product.service.ProductInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: tiangong
 * @ClassName ProductInfoServiceImpl
 * @description:
 * @author: 湫
 * @create: 2024/09/20/ 12:44
 * @Version 1.0
 **/

@Slf4j
@Service
public class ProductInfoServiceImpl implements ProductInfoService {

    @Autowired
    private ProductInfoMapper productInfoMapper;

    @Override
    public List<HotelProductIdsResponse> queryProductIdsByHotelId(QueryProductIdsRequest queryProductIdsRequest) {
        return productInfoMapper.queryProductIdsByHotelId(queryProductIdsRequest);
    }

    @Override
    public List<HotelSaleInfoItemResp> selectProductHotel(QueryProductIdsRequest queryProductIdsRequest) {
        return productInfoMapper.selectProductHotel(queryProductIdsRequest);
    }
}
