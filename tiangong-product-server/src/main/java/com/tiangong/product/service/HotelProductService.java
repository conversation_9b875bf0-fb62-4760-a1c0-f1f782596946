package com.tiangong.product.service;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.DhubProductDetailInfoResponse;
import com.tiangong.product.domain.req.HotelTimeZoneRequest;
import com.tiangong.product.domain.resp.HotelTimeZoneResp;


public interface HotelProductService {

    /**
     * 酒店实时产品查询
     */
    ResponseResult<DhubProductDetailInfoResponse> queryHotelProductDetail(ProductDetailRequest request);

    /**
     * 查询酒店时区
     */
    Response<HotelTimeZoneResp> queryHotelTimeZone(HotelTimeZoneRequest request);
}