package com.tiangong.product.service;

import com.github.pagehelper.PageInfo;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.product.domain.resp.HotelSaleInfoItemResp;
import com.tiangong.product.dto.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 11:20
 * @Description: 产品销售
 */
public interface ProductSaleService {

    /**
     * 查询酒店列表
     */
    Response<PaginationSupportDTO<HotelProductsDTO>> queryHotelList(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 查询产品售价列表
     */
    Response<ProductSalePriceDetailDTO> querySalePriceList(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 单产品上下架
     */
    void singleProductModifySaleStatus(Map<String, String> requestMap);

    /**
     * 单天调整售价
     */
    void dailyModifySalePrice(Map<String, String> requestMap);

    /**
     * 批量调整售价
     */
    void batchModifySalePrice(BatchSaleDTO batchSaleDTO);

    /**
     * 批量调整售卖状态
     */
    void batchModifySaleStatus(BatchSaleDTO batchSaleDTO);


    /**
     * 查询产品详情供订单使用
     */
    Response<List<ProductSalePriceItemDTO>> queryOrderProductPrice(@RequestBody QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 查询产品销售日志
     */
    PageInfo<ProductSaleLogDTO> queryProductSaleLogList(Map<String, String> requestMap);

    /**
     * 分解每日加幅数据
     */
    Response<String> resolveDayIncrease(String startDate, String endDate);

    /**
     * 将售价信息推送到对应的渠道
     */
    void updateSalePrice(UpdateSalePriceDTO updateSalePriceDTO);

    /**
     * 计算分销商可售酒店列表
     */
    void calculateAgentAvailableHotel(String agentCode, List<String> hotelIdList);

    /**
     * 处理分销商可售酒店
     */
    void disposeAgentAvailableHotel(String agentCode, List<String> supplyCodes);

    /**
     * 删除分销商无效可售酒店
     */
    void deleteAgentNotAvailHotel(String xTable, String date);

    /**
     * 计算酒店热度可订分数任务
     */
    void calculateHotelHeatBookableScoreTask(String param);

    /**
     * 计算酒店热度酒店平均房价任务
     */
    void calculateHotelHeatHotelAvgPriceTask(String param);

    /**
     * 初始化计算自签酒店起价任务
     */
    void initCalculateSelfHotelLowestPriceTask(String param);

    /**
     * 计算自签酒店起价
     */
    void calculateSelfHotelLowestPrice(List<HotelSaleInfoItemResp> respList);

    /**
     * 处理需要计算自签酒店起价
     */
    void calculateNeedSelfHotelLowestPrice(CalculateSelfHotelLowestDTO dto);
}
