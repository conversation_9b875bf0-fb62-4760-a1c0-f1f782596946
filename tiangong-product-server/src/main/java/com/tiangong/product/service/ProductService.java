package com.tiangong.product.service;

import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.dto.BasicHotelInfoDTO;
import com.tiangong.product.domain.resp.ProductDetailResponse;
import com.tiangong.product.dto.*;
import com.tiangong.product.resp.HotelLabelDTO;
import com.tiangong.product.resp.HotelPriceDTO;

import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 11:20
 * @Description: 产品
 */
public interface ProductService {

    /**
     * 判断是否配置了税规则
     * 存在 返回true 否则 返回false
     * @return
     */
    Boolean checkTaxRuleConfigExists(Long taxRuleConfigId);

    /**
     * 新增产品
     */
    Integer addProduct(ProductDTO productDTO);

    /**
     * 修改产品
     */
    void modifyProduct(ProductDTO productDTO);

    /**
     * 删除产品
     */
    DeleteProductResponseDTO deleteProduct(Map<String, String> requestMap);

    /**
     * 根据产品Id查询产品
     */
    ProductDTO queryProduct(Map<String, String> requestMap);

    /**
     * 查询酒店列表
     */
    List<ProductHotelDTO> queryHotelList(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 查询酒店产品详情
     */
    HotelProductsDTO queryHotelProducts(QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 单日修改产品价格、房态
     */
    void dailyModifyProductInfo(ProductDayQuotationDTO productDayQuotationDTO);

    /**
     * 查询产品销售日期税费详情
     */
    ProductSaleDateTaxDetailDTO queryProductSaleDateTaxDetail(QueryProductSaleDateTaxDetailDTO req);

    /**
     * 批量修改价格
     */
    void batchModifyBasePrice(List<BatchQuotationDTO> batchQuotationDTOList);

    /**
     * 批量修改配额、房态
     */
    void batchModifyRoomStatus(List<BatchQuotationDTO> batchQuotationDTOList);

    /**
     * 查询产品list给订单用
     */
    PaginationSupportDTO<ProductOrderQueryDTO> queryOrderProductList(ProductOrderQueryRequestDTO productOrderQueryRequestDTO);

    /**
     * 查询产品日志
     */
    PageInfo<ProductLogDTO> queryProductLogList(Map<String, String> requestMap);

    /**
     * 修改产品停售状态
     */
    void modifyOffShelveStatus(Map<String, String> requestMap);

    /**
     * 批量调整酒店房态
     */
    void batchModifyHotelProductStatus(BatchModifyHotelProductStatusDTO batchModifyHotelProductStatusDTO);

    /**
     * 查询产品当日条款
     */
    ProductRestrictDTO queryProductRestrict(Map<String, String> requestMap);

    /**
     * 查询产品条款信息
     */
    List<ProductDTO> queryProductRestrictList(QueryProductRestrictDTO queryProductRestrictDTO);

    /**
     * 根据id查询产品
     */
    ProductDTO queryProductById(Map<String, String> requestMap);

    /**
     * 初始化redis丢失数据
     */
    void initLostRedisProductBaseInfo(Map<String, String> requestMap);

    /**
     * 查询客户酒店标签
     */
    List<HotelLabelDTO> selectAgentHotelLabel(HotelPriceReq hotelPriceReq);
}
