package com.tiangong.product.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.product.domain.req.ProTaxRuleConfigAditReq;
import com.tiangong.product.domain.req.ProTaxRuleConfigPageReq;
import com.tiangong.product.domain.req.ProTaxRuleConfigQueryReq;
import com.tiangong.product.domain.req.ProTaxRuleConfigResp;

import java.util.Map;
import java.util.Set;

/**
 * @program: tiangong
 * @ClassName ProTaxRuleConfigService
 * @description: 税费规则配置
 * @author: 湫
 * @create: 2024/09/02/ 17:03
 * @Version 1.0
 **/

public interface ProTaxRuleConfigService {

    /**
     * 新增税费规则配置
     * @param req
     */
    Integer proTaxRuleConfigAdd(ProTaxRuleConfigAditReq req);


    /**
     * 修改税费规则配置
     * @param req
     */
    Integer proTaxRuleConfigEdit(ProTaxRuleConfigAditReq req);

    //删除

    /**
     * 修改税费规则配置
     * @param req
     */
    Integer proTaxRuleConfigDelete(ProTaxRuleConfigQueryReq req);

    // 分页查询 列表

    /**
     * 可见性表列表（分页）
     */
    PaginationSupportDTO<ProTaxRuleConfigResp> proTaxRuleConfigPage(ProTaxRuleConfigPageReq req);

    /**
     * 获取详情
     * @param req
     * @return
     */
    ProTaxRuleConfigResp proTaxRuleConfigDetail(ProTaxRuleConfigQueryReq req);

    /**
     * 根据税费规则配置id查询 列表查询详情
     * @param taxRuleConfigIds
     * @return key 税费规则id
     *  value 税费规则明细
     */
    Map<Long,ProTaxRuleConfigResp> proTaxRuleConfigDetailList(Set<Long> taxRuleConfigIds);

    /**
     * 初始化缓存
     */
    void proTaxRuleConfigCacheInit();
}
