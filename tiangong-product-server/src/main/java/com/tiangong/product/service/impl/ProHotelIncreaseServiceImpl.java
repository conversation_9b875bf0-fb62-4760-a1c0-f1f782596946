package com.tiangong.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.product.SupplyHotelIncrease;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.domain.entity.ProHotelIncreaseEntity;
import com.tiangong.product.domain.req.*;
import com.tiangong.product.domain.resp.ProHotelIncreasePageResp;
import com.tiangong.product.mapper.ProHotelIncreaseMapper;
import com.tiangong.product.service.ProHotelIncreaseService;
import com.tiangong.product.service.ProOrgAvailableService;
import com.tiangong.redis.core.RedisTemplateX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @ClassName: ProHotelIncreaseServiceImpl
 * @Author: xubohao
 * @CreateDate: 2025/4/22 11:23
 * @Version: 1.0
 */

@Service
public class ProHotelIncreaseServiceImpl extends ServiceImpl<ProHotelIncreaseMapper, ProHotelIncreaseEntity> implements ProHotelIncreaseService {

    @Autowired
    private ProHotelIncreaseMapper proHotelIncreaseMapper;

    @Autowired
    private ProOrgAvailableService proOrgAvailableService;

    @Autowired
    private HotelRemote hotelRemote;

    /**
     * 新增
     *
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> proHotelIncreaseAdd(ProHotelIncreaseAddReq request) {

        //校验数据
        if (StrUtil.isBlank(request.getAgentCode()) || StrUtil.isBlank(request.getSupplierCode())
                || StrUtil.isBlank(request.getHotelIds()) || Objects.isNull(request.getAdjustmentType())
                || Objects.isNull(request.getModifiedAmt())) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        String[] hotelIdStrList = request.getHotelIds().split("[,，]");
        //每次最多批量导入100个酒店
        if (hotelIdStrList.length > 100) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        //校验客户和供应商编码
        List<String> availableSupplierList = proOrgAvailableService.getAvailableSupplierByAgentCode(request.getAgentCode(), request.getSupplierCode());
        if (CollUtil.isEmpty(availableSupplierList) || !availableSupplierList.contains(request.getSupplierCode())) {
            Map<String, String> mapTmp = new HashMap<>();
            mapTmp.put("errorMessage", "此供应商对此客户不可用");
            return Response.success(mapTmp);
        }

        Set<Long> hotelIdsSet = new HashSet<>();
        for (String hotelIdStr : hotelIdStrList) {
            try {
                Long hotelId = Long.valueOf(hotelIdStr);
                //重复酒店
                if (hotelIdsSet.contains(hotelId)) {
                    Map<String, String> mapTmp = new HashMap<>();
                    mapTmp.put("errorMessage", "重复酒店:" + hotelIdStr);
                    return Response.success(mapTmp);
                }
                //酒店未重复
                hotelIdsSet.add(hotelId);
            } catch (Exception e) {
                //异常数据
                Map<String, String> mapTmp = new HashMap<>();
                mapTmp.put("errorMessage", "错误酒店数据:" + hotelIdStr);
                return Response.success(mapTmp);
            }
        }

        //查询基础信息的ES获取对应的酒店信息
        DestinationReq destinationReq = new DestinationReq();
        destinationReq.setLanguage(request.getLanguage());
        destinationReq.setDataSize(hotelIdsSet.size());
        destinationReq.setHotelIds(hotelIdsSet);
        Response<List<EsHotelDto>> esHotelDTOResponse = hotelRemote.searchDestinationHotel(destinationReq);
        if (esHotelDTOResponse.isError() || CollUtil.isEmpty(esHotelDTOResponse.getModel())) {
            Map<String, String> mapTmp = new HashMap<>();
            mapTmp.put("errorMessage", "无效酒店列表");
            return Response.success(mapTmp);
        }
        Set<Long> esHotelIds = esHotelDTOResponse.getModel().stream().map(EsHotelDto::getHotelId).collect(Collectors.toSet());
        //校验数据是否存在
        List<ProHotelIncreaseEntity> list = this.list(new LambdaQueryWrapper<ProHotelIncreaseEntity>()
                .eq(ProHotelIncreaseEntity::getAgentCode, request.getAgentCode())
                .eq(ProHotelIncreaseEntity::getSupplierCode, request.getSupplierCode())
                .in(ProHotelIncreaseEntity::getHotelId, hotelIdsSet));
        if (CollUtil.isNotEmpty(list)) {
            Map<String, String> mapTmp = new HashMap<>();
            mapTmp.put("errorMessage", "列表中此供应商已配置酒店（ID：" + list.get(0).getHotelId() + "）的加幅，请删除后重新提交");
            return Response.success(mapTmp);
        }
        //保存数据
        Map<String, String> map = new HashMap<>();
        List<ProHotelIncreaseEntity> insertList = new ArrayList<>();
        for (Long hotelId : hotelIdsSet) {
            //校验酒店Id是否合法
            if (!esHotelIds.contains(hotelId)) {
                //无效酒店Id
                Map<String, String> mapTmp = new HashMap<>();
                mapTmp.put("errorMessage", "无效酒店id:" + hotelId);
                return Response.success(mapTmp);
            }
            ProHotelIncreaseEntity proHotelIncreaseEntity = new ProHotelIncreaseEntity();
            proHotelIncreaseEntity.setAgentCode(request.getAgentCode());
            proHotelIncreaseEntity.setSupplierCode(request.getSupplierCode());
            proHotelIncreaseEntity.setHotelId(hotelId);
            proHotelIncreaseEntity.setAdjustmentType(request.getAdjustmentType());
            proHotelIncreaseEntity.setModifiedAmt(request.getModifiedAmt());
            proHotelIncreaseEntity.setLowestIncreaseAmt(request.getLowestIncreaseAmt());
            proHotelIncreaseEntity.setCreatedBy(request.getCreatedBy());
            proHotelIncreaseEntity.setCreatedDt(new Date());
            proHotelIncreaseEntity.setUpdatedBy(request.getUpdatedBy());
            proHotelIncreaseEntity.setUpdatedDt(new Date());
            insertList.add(proHotelIncreaseEntity);

            SupplyHotelIncrease supplyHotelIncrease = new SupplyHotelIncrease();
            supplyHotelIncrease.setAdjustmentType(request.getAdjustmentType());
            supplyHotelIncrease.setModifiedAmt(request.getModifiedAmt());
            supplyHotelIncrease.setLowestIncreaseAmt(request.getLowestIncreaseAmt());
            supplyHotelIncrease.setSupplyCode(request.getSupplierCode());
            supplyHotelIncrease.setHotelId(hotelId);

            map.put(request.getAgentCode().concat("_").concat(request.getSupplierCode()).concat("_").concat(hotelId.toString()), JSONUtil.toJsonStr(supplyHotelIncrease));
        }

        //新增数据
        if (CollUtil.isNotEmpty(insertList)) {
            this.saveBatch(insertList);
        }
        // 更新缓存
        RedisTemplateX.hPutAll(RedisKey.AGENT_SUPPLY_HOTEL_INCREASE_KEY, map);
        return Response.success();
    }

    /**
     * 删除
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void proHotelIncreaseDel(ProHotelIncreaseDelReq request) {
        ProHotelIncreaseEntity proHotelIncreaseEntity = new ProHotelIncreaseEntity();
        proHotelIncreaseEntity.setId(request.getId());
        int count = this.count(new LambdaQueryWrapper<ProHotelIncreaseEntity>()
                .eq(ProHotelIncreaseEntity::getId, request.getId())
                .eq(ProHotelIncreaseEntity::getAgentCode, request.getAgentCode())
                .eq(ProHotelIncreaseEntity::getSupplierCode, request.getSupplierCode())
                .eq(ProHotelIncreaseEntity::getHotelId, request.getHotelId()));
        //错误数据
        if (count == 0) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
        if (this.removeById(request.getId())) {
            // 删除缓存
            RedisTemplateX.hDelete(RedisKey.AGENT_SUPPLY_HOTEL_INCREASE_KEY, request.getAgentCode().concat("_").concat(request.getSupplierCode()).concat("_").concat(request.getHotelId().toString()));
        }
    }

    /**
     * 编辑
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void proHotelIncreaseEdit(ProHotelIncreaseEditReq request) {
        //校验数据
        if (Objects.isNull(request.getId()) || StrUtil.isBlank(request.getAgentCode())
                || StrUtil.isBlank(request.getOldSupplierCode()) || Objects.isNull(request.getOldHotelId())
                || StrUtil.isBlank(request.getSupplierCode()) || Objects.isNull(request.getHotelId())
                || Objects.isNull(request.getAdjustmentType()) || Objects.isNull(request.getModifiedAmt())) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
        ProHotelIncreaseEntity proHotelIncreaseEntity = this.lambdaQuery()
                .eq(ProHotelIncreaseEntity::getAgentCode, request.getAgentCode())
                .eq(ProHotelIncreaseEntity::getSupplierCode, request.getSupplierCode())
                .eq(ProHotelIncreaseEntity::getHotelId, request.getHotelId())
                .one();
        //已存在相同的数据
        if (Objects.nonNull(proHotelIncreaseEntity)
                && !Objects.equals(request.getId(), proHotelIncreaseEntity.getId())) {
            throw new SysException(ParamErrorEnum.THE_SAME_DATA_EXISTS);
        }
        //校验客户和供应商编码
        List<String> availableSupplierList = proOrgAvailableService.getAvailableSupplierByAgentCode(request.getAgentCode(), request.getSupplierCode());
        if (CollUtil.isEmpty(availableSupplierList) || !availableSupplierList.contains(request.getSupplierCode())) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        //校验酒店id是否有效
        Set<Long> hotelIdsSet = new HashSet<>();
        hotelIdsSet.add(request.getHotelId());
        DestinationReq destinationReq = new DestinationReq();
        destinationReq.setLanguage(request.getLanguage());
        destinationReq.setHotelIds(hotelIdsSet);
        Response<List<EsHotelDto>> esHotelDTOResponse = hotelRemote.searchDestinationHotel(destinationReq);
        if (esHotelDTOResponse.isError() || CollUtil.isEmpty(esHotelDTOResponse.getModel())) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
        Set<Long> esHotelIds = esHotelDTOResponse.getModel().stream().map(EsHotelDto::getHotelId).collect(Collectors.toSet());
        if (!esHotelIds.contains(request.getHotelId())) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        boolean updateRes = this.update(null, new LambdaUpdateWrapper<ProHotelIncreaseEntity>()
                .eq(ProHotelIncreaseEntity::getId, request.getId())
                .eq(ProHotelIncreaseEntity::getAgentCode, request.getAgentCode())
                .eq(ProHotelIncreaseEntity::getSupplierCode, request.getOldSupplierCode())
                .eq(ProHotelIncreaseEntity::getHotelId, request.getOldHotelId())
                .set(ProHotelIncreaseEntity::getSupplierCode, request.getSupplierCode())
                .set(ProHotelIncreaseEntity::getHotelId, request.getHotelId())
                .set(ProHotelIncreaseEntity::getAdjustmentType, request.getAdjustmentType())
                .set(ProHotelIncreaseEntity::getModifiedAmt, request.getModifiedAmt())
                .set(ProHotelIncreaseEntity::getLowestIncreaseAmt, request.getLowestIncreaseAmt())
                .set(ProHotelIncreaseEntity::getUpdatedBy, request.getUpdatedBy())
                .set(ProHotelIncreaseEntity::getUpdatedDt, new Date()));
        if (!updateRes) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        // 更新缓存
        SupplyHotelIncrease supplyHotelIncrease = new SupplyHotelIncrease();
        supplyHotelIncrease.setAdjustmentType(request.getAdjustmentType());
        supplyHotelIncrease.setModifiedAmt(request.getModifiedAmt());
        supplyHotelIncrease.setLowestIncreaseAmt(request.getLowestIncreaseAmt());
        supplyHotelIncrease.setSupplyCode(request.getSupplierCode());
        supplyHotelIncrease.setHotelId(request.getHotelId());
        String key = request.getAgentCode().concat("_").concat(request.getSupplierCode()).concat("_").concat(request.getHotelId().toString());
        //把老的缓存数据删除
        RedisTemplateX.hDelete(RedisKey.AGENT_SUPPLY_HOTEL_INCREASE_KEY, request.getAgentCode().concat("_").concat(request.getOldSupplierCode()).concat("_").concat(request.getOldHotelId().toString()));
        RedisTemplateX.hPut(RedisKey.AGENT_SUPPLY_HOTEL_INCREASE_KEY, key, JSONUtil.toJsonStr(supplyHotelIncrease));
    }

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @Override
    public PaginationSupportDTO<ProHotelIncreasePageResp> proHotelIncreasePage(ProHotelIncreasePageReq request) {
        //校验数据
        if (StrUtil.isBlank(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        List<String> availableSupplierList = new ArrayList<>();
        //查询全部供应商
        if (StrUtil.isBlank(request.getSupplierCode())) {
            availableSupplierList = proOrgAvailableService.getAvailableSupplierByAgentCode(request.getAgentCode(), request.getSupplierCode());
        } else {
            //指定某个供应商
            availableSupplierList.add(request.getSupplierCode());
        }
        if (CollUtil.isEmpty(availableSupplierList)) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        //分页查询数据
        IPage<ProHotelIncreaseEntity> iPage = new Page<>(request.getCurrentPage(), request.getPageSize());
        IPage<ProHotelIncreasePageResp> page = proHotelIncreaseMapper.proHotelIncreasePage(iPage, request);
        //拼装酒店名
        if (CollUtil.isNotEmpty(page.getRecords())) {
            Map<Long, String> hotelIdAndHotelName = new HashMap<>();
            Set<Long> hotelIds = page.getRecords().stream().map(ProHotelIncreasePageResp::getHotelId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(hotelIds)) {
                //查询基础信息的ES获取对应的酒店信息
                DestinationReq destinationReq = new DestinationReq();
                destinationReq.setLanguage(request.getLanguage());
                destinationReq.setDataSize(hotelIds.size());
                destinationReq.setHotelIds(hotelIds);
                Response<List<EsHotelDto>> esHotelDTOResponse = hotelRemote.searchDestinationHotel(destinationReq);
                if (esHotelDTOResponse.isSuccess() && CollUtil.isNotEmpty(esHotelDTOResponse.getModel())) {
                    hotelIdAndHotelName = esHotelDTOResponse.getModel().stream().collect(Collectors.toMap(EsHotelDto::getHotelId, EsHotelDto::getHotelName, (key1, key2) -> key2));
                }
            }
            for (ProHotelIncreasePageResp proHotelIncreasePageResp : page.getRecords()) {
                proHotelIncreasePageResp.setHotelName(hotelIdAndHotelName.get(proHotelIncreasePageResp.getHotelId()));
            }
        }

        //拼装返回结果
        PaginationSupportDTO<ProHotelIncreasePageResp> proHotelIncreasePage = new PaginationSupportDTO<>();
        proHotelIncreasePage = proHotelIncreasePage.getPaginationSupportDTO(page);
        return proHotelIncreasePage;
    }

    /**
     * 批量修改
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void proHotelIncreaseBatchEdit(ProHotelIncreaseBatchEditReq request) {
        //校验数据
        if (CollUtil.isEmpty(request.getIds())
                || Objects.isNull(request.getAdjustmentType())
                || Objects.isNull(request.getModifiedAmt())) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        //查询原先的值
        List<ProHotelIncreaseEntity> list = this.list(new LambdaQueryWrapper<ProHotelIncreaseEntity>().in(ProHotelIncreaseEntity::getId, request.getIds()));
        if (CollUtil.isEmpty(list)) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        Map<String, String> map = new HashMap();
        //更新数据
        for (ProHotelIncreaseEntity proHotelIncreaseEntity : list) {
            SupplyHotelIncrease supplyHotelIncrease = new SupplyHotelIncrease();
            supplyHotelIncrease.setAdjustmentType(request.getAdjustmentType());
            supplyHotelIncrease.setModifiedAmt(request.getModifiedAmt());
            supplyHotelIncrease.setLowestIncreaseAmt(request.getLowestIncreaseAmt());
            supplyHotelIncrease.setSupplyCode(proHotelIncreaseEntity.getSupplierCode());
            supplyHotelIncrease.setHotelId(proHotelIncreaseEntity.getHotelId());
            map.put(proHotelIncreaseEntity.getAgentCode().concat("_").concat(proHotelIncreaseEntity.getSupplierCode()).concat("_").concat(proHotelIncreaseEntity.getHotelId().toString()), JSONUtil.toJsonStr(supplyHotelIncrease));
        }

        boolean updateRes = this.update(null, new LambdaUpdateWrapper<ProHotelIncreaseEntity>()
                .in(ProHotelIncreaseEntity::getId, request.getIds())
                .set(ProHotelIncreaseEntity::getAdjustmentType, request.getAdjustmentType())
                .set(ProHotelIncreaseEntity::getModifiedAmt, request.getModifiedAmt())
                .set(ProHotelIncreaseEntity::getLowestIncreaseAmt, request.getLowestIncreaseAmt()));
        if (!updateRes) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
        RedisTemplateX.hPutAll(RedisKey.AGENT_SUPPLY_HOTEL_INCREASE_KEY, map);
    }

}
