package com.tiangong.product.service.impl;


import com.alibaba.fastjson.JSON;
import com.tiangong.dis.dto.IncrementDTO;
import com.tiangong.dis.dto.IncrementObjectDTO;
import com.tiangong.dis.remote.InitRemote;
import com.tiangong.enums.IncrementTypeEnum;
import com.tiangong.product.domain.DebitedQuotaPO;
import com.tiangong.product.domain.QuotaPO;
import com.tiangong.product.dto.ProductDayQuotationDTO;
import com.tiangong.product.dto.QuotaDTO;
import com.tiangong.product.mapper.DebitedQuotaMapper;
import com.tiangong.product.mapper.QuotaMapper;
import com.tiangong.product.service.QuotaService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service("quotaService")
public class QuotaServicelmpl implements QuotaService {

    @Autowired
    private QuotaMapper quotaMapper;

    @Autowired
    private DebitedQuotaMapper debitedQuotaMapper;

    @Autowired
    private InitRemote initRemote;

    @Override
    public void modifyQuota(QuotaDTO request) {
        String quotaAccountId = quotaMapper.queryQuotaAccountId(request.getProductId());//通过供应商编码查询配额账号ID
        if (StrUtilX.isNotEmpty(quotaAccountId)) {
            Map params = new HashMap<>();
            List saleDate = new ArrayList();
            String[] saleDates = request.getSaleDate().split(",");
            for(int i=0; i<saleDates.length-1;i++){//减掉最后一天，07-23 - 07-24 为一天
                saleDate.add(saleDates[i]);
            }

            //解决只传入一个日期，逻辑需要优化
            if (saleDate.size() < 1) {
                saleDate.add(saleDates[0]);
            }

            params.put("quotaAccountId", quotaAccountId);
            params.put("saleDate", saleDate);
            List<QuotaPO> quotas = quotaMapper.queryQuota(params);
            List<DebitedQuotaPO> debitedQuotaPOS = new ArrayList<>();
            Date date = new Date();
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd  HH:mm:ss");
            int quotai = 0;
            if (CollUtilX.isNotEmpty(quotas)) {
                Set<String> saleDateList = new HashSet<>();
                List<ProductDayQuotationDTO> productDayQuotationDTOS = new ArrayList<>();
                for (QuotaPO quota : quotas) {
                    if (request.getQuota() > 0) { //判断是增加或者扣除
                        quotai = request.getQuota();
                    } else {//为负数就变为整数去做比较，取剩余的负数
                        quotai = (-request.getQuota() > quota.getRemainingQuota()) ? -quota.getRemainingQuota() : request.getQuota();//对比如果当前数不够扣除，就扣除剩余数
                    }
                    QuotaPO quotaPO = new QuotaPO();
                    BeanUtils.copyProperties(quota, quotaPO);
                    quotaPO.setQuotaId(quota.getQuotaId());
                    quotaPO.setRemainingQuota(quota.getRemainingQuota() + quotai);//正负得负，正正得正
                    quotaMapper.updateByPrimaryKey(quotaPO);


                    DebitedQuotaPO debitedQuotaPO = new DebitedQuotaPO();
                    debitedQuotaPO.setOrderCode(request.getOrderCode());
                    debitedQuotaPO.setOrderId(request.getOrderId());
                    debitedQuotaPO.setProductId(request.getProductId());
                    debitedQuotaPO.setQuota(quotai);
                    debitedQuotaPO.setSupplyOrderId(request.getSupplyOrderId());
                    debitedQuotaPO.setSupplyOrderCode(request.getSupplyOrderCode());
                    debitedQuotaPO.setSaleDate(quota.getSaleDate());
                    debitedQuotaPO.setQuotaAccountId(quota.getQuotaAccountId());
                    debitedQuotaPO.setCreatedBy("");
                    debitedQuotaPO.setCreatedDt(format.format(date));
                    debitedQuotaPO.setType(request.getQuota() > 0 ? 2 : 1);//1：扣配额 2：退配额
                    debitedQuotaPOS.add(debitedQuotaPO);

                    saleDateList.add(DateUtilX.dateToString(DateUtilX.stringToDate(quota.getSaleDate())));

                    ProductDayQuotationDTO productDayQuotationDTO = new ProductDayQuotationDTO();
                    productDayQuotationDTO.setSaleDate(DateUtilX.dateToString(DateUtilX.stringToDate(quota.getSaleDate())));
                    productDayQuotationDTO.setModifiedQuota(request.getQuota());
                    productDayQuotationDTO.setQuotaAdjustmentType(request.getQuota() > 0 ? 0 : 1);
                    productDayQuotationDTO.setProductId(request.getProductId());
                    productDayQuotationDTO.setType(1);
                    productDayQuotationDTOS.add(productDayQuotationDTO);
                }

                debitedQuotaMapper.insertDebitedQuota(debitedQuotaPOS);

                IncrementDTO incrementDTO = new IncrementDTO();
                incrementDTO.setType(IncrementTypeEnum.ROOM_STATUS.no);
                incrementDTO.setProductId(request.getProductId());
                incrementDTO.setSaleDate(saleDateList);

                IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
                incrementObjectDTO.setIncrementList(new ArrayList<>(Collections.singletonList(JSON.toJSONString(incrementDTO))));
                incrementObjectDTO.setObject(productDayQuotationDTOS);

                initRemote.initBasePriceAndRoomStatus(incrementObjectDTO);
            }
        }
    }
}
