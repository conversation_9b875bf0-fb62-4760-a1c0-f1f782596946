package com.tiangong.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.enums.AvailableEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.organization.remote.dto.SupplierBaseInfoReq;
import com.tiangong.organization.remote.dto.SupplierBaseInfoResp;
import com.tiangong.product.convert.CommonConvert;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableAddDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableBaseDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailablePageDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableRespDTO;
import com.tiangong.product.domain.entity.ProOrgHotelAvailableEntity;
import com.tiangong.product.domain.vo.ProOrgHotelAvailableExcelErrorVO;
import com.tiangong.product.domain.vo.ProOrgHotelAvailableExcelVO;
import com.tiangong.product.dto.UploadRespVO;
import com.tiangong.product.enums.AvailableTypeEnum;
import com.tiangong.product.mapper.ProOrgHotelAvailableMapper;
import com.tiangong.product.service.ProOrgHotelAvailableService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.FileUpUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 供应商酒店黑白名单表
 *
 * <AUTHOR>
 * @date 2025/6/9
 * @Description 供应商酒店黑白名单业务逻辑实现，包含缓存管理和过滤功能
 */
@Slf4j
@Service
public class ProOrgHotelAvailableServiceImpl implements ProOrgHotelAvailableService {

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private ProOrgHotelAvailableMapper baseMapper;

    @Autowired
    private AgentRemote agentRemote;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private ProOrgHotelAvailableServiceImpl proOrgHotelAvailableService;

    private static final List<String> EXCEL_HEADER = Arrays.asList("供应商编码", "酒店ID", "类型");

    private static final Integer MAX_ROW = 5000;

    private static final String PRO_ORG_HOTEL_AVAILABLE_CACHE_KEY = "PRO_ORG_HOTEL_AVAILABLE_DATA:";

    private static final String EMPTY_CACHE_VALUE = "[]";

    private static final TypeReference<List<ProOrgHotelAvailableBaseDTO>> PRO_ORG_HOTEL_AVAILABLE_TYPE_REF =
            new TypeReference<List<ProOrgHotelAvailableBaseDTO>>() {
            };

    /**
     * 分页查询供应商酒店黑白名单列表
     */
    @Override
    public PaginationSupportDTO<ProOrgHotelAvailableRespDTO> queryProOrgHotelAvailablePage(ProOrgHotelAvailablePageDTO dto) {
        // 1. 执行分页查询获取基础数据
        IPage<ProOrgHotelAvailableRespDTO> page = baseMapper.queryProOrgHotelAvailablePage(new Page<>(dto.getCurrentPage(), dto.getPageSize()), dto);
        PaginationSupportDTO<ProOrgHotelAvailableRespDTO> pageInfo = new PaginationSupportDTO<>();
        // 2. 如果查询结果为空，直接返回空的分页对象
        List<ProOrgHotelAvailableRespDTO> list = page.getRecords();
        if (CollUtilX.isEmpty(list)) {
            return pageInfo;
        }

        // 3. 从查询结果中提取所有酒店ID并去重
        Set<Long> hotelIds = new HashSet<>();
        for (ProOrgHotelAvailableRespDTO item : list) {
            hotelIds.add(item.getHotelId());
        }
        // 4. 如果有酒店ID，调用searchDestinationHotel方法查询酒店信息
        Map<Long, EsHotelDto> hotelInfoMap = queryHotelInfoMap(hotelIds);
        // 5. 遍历原查询结果，根据Map中的酒店信息为每条记录赋值酒店相关字段
        list.forEach(item -> {
            //设置酒店信息
            EsHotelDto hotelInfo = hotelInfoMap.get(item.getHotelId());
            if (hotelInfo != null) {
                item.setHotelName(hotelInfo.getHotelName());
                item.setCountryCode(hotelInfo.getCountryCode());
                item.setCountryName(hotelInfo.getCountryName());
                item.setProvinceCode(hotelInfo.getProvinceCode());
                item.setProvinceName(hotelInfo.getProvinceName());
                item.setCityCode(hotelInfo.getCityCode());
                item.setCityName(hotelInfo.getCityName());
            }
        });
        // 6. 返回分页结果
        PaginationSupportDTO<ProOrgHotelAvailableRespDTO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(list);
        paginationSupportDTO.setCurrentPage((int) page.getCurrent());
        paginationSupportDTO.setPageSize((int) page.getSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage((int) page.getPages());
        return paginationSupportDTO;
    }

    /**
     * 查询酒店信息并转换为Map
     */
    private Map<Long, EsHotelDto> queryHotelInfoMap(Set<Long> hotelIds) {
        // 构建查询请求
        DestinationReq destinationReq = new DestinationReq();
        destinationReq.setHotelIds(hotelIds);
        destinationReq.setDataSize(hotelIds.size()); // 设置数据数量为酒店ID的数量
        destinationReq.setLanguage(httpServletRequest.getHeader(HttpConstant.Language));
        // 调用searchDestinationHotel方法
        Response<List<EsHotelDto>> response = hotelRemote.searchDestinationHotel(destinationReq);

        if (response == null || !response.isSuccess() || CollUtilX.isEmpty(response.getModel())) {
            return MapUtil.empty();
        }

        // 将结果转换为Map（以酒店ID为key）
        return response.getModel().stream()
                .filter(hotel -> hotel.getHotelId() != null)
                .collect(Collectors.toMap(
                        EsHotelDto::getHotelId,
                        e -> e,
                        (existing, replacement) -> existing // 如果有重复的key，保留第一个
                ));
    }

    /**
     * 新增供应商酒店黑白名单
     */
    @Override
    public void addProOrgHotelAvailable(ProOrgHotelAvailableAddDTO dto) {
        // 1. 参数校验
        validateAddParams(dto);

        // 2. 重复性检查
        checkDuplicate(dto);
        //校验hotelId合法性
        Map<Long, EsHotelDto> hotelDtoMap = queryHotelInfoMap(Collections.singleton(dto.getHotelId()));
        if (CollUtil.isEmpty(hotelDtoMap)) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_EXIST);
        }
        //校验supplierCode合法性
        Map<String, SupplierBaseInfoResp> supplierCodeMap = getSupplierCodeMap(Collections.singletonList(dto.getSupplierCode()));
        if (CollUtil.isEmpty(supplierCodeMap)) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_EXIST);
        }
        SupplierBaseInfoResp orgBaseInfoResp = supplierCodeMap.get(dto.getSupplierCode());
        if (!AvailableEnum.Start.key.equals(orgBaseInfoResp.getAvailableStatus())) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_ENABLE);
        }
        EsHotelDto esHotelDto = hotelDtoMap.get(dto.getHotelId());
        // 3. 数据入库
        ProOrgHotelAvailableEntity entity = CommonConvert.INSTANCE.convertToPO(dto);
        entity.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        entity.setCityCode(esHotelDto.getCityCode());
        entity.setId(null);

        proOrgHotelAvailableService.insertDb(dto, entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertDb(ProOrgHotelAvailableAddDTO dto, ProOrgHotelAvailableEntity entity) {
        baseMapper.insert(entity);
        //使用事务同步机制，确保在事务提交后更新缓存
        registerCacheUpdateAfterTransaction(Collections.singleton(dto.getSupplierCode()));
    }

    /**
     * 参数校验
     */
    private void validateAddParams(ProOrgHotelAvailableAddDTO dto) {
        // 酒店ID校验
        if (dto.getHotelId() == null) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_NULL);
        }

        // 供应商编码校验
        if (StrUtilX.isEmpty(dto.getSupplierCode())) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_ID_NOT_NULL);
        }

        // 黑白名单类型校验
        if (dto.getAvailableType() == null) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_TYPE_NOT_NULL);
        }

        // 黑白名单类型值校验（1-白名单 2-黑名单）
        if (AvailableTypeEnum.BLACK.getKey().equals(dto.getAvailableType()) && AvailableTypeEnum.WHITE.getKey().equals(dto.getAvailableType())) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_TYPE_INVALID);
        }
    }

    /**
     * 重复性检查（同一酒店+供应商只能存在一种黑白名单）
     */
    private void checkDuplicate(ProOrgHotelAvailableAddDTO dto) {
        List<ProOrgHotelAvailableEntity> exsitList = baseMapper.selectList(Wrappers.<ProOrgHotelAvailableEntity>lambdaQuery()
                .select(ProOrgHotelAvailableEntity::getAvailableType)
                .eq(ProOrgHotelAvailableEntity::getSupplierCode, dto.getSupplierCode())
                .eq(ProOrgHotelAvailableEntity::getHotelId, dto.getHotelId()).last("limit 1"));
        if (CollUtilX.isEmpty(exsitList)) {
            return;
        }
        ProOrgHotelAvailableEntity oldEntity = exsitList.get(0);
        if (oldEntity.getAvailableType().equals(dto.getAvailableType())) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_DUPLICATE);
        }
        if (AvailableTypeEnum.BLACK.getKey().equals(oldEntity.getAvailableType())) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_BLACK);
        }
        throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_WHITE);
    }

    /**
     * 删除供应商酒店黑白名单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProOrgHotelAvailable(Integer id) {
        ProOrgHotelAvailableEntity proOrgHotelAvailableEntity = baseMapper.selectById(id);
        if (proOrgHotelAvailableEntity == null) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_QUERY_ERROR);
        }
        baseMapper.deleteById(id);
        //使用事务同步机制，确保在事务提交后更新缓存
        registerCacheUpdateAfterTransaction(Collections.singleton(proOrgHotelAvailableEntity.getSupplierCode()));
    }

    @Override
    public UploadRespVO uploadFile(MultipartFile file, String createBy) {
        //1.导入前校验 无需记录错误 直接抛出异常
        //校验传入文件 数据条数,导入文件名称及格式
        validFileParam(file);
        //文件读取
        List<ProOrgHotelAvailableExcelVO> dataList = readFile(file);
        //2.导入后校验 需要记录错误到dataList中
        AtomicBoolean hasError = new AtomicBoolean(false); // 使用AtomicBoolean解决传递问题
        List<ProOrgHotelAvailableExcelErrorVO> errorVOList = validAndRecordError(dataList, hasError);
        //3.初始化待插入列表
        List<ProOrgHotelAvailableEntity> needSaveList = new ArrayList<>(dataList.size());
        String createDt = DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
        //4.校验hotelId与supplierCode合法性
        for (List<ProOrgHotelAvailableExcelErrorVO> excelErrorVOS : ListUtil.partition(errorVOList, 1000)) {
            // 批量检查数据库中的重复性
            checkBatchDuplicateAndSetError(excelErrorVOS, hasError);
            //校验hotelId合法性
            List<Long> hotelIds = excelErrorVOS.stream()
                    .filter(e -> StrUtil.isEmpty(e.getErrorDesc()) && StrUtil.isNotEmpty(e.getHotelId()))
                    .map(e->Long.parseLong(e.getHotelId())).distinct().collect(Collectors.toList());
            Map<Long, EsHotelDto> longEsHotelDtoMap = CollUtilX.isEmpty(hotelIds) ? MapUtil.empty() : queryHotelInfoMap(new HashSet<>(hotelIds));
            //校验supplierCode合法性
            List<String> supplierCodes = excelErrorVOS.stream()
                    .filter(e -> StrUtil.isEmpty(e.getErrorDesc()) && StrUtil.isNotEmpty(e.getSupplierCode()))
                    .map(ProOrgHotelAvailableExcelErrorVO::getSupplierCode).distinct().collect(Collectors.toList());
            Map<String, SupplierBaseInfoResp> supplierCodeMap = CollUtilX.isEmpty(supplierCodes) ? MapUtil.empty() : getSupplierCodeMap(supplierCodes);

            for (ProOrgHotelAvailableExcelErrorVO errorVO : excelErrorVOS) {
                // 跳过已经有错误的记录
                if (StrUtil.isNotEmpty(errorVO.getErrorDesc())) {
                    continue;
                }
                // 校验酒店ID合法性
                EsHotelDto esHotelDto = ObjectUtil.isEmpty(errorVO.getHotelId()) ? null : longEsHotelDtoMap.get(Long.parseLong(errorVO.getHotelId()));
                if (esHotelDto == null) {
                    errorVO.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_EXIST.getErrorDesc());
                    hasError.set(true);
                    continue;
                }

                // 校验供应商编码合法性
                if (StrUtil.isNotEmpty(errorVO.getSupplierCode())) {
                    SupplierBaseInfoResp orgBaseInfoResp = supplierCodeMap.get(errorVO.getSupplierCode());
                    if (orgBaseInfoResp == null) {
                        errorVO.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_EXIST.getErrorDesc());
                        hasError.set(true);
                        continue;
                    }
                    if (!AvailableEnum.Start.key.equals(orgBaseInfoResp.getAvailableStatus())) {
                        errorVO.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_ENABLE.getErrorDesc());
                        hasError.set(true);
                        continue;
                    }
                }

                //没有任何错误加载到待插入列表
                ProOrgHotelAvailableEntity proOrgHotelAvailableEntity = new ProOrgHotelAvailableEntity();
                proOrgHotelAvailableEntity.setHotelId(Long.parseLong(errorVO.getHotelId()));
                proOrgHotelAvailableEntity.setSupplierCode(errorVO.getSupplierCode());
                proOrgHotelAvailableEntity.setAvailableType(AvailableTypeEnum.getKey(errorVO.getAvailableType()));
                proOrgHotelAvailableEntity.setCityCode(esHotelDto.getCityCode());
                proOrgHotelAvailableEntity.setCreatedBy(createBy);
                proOrgHotelAvailableEntity.setCreatedDt(createDt);
                needSaveList.add(proOrgHotelAvailableEntity);
            }
        }
        List<ProOrgHotelAvailableExcelErrorVO> errorList = errorVOList.stream()
                .filter(e -> StrUtil.isNotEmpty(e.getErrorDesc())).collect(Collectors.toList());
        long failCount = errorList.size();
        long successCount = errorVOList.size() - failCount;
        UploadRespVO uploadRespVO = new UploadRespVO();
        uploadRespVO.setFailedCount(Long.toString(failCount));
        uploadRespVO.setSuccessCount(Long.toString(successCount));
        if (hasError.get()) {
            //执行失败处理
            String failedDataFileUrl = failHandler(errorList);
            uploadRespVO.setFailedDataFileUrl(failedDataFileUrl);
        }
        proOrgHotelAvailableService.successHandler(needSaveList);
        return uploadRespVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void successHandler(List<ProOrgHotelAvailableEntity> needSaveList) {
        if (CollUtilX.isEmpty(needSaveList)) {
            return;
        }
        for (List<ProOrgHotelAvailableEntity> proOrgHotelAvailableEntities : ListUtil.partition(needSaveList, 1000)) {
            baseMapper.insertList(proOrgHotelAvailableEntities);
        }
        //使用事务同步机制，确保在事务提交后更新缓存
        Set<String> supplierCodes = needSaveList.stream().map(ProOrgHotelAvailableEntity::getSupplierCode).collect(Collectors.toSet());
        registerCacheUpdateAfterTransaction(supplierCodes);
    }

    private String failHandler(List<ProOrgHotelAvailableExcelErrorVO> dataList) {
        //1. 使用模板文件导出错误数据
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (InputStream templateInputStream = this.getClass().getClassLoader()
                .getResourceAsStream("template/orgHotelAvailableTemplateError.xlsx")) {
            // 读取模板文件
            if (templateInputStream == null) {
                throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION.getErrorCode(), "找不到错误导出模板文件");
            }

            // 使用 XSSFWorkbook 打开模板
            XSSFWorkbook workbook = new XSSFWorkbook(templateInputStream);
            XSSFSheet sheet = workbook.getSheetAt(0);

            // 从第三行开始写入数据（索引为2）
            int startRowIndex = 2;
            for (int i = 0; i < dataList.size(); i++) {
                ProOrgHotelAvailableExcelErrorVO errorVO = dataList.get(i);
                XSSFRow row = sheet.createRow(startRowIndex + i);

                // 写入供应商编码
                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(errorVO.getSupplierCode() != null ? errorVO.getSupplierCode() : "");

                // 写入酒店ID
                XSSFCell cell1 = row.createCell(1);
                if (ObjectUtil.isNotEmpty(errorVO.getHotelId())) {
                    cell1.setCellValue(errorVO.getHotelId());
                }

                // 写入类型
                XSSFCell cell2 = row.createCell(2);
                cell2.setCellValue(errorVO.getAvailableType() != null ? errorVO.getAvailableType() : "");

                // 写入错误描述
                XSSFCell cell3 = row.createCell(3);
                cell3.setCellValue(errorVO.getErrorDesc() != null ? errorVO.getErrorDesc() : "");
            }

            // 写入到输出流
            workbook.write(outputStream);
            workbook.close();

        } catch (IOException e) {
            log.error("使用模板导出错误数据失败", e);
            throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION.getErrorCode(), "导出错误数据失败");
        }
        // 转换为 MultipartFile
        MultipartFile multipartFile;
        try {
            multipartFile = convert(outputStream, "供应商酒店黑白名单导入失败.xlsx");
        } catch (IOException e) {
            throw new SysException(e);
        }

        // 2. 上传文件到阿里云
        return fileUpUtil.uploadFile2(multipartFile, "供应商酒店黑白名单导入失败.xlsx");
    }

    /**
     * ByteArrayOutputStream 转换成 MultipartFile
     */
    public static MultipartFile convert(ByteArrayOutputStream byteArrayOutputStream, String fileName) throws IOException {
        // 获取字节数组
        byte[] bytes = byteArrayOutputStream.toByteArray();

        // 创建 MockMultipartFile
        return new MockMultipartFile("file", fileName, "application/octet-stream", bytes);
    }

    private Map<String, SupplierBaseInfoResp> getSupplierCodeMap(List<String> list) {
        SupplierBaseInfoReq orgBaseInfoReq = new SupplierBaseInfoReq();
        orgBaseInfoReq.setCompanyCode(list);
        Response<List<SupplierBaseInfoResp>> listResponse = agentRemote.listSupplierBaseInfoByAgentCode(orgBaseInfoReq);
        if (!listResponse.isSuccess()) {
            throw new SysException(listResponse.getFailCode(), listResponse.getFailReason());
        }
        return listResponse.getModel().stream().collect(Collectors.toMap(SupplierBaseInfoResp::getOrgCode, e -> e, (oldVal, newVal) -> oldVal));
    }

    private List<ProOrgHotelAvailableExcelErrorVO> validAndRecordError(List<ProOrgHotelAvailableExcelVO> dataList, AtomicBoolean hasError) {
        Set<String> onlyVaildSet = new HashSet<>();
        List<ProOrgHotelAvailableExcelErrorVO> errorVOList = new ArrayList<>(dataList.size());
        for (ProOrgHotelAvailableExcelVO item : dataList) {
            ProOrgHotelAvailableExcelErrorVO errorItem = CommonConvert.INSTANCE.convert(item);
            errorVOList.add(errorItem);
            //空值检查
            if (StrUtil.isEmpty(errorItem.getSupplierCode())) {
                errorItem.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_ID_NOT_NULL.getErrorDesc());
                hasError.set(true);
                continue;
            }
            if (ObjectUtil.isEmpty(errorItem.getHotelId())) {
                errorItem.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_NULL.getErrorDesc());
                hasError.set(true);
                continue;
            }
            if (StrUtil.isEmpty(errorItem.getAvailableType())) {
                errorItem.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_TYPE_NOT_NULL.getErrorDesc());
                hasError.set(true);
                continue;
            }
            if (!AvailableTypeEnum.WHITE.getDesc().equals(errorItem.getAvailableType()) && !AvailableTypeEnum.BLACK.getDesc().equals(errorItem.getAvailableType())) {
                errorItem.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_TYPE_INVALID.getErrorDesc());
                hasError.set(true);
                continue;
            }
            //非数字检查
            if(!StrUtilX.isNumeric(String.valueOf(errorItem.getHotelId()))){
                errorItem.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_ERROR.getErrorDesc());
                hasError.set(true);
                continue;
            }
            //2唯一校验及记录错误（同一酒店+供应商只能存在一种黑白名单）
            String fm = String.format("%s:%s", item.getHotelId(), item.getSupplierCode());
            if (onlyVaildSet.contains(fm)) {
                errorItem.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_DUPLICATE_ROW.getErrorDesc());
                hasError.set(true);
                continue;
            }
            onlyVaildSet.add(fm);
        }

        return errorVOList;
    }

    /**
     * 批量检查重复性并设置错误信息
     */
    private void checkBatchDuplicateAndSetError(List<ProOrgHotelAvailableExcelErrorVO> itemList, AtomicBoolean hasError) {
        if (CollUtilX.isEmpty(itemList)) {
            return;
        }
        Set<Long> hotelIds = new HashSet<>();
        Set<String> supplierCode = new HashSet<>();
        //收集需要验重检查的hotelId和supplierCode
        for (ProOrgHotelAvailableExcelErrorVO errorVO : itemList) {
            if (StrUtilX.isEmpty(errorVO.getErrorDesc()) && StrUtil.isNotEmpty(errorVO.getHotelId()) && StrUtilX.isNotEmpty(errorVO.getSupplierCode())) {
                hotelIds.add(Long.parseLong(errorVO.getHotelId()));
                supplierCode.add(errorVO.getSupplierCode());
            }
        }
        if (CollUtilX.isEmpty(hotelIds) || CollUtilX.isEmpty(supplierCode)) {
            return;
        }
        //查询已存在的
        Map<String, Integer> duplicateSet = baseMapper.selectList(Wrappers.<ProOrgHotelAvailableEntity>lambdaQuery()
                        .select(ProOrgHotelAvailableEntity::getHotelId, ProOrgHotelAvailableEntity::getSupplierCode, ProOrgHotelAvailableEntity::getAvailableType)
                        .in(ProOrgHotelAvailableEntity::getSupplierCode, supplierCode)
                        .in(ProOrgHotelAvailableEntity::getHotelId, hotelIds)).stream()
                .collect(Collectors.toMap(e -> StrUtil.format("{}{}", e.getHotelId(), e.getSupplierCode()), ProOrgHotelAvailableEntity::getAvailableType));
        for (ProOrgHotelAvailableExcelErrorVO errorVO : itemList) {
            //验重检查
            if (StrUtilX.isEmpty(errorVO.getErrorDesc()) && ObjectUtil.isNotEmpty(errorVO.getHotelId()) && StrUtilX.isNotEmpty(errorVO.getSupplierCode())) {
                Integer existAvailableType = duplicateSet.get(StrUtil.format("{}{}", errorVO.getHotelId(), errorVO.getSupplierCode()));
                if (existAvailableType == null) {
                    continue;
                }
                hasError.set(true);
                Integer excelAvailableType = AvailableTypeEnum.getKey(errorVO.getAvailableType());
                if (existAvailableType.equals(excelAvailableType)) {
                    errorVO.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_DUPLICATE.getErrorDesc());
                    continue;
                }
                if (AvailableTypeEnum.BLACK.getKey().equals(existAvailableType)) {
                    errorVO.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_BLACK.getErrorDesc());
                    continue;
                }
                errorVO.setErrorDesc(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_WHITE.getErrorDesc());
            }
        }
    }

    private List<ProOrgHotelAvailableExcelVO> readFile(MultipartFile file) {
        List<ProOrgHotelAvailableExcelVO> dataList;
        try {
            dataList = EasyExcel.read(file.getInputStream()).headRowNumber(1).headRowNumber(2).head(ProOrgHotelAvailableExcelVO.class).sheet().doReadSync();
        } catch (IOException e) {
            log.error("读取批量导入文件异常", e);
            throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION);
        } catch (Exception e) {
            //此处异常一定是酒店非数字
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_ERROR);
        }
        //单词最大上传名单大于N条校验
        if (dataList.isEmpty()) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_FILE_EMPTY_ERROR);
        }
        if (dataList.size() > MAX_ROW) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_FILE_SIZE_EXCEED);
        }
        return dataList;
    }

    private void validFileParam(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StrUtil.isEmpty(filename)) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_FILE_FORMAT_ERROR);
        } else if (!StrUtil.endWithIgnoreCase(filename, ".xlsx")) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_FILE_FORMAT_ERROR);
        }
        boolean result;
        try {
            result = checkExcelHeaders(file, EXCEL_HEADER, Collections.singleton(3), 1);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (!result) {
            throw new SysException(ErrorCodeEnum.PRO_ORG_HOTEL_AVAILABLE_HEADER_MISMATCH_ERROR);
        }
    }

    /**
     * 检查 Excel文件表头信息
     *
     * @param file         文件
     * @param dataList     表头
     * @param filterColumn 忽略的列
     * @param rowCount     校验表头第几行
     * @return 校验结果
     */
    public static boolean checkExcelHeaders(MultipartFile file, List<String> dataList, Set<Integer> filterColumn, Integer rowCount) throws IOException {
        InputStream inputStream = null;
        try {
            byte[] byteArr = file.getBytes();
            inputStream = new ByteArrayInputStream(byteArr);
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            //获取 excel 第N行数据（表头）
            Row row = sheet.getRow(rowCount);
            //存放表头信息
            List<String> headerList = new ArrayList<>();
            //算下有多少列
            int colCount = row.getLastCellNum();
            for (int j = 0; j < colCount; j++) {
                Cell cell = row.getCell(j);
                // 指定过滤 不进行模板校验的列
                if (filterColumn != null && filterColumn.contains(j)) {
                    continue;
                }

                if (cell != null && cell.getStringCellValue() != null && !cell.getStringCellValue().trim().isEmpty()) {
                    String cellValue = cell.getStringCellValue().trim();
                    // 过滤 第一行为空的 列
                    if (StrUtil.isNotBlank(cellValue) && !dataList.contains(cellValue)) {
                        return false;
                    }
                    headerList.add(cellValue);
                }
            }
            //符合条件的只能多 不能少
            return headerList.size() >= dataList.size();
        } catch (Exception e) {
            log.error("系统异常", e);
            return false;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * 根据酒店黑白名单规则过滤供应商列表
     */
    @Override
    public List<String> filterSuppliersByHotelAvailableRule(Long hotelId, List<String> supplierCodes) {
        // 1.参数校验
        if (hotelId == null) {
            return new ArrayList<>(supplierCodes);
        }

        if (CollUtilX.isEmpty(supplierCodes)) {
            return new ArrayList<>(supplierCodes);
        }
        log.info("开始过滤供应商列表，hotelId={}, 原始供应商数量={}", hotelId, supplierCodes.size());

        // 2.查询供应商黑白名单
        List<ProOrgHotelAvailableBaseDTO> hotelRules = cacheHotelAvailableRule(supplierCodes);

        // 3.使用简化的数据结构：只存储酒店ID集合，减少内存占用
        Map<String, Set<Long>> whiteHotels = new HashMap<>();  // 供应商 -> 白名单酒店ID集合
        Map<String, Set<Long>> blackHotels = new HashMap<>();  // 供应商 -> 黑名单酒店ID集合

        // 4.使用传统循环构建数据结构，避免Stream的中间对象创建
        for (ProOrgHotelAvailableBaseDTO rule : hotelRules) {
            String supplierCode = rule.getSupplierCode();
            Long ruleHotelId = rule.getHotelId();

            if (AvailableTypeEnum.WHITE.getKey().equals(rule.getAvailableType())) {
                // 白名单
                whiteHotels.computeIfAbsent(supplierCode, k -> new HashSet<>()).add(ruleHotelId);
            } else {
                // 黑名单
                blackHotels.computeIfAbsent(supplierCode, k -> new HashSet<>()).add(ruleHotelId);
            }
        }

        // 5.过滤供应商：使用传统循环避免Stream开销
        List<String> filteredSuppliers = new ArrayList<>();
        for (String supplierCode : supplierCodes) {
            // 白名单过滤：如果供应商有白名单配置，但酒店不在白名单中，则过滤掉
            Set<Long> whiteSet = whiteHotels.get(supplierCode);
            if (whiteSet != null && !whiteSet.contains(hotelId)) {
                continue; // 过滤掉
            }

            // 黑名单过滤：如果酒店在黑名单中，则过滤掉
            Set<Long> blackSet = blackHotels.get(supplierCode);
            if (blackSet != null && blackSet.contains(hotelId)) {
                continue; // 过滤掉
            }

            // 通过过滤，添加到结果列表
            filteredSuppliers.add(supplierCode);
        }

        log.info("过滤供应商列表完成，hotelId={}, 原始数量={}, 过滤后数量={}, 原始:{},过滤后:{}",
                hotelId, supplierCodes.size(), filteredSuppliers.size(),supplierCodes,filteredSuppliers);
        return filteredSuppliers;
    }

    /**
     * 获取酒店黑白名单数据
     * 使用新的主动缓存维护策略，直接从缓存获取数据
     */
    private List<ProOrgHotelAvailableBaseDTO> cacheHotelAvailableRule(List<String> supplierCodes) {
        log.info("从缓存获取酒店黑白名单数据，供应商编码数量：{}", supplierCodes.size());

        List<ProOrgHotelAvailableBaseDTO> allData = new ArrayList<>();

        try {
            // 构建缓存key列表
            List<String> cacheKeys = supplierCodes.stream()
                    .map(supplierCode -> PRO_ORG_HOTEL_AVAILABLE_CACHE_KEY + supplierCode)
                    .collect(Collectors.toList());

            // 批量获取缓存
            List<String> cachedValues = RedisTemplateX.multiGet(cacheKeys);

            // 处理缓存结果
            for (int i = 0; i < cachedValues.size(); i++) {
                String cachedValue = cachedValues.get(i);
                String supplierCode = supplierCodes.get(i);

                if (cachedValue != null && !EMPTY_CACHE_VALUE.equals(cachedValue)) {
                    // 缓存命中且有数据
                    try {
                        List<ProOrgHotelAvailableBaseDTO> parsedData = StrUtilX.parseObject(cachedValue, PRO_ORG_HOTEL_AVAILABLE_TYPE_REF);
                        if (CollUtilX.isNotEmpty(parsedData)) {
                            allData.addAll(parsedData);
                        }
                    } catch (Exception e) {
                        log.error("解析酒店黑白名单缓存数据失败，供应商编码：{}", supplierCode, e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("从缓存获取酒店黑白名单数据失败，降级为数据库查询", e);
            // 缓存异常时降级为数据库查询
            allData = baseMapper.selectList(Wrappers.<ProOrgHotelAvailableEntity>lambdaQuery()
                            .select(ProOrgHotelAvailableEntity::getAvailableType,
                                    ProOrgHotelAvailableEntity::getSupplierCode,
                                    ProOrgHotelAvailableEntity::getHotelId)
                            .in(ProOrgHotelAvailableEntity::getSupplierCode, supplierCodes))
                    .stream().map(CommonConvert.INSTANCE::convertToDTO).collect(Collectors.toList());
        }

        return allData;
    }

    /**
     * 注册事务提交后的缓存更新操作
     * 使用Spring事务同步机制，确保在事务成功提交后才更新缓存
     */
    private void registerCacheUpdateAfterTransaction(Set<String> supplierCodes) {
        if (CollUtilX.isEmpty(supplierCodes)) {
            return;
        }

        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    log.info("事务提交成功，开始更新酒店黑白名单缓存，供应商编码：{}", supplierCodes);
                    updateCacheForSupplierCodesAfterCommit(supplierCodes);
                }
            });
        } else {
            // 如果没有事务上下文，直接更新缓存
            log.warn("没有事务上下文，直接更新酒店黑白名单缓存，供应商编码：{}", supplierCodes);
            updateCacheForSupplierCodesAfterCommit(supplierCodes);
        }
    }

    /**
     * 事务提交后更新指定供应商的酒店黑白名单缓存
     * 此时数据库事务已经提交，可以查询到最新数据
     *
     * @param supplierCodes 需要更新缓存的供应商编码集合
     */
    private void updateCacheForSupplierCodesAfterCommit(Set<String> supplierCodes) {
        updateCacheForSupplierCodes(supplierCodes);
    }

    /**
     * 主动更新指定供应商的酒店黑白名单缓存
     *
     * @param supplierCodes 需要更新缓存的供应商编码集合
     */
    private void updateCacheForSupplierCodes(Set<String> supplierCodes) {
        if (CollUtilX.isEmpty(supplierCodes)) {
            return;
        }
        // 查询这些供应商的最新酒店黑白名单数据
        List<ProOrgHotelAvailableBaseDTO> dataList = baseMapper.selectList(Wrappers.<ProOrgHotelAvailableEntity>lambdaQuery()
                        .select(ProOrgHotelAvailableEntity::getAvailableType,
                                ProOrgHotelAvailableEntity::getSupplierCode,
                                ProOrgHotelAvailableEntity::getHotelId)
                        .in(ProOrgHotelAvailableEntity::getSupplierCode, supplierCodes))
                .stream().map(CommonConvert.INSTANCE::convertToDTO).collect(Collectors.toList());

        // 构建缓存数据
        Map<String, String> cacheDataMap = new HashMap<>();

        // 按供应商编码分组
        Map<String, List<ProOrgHotelAvailableBaseDTO>> groupedData = dataList.stream()
                .collect(Collectors.groupingBy(ProOrgHotelAvailableBaseDTO::getSupplierCode));

        // 为每个供应商编码构建缓存
        for (String supplierCode : supplierCodes) {
            String cacheKey = PRO_ORG_HOTEL_AVAILABLE_CACHE_KEY + supplierCode;
            List<ProOrgHotelAvailableBaseDTO> supplierData = groupedData.get(supplierCode);

            if (CollUtilX.isNotEmpty(supplierData)) {
                // 有黑白名单数据，缓存实际数据
                cacheDataMap.put(cacheKey, JSONUtil.toJsonStr(supplierData));
            } else {
                // 无黑白名单数据，缓存空值防止缓存穿透
                cacheDataMap.put(cacheKey, EMPTY_CACHE_VALUE);
            }
        }

        // 批量更新缓存
        if (!cacheDataMap.isEmpty()) {
            RedisTemplateX.multiSet(cacheDataMap);
        }
    }

    /**
     * 重置酒店黑白名单缓存
     * 使用无损重置策略，避免清空缓存导致权益丢失
     */
    @Override
    public void resetHotelAvailableCache() {
        // 使用无损重置策略：先加载新数据，再替换旧数据
        reloadAllHotelAvailableDataToCache();
    }

    /**
     * 无损重新加载所有酒店黑白名单数据到缓存
     * 使用原子性更新，避免清空缓存导致权益丢失
     */
    private void reloadAllHotelAvailableDataToCache() {
        log.info("开始无损重新加载所有酒店黑白名单数据到缓存");
        try {
            // 查询所有酒店黑白名单数据
            List<ProOrgHotelAvailableBaseDTO> allData = baseMapper.selectList(Wrappers.<ProOrgHotelAvailableEntity>lambdaQuery()
                            .select(ProOrgHotelAvailableEntity::getAvailableType,
                                    ProOrgHotelAvailableEntity::getSupplierCode,
                                    ProOrgHotelAvailableEntity::getHotelId))
                    .stream().map(CommonConvert.INSTANCE::convertToDTO).collect(Collectors.toList());

            // 按供应商编码分组构建缓存数据
            Map<String, String> cacheDataMap = new HashMap<>();

            if (CollUtilX.isNotEmpty(allData)) {
                // 有黑白名单数据的供应商
                Map<String, String> dataMap = allData.stream()
                        .collect(Collectors.groupingBy(
                                ProOrgHotelAvailableBaseDTO::getSupplierCode,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        JSONUtil::toJsonStr
                                )
                        ))
                        .entrySet()
                        .stream()
                        .collect(Collectors.toMap(
                                entry -> PRO_ORG_HOTEL_AVAILABLE_CACHE_KEY + entry.getKey(),
                                Map.Entry::getValue
                        ));
                cacheDataMap.putAll(dataMap);
            }

            // 查询所有已缓存的供应商编码，为没有黑白名单数据的供应商设置空值缓存
            Set<String> existingCacheKeys = RedisTemplateX.keys(PRO_ORG_HOTEL_AVAILABLE_CACHE_KEY + "*");
            if (CollUtilX.isNotEmpty(existingCacheKeys)) {
                Set<String> existingSupplierCodes = existingCacheKeys.stream()
                        .map(key -> key.replace(PRO_ORG_HOTEL_AVAILABLE_CACHE_KEY, ""))
                        .collect(Collectors.toSet());

                Set<String> dataSupplierCodes = allData.stream()
                        .map(ProOrgHotelAvailableBaseDTO::getSupplierCode)
                        .collect(Collectors.toSet());

                // 为没有黑白名单数据但已缓存的供应商设置空值
                existingSupplierCodes.stream()
                        .filter(supplierCode -> !dataSupplierCodes.contains(supplierCode))
                        .forEach(supplierCode -> cacheDataMap.put(PRO_ORG_HOTEL_AVAILABLE_CACHE_KEY + supplierCode, EMPTY_CACHE_VALUE));
            }

            // 原子性批量更新缓存
            if (!cacheDataMap.isEmpty()) {
                RedisTemplateX.multiSet(cacheDataMap);
            }

        } catch (Exception e) {
            log.error("重新加载酒店黑白名单数据到缓存失败", e);
            throw e;
        }
    }
}
