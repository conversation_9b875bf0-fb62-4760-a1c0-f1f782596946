package com.tiangong.product.service;

import com.tiangong.product.domain.resp.HotelProductIdsResponse;
import com.tiangong.product.domain.resp.HotelSaleInfoItemResp;
import com.tiangong.product.domain.resp.QueryProductIdsRequest;

import java.util.List;

public interface ProductInfoService  {

    /**
     * 根据酒店id查询出产品ids
     */
    List<HotelProductIdsResponse> queryProductIdsByHotelId(QueryProductIdsRequest queryProductIdsRequest);

    /**
     * 根据币种查询酒店
     */
    List<HotelSaleInfoItemResp> selectProductHotel(QueryProductIdsRequest queryProductIdsRequest);
}
