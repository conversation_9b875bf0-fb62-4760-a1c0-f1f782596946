package com.tiangong.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.dto.GuaranteeDTO;
import com.tiangong.dis.dto.ProductRestrictDTO;
import com.tiangong.dis.dto.ProductSaleStatusDTO;
import com.tiangong.dis.remote.InitRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.hotel.base.RoomtypeDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.OrderQueryRemote;
import com.tiangong.order.remote.dto.FuzzySupplierDTO;
import com.tiangong.product.convert.CommonConvert;
import com.tiangong.product.domain.*;
import com.tiangong.product.domain.dto.DateRangeDTO;
import com.tiangong.product.domain.req.ProTaxDetailBase;
import com.tiangong.product.domain.req.ProTaxRuleConfigQueryReq;
import com.tiangong.product.domain.req.ProTaxRuleConfigResp;
import com.tiangong.product.domain.resp.HotelSaleInfoResp;
import com.tiangong.product.domain.resp.HotelSaleInfoItemResp;
import com.tiangong.product.domain.resp.QueryProductIdsRequest;
import com.tiangong.product.dto.*;
import com.tiangong.product.dto.ProductSaleIncreaseDTO;
import com.tiangong.product.mapper.*;
import com.tiangong.product.resp.HotelLabelDTO;
import com.tiangong.product.service.ProTaxRuleConfigService;
import com.tiangong.product.service.ProductInfoService;
import com.tiangong.product.service.ProductSaleService;
import com.tiangong.product.service.ProductService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tiangong.util.DateUtilX.hour_format;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 11:21
 * @Description: 产品service实现类
 */
@Slf4j
@Service("productService")
public class ProductServiceImpl implements ProductService {

    @Autowired
    private DailyGuaranteeMapper dailyGuaranteeMapper;

    @Autowired
    private GuaranteeMapper guaranteeMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private QuotaAccountMapper quotaAccountMapper;

    @Autowired
    private ProductSaleStatusMapper productSaleStatusMapper;

    @Autowired
    private ProductRestrictMapper productRestrictMapper;

    @Autowired
    private CompanyChannelMapper companyChannelMapper;

    @Autowired
    private ProductPriceMapper productPriceMapper;

    @Autowired
    private ProductQuotaMapper productQuotaMapper;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private ProductLogMapper productLogMapper;

    @Autowired
    private DailyRestrictMapper dailyRestrictMapper;

    @Autowired
    private InitRemote initRemote;

    @Autowired
    private OrderQueryRemote orderQueryRemote;

    @Autowired
    private ProTaxRuleConfigService proTaxRuleConfigService;

    @Autowired
    private ProductInfoService productInfoService;

    @Autowired
    private ProductSaleService productSaleService;

    @Autowired
    private ProductDayIncreaseMapper productDayIncreaseMapper;

    @Autowired
    protected HttpServletRequest request;

    @Resource(name = "processingProductInfoExecutor")
    private ThreadPoolTaskExecutor processingProductInfoExecutor;


    @Override
    public Boolean checkTaxRuleConfigExists(Long taxRuleConfigId) {
        return productMapper.checkTaxRuleConfigExists(taxRuleConfigId) > 0;
    }

    @Override
    @Transactional
    public Integer addProduct(ProductDTO productDTO) {
        // 校验参数
        checkAddProductParam(productDTO);

        // 添加配额账号
        QuotaAccountPO quotaAccountPO = this.assemblyQuotaAccount(productDTO);
        quotaAccountMapper.insert(quotaAccountPO);

        // 添加产品
        ProductPO productPO = CommonConvert.INSTANCE.productPOConvert(productDTO);
        if (StrUtilX.isNotEmpty(productDTO.getBedTypes())) {
            List<BedTypesDTO> bedInfos = JSON.parseArray(productDTO.getBedTypes(), BedTypesDTO.class);
            productPO.setBedTypes(JSON.toJSONString(bedInfos));
        }
        productPO.setOffShelveStatus(1);//默认为启售
        productPO.setQuotaAccountId(quotaAccountPO.getQuotaAccountId());
        productPO.setActive(1);
        if (productDTO.getGuestQtyType() != null) {
            // 默认
            if (productDTO.getGuestQtyType() == 0) {
                productPO.setMaxChildrenAge(null);
                productPO.setMaxChildrenQty(null);
                productPO.setMaxGuestQty(null);
            } else {
                //自定义
                productPO.setMaxGuestQty(productDTO.getMaxAdultQty());
                productPO.setMaxChildrenQty(productDTO.getMaxChildQty());
                productPO.setMaxChildrenAge(productDTO.getMaxChildAge());
            }
        }
        productMapper.insert(productPO);

        // 添加条款
        ProductRestrictPO productRestrictPO = CommonConvert.INSTANCE.ProductRestrictPOConvert(productDTO);
        productRestrictPO.setProductId(productPO.getProductId());
        productRestrictMapper.insert(productRestrictPO);

        // 批量插入担保条款
        if (CollectionUtil.isNotEmpty(productDTO.getGuarantees())) {
            List<GuaranteePO> guarantees = CommonConvert.INSTANCE.guaranteePOListConvert(productDTO.getGuarantees());
            for (GuaranteePO guarantee : guarantees) {
                guarantee.setProductId(productPO.getProductId());
            }
            guaranteeMapper.insertList(guarantees);
        }

        List<ProductSaleStatusPO> productSaleStatusDTOS = new LinkedList<>();

        // 添加产品和运营商管理表（渠道上架状态）
        ProductSaleStatusPO productSaleStatusPO = new ProductSaleStatusPO();
        productSaleStatusPO.setActive(1);
        productSaleStatusPO.setProductId(productPO.getProductId());
        productSaleStatusPO.setCompanyCode(productDTO.getCompanyCode());
        productSaleStatusPO.setB2bSaleStatus(0);
        productSaleStatusPO.setCreatedBy(productDTO.getCreatedBy());
        productSaleStatusPO.setCreatedDt(productDTO.getCreatedDt());
        productSaleStatusDTOS.add(productSaleStatusPO);

        // 判断是否为共享供应商，若为共享供应商则查询供应商下面对应的所有商家
        Integer shareSupplierStatus = productSaleStatusMapper.querySupplierShareStatus(productDTO.getSupplierCode());
        if (shareSupplierStatus != null && shareSupplierStatus == 1) {
            List<String> companyCodeList = productSaleStatusMapper.querySupplierCompanyList(productDTO.getSupplierCode());
            if (CollUtilX.isNotEmpty(companyCodeList)) {
                for (String companyCode : companyCodeList) {
                    if (!productDTO.getCompanyCode().equals(companyCode)) {
                        ProductSaleStatusPO productSaleStatus = new ProductSaleStatusPO(productSaleStatusPO);
                        productSaleStatus.setCompanyCode(companyCode);
                        productSaleStatusDTOS.add(productSaleStatus);
                    }
                }
            }
        }

        productSaleStatusMapper.insertList(productSaleStatusDTOS);

        productDTO.setActive(1);
        productDTO.setProductId(productPO.getProductId());

        // 初始化产品信息
        IncrementObjectDTO incrementObj = new IncrementObjectDTO();
        incrementObj.setObject(new ArrayList<>(Collections.singletonList(productDTO)));
        initRemote.initProductInfo(incrementObj);

        // 初始化条款
        initRemote.initRestrict(incrementObj);

        // 初始化上下架信息
        IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
        incrementObjectDTO.setObject(productSaleStatusDTOS.stream().map(CommonConvert.INSTANCE::productSaleStatusDTOConvert).collect(Collectors.toList()));
        initRemote.initSaleStatus(incrementObjectDTO);

        if (productPO.getHotelId() != null) {
            // 刪除原本的统计值
            productMapper.deleteHotelSaleInfoByHotelId(productPO.getHotelId());
            // 添加酒店销售信息
            addHotelSaleInfoGroup(productPO.getHotelId(), productDTO.getLanguage());
            Map<String, String> hotelIdMap = new HashMap<String, String>() {{
                put(productPO.getHotelId().toString(), productPO.getHotelId().toString());
            }};
            RedisTemplateX.hPutAll(RedisKey.CONTRACT_HOTEL_KEY, hotelIdMap);
        }
        return productPO.getProductId();
    }

    /**
     * 添加酒店销售信息
     */
    private int addHotelSaleInfoGroup(Long hotelId, String language) {
        // 重新统计这个酒店
        QueryProductRequestDTO queryProductRequestDTO = new QueryProductRequestDTO();
        queryProductRequestDTO.setHotelId(hotelId);

        // 查询ES酒店
        DestinationReq destinationReq = new DestinationReq();
        destinationReq.setLanguage(language);
        destinationReq.setHotelIds(Collections.singleton(hotelId));
        Response<List<EsHotelDto>> esHotelDTOResponse = hotelRemote.searchDestinationHotel(destinationReq);
        if (esHotelDTOResponse.isError()) {
            log.error("查询ES酒店异常，req={}，resp={}", JSONUtil.toJsonStr(destinationReq), JSONUtil.toJsonStr(esHotelDTOResponse));
            throw new SysException(ErrorCodeEnum.READ_TIME_OUT);
        }
        if (CollUtilX.isEmpty(esHotelDTOResponse.getModel())) {
            throw new SysException(ErrorCodeEnum.NOT_EXIST_HOTEL);
        }
        EsHotelDto esHotelDto = esHotelDTOResponse.getModel().get(0);
        queryProductRequestDTO.setCityCode(esHotelDto.getCityCode());
        queryProductRequestDTO.setHotelName(esHotelDto.getHotelName());
        return productMapper.addHotelSaleInfoGroupByHotelId(queryProductRequestDTO);
    }

    /**
     * 校验新增产品参数
     */
    private void checkAddProductParam(ProductDTO req) {
        if (null == req.getHotelId()) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_HOTELID);
        }
        if (null == req.getRoomId()) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMID);
        }
        if (null == req.getBreakfastQty()) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_BREAKFASTQTY);
        }
        if (null == req.getPurchaseType()) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_PURCHASETYPE);
        }
        if (null == req.getCurrency()) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CURRENCY);
        }
        if (StrUtilX.isEmpty(req.getProductName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_PRODUCTNAME);
        }
        if (StrUtilX.isEmpty(req.getSupplierCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
        }
        if (StrUtilX.isEmpty(req.getBedTypes())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_BEDTYPE);
        }
    }

    @Override
    @Transactional
    public void modifyProduct(ProductDTO productDTO) {
        if (null == productDTO.getProductId()) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 修改产品
        ProductPO productPO = CommonConvert.INSTANCE.productDTOConvert(productDTO);
        if (StrUtilX.isNotEmpty(productDTO.getBedTypes())) {
            List<BedTypesDTO> bedInfos = JSON.parseArray(productDTO.getBedTypes(), BedTypesDTO.class);
            productPO.setBedTypes(JSON.toJSONString(bedInfos));
        }
        productPO.setCreatedDt(null);
        productPO.setUpdatedBy(productDTO.getUpdatedBy());
        productPO.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        if (productDTO.getGuestQtyType() != null) {
            productMapper.updateByPrimaryKey(productPO);
        }

        List<String> productNameIncrementDTO = handlerProductName(new ArrayList<>(Collections.singletonList(productDTO)));
        List<String> breakfastIncrementDTO = handlerBreakfast(new ArrayList<>(Collections.singletonList(productDTO)));
        if (CollUtilX.isNotEmpty(breakfastIncrementDTO) || CollUtilX.isNotEmpty(productNameIncrementDTO)) {
            if (CollUtilX.isNotEmpty(breakfastIncrementDTO)) {
                IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
                incrementObjectDTO.setObject(new ArrayList<>(Collections.singletonList(productDTO)));
                incrementObjectDTO.setIncrementList(breakfastIncrementDTO);
                initRemote.initProductInfo(incrementObjectDTO);
            }

            if (CollUtilX.isNotEmpty(productNameIncrementDTO)) {
                IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
                incrementObjectDTO.setObject(new ArrayList<>(Collections.singletonList(productDTO)));
                incrementObjectDTO.setIncrementList(productNameIncrementDTO);
                initRemote.initProductInfo(incrementObjectDTO);
            }
        }

        // 修改条款
        ProductRestrictPO productRestrictPO = CommonConvert.INSTANCE.ProductRestrictPOConvert(productDTO);
        productRestrictPO.setCreatedDt(null);
        productRestrictPO.setUpdatedBy(productDTO.getUpdatedBy());
        productRestrictPO.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));

        List<ProductRestrictPO> productRestrictDTOS = new ArrayList<>();
        productRestrictDTOS.add(productRestrictPO);
        // 过滤条款修改
        List<String> restrictIncrementDTO = handlerProductRestrict(productRestrictDTOS);

//        if (CollUtilX.isNotEmpty(restrictIncrementDTO)) {
//            Example productRestrictExample = new Example(ProductRestrictPO.class);
//            Example.Criteria productRestrictCriteria = productRestrictExample.createCriteria();
//            productRestrictCriteria.andEqualTo("productId", productDTO.getProductId());
        productRestrictPO.setProductId(productDTO.getProductId());
        productRestrictMapper.updateProductRestrict(productRestrictPO);

        // 删除之后新增list 较沿很麻烦
        Example cancelRestrictExample = new Example(GuaranteePO.class);
        Example.Criteria canceRestrictCriteria = cancelRestrictExample.createCriteria();
        canceRestrictCriteria.andEqualTo("productId", productDTO.getProductId());
        guaranteeMapper.deleteByExample(cancelRestrictExample);

        if (CollUtilX.isNotEmpty(productDTO.getGuarantees())) {
            List<GuaranteePO> guarantees = new ArrayList<>();
            for (com.tiangong.product.dto.GuaranteeDTO guarantee : productDTO.getGuarantees()) {
                GuaranteePO guaranteePO = CommonConvert.INSTANCE.guaranteePOConvert(guarantee);
                guaranteePO.setProductId(productDTO.getProductId());
                guarantees.add(guaranteePO);
            }
            guaranteeMapper.insertList(guarantees);
        }
//        }

//        if (CollUtilX.isNotEmpty(restrictIncrementDTO)) {
        IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
        incrementObjectDTO.setObject(new ArrayList<>(Collections.singletonList(productDTO)));
        incrementObjectDTO.setIncrementList(restrictIncrementDTO);
        initRemote.initRestrict(incrementObjectDTO);
//        }
    }


    @Override
    @Transactional
    public DeleteProductResponseDTO deleteProduct(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("productId"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        DeleteProductResponseDTO deleteProductResponseDTO = new DeleteProductResponseDTO();
        // 查询产品上架状态
        ProductSaleStatusPO productSaleStatusPO = new ProductSaleStatusPO();
        productSaleStatusPO.setProductId(Integer.valueOf(requestMap.get("productId")));
        productSaleStatusPO.setCompanyCode(requestMap.get("companyCode"));
        productSaleStatusPO.setActive(1);
        ProductSaleStatusPO productSaleStatusPO1 = productSaleStatusMapper.selectOne(productSaleStatusPO);
        if (this.checkSaleStatus(productSaleStatusPO1)) {// 有渠道上架，返回不能删除
            deleteProductResponseDTO.setStatus(0);
            deleteProductResponseDTO.setReason(ErrorCodeEnum.PRODUCTONSALEWITHNODELETE.errorDesc);
        } else {// 无渠道上架，将此运营商产品设置为无效
            productSaleStatusPO.setActive(0);
            productSaleStatusPO.setId(productSaleStatusPO1.getId());
            productSaleStatusPO.setUpdatedBy(requestMap.get("updatedBy"));
            productSaleStatusPO.setUpdatedDt(requestMap.get("updatedDt"));
            productSaleStatusMapper.updateByPrimaryKeySelective(productSaleStatusPO);
            deleteProductResponseDTO.setStatus(1);

            //TODO 需要处理
        }

        if (StrUtilX.isNotEmpty(requestMap.get("productId"))) {
            Example example = new Example(ProductPO.class);
            example.createCriteria().andEqualTo("productId", Integer.valueOf(requestMap.get("productId")));
            List<ProductPO> productPOS = productMapper.selectByExample(example);
            if (CollUtilX.isNotEmpty(productPOS) && productPOS.get(0) != null && productPOS.get(0).getHotelId() != null) {
                //刪除原本的统计值
                productMapper.deleteHotelSaleInfoByHotelId(productPOS.get(0).getHotelId());
                // 重新统计这个酒店
                int row = addHotelSaleInfoGroup(productPOS.get(0).getHotelId(), requestMap.get("language"));
                if (row > 0) {
                    Map<String, String> hotelIdMap = new HashMap<String, String>() {{
                        put(productPOS.get(0).getHotelId().toString(), productPOS.get(0).getHotelId().toString());
                    }};
                    RedisTemplateX.hPutAll(RedisKey.CONTRACT_HOTEL_KEY, hotelIdMap);
                } else if (row == 0) {
                    RedisTemplateX.hDelete(RedisKey.CONTRACT_HOTEL_KEY, productPOS.get(0).getHotelId().toString());
                }
            }
        }

        // 更新酒店售卖信息
        List<String> productIdList = new ArrayList<>();
        productIdList.add(requestMap.get("productId"));
        RedisTemplateX.setAdd(RedisKey.hotelSaleInfoModifyKey, productIdList.toArray(new String[0]));
//        productMapper.updateHotelSaleInfo(productIdList);
        return deleteProductResponseDTO;
    }

    @Override
    public ProductDTO queryProduct(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("productId"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        return productMapper.queryProduct(requestMap.get("productId"));
    }

    @Override
    public List<ProductHotelDTO> queryHotelList(QueryProductRequestDTO queryProductRequestDTO) {
        List<ProductHotelDTO> dtoList = new ArrayList<>();
        if (queryProductRequestDTO.getHotelId() != null || queryProductRequestDTO.getHasProducts() != null || StrUtilX.isNotEmpty(queryProductRequestDTO.getSupplierCode())) {
            dtoList = productMapper.queryHotelListByStatistics(queryProductRequestDTO);
            // 设置酒店名称
            if (CollUtilX.isNotEmpty(dtoList)) {
                Set<Long> hotelIds = dtoList.stream().map(ProductHotelDTO::getHotelId).collect(Collectors.toSet());
                DestinationReq destinationReq = new DestinationReq();
                destinationReq.setLanguage(queryProductRequestDTO.getLanguage());
                destinationReq.setHotelIds(hotelIds);
                Response<List<EsHotelDto>> response = hotelRemote.searchDestinationHotel(destinationReq);
                if (response.isSuccess() && CollUtilX.isNotEmpty(response.getModel())) {
                    Map<Long, String> hotelMap = response.getModel().stream().collect(Collectors.toMap(EsHotelDto::getHotelId, EsHotelDto::getHotelName));
                    dtoList.forEach(item -> {
                        item.setHotelName(hotelMap.get(item.getHotelId()));
                    });
                }
            }
        } else {
            DestinationReq destinationReq = new DestinationReq();
            destinationReq.setLanguage(queryProductRequestDTO.getLanguage());
            destinationReq.setCityCode(queryProductRequestDTO.getCityCode());
            Response<List<EsHotelDto>> response = hotelRemote.searchDestinationHotel(destinationReq);
            if (response.isSuccess() && CollUtilX.isNotEmpty(response.getModel())) {
                for (EsHotelDto esHotelDto : response.getModel()) {
                    ProductHotelDTO productHotelDTO = new ProductHotelDTO();
                    productHotelDTO.setHotelId(esHotelDto.getHotelId());
                    productHotelDTO.setHotelName(esHotelDto.getHotelName());
                    dtoList.add(productHotelDTO);
                }
            }
        }
        return dtoList;
    }

    @Override
    public HotelProductsDTO queryHotelProducts(QueryProductRequestDTO queryProductRequestDTO) {
        if (null == queryProductRequestDTO.getHotelId() || StrUtilX.isEmpty(queryProductRequestDTO.getStartDate())
                || StrUtilX.isEmpty(queryProductRequestDTO.getEndDate())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 查询酒店产品列表
        List<ProductTempDTO> productTempDTOList = productMapper.queryHotelProducts(queryProductRequestDTO);

        // 查询产品 不含税费 税费规则明细 用于计算
        Set<Long> taxRuleConfigIdSet = new HashSet<>();
        for (ProductTempDTO productTempDTO : productTempDTOList) {
            if (productTempDTO.getPriceType() != null
                    && productTempDTO.getPriceType().equals(1)
                    && productTempDTO.getBasePrice() != null) {
                taxRuleConfigIdSet.add(productTempDTO.getTaxRuleConfigId());
            }
        }
        Map<Long, ProTaxRuleConfigResp> longProTaxRuleConfigRespMap = null;
        if (CollectionUtil.isNotEmpty(taxRuleConfigIdSet)) {
            longProTaxRuleConfigRespMap = proTaxRuleConfigService.proTaxRuleConfigDetailList(taxRuleConfigIdSet);
        }

        // 查询运营商对应渠道
        Example companyChannelExample = new Example(CompanyChannelPO.class);
        Example.Criteria companyChannelCriteria = companyChannelExample.createCriteria();
        companyChannelCriteria.andEqualTo("companyCode", queryProductRequestDTO.getCompanyCode());
        List<CompanyChannelPO> companyChannelPOList = companyChannelMapper.selectByExample(companyChannelExample);
        List<String> dateList = new ArrayList<>();
        dateList.add(queryProductRequestDTO.getStartDate());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(sdf.parse(queryProductRequestDTO.getStartDate()));
        } catch (Exception e) {
            log.error("查询酒店产品详情，转换时间异常", e);
        }
        for (int i = 0; i < 31; i++) {
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            String dateStr = sdf.format(calendar.getTime());
            dateList.add(dateStr);
            if (dateStr.equals(queryProductRequestDTO.getEndDate())) {
                break;
            }
        }
        // 组装酒店产品数据
        return this.assemblyHotelProducts(productTempDTOList, companyChannelPOList, queryProductRequestDTO.getHotelId(), queryProductRequestDTO.getCompanyCode(), dateList, queryProductRequestDTO.getLanguage(), longProTaxRuleConfigRespMap);
    }

    @Override
    @Transactional
    public void dailyModifyProductInfo(ProductDayQuotationDTO productDayQuotationDTO) {
        if (null == productDayQuotationDTO.getProductId() || StrUtilX.isEmpty(productDayQuotationDTO.getSaleDate())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        List<ProductDayQuotationDTO> productDayQuotationDTOList = new ArrayList<>();
        //设置配额账号Id
        ProductPO productPO = new ProductPO();
        productPO.setProductId(productDayQuotationDTO.getProductId());
        ProductPO productPO1 = productMapper.selectByPrimaryKey(productPO.getProductId());
        // Boolean subtractFlag = false;
        if (null != productPO1.getQuotaAccountId()) {
            //调整方式为减少时，配额改为负数
            if (productDayQuotationDTO.getQuotaAdjustmentType().equals(1)) {
                //subtractFlag = true;
                productDayQuotationDTO.setModifiedQuota(-productDayQuotationDTO.getModifiedQuota());
            }
            productDayQuotationDTO.setQuotaAccountId(productPO1.getQuotaAccountId());
        }
        if (productDayQuotationDTO.getBasePriceAdjustmentType() != null
                && productDayQuotationDTO.getModifiedBasePrice() != null
                && !(productDayQuotationDTO.getModifiedBasePrice().compareTo(BigDecimal.ZERO) == 0)) {

            //subtractFlag = true;
            if (productDayQuotationDTO.getBasePriceAdjustmentType().equals(1)) {
                //subtractFlag = true;
                productDayQuotationDTO.setModifiedBasePrice(BigDecimal.ZERO.subtract(productDayQuotationDTO.getModifiedBasePrice()));
            }
//            Set<Integer> productIdSet = new HashSet<>();
//            productIdSet.add(productDayQuotationDTO.getProductId());
//
//            // 获取产品对应的价税费类型
//            List<ProductTaxRuleDTO> productTaxRuleDTOS = productMapper.queryProductTaxRuleConfig(productIdSet);
//            Map<Integer, Long> productTaxRuleMap = new HashMap<>();
//            Map<Long, ProTaxRuleConfigResp> longProTaxRuleConfigRespMap = new HashMap<>();
//            if (CollectionUtil.isNotEmpty(productTaxRuleDTOS)) {
//                productTaxRuleMap = productTaxRuleDTOS.stream().collect(Collectors.toMap(ProductTaxRuleDTO::getProductId, ProductTaxRuleDTO::getTaxRuleConfigId));
//                Set<Long> productTaxRuleIdSet = productTaxRuleDTOS.stream().map(ProductTaxRuleDTO::getTaxRuleConfigId).collect(Collectors.toSet());
//                longProTaxRuleConfigRespMap = proTaxRuleConfigService.proTaxRuleConfigDetailList(productTaxRuleIdSet);
//            }
//
//            Long taxRuleConfigId = productTaxRuleMap.get(productDayQuotationDTO.getProductId());
//            List<ProTaxDetailBase> proTaxDetails = null;
//            if (taxRuleConfigId != null) {
//                ProTaxRuleConfigResp proTaxRuleConfigResp = longProTaxRuleConfigRespMap.get(taxRuleConfigId);
//                if (proTaxRuleConfigResp != null) {
//                    proTaxDetails= proTaxRuleConfigResp.getProTaxDetails();
//                }
//            }
//
//
//            QueryProductDayPriceDTO queryProductDayPriceDTO = new QueryProductDayPriceDTO();
//            queryProductDayPriceDTO.setProductId(productDayQuotationDTO.getProductId());
//            queryProductDayPriceDTO.setSaleDate(productDayQuotationDTO.getSaleDate());
//            // 价格变动 需要查询改产品指定日期的所有价格需要查询某个产品 指定时间段内的所有价格 进行计算后修改
//            List<ProductDayPriceDTO> productDayPriceDTOS = productPriceMapper.queryProductPriceList(queryProductDayPriceDTO);
//            // 每日价格列表 当价格发生变更时 因为税费原因，需要重新使用房费计算税费
//            Map<String, ProductDayPriceDTO> productDayPriceMap = new HashMap<>();
//            if(CollectionUtil.isNotEmpty(productDayPriceDTOS)){
//                productDayPriceMap= productDayPriceDTOS.stream().collect(Collectors.toMap(ProductDayPriceDTO::getSaleDate, productDayPriceDTO -> productDayPriceDTO));
//            }
//
//            // roomPrice = 旧的房费 + 修改后价格 < 0则过滤
//            ProductDayPriceDTO productDayPriceDTO = productDayPriceMap.get(productDayQuotationDTO.getSaleDate());
//            if(productDayPriceDTO!=null){
//                // 用房费计算税费
//                //1. 获取旧的房费  可能存在一种情况 房费为0 ，但是存在底价 那么底价=房费，因为是含税价格
//                BigDecimal roomPrice = productDayPriceDTO.getRoomPrice() == null ? productDayPriceDTO.getBasePrice() : productDayPriceDTO.getRoomPrice();
//
//                //2. 通过旧的房费计算新的房费
//                if(productDayQuotationDTO.getBasePriceAdjustmentType().equals(0)){
//                    // 加
//                    roomPrice = roomPrice.add(productDayQuotationDTO.getModifiedBasePrice());
//                }else if(productDayQuotationDTO.getBasePriceAdjustmentType().equals(1)){
//                    // 减
//                    roomPrice = roomPrice.subtract(productDayQuotationDTO.getModifiedBasePrice());
//                }else if(productDayQuotationDTO.getBasePriceAdjustmentType().equals(2)){
//                    // 等于
//                    roomPrice =productDayQuotationDTO.getModifiedBasePrice();
//                }
//                // 3. 判断新的房费是否大于0
//                if (roomPrice.compareTo(BigDecimal.ZERO) > 0){
//                    //设置新的房费
//                    getFaxPrice(productDayQuotationDTO, roomPrice, proTaxDetails);
//                }else {
//                    // 新的房费小于|等于0 结束
//                }
//            }else {
//                // 原先不存在价格 这时候需要新增
//                getFaxPrice(productDayQuotationDTO, productDayQuotationDTO.getModifiedBasePrice(), proTaxDetails);
//            }
        } else {
            // 置为空，不设置价格
            productDayQuotationDTO.setModifiedBasePrice(null);
        }
        productDayQuotationDTOList.add(productDayQuotationDTO);

        //TODO 对比redis里面的数据，进行一次过滤
        List<String> incrementList = filterModifyData(productDayQuotationDTOList);

        if (CollUtilX.isNotEmpty(productDayQuotationDTOList)) {
            //修改价格，传入价格时才修改价格
//            if (null != productDayQuotationDTO.getModifiedBasePrice() && productDayQuotationDTO.getBasePriceAdjustmentType() != null) {
//                productPriceMapper.batchModifyBasePriceEquals(productDayQuotationDTOList);
//            }
            if (null != productDayQuotationDTO.getModifiedBasePrice() && productDayQuotationDTO.getBasePriceAdjustmentType() != null) {
                switch (productDayQuotationDTO.getBasePriceAdjustmentType()) {
                    case 0:
                        productPriceMapper.batchModifyBasePriceAdd(productDayQuotationDTOList);
                        break;
                    case 1:
                        productPriceMapper.batchModifyBasePriceSubtract(productDayQuotationDTOList);
                            /*if (subtractFlag) {//如果是减少配额，需要将负数配额设置为0
                                List<Integer> quotaAccountIdList = new ArrayList<Integer>();
                                quotaAccountIdList.add(productPO1.getQuotaAccountId());
                                productQuotaMapper.updateQuotaLessThanZero(quotaAccountIdList);
                                productQuotaMapper.updateRemainingQuotaLessThanZero(quotaAccountIdList);
                            }*/
                        break;
                    case 2:
                        productPriceMapper.batchModifyBasePriceEquals(productDayQuotationDTOList);
                        break;
                    default:
                }

                //productPriceMapper.batchModifyBasePrice(productDayQuotationDTOList);
            }
            //修改房态配额
            if (null != productDayQuotationDTO.getQuotaAccountId() && null != productDayQuotationDTO.getQuotaAdjustmentType()) {
                //房态为不变时，传-1，设置为null
                if (null != productDayQuotationDTO.getRoomStatus() && productDayQuotationDTO.getRoomStatus() < 0) {
                    productDayQuotationDTO.setRoomStatus(null);
                }
                //是否可超为不变时，传-1，设置为null
                if (null != productDayQuotationDTO.getOverDraftStatus() && productDayQuotationDTO.getOverDraftStatus() < 0) {
                    productDayQuotationDTO.setOverDraftStatus(null);
                }
                switch (productDayQuotationDTO.getQuotaAdjustmentType()) {
                    case 0:
                        productQuotaMapper.batchModifyQuotaAdd(productDayQuotationDTOList);
                        break;
                    case 1:
                        productQuotaMapper.batchModifyQuotaSubtract(productDayQuotationDTOList);
                            /*if (subtractFlag) {//如果是减少配额，需要将负数配额设置为0
                                List<Integer> quotaAccountIdList = new ArrayList<Integer>();
                                quotaAccountIdList.add(productPO1.getQuotaAccountId());
                                productQuotaMapper.updateQuotaLessThanZero(quotaAccountIdList);
                                productQuotaMapper.updateRemainingQuotaLessThanZero(quotaAccountIdList);
                            }*/
                        break;
                    case 2:
                        productQuotaMapper.batchModifyQuotaEquals(productDayQuotationDTOList);
                        break;
                    default:
                }
            }

            IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
            incrementObjectDTO.setObject(productDayQuotationDTOList);
            incrementObjectDTO.setIncrementList(incrementList);
            initRemote.initBasePriceAndRoomStatus(incrementObjectDTO);

        }

        ProductLogPO productLogPO = new ProductLogPO();
        productLogPO.setProductId(productDayQuotationDTO.getProductId());
        productLogPO.setStartDate(productDayQuotationDTO.getSaleDate());
        productLogPO.setEndDate(productDayQuotationDTO.getSaleDate());
        productLogPO.setCreatedBy(productDayQuotationDTO.getUpdatedBy());
        productLogPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        String content = (productDayQuotationDTO.getBasePriceAdjustmentType() == null || productDayQuotationDTO.getModifiedBasePrice() == null ? "" : "底价" + AdjustmentTypeEnum.getDesc(productDayQuotationDTO.getBasePriceAdjustmentType()) + productDayQuotationDTO.getModifiedBasePrice() + ",")
                + (productDayQuotationDTO.getRoomStatus() == null ? "" : "关房设置改为" + RoomStatusEnum.getDesc(productDayQuotationDTO.getRoomStatus()) + ",")
                + (productDayQuotationDTO.getOverDraftStatus() == null ? "" : "售罄设置改为" + OverDraftStatusEnum.getDesc(productDayQuotationDTO.getOverDraftStatus()) + ",")
                + (productDayQuotationDTO.getQuotaAdjustmentType() == null || productDayQuotationDTO.getModifiedQuota() == null ? "" : "可售数量" + AdjustmentTypeEnum.getDesc(productDayQuotationDTO.getQuotaAdjustmentType()) + productDayQuotationDTO.getModifiedQuota() + ",");

        productLogPO.setContent(content.substring(0, content.length() - 1));
        productLogMapper.insert(productLogPO);


        // 每日条款的修改
        if (productDayQuotationDTO.getReservationTermStatus() != null) {
            Example example = new Example(DailyRestrictPO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("saleDate", productDayQuotationDTO.getSaleDate()).andEqualTo("productId", productDayQuotationDTO.getProductId());

            // 担保条款无论如何都要删除
            Example dgexample = new Example(DailyGuaranteePO.class);
            Example.Criteria dgcriteria = dgexample.createCriteria();
            dgcriteria.andEqualTo("saleDate", productDayQuotationDTO.getSaleDate()).andEqualTo("productId", productDayQuotationDTO.getProductId());
            dailyGuaranteeMapper.deleteByExample(dgexample);

            // 存在则新增
            if (CollectionUtil.isNotEmpty(productDayQuotationDTO.getGuarantees())) {
                List<DailyGuaranteePO> dailyGuarantees = new ArrayList<>();
                for (com.tiangong.product.dto.GuaranteeDTO guaranteeDTO : productDayQuotationDTO.getGuarantees()) {
                    DailyGuaranteePO dailyGuaranteePO = new DailyGuaranteePO();
                    dailyGuaranteePO.setProductId(productDayQuotationDTO.getProductId());
                    dailyGuaranteePO.setSaleDate(DateUtilX.stringToDate(productDayQuotationDTO.getSaleDate()));
                    dailyGuaranteePO.setGuaranteeType(guaranteeDTO.getGuaranteeType());
                    dailyGuaranteePO.setGuaranteeFeeType(guaranteeDTO.getGuaranteeFeeType());
                    dailyGuaranteePO.setGuaranteeCondition(guaranteeDTO.getGuaranteeCondition());
                    dailyGuarantees.add(dailyGuaranteePO);
                }
                dailyGuaranteeMapper.insertList(dailyGuarantees);
            }

            List<DailyRestrictPO> dailyRestrictPOS = dailyRestrictMapper.queryDailyRestrictList(productDayQuotationDTO.getProductId(), productDayQuotationDTO.getSaleDate());
            if (dailyRestrictPOS != null && !dailyRestrictPOS.isEmpty()) {
                if (productDayQuotationDTO.getReservationTermStatus() != 0) {
                    DailyRestrictPO dailyRestrictPO = new DailyRestrictPO();
                    dailyRestrictPO.setProductId(productDayQuotationDTO.getProductId());
                    dailyRestrictPO.setReservationTermStatus(productDayQuotationDTO.getReservationTermStatus());
                    dailyRestrictPO.setCancellationType(productDayQuotationDTO.getCancellationType());
                    dailyRestrictPO.setCancelPenaltiesType(productDayQuotationDTO.getCancelPenaltiesType());
                    dailyRestrictPO.setCancelPenaltiesValue(productDayQuotationDTO.getCancelPenaltiesValue());
                    dailyRestrictPO.setReservationAdvanceDays(productDayQuotationDTO.getReservationAdvanceDays());
                    dailyRestrictPO.setReservationDueTime(productDayQuotationDTO.getReservationDueTime());
                    dailyRestrictPO.setReservationLimitNights(productDayQuotationDTO.getReservationLimitNights());
                    dailyRestrictPO.setReservationLimitRooms(productDayQuotationDTO.getReservationLimitRooms());
                    dailyRestrictPO.setComparisonType(productDayQuotationDTO.getComparisonType());
                    dailyRestrictPO.setCancellationAdvanceDays(productDayQuotationDTO.getCancellationAdvanceDays());
                    dailyRestrictPO.setCancellationDeductionTerm(productDayQuotationDTO.getCancellationDeductionTerm());
                    dailyRestrictPO.setCancellationDueTime(productDayQuotationDTO.getCancellationDueTime());
                    dailyRestrictPO.setSaleDate(DateUtilX.stringToDate(productDayQuotationDTO.getSaleDate()));
                    dailyRestrictPO.setUpdatedBy(productDayQuotationDTO.getUpdatedBy());
                    dailyRestrictPO.setUpdatedDt(productDayQuotationDTO.getUpdatedDt());
                    // 自定义更新条款内容 为空也更新
                    dailyRestrictMapper.updateProductDailyRestrict(dailyRestrictPO);
                } else {
                    // 如果默认条款就删除每日条款信息
                    dailyRestrictMapper.deleteByExample(example);
                }
            } else {
                if (productDayQuotationDTO.getReservationTermStatus() != null && productDayQuotationDTO.getReservationTermStatus() != 0) {
                    DailyRestrictPO dailyRestrictPO = new DailyRestrictPO();
                    dailyRestrictPO.setProductId(productDayQuotationDTO.getProductId());
                    dailyRestrictPO.setReservationTermStatus(productDayQuotationDTO.getReservationTermStatus());
                    dailyRestrictPO.setCancellationType(productDayQuotationDTO.getCancellationType());
                    dailyRestrictPO.setCancelPenaltiesType(productDayQuotationDTO.getCancelPenaltiesType());
                    dailyRestrictPO.setCancelPenaltiesValue(productDayQuotationDTO.getCancelPenaltiesValue());
                    dailyRestrictPO.setReservationAdvanceDays(productDayQuotationDTO.getReservationAdvanceDays());
                    dailyRestrictPO.setReservationDueTime(productDayQuotationDTO.getReservationDueTime());
                    dailyRestrictPO.setReservationLimitNights(productDayQuotationDTO.getReservationLimitNights());
                    dailyRestrictPO.setReservationLimitRooms(productDayQuotationDTO.getReservationLimitRooms());
                    dailyRestrictPO.setComparisonType(productDayQuotationDTO.getComparisonType());
                    dailyRestrictPO.setCancellationAdvanceDays(productDayQuotationDTO.getCancellationAdvanceDays());
                    dailyRestrictPO.setCancellationDeductionTerm(productDayQuotationDTO.getCancellationDeductionTerm());
                    dailyRestrictPO.setCancellationDueTime(productDayQuotationDTO.getCancellationDueTime());
                    dailyRestrictPO.setSaleDate(DateUtilX.stringToDate(productDayQuotationDTO.getSaleDate()));
                    dailyRestrictMapper.insert(dailyRestrictPO);
                }
            }
        }

        List<String> increment = filterModifyRestrict(new ArrayList<>(Collections.singletonList(productDayQuotationDTO)));
        if (CollUtilX.isNotEmpty(increment)) {
            ProductDTO productDTO = CommonConvert.INSTANCE.productConvert(productDayQuotationDTO);
            IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
            incrementObjectDTO.setObject(new ArrayList<>(Collections.singletonList(productDTO)));
            incrementObjectDTO.setIncrementList(increment);
            initRemote.initRestrict(incrementObjectDTO);
        }

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 异步计算起价
                CompletableFuture.runAsync(() -> {
                    QueryProductIdsRequest productIdsRequest = new QueryProductIdsRequest();
                    productIdsRequest.setProductId(productDayQuotationDTO.getProductId());
                    List<HotelSaleInfoItemResp> itemRespList = productInfoService.selectProductHotel(productIdsRequest);
                    if (CollUtilX.isNotEmpty(itemRespList)) {
                        for (HotelSaleInfoItemResp resp : itemRespList) {
                            resp.setStartDate(productDayQuotationDTO.getSaleDate());
                            resp.setEndDate(productDayQuotationDTO.getSaleDate());
                            resp.setSaleStatus(productDayQuotationDTO.getRoomStatus());
                        }
                        RedisTemplateX.convertAndSend(RedisKey.CALCULATE_SELF_HOTEL_LOWEST, JSONUtil.toJsonStr(HotelSaleInfoResp.builder()
                                .logId(UUID.randomUUID().toString())
                                .respList(itemRespList)
                                .build()));
                    }
                }, processingProductInfoExecutor);
            }
        });
        dailySetDefaultZeroAdjustmentSalePrice(productDayQuotationDTO);
    }


    @Override
    public ProductSaleDateTaxDetailDTO queryProductSaleDateTaxDetail(QueryProductSaleDateTaxDetailDTO req) {
        ProductSaleDateTaxDetailDTO result = new ProductSaleDateTaxDetailDTO();
        // 1. 查询产品对应税费规则
        Set<Integer> productId = new HashSet<>();
        productId.add(req.getProductId());
        List<ProductTaxRuleDTO> productTaxRuleDTOS = productMapper.queryProductTaxRuleConfig(productId);
        ProTaxRuleConfigResp proTaxRuleConfigResp = null;
        if (CollUtilX.isNotEmpty(productTaxRuleDTOS)) {
            ProductTaxRuleDTO productTaxRuleDTO = productTaxRuleDTOS.get(0);
            if (productTaxRuleDTO.getTaxRuleConfigId() != null) {
                // 2. 查询税费明细规则
                ProTaxRuleConfigQueryReq queryReq = new ProTaxRuleConfigQueryReq();
                queryReq.setId(productTaxRuleDTO.getTaxRuleConfigId());
                proTaxRuleConfigResp = proTaxRuleConfigService.proTaxRuleConfigDetail(queryReq);

            }
        }
        // 3. 查询产品指定日期价格以及税费明细

        QueryProductDayPriceDTO queryProductDayPriceDTO = new QueryProductDayPriceDTO();
        queryProductDayPriceDTO.setProductId(req.getProductId());
        queryProductDayPriceDTO.setSaleDate(req.getSaleDate());
        List<ProductDayPriceDTO> productDayPriceDTOS = productPriceMapper.queryProductPriceList(queryProductDayPriceDTO);
        if (CollectionUtil.isNotEmpty(productDayPriceDTOS)) {
            ProductDayPriceDTO productDayPrice = productDayPriceDTOS.get(0);

            // 4. 组装返回
            result.setProductId(req.getProductId());
            result.setSaleDate(productDayPrice.getSaleDate());
            result.setBasePrice(productDayPrice.getBasePrice());
            if (result.getBasePrice() != null) {
                // 组装价格明细
                getAllFaxPrice(result, proTaxRuleConfigResp.getProTaxDetails());
            }

            if (CollectionUtil.isNotEmpty(proTaxRuleConfigResp.getProTaxDetails())) {
                List<ProTaxDetailDTO> taxDetails = CommonConvert.INSTANCE.proTaxDetailDTOConvert(proTaxRuleConfigResp.getProTaxDetails());
                result.setProTaxDetails(taxDetails);
            }
            // 组装总税费
            BigDecimal totalTax = result.getSalesTax() == null ? BigDecimal.ZERO : result.getSalesTax()
                    .add(result.getTax() == null ? BigDecimal.ZERO : result.getTax())
                    .add(result.getOtherTaxFee() == null ? BigDecimal.ZERO : result.getOtherTaxFee());
            result.setTotalTax(totalTax);
        }
        return result;
    }

    @Override
    @Transactional
    public void batchModifyBasePrice(List<BatchQuotationDTO> batchQuotationDTOList) {
        // 组装每日价格
        List<ProductDayQuotationDTO> productDayQuotationDTOList = this.assemblyProductDayQuotation(batchQuotationDTOList, 1);

        List<String> increment = filterModifyData(productDayQuotationDTOList);


        List<ProductDayQuotationDTO> productDayQuotationAdd = new ArrayList<ProductDayQuotationDTO>();
        List<ProductDayQuotationDTO> productDayQuotationSubstract = new ArrayList<ProductDayQuotationDTO>();
        List<ProductDayQuotationDTO> productDayQuotationEquals = new ArrayList<ProductDayQuotationDTO>();
        productDayQuotationDTOList.forEach(t -> {
            if (t.getBasePriceAdjustmentType() != null) {
                switch (t.getBasePriceAdjustmentType()) {
                    case 0:
                        productDayQuotationAdd.add(t);
                        break;
                    case 1:
                        productDayQuotationSubstract.add(t);
                            /*if (subtractFlag) {//如果是减少配额，需要将负数配额设置为0
                                List<Integer> quotaAccountIdList = new ArrayList<Integer>();
                                quotaAccountIdList.add(productPO1.getQuotaAccountId());
                                productQuotaMapper.updateQuotaLessThanZero(quotaAccountIdList);
                                productQuotaMapper.updateRemainingQuotaLessThanZero(quotaAccountIdList);
                            }*/
                        break;
                    case 2:
                        productDayQuotationEquals.add(t);
                        break;
                    default:
                }
                // 24-09-12 湫
//                productDayQuotationEquals.add(t);
            }
        });

        if (CollectionUtils.isNotEmpty(productDayQuotationAdd)) {
            productPriceMapper.batchModifyBasePriceAdd(productDayQuotationAdd);
        }

        if (CollectionUtils.isNotEmpty(productDayQuotationSubstract)) {
            productPriceMapper.batchModifyBasePriceSubtract(productDayQuotationSubstract);
        }


        if (CollUtilX.isNotEmpty(productDayQuotationEquals)) {
            productPriceMapper.batchModifyBasePriceEquals(productDayQuotationEquals);
        }

        // 需要计算起价的列表
        // key=hotelId_supplierCode
        Map<String, HotelSaleInfoItemResp> hotelSaleInfoRespMap = new HashMap<>();

        List<ProductLogPO> productLogPOList = new ArrayList<>();
        for (BatchQuotationDTO batchQuotationDTO : batchQuotationDTOList) {
            ProductLogPO productLogPO = new ProductLogPO();
            productLogPO.setProductId(batchQuotationDTO.getProductId());
            productLogPO.setStartDate(batchQuotationDTO.getStartDate());
            productLogPO.setEndDate(batchQuotationDTO.getEndDate());
            productLogPO.setCreatedBy(batchQuotationDTO.getUpdatedBy());
            productLogPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
            productLogPO.setOperationWeek(batchQuotationDTO.getWeekList());

            String content = (batchQuotationDTO.getBasePriceAdjustmentType() == null || batchQuotationDTO.getModifiedBasePrice() == null ? "" : "底价" + AdjustmentTypeEnum.getDesc(batchQuotationDTO.getBasePriceAdjustmentType()) + batchQuotationDTO.getModifiedBasePrice() + ",")
                    + (batchQuotationDTO.getRoomStatus() == null ? "" : "关房设置改为" + RoomStatusEnum.getDesc(batchQuotationDTO.getRoomStatus()) + ",")
                    + (batchQuotationDTO.getOverDraftStatus() == null ? "" : "售罄设置改为" + OverDraftStatusEnum.getDesc(batchQuotationDTO.getOverDraftStatus()) + ",")
                    + (batchQuotationDTO.getQuotaAdjustmentType() == null || batchQuotationDTO.getModifiedQuota() == null ? "" : "可售数量" + AdjustmentTypeEnum.getDesc(batchQuotationDTO.getQuotaAdjustmentType()) + batchQuotationDTO.getModifiedQuota() + ",");
            productLogPO.setContent(content.substring(0, content.length() - 1));
            productLogPOList.add(productLogPO);

            // 组装需要计算起价数据
            String key = String.format("%s_%s", batchQuotationDTO.getHotelId(), batchQuotationDTO.getSupplierCode());
            HotelSaleInfoItemResp hotelSaleInfoResp = hotelSaleInfoRespMap.get(key);
            if (hotelSaleInfoResp == null) {
                hotelSaleInfoRespMap.put(key, HotelSaleInfoItemResp.builder()
                        .hotelId(batchQuotationDTO.getHotelId())
                        .supplierCode(batchQuotationDTO.getSupplierCode())
                        .startDate(batchQuotationDTO.getStartDate())
                        .endDate(batchQuotationDTO.getEndDate())
                        .build());
            } else {
                // 取时间更早的
                if (DateUtilX.compare(DateUtilX.stringToDate(hotelSaleInfoResp.getStartDate()), DateUtilX.stringToDate(batchQuotationDTO.getStartDate())) == 1) {
                    hotelSaleInfoResp.setStartDate(batchQuotationDTO.getStartDate());
                }
                // 取时间更晚的
                if (DateUtilX.compare(DateUtilX.stringToDate(hotelSaleInfoResp.getEndDate()), DateUtilX.stringToDate(batchQuotationDTO.getEndDate())) == -1) {
                    hotelSaleInfoResp.setEndDate(batchQuotationDTO.getEndDate());
                }
            }
        }
        productLogMapper.insertList(productLogPOList);


        if (CollUtilX.isNotEmpty(productDayQuotationDTOList)) {
            IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
            incrementObjectDTO.setObject(productDayQuotationDTOList);
            incrementObjectDTO.setIncrementList(increment);
            initRemote.initBasePriceAndRoomStatus(incrementObjectDTO);
        }

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 提交队列处理
                RedisTemplateX.convertAndSend(RedisKey.CALCULATE_SELF_HOTEL_LOWEST, JSONUtil.toJsonStr(HotelSaleInfoResp.builder()
                        .logId(UUID.randomUUID().toString())
                        .respList(new ArrayList<>(hotelSaleInfoRespMap.values()))
                        .build()));
            }
        });

        //批量设置底价之后，加幅默认设置+0
        setDefaultZeroAdjustmentSalePrice(batchQuotationDTOList);
    }

    @Override
    @Transactional
    public void batchModifyRoomStatus(List<BatchQuotationDTO> batchQuotationDTOList) {
        //组装每日房态
        List<ProductDayQuotationDTO> productDayQuotationDTOList = this.assemblyProductDayQuotation(batchQuotationDTOList, 2);
        //批量修改房态配额
        //拆分成三部分更新：增加、减少，等于
        List<ProductDayQuotationDTO> productDayQuotationDTOListAdd = new ArrayList<>();
        List<ProductDayQuotationDTO> productDayQuotationDTOListSubtract = new ArrayList<>();
        List<ProductDayQuotationDTO> productDayQuotationDTOListEquals = new ArrayList<>();

        List<String> incrementList = filterModifyData(productDayQuotationDTOList);
        for (ProductDayQuotationDTO productDayQuotationDTO : productDayQuotationDTOList) {
            if (null != productDayQuotationDTO.getQuotaAdjustmentType()) {
                switch (productDayQuotationDTO.getQuotaAdjustmentType()) {
                    case 0:
                        productDayQuotationDTOListAdd.add(productDayQuotationDTO);
                        break;
                    case 1:
                        productDayQuotationDTOListSubtract.add(productDayQuotationDTO);
                        break;
                    case 2:
                        productDayQuotationDTOListEquals.add(productDayQuotationDTO);
                        break;
                    default:
                }
            }
        }

        if (CollUtilX.isNotEmpty(productDayQuotationDTOListAdd)) {
            productQuotaMapper.batchModifyQuotaAdd(productDayQuotationDTOListAdd);
        }
        if (CollUtilX.isNotEmpty(productDayQuotationDTOListSubtract)) {
            productQuotaMapper.batchModifyQuotaSubtract(productDayQuotationDTOListSubtract);
               /* if (null != quotaAccountIdMap) {
                    List<Integer> quotaAccountIdList = new ArrayList<Integer>();
                    for (Integer quotaAccountId : quotaAccountIdMap.keySet()) {
                        quotaAccountIdList.add(quotaAccountId);
                    }
                    productQuotaMapper.updateRemainingQuotaLessThanZero(quotaAccountIdList);
                    productQuotaMapper.updateQuotaLessThanZero(quotaAccountIdList);
                }*/

        }
        if (CollUtilX.isNotEmpty(productDayQuotationDTOListEquals)) {
            productQuotaMapper.batchModifyQuotaEquals(productDayQuotationDTOListEquals);
        }

        List<ProductLogPO> productLogPOList = new ArrayList<ProductLogPO>();
        for (BatchQuotationDTO batchQuotationDTO : batchQuotationDTOList) {
            ProductLogPO productLogPO = new ProductLogPO();
            productLogPO.setProductId(batchQuotationDTO.getProductId());
            productLogPO.setStartDate(batchQuotationDTO.getStartDate());
            productLogPO.setEndDate(batchQuotationDTO.getEndDate());
            productLogPO.setCreatedBy(batchQuotationDTO.getUpdatedBy());
            productLogPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
            productLogPO.setOperationWeek(batchQuotationDTO.getWeekList());

            String content = (batchQuotationDTO.getBasePriceAdjustmentType() == null || batchQuotationDTO.getModifiedBasePrice() == null ? "" : "底价" + AdjustmentTypeEnum.getDesc(batchQuotationDTO.getBasePriceAdjustmentType()) + batchQuotationDTO.getModifiedBasePrice() + ",")
                    + (batchQuotationDTO.getRoomStatus() == null ? "" : "关房设置改为" + RoomStatusEnum.getDesc(batchQuotationDTO.getRoomStatus()) + ",")
                    + (batchQuotationDTO.getOverDraftStatus() == null ? "" : "售罄设置改为" + OverDraftStatusEnum.getDesc(batchQuotationDTO.getOverDraftStatus()) + ",")
                    + (batchQuotationDTO.getQuotaAdjustmentType() == null || batchQuotationDTO.getModifiedQuota() == null ? "" : "可售数量" + AdjustmentTypeEnum.getDesc(batchQuotationDTO.getQuotaAdjustmentType()) + batchQuotationDTO.getModifiedQuota() + ",");
            productLogPO.setContent(content.substring(0, content.length() - 1));
            productLogPOList.add(productLogPO);
        }
        productLogMapper.insertList(productLogPOList);

        IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
        incrementObjectDTO.setIncrementList(incrementList);
        incrementObjectDTO.setObject(productDayQuotationDTOList);
        initRemote.initBasePriceAndRoomStatus(incrementObjectDTO);
    }

    /**
     * 查询产品列表供订单使用
     * TODO 该接口幂等性不行，需要优化
     */
    @Override
    public PaginationSupportDTO<ProductOrderQueryDTO> queryOrderProductList(ProductOrderQueryRequestDTO request) {
        if (request.getHotelId() == null || request.getRoomId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        List<ProductOrderQueryDTO> productOrderQueryDTOS = new CopyOnWriteArrayList<>();

        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("companyCode", request.getCompanyCode());
        requestMap.put("hotelId", request.getHotelId().toString());

        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            requestMap.put("supplierCode", request.getSupplierCode());
        }

        Response<List<FuzzySupplierDTO>> resp = orderQueryRemote.querySupplier(requestMap);

        if (resp != null && resp.getResult() == 1 && resp.getModel() != null) {
            List<FuzzySupplierDTO> list = resp.getModel();
            if (CollUtilX.isNotEmpty(list)) {

                if (StrUtilX.isNotEmpty(request.getSupplierCode())) { //若是有供应商传入，则只查询该供应商
                    request.setIsDirectlyConnectedSupplier(list.get(0).getIsDirectlyConnectedSupplier());
                    request.setSupplierName(list.get(0).getSupplierName());
                    List<ProductOrderQueryDTO> products = queryCachedProduct(request);
                    if (CollUtilX.isNotEmpty(products)) {
                        productOrderQueryDTOS.addAll(products);
                    }

                } else {
                    // 自签产品
                    List<ProductOrderQueryDTO> orderQueryDTOS = queryCachedProduct(request);
                    if (CollUtilX.isNotEmpty(orderQueryDTOS)) {
                        productOrderQueryDTOS.addAll(orderQueryDTOS);
                    }
                }
            }
        }

        if (CollUtilX.isNotEmpty(productOrderQueryDTOS)) {
            productOrderQueryDTOS = productOrderQueryDTOS.stream()
                    .sorted((x, y) -> x.getTotalAmt().compareTo(y.getTotalAmt()))
                    .collect(Collectors.toList());

            List<ProductOrderQueryDTO> partProduct = productOrderQueryDTOS
                    .subList((request.getCurrentPage() - 1) * request.getPageSize(),
                            (Math.min(request.getCurrentPage() * request.getPageSize(), productOrderQueryDTOS.size())));

            PaginationSupportDTO<ProductOrderQueryDTO> paginationSupportDTO = new PaginationSupportDTO<>();
            paginationSupportDTO.setItemList(partProduct);
            paginationSupportDTO.setTotalCount(productOrderQueryDTOS.size());
            paginationSupportDTO.setCurrentPage(request.getCurrentPage());
            paginationSupportDTO.setPageSize(request.getPageSize());
            paginationSupportDTO.setTotalPage((productOrderQueryDTOS.size() / request.getPageSize()) +
                    (productOrderQueryDTOS.size() % request.getPageSize() == 0 ? 0 : 1));
            return paginationSupportDTO;
        } else {
            return new PaginationSupportDTO<>();
        }
    }

    /**
     * 查询非直连的供应商的产品
     */
    private List<ProductOrderQueryDTO> queryCachedProduct(ProductOrderQueryRequestDTO request) {
        QueryProductRequestDTO queryProductRequestDTO = new QueryProductRequestDTO();

        List<Long> hotelId = new ArrayList<>();
        hotelId.add(request.getHotelId());
        queryProductRequestDTO.setOffShelveStatus(request.getOffShelveStatus());
        queryProductRequestDTO.setCompanyCode(request.getCompanyCode());
        queryProductRequestDTO.setHotelIdList(hotelId);
        queryProductRequestDTO.setRoomId(request.getRoomId());
        queryProductRequestDTO.setSupplierCode(request.getSupplierCode());
        List<ProductTempDTO> productTempDTOS = productMapper.querySaleProducts(queryProductRequestDTO);

        List<ProductOrderQueryDTO> productOrderQueryDTO = new ArrayList<>();
        Map<Integer, BigDecimal> totalMap = new HashMap<>();
        if (request.getStartDate() != null) {
            List<TotalAmtDTO> totalAmtDTOS = productMapper.queryTotalAmt(request);
            for (TotalAmtDTO totalAmt : totalAmtDTOS) {
                totalMap.merge(totalAmt.getProductId(), totalAmt.getBasePrice(), BigDecimal::add);
            }
        }

        for (ProductTempDTO productTempDTOS1 : productTempDTOS) {

            if (request.getStartDate() != null && (null == totalMap.get(productTempDTOS1.getProductId())
                    || totalMap.get(productTempDTOS1.getProductId()).compareTo(BigDecimal.ZERO) == 0)) {
                continue;
            }
            ProductOrderQueryDTO productOrderQueryDTO1 = new ProductOrderQueryDTO();
            productOrderQueryDTO1.setProductId(productTempDTOS1.getProductId());
            productOrderQueryDTO1.setProductName(productTempDTOS1.getProductName());
            productOrderQueryDTO1.setRoomId(productTempDTOS1.getRoomId());
            productOrderQueryDTO1.setBedTypes(productTempDTOS1.getBedTypes());
            productOrderQueryDTO1.setPurchaseType(productTempDTOS1.getPurchaseType());
            productOrderQueryDTO1.setSupplierCode(productTempDTOS1.getSupplierCode());
            productOrderQueryDTO1.setSupplierName(productTempDTOS1.getSupplierName());
            productOrderQueryDTO1.setBreakfastQty(productTempDTOS1.getBreakfastQty());
            productOrderQueryDTO1.setCurrency(productTempDTOS1.getCurrency());
            productOrderQueryDTO1.setRoomQty(request.getRoomQty());
            productOrderQueryDTO1.setTotalAmt(null == totalMap.get(productTempDTOS1.getProductId()) ?
                    BigDecimal.ZERO : totalMap.get(productTempDTOS1.getProductId()).multiply(
                    null == request.getRoomQty() ? BigDecimal.ONE : new BigDecimal(request.getRoomQty())));
            productOrderQueryDTO1.setIsSpProduct(0);
            productOrderQueryDTO1.setCancellationType(productTempDTOS1.getCancellationType());
            productOrderQueryDTO1.setCancellationAdvanceDays(productTempDTOS1.getCancellationAdvanceDays());
            productOrderQueryDTO1.setCancellationDueTime(productTempDTOS1.getCancellationDueTime());
            productOrderQueryDTO1.setCancellationDeductionTerm(productTempDTOS1.getCancellationDeductionTerm());
            productOrderQueryDTO1.setComparisonType(productTempDTOS1.getComparisonType());
            productOrderQueryDTO1.setReservationLimitNights(productTempDTOS1.getReservationLimitNights());
            productOrderQueryDTO1.setReservationDueTime(productTempDTOS1.getReservationDueTime());
            productOrderQueryDTO1.setReservationLimitRooms(productTempDTOS1.getReservationLimitRooms());
            productOrderQueryDTO1.setReservationAdvanceDays(productTempDTOS1.getReservationAdvanceDays());
            productOrderQueryDTO1.setCancelPenaltiesType(productTempDTOS1.getCancelPenaltiesType());
            productOrderQueryDTO1.setCancelPenaltiesValue(productTempDTOS1.getCancelPenaltiesValue());
            productOrderQueryDTO.add(productOrderQueryDTO1);
        }

        return productOrderQueryDTO;
    }

    @Override
    public PageInfo<ProductLogDTO> queryProductLogList(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("productId")) || StrUtilX.isEmpty(requestMap.get("saleDate"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        PageHelper.startPage(Integer.parseInt(requestMap.get("currentPage")), Integer.parseInt(requestMap.get("pageSize")));
        List<ProductLogDTO> productLogDTOS = productMapper.queryProductLogList(requestMap);

        //计算星期几
        String saleDate = requestMap.get("saleDate");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtilX.stringToDate(saleDate));
        String week = String.valueOf(calendar.get(Calendar.DAY_OF_WEEK) - 1);

        //批量操作时候，可能会有适应星期几的操作，这里处理，若是不在该操作范围内移除
        productLogDTOS.removeIf(productLogDTO -> productLogDTO.getOperationWeek() != null && StrUtilX.isNotEmpty(productLogDTO.getOperationWeek()) && !productLogDTO.getOperationWeek().contains(week));
        return new PageInfo<>(productLogDTOS);
    }

    /**
     * 修改停售状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyOffShelveStatus(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("productId")) || StrUtilX.isEmpty(requestMap.get("offShelveStatus"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        ProductPO productPO = new ProductPO();
        productPO.setProductId(Integer.valueOf(requestMap.get("productId")));
        productPO.setOffShelveStatus(Integer.valueOf(requestMap.get("offShelveStatus")));
        productPO.setUpdatedBy(requestMap.get("updatedBy"));
        productPO.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));

        productMapper.updateByPrimaryKeySelective(productPO);

        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(Integer.valueOf(requestMap.get("productId")));
        productDTO.setOffShelveStatus(Integer.valueOf(requestMap.get("offShelveStatus")));

        IncrementDTO incrementDTO = new IncrementDTO();
        incrementDTO.setProductId(Integer.valueOf(requestMap.get("productId")));
        incrementDTO.setType(IncrementTypeEnum.CLOSE_PRODUCT.no);

        IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
        incrementObjectDTO.setObject(new ArrayList<>(Collections.singletonList(productDTO)));
        incrementObjectDTO.setIncrementList(new ArrayList<>(Collections.singletonList(JSON.toJSONString(incrementObjectDTO))));
        initRemote.initProductInfo(incrementObjectDTO);

        // 调整产品销售状态时加日志
        ProductLogPO productLogPO = new ProductLogPO();
        productLogPO.setStartDate(DateUtilX.dateToString(new Date()));
        Calendar instance = Calendar.getInstance();
        instance.set(9999, 99, 99);
        Date date = instance.getTime();
        productLogPO.setEndDate(String.valueOf(date));
        productLogPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        productLogPO.setCreatedBy(requestMap.get("updatedBy"));
        productLogPO.setProductId(Integer.valueOf(requestMap.get("productId")));
        String content = Integer.parseInt(requestMap.get("offShelveStatus")) == 0 ? "产品状态改为停售" : "产品状态改为可售";
        productLogPO.setContent(content);
        productLogMapper.insert(productLogPO);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 异步计算起价
                CompletableFuture.runAsync(() -> {
                    QueryProductIdsRequest productIdsRequest = new QueryProductIdsRequest();
                    productIdsRequest.setProductId(Integer.valueOf(requestMap.get("productId")));
                    List<HotelSaleInfoItemResp> itemRespList = productInfoService.selectProductHotel(productIdsRequest);
                    if (CollUtilX.isNotEmpty(itemRespList)) {
                        for (HotelSaleInfoItemResp resp : itemRespList) {
                            resp.setSaleStatus(Integer.valueOf(requestMap.get("offShelveStatus")));
                        }
                        RedisTemplateX.convertAndSend(RedisKey.CALCULATE_SELF_HOTEL_LOWEST, JSONUtil.toJsonStr(HotelSaleInfoResp.builder()
                                .logId(UUID.randomUUID().toString())
                                .respList(itemRespList)
                                .build()));
                    }
                }, processingProductInfoExecutor);
            }
        });
    }

    @Override
    @Transactional
    public void batchModifyHotelProductStatus(BatchModifyHotelProductStatusDTO batchModifyHotelProductStatusDTO) {
        if (StrUtilX.isEmpty(batchModifyHotelProductStatusDTO.getStartDate()) || StrUtilX.isEmpty(batchModifyHotelProductStatusDTO.getEndDate()) ||
                StrUtilX.isEmpty(batchModifyHotelProductStatusDTO.getWeekList()) || batchModifyHotelProductStatusDTO.getRoomStatus() == null ||
                StrUtilX.isEmpty(batchModifyHotelProductStatusDTO.getPurchaseType())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        List<Integer> productIds = productMapper.queryWillModifyProductStatus(batchModifyHotelProductStatusDTO);
        List<BatchQuotationDTO> batchQuotationDTOList = new ArrayList<>();
        for (Integer id : productIds) {
            BatchQuotationDTO batchQuotationDTO = CommonConvert.INSTANCE.batchQuotationDTOConvert(batchModifyHotelProductStatusDTO);
            batchQuotationDTO.setProductId(id);
            batchQuotationDTO.setQuotaAdjustmentType(0);
            batchQuotationDTOList.add(batchQuotationDTO);
        }
        batchModifyRoomStatus(batchQuotationDTOList);
    }

    /**
     * 过滤修改条款
     */
    private List<String> filterModifyRestrict(List<ProductDayQuotationDTO> productDayQuotationDTOS) {
        if (CollUtilX.isNotEmpty(productDayQuotationDTOS)) {

            List<String> incrementList = new ArrayList<>();
            for (ProductDayQuotationDTO productDayQuotationDTO : productDayQuotationDTOS) {
                // 标记是否修改了, 0为未修改，1为修改
                int type = 0;

                // 每日条款
                String redisKey = StrUtilX.concat(String.valueOf(productDayQuotationDTO.getProductId()), "_", productDayQuotationDTO.getSaleDate());
                ProductRestrictDTO productRestrictDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, redisKey), ProductRestrictDTO.class);

                //为默认条款
                if (productDayQuotationDTO.getReservationTermStatus() != null && productDayQuotationDTO.getReservationTermStatus() == 0) {
                    if (productRestrictDTO != null) {
                        type = 1;
                    }
                } else { // 自定义
                    if (productRestrictDTO == null) {
                        type = 1;
                    } else {
                        if (StrUtilX.isNotEmpty(productRestrictDTO.getCancellationDeductionTerm()) && !productRestrictDTO.getCancellationDeductionTerm().equals(productDayQuotationDTO.getCancellationDeductionTerm())) {
                            type = 1;
                        } else if (productRestrictDTO.getCancellationType() != null && !productRestrictDTO.getCancellationType().equals(productDayQuotationDTO.getCancellationType())) {
                            type = 1;
                        } else if (productRestrictDTO.getComparisonType() != null && !productRestrictDTO.getComparisonType().equals(productDayQuotationDTO.getComparisonType())) {
                            type = 1;
                        } else if (productRestrictDTO.getReservationLimitRooms() != null && !productRestrictDTO.getReservationLimitRooms().equals(productDayQuotationDTO.getReservationLimitRooms())) {
                            type = 1;
                        } else if (productRestrictDTO.getReservationLimitNights() != null && !productRestrictDTO.getReservationLimitNights().equals(productDayQuotationDTO.getReservationLimitNights())) {
                            type = 1;
                        } else if (StrUtilX.isNotEmpty(productRestrictDTO.getReservationDueTime()) && !productRestrictDTO.getReservationDueTime().equals(productDayQuotationDTO.getReservationDueTime())) {
                            type = 1;
                        } else if (productRestrictDTO.getReservationAdvanceDays() != null && !productRestrictDTO.getReservationAdvanceDays().equals(productDayQuotationDTO.getReservationAdvanceDays())) {
                            type = 1;
                        } else if (productRestrictDTO.getCancellationAdvanceDays() != null && !productRestrictDTO.getCancellationAdvanceDays().equals(productDayQuotationDTO.getCancellationAdvanceDays())) {
                            type = 1;
                        } else if (productRestrictDTO.getCancellationDueTime() != null && !productRestrictDTO.getCancellationDueTime().equals(productDayQuotationDTO.getCancellationDueTime())) {
                            type = 1;
                        } else if (productRestrictDTO.getCancelPenaltiesType() != null && !productRestrictDTO.getCancelPenaltiesType().equals(productDayQuotationDTO.getCancelPenaltiesType())) {
                            type = 1;
                        } else if (productRestrictDTO.getCancelPenaltiesValue() != null && !productRestrictDTO.getCancelPenaltiesValue().equals(productDayQuotationDTO.getCancelPenaltiesValue())) {
                            type = 1;
                        } else if (compareGuarantees(productRestrictDTO.getGuarantees(), productDayQuotationDTO.getGuarantees())) {
                            type = 1;
                        }
                    }
                }

                if (type == 1) {
                    IncrementDTO incrementDTO = new IncrementDTO();
                    incrementDTO.setSaleDate(new HashSet<>(Collections.singletonList(productDayQuotationDTO.getSaleDate())));
                    incrementDTO.setProductId(productDayQuotationDTO.getProductId());
                    incrementDTO.setType(IncrementTypeEnum.RESTRICT.no);
                    incrementList.add(JSON.toJSONString(incrementDTO));
                }
            }

            return incrementList;
        }
        return null;
    }

    /**
     * list 比较 相同 true 不同 false
     */
    private boolean compareGuarantees(List<GuaranteeDTO> oldList, List<com.tiangong.product.dto.GuaranteeDTO> newList) {
        if (CollectionUtil.isEmpty(oldList) && CollectionUtil.isEmpty(newList)) {
            // 都为空
            return true;
        } else if (CollectionUtil.isEmpty(oldList) && CollectionUtil.isNotEmpty(newList)) {
            // 某一个为空
            return false;
        } else if (CollectionUtil.isNotEmpty(oldList) && CollectionUtil.isEmpty(newList)) {
            // 某一个为空
            return false;
        } else if (oldList.size() != newList.size()) {
            // 长度不一致
            return false;
        }

        // 循环比较bean
        for (int i = 0; i < oldList.size(); i++) {
            GuaranteeDTO oldGua = oldList.get(i);
            com.tiangong.product.dto.GuaranteeDTO newGua = newList.get(i);

            if (!oldGua.getGuaranteeType().equals(newGua.getGuaranteeType())) {
                return false;
            } else if (!oldGua.getGuaranteeFeeType().equals(oldGua.getGuaranteeFeeType())) {
                return false;
            } else if (!oldGua.getGuaranteeCondition().equals(newGua.getGuaranteeCondition())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 组装配额账号
     */
    private QuotaAccountPO assemblyQuotaAccount(ProductDTO productDTO) {
        QuotaAccountPO quotaAccountPO = new QuotaAccountPO();
        quotaAccountPO.setHotelId(productDTO.getHotelId());
        quotaAccountPO.setRoomId(productDTO.getRoomId());
        quotaAccountPO.setActive(1);
        quotaAccountPO.setQuotaAccountName(productDTO.getProductName());
        quotaAccountPO.setSupplierCode(productDTO.getSupplierCode());
        quotaAccountPO.setCreatedDt(productDTO.getCreatedDt());
        quotaAccountPO.setCreatedBy(productDTO.getCreatedBy());
        return quotaAccountPO;
    }

    /**
     * 判断是否上架售卖
     */
    private Boolean checkSaleStatus(ProductSaleStatusPO productSaleStatusPO) {
        if (null != productSaleStatusPO) {
            //B2B
            return null != productSaleStatusPO.getB2bSaleStatus() && productSaleStatusPO.getB2bSaleStatus() > 0;
        }
        return false;
    }

    /**
     * 组装酒店产品数据
     */
    private HotelProductsDTO assemblyHotelProducts(List<ProductTempDTO> productTempDTOList, List<CompanyChannelPO> companyChannelPOList, Long hotelId, String companyCode, List<String> dateList, String language, Map<Long, ProTaxRuleConfigResp> longProTaxRuleConfigRespMap) {
        HotelProductsDTO hotelProductsDTO = new HotelProductsDTO();
        hotelProductsDTO.setHotelId(hotelId);
        Map<Integer, ProductRoomDTO> roomMap = new HashMap<>();//房型
        Map<Integer, Map<Integer, ProductForShowDTO>> roomProductsMap = new HashMap<>();//房型对应产品
        Map<Integer, List<ProductSaleItemDTO>> productSaleItemDTOMap = new HashMap<>();//产品对应行情信息

        productTempDTOList.forEach(productTempDTO -> {
            ProductSaleItemDTO productSaleItemDTO = CommonConvert.INSTANCE.productSaleItemDTOConvert(productTempDTO);

            // 设置税费
            if (productTempDTO.getPriceType() != null
                    && productTempDTO.getPriceType().equals(1)
                    && productTempDTO.getBasePrice() != null) {
                ProTaxRuleConfigResp proTaxRuleConfigResp = longProTaxRuleConfigRespMap.get(productTempDTO.getTaxRuleConfigId());
                if (proTaxRuleConfigResp != null
                        && CollectionUtil.isNotEmpty(proTaxRuleConfigResp.getProTaxDetails())) {
                    getFaxPrice(productSaleItemDTO, productTempDTO.getBasePrice(), proTaxRuleConfigResp.getProTaxDetails());
                } else {
                    productSaleItemDTO.setRoomPrice(productSaleItemDTO.getBasePrice());
                }
            } else {
                productSaleItemDTO.setRoomPrice(productSaleItemDTO.getBasePrice());
            }

            //组装酒店信息
            if (StrUtilX.isEmpty(hotelProductsDTO.getHotelName())) {
                hotelProductsDTO.setHotelName(productTempDTO.getHotelName());
            }
            //组装房型信息
            if (null == roomMap.get(productTempDTO.getRoomId())) {
                ProductRoomDTO productRoomDTO = new ProductRoomDTO();
                productRoomDTO.setRoomId(productTempDTO.getRoomId());
                productRoomDTO.setRoomName(productTempDTO.getRoomName());
                if (StrUtilX.isNotEmpty(productTempDTO.getRoomBedTypes())) {
                    String roomBedTypes = productTempDTO.getRoomBedTypes();
                    productRoomDTO.setBedTypes(productTempDTO.getRoomBedTypes());
                }

                roomMap.put(productTempDTO.getRoomId(), productRoomDTO);
            }
            //组装房型产品信息
            if (null == roomProductsMap.get(productTempDTO.getRoomId())) {
                Map<Integer, ProductForShowDTO> productForShowDTOMap = new HashMap<>();
                ProductForShowDTO productForShowDTO = this.assemblyProduct(productTempDTO, companyChannelPOList);
                productForShowDTOMap.put(productTempDTO.getProductId(), productForShowDTO);
                roomProductsMap.put(productTempDTO.getRoomId(), productForShowDTOMap);
            } else if (null == roomProductsMap.get(productTempDTO.getRoomId()).get(productTempDTO.getProductId())) {
                ProductForShowDTO productForShowDTO = this.assemblyProduct(productTempDTO, companyChannelPOList);
                roomProductsMap.get(productTempDTO.getRoomId()).put(productTempDTO.getProductId(), productForShowDTO);
            }
            //组装每日行情信息
            if (null == productSaleItemDTOMap.get(productTempDTO.getProductId())) {
                List<ProductSaleItemDTO> productSaleItemDTOList = new ArrayList<>();
                productSaleItemDTOList.add(productSaleItemDTO);
                productSaleItemDTOMap.put(productTempDTO.getProductId(), productSaleItemDTOList);
            } else {
                productSaleItemDTOMap.get(productTempDTO.getProductId()).add(productSaleItemDTO);
            }
        });
        //封装产品到房型
        for (Integer roomId : roomProductsMap.keySet()) {
            List<ProductForShowDTO> productForShowDTOList = new ArrayList<>();
            for (Integer productId : roomProductsMap.get(roomId).keySet()) {
                roomProductsMap.get(roomId).get(productId).setSaleItemList(productSaleItemDTOMap.get(productId));
                productForShowDTOList.add(roomProductsMap.get(roomId).get(productId));
            }
            //产品倒叙排序
            productForShowDTOList.sort((ProductForShowDTO p1, ProductForShowDTO p2) -> p2.getProductId() - p1.getProductId());
            //dateList;
            for (ProductForShowDTO productForShowDTO : productForShowDTOList) {
                if (com.tiangong.util.CollUtilX.isNotEmpty(productForShowDTO.getSaleItemList())) {
                    // 无论如何都需要排序
//                    if (productForShowDTO.getSaleItemList().size()==dateList.size()) {
                    List<ProductSaleItemDTO> saleItemListSort = new ArrayList<>();
                    Map<String, ProductSaleItemDTO> productSaleItemDTOMap1 = productForShowDTO.getSaleItemList().stream()
                            .collect(Collectors.toMap(ProductSaleItemDTO::getSaleDate, Function.identity(), (s1, s2) -> s1, LinkedHashMap::new));
                    for (String dateStr : dateList) {
                        if (!productSaleItemDTOMap1.containsKey(dateStr)) {
                            ProductSaleItemDTO productSaleItemDTO = new ProductSaleItemDTO();
                            productSaleItemDTO.setSaleDate(dateStr);
                            saleItemListSort.add(productSaleItemDTO);
                        } else {
                            saleItemListSort.add(productSaleItemDTOMap1.get(dateStr));
                        }
                    }
                    productForShowDTO.setSaleItemList(saleItemListSort);
//                    }
                }
            }
            roomMap.get(roomId).setProductList(productForShowDTOList);
        }

        List<RoomtypeDTO> roomInfo = new ArrayList<>();

        HotelInfoCollectionReq hotelInfoCollectionReq = new HotelInfoCollectionReq();
        hotelInfoCollectionReq.setHotelIds(Collections.singletonList(hotelId));
        hotelInfoCollectionReq.setLanguageType(language);
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.hotelName);
        settings.add(BaseHotelInfoUrl.rooms);
        hotelInfoCollectionReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> response = hotelRemote.queryHotelInfo(hotelInfoCollectionReq);

        if (response.isSuccess() && response.getModel() != null) {
            if (CollUtilX.isNotEmpty(response.getModel().getRooms())) {
                roomInfo = response.getModel().getRooms();
            }
            //没有产品的情况，补充酒店Id和酒店名称
            if (StrUtilX.isEmpty(hotelProductsDTO.getHotelName())) {
                hotelProductsDTO.setHotelName(response.getModel().getHotelName());
            }
        }
        // 封装房型到酒店
        List<ProductRoomDTO> productRoomDTOList = new ArrayList<>();
        HashMap<Long, RoomtypeDTO> roomInfoMap = new HashMap<>();
        for (RoomtypeDTO roomtypeDTO : roomInfo) {
            if (roomtypeDTO.getRoomtypeId() != null) {
                roomInfoMap.put(roomtypeDTO.getRoomtypeId(), roomtypeDTO);
            }
        }
        for (Integer roomId : roomMap.keySet()) {
            ProductRoomDTO productRoomDTO = roomMap.get(roomId);
            RoomtypeDTO roomtypeDTO = roomInfoMap.get(roomId.longValue());
            if (roomtypeDTO != null) {
                productRoomDTO.setRoomName(roomtypeDTO.getRoomtypeName());
                if (CollUtilX.isNotEmpty(roomtypeDTO.getRoomBed())) {
                    productRoomDTO.setBedTypes(JSONUtil.toJsonStr(roomtypeDTO.getRoomBed()));
                }
            }
            productRoomDTOList.add(productRoomDTO);
            roomInfoMap.remove(roomId.longValue());
        }
        for (Long roomId : roomInfoMap.keySet()) {
            ProductRoomDTO productRoomDTO = new ProductRoomDTO();
            productRoomDTO.setRoomId(roomInfoMap.get(roomId).getRoomtypeId().intValue());
            productRoomDTO.setRoomName(roomInfoMap.get(roomId).getRoomtypeName());
            if (CollUtilX.isNotEmpty(roomInfoMap.get(roomId).getRoomBed())) {
                productRoomDTO.setBedTypes(JSONUtil.toJsonStr(roomInfoMap.get(roomId).getRoomBed()));
            }
            productRoomDTOList.add(productRoomDTO);
        }
        hotelProductsDTO.setRoomList(productRoomDTOList.stream().sorted(Comparator.comparingInt(ProductRoomDTO::getRoomId).reversed()).collect(Collectors.toList()));
        return hotelProductsDTO;
    }

    /**
     * 组装产品数据
     */
    private ProductForShowDTO assemblyProduct(ProductTempDTO productTempDTO, List<CompanyChannelPO> companyChannelPOList) {
        ProductForShowDTO productForShowDTO = CommonConvert.INSTANCE.productForShowDTOConvert(productTempDTO);
        StringBuilder saleChannelSB = new StringBuilder();
        StringBuilder noSaleChannelSB = new StringBuilder();
        if (null != companyChannelPOList) {
            StringBuffer channelSb = new StringBuffer();
            companyChannelPOList.forEach(companyChannelPO -> {
                channelSb.append(companyChannelPO.getChannelId()).append(",");
            });
            //商家配置的所有渠道
            String channelStr = channelSb.toString();
            if (channelStr.contains(String.valueOf(ChannelEnum.B2B.no))) {
                if (null != productTempDTO.getB2bSaleStatus() && productTempDTO.getB2bSaleStatus().equals(1)) {
                    saleChannelSB.append(ChannelEnum.B2B.value).append(",");
                } else {
                    noSaleChannelSB.append(ChannelEnum.B2B.value).append(",");
                }
            }
        }
        //销售中渠道
        if (saleChannelSB.length() > 0) {
            productForShowDTO.setOnSaleChannels(saleChannelSB.substring(0, saleChannelSB.length() - 1));
        }
        //仓库中渠道
        if (noSaleChannelSB.length() > 0) {
            productForShowDTO.setOffShelveChannels(noSaleChannelSB.substring(0, noSaleChannelSB.length() - 1));
        }
        return productForShowDTO;
    }

    /**
     * 组装产品每日行情数据（用于批量调整）
     * type = 1 价格
     * type = 2 房态和配额
     * Type = 2 时，需要查询配额账号Id
     */
    private List<ProductDayQuotationDTO> assemblyProductDayQuotation(List<BatchQuotationDTO> batchQuotationDTOList, int type) {
        List<ProductDayQuotationDTO> productDayQuotationDTOList = new ArrayList<>();

        Map<Integer, Long> productTaxRuleMap = new HashMap<>();
        // 24-09-12 湫
//        Map<Long, ProTaxRuleConfigResp> longProTaxRuleConfigRespMap= new HashMap<>();
//        if (type == 1) {
//            // 所有产品id
//            Set<Integer> productId = new HashSet<>();
//            for (BatchQuotationDTO batchQuotationDTO : batchQuotationDTOList) {
//                productId.add(batchQuotationDTO.getProductId());
//            }
//            // 获取产品对应的价税费类型
//            List<ProductTaxRuleDTO> productTaxRuleDTOS = productMapper.queryProductTaxRuleConfig(productId);
//            productTaxRuleMap = new HashMap<>();
//            longProTaxRuleConfigRespMap = new HashMap<>();
//            if (CollectionUtil.isNotEmpty(productTaxRuleDTOS)) {
//                productTaxRuleMap = productTaxRuleDTOS.stream().collect(Collectors.toMap(ProductTaxRuleDTO::getProductId, ProductTaxRuleDTO::getTaxRuleConfigId));
//                Set<Long> productTaxRuleIdSet = productTaxRuleDTOS.stream().map(ProductTaxRuleDTO::getTaxRuleConfigId).collect(Collectors.toSet());
//                longProTaxRuleConfigRespMap = proTaxRuleConfigService.proTaxRuleConfigDetailList(productTaxRuleIdSet);
//            }
//        }
        for (BatchQuotationDTO batchQuotationDTO : batchQuotationDTOList) {


            //房态为不变时，传-1，设置为null
            if (null != batchQuotationDTO.getRoomStatus() && batchQuotationDTO.getRoomStatus() < 0) {
                batchQuotationDTO.setRoomStatus(null);
            }
            //是否可超为不变时，传-1，设置为null
            if (null != batchQuotationDTO.getOverDraftStatus() && batchQuotationDTO.getOverDraftStatus() < 0) {
                batchQuotationDTO.setOverDraftStatus(null);
            }
            //默认最大调整一年的价格，超过一年自动截取
            Date endDate = DateUtilX.stringToDate(batchQuotationDTO.getEndDate());
            if (DateUtilX.getDay(DateUtilX.getCurrentDate(), endDate) > 365) {
                endDate = DateUtilX.getDate(DateUtilX.getCurrentDate(), 12);//结束时间最大为当前时间加一年
            }
            //type=2时，查询配额账号Id
            ProductPO productPO = new ProductPO();
            productPO.setProductId(batchQuotationDTO.getProductId());
            //TODO 一个个查询可能会有瓶颈
            ProductPO productPO1 = productMapper.selectByPrimaryKey(productPO);
            batchQuotationDTO.setHotelId(productPO1.getHotelId());
            batchQuotationDTO.setSupplierCode(productPO1.getSupplierCode());

            if (DateUtilX.compare(DateUtilX.stringToDate(batchQuotationDTO.getStartDate()), endDate) <= 0 && null != productPO1 && null != productPO1.getQuotaAccountId()) {
                // 24-09-12 湫
//               // 每日价格列表 当价格发生变更时 因为税费原因，需要重新使用房费计算税费
//                Map<String, ProductDayPriceDTO> productDayPriceMap = new HashMap<>();
//                if (type == 1 ) {
//                    QueryProductDayPriceDTO queryProductDayPriceDTO = new QueryProductDayPriceDTO();
//                    queryProductDayPriceDTO.setProductId(batchQuotationDTO.getProductId());
//                    queryProductDayPriceDTO.setBeginDate(batchQuotationDTO.getStartDate());
//                    queryProductDayPriceDTO.setEndDate(batchQuotationDTO.getEndDate());
//                    // 价格变动 需要查询改产品指定日期的所有价格需要查询某个产品 指定时间段内的所有价格 进行计算后修改
//                    List<ProductDayPriceDTO> productDayPriceDTOS = productPriceMapper.queryProductPriceList(queryProductDayPriceDTO);
//                    if(CollectionUtil.isNotEmpty(productDayPriceDTOS)){
//                        productDayPriceMap= productDayPriceDTOS.stream().collect(Collectors.toMap(ProductDayPriceDTO::getSaleDate, productDayPriceDTO -> productDayPriceDTO));
//                    }
//                }

                List<Date> dateList = DateUtilX.getDateInWeekList(DateUtilX.stringToDate(batchQuotationDTO.getStartDate()), endDate, batchQuotationDTO.getWeekList());
                if (CollUtilX.isNotEmpty(dateList)) {
                    for (Date date : dateList) {
                        ProductDayQuotationDTO productDayQuotationDTO = CommonConvert.INSTANCE.productDayQuotationDTOConvert(batchQuotationDTO);
                        productDayQuotationDTO.setSaleDate(DateUtilX.dateToString(date));
                        productDayQuotationDTO.setModifiedQuota(null != batchQuotationDTO.getModifiedQuota() ? batchQuotationDTO.getModifiedQuota() : 0);
                        if (type == 2) {
                            productDayQuotationDTO.setQuotaAccountId(productPO1.getQuotaAccountId());
                            productDayQuotationDTO.setModifiedBasePrice(null);
                            productDayQuotationDTO.setBasePriceAdjustmentType(null);
                            if (productDayQuotationDTO.getQuotaAdjustmentType() != null && productDayQuotationDTO.getQuotaAdjustmentType().equals(1)) {
                                //调整方式为减少的时候，配额值改为负数
                                productDayQuotationDTO.setModifiedQuota(-productDayQuotationDTO.getModifiedQuota());
                            }
                        }
                        if (type == 1) {
                            // 24-09-12 湫
//                            productDayQuotationDTO.setModifiedQuota(null);
//                            productDayQuotationDTO.setQuotaAdjustmentType(null);
//                            Long taxRuleConfigId = productTaxRuleMap.get(batchQuotationDTO.getProductId());
//                            List<ProTaxDetailBase> proTaxDetails = null;
//                            if (taxRuleConfigId != null) {
//                                ProTaxRuleConfigResp proTaxRuleConfigResp = longProTaxRuleConfigRespMap.get(taxRuleConfigId);
//                                if (proTaxRuleConfigResp != null) {
//                                    proTaxDetails= proTaxRuleConfigResp.getProTaxDetails();
//
//                                }
//                            }
//                            // roomPrice = 旧的房费 + 修改后价格 < 0则过滤
//                            ProductDayPriceDTO productDayPriceDTO = productDayPriceMap.get(DateUtilX.dateToString(date));
//                            if(productDayPriceDTO!=null){
//                                // 用房费计算税费
//                                //1. 获取旧的房费  可能存在一种情况 房费为0 ，但是存在底价 那么底价=房费，因为是含税价格
//                                BigDecimal roomPrice = productDayPriceDTO.getRoomPrice() == null ? productDayPriceDTO.getBasePrice() : productDayPriceDTO.getRoomPrice();
//
//                                //2. 通过旧的房费计算新的房费
//                                if(productDayQuotationDTO.getBasePriceAdjustmentType().equals(0)){
//                                    // 加
//                                    roomPrice = roomPrice.add(productDayQuotationDTO.getModifiedBasePrice());
//                                }else if(productDayQuotationDTO.getBasePriceAdjustmentType().equals(1)){
//                                    // 减
//                                    roomPrice = roomPrice.subtract(productDayQuotationDTO.getModifiedBasePrice());
//                                }else if(productDayQuotationDTO.getBasePriceAdjustmentType().equals(2)){
//                                    // 等于
//                                    roomPrice =productDayQuotationDTO.getModifiedBasePrice();
//                                }
//                                // 3. 判断新的房费是否大于0
//                                if (roomPrice.compareTo(BigDecimal.ZERO) > 0){
//                                    //设置新的房费
//                                    getFaxPrice(productDayQuotationDTO, roomPrice, proTaxDetails);
//                                }else {
//                                    continue;
//                                }
//                            }else {
//                                // 原先不存在价格 这时候需要新增
//                                getFaxPrice(productDayQuotationDTO, productDayQuotationDTO.getModifiedBasePrice(), proTaxDetails);
//                            }
                            productDayQuotationDTO.setModifiedQuota(null);
                            productDayQuotationDTO.setQuotaAdjustmentType(null);
                            if (productDayQuotationDTO.getBasePriceAdjustmentType() != null && productDayQuotationDTO.getBasePriceAdjustmentType().equals(1)) {
                                //调整方式为减少的时候，配额值改为负数
                                productDayQuotationDTO.setModifiedBasePrice(BigDecimal.ZERO.subtract(productDayQuotationDTO.getModifiedBasePrice()));
                            }
                        }
                        productDayQuotationDTOList.add(productDayQuotationDTO);
                    }
                }
            }
        }
        return productDayQuotationDTOList;
    }

    /**
     * 获取税后价格
     */
    private void getFaxPrice(ProductSaleItemDTO productDayQuotationDTO, BigDecimal roomPrice, List<ProTaxDetailBase> proTaxDetails) {
        if (roomPrice == null || roomPrice.compareTo(BigDecimal.ZERO) == 0) {
            return;
        } else if (CollectionUtil.isNotEmpty(proTaxDetails)) {

            // 存在税费
            BigDecimal basePrice = BigDecimal.ZERO;
            BigDecimal salesTax = BigDecimal.ZERO;
            BigDecimal tax = BigDecimal.ZERO;
            BigDecimal payAtHotelFee = BigDecimal.ZERO;
            BigDecimal otherTaxFee = BigDecimal.ZERO;

            for (ProTaxDetailBase proTaxDetail : proTaxDetails) {
                switch (proTaxDetail.getTaxType()) {
                    case 0:
                        //销售税
                        salesTax = salesTax.add(getTax(roomPrice, proTaxDetail));
                        break;
                    case 1:
                        //销售税
                        tax = tax.add(getTax(roomPrice, proTaxDetail));
                        break;
                    case 2:
                        // 到店付
                        payAtHotelFee = payAtHotelFee.add(getTax(roomPrice, proTaxDetail));
                        break;
                    case 3:
                        // 到店付
                        otherTaxFee = otherTaxFee.add(getTax(roomPrice, proTaxDetail));
                        break;
                }
            }
            // 修改后的价格  底价=房费+所有税费 不含到点另付费用
            basePrice = roomPrice.add(salesTax).add(tax).add(otherTaxFee);
            // 房费=房费
            productDayQuotationDTO.setRoomPrice(roomPrice);
            productDayQuotationDTO.setBasePrice(basePrice);
            productDayQuotationDTO.setSalesTax(salesTax);
            productDayQuotationDTO.setTax(tax);
            productDayQuotationDTO.setPayAtHotelFee(payAtHotelFee);
            productDayQuotationDTO.setOtherTaxFee(otherTaxFee);
        } else {
            // 不存在税费 则 底价=房费
            productDayQuotationDTO.setRoomPrice(productDayQuotationDTO.getRoomPrice());
            productDayQuotationDTO.setBasePrice(productDayQuotationDTO.getRoomPrice());

        }
    }

    /**
     * 获取税后价格
     */
    private void getAllFaxPrice(ProductSaleDateTaxDetailDTO productDayQuotationDTO, List<ProTaxDetailBase> proTaxDetails) {
        // 税后价格   房费 = 底价
        BigDecimal roomPrice = productDayQuotationDTO.getBasePrice();
        if (roomPrice == null || roomPrice.compareTo(BigDecimal.ZERO) == 0) {
        } else if (CollectionUtil.isNotEmpty(proTaxDetails)) {
            // 存在税费
            BigDecimal basePrice = BigDecimal.ZERO;
            BigDecimal salesTax = BigDecimal.ZERO;
            BigDecimal tax = BigDecimal.ZERO;
            BigDecimal payAtHotelFee = BigDecimal.ZERO;
            BigDecimal otherTaxFee = BigDecimal.ZERO;

            for (ProTaxDetailBase proTaxDetail : proTaxDetails) {
                switch (proTaxDetail.getTaxType()) {
                    case 0:
                        //销售税
                        salesTax = salesTax.add(getTax(roomPrice, proTaxDetail));
                        break;
                    case 1:
                        //销售税
                        tax = tax.add(getTax(roomPrice, proTaxDetail));
                        break;
                    case 2:
                        // 到店付
                        payAtHotelFee = payAtHotelFee.add(getTax(roomPrice, proTaxDetail));
                        break;
                    case 3:
                        // 到店付
                        otherTaxFee = otherTaxFee.add(getTax(roomPrice, proTaxDetail));
                        break;
                }
            }

            // 房费=房费
            productDayQuotationDTO.setRoomPrice(roomPrice);
            productDayQuotationDTO.setSalesTax(salesTax);
            productDayQuotationDTO.setTax(tax);
            productDayQuotationDTO.setPayAtHotelFee(payAtHotelFee);
            productDayQuotationDTO.setOtherTaxFee(otherTaxFee);
            // 修改后的价格  底价=房费+所有税费 不含到点另付费用
            basePrice = roomPrice.add(salesTax).add(tax).add(otherTaxFee);
            productDayQuotationDTO.setBasePrice(basePrice);
        } else {
            // 不存在税费 则 底价=房费
            productDayQuotationDTO.setRoomPrice(productDayQuotationDTO.getRoomPrice());
        }
    }


    /**
     * 获取税费
     */
    private BigDecimal getTax(BigDecimal roomPrice, ProTaxDetailBase proTaxDetail) {
        if (proTaxDetail.getTaxIncreaseType() != null && proTaxDetail.getTaxIncreaseValue() != null) {
            if (proTaxDetail.getTaxIncreaseType().equals(TaxIncreaseEnum.FIXED.getCode())) {
                // 固定
                return proTaxDetail.getTaxIncreaseValue();
            } else {
                // 百分比
                return roomPrice.multiply(proTaxDetail.getTaxIncreaseValue().divide(new BigDecimal(100)));
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 筛选修改了的价格房态配额信息
     */
    private List<String> filterModifyData(List<ProductDayQuotationDTO> productDayQuotationList) {

        if (CollUtilX.isEmpty(productDayQuotationList)) {
            return null;
        }

        Map<String, IncrementDTO> incrementMap = new HashMap<>();

        Iterator<ProductDayQuotationDTO> iterator = productDayQuotationList.iterator();
        while (iterator.hasNext()) {
            ProductDayQuotationDTO productDayQuotationDTO = iterator.next();

            //从redis中取出来的数据
            ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productBasePriceAndRoomStatusKey, StrUtilX.concat(String.valueOf(productDayQuotationDTO.getProductId()), "_", productDayQuotationDTO.getSaleDate())), ProductBasePriceAndRoomStatusDTO.class);
            int type = -1; //初始化类型，为了做判断


            //价格是否有变化
            if (productDayQuotationDTO.getModifiedBasePrice() != null && productDayQuotationDTO.getBasePriceAdjustmentType() != null) {
                if (productBasePriceAndRoomStatusDTO == null || productBasePriceAndRoomStatusDTO.getBasePrice() == null) {
                    type = IncrementTypeEnum.PRICE.no;
                } else if (productDayQuotationDTO.getBasePriceAdjustmentType() != 2) {
                    if (productDayQuotationDTO.getModifiedBasePrice().compareTo(BigDecimal.ZERO) != 0) {
                        type = IncrementTypeEnum.PRICE.no;
                    }
                } else {
                    if (productBasePriceAndRoomStatusDTO.getBasePrice().compareTo(productDayQuotationDTO.getModifiedBasePrice()) != 0) {
                        type = IncrementTypeEnum.PRICE.no;
                    }
                }
            }

            int flag = 0;
            // 判断可超类型
            if (productDayQuotationDTO.getOverDraftStatus() != null) {
                if (productBasePriceAndRoomStatusDTO == null || productBasePriceAndRoomStatusDTO.getOverDraftStatus() == null) {
                    flag = 1;
                } else if (!productBasePriceAndRoomStatusDTO.getOverDraftStatus().equals(productDayQuotationDTO.getOverDraftStatus())) {
                    flag = 1;
                }
            }

            // 判断房态
            if (productDayQuotationDTO.getRoomStatus() != null) {
                if (productBasePriceAndRoomStatusDTO == null || productBasePriceAndRoomStatusDTO.getRoomStatus() == null) {
                    flag = 1;
                } else if (!productBasePriceAndRoomStatusDTO.getRoomStatus().equals(productDayQuotationDTO.getRoomStatus())) {
                    flag = 1;
                }
            }

            // 判断可售数量
            if (productDayQuotationDTO.getQuotaAdjustmentType() != null && productDayQuotationDTO.getModifiedQuota() != null) {
                if (productBasePriceAndRoomStatusDTO == null || productBasePriceAndRoomStatusDTO.getRemainingQuota() == null || productBasePriceAndRoomStatusDTO.getQuota() == null) {
                    flag = 1;
                } else if (productDayQuotationDTO.getQuotaAdjustmentType() != 2) {
                    if (productDayQuotationDTO.getModifiedQuota() != 0) {
                        flag = 1;
                    }
                } else {
                    if (!productDayQuotationDTO.getModifiedQuota().equals(productBasePriceAndRoomStatusDTO.getRemainingQuota())
                            || !productDayQuotationDTO.getModifiedQuota().equals(productBasePriceAndRoomStatusDTO.getQuota())) {
                        flag = 1;
                    }
                }
            }

            if (flag == 1) {
                if (type == -1) {
                    type = IncrementTypeEnum.ROOM_STATUS.no;
                } else {
                    type = IncrementTypeEnum.PRICE_AND_ROOM_STATUS.no;
                }
            }

            if (type != -1) {
                if (!incrementMap.isEmpty() && incrementMap.get(String.valueOf(productDayQuotationDTO.getProductId())) != null) {
                    incrementMap.get(String.valueOf(productDayQuotationDTO.getProductId())).getSaleDate().add(productDayQuotationDTO.getSaleDate());
                } else {
                    IncrementDTO incrementDTO = new IncrementDTO();
                    incrementDTO.setProductId(productDayQuotationDTO.getProductId());
                    incrementDTO.setType(type);
                    incrementDTO.setSaleDate(new HashSet<>(Collections.singletonList(productDayQuotationDTO.getSaleDate())));
                    incrementMap.put(String.valueOf(productDayQuotationDTO.getProductId()), incrementDTO);
                }
            } else {
                iterator.remove();
            }
        }

        if (!incrementMap.isEmpty()) {
            return incrementMap.values().stream().map(JSON::toJSONString).collect(Collectors.toList());
        }
        return null;
    }


    /**
     * 筛选修改的条款信息
     */
    private List<String> handlerProductRestrict(List<ProductRestrictPO> productRestrictPOS) {
        if (CollUtilX.isEmpty(productRestrictPOS)) {
            return null;
        }

        List<String> incrementDTOS = new ArrayList<>();
        Iterator<ProductRestrictPO> poIterable = productRestrictPOS.iterator();
        while (poIterable.hasNext()) {
            ProductRestrictPO productRestrictPO = poIterable.next();

            String redisKey = String.valueOf(productRestrictPO.getProductId());
            if (StrUtilX.isNotEmpty(productRestrictPO.getSaleDate())) {
                redisKey = redisKey + "_" + productRestrictPO.getSaleDate();
            }
            com.tiangong.dis.dto.ProductRestrictDTO productRestrictDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, redisKey), com.tiangong.dis.dto.ProductRestrictDTO.class);
            int type = -1;

            if (productRestrictPO.getCancellationAdvanceDays() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getCancellationAdvanceDays() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getCancellationAdvanceDays().equals(productRestrictDTO.getCancellationAdvanceDays())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (StrUtilX.isNotEmpty(productRestrictPO.getCancellationDeductionTerm())) {
                if (productRestrictDTO == null || StrUtilX.isEmpty(productRestrictDTO.getCancellationDeductionTerm())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getCancellationDeductionTerm().equals(productRestrictDTO.getCancellationDeductionTerm())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (StrUtilX.isNotEmpty(productRestrictPO.getCancellationDueTime())) {
                if (productRestrictDTO == null || StrUtilX.isEmpty(productRestrictDTO.getCancellationDueTime())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getCancellationDueTime().equals(productRestrictDTO.getCancellationDueTime())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (productRestrictPO.getCancellationType() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getCancellationType() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getCancellationType().equals(productRestrictDTO.getCancellationType())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (productRestrictPO.getComparisonType() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getComparisonType() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getComparisonType().equals(productRestrictDTO.getComparisonType())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (productRestrictPO.getReservationAdvanceDays() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getReservationAdvanceDays() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getReservationAdvanceDays().equals(productRestrictDTO.getReservationAdvanceDays())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (StrUtilX.isNotEmpty(productRestrictPO.getReservationDueTime())) {
                if (productRestrictDTO == null || StrUtilX.isEmpty(productRestrictDTO.getReservationDueTime())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getReservationDueTime().equals(productRestrictDTO.getReservationDueTime())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (productRestrictPO.getReservationLimitNights() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getReservationLimitNights() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getReservationLimitNights().equals(productRestrictDTO.getReservationLimitNights())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (productRestrictPO.getReservationLimitRooms() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getReservationLimitRooms() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getReservationLimitRooms().equals(productRestrictDTO.getReservationLimitRooms())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (productRestrictPO.getCancellationType() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getCancellationType() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getCancellationType().equals(productRestrictDTO.getCancellationType())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }

            if (productRestrictPO.getCancelPenaltiesValue() != null) {
                if (productRestrictDTO == null || productRestrictDTO.getCancelPenaltiesValue() == null) {
                    type = IncrementTypeEnum.RESTRICT.no;
                } else if (!productRestrictPO.getCancelPenaltiesValue().equals(productRestrictDTO.getCancelPenaltiesValue())) {
                    type = IncrementTypeEnum.RESTRICT.no;
                }
            }


            if (type != -1) {
                IncrementDTO increment = new IncrementDTO();
                increment.setProductId(productRestrictPO.getProductId());
                increment.setType(type);
                incrementDTOS.add(JSON.toJSONString(increment));
            } else {
                poIterable.remove();
            }
        }
        return incrementDTOS;
    }

    /**
     * 筛选修改的早餐
     */
    private List<String> handlerBreakfast(List<ProductDTO> productDTOS) {
        if (CollUtilX.isEmpty(productDTOS)) {
            return null;
        }

        List<String> incrementDTOS = new ArrayList<>();
        for (ProductDTO productDTO : productDTOS) {
            IncrementDTO incrementDTO = new IncrementDTO();
            ProductDTO product = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productInfoKey, String.valueOf(productDTO.getProductId())), ProductDTO.class);

            if (productDTO.getBreakfastQty() != null)
                if (product == null || product.getBreakfastQty() == null || !productDTO.getBreakfastQty().equals(product.getBreakfastQty())) {
                    incrementDTO.setProductId(productDTO.getProductId());
                    incrementDTO.setType(IncrementTypeEnum.BREAKFAST.no);
                    incrementDTOS.add(JSON.toJSONString(incrementDTO));
                }
        }

        return incrementDTOS;
    }

    /**
     * 筛选修改的产品名
     */
    private List<String> handlerProductName(List<ProductDTO> productDTOS) {
        if (CollUtilX.isEmpty(productDTOS)) {
            return null;
        }

        List<String> incrementDTOS = new ArrayList<>();
        for (ProductDTO productDTO : productDTOS) {
            IncrementDTO incrementDTO = new IncrementDTO();
            ProductDTO product = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productInfoKey, String.valueOf(productDTO.getProductId())), ProductDTO.class);

            if (StrUtilX.isNotEmpty(productDTO.getProductName()))
                if (product == null || StrUtilX.isEmpty(product.getProductName()) || !productDTO.getProductName().equals(product.getProductName())) {
                    incrementDTO.setProductId(productDTO.getProductId());
                    incrementDTO.setType(IncrementTypeEnum.PRODUCT_NAME.no);
                    incrementDTOS.add(JSON.toJSONString(incrementDTO));
                }
        }

        return incrementDTOS;
    }

    @Override
    public com.tiangong.product.dto.ProductRestrictDTO queryProductRestrict(Map<String, String> requestMap) {
        // 设置币种和支付方式
        ProductDTO productDTO = productMapper.queryProduct(requestMap.get("productId"));
        // 查询自定义产品条款
        com.tiangong.product.dto.ProductRestrictDTO productRestrictDTO = productPriceMapper.queryProductRestrictDate(requestMap.get("productId"), requestMap.get("saleDate"));

        if (productRestrictDTO == null) {
            // 不存在每日的则查询默认条款
            productRestrictDTO = productPriceMapper.queryProductRestrictDefault(requestMap.get("productId"));
        }
        productRestrictDTO.setSaleDate(requestMap.get("saleDate"));
        productRestrictDTO.setCurrency(productDTO.getCurrency());
        productRestrictDTO.setPayMethod(productDTO.getPayMethod());
        return productRestrictDTO;
    }

    /**
     * 查询条款list
     */
    @Override
    public List<ProductDTO> queryProductRestrictList(QueryProductRestrictDTO queryProductRestrictDTO) {
        List<ProductDTO> productRestrictList = productMapper.queryProductRestrictList(queryProductRestrictDTO);

        if (CollUtilX.isEmpty(productRestrictList)) {
            productRestrictList = assemblyProductRestrictList(queryProductRestrictDTO);
        }
        return productRestrictList;
    }

    @Override
    public ProductDTO queryProductById(Map<String, String> requestMap) {
        return productMapper.queryProduct(requestMap.get("productId"));
    }

    /**
     * 初始化redis产品
     */
    @Override
    public void initLostRedisProductBaseInfo(Map<String, String> requestMap) {
        Integer productNum = productMapper.querySelfSignedProductCount(requestMap);

        int loop = (productNum / 50) + (productNum % 50 == 0 ? 0 : 1);
        for (int i = 1; i <= loop; i++) {
            PageHelper.startPage(i, 50);
            List<ProductDTO> productDTOS = productMapper.querySelfSignedProductInfo(requestMap);
            List<String> breakIncrementList = handlerBreakfast(productDTOS);

            if (CollUtilX.isNotEmpty(breakIncrementList)) {
                //早餐数增量+产品信息
                IncrementObjectDTO productInfo = new IncrementObjectDTO();
                productInfo.setIncrementList(breakIncrementList);
                productInfo.setObject(productDTOS);
                initRemote.initProductInfo(productInfo);
            }

            List<ProductRestrictPO> productRestrictList = productMapper.querySelfSignedProductRestrict(productDTOS);
            List<String> restrictIncrementList = handlerProductRestrict(productRestrictList);

            if (CollUtilX.isNotEmpty(restrictIncrementList)) {
                //条款信息
                IncrementObjectDTO restrictInfo = new IncrementObjectDTO();
                restrictInfo.setObject(productRestrictList);
                restrictInfo.setIncrementList(restrictIncrementList);
                initRemote.initRestrict(restrictInfo);
            }

            List<ProductDayQuotationDTO> productDayQuotationDTOS = productMapper.querySelfSignedBasePriceAndRoomStatus(productDTOS);
            List<String> basePriceAndRoomStatusList = filterModifyData(productDayQuotationDTOS);

            if (CollUtilX.isNotEmpty(basePriceAndRoomStatusList)) {
                //底价和房态信息
                IncrementObjectDTO basePriceAndRoomStatusInfo = new IncrementObjectDTO();
                basePriceAndRoomStatusInfo.setObject(productDayQuotationDTOS);
                basePriceAndRoomStatusInfo.setIncrementList(basePriceAndRoomStatusList);
                initRemote.initBasePriceAndRoomStatus(basePriceAndRoomStatusInfo);
            }

            List<ProductDayIncreasePO> productDayIncreasePOList = productMapper.querySelfSignedSalePrice(productDTOS);
            List<String> dayIncreaseList = handlerProductIncrease(productDayIncreasePOList);

            if (CollUtilX.isNotEmpty(dayIncreaseList)) {
                //售价
                IncrementObjectDTO salePriceInfo = new IncrementObjectDTO();
                salePriceInfo.setObject(productDayIncreasePOList);
                salePriceInfo.setIncrementList(dayIncreaseList);
                initRemote.initSalePrice(salePriceInfo);
            }

            List<ProductSaleStatusPO> productSaleStatusPOList = productMapper.querySelfSignedSaleStatus(productDTOS);
            List<String> saleStatusList = handlerProductSaleStatus(productSaleStatusPOList);

            if (CollUtilX.isNotEmpty(saleStatusList)) {
                //销售状态
                IncrementObjectDTO saleStatusInfo = new IncrementObjectDTO();
                saleStatusInfo.setObject(productSaleStatusPOList);
                saleStatusInfo.setIncrementList(saleStatusList);
                initRemote.initSaleStatus(saleStatusInfo);
            }
        }
    }

    public List<HotelLabelDTO> selectAgentHotelLabel(HotelPriceReq hotelPriceReq) {
        return productMapper.selectAgentHotelLabel(hotelPriceReq);
    }

    /**
     * 组装每日条款(查询redis)
     */
    private List<ProductDTO> assemblyProductRestrictList(QueryProductRestrictDTO queryProductRestrictDTO) {
        Set<String> saleDateList = DateUtilX.getDateListToString(DateUtilX.stringToDate(queryProductRestrictDTO.getStartDate()),
                DateUtilX.stringToDate(queryProductRestrictDTO.getEndDate()));


        List<ProductDTO> productDTOS = new ArrayList<>();
        for (String saleDate : saleDateList) {
            ProductRestrictDTO productRestrictDTO = StrUtilX.parseObject(
                    RedisTemplateX.hashGet(RedisKey.productRestrictKey, StrUtilX.concat(String.valueOf(queryProductRestrictDTO.getProductId()), "_", saleDate)),
                    ProductRestrictDTO.class);

            if (productRestrictDTO == null) {
                productRestrictDTO = StrUtilX.parseObject(
                        RedisTemplateX.hashGet(RedisKey.productRestrictKey, String.valueOf(queryProductRestrictDTO.getProductId())), ProductRestrictDTO.class);
            }

            ProductDTO productDTO = CommonConvert.INSTANCE.productRestrictDTOConvert(productRestrictDTO);
            productDTOS.add(productDTO);
        }
        return productDTOS;
    }

    /**
     * 过滤产品是否有变化
     */
    private List<String> handlerProductSaleStatus(List<ProductSaleStatusPO> productSaleStatusList) {
        if (CollUtilX.isEmpty(productSaleStatusList)) {
            return null;
        }

        List<String> incrementList = new ArrayList<>();
        Iterator<ProductSaleStatusPO> iterator = productSaleStatusList.iterator();
        while (iterator.hasNext()) {
            ProductSaleStatusPO productSaleStatusPO = iterator.next();
            String redisKey = StrUtilX.concat(productSaleStatusPO.getCompanyCode(), "_", String.valueOf(productSaleStatusPO.getProductId()));

            ProductSaleStatusDTO productSaleStatusDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSaleStatusKey, redisKey), ProductSaleStatusDTO.class);
            int type = -1;
            String channelCode;

            if ((productSaleStatusPO.getB2bSaleStatus() != null) && (productSaleStatusDTO == null || productSaleStatusDTO.getB2bSaleStatus() == null
                    || (!productSaleStatusDTO.getB2bSaleStatus().equals(productSaleStatusPO.getB2bSaleStatus())))) {
                channelCode = ChannelEnum.B2B.key;
                type = IncrementTypeEnum.ON_SALE.no;

                assemblySaleStatusIncrementMap(incrementList, productSaleStatusPO, channelCode, type);
            }

            if (type == -1) {
                iterator.remove();
            }
        }
        return incrementList;
    }

    /**
     * 处理产品加幅
     */
    private List<String> handlerProductIncrease(List<ProductDayIncreasePO> productDayIncreasePOList) {
        if (CollUtilX.isEmpty(productDayIncreasePOList)) {
            return null;
        }

        Map<String, IncrementDTO> incrementMap = new HashMap<>();
        Iterator<ProductDayIncreasePO> iterator = productDayIncreasePOList.iterator();
        while (iterator.hasNext()) {
            ProductDayIncreasePO productDayIncreasePO = iterator.next();
            String redisKey = StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_", String.valueOf(productDayIncreasePO.getProductId()), "_", productDayIncreasePO.getSaleDate());

            ProductSaleIncreaseDTO productSaleIncreaseDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSalePriceKey, redisKey), ProductSaleIncreaseDTO.class);

            int type = -1;
            String channelCode;

            if ((productDayIncreasePO.getB2bAdjustmentType() != null && productDayIncreasePO.getB2bModifiedAmt() != null) &&
                    (productSaleIncreaseDTO == null || productSaleIncreaseDTO.getB2bAdjustmentType() == null || productSaleIncreaseDTO.getB2bModifiedAmt() == null
                            || productDayIncreasePO.getB2bModifiedAmt().compareTo(productSaleIncreaseDTO.getB2bModifiedAmt()) != 0 || !productDayIncreasePO.getB2bAdjustmentType().equals(productSaleIncreaseDTO.getB2bAdjustmentType()))) {
                type = IncrementTypeEnum.SALE_PRICE.no;
                channelCode = ChannelEnum.B2B.key;
                assemblySalePriceIncrementMap(incrementMap, productDayIncreasePO, channelCode, type);
            }

            if (type == -1) {
                iterator.remove();
            }
        }

        if (!incrementMap.isEmpty()) {
            return incrementMap.values().stream().map(JSON::toJSONString).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 组合加幅增量map
     */
    private void assemblySalePriceIncrementMap(Map<String, IncrementDTO> incrementMap, ProductDayIncreasePO productDayIncreasePO, String channelCode, Integer type) {
        if (!incrementMap.isEmpty() && incrementMap.get(StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_", String.valueOf(productDayIncreasePO.getProductId()))) != null) {
            incrementMap.get(StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_",
                    String.valueOf(productDayIncreasePO.getProductId()), "_", channelCode)).getSaleDate().add(productDayIncreasePO.getSaleDate());
        } else {
            IncrementDTO incrementDTO = new IncrementDTO();
            incrementDTO.setProductId(productDayIncreasePO.getProductId());
            incrementDTO.setCompanyCode(productDayIncreasePO.getCompanyCode());
            incrementDTO.setChannelCode(channelCode);
            incrementDTO.setType(type);
            incrementDTO.setSaleDate(new HashSet<>(Collections.singletonList(productDayIncreasePO.getSaleDate())));
            incrementMap.put(StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_",
                    String.valueOf(productDayIncreasePO.getProductId()), "_", incrementDTO.getChannelCode()), incrementDTO);
        }
    }

    /**
     * 组装销售状态增量
     */
    private void assemblySaleStatusIncrementMap(List<String> incrementList, ProductSaleStatusPO productSaleStatusDTO, String channelCode, Integer type) {
        IncrementDTO incrementDTO = new IncrementDTO();
        incrementDTO.setProductId(productSaleStatusDTO.getProductId());
        incrementDTO.setType(type);
        incrementDTO.setChannelCode(channelCode);
        incrementDTO.setCompanyCode(productSaleStatusDTO.getCompanyCode());
        incrementList.add(JSON.toJSONString(incrementDTO));
    }

    /**
     * 设置默认加幅+0
     * <p>
     * 每个产品对应的每一天没有加幅过话，则设置加幅+0，如果加幅过，则不设置
     *
     * @param batchQuotationDTOList
     */
    private void setDefaultZeroAdjustmentSalePrice(List<BatchQuotationDTO> batchQuotationDTOList) {
        LoginUser loginUser = TokenManager.getUser(request);
        //循环遍历每个产品，依次处理
        for (BatchQuotationDTO batchQuotationDTO : batchQuotationDTOList) {
            // 1. 查询加幅信息
            Example salePriceExample = new Example(ProductDayIncreasePO.class);
            Example.Criteria salePriceCriteria = salePriceExample.createCriteria();
            salePriceCriteria.andEqualTo("productId", batchQuotationDTO.getProductId());
            salePriceCriteria.andEqualTo("companyCode", loginUser.getCompanyCode());
            salePriceCriteria.andBetween("saleDate", batchQuotationDTO.getStartDate(), batchQuotationDTO.getEndDate());
            List<ProductDayIncreasePO> productDayIncreasePOList = productDayIncreaseMapper.selectByExample(salePriceExample);
            // 2. 如果从未加幅过，则直接批量设置加幅+0即可
            BatchSaleDTO batchSaleDTO = new BatchSaleDTO();
            batchSaleDTO.setChannelCode(ChannelEnum.B2B.getValue());
            batchSaleDTO.setUpdatedBy(loginUser.getUserName());
            batchSaleDTO.setUpdatedDt(DateUtilX.getCurrentDateStr(1));
            batchSaleDTO.setCompanyCode(loginUser.getCompanyCode());
            batchSaleDTO.setLanguage(request.getHeader(HttpConstant.Language));
            if (CollUtilX.isEmpty(productDayIncreasePOList)) {
                BatchSaleItemDTO item = CommonConvert.INSTANCE.batchSaleItemDTOConvert(batchQuotationDTO);
                item.setSalePriceAdjustmentType(SaleAdjustmentTypeEnum.PLUS_NUMBER.no);
                item.setModifiedSalePrice(BigDecimal.ZERO);
                batchSaleDTO.setItemList(new ArrayList<>(Collections.singletonList(item)));
                productSaleService.batchModifySalePrice(batchSaleDTO);
                continue;
            }

            // 3. 将saleDate转换格式、去重、排序
            List<String> sortedUniqueDates = productDayIncreasePOList.stream()
                    .map(productDayIncreasePO -> DateUtilX.dateToString(DateUtilX.stringToDate(productDayIncreasePO.getSaleDate(), DateUtilX.hour_format)))
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());

            String startDate = batchQuotationDTO.getStartDate();
            String endDate = batchQuotationDTO.getEndDate();

            //遍历已经加幅过的日期，对应跳过，如batchQuotationDTO的startDate=2025-06-01和endDate=2025-06-30，sortedUniqueDates包含了，2025-06-01,2025-06-10,2025-06-25,则应该去批量设置2025-06-02到2025-06-09，2025-06-11到2025-06-24，2025-06-26到2025-06-30这些时间段的加幅+0
            // 4. 生成完整的日期范围列表
            List<String> allDateList = new ArrayList<>();
            Date start = DateUtilX.stringToDate(startDate);
            Date end = DateUtilX.stringToDate(endDate);
            List<Date> dateRange = DateUtilX.getDateList(start, end);
            for (Date date : dateRange) {
                allDateList.add(DateUtilX.dateToString(date));
            }

            // 5. 找出没有加幅过的日期段
            List<String> unprocessedDates = new ArrayList<>();
            for (String date : allDateList) {
                if (!sortedUniqueDates.contains(date)) {
                    unprocessedDates.add(date);
                }
            }

            // 6. 将连续的日期合并成日期段
            if (CollUtilX.isNotEmpty(unprocessedDates)) {
                List<DateRangeDTO> dateRanges = mergeContinuousDates(unprocessedDates);

                // 7. 为每个日期段批量设置加幅+0
                for (DateRangeDTO dateRangeDTO : dateRanges) {
                    BatchSaleItemDTO item = CommonConvert.INSTANCE.batchSaleItemDTOConvert(batchQuotationDTO);
                    item.setStartDate(dateRangeDTO.getStartDate());
                    item.setEndDate(dateRangeDTO.getEndDate());
                    item.setSalePriceAdjustmentType(SaleAdjustmentTypeEnum.PLUS_NUMBER.no);
                    item.setModifiedSalePrice(BigDecimal.ZERO);
                    batchSaleDTO.setItemList(new ArrayList<>(Collections.singletonList(item)));
                    productSaleService.batchModifySalePrice(batchSaleDTO);
                }
            }
        }
    }

    /**
     * 将连续的日期合并成日期段
     *
     * @param dates 排序后的日期列表
     * @return 日期段列表
     */
    private List<DateRangeDTO> mergeContinuousDates(List<String> dates) {
        if (CollUtilX.isEmpty(dates)) {
            return new ArrayList<>();
        }

        // 先排序
        dates.sort(String::compareTo);

        List<DateRangeDTO> ranges = new ArrayList<>();
        String rangeStart = dates.get(0);
        String rangeEnd = dates.get(0);

        for (int i = 1; i < dates.size(); i++) {
            String currentDate = dates.get(i);
            String previousDate = dates.get(i - 1);

            // 检查是否连续（相差1天）
            Date current = DateUtilX.stringToDate(currentDate);
            Date previous = DateUtilX.stringToDate(previousDate);

            if (DateUtilX.getDay(previous, current) == 1) {
                // 连续日期，扩展当前范围
                rangeEnd = currentDate;
            } else {
                // 不连续，保存当前范围并开始新范围
                ranges.add(new DateRangeDTO(rangeStart, rangeEnd));
                rangeStart = currentDate;
                rangeEnd = currentDate;
            }
        }

        // 添加最后一个范围
        ranges.add(new DateRangeDTO(rangeStart, rangeEnd));

        return ranges;
    }

    /**
     * 天级别设置0加幅
     *
     * @param productDayQuotationDTO
     */
    private void dailySetDefaultZeroAdjustmentSalePrice(ProductDayQuotationDTO productDayQuotationDTO) {
        LoginUser loginUser = TokenManager.getUser(request);
        // 1. 查询加幅信息
        Example salePriceExample = new Example(ProductDayIncreasePO.class);
        Example.Criteria salePriceCriteria = salePriceExample.createCriteria();
        salePriceCriteria.andEqualTo("productId", productDayQuotationDTO.getProductId());
        salePriceCriteria.andEqualTo("companyCode", loginUser.getCompanyCode());
        //salePriceCriteria.andBetween("saleDate", productDayQuotationDTO.getSaleDate(), productDayQuotationDTO.getSaleDate());
        salePriceCriteria.andEqualTo("saleDate", productDayQuotationDTO.getSaleDate());
        List<ProductDayIncreasePO> productDayIncreasePOList = productDayIncreaseMapper.selectByExample(salePriceExample);
        // 2. 如果存在加幅信息，则不进行默认设置
        if (CollUtilX.isNotEmpty(productDayIncreasePOList)) {
            return;
        }
        // 3. 如果不存在加幅信息，则设置加幅为0
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("companyCode", loginUser.getCompanyCode());
        requestMap.put("updatedBy", loginUser.getUserName());
        requestMap.put("channelCode", ChannelEnum.B2B.getValue());
        requestMap.put("productId", String.valueOf(productDayQuotationDTO.getProductId()));
        requestMap.put("saleDate", productDayQuotationDTO.getSaleDate());
        requestMap.put("salePriceAdjustmentType", String.valueOf(SaleAdjustmentTypeEnum.PLUS_NUMBER.no));
        requestMap.put("modifiedSalePrice", String.valueOf(BigDecimal.ZERO));
        productSaleService.dailyModifySalePrice(requestMap);
    }
}
