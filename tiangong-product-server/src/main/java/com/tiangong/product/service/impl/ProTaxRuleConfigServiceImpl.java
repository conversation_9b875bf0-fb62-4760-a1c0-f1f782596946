package com.tiangong.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.convert.CommonConvert;
import com.tiangong.product.domain.entity.ProTaxDetailEntity;
import com.tiangong.product.domain.entity.ProTaxRuleConfigEntity;
import com.tiangong.product.domain.req.*;
import com.tiangong.product.dto.CalculateSelfHotelLowestDTO;
import com.tiangong.product.mapper.ProTaxDetailMapper;
import com.tiangong.product.mapper.ProTaxRuleConfigMapper;
import com.tiangong.product.service.ProTaxRuleConfigService;
import com.tiangong.product.service.ProductSaleService;
import com.tiangong.product.service.ProductService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.ServletUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: tiangong
 * @ClassName ProTaxRuleConfigServiceImpl
 * @description:
 * @author: 湫
 * @create: 2024/09/02/ 17:04
 * @Version 1.0
 **/

@Service
@Slf4j
public class ProTaxRuleConfigServiceImpl extends ServiceImpl<ProTaxRuleConfigMapper,ProTaxRuleConfigEntity> implements ProTaxRuleConfigService {

    @Autowired
    private ProTaxRuleConfigMapper proTaxRuleConfigMapper;

    @Autowired
    private ProTaxDetailMapper proTaxDetailMapper;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductSaleService productSaleService;

    @Resource(name = "processingProductInfoExecutor")
    private ThreadPoolTaskExecutor processingProductInfoExecutor;

    @Override
    @Transactional
    public Integer proTaxRuleConfigAdd(ProTaxRuleConfigAditReq req) {
        HttpServletRequest request = ServletUtilX.getRequest();
        LoginUser user = TokenManager.getUser(request);
        if (user == null) {
            return null;
        }

        if (TaxScopeOfApplicationEnum.taxScopeOfApplication(req.getTaxScopeOfApplicationType()) == null) {
            //税费适用范围 不在范围内
            throw new SysException(ErrorCodeEnum.TAX_SCOPE_OF_APPLICATION_TYPE_ERROR);
        }
        QueryWrapper<ProTaxRuleConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tax_rule_name", req.getTaxRuleName());
        List<ProTaxRuleConfigEntity> proTaxRuleConfigEntities = proTaxRuleConfigMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(proTaxRuleConfigEntities)){
            throw new SysException(ErrorCodeEnum.TAX_RULE_NAME_EXISTS);
        }

        ProTaxRuleConfigEntity proTaxRuleConfigEntity = CommonConvert.INSTANCE.proTaxRuleConfigConvert(req);
        proTaxRuleConfigEntity.setCreatedDt(new Date());
        proTaxRuleConfigEntity.setCreatedBy(user.getUserName());
        proTaxRuleConfigEntity.setUpdatedDt(new Date());
        proTaxRuleConfigEntity.setUpdatedBy(user.getUserName());
        int insert = proTaxRuleConfigMapper.insert(proTaxRuleConfigEntity);
        req.setId(proTaxRuleConfigEntity.getId());

        if (CollectionUtil.isNotEmpty(req.getProTaxDetails())) {
            List<ProTaxDetailBase> proTaxDetails = new ArrayList<>();
            List<ProTaxDetailEntity> reqProTaxDetails = CommonConvert.INSTANCE.proTaxDetailList(req.getProTaxDetails());
            for (ProTaxDetailEntity reqProTaxDetail : reqProTaxDetails) {
                if (TaxStandardsEnum.taxStandards(reqProTaxDetail.getTaxStandard()) == null) {
                    //税费标准 不在范围内
                    throw new SysException(ErrorCodeEnum.TAX_STANDARD_ERROR);
                } else if (TaxTypeEnum.taxType(reqProTaxDetail.getTaxType()) == null) {
                    // 税费类型不在范围内
                    throw new SysException(ErrorCodeEnum.TAX_TYPE_ERROR);
                } else if (TaxIncreaseEnum.taxIncrease(reqProTaxDetail.getTaxIncreaseType()) == null) {
                    // 税费/税率 不在范围内
                    throw new SysException(ErrorCodeEnum.TAX_INCREASE_TYPE_ERROR);
                }
                reqProTaxDetail.setTaxRuleConfigId(proTaxRuleConfigEntity.getId());
                reqProTaxDetail.setCreatedDt(new Date());
                reqProTaxDetail.setCreatedBy(user.getUserName());
                reqProTaxDetail.setUpdatedDt(new Date());
                reqProTaxDetail.setUpdatedBy(user.getUserName());
                proTaxDetailMapper.insert(reqProTaxDetail);
                ProTaxDetailBase proTaxDetailBase = CommonConvert.INSTANCE.reqProTaxDetail(reqProTaxDetail);
                proTaxDetails.add(proTaxDetailBase);
            }
            req.setProTaxDetails(proTaxDetails);
        }
        RedisTemplateX.hPut(RedisKey.proTaxRuleConfigKey, String.valueOf(proTaxRuleConfigEntity.getId()), JSON.toJSONString(req));
        return insert;
    }

    @Override
    @Transactional
    public Integer proTaxRuleConfigEdit(ProTaxRuleConfigAditReq req) {
        if (TaxScopeOfApplicationEnum.taxScopeOfApplication(req.getTaxScopeOfApplicationType()) == null) {
            //税费适用范围 不在范围内
            throw new SysException(ErrorCodeEnum.TAX_SCOPE_OF_APPLICATION_TYPE_ERROR);
        }

        QueryWrapper<ProTaxRuleConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tax_rule_name", req.getTaxRuleName());
        List<ProTaxRuleConfigEntity> proTaxRuleConfigEntities = proTaxRuleConfigMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(proTaxRuleConfigEntities)) {
            if(proTaxRuleConfigEntities.size()>1){
                throw new SysException(ErrorCodeEnum.TAX_RULE_NAME_EXISTS);
            }else {
                ProTaxRuleConfigEntity proTaxRuleConfigEntity = proTaxRuleConfigEntities.get(0);
                if (!proTaxRuleConfigEntity.getId().equals(req.getId())) {
                    //taxRuleName已存在，且不为当前编辑的记录，则抛异常
                    throw new SysException(ErrorCodeEnum.TAX_RULE_NAME_EXISTS);
                }
            }
        }

        ProTaxRuleConfigEntity proTaxRuleConfigEntity = CommonConvert.INSTANCE.proTaxRuleConfigAditReqConvert(req);
        proTaxRuleConfigEntity.setUpdatedBy(req.getUpdatedBy());
        proTaxRuleConfigEntity.setUpdatedDt(new Date());
        Integer result = null;
        if (proTaxRuleConfigEntity.getId() != null) {
            result = proTaxRuleConfigMapper.updateById(proTaxRuleConfigEntity);
        }

        //先删除 在新增 税费明细,避免在页面时,编辑税费明细 （新增、修改、删除） 一个一个判断很麻烦~
        Map<String, Object> detailCriteria = new HashMap<>();
        detailCriteria.put("tax_rule_config_Id", req.getId());
        proTaxDetailMapper.deleteByMap(detailCriteria);
        if (CollectionUtil.isNotEmpty(req.getProTaxDetails())) {
            List<ProTaxDetailEntity> reqProTaxDetails = CommonConvert.INSTANCE.proTaxDetailList(req.getProTaxDetails());
            for (ProTaxDetailEntity reqProTaxDetail : reqProTaxDetails) {
                if (TaxStandardsEnum.taxStandards(reqProTaxDetail.getTaxStandard()) == null) {
                    //税费标准 不在范围内
                    throw new SysException(ErrorCodeEnum.TAX_STANDARD_ERROR);
                } else if (TaxTypeEnum.taxType(reqProTaxDetail.getTaxType()) == null) {
                    // 税费类型不在范围内
                    throw new SysException(ErrorCodeEnum.TAX_TYPE_ERROR);
                } else if (TaxIncreaseEnum.taxIncrease(reqProTaxDetail.getTaxIncreaseType()) == null) {
                    // 税费/税率 不在范围内
                    throw new SysException(ErrorCodeEnum.TAX_INCREASE_TYPE_ERROR);
                }
                if (reqProTaxDetail.getId() == null) {
                    reqProTaxDetail.setTaxRuleConfigId(req.getId());
                }
                reqProTaxDetail.setCreatedDt(new Date());
                reqProTaxDetail.setCreatedBy(req.getCreatedBy());
                proTaxDetailMapper.insert(reqProTaxDetail);
            }
        }
        RedisTemplateX.hPut(RedisKey.proTaxRuleConfigKey, String.valueOf(proTaxRuleConfigEntity.getId()), JSON.toJSONString(req));

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(() -> {
                    // 处理需要计算自签酒店起价
                    CalculateSelfHotelLowestDTO dto = new CalculateSelfHotelLowestDTO();
                    dto.setTaxRuleConfigId(req.getId());
                    productSaleService.calculateNeedSelfHotelLowestPrice(dto);
                }, processingProductInfoExecutor);
            }
        });
        return result;
    }

    @Override
    @Transactional
    public Integer proTaxRuleConfigDelete(ProTaxRuleConfigQueryReq req) {
        Integer result = null;
        if (req.getId() != null) {
            Boolean exists = productService.checkTaxRuleConfigExists(req.getId());
            if(exists){
                throw new SysException(ErrorCodeEnum.TAX_RULE_IN_USE);
            }else {
                result = proTaxRuleConfigMapper.deleteById(req.getId());
                Map<String, Object> detailCriteria = new HashMap<>();
                detailCriteria.put("tax_rule_config_Id", req.getId());
                proTaxDetailMapper.deleteByMap(detailCriteria);
            }
        }
        RedisTemplateX.hDelete(RedisKey.proTaxRuleConfigKey, String.valueOf(req.getId()));
        return result;
    }


    @Override
    public PaginationSupportDTO<ProTaxRuleConfigResp> proTaxRuleConfigPage(ProTaxRuleConfigPageReq req) {

        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        List<ProTaxRuleConfigResp> proTaxRuleConfigPage = proTaxRuleConfigMapper.queryTaxRuleConfigByPage(req);

        PageInfo<ProTaxRuleConfigResp> pageInfo = new PageInfo<>(proTaxRuleConfigPage);
        PaginationSupportDTO<ProTaxRuleConfigResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.copyProperties(pageInfo, ProTaxRuleConfigResp.class);
        return paginationSupportDTO;
    }

    @Override
    public ProTaxRuleConfigResp proTaxRuleConfigDetail(ProTaxRuleConfigQueryReq req) {
        return proTaxRuleConfigMapper.queryTaxRuleConfigDetail(req);
    }

    @Override
    public Map<Long, ProTaxRuleConfigResp> proTaxRuleConfigDetailList(Set<Long> taxRuleConfigIds) {
        if (CollectionUtil.isNotEmpty(taxRuleConfigIds)) {
            // 查库
            List<ProTaxRuleConfigResp> proTaxRuleConfigResps = proTaxRuleConfigMapper.queryTaxRuleConfigDetailList(taxRuleConfigIds);
            if (CollectionUtil.isNotEmpty(proTaxRuleConfigResps)) {
                return proTaxRuleConfigResps.stream().collect(Collectors.toMap(ProTaxRuleConfigResp::getId, Function.identity()));
            } else {
                return new HashMap<>();
            }
        } else {
            return new HashMap<>();
        }
    }

    @Override
    public void proTaxRuleConfigCacheInit() {
        // 所有旧的缓存key
        Set<Object> keys = RedisTemplateX.hKeys(RedisKey.proTaxRuleConfigKey);


        // 查询所有 税费规则
        List<ProTaxRuleConfigResp> proTaxRuleConfigResps = proTaxRuleConfigMapper.queryTaxRuleConfigDetailList(null);

        //循环加载至缓存
        if (CollectionUtil.isNotEmpty(proTaxRuleConfigResps)) {
            for (ProTaxRuleConfigResp proTaxRuleConfigResp : proTaxRuleConfigResps) {
                // 删除
                keys.remove(proTaxRuleConfigResp.getId());
                //加入缓存
                RedisTemplateX.hPut(RedisKey.proTaxRuleConfigKey, String.valueOf(proTaxRuleConfigResp.getId()), JSON.toJSONString(proTaxRuleConfigResp));
            }
        }

        // 删除
        if (CollectionUtil.isNotEmpty(keys)) {
            RedisTemplateX.hDelete(RedisKey.proTaxRuleConfigKey, keys.toArray(new String[keys.size()]));
        }
    }



}
