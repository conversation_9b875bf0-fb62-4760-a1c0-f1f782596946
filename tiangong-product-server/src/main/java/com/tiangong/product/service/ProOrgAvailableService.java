package com.tiangong.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.common.Response;
import com.tiangong.product.domain.entity.ProOrgAvailableEntity;
import com.tiangong.product.domain.req.ProOrgAvailableReq;
import com.tiangong.product.domain.resp.ProOrgAvailableResp;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 可见性表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-14 14:24:58
 */
public interface ProOrgAvailableService extends IService<ProOrgAvailableEntity> {

    /**
     * 可见性表新增
     */
    void proOrgAvailableAdd(ProOrgAvailableReq req);

    /**
     * 可见性表删除
     */
    void proOrgAvailableDel(ProOrgAvailableReq req);

    /**
     * 可见性表编辑
     */
    void proOrgAvailableEdit(ProOrgAvailableReq req);

    /**
     * 可见性表详情
     */
    ProOrgAvailableResp proOrgAvailableDetail(ProOrgAvailableReq req);

    /**
     * 可见性表列表（全部）
     */
    List<ProOrgAvailableResp> proOrgAvailableList(ProOrgAvailableReq req);

    /**
     * 可见性表列表（分页）
     */
    PaginationSupportDTO<ProOrgAvailableResp> proOrgAvailablePage(ProOrgAvailableReq req);

    /**
     * 初始化缓存
     */
    void cacheAvailable(String companyCode, boolean deleteRedisInfo);

    /**
     * 通用使用 根据客户编码获取所有白名单的供应商code列表
     */
    List<String> getAvailableSupplierByAgentCode(String agentCode, String supplierCode);

    /**
     * 导入客户白名单配置
     */
    Response<Object> importProAgentAvailable(MultipartFile file, String operator);
}

