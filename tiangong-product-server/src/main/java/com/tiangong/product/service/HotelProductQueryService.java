package com.tiangong.product.service;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.product.request.AvailableHotelRequest;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.DhubProductDetailInfoResponse;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;

import java.util.List;


public interface HotelProductQueryService {

    /**
     * 可售酒店列表查询接口
     */
    List<Long> queryAvailableHotelList(AvailableHotelRequest request);

    /**
     * 酒店每日起价查询-Dhub
     */
    ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店Id列表查询接口-Dhub
     */
    ResponseResult<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request);

    /**
     * 酒店每日起价查询-B2B
     */
    ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店实时产品查询
     */
    ResponseResult<ProductDetailResponse> queryProductDetail(ProductDetailRequest request);

    /**
     * 添加供货单查询产品
     */
    Response<ProductDetailResponse> queryHotelProductList(ProductDetailRequest request);

    /**
     * 查询客户可见性供应商
     */
    Response<List<FuzzySupplierDTO>> getAgentSupplyAvailable(FuzzyQueryDTO request);

    /**
     * 主动更新客户起价任务
     */
    void autoUpdateAgentLowestPriceTask(String param);
}
