package com.tiangong.product.server;


import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.product.dto.QuotaDTO;
import com.tiangong.product.service.QuotaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/product/quota")
public class QuotaServer extends BaseController {

    @Autowired
    private QuotaService quotaService;

    /**
     * 修改配额
     */
    @PostMapping(value = "/modifyQuota")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "modifyQuota", topic = "/product/quota/modifyQuota", source = "tiangong-product-server")
    public Response<Object> modifyQuota(@RequestBody QuotaDTO quotaDTO) {
        quotaService.modifyQuota(quotaDTO);
        return Response.success();
    }


}
