package com.tiangong.product.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.product.convert.CommonConvert;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableAddDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailablePageDTO;
import com.tiangong.product.domain.dto.ProOrgHotelAvailableRespDTO;
import com.tiangong.product.domain.vo.ProOrgHotelAvailableAddVO;
import com.tiangong.product.domain.vo.ProOrgHotelAvailableDeleteVO;
import com.tiangong.product.domain.vo.ProOrgHotelAvailablePageVO;
import com.tiangong.product.domain.vo.ProOrgHotelAvailableRespVO;
import com.tiangong.product.dto.UploadRespVO;
import com.tiangong.product.service.ProOrgHotelAvailableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.util.List;

/**
 * 供应商酒店黑白名单表
 * <AUTHOR>
 * @date 2025/6/9
 * @Description 供应商酒店黑白名单控制器，处理黑白名单相关的HTTP请求
 */
@Slf4j
@RestController
@RequestMapping("/product/proOrgHotelAvailable")
public class ProOrgHotelAvailableController extends BaseController {

    @Autowired
    private ProOrgHotelAvailableService proOrgHotelAvailableService;

    /**
     * 供应商酒店黑白名单列表（分页）
     */
    @PostMapping("/page")
    public Response<PaginationSupportDTO<ProOrgHotelAvailableRespVO>> page(@RequestBody ProOrgHotelAvailablePageVO vo) {
        // 1.VO转DTO
        ProOrgHotelAvailablePageDTO dto = CommonConvert.INSTANCE.convertVoToDto(vo);
        // 2.分页查询
        PaginationSupportDTO<ProOrgHotelAvailableRespDTO> result = proOrgHotelAvailableService.queryProOrgHotelAvailablePage(dto);
        // 3.DTO转VO
        PaginationSupportDTO<ProOrgHotelAvailableRespVO> response = convertDtoToVo(result);
        return Response.success(response);
    }

    /**
     * 新增供应商酒店黑白名单
     */
    @PostMapping("/add")
    public Response<Boolean> add(@RequestBody ProOrgHotelAvailableAddVO vo) {
        // VO转DTO
        ProOrgHotelAvailableAddDTO dto = CommonConvert.INSTANCE.convertAddVoToDto(vo);
        dto.setCreatedBy(getUserName());
        // 调用服务层
        proOrgHotelAvailableService.addProOrgHotelAvailable(dto);

        return Response.success(true);
    }

    /**
     * 删除供应商酒店黑白名单
     */
    @PostMapping("/delete")
    public Response<Boolean> delete(@Valid @RequestBody ProOrgHotelAvailableDeleteVO vo) {
        // 调用服务层
        proOrgHotelAvailableService.deleteProOrgHotelAvailable(vo.getId());
        return Response.success(true);
    }

    /**
     * 文件模板下载
     *
     */
    @PostMapping("/downloadFileTemplate")
    public void downloadFileTemplate(HttpServletResponse resp) {
        InputStream inputStream = null;
        try {
            inputStream = this.getClass().getClassLoader().getResourceAsStream("template/orgHotelAvailableTemplate.xlsx");
            if (inputStream == null) {
                log.error("资源文件未找到");
                resp.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            String fileName = java.net.URLEncoder.encode("供应商酒店黑白名单配置导入模板.xlsx", "UTF-8");
            resp.setContentType("application/octet-stream");
            resp.setHeader("Content-Disposition", "attachment; filename=" + fileName); // 指定下载文件的名称
            int bytesRead;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                resp.getOutputStream().write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            log.error("下载供应商酒店黑白名单配置导入模板文件模板失败", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error("关闭流失败", e);
                }
            }
        }
    }

    /**
     * 文件导入
     *
     * @param file 文件
     * @return 导入结果
     */
    @PostMapping("/uploadFileImport")
    public Response<UploadRespVO> uploadFile(@RequestParam("file") MultipartFile file) {
        return Response.success(proOrgHotelAvailableService.uploadFile(file, getUserName()));
    }

    /**
     * 重置酒店黑白名单缓存
     * 清空所有酒店黑白名单相关缓存并重新加载
     */
    @PostMapping("/resetCache")
    public Response<Boolean> resetHotelAvailableCache() {
        proOrgHotelAvailableService.resetHotelAvailableCache();
        return Response.success(true);
    }

    /**
     * DTO分页结果转VO分页结果
     * @param dtoPage DTO分页结果对象
     * @return VO分页结果对象
     */
    private PaginationSupportDTO<ProOrgHotelAvailableRespVO> convertDtoToVo(PaginationSupportDTO<ProOrgHotelAvailableRespDTO> dtoPage) {
        PaginationSupportDTO<ProOrgHotelAvailableRespVO> voPage = new PaginationSupportDTO<>();
        voPage.setCurrentPage(dtoPage.getCurrentPage());
        voPage.setPageSize(dtoPage.getPageSize());
        voPage.setTotalCount(dtoPage.getTotalCount());
        voPage.setTotalPage(dtoPage.getTotalPage());
        // 使用CommonConvert转换列表
        List<ProOrgHotelAvailableRespVO> voList = CommonConvert.INSTANCE.convertDtoListToVoList(dtoPage.getItemList());
        voPage.setItemList(voList);
        return voPage;
    }
}
