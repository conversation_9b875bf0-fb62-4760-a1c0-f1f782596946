package com.tiangong.product.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.product.domain.req.*;
import com.tiangong.product.domain.resp.ProHotelIncreasePageResp;
import com.tiangong.product.service.ProHotelIncreaseService;
import com.tiangong.product.task.InitAgentSupplierHotelIncreaseTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 产品酒店加幅
 * @ClassName: ProHotelIncreaseController
 * @Author: xuboh<PERSON>
 * @CreateDate: 2025/4/22 11:26
 * @Version: 1.0
 */

@RestController
@RequestMapping("/product/proHotelIncrease")
public class ProHotelIncreaseController extends BaseController {

    @Autowired
    private ProHotelIncreaseService proHotelIncreaseService;

    @Autowired
    private InitAgentSupplierHotelIncreaseTask initAgentSupplierHotelIncreaseTask;

    /**
     * 新增
     */
    @PostMapping("/proHotelIncreaseAdd")
    @PreAuthorize("@syyo.check('product:proHotelIncreaseAdd')")
    public Response<Object> add(@RequestBody ProHotelIncreaseAddReq req) {
        req.setUpdatedBy(super.getUserName());
        req.setCreatedBy(super.getUserName());
        return proHotelIncreaseService.proHotelIncreaseAdd(req);
    }

    /**
     * 删除
     */
    @PostMapping("/proHotelIncreaseDel")
    @PreAuthorize("@syyo.check('product:proHotelIncreaseDel')")
    public Response<Object> del(@RequestBody ProHotelIncreaseDelReq req) {
        proHotelIncreaseService.proHotelIncreaseDel(req);
        return Response.success();
    }

    /**
     * 编辑
     */
    @PostMapping("/proHotelIncreaseEdit")
    @PreAuthorize("@syyo.check('product:proHotelIncreaseEdit')")
    public Response<Object> edit(@RequestBody ProHotelIncreaseEditReq req) {
        req.setUpdatedBy(super.getUserName());
        proHotelIncreaseService.proHotelIncreaseEdit(req);
        return Response.success();
    }

    /**
     * 分页查询
     */
    @PostMapping("/proHotelIncreasePage")
    @PreAuthorize("@syyo.check('product:proHotelIncreasePage')")
    public Response<PaginationSupportDTO<ProHotelIncreasePageResp>> page(@RequestBody ProHotelIncreasePageReq req) {
        return Response.success(proHotelIncreaseService.proHotelIncreasePage(req));
    }

    /**
     * 批量修改
     */
    @PostMapping("/proHotelIncreaseBatchEdit")
    @PreAuthorize("@syyo.check('product:proHotelIncreaseBatchEdit')")
    public Response<Object> edit(@RequestBody ProHotelIncreaseBatchEditReq req) {
        proHotelIncreaseService.proHotelIncreaseBatchEdit(req);
        return Response.success();
    }

}
