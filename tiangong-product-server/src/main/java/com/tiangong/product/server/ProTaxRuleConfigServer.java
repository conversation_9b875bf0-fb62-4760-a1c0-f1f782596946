package com.tiangong.product.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.product.domain.req.ProTaxRuleConfigAditReq;
import com.tiangong.product.domain.req.ProTaxRuleConfigPageReq;
import com.tiangong.product.domain.req.ProTaxRuleConfigQueryReq;
import com.tiangong.product.domain.req.ProTaxRuleConfigResp;
import com.tiangong.product.service.ProTaxRuleConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 税费规则配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/product")
@Validated
public class ProTaxRuleConfigServer extends BaseController {

    @Resource
    private ProTaxRuleConfigService proTaxRuleConfigService;


    /**
     * 新增|修改
     */
    @PostMapping("/taxRuleConfigAdit")
    public Response<Integer> add(@RequestBody ProTaxRuleConfigAditReq req) {
        req.setCreatedBy(getUserName());
        req.setUpdatedBy(getUserName());
        if (req.getId() == null) {
            return Response.success(proTaxRuleConfigService.proTaxRuleConfigAdd(req));
        } else {
            return Response.success(proTaxRuleConfigService.proTaxRuleConfigEdit(req));
        }

    }

    /**
     * 删除
     */
    @PostMapping("/taxRuleConfigDel")
    public Response<Integer> delete(@RequestBody ProTaxRuleConfigQueryReq req) {
        return Response.success(proTaxRuleConfigService.proTaxRuleConfigDelete(req));
    }

    /**
     * 分页查询
     */
    @PostMapping("/taxRuleConfigPage")
    public Response<PaginationSupportDTO<ProTaxRuleConfigResp>> page(@RequestBody ProTaxRuleConfigPageReq req) {
        return Response.success(proTaxRuleConfigService.proTaxRuleConfigPage(req));
    }

    /**
     * 获取详情
     */
    @PostMapping("/taxRuleConfigDetail")
    public Response<ProTaxRuleConfigResp> proTaxRuleConfigDetail(@RequestBody ProTaxRuleConfigQueryReq req) {
        return Response.success(proTaxRuleConfigService.proTaxRuleConfigDetail(req));
    }
}
