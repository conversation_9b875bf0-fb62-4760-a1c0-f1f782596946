package com.tiangong.product.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.product.request.AvailableHotelRequest;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.DhubProductDetailInfoResponse;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.domain.req.HotelTimeZoneRequest;
import com.tiangong.product.domain.resp.HotelTimeZoneResp;
import com.tiangong.product.service.HotelProductQueryService;
import com.tiangong.product.service.HotelProductService;
import com.tiangong.redis.core.RedisTemplateX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 产品输出接口
 */
@RestController
@Slf4j
@RequestMapping("/product")
public class HotelProductServer extends BaseController {

    @Autowired
    private HotelProductQueryService hotelProductQueryService;

    @Autowired
    private HotelProductService hotelProductService;

    @PostMapping("/queryAvailableHotelList")
    @AnonymousAccess
    public List<Long> queryAvailableHotelList(@RequestBody AvailableHotelRequest request) {
        try {
            return hotelProductQueryService.queryAvailableHotelList(request);
        } catch (Exception e) {
            log.error("product-queryAvailableHotelList error!", e);
        }
        return null;
    }


    /**
     * 酒店每日起价查询-Dhub
     */
    @PostMapping("/queryHotelLowestPrice")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryHotelLowestPrice", topic = "/product/queryHotelLowestPrice", source = "tiangong-product-server")
    public ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(@RequestBody HotelLowestPriceRequest request) {
        try {
            return hotelProductQueryService.queryHotelLowestPrice(request);
        } catch (Exception e) {
            log.error("product-queryHotelLowestPrice error!", e);
        }
        return null;
    }

    /**
     * 酒店列表查询接口-Dhub
     */
    @PostMapping("/queryDisHotelList")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryDisHotelList", topic = "/product/queryDisHotelList", source = "tiangong-product-server")
    public ResponseResult<HotelIdListResponse> queryDisHotelList(@RequestBody HotelIdListRequest request) {
        try {
            return hotelProductQueryService.queryHotelIdList(request);
        } catch (Exception e) {
            log.error("product-queryHotelList error!", e);
        }
        return null;
    }

    /**
     * 酒店每日起价查询-B2B
     */
    @PostMapping("/hotelLowestPrice")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "hotelLowestPrice", topic = "/product/hotelLowestPrice", source = "tiangong-product-server")
    public ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(@RequestBody HotelLowestPriceRequest request) {
        try {
            return hotelProductQueryService.hotelLowestPrice(request);
        } catch (Exception e) {
            log.error("product-hotelLowestPrice error!", e);
        }
        return null;
    }

    /**
     * 酒店实时产品查询
     */
    @PostMapping("/queryHotelProductDetail")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryHotelProductDetail", topic = "/product/queryHotelProductDetail", source = "tiangong-product-server")
    public ResponseResult<ProductDetailResponse> queryHotelProductDetail(@RequestBody ProductDetailRequest request) {
        try {
            return hotelProductQueryService.queryProductDetail(request);
        } catch (Exception e) {
            log.error("product-queryProductDetail error!", e);
        }
        return null;
    }

    /**
     * 酒店实时产品查询(dhub)
     */
    @PostMapping("/queryDhubHotelProductDetail")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryDhubHotelProductDetail", topic = "/product/queryDhubHotelProductDetail", source = "tiangong-product-server")
    public ResponseResult<DhubProductDetailInfoResponse> queryDhubHotelProductDetail(@RequestBody ProductDetailRequest request) {
        try {
//            return hotelProductQueryService.queryDhubHotelProductDetail(request);
            return hotelProductService.queryHotelProductDetail(request);
        } catch (Exception e) {
            log.error("product-queryDhubHotelProductDetail error!", e);
        }
        return null;
    }

    /**
     * 添加供货单查询产品
     * 订单详细使用
     */
    @PostMapping("/queryHotelProductList")
    @SlsLog(level = "info", name = "查询", message = "queryHotelProductList", topic = "/product/queryHotelProductList", source = "tiangong-product-server")
    public Response<ProductDetailResponse> queryHotelProductList(@RequestBody ProductDetailRequest request) {
        request.setUserType(getUser().getUserType());
        request.setUserAccount(getUserAccount());
        request.setLanguage(getLanguage());
        String token = getToken();
        token = token.substring(token.length() - 32);
        RedisTemplateX.set(RedisKey.TOKEN_RANDOM_ID + getUserAccount(), token);
        RedisTemplateX.set(RedisKey.USER_IPADDRESS + getUserAccount(), getIpAddress());
        return hotelProductQueryService.queryHotelProductList(request);
    }

    /**
     * 查询客户可见性供应商
     * 订单详细使用
     */
    @PostMapping("/getAgentSupplyAvailable")
    @SlsLog(level = "info", name = "查询", message = "getAgentSupplyAvailable", topic = "/product/getAgentSupplyAvailable", source = "tiangong-product-server")
    public Response<List<FuzzySupplierDTO>> getAgentSupplyAvailable(@RequestBody FuzzyQueryDTO request) {
        return hotelProductQueryService.getAgentSupplyAvailable(request);
    }

    /**
     * 主动更新客户起价任务
     */
    @PostMapping("/autoUpdateAgentLowestPriceTask")
    @AnonymousAccess
    public Response<Object> autoUpdateAgentLowestPriceTask(@RequestBody Map<String, String> paramMap) {
        try {
            hotelProductQueryService.autoUpdateAgentLowestPriceTask(paramMap.get("hotelId"));
        } catch (Exception e) {
            log.error("主动更新客户起价任务异常", e);
        }
        return Response.success();
    }

    /**
     * 查询酒店时区
     */
    @PostMapping("/queryHotelTimeZone")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryHotelTimeZone", topic = "/product/queryHotelTimeZone", source = "tiangong-product-server")
    public Response<HotelTimeZoneResp> queryHotelTimeZone(@RequestBody HotelTimeZoneRequest request) {
        return hotelProductService.queryHotelTimeZone(request);
    }
}
