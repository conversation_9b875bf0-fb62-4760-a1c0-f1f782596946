package com.tiangong.product.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 系统配置类
 * <p>
 * 全局参数配置
 * @RefreshScope 实时更新nacos配置
 */
@Data
@Configuration
@RefreshScope// 实时更新nacos配置
public class SettingsConstant {

    /**
     * 每次消费计算热度可订分数酒店数量
     */
    @Value("${consumer.calculateBookableScore.count}")
    private Integer consumerCalculateBookableScoreCount;

    /**
     * 计算热度酒店可订分数酒店天数
     */
    @Value("${consumer.calculateBookableScore.day}")
    private Integer consumerCalculateBookableScoreDay;

    /**
     * 每次消费计算热度酒店平均房价分数酒店数量
     */
    @Value("${consumer.calculateHotelAvgPriceScore.count}")
    private Integer consumerCalculateHotelAvgPriceScoreCount;

    /**
     * 计算热度酒店平均房价分数酒店天数
     */
    @Value("${consumer.calculateHotelAvgPriceScore.day}")
    private Integer consumerCalculateHotelAvgPriceScoreDay;

    /**
     * 查询不落地产品线程等待总时长
     *  时间单位 毫秒
     */
    @Value("${querySupplyProductWaitTimeOut:10000}")
    private Integer querySupplyProductWaitTimeOut;

    /**
     * 开放平台地址
     */
    @Value("${openGlink.url}")
    private String openGlinkUrl;

    /**
     * *天内存在供应商起价,则认为是可售酒店,用于输出可售酒店id
     */
    @Value("${availableHotelDates:1}")
    private Integer availableHotelDates;

    /**
     * 计算自签酒店起价天数
     */
    @Value("${calculate.selfHotelLowestPrice.day:59}")
    private Integer calculateSelfHotelLowestPriceDay;
}
