package com.tiangong.product.config;

import com.tiangong.enums.LanguageTypeEnum;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@ConfigurationProperties(prefix = "bed.name")
public class BedNameConfig {
    /**
     * 中文床型
     */
    private List<Map<String, String>> zh_cn;
    /**
     * 英文床型
     */
    private List<Map<String, String>> en_us;

    // 新增缓存 Map
    private Map<String, String> cachedZhCnMap;
    private Map<String, String> cachedEnUsMap;

    public List<Map<String, String>> getZh_cn() {
        return zh_cn;
    }

    public void setZh_cn(List<Map<String, String>> zh_cn) {
        this.zh_cn = zh_cn;
    }

    public List<Map<String, String>> getEn_us() {
        return en_us;
    }

    public void setEn_us(List<Map<String, String>> en_us) {
        this.en_us = en_us;
    }

    @PostConstruct
    private void init() {
        cachedZhCnMap = Collections.unmodifiableMap(zh_cn.stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

        cachedEnUsMap = Collections.unmodifiableMap(en_us.stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }

    /**
     * 根据语言获取床型名称
     */
    public Map<String, String> getBedNames(String language) {
        if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
            return cachedZhCnMap;
        } else if (LanguageTypeEnum.en_US.getValue().equals(language)) {
            return cachedEnUsMap;
        }
        // 根据需求，可以抛出异常或返回默认语言
        return cachedZhCnMap; // 假设提供一个默认处理方式
    }

    /**
     * 获取床型名称
     */
    public String getBedTypeName(String bedTypeCode, String language) {
        if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
            return cachedZhCnMap.getOrDefault(bedTypeCode, "未知类型");
        } else if (LanguageTypeEnum.en_US.getValue().equals(language)) {
            return cachedEnUsMap.getOrDefault(bedTypeCode, "Unknown Type");
        }
        // 根据需求，可以抛出异常或提供默认处理
        return "未知类型";
    }
}
