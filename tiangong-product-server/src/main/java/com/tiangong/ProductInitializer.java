package com.tiangong;

import cn.hutool.json.JSONUtil;
import com.tiangong.dto.common.AgentSupplierLabelDTO;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 产品初始化
 */
@Slf4j
@Component
public class ProductInitializer implements ApplicationRunner {

    // 使用原子引用保证线程安全
    private static final AtomicReference<Map<Object, Object>> agentSupplierLabelHolder = new AtomicReference<>();

    private static final AtomicReference<Map<String, BigDecimal>> rateHolder = new AtomicReference<>();

    /**
     * 项目启动自动执行初始化
     */
    @Override
    public void run(ApplicationArguments args) {
        if (agentSupplierLabelHolder.get() == null) {
            initializeAgentSupplierLabelData(null);
        }
        if (rateHolder.get() == null) {
            initializeRateData(null, null);
        }
    }

    /**
     * 客户供应商标签信息初始化（对外暴露）
     * @return 是否初始化成功
     */
    public static synchronized boolean initializeAgentSupplierLabelData(AgentSupplierLabelDTO dto) {
        try {
            if (dto != null && StrUtilX.isNotEmpty(dto.getAgentCode()) && StrUtilX.isNotEmpty(dto.getSupplierCode())) {
                Map<Object, Object> map = agentSupplierLabelHolder.get();
                String key = dto.getAgentCode() + "_" + dto.getSupplierCode();
                String value = dto.getLabelType() + "";
                if (map != null && dto.getDeleted() != null && dto.getDeleted() == 1) {
                    map.remove(key);
                } else if (map != null && !key.equals("null_null") && !value.equals("null")) {
                    map.put(key, value);
                    agentSupplierLabelHolder.set(map);
                }
                log.info("加载客户供应商标签信息成功，dto={}", JSONUtil.toJsonStr(dto));
            } else {
                Map<Object, Object> agentSupplierLabelMap = RedisTemplateX.hGetAll(RedisKey.AGENT_SUPPLIER_LABEL);
                if (agentSupplierLabelMap.size() > 0) {
                    agentSupplierLabelHolder.set(agentSupplierLabelMap);
                }
                log.info("客户供应商标签信息初始化成功");
            }
            return true;
        } catch (Exception e) {
            log.error("客户供应商标签信息初始化失败", e);
            return false;
        }
    }

    /**
     * 获取客户供应商标签信息（空安全处理）
     */
    public static Map<Object, Object> getAgentSupplierLabelMap() {
        return agentSupplierLabelHolder.get();
    }

    /**
     * 汇率信息初始化方法（对外暴露）
     * @return 是否初始化成功
     */
    public static synchronized boolean initializeRateData(String key, BigDecimal value) {
        try {
            Map<String, BigDecimal> map;
            if (StrUtilX.isNotEmpty(key) && value != null) {
                map = rateHolder.get();
                if (map == null) {
                    map = new HashMap<>();
                }
                map.put(key, value);
                rateHolder.set(map);
                log.info("加载汇率信息成功，key={}，value={}", key, value);
            } else {
                map = RedisUtil.queryAllRate();
                rateHolder.set(map);
                log.info("汇率信息初始化成功");
            }
            return true;
        } catch (Exception e) {
            log.error("汇率信息初始化失败", e);
            return false;
        }
    }

    /**
     * 获取汇率信息（空安全处理）
     */
    public static Map<String, BigDecimal> getRateMap() {
        return rateHolder.get();
    }

}
