<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.product.mapper.ProHotelIncreaseMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.product.domain.entity.ProHotelIncreaseEntity">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="hotelId" column="hotel_id"/>
        <result property="adjustmentType" column="adjustment_type"/>
        <result property="modifiedAmt" column="modified_amt"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="deleted" column="deleted"/>
        <result property="lowestIncreaseAmt" column="lowest_increase_amt"/>
    </resultMap>

    <select id="proHotelIncreasePage" parameterType="com.tiangong.product.domain.req.ProHotelIncreasePageReq"
            resultType="com.tiangong.product.domain.resp.ProHotelIncreasePageResp">
        SELECT
        tphi.id id,
        tphi.supplier_code supplierCode,
        too.org_name supplierName,
        tphi.hotel_id hotelId,
        ifnull(tphi.adjustment_type, 2) adjustmentType,
        ifnull(tphi.modified_amt, 0) modifiedAmt,
        tphi.lowest_increase_amt lowestIncreaseAmt
        FROM
        t_pro_hotel_increase tphi
        LEFT JOIN t_org_organization too ON tphi.supplier_code = too.org_code
        <where>
            <if test="req.agentCode != null and req.agentCode != '' ">
                tphi.agent_code = #{req.agentCode}
            </if>
            <if test="req.supplierCode != null and req.supplierCode != '' ">
                and tphi.supplier_code = #{req.supplierCode}
            </if>
            <if test="req.hotelId != null">
                and tphi.hotel_id = #{req.hotelId}
            </if>
        </where>
        order by tphi.id desc
    </select>

    <select id="proHotelIncreaseCustomPage"
            resultType="com.tiangong.product.domain.entity.ProHotelIncreaseEntity">
        SELECT id                  id,
               agent_code          agentCode,
               supplier_code       supplierCode,
               hotel_id            hotelId,
               adjustment_type     adjustmentType,
               modified_amt        modifiedAmt,
               lowest_increase_amt lowestIncreaseAmt
        FROM t_pro_hotel_increase
        ORDER BY id ASC
            LIMIT #{batchSize}, #{pageSize};
    </select>

</mapper>