<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tiangong.product.mapper.ProductMapper" >

    <resultMap id="QueryHotelsMap" type="com.tiangong.product.resp.HotelPriceDTO">
        <result property="hotelName" column="hotel_name"/>
        <result property="hotelId" column="hotel_id"/>
        <result property="hotelAddress" column="hotel_address"/>
        <result property="mainUrl" column="mainUrl"/>
        <result property="cityName" column="city_name"/>
        <result property="districtName" column="district_name"/>
        <result property="businessName" column="businessName"/>
        <result property="hotelStar" column="hotelStar"/>
        <result property="hotelScore" column="hotel_score"/>
        <result property="cityCode" column="city_code"/>
        <result property="saleCurrency" column="sale_currency"/>
        <result property="payAtHotelFee" column="pay_at_hotel_fee"/>
        <result property="payAtHotelCurrency" column="pay_at_hotel_currency"/>
        <result property="lowestPrice" column="lowest_price"/>
        <result property="countryCode" column="country_code"/>
        <result property="groupName" column="group_name"/>
        <result property="brandName" column="brand_name"/>
        <result property="hotelRecommendId" column="hotelRecommendId"/>

        <collection property="hotelLabelNameList" select="com.tiangong.product.mapper.ProductMapper.selectHotelLabelNameList"
                    column="hotelRecommendId" ofType="java.lang.String">
            <result column="label_name"/>
        </collection>
    </resultMap>

    <resultMap id="HotelLabelMap" type="com.tiangong.product.resp.HotelLabelDTO">
        <result property="hotelId" column="hotel_id"/>

        <collection property="hotelLabelNameList" ofType="java.lang.String">
            <result column="label_name"/>
        </collection>
    </resultMap>

    <select id="queryProductTaxRuleConfig"  resultType="com.tiangong.product.dto.ProductTaxRuleDTO" parameterType="java.lang.Integer">
        select
            tax_rule_config_id as taxRuleConfigId,
            product_id as productId
        from t_pro_product
        where
           active = 1
          and price_type = 1
          and product_id in
        <foreach collection="productIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 保证快速查询 这里使用 limit 1 只要有一条有效 则为存在 -->
    <select id="checkTaxRuleConfigExists" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select
            count(*)
        from t_pro_product
        where tax_rule_config_id = #{taxRuleConfigId}
          and active = 1
            LIMIT 1
    </select>

    <!--查询产品列表 先统计后查询-->
    <select id="queryHotelListByStatistics" parameterType="com.tiangong.product.dto.QueryProductRequestDTO"
            resultType="com.tiangong.product.dto.ProductHotelDTO">
        SELECT
            distinct
            tphsi.hotel_id
        FROM t_pro_hotel_sale_info tphsi
        WHERE
        1=1
        <if test="null != hotelId and hotelId > 0">
            and tphsi.hotel_id = #{hotelId}
        </if>
        <if test='null != hasProducts and hasProducts == "1"'>
            and tphsi.product_count > 0
            and tphsi.purchase_type &lt; 2
        </if>
        <if test='null != hasProducts and hasProducts == "0"'>
            and tphsi.product_count is null
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            and tphsi.supplier_code = #{supplierCode}
        </if>
        <if test="null != purchaseType and purchaseType != ''">
            and tphsi.purchase_type = #{purchaseType}
        </if>
    </select>

    <!--查询酒店产品-->
    <select id="queryHotelProducts" parameterType="com.tiangong.product.dto.QueryProductRequestDTO" resultType="com.tiangong.product.dto.ProductTempDTO">
        select
            products.hotel_id                   hotelId,
            products.off_shelve_status          offShelveStatus,
            products.room_id                    roomId,
            products.product_id                 productId,
            products.product_name               productName,
            products.advance_payment_id         advancePaymentId,
            products.breakfast_qty              breakfastQty,
            products.supplier_code              supplierCode,
            products.org_name                   supplierName,
            products.price_type                 priceType,
            products.tax_rule_config_id         taxRuleConfigId,
            products.b2b_sale_status            b2bSaleStatus,
            products.bed_types                  bedTypes,
            products.purchase_type              purchaseType,
            products.currency                   currency,
            quotations.sale_date                saleDate,
            quotations.base_price               basePrice,
            quotations.over_draft_status        overDraftStatus,
            quotations.quota                    quota,
            quotations.remaining_quota          remainingQuota,
            quotations.room_status              roomStatus
            from
            (SELECT
                p.hotel_id,
                p.room_id,
                p.product_id,
                p.product_name,
                p.advance_payment_id,
                p.breakfast_qty,
                p.supplier_code,
                p.bed_types,
                p.purchase_type,
                p.currency,
                p.off_shelve_status,
                p.price_type,
                p.tax_rule_config_id,
                o.org_name,
                ps.b2b_sale_status
            FROM
                t_pro_product p,
                t_pro_sale_status ps,
                t_org_company_supplier cs,
                t_org_organization o
            where p.product_id = ps.product_id
            and o.org_code = p.supplier_code
            and cs.company_code = ps.company_code
            and cs.org_id = o.org_id
            and cs.available_status = 1
            <if test="companyCode != null and companyCode != ''">
                and ps.company_code = #{companyCode}
            </if>
            and p.hotel_id = #{hotelId}
            <if test="null != supplierCode and supplierCode != '' ">
                and p.supplier_code = #{supplierCode}
            </if>
            <if test="null != purchaseType and purchaseType != '' ">
            and p.purchase_type = #{purchaseType}
            </if>
            and p.purchase_type &lt; 2
            <if test="null != offShelveStatus and offShelveStatus != ''">
                and p.off_shelve_status = #{offShelveStatus}
            </if>
            and ps.active = 1) products
            left join
            (SELECT
                saledate.product_id,
                saledate.sale_date,
                price.base_price,
                quota.over_draft_status,
                quota.quota,
                quota.remaining_quota,
                quota.room_status
            FROM
            (
            SELECT
                sd.sale_date,
                p.product_id
            FROM
                t_pro_sale_date sd,
                t_pro_product p
            WHERE
              sd.sale_date &gt;= #{startDate}
            AND sd.sale_date &lt;= #{endDate}
            AND p.hotel_id = #{hotelId}
            ) saledate
            LEFT JOIN (
            SELECT
                p.product_id,
                pi.sale_date,
                pi.base_price
        FROM
                t_pro_product p,
                t_pro_price pi
            WHERE
              p.product_id = pi.product_id
            AND p.hotel_id = #{hotelId}
            AND pi.sale_date &gt;= #{startDate}
            AND pi.sale_date &lt;= #{endDate}
            ) price ON saledate.sale_date = price.sale_date
            AND saledate.product_id = price.product_id
            LEFT JOIN (
                SELECT
                q1.product_id,
                q1.over_draft_status,
                q1.quota AS quota,
                q1.remaining_quota,
                q1.room_status,
                q1.sale_date
                FROM
                (
                SELECT
                p.product_id,
                q.over_draft_status,
                q.quota_account_id,
                q.quota,
                q.remaining_quota,
                q.room_status,
                q.sale_date
                FROM
                t_pro_product p,
                t_pro_quota q
                WHERE
                p.quota_account_id = q.quota_account_id
                AND p.hotel_id = #{hotelId}
                AND q.sale_date &gt;= #{startDate}
                AND q.sale_date &lt;= #{endDate}) q1
                LEFT JOIN (
                SELECT
                tdq.product_id,
                tdq.quota_account_id,
                tdq.sale_date,
                sum(quota) AS usedQuota
                FROM
                t_pro_debited_quota tdq
                GROUP BY
                tdq.product_id,
                tdq.quota_account_id,
                tdq.sale_date
                ) q2 ON q1.product_id = q2.product_id
                AND q1.quota_account_id = q2.quota_account_id
                AND q1.sale_date = q2.sale_date
            ) quota ON saledate.sale_date = quota.sale_date
            AND saledate.product_id = quota.product_id
            ) quotations on products.product_id = quotations.product_id
    </select>

    <!--查询售卖酒店Id列表  先统计后查询-->
    <select id="querySaleHotelIdsByStatistics" parameterType="com.tiangong.product.dto.QueryProductRequestDTO" resultType="java.lang.Long">
        SELECT
        a.hotel_id                   hotelId,
        a.product_count              productCount,
        IFNULL(tbhs.sort_rank,1000)  sortRank
        FROM(SELECT
        h.hotel_id,sum(h.product_count) product_count
        FROM
        t_pro_hotel_sale_info h
        WHERE
        h.active = 1
        AND h.company_code = #{companyCode}
        <if test="null != cityCode and cityCode != '' ">
            AND h.city_code = #{cityCode}
        </if>
        <if test="null != id and id != '' ">
            AND h.hotel_id = #{id}
        </if>
        <if test="null != hotelId and hotelId != '' ">
            AND h.hotel_id = #{hotelId}
        </if>
        <if test="null == hotelId and null != hotelName and hotelName != ''">
            AND h.hotel_name LIKE "%"#{hotelName}"%"
        </if>
        <if test="null != supplierCode and supplierCode != '' ">
            AND h.supplier_code = #{supplierCode}
        </if>
        <if test="null != purchaseType and purchaseType != '' ">
            AND h.purchase_type = #{purchaseType}
        </if>
        <if test='null != saleStatus and saleStatus == "1" and channelCode == "B2B"'>
            AND h.b2b_sale_count > 0
        </if>
        <if test='null != saleStatus and saleStatus == "0" and channelCode == "B2B"'>
            AND h.product_count > ifnull(h.b2b_sale_count,0)
        </if>
        group by h.hotel_id
        )
        a
        LEFT JOIN  t_baseinfo_hotel_sort
        tbhs
        ON
        tbhs.hotel_id = a.hotel_id
        AND tbhs.org_code = #{companyCode}
        ORDER BY sortRank ASC
    </select>

    <insert id="addHotelSaleInfoGroupByHotelId">
        insert into t_pro_hotel_sale_info(company_code,supplier_code,city_code,hotel_id,hotel_name,active,purchase_type,
                                          product_count,b2b_sale_count,created_dt)
        select s.company_code,hh.supplier_code,#{cityCode},hh.hotel_id,#{hotelName},ifnull(cs.available_status,0),
               max(hh.purchase_type),count(hh.product_id),sum(s.b2b_sale_status),now()
        from t_pro_product hh,t_pro_sale_status s,
             t_org_company_supplier cs,t_org_organization o
        where hh.product_id = s.product_id
          and hh.supplier_code = o.org_code
          and o.org_id = cs.org_id
          and hh.active = 1
          and s.active = 1
          and hh.hotel_id = #{hotelId}
        group by s.company_code,hh.supplier_code,#{cityCode},hh.hotel_id,#{hotelName}
        order by s.company_code,hh.supplier_code,#{cityCode};
    </insert>

    <delete id="deleteHotelSaleInfoByHotelId">
        delete from t_pro_hotel_sale_info where hotel_id = #{hotelId}
    </delete>

    <select id="querySaleProducts" parameterType="com.tiangong.product.dto.QueryProductRequestDTO" resultType="com.tiangong.product.dto.ProductTempDTO">
        SELECT
            p.hotel_id                          hotelId,
            p.room_id                           roomId,
            p.product_id                        productId,
            p.product_name                      productName,
            p.breakfast_qty                     breakfastQty,
            p.bed_types                         bedTypes,
            p.pay_method                        payMethod,
            p.currency                          currency,
            p.supplier_code                     supplierCode,
            o.org_name                          supplierName,
            p.purchase_type                     purchaseType,
            s.b2b_sale_status                   b2bSaleStatus,
            tpr.cancellation_type               cancellationType,
            tpr.cancellation_advance_days       cancellationAdvanceDays,
            tpr.cancellation_due_time           cancellationDueTime,
            tpr.cancellation_deduction_term     cancellationDeductionTerm,
            tpr.cancel_penalties_type           cancelPenaltiesType,
            tpr.cancel_penalties_value          cancelPenaltiesValue,
            tpr.comparison_type                 comparisonType,
            tpr.reservation_limit_nights        reservationLimitNights,
            tpr.reservation_advance_days        reservationAdvanceDays,
            tpr.reservation_due_time            reservationDueTime,
            tpr.reservation_limit_rooms         reservationLimitRooms
        FROM t_pro_product p
        LEFT JOIN t_pro_sale_status s ON s.product_id = p.product_id
        LEFT JOIN t_org_organization o ON o.org_code = p.supplier_code
        LEFT JOIN t_org_company_supplier cs ON cs.org_id = o.org_id
        LEFT JOIN t_pro_restrict tpr ON tpr.product_id = p.product_id
        WHERE p.active = 1
        AND s.active = 1
        AND cs.available_status = 1
        AND p.hotel_id in
        <foreach collection="hotelIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <choose>
          <when test='null != offShelveStatus and offShelveStatus == "0"'>
              AND p.off_shelve_status = 0
          </when>
          <when test='null != offShelveStatus and offShelveStatus == "1"'>
              AND p.off_shelve_status = 1
          </when>
        </choose>
        <if test="null != supplierCode and supplierCode != '' ">
            AND p.supplier_code = #{supplierCode}
        </if>
        <if test="null != roomId and roomId != '' ">
            AND p.room_id= #{roomId}
        </if>
        <if test='null != saleStatus and saleStatus == "1" and channelCode == "B2B"'>
            AND s.b2b_sale_status = 1
        </if>
        <if test='null != saleStatus and saleStatus == "0" and channelCode == "B2B"'>
            AND ifnull(s.b2b_sale_status,0) = 0
        </if>
        <if test="null != purchaseType and purchaseType != '' ">
            AND p.purchase_type = #{purchaseType}
        </if>
    </select>

    <resultMap id="ProductDTOMap" type="com.tiangong.product.dto.ProductDTO">
        <result column="productId" property="productId"/>
        <result column="productName" property="productName"/>
        <result column="supplierCode" property="supplierCode"/>
        <result column="quotaAccountId" property="quotaAccountId"/>
        <result column="supplierName" property="supplierName"/>
        <result column="hotelId" property="hotelId"/>
<!--        <result column="hotelName" property="hotelName"/>-->
        <result column="roomId" property="roomId"/>
<!--        <result column="roomName" property="roomName"/>-->
        <result column="breakfastQty" property="breakfastQty"/>
        <result column="bedTypes" property="bedTypes"/>
        <result column="purchaseType" property="purchaseType"/>
        <result column="remark" property="remark"/>
        <result column="currency" property="currency"/>
        <result column="priceType" property="priceType"/>
        <result column="taxRuleConfigId" property="taxRuleConfigId"/>
        <result column="payMethod" property="payMethod"/>
        <result column="commissionType" property="commissionType"/>
        <result column="commissionValue" property="commissionValue"/>
        <result column="advancePaymentId" property="advancePaymentId"/>
        <result column="supplyType" property="supplyType"/>
        <result column="maxAdultQty" property="maxAdultQty"/>
        <result column="maxChildQty" property="maxChildQty"/>
        <result column="guestQtyType" property="guestQtyType"/>
        <result column="active" property="active"/>
        <result column="supplyType" property="supplyType"/>
        <result column="cancellationType" property="cancellationType"/>
        <result column="cancellationAdvanceDays" property="cancellationAdvanceDays"/>
        <result column="cancellationDueTime" property="cancellationDueTime"/>
        <result column="cancellationDeductionTerm" property="cancellationDeductionTerm"/>
        <result column="cancelPenaltiesType" property="cancelPenaltiesType"/>
        <result column="cancelPenaltiesValue" property="cancelPenaltiesValue"/>
        <result column="comparisonType" property="comparisonType"/>
        <result column="reservationLimitNights" property="reservationLimitNights"/>
        <result column="reservationAdvanceDays" property="reservationAdvanceDays"/>
        <result column="reservationDueTime" property="reservationDueTime"/>
        <result column="reservationLimitRooms" property="reservationLimitRooms"/>
        <result column="supplierType" property="supplierType"/>
        <result column="offShelveStatus" property="offShelveStatus"/>
        <collection property="guarantees"
                    ofType="com.tiangong.product.dto.GuaranteeDTO"
                    select="com.tiangong.product.mapper.GuaranteeMapper.queryGuarantee"
                    column="{productId=productId}"/>

    </resultMap>


    <select id="queryProduct"  resultMap="ProductDTOMap">
        SELECT
            p.product_id               productId,
            p.product_name             productName,
            p.supplier_code            supplierCode,
            p.quota_account_id         quotaAccountId,
            o.org_name                 supplierName,
            p.hotel_id                 hotelId,
            p.room_id                  roomId,
            p.breakfast_qty            breakfastQty,
            p.bed_types                bedTypes,
            p.purchase_type            purchaseType,
            p.remark                   remark,
            p.currency                 currency,
            p.price_type               priceType,
            p.tax_rule_config_id       taxRuleConfigId,
            p.pay_method               payMethod,
            p.commission_type          commissionType,
            p.commission_value          commissionValue,
            p.advance_payment_id       advancePaymentId,
            IFNULL(p.max_guest_qty,0)	as maxAdultQty,
	        IFNULL(p.max_children_qty,0)	as maxChildQty,
	        IFNULL(p.max_children_age,12)	as maxChildAge,
	        IF(ISNULL(p.max_guest_qty), 0, 1) guestQtyType,
	        p.active                    active,
	        p.supply_type               supplyType,
	        tpr.cancellation_type            cancellationType,
	        tpr.cancellation_advance_days    cancellationAdvanceDays,
	        tpr.cancellation_due_time        cancellationDueTime,
	        tpr.cancellation_deduction_term  cancellationDeductionTerm,
            tpr.cancel_penalties_type        cancelPenaltiesType,
            tpr.cancel_penalties_value       cancelPenaltiesValue,
	        tpr.comparison_type              comparisonType,
	        tpr.reservation_limit_nights     reservationLimitNights,
	        tpr.reservation_advance_days     reservationAdvanceDays,
	        tpr.reservation_due_time         reservationDueTime,
	        tpr.reservation_limit_rooms      reservationLimitRooms,
            p.off_shelve_status              offShelveStatus
        FROM t_pro_product p
        LEFT JOIN  t_org_organization o ON p.supplier_code = o.org_code
        LEFT JOIN  t_pro_restrict tpr ON tpr.product_id = p.product_id
        WHERE
          p.product_id = #{productId}
    </select>

    <!--按照日期查询产品底价-->
    <select id="queryTotalAmt" parameterType="com.tiangong.product.dto.ProductOrderQueryRequestDTO" resultMap="queryTotalAmtMap">
    SELECT
    tpp.product_id productId,
    tprice.base_price basePrice,
    tprice.sale_date saleDate
    FROM t_pro_product tpp
    LEFT JOIN
    t_pro_price tprice
    ON
    tprice.product_id = tpp.product_id
    WHERE
    tpp.hotel_id = #{hotelId}
    AND tpp.room_id = #{roomId}
    AND  tprice.sale_date BETWEEN
    #{startDate} AND
    #{endDate}
    <if test='null != supplierCode and supplierCode!=""'>
            AND tpp.supplier_code = #{supplierCode}
    </if>
    </select>
    <resultMap id="queryTotalAmtMap" type="com.tiangong.product.dto.TotalAmtDTO">
            <result column="productId" property="productId"/>
            <result column="basePrice" property="basePrice"/>
            <result column="saleDate" property="saleDate"/>
    </resultMap>

    <select id="queryProductLogList" parameterType="java.util.Map" resultType="com.tiangong.product.dto.ProductLogDTO">
        SELECT
          p.content content,
          p.created_dt createdDt,
          p.created_by createdBy,
          p.operation_week operationWeek
        FROM t_pro_log p
        WHERE p.product_id = #{productId} AND p.start_date  &lt;= #{saleDate} AND p.end_date >= #{saleDate}
        ORDER BY p.id DESC
    </select>

    <select id="queryProductSaleLogList" parameterType="java.util.Map" resultType="com.tiangong.product.dto.ProductSaleLogDTO">
        SELECT
          p.content content,
          p.created_dt createdDt,
          p.created_by createdBy,
          p.operation_week operationWeek
        FROM t_pro_sale_log p
        WHERE p.product_id = #{productId}
              AND p.company_code = #{companyCode}
        <if test="saleDate != null and saleDate != ''">
            AND p.start_date &lt;= #{saleDate} AND p.end_date >= #{saleDate}
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND p.channel_code = #{channelCode}
        </if>
        ORDER BY p.id DESC
    </select>
    <!-- 根据供应商编码查询运营商编码-->
    <select id="getCompanyCode" resultType="java.lang.String">
            SELECT
            tocs.company_code companyCode,
            too.org_code supplierCode
            FROM t_org_organization too
            LEFT JOIN t_org_company_supplier tocs
            ON too.org_id  = tocs.org_id
            WHERE too.org_code = #{supplierCode}
    </select>

    <select id="queryWillModifyProductStatus" resultType="java.lang.Integer">
        SELECT
           tpp.product_id
        FROM t_pro_product tpp
        WHERE tpp.purchase_type = #{purchaseType}
        <if test="supplierList != null and supplierList.size > 0">
            AND tpp.supplier_code IN
            <foreach collection="supplierList" item="item" index="index" separator="," close=")" open="(">
               #{item}
            </foreach>
        </if>
        <if test="hotelList != null and hotelList.size > 0">
            AND tpp.hotel_id NOT IN
            <foreach collection="hotelList" item="i" index="index" separator="," close=")" open="(">
                #{i}
            </foreach>
        </if>
    </select>

    <select id="queryProductRestrictList" resultType="com.tiangong.product.dto.ProductDTO">
        SELECT
          IFNULL(td.comparison_type, a.comparison_type) comparisonType,
          a.sale_date saleDate,
          a.product_id productId,
          IFNULL(td.reservation_limit_nights, a.reservation_limit_nights) reservationLimitNights,
          IFNULL(td.reservation_advance_days, a.reservation_advance_days) reservationAdvanceDays,
          IFNULL(td.reservation_due_time, a.reservation_due_time) reservationDueTime,
          IFNULL(td.reservation_limit_rooms, a.reservation_limit_rooms) reservationLimitRooms,
          IFNULL(td.cancellation_type, a.cancellation_type) cancellationType,
          IFNULL(td.cancellation_advance_days, a.cancellation_advance_days) cancellationAdvanceDays,
          IFNULL(td.cancellation_due_time, a.cancellation_due_time) cancellationDueTime,
          IFNULL(td.cancellation_deduction_term, a.cancellation_deduction_term) cancellationDeductionTerm
        FROM
          ( SELECT
              tr.comparison_type,
              tr.reservation_limit_nights,
              tr.reservation_advance_days,
              tr.reservation_due_time,
              tr.reservation_limit_rooms,
              tr.cancellation_type,
              tr.cancellation_advance_days,
              tr.cancellation_due_time,
              tr.cancellation_deduction_term,
              ts.sale_date,
              tr.product_id
            FROM t_pro_restrict tr, t_pro_sale_date ts WHERE tr.product_id = #{productId} AND ts.sale_date &gt;= #{startDate} AND ts.sale_date &lt;= #{endDate}) a
            LEFT JOIN t_pro_restrict_daily td ON a.sale_date = td.sale_date AND a.product_id = td.product_id
    </select>

    <select id="querySelfSignedProductCount" resultType="java.lang.Integer">
        SELECT
          count(*)
        FROM t_pro_product t
        LEFT JOIN t_pro_sale_status tpss ON tpss.product_id = t.product_id
        WHERE tpss.active = 1 AND t.supply_type IS NULL
        <if test="productId != null and productId.length > 0">
            AND t.product_id = #{productId}
        </if>
        <if test="supplierCode != null and supplierCode.length > 0">
            AND t.supplier_code = #{supplierCode}
        </if>
        <if test="company_code != null and companyCode.length > 0">
            AND tpss.company_code = #{companyCode}
        </if>

    </select>

    <select id="querySelfSignedProductInfo" resultType="com.tiangong.product.dto.ProductDTO">
        SELECT
           t.product_id productId,
           t.product_name productName,
           t.supplier_code supplierCode,
           t.hotel_id hotelId,
           t.room_id roomId,
           t.breakfast_qty breakfastQty,
           t.purchase_type purchaseType,
           t.supply_type supplyType,
           t.currency currency,
           t.active active
        FROM t_pro_product t
        LEFT JOIN t_pro_sale_status tpss ON tpss.product_id = t.product_id
        WHERE tpss.active = 1 AND t.supply_type IS NULL
        <if test="productId != null and productId.length > 0">
            AND t.product_id = #{productId}
        </if>
        <if test="supplierCode != null and supplierCode.length > 0">
            AND t.supplier_code = #{supplierCode}
        </if>
        ORDER BY t.product_id
    </select>

    <select id="querySelfSignedProductRestrict" resultType="com.tiangong.product.domain.ProductRestrictPO">
        (SELECT
          t.product_id productId,
          t.cancellation_type cancellationType,
          t.cancellation_advance_days cancellationAdvanceDays,
          t.cancellation_due_time cancellationDueTime,
          t.cancellation_deduction_term cancellationDeductionTerm,
          t.comparison_type comparisonType,
          t.reservation_limit_nights reservationLimitNights,
          t.reservation_advance_days reservationAdvanceDays,
          t.reservation_due_time reservationDueTime,
          t.reservation_limit_rooms reservationLimitRooms,
          null saleDate,
          t.product_id redisKey
        FROM t_pro_restrict t
        <where>
            <if test="list != null and list.size > 0">
                t.product_id IN
                <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                    #{item.productId}
                </foreach>
            </if>
        </where>
        )
        UNION
        (SELECT
        td.product_id productId,
        td.cancellation_type cancellationType,
        td.cancellation_advance_days cancellationAdvanceDays,
        td.cancellation_due_time cancellationDueTime,
        td.cancellation_deduction_term cancellationDeductionTerm,
        td.comparison_type comparisonType,
        td.reservation_limit_nights reservationLimitNights,
        td.reservation_advance_days reservationAdvanceDays,
        td.reservation_due_time reservationDueTime,
        td.reservation_limit_rooms reservationLimitRooms,
        DATE_FORMAT(td.sale_date, "%Y-%m-%d") saleDate,
        CONCAT(td.product_id, "_", td.sale_date) redisKey
        FROM t_pro_restrict_daily td
        <where>
            <if test="list != null and list.size > 0">
                td.product_id IN
                <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                    #{item.productId}
                </foreach>
            </if>
        </where>
        )
    </select>

    <select id="querySelfSignedBasePriceAndRoomStatus" resultType="com.tiangong.product.dto.ProductDayQuotationDTO">
        SELECT
          a.product_id productId,
          a.sale_date saleDate,
          tpp.base_price modifiedBasePrice,
          tpq.room_status roomStatus,
          tpq.remaining_quota modifiedQuota,
          2 quotaAdjustmentType,
          2 basePriceAdjustmentType,
          tpq.over_draft_status overDraftStatus
        FROM
        (SELECT
          tpp.product_id,
          tpp.quota_account_id,
          tpsd.sale_date,
          tpp.currency
        FROM
          t_pro_product tpp,t_pro_sale_date tpsd
        WHERE
          tpsd.sale_date >= DATE_FORMAT(NOW(), "%Y-%m-%d") AND tpsd.sale_date &lt;= DATE_ADD(NOW(), INTERVAL 100 DAY)
        )a
        LEFT JOIN t_pro_price tpp ON tpp.product_id = a.product_id AND a.sale_date = tpp.sale_date
        LEFT JOIN t_pro_quota tpq ON tpq.quota_account_id = a.quota_account_id AND a.sale_date = tpq.sale_date
        <where>
            <if test="list != null and list.size > 0">
                a.product_id IN
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                   #{item.productId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="querySelfSignedSalePrice" resultType="com.tiangong.product.domain.ProductDayIncreasePO">
        SELECT
          t.product_id productId,
          t.company_code companyCode,
          DATE_FORMAT(t.sale_date, "%Y-%m-%d") saleDate,
          t.b2b_adjustment_type b2bAdjustmentType,
          t.b2b_modified_amt b2bModifiedAmt
        FROM t_pro_dayincrease t
        WHERE  t.sale_date >= DATE_FORMAT(NOW(), "%Y-%m-%d") AND t.sale_date &lt;= DATE_ADD(NOW(), INTERVAL 100 DAY)
          <if test="list != null and list.size > 0">
              AND t.product_id IN
              <foreach collection="list" open="(" close=")" separator="," index="index" item="item">
                  #{item.productId}
              </foreach>
          </if>
    </select>

    <select id="querySelfSignedSaleStatus" resultType="com.tiangong.product.domain.ProductSaleStatusPO">
        SELECT
            tpss.product_id productId,
            tpss.company_code companyCode,
            tpss.b2b_sale_status b2bSaleStatus
        FROM t_pro_sale_status tpss
        <where>
            <if test="list != null and list.size > 0">
                tpss.product_id IN
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item.productId}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteAgentNotAvailHotel">
        delete from ${xTable}
        where updated_dt &lt; #{date}
    </delete>

    <select id="agentApiConfig" resultType="com.tiangong.dto.product.AgentApiConfigDTO" parameterType="java.lang.Object">
        select
         IFNULL(original_protocol_price_switch, 0) originalProtocolPriceSwitch,
         IFNULL(quote_switch, 0) quoteSwitch,
         rating,
         IFNULL(data_encryption_switch, 0) dataEncryptionSwitch,
         ops_secret_key,
         customer_public_key,
         tiangong_public_key
         from t_org_agent_api_config where agent_code = #{agentCode} and domestic_or_overseas = #{supplyType}
    </select>

    <select id="selectApplyAllAgentHotelLabel" resultMap="HotelLabelMap">
        SELECT
            t1.hotel_id,
            t3.label_name
        FROM t_hotel_recommend t1
        LEFT JOIN t_hotel_recommend_label_rel t2 ON t2.hotel_recommend_id = t1.id
        LEFT JOIN t_hotel_label_config t3 ON t3.hotel_label_config_id = t2.hotel_label_config_id
        WHERE t1.deleted = 0 and t1.agent_code = "0"
        AND t1.hotel_id IN
        <foreach collection="hotelIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAgentHotelLabel" resultMap="HotelLabelMap">
        SELECT
        t1.hotel_id,
        t3.label_name
        FROM t_hotel_recommend t1
        LEFT JOIN t_hotel_recommend_label_rel t2 ON t2.hotel_recommend_id = t1.id
        LEFT JOIN t_hotel_label_config t3 ON t3.hotel_label_config_id = t2.hotel_label_config_id
        WHERE
            t1.deleted = 0
        AND (t1.agent_code = #{agentCode} or t1.agent_code = '0')
        <if test="hotelIdList!=null">
            AND t1.hotel_id IN
            <foreach collection="hotelIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="hotelId!=null">
            AND t1.hotel_id =#{hotelId}
        </if>
    </select>

    <select id="selectHotelLabelNameList" resultType="java.lang.String">
        select
            hlc.label_name
        from t_hotel_recommend_label_rel hrlr
        left join t_hotel_label_config hlc on hlc.hotel_label_config_id = hrlr.hotel_label_config_id
        where hrlr.hotel_recommend_id = #{hotelRecommendId}
    </select>

    <select id="selectHotelSaleInfoList" resultType="com.tiangong.product.domain.resp.HotelSaleInfoItemResp">
        SELECT
            sale_info_id,
            supplier_code,
            hotel_id
        FROM t_pro_hotel_sale_info
        WHERE active = 1
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
        AND product_count > 0
        AND sale_info_id > #{lastId}
        ORDER BY sale_info_id
        LIMIT #{batchSize}
    </select>
</mapper>
