<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tiangong.product.mapper.ProductInfoMapper">


    <select id="queryProductIdsByHotelId" parameterType="com.tiangong.product.domain.resp.QueryProductIdsRequest"
            resultType="com.tiangong.product.domain.resp.HotelProductIdsResponse">
        select t1.hotel_id as hotelId,
               t1.product_id as productId ,
               t1.breakfast_qty as breakfastQty,
               t1.bed_types as bedTypes
        from t_pro_product t1
            inner join t_pro_sale_status t2 on t1.product_id = t2.product_id
        <where>
            t1.active = t2.active and t1.active = 1 and t2.b2b_sale_status = 1
            and t1.off_shelve_status=1
            <if test="hotelId != null and  hotelId != ''">
                and t1.hotel_id = #{hotelId}
            </if>
            <if test="payMethod != null">
                and t1.pay_method = #{payMethod}
            </if>
            <if test="companyCode != null and  companyCode != ''">
                and t2.company_code = #{companyCode}
            </if>
            <if test="priceType!=null">
                and t1.price_type = #{priceType}
            </if>
            <if test="supplyCodes != null and supplyCodes.size > 0 ">
                and t1.supplier_code in
                <foreach collection="supplyCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>

    <select id="selectProductHotel" resultType="com.tiangong.product.domain.resp.HotelSaleInfoItemResp">
        select
            t1.hotel_id,
            t1.supplier_code
        from t_pro_product t1
        inner join t_pro_sale_status t2 on t1.product_id = t2.product_id
        <where>
            t1.active = t2.active
            and t1.active = 1
            and t2.b2b_sale_status = 1
            and t1.off_shelve_status = 1
            <if test="currency != null">
                and t1.currency = #{currency}
            </if>
            <if test="taxRuleConfigId != null">
                and t1.tax_rule_config_id = #{taxRuleConfigId}
            </if>
            <if test="productId != null">
                and t1.product_id = #{productId}
            </if>
        </where>
        group by t1.hotel_id, t1.supplier_code
    </select>

</mapper>
