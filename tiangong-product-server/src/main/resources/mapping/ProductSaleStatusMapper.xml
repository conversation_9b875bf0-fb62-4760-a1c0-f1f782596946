<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tiangong.product.mapper.ProductSaleStatusMapper">
    <update id="batchModifyProductSaleStatus" parameterType="java.util.List">
        UPDATE
        t_pro_sale_status
        SET
        <if test="channelCode != null">
            <if test="channelCode == 'B2B'">
                b2b_sale_status = #{saleStatus},
            </if>
        </if>
        updated_by = #{updatedBy},
        updated_dt = NOW()
        WHERE company_code = #{companyCode} AND product_id IN
        <foreach collection="list" item="item" open="(" index="index" close=")" separator=",">
            #{item.productId}
        </foreach>
    </update>

    <select id="querySupplierShareStatus" resultType="java.lang.Integer">
        SELECT is_share_supplier
        FROM t_org_organization
        WHERE org_code = #{supplierCode}
    </select>

    <select id="querySupplierCompanyList" resultType="java.lang.String">
        SELECT s.company_code
        FROM t_org_company_supplier s, t_org_organization t
        WHERE s.available_status = 1 AND s.org_id = t.org_id
        AND t.org_code = #{supplierCode}
    </select>


</mapper>
