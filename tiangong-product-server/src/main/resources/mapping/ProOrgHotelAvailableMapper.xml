<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.product.mapper.ProOrgHotelAvailableMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.product.domain.entity.ProOrgHotelAvailableEntity">
        <result property="id" column="id"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="availableType" column="available_type"/>
        <result property="hotelId" column="hotel_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <!-- 分页查询供应商酒店黑白名单列表 -->
    <select id="queryProOrgHotelAvailablePage" parameterType="com.tiangong.product.domain.dto.ProOrgHotelAvailablePageDTO"
            resultType="com.tiangong.product.domain.dto.ProOrgHotelAvailableRespDTO">
        SELECT
            h.id,
            h.supplier_code supplierCode,
            h.city_code cityCode,
            h.hotel_id hotelId,
            h.available_type availableType,
            ogn.org_name supplierName
        FROM t_pro_org_hotel_available h
        LEFT JOIN t_org_organization ogn ON h.supplier_code = ogn.org_code
        <where>
            <if test="dto.supplierCode != null and dto.supplierCode != ''">
                AND h.supplier_code = #{dto.supplierCode}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                AND ogn.org_name LIKE CONCAT('%', #{dto.supplierName}, '%')
            </if>
            <if test="dto.hotelId != null">
                AND h.hotel_id = #{dto.hotelId}
            </if>
            <if test="dto.cityCode != null and dto.cityCode != ''">
                AND h.city_code = #{dto.cityCode}
            </if>
        </where>
        ORDER BY h.id DESC
    </select>

    <!-- 根据酒店ID查询所有黑白名单配置 -->
    <select id="selectByHotelId" resultMap="BaseResultMap">
        SELECT
            id,
            supplier_code,
            available_type,
            hotel_id,
            created_by,
            created_dt,
            updated_by,
            updated_dt
        FROM t_pro_org_hotel_available
        WHERE hotel_id = #{hotelId}
        ORDER BY available_type ASC, supplier_code ASC
    </select>

</mapper>
