package com.tiangong.dto.product;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 税费明细
 */
@Data
public class TaxDetailDto {

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 税费
     */
    private BigDecimal taxFee;

    /**
     * 销售税费
     */
    private BigDecimal salesTax;

    /**
     * 其他税费
     */
    private BigDecimal otherTax;

    /**
     * 折扣
     */
    private BigDecimal discount;


    /**
     * 房费
     * 供应商输出的
     */
    private BigDecimal supplyRoomPrice;

    /**
     * 税费
     * 供应商输出的
     */
    private BigDecimal supplyTaxFee;

    /**
     * 销售税费
     * 供应商输出的
     */
    private BigDecimal supplySalesTax;

    /**
     * 其他税费
     * 供应商输出的
     */
    private BigDecimal supplyOtherTax;

    /**
     * 供应商折扣，
     * 此处为用到默认设置为0
     */
    private BigDecimal supplyDiscount;
}