package com.tiangong.dto.product.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class DhubProductDetailDto {

    /**
     * 房型id
     */
    private Long roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 价格计划id
     * 为sp_hotel_id+_+sp_room_id+_+sp_ratePlanId/productId+_+supplyCode+_+protocolFlag(1/0)#SpPricePlanName
     * 示例：561794_1702770_85232030_S10267587_1#单早
     */
    private String ratePlanId;

    /**
     * 价格计划名称
     */
    private String ratePlanName;

    /**
     * 床型信息
     */
    private List<BedTypesDetailDto> bedTypeDetails;

    /**
     * 床型描述
     */
    private String bedInfoDesc;

    /**
     * 窗型编码 枚举值见【窗型表】
     */
    private Integer windowType;

    /**
     * 是否立即确认 是否立即确认
     * 0 否
     * 1 是
     * 空 不确定
     */
    private Integer immediateConfirm;

    /**
     * 是否可加床
     * 0 否
     * 1 是
     * 空 不确定
     */
    private Integer addBed;

    /**
     * 加床费用
     */
    private BigDecimal addBedPrice;

    /**
     * 加床费用描述
     */
    private String addBedPriceDesc;

    /**
     * 均价
     */
    private BigDecimal avgPrice;

    /**
     * 订单是否需要证件信息
     * 0 不需要
     * 1 需要
     * 空 默认为不需要
     */
    private Integer certificateFlag;


    /**
     * 宽带 1：收费，2：免费，3:无，4：未设置
     */
    private Integer broadNet;

    /***/
    private String supplyCode;

    /**
     * 预订状态
     */
    private Integer bookType;

    /**
     * 发票模式
     */
    private Integer invoiceModel;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 提示信息，带有 html 标签，展示时需支持按html 标签
     */
//    private String reminder;

    /**
     * 售卖信息列表
     */
    private List<DhubPriceItemDto> priceItems;

    /**
     * 每间每晚的价格数据
     */
    private List<DhubRoomItemDetailDto> roomItemDetails;

    /**
     * 是否到店付：1-是，0或其它-否， 默认0
     */
    private Integer payAtHotelFlag;

    /**
     * 2022年1月4日增加
     * 产品标签（0或者null：无标签  1：协议价格标签）
     */
    private Integer productLabel;

    /**
     * 0或者空：表示非代结算
     * 1：代结算
     */
    private Integer labelType;

    /**
     * 产品小类标签
     */
    private Integer smallProductLabel;


    /**
     * 供应商编码
     */
    @JsonIgnore
    private String supplyClass;

    /**
     * 到店另付费用；针对海外酒店有效
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种；针对海外酒店有效
     */
    private String payAtHotelFeeCurrency;

    /**
     * 房费、税及其他费用明细；针对海外酒店有效，暂不支持
     */
    private DhubTaxAndFeeDetail taxAndFeeDetails;

    /**
     * 0或空非快速处理，1 快速处理  表示供应商通过直连方式进行确认，确认速度较快，实际确认结果仍以订单确认通知结果为准。
     */
    private Integer rapidProcessing;

    /**
     * 最大入住人数
     */
    private Integer maxPerson;

    /**
     * 最小入住人数
     */
    private Integer minPerson;

    /**
     * 入住类型：1日历房（全日房），2钟点房
     */
    private Integer checkInType;

    /**
     * 钟点房可订时间类型，1-全天24可入住，2-自定义（最早入住到最晚离店时分之间）
     */
    private Integer timeSlotType;

    /**
     * 自定义)钟点房最早入住时分，举例：09:00
     */
    private String checkInEarlyTime;

    /**
     * (自定义)钟点房最晚离店时分，跨天会显示次日，举例：次日04:00
     */
    private String checkOutLateTime;

    /**
     * 钟点房入住小时数
     */
    private BigDecimal checkInHour;
//
//    /**
//     * 快速处理标签开关
//     */
//    private Integer quickProcessingSwitch;

    /**
     * 每日总价（含税费）
     */
    private BigDecimal totalSalePrice;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    /**
     * 每日总底价(供应商金额)
     */
    private BigDecimal totalBasePrice;

    /**
     * 底价币种（供应商币种）
     */
    private String baseCurrency;

    /**
     * 房费总价（不含税费）
     */
    private BigDecimal roomPrice;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;

    /**
     * 供应商类型
     * @see com.tiangong.enums.SupplierFormEnum
     */
    private Integer supplierForm;
}
