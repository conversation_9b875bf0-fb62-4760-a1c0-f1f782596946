package com.tiangong.dto.product.request;

import com.tiangong.dto.common.BusinessRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class HotelImageRequest extends BusinessRequest {

    /**
     * 酒店id列表
     */
    @Size(min = 1, max = 50, message = "PARAM_MAX_50_HOTELIDS")
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private List<Long> hotelIds;

    /**
     * 请求的图片地址类型
     */
    private Integer type = 1;

    /**
     * 返回城市名称的语言类型，针对海外城市有效，参考语言类型字典项，不传默认查询中文
     */
    private String language;
}
