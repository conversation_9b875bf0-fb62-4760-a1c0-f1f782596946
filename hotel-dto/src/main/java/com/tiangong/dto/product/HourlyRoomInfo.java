package com.tiangong.dto.product;



import lombok.Data;

/**
 * 钟点房时间信息
 */
@Data
public class HourlyRoomInfo {

    /**
     * 最早到店时间     Integer	如 10:00，该值为 600
     */
    private Integer earliestArriveTime;
    /**
     * 最晚离店时间	Integer	如 18:00，该值为 1080
     */
    private Integer latestLeaveTime;
    /**
     * 连住时间	Integer	如 3小时，该值为3
     */
    private Integer duration;
    /**
     * 钟点房提示话术	String
     */
    private String hourlyRoomTips;

}
