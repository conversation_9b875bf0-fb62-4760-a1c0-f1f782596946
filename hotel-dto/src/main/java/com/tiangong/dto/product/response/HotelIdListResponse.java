package com.tiangong.dto.product.response;


import com.tiangong.dto.common.BusinessResponse;
import lombok.Data;

import java.util.List;

@Data
public class HotelIdListResponse extends BusinessResponse {

    /**
     * maxId标记值，用于下一页查询。当maxId为-1时，表示已经查询到最后一页
     */
    private Long maxId;

    /**
     * 酒店id列表
     */
    private List<Long> hotelIds;

    /**
     * 总页数
     */
    private Integer totalPage;

    /**
     * 当前页
     */
    private Integer currentPage;

    /**
     * 总记录数
     */
    private Integer total;

    /**
     * 页大小
     */
    private Integer pageSize;
}
