package com.tiangong.dto.product.request;

import com.tiangong.dto.common.BusinessRequest;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Validated
public class HotelProductRequest extends BusinessRequest {

    /**
     * 与cityCode二选一必填
     * 每次最多查20个酒店，如果传入酒店Id，则其他筛选条件不生效（适用于合作商有自己的排序和过滤逻辑，只需要查具体酒店的列表展示信息）
     */
    private List<Long> hotelIds;
    private Long[] hotelIdsArr;

    /**
     * cityCode	城市编码	String	否	与hotelIds二选一必填
     */
    private String cityCode;
    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;
    private Date checkInDateDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;
    private Date checkOutDateDate;

    /**
     * 入住天数 入离时间差 必填
     */
    private Integer checkDays;

    /**
     * keyWords	关键词	String	否	关键词查询，酒店名称
     */
    private String keyWords;
    /**
     * roomNum	间数	Integer	否	房间数量
     */
    private Integer roomNum;
    /**
     * district	行政区	String	否	行政区编码
     */
    private String district;
    /**
     * hotelStar	星级	List<String>	否	星级编码，见【酒店星级】常量
     */
    private List<String> hotelStar;
    /**
     * 需要排序使用 数据库存放数字类型
     */
    private Integer[] hotelStarArr;
    /**
     * lowestPrice	最低价	BigDecimal	否	最低价
     * 注意 价格范围：传200-300，实际查询0-300  也就是传入的最低价不生效
     */
    private BigDecimal lowestPrice;
    /**
     * highestPrice	最高价	BigDecimal	否	最高价
     */
    private BigDecimal highestPrice;
    /**
     * bedType	床型	String	否	床型编码
     */
    private String bedType;
    /**
     * hotelBrands	酒店品牌	List<String>	否	品牌编码
     */
    private List<String> hotelBrands;
    private String[] hotelBrandsArr;
    /**
     * longitude	经度	String	否	百度经度
     */
    private String longitude;
    private Double longitudel;
    /**
     * latitude	纬度	String	否	百度纬度
     */
    private String latitude;
    private Double latitudel;
    /**
     * 距离（单位：米）
     * 传入距离时，必须同时传入经纬度值，否则距离参数不生效
     */
    private Integer distance;

    /**
     * protocolFlag	协议酒店	Integer	否	传入1则只查询协议酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private Integer protocolFlag;
    /**
     * cancelFlag	可取消	Integer	否	传入1则只查询有可取消产品酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private Integer cancelFlag;
    /**
     * breakfastFlag	含早	Integer	否	传入1则只查询有含早餐产品酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private Integer breakfastFlag;
    /**
     * InstantConfirmFlag	即时确认	Integer	否	传入1则只查询有即时确认产品酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private Integer instantConfirmFlag;
    /**
     * currentPage	当前页	Integer	否	当前查询页数，为空默认1
     */
    private Integer currentPage;
    /**
     * pageSize	每页记录数	Integer	否	每页查询记录数，为空默认为10，最大不可超过20
     */
    private Integer pageSize;

    /**
     * 是否钟点房
     * 1：是
     * 0：否
     * 空：否
     */
    private Integer hourlyRoom;

    /**
     * 排序方式：
     * 1.推荐排序（推荐分值越高，排序越靠前）
     * 2.距离由近到远
     * 3.价格由低到高
     * 4.价格由高到低
     * 5.星级由低到高
     * 6.星级由高到低
     * 为空默认为推荐排序
     */
    private Integer sortType;
    /**
     * 要查询的表明 由于分页 需要自定义查询某个表
     */
    private String xTableName;

}
