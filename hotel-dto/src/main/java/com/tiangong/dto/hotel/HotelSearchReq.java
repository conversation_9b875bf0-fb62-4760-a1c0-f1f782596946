package com.tiangong.dto.hotel;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class HotelSearchReq {

    /**
     * 搜索目的地id
     */
    @NotEmpty(message = "ADDRESS_BY_ID_NOT_EMPTY")
    private String destinationId;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 语言
     */
    private String language = "zh-CN";
}
