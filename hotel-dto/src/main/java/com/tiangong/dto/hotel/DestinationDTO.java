package com.tiangong.dto.hotel;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

@Data
public class DestinationDTO {
    /**
     * 目的地id
     */
    private String destinationId;

    /**
     * 目的地
     */
    private Set<DestinationDesc> destinationName = new HashSet<>();

    /**
     * 目的地类型
     */
    private Integer dataType;

    /**
     * 目的地对应表的唯一id
     */
    private String dataTypeId;

    /**
     * 围栏坐标
     */
    private String fenceSignage;

    /**
     * 酒店id
     */
    private Set<Long> hotelIds;

    /**
     * 热度分数值
     */
    private Long popularityScore;
}
