package com.tiangong.dto.hotel.base;

import lombok.Data;

@Data
public class ImageDTO {
    /**
     * 图片ID
     */
    private Long imgId;
    /**
     * 图片类型 1：房型 2：外景 3：大堂 4:设施  5:其它  ;6宴会厅;7会议厅;8门票;9其它服务
     */
    private Integer imgType;
    /**
     * 是否酒店主图
     */
    private String isMainImg;
    /**
     * 是否房型主图 会议室主图共用)(1：是；0 or null：不是)
     */
    private String isRoomMainImg;
    /**
     * 对外图片地址
     */
    private String imgUrl;
    /**
     * 房型或会议室id
     */
    private Long roomtypeId;
}