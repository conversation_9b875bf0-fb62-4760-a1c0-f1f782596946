package com.tiangong.dto.hotel.base;

import lombok.Data;

@Data
public class HotelTypeVideoDTO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 视频类型（1-酒店视频，2-房型视频）
     */
    private String videoType;
    /**
     * 所属者id（房仓（酒店/房型）的id）
     */
    private Long ownerId;
    /**
     * 视频的源地址
     */
    private String originVideoUrl;
    /**
     * 房仓的视频地址
     */
    private String videoUrl;
    /**
     * 房仓FTP地址
     */
    private String ftpVideoUrl;
    /**
     * 海报地址
     */
    private String posterUrl;
    /**
     * 大海报地址
     */
    private String bigPosterUrl;
    /**
     * 海报FTP地址
     */
    private String ftpPosterUrl;
    /**
     * 大海报FTP地址
     */
    private String ftpBigPosterUrl;
    /**
     * 海报源地址
     */
    private String originPosterUrl;
    /**
     * 大海报源地址
     */
    private String originBigPosterUrl;
    /**
     * 视频来源
     */
    private String videoOrigin;
    /**
     * 下载状态（0-未下载、1-已下载）
     */
    private String downloadStatus;
    /**
     * 是否有效（0-无效、1-有效）
     */
    private String status;
}