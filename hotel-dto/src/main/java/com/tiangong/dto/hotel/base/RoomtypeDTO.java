package com.tiangong.dto.hotel.base;

import lombok.Data;

import java.util.List;

@Data
public class RoomtypeDTO {
    /**
     * 房型ID
     */
    private Long roomtypeId;
    /**
     * 酒店id
     */
    private Long hotelId;
    /**
     * 房型名称
     */
    private String roomtypeName;
    /**
     * 房型英文名称 国内酒店需存储该字段
     */
    private String roomtypeEngName;
    /**
     * 房间面积
     */
    private String roomArea;
    /**
     * 房间数量
     */
    private String roomCount;
    /**
     * 是否允许加床
     */
    private String allowAddBed;
    /**
     * 是否允许加婴儿床
     */
    private String allowAddBabyBed;
    /**
     * 房型所属楼层
     */
    private String roomFloor;
    /**
     * 最大入住人数
     */
    private Integer maxPerson;
    /**
     * 最大入住儿童数
     */
    private Integer maxChild;
    /**
     * 最大入住成人数
     */
    private Integer maxGrown;
    /**
     * 最小入住成人年龄
     */
    private Integer minGrownAge;
    /**
     * 最小入住儿童年龄
     */
    private Integer minChildAge;
    /**
     * 最小入住婴儿年龄
     */
    private Integer minBabyAge;
    /**
     * 供应商房型ID
     */
    private String supplyRoomId;
    /**
     * 状态
     */
    private String status;
    /**
     * 来源
     */
    private String source;
    /**
     * 无线宽带
     * 0-没有
     * 1-全部房间有且收费
     * 2-全部房间有且免费
     * 3-部分房间有且收费
     * 4-部分房间有且免费
     */
    private Integer wirelessBroadNet;
    /**
     * 有线宽带
     * 0-没有
     * 1-全部房间有且收费
     * 2-全部房间有且免费
     * 3-部分房间有且收费
     * 4-部分房间有且免费
     */
    private Integer wiredBroadNet;
    /**
     * 是否可吸烟
     * 1-是
     * 2-否
     * 3-部分
     */
    private Integer isAllowSmoking;
    /**
     * 窗户类型
     * 0-无窗
     * 1-部分有窗
     * 2-有窗
     * 4-内窗
     * 5-天窗
     */
    private Integer windowDetail;
    /**
     * 语言类型
     */
    private String languageType;
    /**
     * 卧室床
     */
    private List<RoomDetailBedDTO> roomBed;
    /**
     * 客厅床
     */
    private List<RoomDetailBedDTO> livingBed;
    /**
     * 卫生间
     */
    private List<ToiletDTO> toilet;
    /**
     * 房型图片地址
     */
    private String imgUrl;
    /**
     * 描述
     */
    private String description;
    /**
     * 时区
     */
    private String timeZone;
}