package com.tiangong.dto.hotel.base;

import lombok.Data;

import java.util.List;

@Data
public class FacilityPublicDTO {
    /**
     * 设施ID
     */
    private Long facilityId;
    /**
     * 设施名称
     */
    private String facilityName;
    /**
     * 适用范围
     */
    private Integer facilityRadius;
    /**
     * 设施值
     */
    private String facilityValue;
    /**
     * 设施描述
     */
    private String facilityDes;
    /**
     * 设施类别编码
     */
    private Long categoryId;
    /**
     * 是否热门
     */
    private Integer isHote;
    /**
     * 是否展示
     */
    private Integer isShow;
    /**
     * 设施编码
     */
    private String facilityCode;
    /**
     * 设施类型
     */
    private Integer facilityType;
    /**
     * 状态
     */
    private String status;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 设施编码
     */
    private Long dataId;
    /**
     * 设施类型名称
     */
    private String dataName;
    /**
     * 设施类型排序
     */
    private Integer dataSort;
    /**
     * 房型设施集合
     */
    private List<RoomFacilityDTO> roomFacilityList;
}