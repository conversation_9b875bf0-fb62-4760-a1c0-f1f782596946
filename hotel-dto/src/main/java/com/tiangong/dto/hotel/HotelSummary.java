package com.tiangong.dto.hotel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HotelSummary implements Serializable {

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店联系电话
     */
    private String tel;

    /**
     * 位置（国家、省份、城市、地址）
     */
    private HotelLocation location;

    /**
     * 经纬度（百度、谷歌、高德）
     */
    private GeoInfo geoInfo;

    /**
     * 星级
     */
    private HotelStar hotelStar;

    /**
     * 房型信息
     */
    private List<RoomSummary> rooms;

    /**
     * 集团
     */
    private GroupDTO group;

    /**
     * 品牌
     */
    private BrandDTO brand;

    /**
     * 评分
     */
    private HotelRatingsDTO rating;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省份编码
     */
    private String provinceCode;
}
