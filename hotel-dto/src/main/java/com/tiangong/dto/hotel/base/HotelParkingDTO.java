package com.tiangong.dto.hotel.base;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class HotelParkingDTO {
    /**
     * 酒店id
     */
    private Long hotelId;
    /**
     * 位置 0-酒店内 1-酒店附近
     */
    private String parkingLocation;
    /**
     * 停车场类型 0-私人停车场 1-公共停车场
     */
    private String parkingType;
    /**
     * 是否预定 0-需要预定 1-无需预定 2-无法提前预定
     */
    private String isReserved;
    /**
     * 是否收费 0-免费 1-部分住客可能收费 2-收费
     */
    private String isChargeable;
    /**
     * 收费金额
     */
    private BigDecimal chargeAmount;
    /**
     * 币种
     */
    private String currency;
    /**
     * 收费方式
     */
    private String chargeManner;
    /**
     * 车位是否限制 0-否 1-是
     */
    private String isLimitParking;
}