package com.tiangong.dto.hotel.base;

import lombok.Data;

@Data
public class InOutTimeDTO {
    /**
     * 酒店id
     */
    private Long hotelId;
    /**
     * 规定入住时间(最早入住)
     */
    private String checkInTime;
    /**
     * 规定退房时间(最晚离店)
     */
    private String checkOutTime;
    /**
     * 最晚入住时间
     */
    private String checkInLateTime;
    /**
     * 最早离店时间
     */
    private String checkOutEarlyTime;
    /**
     * 接受客人
     */
    private String accepCustom;
}