package com.tiangong.dto.hotel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用途：房型概要信息
 *
 * @author：Owen
 */
@Data
public class RoomSummary implements Serializable {

    /**
     * 房型id
     */
    private Long roomtypeId;

    /**
     * 房型中文名称
     */
    private String roomtypeName;

    /**
     * 房型英文名称
     */
    private String roomtypeEngName;

    /**
     * 窗型
     */
    private Integer windowDetail;

    /**
     * 最大入住人数
     */
    private Integer maxPerson;

    /**
     * 卧室床
     */
    private List<RoomDetailBedDTO> roomBed;

    /**
     * 客厅床
     */
    private List<RoomDetailBedDTO> livingBed;
}
