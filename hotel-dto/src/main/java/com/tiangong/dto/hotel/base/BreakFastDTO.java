package com.tiangong.dto.hotel.base;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BreakFastDTO {
    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 是否提供早餐 0-否 1-是
     */
    private String isBreakfast;

    /**
     * 早餐形式
     */
    private String breakfastForm;

    /**
     * 早餐形式描述
     */
    private String breakfastFormDesc;

    /**
     * 固定套餐金额
     */
    private BigDecimal fixedAmount;

    /**
     * 盒装视频/包装食品金额
     */
    private BigDecimal operateAmount;

    /**
     * 自助餐金额
     */
    private BigDecimal buffetAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 早餐类型(以,分割)
     */
    private String breakfastType;

    /**
     * 早餐类型描述(以,分割)
     */
    private String breakfastTypeDesc;

    /**
     * 早餐种类(以,分割)
     */
    private String breakfastVariety;

    /**
     * 早餐种类描述(以,分割)
     */
    private String breakfastVarietyDesc;

    /**
     * 0-每日开放 1-指定日期开放
     */
    private String breakfastOpenType;

    /**
     * 早餐指定时间列表
     */
    private List<HotelBreakfastOpenTimeDTO> breakfastOpenTimes;
}