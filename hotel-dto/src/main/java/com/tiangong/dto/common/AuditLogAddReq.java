package com.tiangong.dto.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AuditLogAddReq implements Serializable {

    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private Integer eventResult;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 登陆账号
     */
    private String userAccount;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 目标id
     */
    private String targetId;
    /**
     * 目标
     */
    private String target;
    /**
     * 目标类型
     * 0：用户
     * <p>
     * 1：订单
     */
    private Integer targetType;

    private Integer auditLogEnumNo;
}