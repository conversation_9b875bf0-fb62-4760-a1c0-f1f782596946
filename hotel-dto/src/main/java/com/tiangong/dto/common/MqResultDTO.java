package com.tiangong.dto.common;

import lombok.Data;

@Data
public class MqResultDTO {

    /**
     * 生产者组或者消费者组名称
     */
    private String groupName;

    /**
     * 注册中心
     */
    private String namesrvAddr;

    /**
     * 主题名称
     */
    private String topicName;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * broker名称
     */
    private String brokerName;

    /**
     * 消费者一次消费的次数
     */
    private Integer consumerTimes;

    /**
     * 读取的tag
     */
    private String tag;

    /**
     * 间隔时间
     */
    private String intervalTime;
}
