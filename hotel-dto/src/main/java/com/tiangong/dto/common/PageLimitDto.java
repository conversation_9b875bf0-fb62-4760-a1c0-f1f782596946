package com.tiangong.dto.common;

import lombok.Data;

@Data
public class PageLimitDto {

    /**
     * 页码 第几页 =PageNum
     */
    private int queryPage = 1;

    /**
     * 分页一次查询记录数=PageSize=100
     */
    private int querySize = 100;

    /**
     * 分页 limit 起始位置
     */
    private int limitStartIndex;

    /**
     * 设置分页 limit 起始位置
     *
     * @return
     */
    public int getLimitStartIndex() {
        return (queryPage - 1) * querySize;
    }


}
