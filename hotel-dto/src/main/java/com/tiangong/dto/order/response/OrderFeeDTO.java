package com.tiangong.dto.order.response;

import com.tiangong.dto.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderFeeDTO extends BaseDTO implements Serializable {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 房间号
     */
    private String roomNumber;

    /**
     * 入住人
     */
    private String guestList;

    /**
     * 入住成人数
     */
    private Integer adultQty;

    /**
     * 入住儿童数
     */
    private Integer childrenQty;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种
     */
    private Integer payAtHotelCurrency;

    /**
     * 费用明细
     */
    private List<OrderFeeDetailDTO> priceList;
}
