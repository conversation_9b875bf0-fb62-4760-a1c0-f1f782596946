package com.tiangong.dto.order.response;


import com.tiangong.dto.common.BusinessResponse;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.request.GuestInfo;
import com.tiangong.dto.order.request.InvoiceInfo;
import com.tiangong.dto.order.request.OrderPriceItem;
import com.tiangong.dto.product.HourlyRoomDetail;
import com.tiangong.dto.product.response.TipInfosDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderDetailResponse extends BusinessResponse {

    private String coOrderCode;

    private String fcOrderCode;

    private Integer orderStatus;

    private String channelState;

    /**
     * 酒店确认号
     */
    private String hotelConfirmNo;

    private Long hotelId;

    private String hotelName;

    private Long roomId;

    private String roomName;

    private String ratePlanId;

    private String ratePlanName;

    private String bedType;

    private String checkInDate;

    private String checkOutDate;

    private String checkInTime;

    private String checkOutTime;

    private String arriveTime;

    private String latestArriveTime;

    private Integer roomNum;

//    private String arriveTime;
//
//    private String latestArrivalTime;

    private Double totalAmount;

    private Double totalBaseAmount;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 底价币种
     */
    private Integer baseCurrency;

    private List<OrderPriceItem> priceItems;

    /**
     * 费用明细(按间明细)
     */
    private List<OrderFeeDTO> feeList;

    private List<GuestInfo> guestInfos;

    /**
     * 订单每间房入住人信息
     */
    private List<OrderRoomGuestDTO> roomGuestDTOS;

    private String linkMan;

    private String linkPhone;

    private String email;

    private Integer invoiceModel;

    /**
     * 发票信息
     */
    private InvoiceInfo invoiceInfo;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 是否钟点房	Integer	1是0否 空代表否
     */
    private Integer hourlyRoom;

    /**
     * 钟点房日期对象
     */
    private HourlyRoomDetail hourlyRoomDetail;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种
     */
    private Integer payAtHotelCurrency;

    /**
     * 到店另付费用币种
     */
    private String payAtHotelCurrencyCode;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;

    /**
     * 罚金金额
     */
    private BigDecimal penaltiesValue;

    /**
     * 服务费
     */
    private BigDecimal serviceCharge;
}
