package com.tiangong.dto.order.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderFeeDetailDTO implements Serializable {

    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 含税售价
     */
    private BigDecimal salePrice;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 税费总额
     */
    private BigDecimal taxTotalAmount;

    /**
     * 按此税费总额
     */
    private BigDecimal timesTaxFee;

    /**
     * 税费明细
     */
    private TaxDTO taxList;

    /**
     * 按次税费明细
     */
    private TaxDTO timesTaxFeeList;

    /**
     * 折扣
     */
    private BigDecimal discount;


}
