package com.tiangong.dto.order.request;


import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class GuestInfo {

    /**
     * 客户姓名
     */
    @NotNull(message = "入住人姓名不能为空!")
    @Size(max = 64, message = "PARAM_MAX_64_GUESTNAME")
    private String guestName;

    /**
     * 英文-名
     */
    @Size(max = 64, message = "PARAM_MAX_64_FIRSTNAME")
    private String firstName;

    /**
     * 英文-姓
     */
    @Size(max = 64, message = "PARAM_MAX_64_LASTNAME")
    private String lastName;

    /**
     * 国籍
     */
    private Integer nationality;

    /**
     * 手机号码
     */
    private String mobileNo;

    /**
     * 手机号区号
     */
    private String countryCode;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 证件号码
     */
    private String idCardNo;

    /**
     * 证件类型
     */
    private Integer idCardType;

    /**
     * 房间序号
     */
    private String roomNumber;

    /**
     * 会员卡号
     */
    private String membershipCardNumber;

    /**
     * 华住需要 入住人是否获得积分 0否 1是
     */
    private Integer earnPoints;

}
