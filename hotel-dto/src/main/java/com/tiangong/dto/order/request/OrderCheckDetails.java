
package com.tiangong.dto.order.request;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderCheckDetails {

    /**
     * 入住日期
     */
    private String checkInDate;

    /**
     * 离店日期
     */
    private String checkOutDate;

    /**
     * 房间号
     */
//    private String roomNumber;

    /**
     * 入住状态
     * Unascertained-未确定;
     * Staying-在住;
     * Left-正常离店;
     * Early-提前离店;
     * NoShow-NoShow;
     */
    private String checkInState;

    /**
     * 房间附加费
     */
    private BigDecimal roomAdditionalCharges;

    /**
     * 入住人姓名
     */
    private List<GuestNameList> guestInfos;

    /**
     * 价格明细
     */
    private List<OrderCheckDetailPriceItem> priceItems;
}
