package com.tiangong.dto.order.request;

import com.tiangong.dto.common.BusinessRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class CreateInvoiceRequest extends BusinessRequest {
    /**
     * 房仓订单号
     */
    @NotNull(message = "EMPTY_PARAM_ORDERCODE")
    private String orderCode;

    /**
     * 发票类型(1普票2专票)
     */
    @NotNull(message = "EMPTY_PARAM_INVOICETYPE")
    private Integer invoiceTypeInteger;
    /**
     * 抬头类型 详情见InvoiceTitleTypeEnum
     */
    @NotNull(message = "抬头类型不能为空")
    private Integer invoiceTitleType;
    /**
     * 发票号
     */

    private String invoiceNO;
    /**
     * 抬头
     */
    @NotNull(message = "EMPTY_PARAM_INVOICEHEADER")
    private String invoiceHeader;
    /**
     * 名称:1房费2会务费3会议费4旅游费5差旅费6服务费
     */
    @NotNull(message = "EMPTY_PARAM_COSTNAMEINTEGER")
    private Integer costNameInteger;
    /**
     * 金额
     */
    @NotNull(message = "EMPTY_PARAM_AMOUNT")
    private BigDecimal amount;

    /************************************ 下面是专票信息  *****************************/

    /**
     * 专票公司电话
     */
    private String companyTel;
    /**
     * 专票公司税号
     */
    private String companyTax;
    /**
     * 专票公司公司注册地址
     */
    private String registerAddress;
    /**
     * 专票公司开户行
     */
    private String bank;
    /**
     * 专票银行账号
     */
    private String bankAccount;

    /************************************ 下面是取票信息  *****************************/
    /**
     * 取票方式：1自取2邮寄,3 电子发票
     */
    private Integer getMethod;
    /**
     * 收件人
     */
    private String receiveName;
    /**
     * 电话
     */
    private String telePhone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 地址
     */
    private String address;

    /**
     * 公司电话区号
     */
    private String companyTelAreaCode;

    /**
     * 电话区号
     */
    @NotEmpty(message = "联系人区号不能为空")
    private String telephoneAreaCode;

    /**
     * 发票备注
     */
    private String remark;
    /**
     * 开票申请单号id
     */
    private String applyId;
    /**
     * 订单类型
     */
    private String orderType;
    /**
     * 序号
     */
    private String sortNum;
    /**
     * 规格
     */
    private String specs;
    /**
     * 单位
     */
    private String perUnit;
    /**
     * 数量
     */
    private String countNum;
    /**
     * 税率
     */
    private String invoiceTaxRate;
    /**
     * 快递单号
     */
    private String trackingNo;
    /**
     * 抬头类型
     */
    private Integer invoiceHeaderType;
}
