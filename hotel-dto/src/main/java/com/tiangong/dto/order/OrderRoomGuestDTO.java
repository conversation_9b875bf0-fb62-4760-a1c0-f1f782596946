package com.tiangong.dto.order;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import java.util.List;

@Data
public class OrderRoomGuestDTO extends BaseDTO {
    private Integer id;
    private Integer orderId;
    /**
     * 房间号
     */
    private Integer roomNumber;

    /**
     * 入住成人数
     */
    private Integer adultQty;

    /**
     * 入住儿童数
     */
    private Integer childrenQty;

    /**
     * 入住儿童年龄
     */
    private String childrenAge;

    /**
     * 入住人信息
     */
    private List<OrderGuestDTO> orderGuestList;

}
