package com.tiangong.dto.order;

import com.tiangong.dto.common.BaseDTO;
import com.tiangong.dto.sensitive.Sensitive;
import com.tiangong.dto.sensitive.SensitiveTypeEnum;
import lombok.Data;

import javax.validation.constraints.Size;

@Data
public class OrderGuestDTO extends BaseDTO {

    private Integer id;

    private Integer orderId;

    /**
     * 客人姓名
     */
    @Size(max = 64, message = "PARAM_MAX_64_GUESTNAME")
    private String name;

    /**
     * 英文-名
     */
    @Size(max = 64, message = "PARAM_MAX_64_FIRSTNAME")
    private String firstName;

    /**
     * 英文-姓
     */
    @Size(max = 64, message = "PARAM_MAX_64_LASTNAME")
    private String lastName;

    /**
     * 国籍
     */
    private Integer nationality;

    /**
     * 手机号码
     */
    //@Sensitive(type = SensitiveTypeEnum.PHONE_NUM)
    private String mobileNo;

    /**
     * 手机号区号
     */
    private String countryCode;

    /**
     * 证件类型
     */
    private Integer idCardType;

    /**
     * 证件号
     */
    //@Sensitive(type = SensitiveTypeEnum.ID_NUM)
    private String idCardNo;

    /**
     * 会员卡号
     */
    private String membershipCardNumber;

    /**
     * 华住需要 入住人是否获得积分 0否 1是
     */
    private Integer earnPoints;

    /**
     * 房间序号
     */
    private Integer roomNumber;

    /**
     * 入住成人数
     */
    private Integer adultQty;

    /**
     * 入住儿童数
     */
    private Integer childrenQty;

    /**
     * 入住儿童年龄
     */
    private String childrenAge;

    /**
     * VIP等级
     */
    private Integer vipLevel;

}
