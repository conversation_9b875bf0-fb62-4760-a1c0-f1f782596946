spring:
  application:
    name: tiangong-report-server
  jackson:
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      #设置单个文件大小
      max-file-size: 100MB
      #设置单次请求文件的总大小
      max-request-size: 100MB
  profiles:
    active: pro

server:
  port: 14008

mybatis-plus:
  mapper-locations: classpath:/mapping/*.xml
  type-aliases-package: com.tiangong.*.domain,com.tiangong.*.*.domain.*
  global-config:
    db-config:
      id-type: auto
      field-strategy: 2
      refresh-mapper: true
      db-type: mysql

tiangong:
  security:
    switch: true # 是否开启security，true：开启，false：关闭
    secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZGE= # Base64编码
    tokentime: 20 # 令牌过期时间 此处单位/小时 ，默认2小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html

mapper:
  mappers: com.tiangong.dto.common.MyMapper
  not-empty: false
  identity: MYSQL

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

logstash:
  url: 1.116.28.125:5044

logging:
  level:
    com:
      alibaba:
        nacos: info





