<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.mapper.SupplierStatementMapper">
    <select id="queryUncheckOutSupplierList" parameterType="com.tiangong.domain.dto.QueryUncheckOutSupplierListDTO"
            resultType="com.tiangong.domain.dto.UncheckOutSupplierDTO">
        SELECT
        so.supplier_code supplierCode,
        oo.org_name supplierName,
        so.base_currency currency,
        sum(so.supply_order_amt - sf.paid_amt) payableAmt,
        count(1) orderQty
        FROM o_order o INNER JOIN o_supply_order so ON o.id=so.order_id
        INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id AND sf.finance_type = 0
        INNER JOIN t_org_organization oo on so.supplier_code=oo.org_code
        WHERE o.company_code=#{companyCode}
        AND so.settlement_type!=3
        AND so.confirmation_status IN (1,2,3)
        AND sf.check_status=1
        AND so.supply_order_amt - sf.paid_amt!=0
--         AND o.order_confirmation_status IN (1,2,3)
        <choose>
            <when test="dateQueryType==0">
                <if test="startDate!=null and startDate!=''">
                    AND o.created_dt >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
                </if>
            </when>
            <when test="dateQueryType==1">
                <if test="startDate!=null and startDate!=''">
                    AND so.start_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate!=null and startDate!=''">
                    AND so.end_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.end_date &lt;= #{endDate}
                </if>
            </when>
        </choose>
        <choose>
            <when test="supplierCode!=null and supplierCode!=''">
                AND so.supplier_code=#{supplierCode}
            </when>
            <otherwise>
                <if test="supplierName != null and supplierName != ''">
                    AND oo.org_name LIKE concat(concat('%',#{supplierName}),'%')
                </if>
            </otherwise>
        </choose>
        GROUP BY so.supplier_code,oo.org_name HAVING count(1)>0
        ORDER BY payableAmt DESC
    </select>
</mapper>