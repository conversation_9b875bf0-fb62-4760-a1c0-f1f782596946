<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.mapper.InvoiceMangeMapper">
    <resultMap id="QuerySupplierInvoiceBillItemListMap" type="com.tiangong.domain.resp.SupplierInvoiceBillItemExportResp">
        <result column="supplyOrderCode" property="supplyOrderCode"/>
        <result column="orderDt" property="orderDt"/>
        <result column="hotelName" property="hotelName"/>
        <result column="roomName" property="roomName"/>
        <result column="checkInDate" property="checkInDate"/>
        <result column="checkOutDate" property="checkOutDate"/>
        <result column="payMethod" property="payMethod"/>
        <result column="currency" property="currency"/>
        <result column="guest" property="guest"/>
        <result column="roomNumbers" property="roomNumbers"/>
        <result column="nightQty" property="nightQty"/>
        <result column="roomQty" property="roomQty"/>
        <result column="invoicePayableAmt" property="invoicePayableAmt"/>
        <result column="invoicedAmt" property="invoicedAmt"/>
        <result column="invoiceBillId" property="invoiceBillId"/>
        <result column="invoiceBillItemId" property="invoiceBillItemId"/>

        <!-- 入住人信息 -->
        <collection property="guests" select="com.tiangong.mapper.InvoiceMangeMapper.selectOrderGuests"
                    column="orderId" javaType="ArrayList" ofType="com.tiangong.order.remote.response.GuestDTO">
            <result column="name" property="name"/>
            <result column="roomNumber" property="roomNumber"/>
        </collection>
    </resultMap>

    <select id="queryInvoiceList" resultType="com.tiangong.domain.resp.SupplierInvoiceResponse">
        SELECT
        fsi.invoice_id invoiceId,
        fsi.invoice_type invoiceType,
        fsi.invoice_num invoiceNum,
        fsi.invoice_date invoiceDate,
        fsi.purchaser_name purchaserName,
        fsi.seller_name sellerName,
        fsi.invoice_status invoiceStatus,
        fsi.invoice_amt invoiceAmt,
        fsi.balance Balance,
        fsi.invoice_tax_rate invoiceTaxRate,
        fsi.remark ,
        fsi.url,
        fsi.created_by createdBy,
        fsi.created_dt createdDt,
        fsi.invoice_source_type invoiceSourceType
        FROM ${tableName} fsi
        <where>
            fsi.merchant_code = #{merchantCode}
            <if test="invoiceStartDt != null and invoiceStartDt != ''">
                AND fsi.invoice_date >= #{invoiceStartDt}
            </if>
            <if test="invoiceEndDt != null and invoiceEndDt != ''">
                <![CDATA[ AND fsi.invoice_date <= #{invoiceEndDt} ]]>
            </if>
            <if test="createdStartDt != null and createdStartDt != '' ">
                AND fsi.created_dt >= #{createdStartDt}
            </if>
            <if test="createdEndDt != null and createdEndDt != ''">
                <![CDATA[ AND fsi.created_dt <= #{createdEndDt} ]]>
            </if>
            <if test="invoiceNum != null and invoiceNum != ''">
                AND fsi.invoice_num like concat('%', #{invoiceNum}, '%')
            </if>
            <if test="purchaserName != null and purchaserName != ''">
                AND fsi.purchaser_name like concat('%', #{purchaserName}, '%')
            </if>
            <if test="sellerName != null and sellerName != ''">
                AND fsi.seller_name like concat('%', #{sellerName}, '%')
            </if>
            <if test="invoiceStatus != null ">
                AND fsi.invoice_status = #{invoiceStatus}
            </if>
            <if test="createdBy != null and createdBy != ''">
                AND fsi.created_by like concat('%', #{createdBy}, '%')
            </if>
        </where>
        ORDER BY fsi.created_dt DESC
    </select>

    <select id="querySupplierInvoiceBillItemList" resultMap="QuerySupplierInvoiceBillItemListMap"
            resultType="com.tiangong.domain.resp.SupplierInvoiceBillItemExportResp">
        SELECT
        oso.supply_order_code supplyOrderCode,
        oso.created_dt orderDt,
        oo.hotel_name hotelName,
        oso.room_name roomName,
        oso.start_date checkInDate,
        oso.end_date checkOutDate,
        oo.pay_method payMethod,
        oso.base_currency currency,
        oo.guest guest,
        oo.id orderId,
        oso.room_numbers roomNumbers,
        IF((oso.start_time is not null), 0.5, 0 + DATEDIFF(oso.end_date, oso.start_date)) nightQty,
        oso.room_qty roomQty,
        IFNULL( bi.invoice_payable_amt, 0 ) invoicePayableAmt,
        IFNULL( bi.invoice_amt, 0 ) invoicedAmt,
        bi.invoice_bill_id as invoiceBillId,
        bi.id invoiceBillItemId
        FROM
        o_supply_order oso
        LEFT JOIN o_order oo ON oo.id = oso.order_id
        LEFT JOIN f_supplier_invoice_bill_item bi ON oso.supply_order_code = bi.supply_order_code
        <where>
            <if test="invoiceBillId != null">
                AND bi.invoice_bill_id = #{invoiceBillId}
            </if>
        </where>
        ORDER BY oso.created_dt DESC
    </select>

    <select id="queryAgentInvoiceBillItemList"
            resultType="com.tiangong.domain.resp.SupplierInvoiceBillItemExportResp">
        SELECT
        oso.order_code orderCode,
        oso.created_dt orderDt,
        oso.hotel_name hotelName,
        oso.room_name roomName,
        oso.start_date checkInDate,
        oso.end_date checkOutDate,
        oso.pay_method payMethod,
        oso.sale_currency currency,
        oso.guest guest,
        IF( ( oso.hourly = 1 ), 0.5, 0+DATEDIFF ( oso.end_date, oso.start_date ) ) nightQty,
        oso.room_qty roomQty,
        IFNULL( oso.order_amt, 0 ) supplyOrderAmt,
        IFNULL( bi.invoice_payable_amt, 0 ) invoicePayableAmt,
        IFNULL( bi.invoice_amt, 0 ) invoicedAmt,
        IFNULL( bs.already_invoice_amt, 0 ) alreadyInvoiceAmt,
        IFNULL( oso.order_amt, 0 )- IFNULL( bs.already_invoice_amt, 0 ) uninvoicedAmt,
        bi.invoice_bill_id as invoiceBillId,
        bi.id invoiceBillItemId
        FROM o_order oso
        LEFT JOIN f_agent_invoice_bill_item bi ON oso.order_code = bi.order_code
        JOIN f_agent_invoice_bill_order_state bs ON oso.order_code = bs.order_code
        <where>
            <if test="invoiceBillId != null">
                AND bi.invoice_bill_id = #{invoiceBillId}
            </if>
        </where>
        ORDER BY oso.created_dt DESC
    </select>

    <select id="selectOrderGuests" resultType="com.tiangong.order.remote.response.GuestDTO">
        SELECT
            t.name as name,
            t.room_number as roomNumber
        FROM o_guest t WHERE t.order_id = #{orderId}
    </select>
</mapper>