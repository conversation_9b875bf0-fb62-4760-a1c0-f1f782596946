<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.mapper.StatisticsMapper">

    <select id="querySalesDetailStatistics" parameterType="com.tiangong.statistics.dto.QuerySaleStatisticsDTO"
            resultType="com.tiangong.statistics.dto.SalesDetailStatisticsDTO">
        SELECT
        o.id orderId,
        o.order_code orderCode,
        o.hotel_id hotelId,
        o.hotel_name hotelName,
        o.room_name roomName,
        o.product_name productName,
        o.created_dt createdDt,
        o.start_date startDate,
        o.end_date endDate,
        o.start_time startTime,
        o.end_time endTime,
        o.room_qty roomQty,
        o.hourly hourly,
        if(
        o.hourly=1,
        0.5* o.room_qty,
        0.0+timestampdiff( DAY, o.start_date, o.end_date ) * o.room_qty
        ) nightQty,
        o.guest guest,
        o.order_confirmation_status orderConfirmationStatus,
        o.settlement_type orderSettlementType,
        o.created_by createdBy,
        o.order_owner_name orderOwnerName,
        u1.user_name saleManagerName,
        o.channel_code agentType,
        o.agent_name agentName,
        o.agent_code agentCode,
        o.channel_order_code channelOrderCode,
        o.sale_currency saleCurrency,
        o.sale_rate saleRate,
        o.order_amt receivableAmt,
        o.product_label productLabel,
        f.received_amt receivedAmt,
        f.unreceived_amt unreceivedAmt,
        s.supply_order_code supplyOrderCode,
        s.room_name supplyOrderRoomName,
        s.product_name supplyOrderProductName,
        s.start_date supplyOrderStartDate,
        s.end_date supplyOrderEndDate,
        s.start_time supplyOrderStartTime,
        s.end_time supplyOrderEndTime,
        s.room_qty supplyOrderRoomQty,
        if(
        o.hourly=1,
        0.5* s.room_qty,
        0.0+timestampdiff( DAY, s.start_date, s.end_date ) * s.room_qty
        ) supplyOrderNightQty,
        o.guest supplyOrderGuest,
        IFNULL(s.confirmation_status, 0) supplyOrderConfirmationStatus,
        p.purchase_type purchaseType,
        s.settlement_type supplyOrderSettlementType,
        u1.user_name purchaseManagerName,
        s.supplier_name supplierName,
        s.supplier_code supplierCode,
        s.supplier_order_code supplierOrderCode,
        s.confirmation_code confirmationCode,
        IFNULL(s.reward_amt,0) rewardAmt,
        IFNULL(s.reward_amt*s.rate,0) rewardOrgCurrencyAmt,
        IFNULL(s.rebate_amt,0) rebateAmt,
        IFNULL(s.rebate_amt*s.rate,0) rebateOrgCurrencyAmt,
        IFNULL(s.base_currency,0) baseCurrency,
        IFNULL(s.rate,0) baseRate,
        IFNULL(s.supply_order_amt,0) payableAmt,
        IFNULL(sf.paid_amt,0) paidAmt,
        IFNULL(sf.unpaid_amt,0) unpaidAmt,
        IFNULL(s.supply_order_amt,0) equivalentOrgCurrencyAmt,
        o.profit profit,
        IFNULL(s.commission,0) commission,
        o.channel_name channelName,
        too.channel_code channelCode,
        o.pay_method payMethod,
        o.product_label_type productLabelType
        FROM
        o_order o
        LEFT JOIN o_supply_order s ON o.id = s.order_id
        LEFT JOIN o_order_finance f ON f.order_id = o.id
        LEFT JOIN o_supply_product p ON s.id = p.supply_order_id
        LEFT JOIN o_supply_order_finance sf ON sf.supply_order_id = s.id AND sf.finance_type = 0
        LEFT JOIN t_auth_user u1 ON u1.user_id =o.merchant_bm
        LEFT JOIN t_auth_user u2 ON u2.user_id =s.merchant_pm
        LEFT JOIN t_org_organization too ON too.org_code = o.agent_code
        WHERE
        o.company_code = #{companyCode}

        <choose>
            <when test="dateQueryType==1">
                <if test="startDate != null and startDate !=''">
                    AND o.start_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate != null and startDate !=''">
                    AND o.end_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.end_date &lt;= #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null and startDate !=''">
                    AND o.created_dt &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
                </if>
            </otherwise>
        </choose>
        <if test="payMethod!=null">
            AND o.pay_method = #{payMethod}
        </if>
        <if test="productLabel!=null">
            AND o.product_label = #{productLabel}
        </if>
        <if test="productLabelType!=null">
            AND o.product_label_type = #{productLabelType}
        </if>
        <if test="hotelId!=null and hotelId!=''">
            AND o.hotel_Id = #{hotelId}
        </if>
        <if test="orderCode!=null and orderCode!=''">
            AND o.order_code = #{orderCode}
        </if>
        <if test="channelCode!=null and channelCode!=''">
            AND EXISTS (
            SELECT * from t_org_organization too
            WHERE too.channel_code = #{channelCode} AND too.org_code = o.agent_code
            )
        </if>
        <if test="agentCode!=null and agentCode!=''">
            AND o.agent_code = #{agentCode}
        </if>
        <if test="orderConfirmationStatus!=null">
            AND o.order_confirmation_status =#{orderConfirmationStatus}
        </if>
        <if test="saleManagerId!=null and saleManagerId!=''">
            AND o.merchant_bm = #{saleManagerId}
        </if>
        <if test="orderOwnerName!=null and orderOwnerName!=''">
            AND o.order_owner_name=#{orderOwnerName}
        </if>
        <if test="orderReceivableType !=null and orderReceivableType == 0">
            AND o.order_amt > 0
        </if>
        <if test="orderReceivableType !=null and orderReceivableType == 1">
            AND o.order_amt = 0
        </if>
        <if test="supplierCode!=null and  supplierCode !=''">
            AND s.supplier_code =#{supplierCode}
        </if>
        <if test="supplyOrderConfirmationStatus!=null">
            AND s.confirmation_status =#{supplyOrderConfirmationStatus}
        </if>
        <if test="confirmationCode!=null and confirmationCode!=''">
            AND s.confirmation_code = #{confirmationCode}
        </if>
        <if test="purchaseManagerId!=null and purchaseManagerId!=''">
            AND s.merchant_pm = #{purchaseManagerId}
        </if>
        <if test="supplyOrderPayableType !=null and supplyOrderPayableType==0">
            AND s.supply_order_amt > 0
        </if>
        <if test="supplyOrderPayableType !=null and supplyOrderPayableType==1">
            AND s.supply_order_amt = 0
        </if>
        ORDER BY o.created_dt DESC
    </select>

    <select id="queryConfirmSupplyByOrderId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT supply_order_code
        FROM o_supply_order
        WHERE order_id = #{orderCode}
          AND confirmation_status = 1
        ORDER BY updated_dt ASC LIMIT 1
    </select>

    <select id="querySalesStatistics" parameterType="com.tiangong.statistics.dto.QuerySaleStatisticsDTO"
            resultType="com.tiangong.statistics.dto.SaleStatisticsDTO">
        select
        SUM(if(
        (c.hourly=1),
        0.5 * c.room_qty,
        0.0+timestampdiff( DAY, c.start_date, c.end_date ) * c.room_qty
        )) saleNightQty,
        SUM( c.order_amt ) saleAmt,
        SUM( c.unreceived_amt ) unreceivedAmt,
        SUM( IFNULL(c.order_amt,0) - c.supply_order_amt ) profit,
        SUM( c.profit ) / SUM( c.order_amt ) profitRate
        from (
        SELECT
        DISTINCT o.order_code,
        o.hourly,
        o.room_qty,
        o.start_date,
        o.end_date ,
        o.order_amt * o.sale_rate as order_amt,
        f.unreceived_amt * o.sale_rate as unreceived_amt,
        o.profit * o.sale_rate as profit,
        IFNULL(so.supply_order_amt * so.rate,0) supply_order_amt
        FROM o_order o
        LEFT JOIN o_order_finance f ON f.order_id = o.id
        LEFT JOIN o_supply_order so ON so.order_id = o.id
        LEFT JOIN o_supply_product p ON so.id=p.supply_order_id
        LEFT JOIN t_org_organization too ON too.org_code = o.agent_code
        WHERE
        o.company_code = #{companyCode}
        <choose>
            <when test="dateQueryType==1">
                <if test="startDate != null and startDate !=''">
                    AND o.start_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate != null and startDate !=''">
                    AND o.end_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.end_date &lt;= #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null and startDate !=''">
                    AND o.created_dt &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
                </if>
            </otherwise>
        </choose>


        <if test="payMethod!=null">
            AND o.pay_method = #{payMethod}
        </if>
        <if test="productLabel!=null">
            AND o.product_label = #{productLabel}
        </if>
        <if test="productLabelType!=null">
            AND o.product_label_type = #{productLabelType}
        </if>
        <if test="hotelId!=null and hotelId!=''">
            AND o.hotel_Id = #{hotelId}
        </if>
        <if test="orderCode!=null and orderCode!=''">
            AND o.order_code = #{orderCode}
        </if>
        <if test="channelCode!=null and channelCode!=''">
            AND EXISTS (
            SELECT * from t_org_organization too
            WHERE too.channel_code = #{channelCode} AND too.org_code = o.agent_code
            )
        </if>
        <if test="agentCode!=null and agentCode!=''">
            AND o.agent_code = #{agentCode}
        </if>
        <if test="orderConfirmationStatus!=null">
            AND o.order_confirmation_status =#{orderConfirmationStatus}
        </if>
        <if test="purchaseManagerId!=null and purchaseManagerId!=''">
            AND o.merchant_bm = #{purchaseManagerId}
        </if>
        <if test="orderOwnerName!=null and orderOwnerName!=''">
            AND o.order_owner_name=#{orderOwnerName}
        </if>
        <if test="orderReceivableType !=null and orderReceivableType == 0">
            AND o.order_amt > 0
        </if>
        <if test="orderReceivableType !=null and orderReceivableType == 1">
            AND o.order_amt = 0
        </if>
        <if test="supplierCode!=null and  supplierCode !=''">
            AND so.supplier_code =#{supplierCode}
        </if>
        <if test="supplyOrderConfirmationStatus!=null">
            AND so.confirmation_status =#{supplyOrderConfirmationStatus}
        </if>
        <if test="confirmationCode!=null and confirmationCode!=''">
            AND so.confirmation_code = #{confirmationCode}
        </if>
        <if test="saleManagerId!=null and saleManagerId!=''">
            AND so.merchant_pm = #{saleManagerId}
        </if>
        <if test="supplyOrderPayableType !=null and supplyOrderPayableType==0">
            AND so.supply_order_amt > 0
        </if>
        <if test="supplyOrderPayableType !=null and supplyOrderPayableType==1">
            AND so.supply_order_amt = 0
        </if>
        ) c
    </select>

    <select id="queryOrderProfitStatistics" parameterType="com.tiangong.statistics.dto.QueryOrderStatisticsDTO"
            resultType="com.tiangong.statistics.dto.OrderProfitStatisticsDTO">
        SELECT
        o.id orderId,
        o.order_code orderCode,
        o.channel_order_code channelOrderCode,
        o.hotel_name hotelName,
        o.room_name roomName,
        DATE_FORMAT(o.created_dt,'%Y-%m-%d %H:%i:%s') createdDt,
        o.start_date startDate,
        o.end_date endDate,
        o.hourly hourly,
        o.start_time startTime,
        o.end_time endTime,
        o.room_qty roomQty,
        if(
        (o.hourly=1),
        0.5 * o.room_qty,
        0.0+timestampdiff( DAY, o.start_date, o.end_date ) * o.room_qty
        ) nightQty,
        DATEDIFF( o.start_date, o.created_dt ) bookingDays,
        o.order_confirmation_status orderConfirmationStatus,
        o.order_amt orderTotalAmt,
        o.sale_rate saleRate,
        o.sale_currency saleCurrency,
        s.base_currency baseCurrency,
        s.rate baseRate,
        SUM( s.supply_order_amt) supplyOrderTotalAmt,
        IFNULL(SUM( s.reward_amt),0) rewardTotalAmt,
        IFNULL(SUM( s.reward_amt*s.rate),0) rewardTotalOrgCurrencyAmt,
        IFNULL(SUM( s.rebate_amt),0) rebateTotalAmt,
        IFNULL(SUM( s.rebate_amt*s.rate),0) rebateTotalOrgCurrencyAmt,
        MAX(o.profit) profit,
        o.order_owner_name orderOwnerName,
        o.channel_code agentType,
        o.agent_name agentName,
        p.area_name provinceName,
        a.area_name cityName,
        o.hotel_id hotelId,
        o.channel_name channelName,
        o.product_label productLabel,
        o.product_label_type productLabelType
        FROM o_order o
        LEFT JOIN o_supply_order s ON o.id = s.order_id
        LEFT JOIN t_baseinfo_areadata_zh_CN a ON o.city_code = a.area_code AND a.area_type = 3
        LEFT JOIN t_baseinfo_areadata_zh_CN p ON p.area_code = o.province_code AND p.area_type = 2
        LEFT JOIN t_org_organization too ON too.org_code = o.agent_code
        WHERE o.company_code = #{companyCode}
        -- AND o.pay_method != 1
        <choose>
            <when test="dateQueryType==1">
                <if test="startDate != null and startDate !=''">
                    AND o.start_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate != null and startDate !=''">
                    AND o.end_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.end_date &lt;= #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null and startDate !=''">
                    AND o.created_dt &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
                </if>
            </otherwise>
        </choose>
        <if test="productLabel!=null">
            AND o.product_label = #{productLabel}
        </if>
        <if test="productLabelType!=null">
            AND o.product_label_type = #{productLabelType}
        </if>
        <if test="hotelId!=null and hotelId!=''">
            AND o.hotel_Id = #{hotelId}
        </if>
        <if test="orderCode!=null and orderCode!=''">
            AND o.order_code = #{orderCode}
        </if>
        <if test="channelCode!=null and channelCode!=''">
            AND too.channel_code = #{channelCode}
        </if>
        <if test="agentCode != null and agentCode != ''">
            AND o.agent_code = #{agentCode}
        </if>
        <if test="orderConfirmationStatus != null">
            AND o.order_confirmation_status = #{orderConfirmationStatus}
        </if>
        <if test="orderConfirmationStatus == null">
            AND o.order_confirmation_status != 0
        </if>
        <if test="orderOwnerName!=null and orderOwnerName != ''">
            AND o.order_owner_name = #{orderOwnerName}
        </if>
        <if test="deficitStatus == 1">
            AND o.profit &lt; 0
        </if>
        <if test="deficitStatus == 0">
            AND o.profit &gt;= 0
        </if>
        GROUP BY o.order_code
        ORDER BY o.order_code DESC
    </select>

    <select id="queryOrderStatistics" parameterType="com.tiangong.statistics.dto.QueryOrderStatisticsDTO"
            resultType="com.tiangong.statistics.dto.OrderStatisticsDTO">
        SELECT
        SUM(if(
        a.hourly=1,
        0.5 * a.room_qty ,
        0.0+timestampdiff( DAY, a.start_date, a.end_date ) * a.room_qty
        ) )nightQty,
        SUM( a.order_amt ) orderTotalAmt,
        SUM( a.supply_order_amt ) supplyOrderTotalAmt,
        IFNULL(SUM( a.reward_amt ),0) rewardTotalAmt,
        IFNULL(SUM( a.rebate_amt ),0) rebateTotalAmt,
        SUM( IFNULL(a.order_amt,0) - a.supply_order_amt ) profit,
        SUM( a.profit ) / SUM( a.order_amt ) profitRate
        FROM ( SELECT
        o.order_amt * o.sale_rate as order_amt,
        SUM( s.supply_order_amt * s.rate ) supply_order_amt,
        SUM( s.reward_amt * s.rate ) reward_amt,
        SUM( s.rebate_amt * s.rate ) rebate_amt,
        o.profit * o.sale_rate as profit,
        o.hourly hourly,
        o.start_date start_date,
        o.end_date end_date,
        o.room_qty
        FROM o_order o
        LEFT JOIN o_supply_order s ON o.id = s.order_id
        LEFT JOIN t_org_organization too ON o.agent_code = too.org_code
        WHERE o.company_code = #{companyCode}
        -- AND o.pay_method != 1
        <choose>
            <when test="dateQueryType==1">
                <if test="startDate != null and startDate !=''">
                    AND o.start_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate != null and startDate !=''">
                    AND o.end_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.end_date &lt;= #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null and startDate !=''">
                    AND o.created_dt &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
                </if>
            </otherwise>
        </choose>

        <if test="productLabel!=null">
            AND o.product_label = #{productLabel}
        </if>
        <if test="productLabelType!=null">
            AND o.product_label_type = #{productLabelType}
        </if>
        <if test="hotelId!=null and hotelId!=''">
            AND o.hotel_Id = #{hotelId}
        </if>
        <if test="orderCode!=null and orderCode!=''">
            AND o.order_code = #{orderCode}
        </if>
        <if test="channelCode!=null and channelCode!=''">
            AND too.channel_code = #{channelCode}
        </if>
        <if test="agentCode!=null and agentCode!=''">
            AND o.agent_code = #{agentCode}
        </if>
        <if test="orderConfirmationStatus!=null">
            AND o.order_confirmation_status =#{orderConfirmationStatus}
        </if>
        <if test="orderConfirmationStatus == null">
            AND o.order_confirmation_status != 0
        </if>
        <if test="orderOwnerName!=null and orderOwnerName!=''">
            AND o.order_owner_name=#{orderOwnerName}
        </if>
        <if test="deficitStatus == 1">
            AND o.profit &lt; 0
        </if>
        <if test="deficitStatus == 0">
            AND o.profit &gt;= 0
        </if>
        GROUP BY o.order_code
        ) a
    </select>

</mapper>