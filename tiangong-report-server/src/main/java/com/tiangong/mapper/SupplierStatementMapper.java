package com.tiangong.mapper;


import com.tiangong.dto.common.MyMapper;
import com.tiangong.domain.dto.QueryUncheckOutSupplierListDTO;
import com.tiangong.domain.dto.UncheckOutSupplierDTO;
import com.tiangong.domain.po.SupplierStatementPO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SupplierStatementMapper extends MyMapper<SupplierStatementPO> {

    /**
     * 未出账查询
     */
    List<UncheckOutSupplierDTO> queryUncheckOutSupplierList(QueryUncheckOutSupplierListDTO request);

}