package com.tiangong.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.order.remote.request.OrderRefundTaskDetailDTO;
import com.tiangong.order.remote.request.QueryOrderRefundTaskDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @author: ziyi
 * @date: 2024/3/18 11:07
 * @description:
 */
@Repository
public interface OrderStatisticsMapper {

    /**
     * 查询订单退款通知列表
     * @param iPage
     * @param request
     * @return
     */
    IPage<OrderRefundTaskDetailDTO> queryOrderRefundTaskList(IPage<OrderRefundTaskDetailDTO> iPage, @Param("request") QueryOrderRefundTaskDTO request);

}
