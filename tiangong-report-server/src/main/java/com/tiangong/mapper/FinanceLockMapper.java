package com.tiangong.mapper;


import com.tiangong.domain.dto.FinanceLockSupplyOrderDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FinanceLockMapper {

    /**
     * 财务订单加锁
     */
    Integer lockSupplyOrder(FinanceLockSupplyOrderDTO request);

    /**
     * 检查供货单是否结算
     *
     * @return
     */
    Integer checkSupplyOrderCanLock(@Param("supplyOrderId") Integer supplyOrderId);
}
