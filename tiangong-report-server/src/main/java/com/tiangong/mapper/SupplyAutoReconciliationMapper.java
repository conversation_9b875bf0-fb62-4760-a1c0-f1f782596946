package com.tiangong.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.tiangong.domain.entity.SupplyAutoReconciliationEntity;
import org.springframework.stereotype.Repository;

import java.util.Collection;

/**
 * 自动对账
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:25
 * @Description:
 */
@Repository
public interface SupplyAutoReconciliationMapper extends BaseMapper<SupplyAutoReconciliationEntity> {

    Integer insertBatchSomeColumn(Collection<SupplyAutoReconciliationEntity> entityList);

}
