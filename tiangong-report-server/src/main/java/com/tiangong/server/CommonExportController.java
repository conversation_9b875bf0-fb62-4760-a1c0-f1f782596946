package com.tiangong.server;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.opencsv.CSVWriter;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.domain.entity.ExportReportEntity;
import com.tiangong.enums.ReportStatus;
import com.tiangong.mapper.ExportReportMapper;
import com.tiangong.report.request.BaseExport;
import com.tiangong.util.CsvUtils;
import com.tiangong.util.FileUpUtil;
import com.tiangong.util.GenerateExportCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.zip.ZipOutputStream;

/**
 * @author: zhiling
 * @date: 2024/3/19 20:40
 * @description:
 */

@RestController
@Slf4j
@RequestMapping(value = "/export")
public class CommonExportController {

    @Autowired
    private GenerateExportCode generateExportCode;

    @Autowired
    private ExportReportMapper exportReportMapper;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Autowired
    HttpServletRequest httpServletRequest;

    /**
     * 公共导出功能
     */
    @RequestMapping(value = "/commonExport", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response<String> commonExport(@RequestBody BaseExport baseExport) {
        LoginUser loginUser = TokenManager.getUser(httpServletRequest);

        String exportCode = generateExportCode.getExportCode(baseExport.getReportType());
//        String saveAddress = saveFileProperties.getSaveAddress() + exportCode + ".zip";
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(baseExport.getReportType().key)
                .exportStatus(ReportStatus.EXPORTING.key)
//                .fileUrl(saveAddress)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);
        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
//                int pageNum = 0; //文件数
                boolean hasMoreData = true;
                while (hasMoreData) {
                    // 文件名称
                    String csvFileName = baseExport.getFileName() + ".csv";
                    String zipEntryName = baseExport.getFileName() + ".csv";
                    CSVWriter csvWriter = new CSVWriter(new FileWriter(csvFileName));
                    csvWriter.writeNext(baseExport.getTableHeader());

                    // 每50w条存入一个csv文件，每次跑5w，防止内存溢出
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }

                        JSONArray exportList = baseExport.getExportList();
                        if (baseExport.getExportList().size() < 50000) {
                            hasMoreData = false;
                        }
                        if (baseExport.getExportList().isEmpty()){
                            log.info("导出数据为空,exportList:"+baseExport.getExportList());
                            break;
                        }

                        //表头
                        String[] header = baseExport.getTableHeader();
                        String[] info = new String[header.length];

                        int batchSize = baseExport.getPageSize();
                        int length = exportList.size();
                        // 计算总共需要切割的批次数,每次跑5w，防止内存溢出
                        int batchCount = (int) Math.ceil((double) length / batchSize);
                        for (int bi = 0; bi < batchCount; bi++) {
                            int startIdx = bi * batchSize;
                            int endIdx = Math.min(startIdx + batchSize, length);
                            for (int j = startIdx; j < endIdx; j++) {
                                Map<String, Object> json = (Map<String, Object>) exportList.get(j);
                                for (int m = 0; m < header.length; m++) {
                                    String fieldValue = "\t" + json.get(header[m]);
                                    info[m] = fieldValue;
                                }
                                csvWriter.writeNext(info);
                            }
                            csvWriter.flush();
                        }
                    }
                    // Csv文件压缩成Zip
                    CsvUtils.setZipByCsv(csvFileName, zipEntryName, zipOut);
                }
                zipOut.finish();
                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();
                    inputStream = new ByteArrayInputStream(bytes);
                    MultipartFile multipartFile = new MockMultipartFile("file", baseExport.getFileName() + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出文件fileUrl=" + fileUrl);

                    stopWatch.stop();
                    log.info("导出表格耗时情况:{}", stopWatch.prettyPrint());
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("导出文件错误 has error", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }
            } catch (Exception e) {
                log.error("导出文件异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出文件管理查看导出结果");
    }
}
