package com.tiangong.server;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.opencsv.CSVWriter;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.domain.entity.ExportReportEntity;
import com.tiangong.domain.req.SalesDetailReq;
import com.tiangong.domain.resp.*;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.mapper.ExportReportMapper;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.organization.remote.dto.CompanyAddDTO;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.ReportStatisticsService;
import com.tiangong.service.StatisticsService;
import com.tiangong.statistics.dto.SaleStatisticsDTO;
import com.tiangong.statistics.dto.*;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Date 2023/7/4 17:48
 * @Description:
 */
@RestController
@Slf4j
@RequestMapping(value = "/report")
public class StatisticsServer extends BaseController {

    @Autowired
    private GenerateExportCode generateExportCode;

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private ExportReportMapper exportReportMapper;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Autowired
    HttpServletRequest httpServletRequest;

    @Autowired
    private ReportStatisticsService reportStatisticsService;

    /**
     * 导出订单利润统计
     */
    @RequestMapping(value = "/statistics/exportOrderProfitStatistics", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response<String> exportOrderProfitImport(@RequestBody SalesDetailReq request) {
        LoginUser loginUser = TokenManager.getUser(httpServletRequest);
        String language = httpServletRequest.getHeader(HttpConstant.Language);
        String exportCode = generateExportCode.getExportCode(ReportType.REPORT_ORDER);
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.REPORT_ORDER.key)
                .exportStatus(ReportStatus.EXPORTING.key)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        // 获取商家编码
        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
        if (StrUtilX.isEmpty(currency)) {
            throw new SysException(ErrorCodeEnum.EXCHANGE_COIN_IS_NOT_ALREADY);
        }

        CompletableFuture.runAsync(() -> {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
                if (null != request.getDeficitStatus() && request.getDeficitStatus().equals(-1)) {
                    request.setDeficitStatus(null);
                }
                if (null != request.getChannelCode() && request.getChannelCode().equals("All")) {
                    request.setChannelCode(null);
                }
                if (null != request.getOrderConfirmationStatus() && request.getOrderConfirmationStatus().equals(-1)) {
                    request.setOrderConfirmationStatus(null);
                }
                request.setCompanyCode(loginUser.getCompanyCode());

                int pageNum = 0; //文件数
                boolean hasMoreData = true;

                while (hasMoreData) {
                    // 文件名称
                    String csvFileName = ReportConstants.EXPORT_PROFIT_STATISTICS_FILE_NAME + pageNum + ".csv";
                    String zipEntryName = ReportConstants.EXPORT_PROFIT_STATISTICS_FILE_NAME + pageNum + ".csv";

                    String[] EXPORT_PROFIT_STATISTICS_TABLE_HEADER = null;
                    // 判断语言
                    if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.en_US.getValue())) {
                        csvFileName = ReportConstants.EXPORT_PROFIT_STATISTICS_FILE_NAME_EN_US + pageNum + ".csv";
                        zipEntryName = ReportConstants.EXPORT_PROFIT_STATISTICS_FILE_NAME_EN_US + pageNum + ".csv";
                        EXPORT_PROFIT_STATISTICS_TABLE_HEADER = new String[ReportConstants.EXPORT_PROFIT_STATISTICS_TABLE_HEADER_EN_US.length];
                        // 替换币种描述
                        for (int i = 0; i < ReportConstants.EXPORT_PROFIT_STATISTICS_TABLE_HEADER_EN_US.length; i++) {
                            EXPORT_PROFIT_STATISTICS_TABLE_HEADER[i] = ReportConstants.EXPORT_PROFIT_STATISTICS_TABLE_HEADER_EN_US[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }

                    CSVWriter csvWriter = new CSVWriter(new FileWriter(csvFileName));

                    // 默认中文
                    if (EXPORT_PROFIT_STATISTICS_TABLE_HEADER == null) {
                        EXPORT_PROFIT_STATISTICS_TABLE_HEADER = new String[ReportConstants.EXPORT_PROFIT_STATISTICS_TABLE_HEADER.length];
                        // 替换币种描述
                        for (int i = 0; i < ReportConstants.EXPORT_PROFIT_STATISTICS_TABLE_HEADER.length; i++) {
                            EXPORT_PROFIT_STATISTICS_TABLE_HEADER[i] = ReportConstants.EXPORT_PROFIT_STATISTICS_TABLE_HEADER[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }
                    csvWriter.writeNext(EXPORT_PROFIT_STATISTICS_TABLE_HEADER);

                    // 每100w条存入一个csv文件，每次跑10w，防止内存溢出
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }
                        int num = pageNum * 10 + i; //页数
                        List<OrderReportDTO> reportDTOs = new ArrayList<>();

                        request.setCurrentPage(num);
                        request.setPageSize(50000);

                        PaginationSupportDTO<OrderReportResp> paginationSupportDTO = reportStatisticsService.querySalesDetailStatistics(request);
                        for (OrderReportResp orderReportResp : paginationSupportDTO.getItemList()) {
                            OrderReportDTO reportDTO = new OrderReportDTO();
                            BeanUtils.copyProperties(orderReportResp, reportDTO);
                            List<SuppleOrderReportDTO> reportList = new ArrayList<>();
                            for (SuppleOrderReportResp suppleOrderReportResp : orderReportResp.getSuppleOrderReportList()) {
                                SuppleOrderReportDTO suppleOrderReportDTO = new SuppleOrderReportDTO();
                                BeanUtils.copyProperties(suppleOrderReportResp, suppleOrderReportDTO);
                                reportList.add(suppleOrderReportDTO);
                            }
                            reportDTO.setSuppleOrderReportList(reportList);
                            reportDTOs.add(reportDTO);
                        }

                        if (CollUtilX.isNotEmpty(reportDTOs)) {
                            for (OrderReportDTO reportDTO : reportDTOs) {
                                if (reportDTO.getHourly() == 1) {
                                    reportDTO.setStartTime(reportDTO.getStartTime().substring(0, 16));
                                    reportDTO.setEndTime(reportDTO.getEndTime().substring(0, 16));
                                }
                                if (reportDTO.getOrderConfirmationStatus() != null) {
                                    reportDTO.setOrderConfirmationDesc(ConfirmationStatusEnum.getValueByKey(reportDTO.getOrderConfirmationStatus()));
                                }
                                if (reportDTO.getSupplierLabel() != null &&
                                        (reportDTO.getSupplierLabel().equals(LabelTypeEnum.PLATFORM.getNo()) ||
                                                reportDTO.getSupplierLabel().equals(LabelTypeEnum.GROUP_ROOM.getNo()) ||
                                                reportDTO.getSupplierLabel().equals(LabelTypeEnum.FIXED_PRICE_AGREEMENT.getNo()) ||
                                                reportDTO.getSupplierLabel().equals(LabelTypeEnum.DISCOUNT_PRICE_AGREEMENT.getNo()))
                                ) {
                                    reportDTO.setProductLabelDesc("是");
                                } else {
                                    reportDTO.setProductLabelDesc("否");
                                }
                                if (reportDTO.getProductLabelType() != null) {
                                    if (reportDTO.getProductLabelType() == 1) {
                                        reportDTO.setProductLabelTypeDesc("代结算");
                                    } else {
                                        reportDTO.setProductLabelTypeDesc("非代结算");
                                    }
                                }

                                if (reportDTO.getSaleCurrency() != null) {
                                    reportDTO.setSaleCurrencyDesc(SettlementCurrencyEnum.getCodeByKey(reportDTO.getSaleCurrency().toString()));
                                }

                                for (SuppleOrderReportDTO suppleOrderReportDTO : reportDTO.getSuppleOrderReportList()) {
                                    if (suppleOrderReportDTO.getBaseCurrency() != null) {
                                        suppleOrderReportDTO.setBaseCurrencyDesc(SettlementCurrencyEnum.getCodeByKey(suppleOrderReportDTO.getBaseCurrency().toString()));
                                    }
                                }

                                if (reportDTO.getHotelRank() != null) {
                                    reportDTO.setHotelRankDesc(HotelRankEnum.getDescBySourceNo(reportDTO.getHotelRank()));
                                }

                                if (reportDTO.getStartTime() != null){
                                    reportDTO.setStartTime(reportDTO.getStartTime()+"\t");
                                }

                                if (reportDTO.getEndTime() != null){
                                    reportDTO.setEndTime(reportDTO.getEndTime()+"\t");
                                }
                            }
                        }

                        if (reportDTOs.size() < 50000) {
                            hasMoreData = false;
                        }

                        // 将数据写入CSV文件
                        for (OrderReportDTO entity : reportDTOs) {
                            if (entity.getHourly() == 1) {
                                entity.setStartTime(entity.getStartTime().substring(0, 16));
                                entity.setEndTime(entity.getEndTime().substring(0, 16));
                            }
                            // 填充CSV行数据
                            String[] info = {entity.getOrderCode(), entity.getHotelName(), entity.getRoomName(), entity.getProductLabelDesc(), entity.getCreatedDt()+"\t",
                                    entity.getCheckInDate()+"\t", entity.getCheckOutDate()+"\t", entity.getStartTime(), entity.getEndTime(), entity.getRoomQty().toString(),
                                    entity.getNightQty().toString(), entity.getBookingDays().toString(), entity.getOrderConfirmationDesc(),
                                    entity.getSaleCurrencyDesc() + " " + entity.getReceivableAmt().toString(),
                                    entity.getReceivableOrgCurrencyAmt() + "",
                                    entity.getSuppleOrderTotalPayableOrgCurrencyAmt() + "",
                                    entity.getSupplierTotalRewardOrgCurrencyAmt() + "",
                                    entity.getSupplierTotalRebateOrgCurrencyAmt() + "",
                                    entity.getSupplierTotalPayableOrgCurrencyAmount() + "",
                                    entity.getProfitOrgCurrencyAmt() + "",
                                    entity.getOrderOwnerName(), entity.getAgentName(), entity.getProvinceName(), entity.getCityName(), entity.getHotelId().toString(),
                                    entity.getHotelRankDesc(), entity.getGroupName(), entity.getBrandName()};

                            csvWriter.writeNext(info);
                        }
                        csvWriter.flush();
                    }

                    pageNum++;
                    // Csv文件压缩成Zip
                    CsvUtils.setZipByCsv(csvFileName, zipEntryName, zipOut);
                }
                zipOut.finish();

                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();

                    inputStream = new ByteArrayInputStream(bytes);

                    MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出利润统计明细 fileUrl=" + fileUrl);

                    stopWatch.stop();
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("导出订单利润统计异常", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }
            } catch (Exception e) {
                log.error("导出订单利润统计异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }

    /**
     * 导出销售明细
     */
    @RequestMapping(value = "/statistics/exportSalesStatistics", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response<String> exportSalesStatisticsImport(@RequestBody SalesDetailReq request) {
        LoginUser loginUser = TokenManager.getUser(httpServletRequest);
        String language = httpServletRequest.getHeader(HttpConstant.Language);
        String exportCode = generateExportCode.getExportCode(ReportType.REPORT_SALE);
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.REPORT_SALE.key)
                .exportStatus(ReportStatus.EXPORTING.key)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        // 获取商家编码
        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
        if (StrUtilX.isEmpty(currency)) {
            throw new SysException(ErrorCodeEnum.EXCHANGE_COIN_IS_NOT_ALREADY);
        }

        CompletableFuture.runAsync(() -> {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
                int pageNum = 0; //文件数
                boolean hasMoreData = true;

                while (hasMoreData) {
                    // 文件名称
                    String csvFileName = ReportConstants.SALES_STATISTICS_FILE_NAME + pageNum + ".csv";
                    String zipEntryName = ReportConstants.SALES_STATISTICS_FILE_NAME + pageNum + ".csv";

                    String[] SALES_STATISTICS_TABLE_HEADER = null;
                    // 判断语言
                    if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.en_US.getValue())) {
                        csvFileName = ReportConstants.SALES_STATISTICS_FILE_NAME_EN_US + pageNum + ".csv";
                        zipEntryName = ReportConstants.SALES_STATISTICS_FILE_NAME_EN_US + pageNum + ".csv";
                        // 替换币种
                        SALES_STATISTICS_TABLE_HEADER = new String[ReportConstants.SALES_STATISTICS_TABLE_HEADER_EN_US.length];
                        for (int i = 0; i < ReportConstants.SALES_STATISTICS_TABLE_HEADER_EN_US.length; i++) {
                            SALES_STATISTICS_TABLE_HEADER[i] = ReportConstants.SALES_STATISTICS_TABLE_HEADER_EN_US[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }

                    CSVWriter csvWriter = new CSVWriter(new FileWriter(csvFileName));

                    // 默认中文
                    if (SALES_STATISTICS_TABLE_HEADER == null) {
                        // 替换币种
                        SALES_STATISTICS_TABLE_HEADER = new String[ReportConstants.SALES_STATISTICS_TABLE_HEADER.length];
                        for (int i = 0; i < ReportConstants.SALES_STATISTICS_TABLE_HEADER.length; i++) {
                            SALES_STATISTICS_TABLE_HEADER[i] = ReportConstants.SALES_STATISTICS_TABLE_HEADER[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }

                    csvWriter.writeNext(SALES_STATISTICS_TABLE_HEADER);

                    // 每100w条存入一个csv文件，每次跑10w，防止内存溢出
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }
                        int num = pageNum * 10 + i; //页数
                        List<OrderReportDTO> reportDTOs = new ArrayList<>();

                        request.setCurrentPage(num);
                        request.setPageSize(50000);
                        PaginationSupportDTO<OrderReportResp> paginationSupportDTO = reportStatisticsService.querySalesDetailStatistics(request);
                        Set<String> supplyCodes = new HashSet<>();
                        for (OrderReportResp orderReportResp : paginationSupportDTO.getItemList()) {
                            OrderReportDTO reportDTO = new OrderReportDTO();
                            BeanUtils.copyProperties(orderReportResp, reportDTO);
                            List<SuppleOrderReportDTO> reportList = new ArrayList<>();
                            for (SuppleOrderReportResp suppleOrderReportResp : orderReportResp.getSuppleOrderReportList()) {
                                SuppleOrderReportDTO suppleOrderReportDTO = new SuppleOrderReportDTO();
                                BeanUtils.copyProperties(suppleOrderReportResp, suppleOrderReportDTO);
                                reportList.add(suppleOrderReportDTO);

                                supplyCodes.add(suppleOrderReportResp.getSupplierCode());
                            }
                            reportDTO.setSuppleOrderReportList(reportList);
                            if (orderReportResp.getSupplierLabel() != null && orderReportResp.getSupplierLabel().equals(LabelTypeEnum.PLATFORM.getNo())) {
                                reportDTO.setProductLabel(1);
                            }
                            reportDTOs.add(reportDTO);
                        }

                        // 获取供应商配置信息
                        Map<String, SupplierAddDTO> supplierConfigMap = getSupplierConfig(new ArrayList<>(supplyCodes));

                        CompanyAddDTO companyAddDTO = new CompanyAddDTO();
                        companyAddDTO.setCompanyCode(loginUser.getCompanyCode());

                        if (CollUtilX.isNotEmpty(reportDTOs)) {
                            for (OrderReportDTO reportDTO : reportDTOs) {

                                if (reportDTO.getOrderConfirmationStatus() != null) {
                                    reportDTO.setOrderConfirmationDesc(ConfirmationStatusEnum.getValueByKey(reportDTO.getOrderConfirmationStatus()));
                                }

                                if (reportDTO.getOrderSettlementType() != null) {
                                    reportDTO.setOrderSettlementDesc(SettlementTypeEnum.getValueByKey(reportDTO.getOrderSettlementType()));
                                }

                                if (reportDTO.getSaleCurrency() != null) {
                                    reportDTO.setSaleCurrencyDesc(SettlementCurrencyEnum.getCodeByKey(reportDTO.getSaleCurrency().toString()));
                                }

                                for (SuppleOrderReportDTO suppleOrderReportDTO : reportDTO.getSuppleOrderReportList()) {
                                    if (suppleOrderReportDTO.getSupplyOrderConfirmationStatus() != null) {
                                        suppleOrderReportDTO.setSupplyOrderConfirmationStatusDesc(ConfirmationStatusEnum.getValueByKey(suppleOrderReportDTO.getSupplyOrderConfirmationStatus()));
                                    }

                                    if (suppleOrderReportDTO.getSupplyOrderSettlementType() != null) {
                                        suppleOrderReportDTO.setSupplyOrderSettlementTypeDesc(SettlementTypeEnum.getValueByKey(suppleOrderReportDTO.getSupplyOrderSettlementType()));
                                    }

                                    if (suppleOrderReportDTO.getBaseCurrency() != null) {
                                        suppleOrderReportDTO.setBaseCurrencyDesc(SettlementCurrencyEnum.getCodeByKey(suppleOrderReportDTO.getBaseCurrency().toString()));
                                    }

                                    if (suppleOrderReportDTO.getSupplyOrderStartTime() != null){
                                        suppleOrderReportDTO.setSupplyOrderStartTime(suppleOrderReportDTO.getSupplyOrderStartTime()+"\t");
                                    }

                                    if (suppleOrderReportDTO.getSupplyOrderEndTime() != null){
                                        suppleOrderReportDTO.setSupplyOrderEndTime(suppleOrderReportDTO.getSupplyOrderEndTime()+"\t");
                                    }

                                    // 获取供应商配置信息
                                    SupplierAddDTO supplierAddDTO = supplierConfigMap.get(suppleOrderReportDTO.getSupplierCode());
                                    if (supplierAddDTO != null) {
                                        suppleOrderReportDTO.setOneLevelChannelType(supplierAddDTO.getOneLevelChannelType());
                                        suppleOrderReportDTO.setTwoLevelChannelType(supplierAddDTO.getTwoLevelChannelType());
                                        suppleOrderReportDTO.setThreeLevelChannelType(supplierAddDTO.getThreeLevelChannelType());
                                    }
                                }

                                if (reportDTO.getSupplierLabel() != null &&
                                        (reportDTO.getSupplierLabel().equals(LabelTypeEnum.PLATFORM.getNo()) ||
                                            reportDTO.getSupplierLabel().equals(LabelTypeEnum.GROUP_ROOM.getNo()) ||
                                            reportDTO.getSupplierLabel().equals(LabelTypeEnum.FIXED_PRICE_AGREEMENT.getNo()) ||
                                            reportDTO.getSupplierLabel().equals(LabelTypeEnum.DISCOUNT_PRICE_AGREEMENT.getNo()))
                                ) {
                                    reportDTO.setProductLabelDesc("是");
                                } else {
                                    reportDTO.setProductLabelDesc("否");
                                }
                                if (reportDTO.getProductLabelType() != null) {
                                    if (reportDTO.getProductLabelType() == 1) {
                                        reportDTO.setProductLabelTypeDesc("代结算");
                                    } else {
                                        reportDTO.setProductLabelTypeDesc("非代结算");
                                    }
                                }
                                //支付类型（1-预付 2-到店付）
                                if (reportDTO.getPayMethod() != null && reportDTO.getPayMethod() == 0) {
                                    reportDTO.setPayMethodDesc("预付");
                                } else if (reportDTO.getPayMethod() != null && reportDTO.getPayMethod() == 1) {
                                    reportDTO.setPayMethodDesc("到店付");
                                }

                                if (reportDTO.getStartTime() != null){
                                    reportDTO.setStartTime(reportDTO.getStartTime()+"\t");
                                }

                                if (reportDTO.getEndTime() != null){
                                    reportDTO.setEndTime(reportDTO.getEndTime()+"\t");
                                }

                            }
                        }

                        if (reportDTOs.size() < 50000) {
                            hasMoreData = false;
                        }

                        // 订单编码集合
                        Set<String> orderCodes = new HashSet<>();
                        // 将数据写入CSV文件
                        for (OrderReportDTO entity : reportDTOs) {
                            for (SuppleOrderReportDTO suppleOrderReportDTO : entity.getSuppleOrderReportList()) {
                                if (entity.getHourly() == 1) {
                                    entity.setStartTime(entity.getStartTime().substring(0, 16));
                                    entity.setEndTime(entity.getEndTime().substring(0, 16));
                                    suppleOrderReportDTO.setSupplyOrderStartTime(suppleOrderReportDTO.getSupplyOrderStartTime().substring(0, 16));
                                    suppleOrderReportDTO.setSupplyOrderEndTime(suppleOrderReportDTO.getSupplyOrderEndTime().substring(0, 16));
                                }

                                String fullName = Stream.of(
                                                entity.getCountryName(),
                                                entity.getProvinceName(),
                                                entity.getCityName()
                                        )
                                        .filter(Objects::nonNull) // 过滤 null
                                        .filter(s -> !s.isEmpty()) // 过滤空字符串
                                        .collect(Collectors.joining("，")); // 用 ", " 连接非空元素

                                String orderSource = OrderSourceEnum.getDescByKey(entity.getOrderSource());

                                // 填充CSV行数据
                                String[] info = {entity.getOrderCode(), orderSource == null ? "--" : orderSource, entity.getPayMethodDesc(), entity.getHotelId() + "", entity.getHotelName(), StrUtilX.isEmpty(fullName)? "--" : fullName ,entity.getRoomName(), entity.getProductName(), entity.getProductLabelDesc(), entity.getCreatedDt() + "\t",
                                        entity.getCheckInDate() + "\t", entity.getStartTime() == null ? "--" : entity.getStartTime(), entity.getCheckOutDate() + "\t", entity.getEndTime() == null ? "--" : entity.getEndTime(),
                                        orderCodes.contains(entity.getOrderCode()) ? "" : entity.getRoomQty().toString(), orderCodes.contains(entity.getOrderCode()) ? "" : entity.getNightQty().toString(),
                                        entity.getGuest(), entity.getOrderConfirmationDesc(), entity.getOrderSettlementDesc(), entity.getCreatedBy(), entity.getOrderOwnerName() == null ? "--" : entity.getOrderOwnerName(),
                                        entity.getAgentType(), entity.getAgentName(), entity.getAgentCode(), entity.getChannelOrderCode(), entity.getSaleCurrencyDesc(),
                                        orderCodes.contains(entity.getOrderCode()) ? "" : entity.getReceivableAmt() + "",
                                        orderCodes.contains(entity.getOrderCode()) ? "" : entity.getReceivedAmt() + "",
                                        orderCodes.contains(entity.getOrderCode()) ? "" : entity.getUnreceivedAmt() + "",
                                        orderCodes.contains(entity.getOrderCode()) ? "" : entity.getReceivableOrgCurrencyAmt() + "",
                                        orderCodes.contains(entity.getOrderCode()) ? "" : entity.getSupplierTotalPayableOrgCurrencyAmount() + "",
                                        orderCodes.contains(entity.getOrderCode()) ? "" : entity.getProfitOrgCurrencyAmt() + "",
                                        suppleOrderReportDTO.getSupplyOrderCode().trim(),
                                        suppleOrderReportDTO.getSupplyOrderRoomName() == null ? "--" : suppleOrderReportDTO.getSupplyOrderRoomName().trim()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderProductName() == null ? "--" : suppleOrderReportDTO.getSupplyOrderProductName().trim()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderStartDate() == null ? "--" : suppleOrderReportDTO.getSupplyOrderStartDate().trim()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderEndDate() == null ? "--" : suppleOrderReportDTO.getSupplyOrderEndDate().trim()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderStartTime() == null ? "--" : suppleOrderReportDTO.getSupplyOrderStartTime().trim()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderEndTime() == null ? "--" : suppleOrderReportDTO.getSupplyOrderEndTime().trim()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderRoomQty() == null ? "--" : suppleOrderReportDTO.getSupplyOrderRoomQty()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderNightQty() == null ? "--" : suppleOrderReportDTO.getSupplyOrderNightQty()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderGuest().trim()+"\t",
                                        suppleOrderReportDTO.getSupplyOrderConfirmationStatusDesc().trim(),
                                        suppleOrderReportDTO.getSupplyOrderSettlementTypeDesc().trim(),
                                        suppleOrderReportDTO.getSupplierName().trim(),
                                        suppleOrderReportDTO.getSupplierCode().trim(),
                                        suppleOrderReportDTO.getOneLevelChannelType() == null ? "--" : SupplyOneLevelChannelTypeEnum.getDesc(suppleOrderReportDTO.getOneLevelChannelType())+"\t",
                                        suppleOrderReportDTO.getTwoLevelChannelType() == null ? "--" : SupplyTwoLevelChannelTypeEnum.getDesc(suppleOrderReportDTO.getTwoLevelChannelType())+"\t",
                                        suppleOrderReportDTO.getThreeLevelChannelType() == null ? "--" : suppleOrderReportDTO.getThreeLevelChannelType()+"\t",
                                        suppleOrderReportDTO.getSupplierOrderCode() == null ? "--" : suppleOrderReportDTO.getSupplierOrderCode().trim()+"\t",
                                        suppleOrderReportDTO.getConfirmationCode() == null ? "--" : suppleOrderReportDTO.getConfirmationCode().trim()+"\t",
                                        suppleOrderReportDTO.getBaseCurrencyDesc().trim(),
                                        suppleOrderReportDTO.getPayableAmt() + "",
                                        suppleOrderReportDTO.getSuppleOrderFinanceReportResp().getSupplyOrderPaidAmt() + "",
                                        suppleOrderReportDTO.getSuppleOrderFinanceReportResp().getSupplyOrderUnpaidAmt() + "",
                                        suppleOrderReportDTO.getPayableOrgCurrencyAmt() + "",
                                        suppleOrderReportDTO.getRewardAmt() + "",
                                        suppleOrderReportDTO.getRewardOrgCurrencyAmt() + "",
                                        suppleOrderReportDTO.getRebateAmt() + "",
                                        suppleOrderReportDTO.getRebateOrgCurrencyAmt() + ""};
                                csvWriter.writeNext(info);

                                // 记录订单编码
                                orderCodes.add(entity.getOrderCode());
                            }
                        }
                        csvWriter.flush();
                    }

                    pageNum++;
                    // Csv文件压缩成Zip
                    CsvUtils.setZipByCsv(csvFileName, zipEntryName, zipOut);
                }
                zipOut.finish();

                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();

                    inputStream = new ByteArrayInputStream(bytes);

                    MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出销售明细 fileUrl=" + fileUrl);

                    stopWatch.stop();
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("导出销售明细异常", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }
            } catch (Exception e) {
                log.error("导出销售明细异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }

    /**
     * 获取供应商配置信息
     */
    public Map<String, SupplierAddDTO> getSupplierConfig(List<String> supplyCodes) {
        Map<String, SupplierAddDTO> supplierMap = new HashMap<>();
        try {
            if (CollUtilX.isEmpty(supplyCodes)) {
                return supplierMap;
            }
            List<Object> supplierInfos = RedisTemplateX.hMultiGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplyCodes);
            if (CollUtilX.isEmpty(supplierInfos)) {
                return supplierMap;
            }
            for (Object supplierInfo : supplierInfos) {
                SupplierAddDTO supplierAddDTO = StrUtilX.parseObject(supplierInfo, SupplierAddDTO.class);
                if (supplierAddDTO == null || StrUtilX.isEmpty(supplierAddDTO.getSupplierCode())) {
                    continue;
                }
                supplierMap.put(supplierAddDTO.getSupplierCode(), supplierAddDTO);
            }
        } catch (Exception e) {
            log.error("getSupplierConfig error!", e);
        }
        return supplierMap;
    }

    /**
     * 导出应收报表
     */
    @RequestMapping(value = "/statistics/exportReceivableStatistics", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response<String> exportReceivableStatisticsImport(@RequestBody SalesDetailReq request) {
        LoginUser loginUser = TokenManager.getUser(httpServletRequest);
        String language = httpServletRequest.getHeader(HttpConstant.Language);
        String exportCode = generateExportCode.getExportCode(ReportType.REPORT_SALE);
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.RECEIVABLE_STATISTICS.key)
                .exportStatus(ReportStatus.EXPORTING.key)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        // 获取商家编码
        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
        // 获取币种
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
        if (StrUtilX.isEmpty(currency)) {
            throw new SysException(ErrorCodeEnum.EXCHANGE_COIN_IS_NOT_ALREADY);
        }

        CompletableFuture.runAsync(() -> {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
                int pageNum = 0; //文件数
                boolean hasMoreData = true;

                while (hasMoreData) {
                    // 文件名称
                    String xlsxFileName = ReportConstants.RECEIVABLE_STATISTICS_FILE_NAME + pageNum + ".xlsx";
                    String zipEntryName = ReportConstants.RECEIVABLE_STATISTICS_FILE_NAME + pageNum + ".xlsx";

                    // 创建工作簿
                    Workbook workbook = new XSSFWorkbook();

                    // 创建工作表
                    Sheet sheet = workbook.createSheet("Sheet1");

                    String[] RECEIVABLE_STATISTICS_TABLE_HEADER = null;
                    // 判断语言
                    if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.en_US.getValue())) {
                        xlsxFileName = ReportConstants.RECEIVABLE_STATISTICS_FILE_NAME_EN_US + pageNum + ".xlsx";
                        zipEntryName = ReportConstants.RECEIVABLE_STATISTICS_FILE_NAME_EN_US + pageNum + ".xlsx";
                        // 替换币种
                        RECEIVABLE_STATISTICS_TABLE_HEADER = new String[ReportConstants.RECEIVABLE_STATISTICS_TABLE_HEADER_EN_US.length];
                        for (int i = 0; i < ReportConstants.RECEIVABLE_STATISTICS_TABLE_HEADER_EN_US.length; i++) {
                            RECEIVABLE_STATISTICS_TABLE_HEADER[i] = ReportConstants.RECEIVABLE_STATISTICS_TABLE_HEADER_EN_US[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }
                    // 默认中文
                    if (RECEIVABLE_STATISTICS_TABLE_HEADER == null) {
                        // 替换币种
                        RECEIVABLE_STATISTICS_TABLE_HEADER = new String[ReportConstants.RECEIVABLE_STATISTICS_TABLE_HEADER.length];
                        for (int i = 0; i < ReportConstants.RECEIVABLE_STATISTICS_TABLE_HEADER.length; i++) {
                            RECEIVABLE_STATISTICS_TABLE_HEADER[i] = ReportConstants.RECEIVABLE_STATISTICS_TABLE_HEADER[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }

                    // 设置列宽度（假设每列宽度都为30个字符）
                    for (int i = 0; i < RECEIVABLE_STATISTICS_TABLE_HEADER.length; i++) {
                        sheet.setColumnWidth(i, 25 * 256);
                    }

                    // 创建行和单元格，并设置表头内容
                    Row headerRow = sheet.createRow(0);
                    for (int i = 0; i < RECEIVABLE_STATISTICS_TABLE_HEADER.length; i++) {
                        Cell cell = headerRow.createCell(i);
                        cell.setCellValue(RECEIVABLE_STATISTICS_TABLE_HEADER[i]);
                    }

                    // 每100w条存入一个csv文件，每次跑5w，防止内存溢出
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }
                        int num = pageNum * 10 + i; //页数
                        List<ReceivableStatisticsDTO> reportDTOs = new ArrayList<>();

                        request.setCurrentPage(num);
                        request.setPageSize(50000);
                        PaginationSupportDTO<ReceivableStatisticsDTO> paginationSupportDTO = reportStatisticsService.queryReceivableStatistics(request);
                        for (ReceivableStatisticsDTO statisticsDTO : paginationSupportDTO.getItemList()) {
                            ReceivableStatisticsDTO reportDTO = new ReceivableStatisticsDTO();
                            BeanUtils.copyProperties(statisticsDTO, reportDTO);
                            reportDTOs.add(reportDTO);
                        }

                        if (CollUtilX.isNotEmpty(reportDTOs)) {
                            for (ReceivableStatisticsDTO reportDTO : reportDTOs) {
                                if (reportDTO.getSaleCurrency() != null) {
                                    reportDTO.setSaleCurrencyDesc(SettlementCurrencyEnum.getCodeByKey(reportDTO.getSaleCurrency().toString()));
                                }
                                if (reportDTO.getOrderSettlementType() != null) {
                                    reportDTO.setOrderSettlementDesc(SettlementTypeEnum.getValueByKey(reportDTO.getOrderSettlementType()));
                                }
                            }
                        }

                        if (reportDTOs.size() < 50000) {
                            hasMoreData = false;
                        }

                        // 将数据写入Excel文件
                        int rowIndex = (num - 1) * 50000 + 1;
                        for (ReceivableStatisticsDTO entity : reportDTOs) {
                            Row dataRow = sheet.createRow(rowIndex++);

                            // 填充CSV行数据
                            String[] info = {entity.getAgentName(),
                                    entity.getAgentCode(),
                                    entity.getAgentManagerName(),
                                    entity.getOrderSettlementDesc(),
                                    entity.getNightQty().toString(),
                                    entity.getSaleCurrencyDesc(),
                                    entity.getReceivableAmt().toString(),
                                    entity.getReceivedAmt().toString(),
                                    entity.getUnreceivedAmt().toString(),
                                    entity.getReceivableOrgCurrencyAmt().toString(),
                                    entity.getSuppleOrderPayableOrgCurrencyAmt().toString(),
                                    entity.getProfitOrgCurrencyAmt().toString()};

                            for (int k = 0; k < info.length; k++) {
                                Cell cell = dataRow.createCell(k);
                                cell.setCellValue(info[k]);

                                // 创建单元格样式并设置自动换行
                                CellStyle style = workbook.createCellStyle();
                                style.setWrapText(true);
                                cell.setCellStyle(style);
                            }
                        }
                    }

                    pageNum++;
                    // 导出工作簿为XLSX文件
                    try (FileOutputStream fileOut = new FileOutputStream(xlsxFileName)) {
                        workbook.write(fileOut);
                    } catch (IOException e) {
                        log.error("导出应收报表异常", e);
                    }

                    // Excel文件压缩成Zip
                    try (FileInputStream fileIn = new FileInputStream(xlsxFileName)) {
                        ZipEntry zipEntry = new ZipEntry(zipEntryName);
                        zipOut.putNextEntry(zipEntry);

                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = fileIn.read(buffer)) != -1) {
                            zipOut.write(buffer, 0, bytesRead);
                        }

                        zipOut.closeEntry();
                    } catch (IOException e) {
                        log.error("导出应收报表异常", e);
                    }
                }
                zipOut.finish();
                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();

                    inputStream = new ByteArrayInputStream(bytes);

                    MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出应收报表 fileUrl=" + fileUrl);

                    stopWatch.stop();
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("导出应收报表异常", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }
            } catch (Exception e) {
                log.error("导出应收报表异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }

    /**
     * 应付报表导出
     */
    @RequestMapping(value = "/statistics/exportPayableStatistics", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response<String> exportPayableStatisticsImport(@RequestBody SalesDetailReq request) {
        LoginUser loginUser = TokenManager.getUser(httpServletRequest);
        String language = httpServletRequest.getHeader(HttpConstant.Language);
        String exportCode = generateExportCode.getExportCode(ReportType.REPORT_SALE);
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.PAYABLE_STATISTICS.key)
                .exportStatus(ReportStatus.EXPORTING.key)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        // 获取商家编码
        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
        if (StrUtilX.isEmpty(currency)) {
            throw new SysException(ErrorCodeEnum.EXCHANGE_COIN_IS_NOT_ALREADY);
        }

        CompletableFuture.runAsync(() -> {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
                int pageNum = 0; //文件数
                boolean hasMoreData = true;

                while (hasMoreData) {

                    // 创建工作簿
                    Workbook workbook = new XSSFWorkbook();

                    // 创建工作表
                    Sheet sheet = workbook.createSheet("Sheet1");

                    // 文件名称
                    String xlsxFileName = ReportConstants.PAYABLE_STATISTICS_FILE_NAME + pageNum + ".xlsx";
                    String zipEntryName = ReportConstants.PAYABLE_STATISTICS_FILE_NAME + pageNum + ".xlsx";

                    String[] PAYABLE_STATISTICS_TABLE_HEADER = null;
                    // 判断语言
                    if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.en_US.getValue())) {
                        xlsxFileName = ReportConstants.PAYABLE_STATISTICS_FILE_NAME_EN_US + pageNum + ".xlsx";
                        zipEntryName = ReportConstants.PAYABLE_STATISTICS_FILE_NAME_EN_US + pageNum + ".xlsx";
                        // 替换币种
                        PAYABLE_STATISTICS_TABLE_HEADER = new String[ReportConstants.PAYABLE_STATISTICS_TABLE_HEADER_EN_US.length];
                        for (int i = 0; i < ReportConstants.PAYABLE_STATISTICS_TABLE_HEADER_EN_US.length; i++) {
                            PAYABLE_STATISTICS_TABLE_HEADER[i] = ReportConstants.PAYABLE_STATISTICS_TABLE_HEADER_EN_US[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }
                    // 默认中文
                    if (PAYABLE_STATISTICS_TABLE_HEADER == null) {
                        // 替换币种
                        PAYABLE_STATISTICS_TABLE_HEADER = new String[ReportConstants.PAYABLE_STATISTICS_TABLE_HEADER.length];
                        for (int i = 0; i < ReportConstants.PAYABLE_STATISTICS_TABLE_HEADER.length; i++) {
                            PAYABLE_STATISTICS_TABLE_HEADER[i] = ReportConstants.PAYABLE_STATISTICS_TABLE_HEADER[i].replaceAll(SettlementCurrencyEnum.CNY.code, currency);
                        }
                    }

                    // 设置列宽度（假设每列宽度都为30个字符）
                    for (int i = 0; i < PAYABLE_STATISTICS_TABLE_HEADER.length; i++) {
                        sheet.setColumnWidth(i, 25 * 256);
                    }

                    // 创建行和单元格，并设置表头内容
                    Row headerRow = sheet.createRow(0);
                    for (int i = 0; i < PAYABLE_STATISTICS_TABLE_HEADER.length; i++) {
                        Cell cell = headerRow.createCell(i);
                        cell.setCellValue(PAYABLE_STATISTICS_TABLE_HEADER[i]);
                    }

                    // 每100w条存入一个csv文件，每次跑5w，防止内存溢出
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }
                        int num = pageNum * 10 + i; //页数
                        List<PayableStatisticsResp> reportDTOs = new ArrayList<>();

                        request.setCurrentPage(num);
                        request.setPageSize(50000);
                        PaginationSupportDTO<PayableStatisticsResp> paginationSupportDTO = reportStatisticsService.queryPayableStatistics(request);
                        for (PayableStatisticsResp payableStatisticsResp : paginationSupportDTO.getItemList()) {
                            PayableStatisticsResp reportDTO = new PayableStatisticsResp();
                            BeanUtils.copyProperties(payableStatisticsResp, reportDTO);
                            reportDTOs.add(reportDTO);
                        }

                        CompanyAddDTO companyAddDTO = new CompanyAddDTO();
                        companyAddDTO.setCompanyCode(loginUser.getCompanyCode());

                        if (reportDTOs.size() < 50000) {
                            hasMoreData = false;
                        }

                        // 将数据写入CSV文件
                        int rowIndex = (num - 1) * 50000 + 1;
                        for (PayableStatisticsResp entity : reportDTOs) {
                            Row dataRow = sheet.createRow(rowIndex++);

                            // 填充CSV行数据
                            String[] info = {entity.getSupplierName(),
                                    entity.getSupplierCode(),
                                    entity.getSupplyOrderNightQty().toString(),
                                    entity.getSupplierOrderPayableOrgCurrencyAmt(),
                                    entity.getSupplierOrderPaidOrgCurrencyAmt(),
                                    entity.getSupplierOrderUnpaidOrgCurrencyAmt()};

                            for (int k = 0; k < info.length; k++) {
                                Cell cell = dataRow.createCell(k);
                                cell.setCellValue(info[k]);

                                // 创建单元格样式并设置自动换行
                                CellStyle style = workbook.createCellStyle();
                                style.setWrapText(true);
                                cell.setCellStyle(style);
                            }
                        }
                    }

                    pageNum++;

                    // 导出工作簿为XLSX文件
                    try (FileOutputStream fileOut = new FileOutputStream(xlsxFileName)) {
                        workbook.write(fileOut);
                    } catch (IOException e) {
                        log.error("应付报表导出异常", e);
                    }

                    // Excel文件压缩成Zip
                    try (FileInputStream fileIn = new FileInputStream(xlsxFileName)) {
                        ZipEntry zipEntry = new ZipEntry(zipEntryName);
                        zipOut.putNextEntry(zipEntry);

                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = fileIn.read(buffer)) != -1) {
                            zipOut.write(buffer, 0, bytesRead);
                        }

                        zipOut.closeEntry();
                    } catch (IOException e) {
                        log.error("应付报表导出异常", e);
                    }
                }
                zipOut.finish();
                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();

                    inputStream = new ByteArrayInputStream(bytes);

                    MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出应付报表 fileUrl=" + fileUrl);

                    stopWatch.stop();
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("应付报表导出异常", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }
            } catch (Exception e) {
                log.error("应付报表导出异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }

    /**
     * 订单利润表
     */
    @PostMapping(value = "/statistics/queryOrderProfitStatistics", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<PaginationSupportDTO<OrderProfitStatisticsDTO>> queryOrderProfitStatistics(@RequestBody QueryOrderStatisticsDTO request) {
        if (null != request.getDeficitStatus() && request.getDeficitStatus().equals(-1)) {
            request.setDeficitStatus(null);
        }

        if (null != request.getChannelCode() && request.getChannelCode().equals("All")) {
            request.setChannelCode(null);
        }

        if (null != request.getOrderConfirmationStatus() && request.getOrderConfirmationStatus().equals(-1)) {
            request.setOrderConfirmationStatus(null);
        }

        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(super.getCompanyCode());
        }

        PaginationSupportDTO<OrderProfitStatisticsDTO> orderStatistics = null;
        // 如果是查询待确认状态直接返回空
        if (null == request.getOrderConfirmationStatus() || !request.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key)) {
            orderStatistics = statisticsService.queryOrderProfitStatistics(request);
        }
        return Response.success(orderStatistics);
    }

    /**
     * 销售明细统计
     */
    @PostMapping(value = "/statistics/querySalesDetailStatistics", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<PaginationSupportDTO<SalesDetailStatisticsDTO>> querySalesDetailStatistics(@RequestBody QuerySaleStatisticsDTO request) {
        if (null != request.getChannelCode() && request.getChannelCode().equals("All")) {
            request.setChannelCode(null);
        }

        if (null != request.getOrderConfirmationStatus() && request.getOrderConfirmationStatus().equals(-1)) {
            request.setOrderConfirmationStatus(null);
        }

        if (null != request.getPurchaseManagerId() && request.getPurchaseManagerId().equals(-1)) {
            request.setPurchaseManagerId(null);
        }

        if (null != request.getSaleManagerId() && request.getSaleManagerId().equals(-1)) {
            request.setSaleManagerId(null);
        }

        if (null != request.getSupplyOrderConfirmationStatus() && request.getSupplyOrderConfirmationStatus().equals(-1)) {
            request.setSupplyOrderConfirmationStatus(null);
        }

        if (null != request.getSupplyOrderPayableType() && request.getSupplyOrderPayableType().equals(-1)) {
            request.setSupplyOrderPayableType(null);
        }

        if (null != request.getOrderReceivableType() && request.getOrderReceivableType().equals(-1)) {
            request.setOrderReceivableType(null);
        }

        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(super.getCompanyCode());
        }

        return Response.success(statisticsService.querySalesDetailStatistics(request));
    }

    /**
     * 销售统计
     */
    @PostMapping(value = "/statistics/querySalesStatistics", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<SaleStatisticsDTO> querySalesStatistics(@RequestBody QuerySaleStatisticsDTO request) {
        if (null != request.getChannelCode() && request.getChannelCode().equals("All")) {
            request.setChannelCode(null);
        }

        if (null != request.getOrderConfirmationStatus() && request.getOrderConfirmationStatus().equals(-1)) {
            request.setOrderConfirmationStatus(null);
        }

        if (null != request.getPurchaseManagerId() && request.getPurchaseManagerId().equals(-1)) {
            request.setPurchaseManagerId(null);
        }

        if (null != request.getSaleManagerId() && request.getSaleManagerId().equals(-1)) {
            request.setPurchaseManagerId(null);
        }

        if (null != request.getSupplyOrderConfirmationStatus() && request.getSupplyOrderConfirmationStatus().equals(-1)) {
            request.setSupplyOrderConfirmationStatus(null);
        }

        if (null != request.getSupplyOrderPayableType() && request.getSupplyOrderPayableType().equals(-1)) {
            request.setSupplyOrderPayableType(null);
        }

        if (null != request.getOrderReceivableType() && request.getOrderReceivableType().equals(-1)) {
            request.setOrderReceivableType(null);
        }

        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(super.getCompanyCode());
        }

        return Response.success(statisticsService.querySalesStatistics(request));
    }

    /**
     * 订单利润统计
     */
    @PostMapping(value = "/statistics/queryOrderStatistics", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<OrderStatisticsDTO> queryOrderStatistics(@RequestBody QueryOrderStatisticsDTO request) {
        if (null != request.getDeficitStatus() && request.getDeficitStatus().equals(-1)) {
            request.setDeficitStatus(null);
        }

        if (null != request.getChannelCode() && request.getChannelCode().equals("All")) {
            request.setChannelCode(null);
        }

        if (null != request.getOrderConfirmationStatus() && request.getOrderConfirmationStatus().equals(-1)) {
            request.setOrderConfirmationStatus(null);
        }

        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(super.getCompanyCode());
        }

        OrderStatisticsDTO orderStatisticsDTO = null;
        // 如果是查询待确认状态直接返回空
        if (null == request.getOrderConfirmationStatus() || !request.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key)) {
            orderStatisticsDTO = statisticsService.queryOrderStatistics(request);
        }
        return Response.success(orderStatisticsDTO);
    }

    /**
     * 上传文件,返回文路径
     */
    @PostMapping("/upload")
    public Response<String> upload(MultipartFile file) {
        String fileSuffixName = "jiali_tiangong/template/" + file.getOriginalFilename();
        String fileUrl = fileUpUtil.uploadFile2(file, fileSuffixName);
        return Response.success(fileUrl);
    }
}
