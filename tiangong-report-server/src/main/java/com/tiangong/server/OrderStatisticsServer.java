package com.tiangong.server;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.opencsv.CSVWriter;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.domain.entity.ExportReportEntity;
import com.tiangong.domain.resp.OrderRefundTaskResp;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.keys.RedisKey;
import com.tiangong.mapper.ExportReportMapper;
import com.tiangong.order.remote.request.OrderRefundTaskDetailDTO;
import com.tiangong.order.remote.request.QueryOrderRefundTaskDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.OrderStatisticsService;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Date 2024/3/18
 * @Description:
 */
@RestController
@Slf4j
@RequestMapping(value = "/report/order")
public class OrderStatisticsServer extends BaseController {

    @Autowired
    private GenerateExportCode generateExportCode;

    @Autowired
    private ExportReportMapper exportReportMapper;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Autowired
    HttpServletRequest httpServletRequest;

    @Autowired
    private OrderStatisticsService orderStatisticsService;

    @RequestMapping(value = "/exportOrderRefundTask", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response exportOrderProfitImport(@RequestBody QueryOrderRefundTaskDTO request){
        log.info("exportOrderRefundTask:" + request);

        LoginUser loginUser = TokenManager.getUser(httpServletRequest);
        String language = httpServletRequest.getHeader(HttpConstant.Language);

        String exportCode = generateExportCode.getExportCode(ReportType.ORDER_REFUND_TASK);
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.ORDER_REFUND_TASK.key)
                .exportStatus(ReportStatus.EXPORTING.key)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
                int pageNum = 0; //文件数
                boolean hasMoreData = true;

                while (hasMoreData) {
                    // 文件名称
                    String csvFileName = ReportConstants.EXPORT_REFUND_TASK_FILE_NAME + pageNum + ".csv";
                    String zipEntryName = ReportConstants.EXPORT_REFUND_TASK_FILE_NAME + pageNum + ".csv";


                    String[] EXPORT_REFUND_TASK_TABLE_HEADER = ReportConstants.EXPORT_REFUND_TASK_TABLE_HEADER;
                    // 判断语言
                    if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.en_US.getValue())) {
                        csvFileName = ReportConstants.EXPORT_REFUND_TASK_FILE_NAME_EN_US + pageNum + ".csv";
                        zipEntryName = ReportConstants.EXPORT_REFUND_TASK_FILE_NAME_EN_US + pageNum + ".csv";
                        EXPORT_REFUND_TASK_TABLE_HEADER = ReportConstants.EXPORT_REFUND_TASK_TABLE_HEADER_EN_US;
                    }
                    CSVWriter csvWriter = new CSVWriter(new FileWriter(csvFileName));
                    csvWriter.writeNext(EXPORT_REFUND_TASK_TABLE_HEADER);

                    // 每100w条存入一个csv文件，每次跑10w，防止内存溢出
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }
                        int num = pageNum * 10 + i; //页数
                        List<OrderRefundTaskResp> reportDTOs = new ArrayList<>();

                        request.setCurrentPage(num);
                        request.setPageSize(50000);

                        PaginationSupportDTO<OrderRefundTaskDetailDTO> response = orderStatisticsService.queryOrderRefundTaskList(request);
                        for (OrderRefundTaskDetailDTO detailDTO : response.getItemList()) {
                            OrderRefundTaskResp reportDTO = new OrderRefundTaskResp();
                            reportDTO.setTaskCode(detailDTO.getTaskCode());
                            reportDTO.setOrderCode(detailDTO.getOrderCode());
                            reportDTO.setCreatedBy(detailDTO.getCreatedBy());
                            reportDTO.setRefundAmount(detailDTO.getRefundAmount());
                            reportDTO.setChannelOrderCode(detailDTO.getChannelOrderCode());
                            reportDTO.setRefundType(OrderRefundTypeEnum.getDesc(detailDTO.getRefundType()));
                            reportDTO.setRefundState(OrderRefundStateEnum.getValue(detailDTO.getRefundState()));
                            reportDTO.setCreatedDt(DateUtilX.dateToString(detailDTO.getCreatedDt(), DateUtilX.hour_format));
                            reportDTO.setCurrency(SettlementCurrencyEnum.getCodeByKey(detailDTO.getCurrency().toString()));
                            reportDTO.setRefundTaskType(OrderRefundTaskTypeEnum.getValueByKey(detailDTO.getRefundTaskType()));
                            reportDTOs.add(reportDTO);
                        }

                        if (reportDTOs.size() < 50000) {
                            hasMoreData = false;
                        }

                        // 将数据写入CSV文件
                        for (OrderRefundTaskResp entity : reportDTOs) {
                            // 填充CSV行数据
                            String[] info = {entity.getTaskCode(),
                                    entity.getRefundTaskType(),
                                    entity.getRefundState(),
                                    entity.getCurrency() + " " + entity.getRefundAmount().toString(),
                                    entity.getRefundType(),
                                    entity.getOrderCode(),
                                    entity.getChannelOrderCode(),
                                    entity.getCreatedDt(),
                                    entity.getCreatedBy()
                            };

                            csvWriter.writeNext(info);
                        }

                        csvWriter.flush();
                    }

                    pageNum++;
                    // Csv文件压缩成Zip
                    CsvUtils.setZipByCsv(csvFileName, zipEntryName, zipOut);
                }
                zipOut.finish();

                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();

                    inputStream = new ByteArrayInputStream(bytes);

                    MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出在线退款通知明细 fileUrl=" + fileUrl);

                    stopWatch.stop();
                    log.info("导出在线退款通知明细耗时情况:{}", stopWatch.prettyPrint());
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("exportOrderRefundTask has error", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }
            } catch (Exception e) {
                log.error("导出在线退款通知明细异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }

}
