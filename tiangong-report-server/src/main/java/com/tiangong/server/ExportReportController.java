package com.tiangong.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.Response;
import com.tiangong.domain.req.DownloadReportReq;
import com.tiangong.domain.req.ExportReportDelReq;
import com.tiangong.domain.req.ExportReportPageReq;
import com.tiangong.service.ExportReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2023/7/6 11:11
 * @Description:
 */
@RestController
@RequestMapping("/report/exportReport")
public class ExportReportController {

    @Autowired
    private ExportReportService exportReportService;

    /**
     * 导出报表列表（分页）
     */
    @PostMapping("/exportReportPage")
    public Response<PageVo> findPage(@RequestBody ExportReportPageReq req) {
        return Response.success(exportReportService.exportReportPage(req));
    }

    /**
     * 导出报表删除
     */
    @PostMapping("/exportReportDel")
    public Response<Object> del(@RequestBody ExportReportDelReq req) {
        exportReportService.exportReportDel(req);
        return Response.success();
    }

    /**
     * 下载报表
     */
    @PostMapping("/downloadReport")
    public void downloadReport(@RequestBody DownloadReportReq downloadReportReq, HttpServletResponse response) {
        exportReportService.downloadReport(downloadReportReq, response);
    }

}
