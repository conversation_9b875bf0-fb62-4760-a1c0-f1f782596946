package com.tiangong.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 自动对账
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:15
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("f_supply_auto_reconciliation")
public class SupplyAutoReconciliationEntity extends Model<SupplyAutoReconciliationEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 对账结果类型
     * 0:一致
     * 1:金额不对
     * 2:供应商少单
     * 3:我方少单
     */
    private Integer reconciliationResultType;
    /**
     * 我方账单Id
     */
    private Integer statementId;
    /**
     * 我方账单编号
     */
    private String statementCode;
    /**
     * 供货单编码
     */
    private String supplyOrderCode;
    /**
     * 应付供应商金额
     */
    private BigDecimal supplyPaidAmt;
    /**
     * 应付供应商金额币种
     */
    private String supplyPaidAmtCurrency;
    /**
     * 供应商账单Id
     */
    private Integer supplierStatementId;
    /**
     * 供应商账单编号
     * 供应商账单编号规则：G+年月日+四位随机数
     */
    private String supplierStatementCode;
    /**
     * 供应商订单号
     */
    private String supplierOrderCode;
    /**
     * 供应商应收金额
     */
    private BigDecimal supplierReceivedAmt;
    /**
     * 供应商应收金额币种
     */
    private String supplierReceivedAmtCurrency;
    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

}
