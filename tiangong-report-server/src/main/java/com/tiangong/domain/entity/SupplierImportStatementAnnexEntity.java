package com.tiangong.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商导入的账单的附件
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:12
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@TableName("f_supplier_import_statement_annex")
public class SupplierImportStatementAnnexEntity extends Model<SupplierImportStatementAnnexEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 供应商订单号
     */
    private String supplierOrderCode;
    /**
     * 供货单编码
     */
    private String supplyOrderCode;
    /**
     * 应收金额
     */
    private BigDecimal receivableAmt;
    /**
     * 币种
     */
    private String currency;
    /**
     * 导入的供应商账单id
     */
    private Integer supplierImportStatementId;
    /**
     * 是否删除
     * 0 否
     * 1 是
     */
    private Integer deleted;
    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

}

