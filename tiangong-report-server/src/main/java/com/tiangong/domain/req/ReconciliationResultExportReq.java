package com.tiangong.domain.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/6/1 16:27
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ReconciliationResultExportReq implements Serializable {

    /**
     * 账单id
     */
    private Integer statementId;
    /**
     * 我方账单编号
     */
    private String statementCode;

}
