package com.tiangong.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

@Data
public class QueryStatementOrderListDTO extends BaseRequest {

    /**
     * 账单id
     */
    private Integer statementId;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 日期查询类型：0下单日期，1入住日期，2离店日期
     */
    private Integer dateQueryType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 客户名称
     */
    private String agentName;
}

