package com.tiangong.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 结算单表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
@Data
public class SettleOrderReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 商家编码 天宫商家编码
     */
    private String merchantCode;
    /**
     * 供应商编码 天宫供应商编码
     */
    private String supplierCode;
    /**
     * 订单编码 天宫订单编码
     */
    private String orderCode;
    /**
     * 订单创建时间
     */
    private Date orderDt;
    /**
     * 供货单编码 天宫供货单编码
     */
    private String supplyOrderCode;
    /**
     * 天宫供货单创建时间 天宫供货单创建时间
     */
    private Date tgSupplyOrderDt;
    /**
     * 订单通商家编码
     */
    private String ddtMerchantCode;
    /**
     * 订单通客户编码 订单通客户编码
     */
    private String ddtCustomerCode;
    /**
     * 订单通订单编码 订单通订单编码
     */
    private String ddtOrderCode;
    /**
     * 订单通订单创建时间 订单通订单创建时间
     */
    private Date ddtOrderDt;
    /**
     * 结算时间 结算时间
     */
    private Date settleDt;
    /**
     * 结算时间控制类型 [1:下单后 2:入住当日之后 3:离店当日之后 4:离店周结 5:离店半月结 6:离店月结]
     */
    private Integer settleType;
    /**
     * 付款主体id 银行账号id值
     */
    private Long payerAccountId;
    /**
     * 公付邮件发送状态 [0:无需发送 1:需要发送 2:发送成功 3:发送失败 4:首次失败 5:二次发送失败]
     */
    private Integer sendStatus;
    /**
     * 公付邮件发送时间
     */
    private Date sendDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;


    /**
     * 【前端条件】日期查询类型：0下单日期，1入住日期，2离店日期
     */
    private Integer dateQueryType;

    /**
     * 【前端条件】开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 【前端条件】结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 确认号
     */
    private String confirmNo;

    /**
     * 酒店id
     */
    private Integer hotelId;

}