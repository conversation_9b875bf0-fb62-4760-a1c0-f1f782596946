package com.tiangong.domain.req;


import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 付款任务表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:59
 */
@Data
public class SettleServiceJobReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 任务编码
     */
    private String taskCode;
    /**
     * 业务订单号 （订单编码/账单编码）
     */
    private String businessCode;
    /**
     * 转账任务状态 [0:收(付)款中 1:收(付)款成功 2:收(付)款失败 3等待收(付)款]
     */
    private Integer transferStatus;
    /**
     * 支付凭证 支付流水号
     */
    private String searialNo;
    /**
     * 商户业务流水号
     */
    private String bizNo;
    /**
     * 订单类型 [0:单结结算 1:周期结结算]
     */
    private Integer orderType;
    /**
     * 订单通状态 [0:未处理 1:处理中 2处理成功]
     */
    private Integer ddtStatus;
    /**
     * 天宫账单编码 天宫账单编码
     */
    private String tgBillCode;
    /**
     * 天宫状态 [0:未处理 1:处理中 2:处理成功]
     */
    private Integer tgStatus;
    /**
     * 任务状态 [0:未完成 1已完成]
     */
    private Integer progressStatus;
    /**
     * 付款金额
     */
    private BigDecimal paymentAmt;
    /**
     * 付款时间
     */
    private Date paymentDt;
    /**
     * 创建人 下单人
     */
    private String createdBy;
    /**
     * 创建时间 下单时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}