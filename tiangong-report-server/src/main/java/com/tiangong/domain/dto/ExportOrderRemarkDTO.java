package com.tiangong.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/10 下午3:52
 * @Description:
 */

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ExportOrderRemarkDTO {


    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 订单确认状态：0待确认，1已确认，2已取消, 3已完成
     * @see com.tiangong.enums.OrderStatusEnum
     */
    //private Integer orderConfirmationStatus;

    /**
     * 订单确认状态：0待确认，1已确认，2已取消, 3已完成
     */
    private String orderConfirmationStatusDesc;


    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 订单创建时间
     */
    private String orderCreatedDt;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 确认时间
     */
    private String confirmationCreatedDt;

    /**
     * 0: 分销商备注，1：供应商备注 ，2：内部备注
     */
    //private Integer remarkType;

    /**
     * 0: 分销商备注，1：供应商备注 ，2：内部备注
     */
    private String remarkTypeDesc;

    /**
     * 备注内容
     */
    private String remark;

    /**
     * 备注创建人
     */
    private String orderRemarkCreatedBy;

    /**
     * 备注创建时间
     */
    private String orderRemarkCreatedDt;

}
