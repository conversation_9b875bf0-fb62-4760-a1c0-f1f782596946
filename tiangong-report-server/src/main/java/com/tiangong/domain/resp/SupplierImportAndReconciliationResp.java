package com.tiangong.domain.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/6/3 10:06
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SupplierImportAndReconciliationResp implements Serializable {

    /**
     * 账单id
     */
    private Integer statementId;

    /**
     * 账单编码
     */
    private String statementCode;

}
