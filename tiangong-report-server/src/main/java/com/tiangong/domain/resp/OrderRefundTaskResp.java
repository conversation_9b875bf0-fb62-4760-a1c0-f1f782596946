package com.tiangong.domain.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单退款任务数据
 * <AUTHOR>
 */
@Data
public class OrderRefundTaskResp {


    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 退款任务类型 0-人工 1-自动
     */
    private String refundTaskType;

    /**
     * 退款状态 0-新建 1-通知成功 2-已取消 3-退款中
     */
    private String refundState;

    /**
     * 币种
     */
    private String currency;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款类型 0-普通退款（平安）1-罚金退款（平安） 3-退款
     */
    private String refundType;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 客户单号
     */
    private String channelOrderCode;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdDt;

}
