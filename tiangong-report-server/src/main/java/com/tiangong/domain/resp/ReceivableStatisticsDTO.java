package com.tiangong.domain.resp;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 应收报表汇总统计
 */
@Data
public class ReceivableStatisticsDTO {

    /**
     * 客户名称
     */
    private  String agentName;

    /**
     * 客户编码
     */
    private  String agentCode;

    /**
     * 客户经理
     */
    private String agentManagerName;

    /**
     * 订单结算方式
     */
    private Integer orderSettlementType;

    /**
     * 订单结算方式
     */
    private String orderSettlementDesc;

    /**
     * 间夜数
     */
    private BigDecimal nightQty;

    /**
     * 销售币种
     */
    private Integer saleCurrency;

    /**
     * 销售币种
     */
    private String saleCurrencyDesc;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmt;

    /**
     * 实收金额
     */
    private BigDecimal receivedAmt;

    /**
     * 未收金额
     */
    private BigDecimal unreceivedAmt;

    /**
     * 本币应收(商家币种)
     */
    private BigDecimal receivableOrgCurrencyAmt;

    /**
     * 成本(商家币种)
     */
    private BigDecimal suppleOrderPayableOrgCurrencyAmt;

    /**
     * 利润(商家币种)
     */
    private BigDecimal profitOrgCurrencyAmt;

    /**
     * 客户经理列表
     */
    private List<String> agentManagerNames;

}
