package com.tiangong.domain.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class HotelMappingResp implements Serializable {

    /**
     * 酒店ID
     */
    private String hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 供应商类型
     */
    private String supplierCode;

    /**
     * 供应商酒店ID
     */
    private String spHotelId;

    /**
     * 供应商酒店ID
     */
    private String spHotelName;

    /**
     * 映射状态
     */
    private String mappingStatus;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private String updatedDt;

}
