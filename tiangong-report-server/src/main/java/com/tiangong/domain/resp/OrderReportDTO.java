package com.tiangong.domain.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单报表数据
 * <AUTHOR>
 */
@Data
public class OrderReportDTO {


    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 集团
     */
    private String groupName;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 星级
     */
    private Integer hotelRank;

    /**
     * 酒店星级描述 HotelRankEnum
     */
    private String hotelRankDesc;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 下单日期
     */
    private String createdDt;

    /**
     * 入住日期
     */
    private String checkInDate;

    /**
     * 离店日期
     */
    private String checkOutDate;

    /**
     * 入住时间
     */
    private String startTime;

    /**
     * 离店时间
     */
    private String endTime;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 钟点房标志 1是 0否
     */
    private Integer hourly;

    /**
     * 夜数
     */
    private Integer nightQty;

    /**
     * 入住人
     */
    private String guest;

    /**
     * 订单确认状态
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer orderConfirmationStatus;

    /**
     * 订单确认状态描述
     */
    private String orderConfirmationDesc;

    /**
     * 订单结算方式
     */
    private Integer orderSettlementType;

    /**
     * 订单结算方式描述
     */
    private String orderSettlementDesc;

    /**
     * 下单人
     */
    private String createdBy;

    /**
     * 归属人
     */
    private String orderOwnerName;

    /**
     * 销售经理
     */
    private String saleManagerName;

    /**
     * 客户类型
     */
    private String agentType;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 渠道单号（客户单号）
     */
    private String channelOrderCode;

    /**
     * 销售币种
     */
    private Integer saleCurrency;

    /**
     * 产品标签（0或者null：无标签  1：协议价格标签）
     * 是否协议订单
     */
    private Integer productLabel;

    /**
     * 产品标签描述
     */
    private String productLabelDesc;

    /**
     * 售价币种兑换商家币种的汇率
     */
    private BigDecimal saleRate;

    /**
     * 销售币种描述
     */
    private String saleCurrencyDesc;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmt;

    /**
     * 应收金额(商家币种)
     */
    private BigDecimal receivableOrgCurrencyAmt;

    /**
     * 实收金额
     */
    private BigDecimal receivedAmt;

    /**
     * 实收金额(商家币种)
     */
    private BigDecimal receivedOrgCurrencyAmt;

    /**
     * 未收金额
     */
    private BigDecimal unreceivedAmt;

    /**
     * 未收金额(商家币种)
     */
    private BigDecimal unreceivedOrgCurrencyAmt;

    /**
     * 采购经理
     */
    private String purchaseManagerName;

    /**
     * 客户渠道
     */
    private String channelName;

    /**
     * 客户渠道编码
     */
    private String channelCode;

    /**
     * 支付类型
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 支付类型（1-预付 2到店付）
     */
    private String payMethodDesc;

    /**
     * 产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    private Integer productLabelType;

    /**
     * 产品标签类型描述（1：代结算  0：非代结算 未配置则为null）
     */
    private String productLabelTypeDesc;

    /**
     * 供货单明细数据
     */
    private List<SuppleOrderReportDTO> suppleOrderReportList;

    /**
     * 供货单确认状态描述
     */
    private String supplyOrderConfirmationStatusDesc;

    /**
     * 供货单结算方式描述
     */
    private String supplyOrderSettlementTypeDesc;

    /**
     * 采购币种描述
     */
    private String baseCurrencyDesc;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 供货单应付总额
     */
    private BigDecimal suppleOrderTotalPayableAmt;

    /**
     * 供货单应付总额(商家币种)
     */
    private BigDecimal suppleOrderTotalPayableOrgCurrencyAmt;

    /**
     * 供应商奖励总额
     */
    private BigDecimal supplierTotalRewardAmt;

    /**
     * 供应商奖励总额(商家币种)
     */
    private BigDecimal supplierTotalRewardOrgCurrencyAmt;

    /**
     * 供应商返佣总额
     */
    private BigDecimal supplierTotalRebateAmt;

    /**
     * 供应商返佣总额(商家币种)
     */
    private BigDecimal supplierTotalRebateOrgCurrencyAmt;

    /**
     * 供应商应付总额
     */
    private BigDecimal supplierTotalPayableAmount;

    /**
     * 供应商应付总额CNY
     */
    private BigDecimal supplierTotalPayableOrgCurrencyAmount;

    /**
     * 供应商未付总金额(商家币种)
     */
    private BigDecimal supplierTotalUnpaidOrgCurrencyAmt;

    /**
     * 利润
     */
    private BigDecimal profit;

    /**
     * 利润CNY
     */
    private BigDecimal profitOrgCurrencyAmt;

    /**
     * 利润率
     */
    private BigDecimal profitOrgCurrencyRate;

    /**
     * 提前预定天数
     */
    private Integer bookingDays;

    /**
     * 订单来源（手工单、GDP、API）
     * @see com.tiangong.enums.OrderSourceEnum
     */
    private Integer orderSource;

    /**
     * 订单渠道编码
     */
    private String orderChannelCode;
}
