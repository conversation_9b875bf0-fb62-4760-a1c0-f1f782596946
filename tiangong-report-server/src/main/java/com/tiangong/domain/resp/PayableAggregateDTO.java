package com.tiangong.domain.resp;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 应收报表汇总统计
 */
@Data
public class PayableAggregateDTO {

    /**
     * 总应付
     */
    private BigDecimal supplierTotalPayableOrgCurrencyAmount = new BigDecimal("0");

    /**
     * 总实付
     */
    private BigDecimal supplierTotalOrderPaidOrgCurrencyAmt = new BigDecimal("0");

    /**
     * 总未付
     */
    private BigDecimal supplierTotalUnpaidOrgCurrencyAmt = new BigDecimal("0");

}
