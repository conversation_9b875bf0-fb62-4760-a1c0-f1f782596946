package com.tiangong.domain.resp;

import com.alibaba.druid.sql.visitor.functions.Bin;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供货单财务数据
 * <AUTHOR>
 */

@Data
public class SuppleOrderFinanceReportResp {

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 供应商汇率（转商家币种汇率）
     */
    private BigDecimal baseRate;

    /**
     * 供货单已付金额
     */
    private BigDecimal supplyOrderPaidAmt;

    /**
     * 供货单已付金额(商家币种)
     */
    private BigDecimal supplyOrderPaidOrgCurrencyAmt;

    /**
     * 供货单未付金额
     */
    private BigDecimal supplyOrderUnpaidAmt;

    /**
     * 供货单未付金额(商家币种)
     */
    private BigDecimal supplyOrderUnpaidOrgCurrencyAmt;




    /**
     * 奖励已收金额
     */
    private BigDecimal rewardPaidAmt;

    /**
     * 奖励已收金额(商家币种)
     */
    private BigDecimal rewardPaidOrgCurrencyAmt;

    /**
     * 奖励未收金额
     */
    private BigDecimal rewardUnpaidAmt;

    /**
     * 奖励未收金额(商家币种)
     */
    private BigDecimal rewardUnpaidOrgCurrencyAmt;



    /**
     * 返佣已付金额
     */
    private BigDecimal rebatePaidAmt;

    /**
     * 返佣已付金额(商家币种)
     */
    private BigDecimal rebatePaidOrgCurrencyAmt;

    /**
     * 返佣未付金额
     */
    private BigDecimal rebateUnpaidAmt;

    /**
     * 返佣未付金额(商家币种)
     */
    private BigDecimal rebateUnpaidOrgCurrencyAmt;


}
