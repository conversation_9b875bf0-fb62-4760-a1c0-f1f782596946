package com.tiangong.domain.resp;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 利润报表汇总统计
 */
@Data
public class ProfitStatisticsDTO {

    /**
         * 总订单金额
     */
    private BigDecimal receivableOrgCurrencyAmt = new BigDecimal("0");

    /**
     * 总供货单金额
     */
    private BigDecimal suppleOrderTotalPayableOrgCurrencyAmt = new BigDecimal("0");

    /**
     * 总供应商奖励
     */
    private BigDecimal supplierTotalRewardOrgCurrencyAmt = new BigDecimal("0");

    /**
     * 总供应商返佣
     */
    private BigDecimal supplierTotalRebateOrgCurrencyAmt = new BigDecimal("0");

    /**
     * 总供应商应付
     */
    private BigDecimal supplierTotalPayableOrgCurrencyAmount = new BigDecimal("0");

    /**
     * 总利润
     */
    private BigDecimal profitOrgCurrencyAmt = new BigDecimal("0");

    /**
     * 利润率
     */
    private BigDecimal profitRate = new BigDecimal("0");
}
