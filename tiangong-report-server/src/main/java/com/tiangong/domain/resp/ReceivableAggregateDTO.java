package com.tiangong.domain.resp;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 应收报表汇总统计
 */
@Data
public class ReceivableAggregateDTO {

    /**
     * 总订单应收
     */
    private BigDecimal receivableAmtTotal = new BigDecimal("0");

    /**
     * 总订单实收
     */
    private BigDecimal receivedAmtTotal = new BigDecimal("0");

    /**
     * 总订单未收
     */
        private BigDecimal unreceivedAmtTotal = new BigDecimal("0");

}
