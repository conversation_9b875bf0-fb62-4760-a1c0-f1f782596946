package com.tiangong.domain.resp;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售报表汇总统计
 */
@Data
public class SaleStatisticsDTO {

    /**
     * 销售间夜
     */
    private BigDecimal saleNightQty = new BigDecimal("0");

    /**
     * 销售金额
     */
    private BigDecimal saleAmt = new BigDecimal("0");

    /**
     * 未收金额
     */
    private BigDecimal unreceivedAmt = new BigDecimal("0");

    /**
     * 利润
     */
    private BigDecimal profit = new BigDecimal("0");

    /**
     * 利润率
     */
    private BigDecimal profitRate = new BigDecimal("0");


}
