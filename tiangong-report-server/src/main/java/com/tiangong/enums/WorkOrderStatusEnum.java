package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单处理状态
 * [0:未处理 1:处理中 2:处理成功]
 */
@Getter
@AllArgsConstructor
public enum WorkOrderStatusEnum {

    WORK_ORDER_STATUS_0(0, "未处理"),

    WORK_ORDER_STATUS_1(1, "处理中"),

    WORK_ORDER_STATUS_2(2, "处理成功");

    public final int key;
    public final String value;

    public static String getValueByKey(int key) {
        String value = null;
        for (WorkOrderStatusEnum checkDetailEnum : WorkOrderStatusEnum.values()) {
            if (checkDetailEnum.key == key) {
                value = checkDetailEnum.value;
                break;
            }
        }
        return value;
    }
}
