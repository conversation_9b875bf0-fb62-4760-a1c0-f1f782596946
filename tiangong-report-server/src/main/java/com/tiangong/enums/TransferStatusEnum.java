package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 转账任务状态枚举值
 * [0:收(付)款中 1:收(付)款成功 2:收(付)款失败 3:等待收(付)款]
 */
@Getter
@AllArgsConstructor
public enum TransferStatusEnum {

    TRANSFER_STATUS_0(0, "付款中"),
    TRANSFER_STATUS_1(1, "付款成功"),
    TRANSFER_STATUS_2(2, "付款失败"),
    TRANSFER_STATUS_3(3, "等待付款");

    public final int key;
    public final String value;

    public static String getValueByKey(int key) {
        String value = null;
        for (TransferStatusEnum checkDetailEnum : TransferStatusEnum.values()) {
            if (checkDetailEnum.key == key) {
                value = checkDetailEnum.value;
                break;
            }
        }
        return value;
    }
}
