package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvoiceMangeStatusEnum {

    UNUSED(0, "未使用"),
    PENDING(1, "待检查"),
    PARTIAL_USE(2, "部分使用"),
    USED(3, "已使用");

    public final int key;
    public final String value;

    public static Integer getKeyByValue(String value) {
        Integer key = null;
        for (InvoiceMangeStatusEnum invoiceStatusEnum : InvoiceMangeStatusEnum.values()) {
            if (invoiceStatusEnum.value.equals(value)) {
                key = invoiceStatusEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (InvoiceMangeStatusEnum invoiceStatusEnum : InvoiceMangeStatusEnum.values()) {
            if (invoiceStatusEnum.key == key) {
                value = invoiceStatusEnum.value;
                break;
            }
        }
        return value;
    }
}
