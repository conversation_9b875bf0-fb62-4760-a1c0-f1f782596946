package com.tiangong.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.order.remote.request.OrderRefundTaskDetailDTO;
import com.tiangong.order.remote.request.QueryOrderRefundTaskDTO;

/**
 * @author: ziyi
 * @date: 2024/3/18
 * @description: 订单退款通知
 */
public interface OrderStatisticsService {


    /**
     * 查询在线退款列表
     *
     * @param request
     * @return
     */
    PaginationSupportDTO<OrderRefundTaskDetailDTO> queryOrderRefundTaskList(QueryOrderRefundTaskDTO request);

}
