package com.tiangong.service;

import com.tiangong.common.Response;
import com.tiangong.domain.dto.AddStatementSupplyOrderListDTO;
import com.tiangong.domain.dto.CreateSupplierStatementDTO;

/**
 * <AUTHOR>
 * @Date 2023/7/3 10:51
 * @Description:
 */
public interface SupplierStatementService {

    /**
     * 创建账单
     */
    String createStatement(CreateSupplierStatementDTO request);

    /**
     * 添加账单明细
     */
    void addStatementOrderList(AddStatementSupplyOrderListDTO request);
}
