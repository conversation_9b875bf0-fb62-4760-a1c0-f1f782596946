package com.tiangong.service;

import com.tiangong.domain.req.SalesDetailReq;
import com.tiangong.domain.req.SummaryReq;
import com.tiangong.domain.resp.*;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.statistics.dto.QuerySaleStatisticsDTO;

import java.util.List;
import java.util.Map;

/**
 * 报表服务处理类
 * <AUTHOR>
 */
public interface ReportStatisticsService {

    /**
     * 初始化所有订单
     */
    List<String> queryAllOrderCode(QuerySaleStatisticsDTO request);

    /**
     * 查询订单全部数据
     */
    void queryAllReport(String orderCode);

    /**
     * 经营概况
     * @param requestMap 入参条件
     */
    BusinessOverviewResp queryBusinessOverview(Map<String, String> requestMap);

    /**
     * 查询销售报表汇总统计
     * @param salesDetailReq 请求参数
     * @return 返回符合条件的数据
     */
    SaleStatisticsDTO querySalesStatistics(SalesDetailReq salesDetailReq);

    /**
     * 查询销售报表
     * @param salesDetailReq 请求参数
     * @return 返回符合条件的数据
     */
    PaginationSupportDTO<OrderReportResp> querySalesDetailStatistics(SalesDetailReq salesDetailReq);


    /**
     * 查询利润报表汇总统计
     * @param salesDetailReq 请求参数
     * @return 返回符合条件的数据
     */
    ProfitStatisticsDTO queryProfitStatistics(SalesDetailReq salesDetailReq);


    /**
     * 概况统计表
     * @param summaryReq
     * @return
     */
    PaginationSupportDTO<OrderReportResp> querySummaryStatistics(SummaryReq summaryReq);

    /**
     * 应收报表
     * @param request
     * @return
     */
    PaginationSupportDTO<ReceivableStatisticsDTO> queryReceivableStatistics(SalesDetailReq request);

    /**
     * 应收报表统计
     * @param request
     * @return
     */
    ReceivableAggregateDTO queryReceivableAggregate(SalesDetailReq request);

    /**
     * 应付报表
     * @param request
     * @return
     */
    PaginationSupportDTO<PayableStatisticsResp> queryPayableStatistics(SalesDetailReq request);

    /**
     * 应付报表统计
     * @param request
     * @return
     */
    PayableAggregateDTO queryPayableAggregate(SalesDetailReq request);

}
