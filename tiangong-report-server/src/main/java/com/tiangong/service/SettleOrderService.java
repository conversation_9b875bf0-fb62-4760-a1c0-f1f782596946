package com.tiangong.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.Response;
import com.tiangong.domain.entity.SettleOrderEntity;
import com.tiangong.domain.req.SettleOrderReq;
import com.tiangong.domain.resp.SettleOrderResp;

import java.util.List;

/**
 * 结算单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
public interface SettleOrderService extends IService<SettleOrderEntity> {

    /**
     * 结算单表列表（分页）
     */
    List<SettleOrderResp> settleOrderList(SettleOrderReq req);

}

