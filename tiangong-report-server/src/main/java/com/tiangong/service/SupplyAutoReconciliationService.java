package com.tiangong.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.domain.entity.SupplyAutoReconciliationEntity;
import com.tiangong.domain.req.ReconciliationResultExportReq;

/**
 * <AUTHOR>
 * @Date 2023/7/3 19:14
 * @Description:
 */
public interface SupplyAutoReconciliationService extends IService<SupplyAutoReconciliationEntity> {

    /**
     * 导出比对结果
     */
    String reconciliationResultExport(ReconciliationResultExportReq req);
}
