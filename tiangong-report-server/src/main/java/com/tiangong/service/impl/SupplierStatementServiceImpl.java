package com.tiangong.service.impl;

import com.alibaba.fastjson.JSON;
import com.tiangong.common.remote.SequenceRemote;
import com.tiangong.domain.dto.*;
import com.tiangong.domain.po.SupplierStatementOrderPO;
import com.tiangong.domain.po.SupplierStatementPO;
import com.tiangong.enums.*;
import com.tiangong.mapper.SupplierStatementMapper;
import com.tiangong.mapper.SupplierStatementOrderMapper;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.FinanceLockService;
import com.tiangong.service.SupplierStatementService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/7/3 10:56
 * @Description:
 */
@Slf4j
@Service
public class SupplierStatementServiceImpl implements SupplierStatementService {

    private final Integer batchQueryCount = 2000;

    @Autowired
    private SupplierStatementMapper supplierStatementMapper;

    @Autowired
    private SupplierStatementOrderMapper supplierStatementOrderMapper;

    @Autowired
    private FinanceLockService financeLockService;

    @Autowired
    private SequenceRemote sequenceRemote;

    @Transactional
    @Override
    public String createStatement(CreateSupplierStatementDTO request) {
        // 1.创建账单
        SupplierStatementPO supplierStatementPO = new SupplierStatementPO();
        BeanUtils.copyProperties(request, supplierStatementPO);
        supplierStatementPO.setStatementStatus(StatementStatusEnum.UN_CHECK.key);
        supplierStatementPO.setStatementAmt(BigDecimal.ZERO);
        supplierStatementPO.setPaidAmt(BigDecimal.ZERO);
        supplierStatementPO.setUnpaidAmt(BigDecimal.ZERO);
        supplierStatementPO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
        supplierStatementPO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
        supplierStatementPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
        supplierStatementPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
        supplierStatementPO.setSettlementStatus(0);
        supplierStatementPO.setCreatedBy(request.getOperator());
        supplierStatementPO.setCreatedDt(new Date());
        supplierStatementPO.setSettlementDate(DateUtilX.stringToDate(request.getSettlementDate()));

        //获取编码
        String key = SystemCodeEnum.SUPPLIERSTATEMENTCODE.code;
        if (Objects.equals(request.getStatementType(), StatementTypeEnum.REWARD_AMT.key)) {
            key = SystemCodeEnum.SUPPLIERREWARDSTATEMENTCODE.code;
        } else if (Objects.equals(request.getStatementType(), StatementTypeEnum.REBATE_AMT.key)) {
            key = SystemCodeEnum.SUPPLIERREBATESTATEMENTCODE.code;
        }
        String code = RedisTemplateX.lRightPop(key);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", key);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(key);
        }
        supplierStatementPO.setStatementCode(code);

        supplierStatementMapper.insert(supplierStatementPO);

        // 2.保存账单明细
        InsertStatementSupplyOrderDTO insertStatementSupplyOrderDTO = new InsertStatementSupplyOrderDTO();
        BeanUtils.copyProperties(request, insertStatementSupplyOrderDTO);
        insertStatementSupplyOrderDTO.setStatementId(supplierStatementPO.getId());
        supplierStatementOrderMapper.saveBatchStatementOrder(insertStatementSupplyOrderDTO);

        // 3.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(supplierStatementPO.getId());
        //supplierStatementOrderMapper.updateStatementAmount(statementIdDTO);
        updateStatementAmount(statementIdDTO);

        // 4.更新订单对账状态为出账中
        UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
        updateSupplyOrderFinanceDTO.setStatementId(supplierStatementPO.getId());
        updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
        updateSupplyOrderFinanceDTO.setFinanceLockStatus(1);
        supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);

        supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(supplierStatementPO.getId());

        //再创建账单时自动为账单里的订单加锁
        QueryStatementSupplyOrderListPageDTO queryStatementSupplyOrderListPageDTO = new QueryStatementSupplyOrderListPageDTO();
        queryStatementSupplyOrderListPageDTO.setStatementId(supplierStatementPO.getId());
        queryStatementSupplyOrderListPageDTO.setPageSize(batchQueryCount);
        Integer indexNum = 0;
        while (true) {
            queryStatementSupplyOrderListPageDTO.setCurrentPage(indexNum * batchQueryCount);
            List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderListPage(queryStatementSupplyOrderListPageDTO);
            if (CollUtilX.isEmpty(list)) {
                break;
            } else {
                for (StatementSupplyOrderDTO statementSupplyOrderDTO : list) {
                    FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
                    Example example = new Example(SupplierStatementOrderPO.class);
                    example.createCriteria().andEqualTo("supplyOrderCode", statementSupplyOrderDTO.getSupplyOrderCode())
                            .andEqualTo("statementId", supplierStatementPO.getId());
                    SupplierStatementOrderPO supplierStatementOrderPO = supplierStatementOrderMapper.selectOneByExample(example);
                    financeLockSupplyOrderDTO.setSupplyOrderId(supplierStatementOrderPO.getSupplyOrderId());
                    financeLockSupplyOrderDTO.setLockStatus(0);
                    financeLockSupplyOrderDTO.setStatementType(request.getStatementType());
                    financeLockService.lockSupplyOrder(financeLockSupplyOrderDTO);
                }
            }
            indexNum++;
        }
        return supplierStatementPO.getStatementCode();
    }

    @Transactional
    @Override
    public void addStatementOrderList(AddStatementSupplyOrderListDTO request) {
        SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());

        //按条件批量增加，查询满足条件的订单
        if (null == request.getSupplyOrderIdList() || 0 == request.getSupplyOrderIdList().size()) {
            QueryUnCheckOutSupplyOrderDTO queryUnCheckOutSupplyOrderDTO = new QueryUnCheckOutSupplyOrderDTO();
            queryUnCheckOutSupplyOrderDTO.setSupplierCode(supplierStatementPO.getSupplierCode());
            queryUnCheckOutSupplyOrderDTO.setCompanyCode(request.getCompanyCode());
            queryUnCheckOutSupplyOrderDTO.setDateQueryType(request.getDateQueryType());
            queryUnCheckOutSupplyOrderDTO.setStartDate(request.getStartDate());
            queryUnCheckOutSupplyOrderDTO.setEndDate(request.getEndDate());
            List<UnCheckOutSupplyOrderDTO> list = supplierStatementOrderMapper.queryUnCheckOutSupplyOrder(queryUnCheckOutSupplyOrderDTO);
            if (CollUtilX.isNotEmpty(list)) {
                List<Integer> supplyOrderIdList = new ArrayList<>();
                for (UnCheckOutSupplyOrderDTO unCheckOutSupplyOrderDTO : list) {
                    supplyOrderIdList.add(unCheckOutSupplyOrderDTO.getSupplyOrderId());
                }
                request.setSupplyOrderIdList(supplyOrderIdList);
            } else {
                return;
            }
        }

        // 1.保存账单明细
        InsertStatementSupplyOrderDTO insertStatementSupplyOrderDTO = new InsertStatementSupplyOrderDTO();
        insertStatementSupplyOrderDTO.setStatementId(request.getStatementId());
        insertStatementSupplyOrderDTO.setSupplyOrderIdList(request.getSupplyOrderIdList());
        insertStatementSupplyOrderDTO.setCompanyCode(supplierStatementPO.getCompanyCode());
        supplierStatementOrderMapper.saveBatchStatementOrder(insertStatementSupplyOrderDTO);

        // 2.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        //supplierStatementOrderMapper.updateStatementAmount(statementIdDTO);
        updateStatementAmount(statementIdDTO);

        // 3.更新订单对账状态为出账中，并将订单加锁
        UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
        updateSupplyOrderFinanceDTO.setStatementId(request.getStatementId());
        updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
        updateSupplyOrderFinanceDTO.setFinanceLockStatus(1);
        updateSupplyOrderFinanceDTO.setSupplyOrderIdList(request.getSupplyOrderIdList());
        supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
        if (Objects.equals(supplierStatementPO.getStatementStatus(), StatementStatusEnum.CHECKING.key)) {
            List<Integer> supplyOrderIdList = request.getSupplyOrderIdList();
            for (Integer supplyOrderId : supplyOrderIdList) {
                FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
                financeLockSupplyOrderDTO.setSupplyOrderId(supplyOrderId);
                financeLockSupplyOrderDTO.setLockStatus(1);
                financeLockService.lockSupplyOrder(financeLockSupplyOrderDTO);
            }
        }
    }

    /**
     * 更新账单金额
     */
    private void updateStatementAmount(StatementIdDTO statementIdDTO) {
        QueryStatementTotalAmountDTO queryStatementTotalAmountDTO = supplierStatementOrderMapper.queryStatementAmount(statementIdDTO);
        if (queryStatementTotalAmountDTO == null) {
            queryStatementTotalAmountDTO = new QueryStatementTotalAmountDTO();
            queryStatementTotalAmountDTO.setAmount("0");
            queryStatementTotalAmountDTO.setStatementId(statementIdDTO.getStatementId());
        }
        supplierStatementOrderMapper.updateStatementAmount(queryStatementTotalAmountDTO);
    }

}
