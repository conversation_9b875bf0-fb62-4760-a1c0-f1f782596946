package com.tiangong.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.mapper.OrderStatisticsMapper;
import com.tiangong.order.remote.request.OrderRefundTaskDetailDTO;
import com.tiangong.order.remote.request.QueryOrderRefundTaskDTO;
import com.tiangong.service.OrderStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: ziyi
 * @date: 2024/3/18 10:57
 * @description: 订单报表
 */
@Slf4j
@Service
public class OrderStatisticsServiceImpl implements OrderStatisticsService {

    @Autowired
    private OrderStatisticsMapper orderStatisticsMapper;

    @Override
    public PaginationSupportDTO<OrderRefundTaskDetailDTO> queryOrderRefundTaskList(QueryOrderRefundTaskDTO request) {
        IPage<OrderRefundTaskDetailDTO> iPage = new Page<>(request.getCurrentPage(), request.getPageSize());
        IPage<OrderRefundTaskDetailDTO> page = orderStatisticsMapper.queryOrderRefundTaskList(iPage, request);

        PaginationSupportDTO paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(page);
        return paginationSupportDTO;
    }

}
