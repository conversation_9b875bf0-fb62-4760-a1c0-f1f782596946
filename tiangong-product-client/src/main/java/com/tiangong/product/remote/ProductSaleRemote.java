package com.tiangong.product.remote;

import com.tiangong.common.Response;
import com.tiangong.product.dto.CalculateSelfHotelLowestDTO;
import com.tiangong.product.dto.ProductSalePriceDetailDTO;
import com.tiangong.product.dto.QueryProductRequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 12:06
 * @Description: 产品销售
 */
@FeignClient(value = "tiangong-product-server")
public interface ProductSaleRemote {

    /**
     * 查询产品售价列表
     */
    @PostMapping("/product/sale/querySalePriceList")
    Response<ProductSalePriceDetailDTO> querySalePriceList(@RequestBody QueryProductRequestDTO queryProductRequestDTO);

    /**
     * 初始化计算自签酒店起价任务
     */
    @PostMapping("/product/sale/initCalculateSelfHotelLowestPriceTask")
    Response<Object> initCalculateSelfHotelLowestPriceTask();

    /**
     * 处理需要计算自签酒店起价
     */
    @PostMapping("/product/sale/calculateNeedSelfHotelLowestPrice")
    Response<Object> calculateNeedSelfHotelLowestPrice(@RequestBody CalculateSelfHotelLowestDTO dto);
}
