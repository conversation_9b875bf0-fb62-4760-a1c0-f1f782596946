package com.tiangong.product.dto;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 14:47
 * @Description:
 */
@Data
public class QueryProductRequestDTO extends BaseRequest implements Serializable {

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 手动输入的酒店id
     */
    private Long id;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店Id列表
     */
    private List<Long> hotelIdList;

    /**
     * 是否含产品
     */
    private Integer hasProducts;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 销售状态（1销售中 0仓库中）
     */
    private String saleStatus;

    /**
     * 闲置状态（1闲置中 0仓库中）
     */
    private String idleStatus;

    /**
     * 销售渠道（B2B）
     */
    private String channelCode;

    /**
     * 产品Id
     */
    private String productId;
    /**
     * 房型Id
     */
    private Integer roomId;
    /**
     * 运营商编码
     */
    private String companyCode;
    /**
     * 采购方式
     */
    private String purchaseType;

    /**
     * 停售状态
     */
    private String offShelveStatus;

    /**
     * 供应商产品Id
     */
    private String spProductId;

    /**
     * 酒店集团编码
     */
    private String groupCode;

    /**
     * 酒店品牌编码
     */
    private String brandCode;

    /**
     * 语言
     */
    private String language;

    /**
     * 分销商编码
     */
    private String agentCode;

}
