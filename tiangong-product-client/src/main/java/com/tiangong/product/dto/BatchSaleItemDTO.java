package com.tiangong.product.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Auther: <PERSON>
 * @Date: 2019/6/19 01:35
 * @Description: 批量调整售价
 */
@Data
public class BatchSaleItemDTO {

    /**
     * 产品Id
     */
    private Integer productId;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 星期（1-7表示周一至周天，选择周一，周二，周四，传入数据为：1,2,4）
     */
    private String weekList;

    /**
     * 调整方式（0加数值 1减数值 2加百分比 3减百分比 4等于）
     */
    private Integer salePriceAdjustmentType;

    /**
     * 调整金额
     */
    private BigDecimal modifiedSalePrice;

    /**
     * 售价最低加幅
     */
    private BigDecimal saleMiniAddRadiation;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 上下架状态
     */
    private Integer saleStatus;


    private Long id;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 供应商编码
     */
    private String supplierCode;
}
