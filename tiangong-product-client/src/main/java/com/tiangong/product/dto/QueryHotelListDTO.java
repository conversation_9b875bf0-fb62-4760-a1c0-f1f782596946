package com.tiangong.product.dto;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

@Data
public class QueryHotelListDTO extends BaseRequest {

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 位置
     */
    private String position;

    /**
     * 入住时间
     */
    private String checkInDate;

    /**
     * 离店时间
     */
    private String checkOutDate;

    /**
     * 最低价格
     */
    private Long lowerPrice;

    /**
     * 最高价格
     */
    private Long higherPrice;

    /**
     * 酒店名/关键字/品牌/集团
     */
    private String keyword;

    /**
     * 酒店星级
     */
    private String hotelRank;

    /**
     * 排序类型  0 默认排序， 1 低价优先， 2 高价优先
     */
    private String sortType;

    /**
     * 行政区
     */
    private String administrativeDistrict;

    /**
     * 纬度
     */
    private String longitude;

    /**
     * 经度
     */
    private String latitude;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商家编码
     */
    private String companyCode;
}
