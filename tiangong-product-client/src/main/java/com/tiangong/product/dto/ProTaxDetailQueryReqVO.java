package com.tiangong.product.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 税费配置 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ProTaxDetailQueryReqVO {

    /** 税费规则id */
    private Long taxRuleConfigId;

    /** 税费说明 */
    private String taxDesc;

    /** 税费标准 [1:/间、2:/晚] */
    private Integer taxStandard;

    /** 税费加辐规则 [1:加固定值、2:加百分比] */
    private Integer taxIncreaseType;

    /** 税费加辐值 */
    private BigDecimal taxIncreaseValue;

    /** 创建时间 */
    private LocalDateTime[] createdDt;

}
