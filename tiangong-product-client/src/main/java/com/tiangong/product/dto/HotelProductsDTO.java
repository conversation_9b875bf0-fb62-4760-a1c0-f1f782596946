package com.tiangong.product.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: Owen
 * @Date: 2019/6/19 00:02
 * @Description: 酒店产品
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelProductsDTO implements Serializable {

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 是否选中
     */
    private Integer selected;
    /**
     * 日期合计总配额数
     */
    private Integer quota;
    /**
     * 日期合计剩余配额数
     */
    private Integer remainingQuota;
    /**
     * 日期合计已售配额数
     */
    private Integer soldQuota;

    /**
     * 是否映射失败
     * 1 是映射失败， 0 是映射成功
     */
    private Integer mappingFailed;
    /**
     * 房型列表
     */
    private List<ProductRoomDTO> roomList;

}
