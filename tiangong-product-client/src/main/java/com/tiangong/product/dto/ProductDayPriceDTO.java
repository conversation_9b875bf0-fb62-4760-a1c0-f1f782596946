package com.tiangong.product.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Auther: <PERSON>
 * @Date: 2019/6/19 01:15
 * @Description: 产品单日行情
 */
@Data
public class ProductDayPriceDTO {


    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 销售税
     */
    private BigDecimal salesTax;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 其他税费
     */
    private BigDecimal otherTaxFee;


}
