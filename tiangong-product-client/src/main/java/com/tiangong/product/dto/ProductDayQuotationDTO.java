package com.tiangong.product.dto;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: Owen
 * @Date: 2019/6/19 01:15
 * @Description: 产品单日行情
 */
@Data
public class ProductDayQuotationDTO extends BaseDTO {

    /**
     * 产品Id
     */
    private Integer productId;

    /**
     * 配额账号
     */
    private Integer quotaAccountId;

    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 底价
     */
    private BigDecimal modifiedBasePrice;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 销售税
     */
    private BigDecimal salesTax;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 其他税费
     */
    private BigDecimal otherTaxFee;

    /**
     * 房态(-1表示不变 0关房 1开房)
     */
    private Integer roomStatus;

    /**
     * 配额数
     */
    private Integer modifiedQuota;

    /**
     * 配额调整方式（0加 1减 2等于）
     */
    private Integer quotaAdjustmentType;

    /**
     * 底价调整方式（0加 1减 2等于）
     */
    private Integer basePriceAdjustmentType;

    /**
     * 售罄设置（1可超 0不可超 -1表示不变）
     */
    private Integer overDraftStatus;

    /**
     * type 为1，则为扣配额
     */
    private Integer type;

    /**
     * 早餐状态    默认为0    自定义早餐为1
     */
    private Integer breakfastState;

    /**
     * 早餐数量
     */
    private Integer breakfastQty;

    /**
     * 取消条款类型（0一经预订不能取消 1可以取消）
     */
    private Integer cancellationType;

    private Integer cancellationAdvanceDays;
    /**
     * 取消条款提前天数
     */
    private String cancellationDueTime;
    /**
     * 取消条款扣费说明
     */
    private String cancellationDeductionTerm;
    /**
     * 预订天数条款天数
     */
    private Integer reservationLimitNights;
    /**
     * 预订提前天数
     */
    private Integer reservationAdvanceDays;
    /**
     * 预订提前时间点
     */
    private String reservationDueTime;
    /**
     * 预订间数条款间数
     */
    private Integer reservationLimitRooms;
    /**
     * 条款状态（默认设置 0 ；自定义状态 1）
     */
    private Integer reservationTermStatus;

    /**
     * 预订天数条款类型（0大于等于 1小于等于 2等于）
     */
    private Integer comparisonType;

    /**
     * 罚金类型
     * 1. *晚
     * 2. 固定金额
     * 3. 百分比
     * 4. 首晚百分比
     * @see com.tiangong.enums.PenaltiesTypeEnum
     */
    private Integer cancelPenaltiesType;

    /**
     * 罚金值
     */
    private Double cancelPenaltiesValue;

    /**
     * 担保条款
     */
    private List<GuaranteeDTO> guarantees;

}
