package com.tiangong.h5.server;

import com.alibaba.fastjson.JSONObject;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dis.entity.vo.hotel.HotelListPageResponse;
import com.tiangong.dto.hotel.AddressRespDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.h5.domain.req.HotelIdsToPriceReq;
import com.tiangong.h5.domain.req.HotelListPageRequest;
import com.tiangong.h5.service.HotelService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/6 17:43
 */
@RestController
@RequestMapping("/h5/hotel")
public class HotelController extends BaseController {

    @Autowired
    private HotelService hotelService;

    /**
     * 查询酒店列表
     */
    @PostMapping("queryHotelBaseList")
    @PreAuthorize("@syyo.check('h5')")
    public Response<HotelListPageResponse> queryHotelBaseList(@Validated @RequestBody HotelListPageRequest req) {
        req.setLanguage(getLanguage());
        return hotelService.queryHotelBaseList(req);
    }

    /**
     * 查询目的地
     */
    @PostMapping("getAddress")
    @PreAuthorize("@syyo.check('h5')")
    public Response<List<AddressRespDTO>> getAddress(@RequestBody Map<String, String> map) {
        return hotelService.getAddress(map);
    }

    /**
     * 查询目的地酒店
     */
    @PostMapping("searchDestinationHotel")
    @PreAuthorize("@syyo.check('h5')")
    public Response<List<EsHotelDto>> searchDestinationHotel(@RequestBody DestinationReq req) {
        return hotelService.searchDestinationHotel(req);
    }

    /**
     * 查询房型信息
     */
    @PostMapping("queryHotelBaseProducts")
    @PreAuthorize("@syyo.check('h5')")
    public Response<List<JSONObject>> queryHotelBaseProducts(@Validated @RequestBody ProductDetailRequest req) {
        req.setLanguage(getLanguage());
        req.setUserType(getUser().getUserType());
        req.setUserAccount(getUserAccount());
        String token = getToken();
        token = token.substring(token.length() - 32);
        RedisTemplateX.set(RedisKey.TOKEN_RANDOM_ID + getUserAccount(), token);
        RedisTemplateX.set(RedisKey.USER_IPADDRESS + getUserAccount(), getIpAddress());

        return hotelService.queryHotelBaseProducts(req);
    }

    /**
     * 查询酒店详细信息
     */
    @PostMapping("queryHotelDetail")
    @PreAuthorize("@syyo.check('h5')")
    public Response<JSONObject> queryHotelDetail(@Validated @RequestBody HotelInfoReq req) {
        req.setLanguageType(getLanguage());
        return hotelService.queryHotelDetail(req);
    }

    /**
     * 通过酒店id获取起价
     */
    @PostMapping("hotelIdsToPrice")
    @PreAuthorize("@syyo.check('h5')")
    public Response<HotelLowestPriceResponse> hotelIdsToPrice(@Validated @RequestBody HotelIdsToPriceReq req) {
        req.setUserAccount(getUserAccount());
        return hotelService.hotelIdsToPrice(req);
    }
}
