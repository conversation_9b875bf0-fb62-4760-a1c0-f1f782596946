package com.tiangong.h5.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.remote.AreaRemote;
import com.tiangong.dto.hotel.*;
import com.tiangong.organization.remote.CompanyRemote;
import com.tiangong.organization.remote.dto.CompanyAddDTO;
import com.tiangong.organization.remote.dto.CompanySelectDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/6 17:23
 */
@RestController
@RequestMapping("/h5/area")
public class AreaController extends BaseController {

    @Autowired
    private AreaRemote areaRemote;

    @Autowired
    private CompanyRemote companyRemote;

    /**
     * 查询商家详情
     */
    @PostMapping("queryCompanyDetail")
    @PreAuthorize("@syyo.check('h5')")
    public Response<CompanySelectDTO> queryCompanyDetail() {
        CompanyAddDTO companyAddDTO = new CompanyAddDTO();
        companyAddDTO.setCompanyCode(getCompanyCode());
        return companyRemote.queryCompanyDetail(companyAddDTO);
    }

    /**
     * 查询国家列表
     */
    @PostMapping("queryCountryList")
    @PreAuthorize("@syyo.check('h5')")
    public Response<List<BaseinfoAreadataDTO>> queryCountryList(@RequestBody CountryReq req) {
        return areaRemote.queryCountryList(req);
    }

    /**
     * 查询热门城市列表
     */
    @PostMapping("queryHotCityList")
    @PreAuthorize("@syyo.check('h5')")
    public Response<List<HotCityResp>> queryHotCityList(@RequestBody CityReq req) {
        req.setLanguage(getLanguage());
        return areaRemote.queryHotCityList(req);
    }

    /**
     * 查询热门城市列表(新)
     */
    @PostMapping("queryHotelPopularCityList")
    @PreAuthorize("@syyo.check('h5')")
    public Response<List<HotelPopularCityDTO>> queryHotelPopularCityList(@RequestBody HotelPopularCityVO vo) {
        vo.setLanguage(getLanguage());
        return areaRemote.queryHotelPopularCityList(vo);
    }

    /**
     * 查询行政区/商业区
     */
    @PostMapping("queryDistrictOrBusinessList")
    @PreAuthorize("@syyo.check('h5')")
    public Response<DistrictOrBusinessResp> queryDistrictOrBusinessList(@Validated @RequestBody DistrictOrBusinessReq req) {
        return areaRemote.queryBusinessAndDistrictList(req);
    }

    /**
     * 获取国际区号
     */
    @PostMapping("getRegion")
    @AnonymousAccess
    public Response<List<BaseinfoRegionDTO>> getRegion() {
        return areaRemote.getRegion();
    }

    /**
     * 根据关键字获取酒店城市信息
     */
    @PostMapping("queryCityAndHotelByKeyword")
    @AnonymousAccess
    @PreAuthorize("@syyo.check('h5')")
    public Response<List<HotCityResp>> queryCityAndHotelByKeyword(@RequestBody SearchCityAndHotelReq req) {
        req.setLanguage(getLanguage());
        if (StringUtils.isEmpty(req.getKeyWord().trim())) {
            return Response.success(null);
        }
        return areaRemote.queryCityAndHotelByKeyword(req);
    }

    /**
     * 更新时区，转发给基础信息云化
     */
    @PostMapping("updateTimeZone")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> updateTimeZone(@RequestBody UpdateTimeZoneDTO updateTimeZone) {
        return areaRemote.updateTimeZone(updateTimeZone);
    }
}
