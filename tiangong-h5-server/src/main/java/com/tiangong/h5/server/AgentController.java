package com.tiangong.h5.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.h5.domain.req.AddUserAgentLogReq;
import com.tiangong.h5.domain.req.AgentFinanceReq;
import com.tiangong.h5.domain.req.AgentListReq;
import com.tiangong.h5.domain.req.DateConfigReq;
import com.tiangong.h5.domain.resp.AgentFinanceResp;
import com.tiangong.h5.service.AgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/6 13:48
 */
@RestController
@RequestMapping("/h5/agent")
public class AgentController extends BaseController {

    @Autowired
    private AgentService agentService;

    /**
     * 代订--客户列表
     */
    @PostMapping("getAgentList")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Map<String, Object>> getAgentList(@Validated @RequestBody AgentListReq req) throws Exception {
        req.setCompanyCode(getCompanyCode());
        return Response.success(agentService.getAgentList(req));
    }

    /**
     * 根据客户编码获取客户财政信息
     */
    @PostMapping("getAgentFinance")
    @PreAuthorize("@syyo.check('h5')")
    public Response<AgentFinanceResp> getAgentFinance(@Validated @RequestBody AgentFinanceReq req) {
        return Response.success(agentService.getAgentFinance(req));
    }

    /**
     * 新增用户选择客户的记录
     */
    @PostMapping("addUserAgentLog")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> addUserAgentLog(@Validated @RequestBody AddUserAgentLogReq req) {
        req.setUserId(getUserId());
        agentService.addUserAgentLog(req);
        return Response.success();
    }

    /**
     * 获取日期可选范围
     */
    @PostMapping("getDataConfig")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Integer> getDataConfig(@Validated @RequestBody DateConfigReq req) {
        return Response.success(agentService.getDataConfig(req));
    }

}
