package com.tiangong.h5.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.exception.SysException;
import com.tiangong.h5.domain.req.*;
import com.tiangong.h5.domain.resp.UserInfo;
import com.tiangong.h5.domain.resp.UserListResp;
import com.tiangong.h5.enums.ErrorEnum;
import com.tiangong.h5.service.UserService;
import com.tiangong.util.DateUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023/11/6 17:03
 */
@RestController
@RequestMapping("/h5/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    /**
     * 获取用户列表
     */
    @PostMapping("userList")
    @PreAuthorize("@syyo.check('h5')")
    public Response<PaginationSupportDTO<UserListResp>> userList(@Validated @RequestBody UserListReq req) {
        return Response.success(userService.userList(req));
    }

    /**
     * 新增普通用户
     */
    @PostMapping("addUser")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> addUser(@Validated @RequestBody AddOrUpdateUserReq req) throws Exception {
        req.setCreatedBy(getUserName());
        req.setCreatedDt(DateUtilX.getDateStr());
        req.setUserType(getUser().getUserType());
        userService.addUser(req);
        return Response.success();
    }

    /**
     * 修改用户
     */
    @PostMapping("updateUser")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> updateUser(@Validated @RequestBody AddOrUpdateUserReq req) throws Exception {
        req.setUpdatedBy(getUserName());
        req.setCreatedDt(DateUtilX.getDateStr());
        req.setUserType(getUser().getUserType());
        userService.updateUser(req);
        return Response.success();
    }

    /**
     * 重置密码
     */
    @PostMapping("resetPassword")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> resetPassword(@Validated @RequestBody ResetPasswordReq req) {
        req.setUpdatedBy(getUserName());
        req.setUserType(getUser().getUserType());
        userService.resetPassword(req);
        return Response.success();
    }

    /**
     * 删除用户
     */
    @PostMapping("delUser")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> delUser(@Validated @RequestBody DelUserReq req) {
        req.setUserType(getUser().getUserType());
        userService.delUser(req);
        return Response.success();
    }

    /**
     * 校验账号是否存在
     */
    @PostMapping("userAccountExist")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> userAccountExist(@Validated @RequestBody UserAccountExistReq req) throws Exception {
        Boolean exist = userService.userAccountExist(req.getUserAccount(), req.getUserId());
        if (!exist) {
            throw new SysException(ErrorEnum.USER_ACCOUNT_IS_ALREADY.type, ErrorEnum.USER_ACCOUNT_IS_ALREADY.content);
        }
        return Response.success();
    }

    /**
     * 修改密码
     */
    @PostMapping("updatePassword")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> updatePassword(@Validated @RequestBody UpdatePasswordReq req) {
        req.setUserId(getUserId());
        userService.updatePassword(req);
        return Response.success();
    }

    /**
     * 修改邮箱
     */
    @PostMapping("updateEmail")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> updateEmail(@Validated @RequestBody UpdateEmailReq req) {
        req.setUserId(getUserId());
        req.setUserType(getUser().getUserType());
        userService.updateEmail(req);
        return Response.success();
    }

    /**
     * 修改手机号
     */
    @PostMapping("updatePhone")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> updatePhone(@Validated @RequestBody UpdatePhoneReq req) throws Exception {
        req.setUserType(getUser().getUserType());
        req.setUserId(getUserId());
        userService.updatePhone(req);
        return Response.success();
    }

    /**
     * 获取登录用户信息
     * 假如是商户登录，则返回客户管理员信息
     */
    @PostMapping("userInfo")
    @PreAuthorize("@syyo.check('h5')")
    public Response<UserInfo> userInfo(@RequestBody UserInfoReq req) {
        req.setUserId(getUserId());
        return Response.success(userService.userInfo(req));
    }

    /**
     * 退出登录
     */
    @PostMapping("loginBack")
    @PreAuthorize("@syyo.check('h5')")
    public Response<Object> loginBack() {
        userService.loginBack(getToken());
        return Response.success();
    }
}
