package com.tiangong.h5.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.h5.domain.req.OrgCenterReq;
import com.tiangong.h5.domain.resp.OrgCenterResp;
import com.tiangong.h5.domain.resp.OrgInfoResp;
import com.tiangong.h5.service.OrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023/11/6 14:42
 */
@RestController
@RequestMapping("/h5/organization")
public class OrganizationController extends BaseController {

    @Autowired
    private OrganizationService organizationService;

    /**
     * 企业中心
     */
    @PostMapping("orgCenter")
    @PreAuthorize("@syyo.check('h5')")
    public Response<OrgCenterResp> orgCenter(@Validated @RequestBody OrgCenterReq req) {
        return Response.success(organizationService.orgCenter(req.getAgentCode()));
    }

    /**
     * 企业信息详情
     */
    @PostMapping("orgInfo")
    @AnonymousAccess
    @PreAuthorize("@syyo.check('h5')")
    public Response<OrgInfoResp> orgInfo(@Validated @RequestBody OrgCenterReq req) {
        return Response.success(organizationService.orgInfo(req.getAgentCode()));
    }
}
