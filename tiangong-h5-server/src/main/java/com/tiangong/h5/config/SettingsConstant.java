package com.tiangong.h5.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 系统配置类
 * <p>
 * 全局参数配置
 * @RefreshScope 实时更新nacos配置
 */
@Data
@Configuration
@RefreshScope
public class SettingsConstant {
    /**
     * 注意设施
     */
    @Value("${policy.travelNoticePolicy}")
    private String travelNoticePolicy;
    /**
     * 创建pdf文件路径
     */
    @Value("${create.voucherUrl}")
    private String createVoucherUrl;
    /**
     * 是否允许同一个账号多地登录：0否 1是
     */
    @Value("${account.token}")
    private int accountToken;

    @Value("${orderVoucher.pdf.fontUrl}")
    private String fontUrl;

    /**
     * 交通服务
     */
    @Value("${facilityIds.transportationIds}")
    private int[] transportationIds;
    /**
     * 清洁服务
     */
    @Value("${facilityIds.cleaningIds}")
    private int[] cleaningIds;
    /**
     * 公共区
     */
    @Value("${facilityIds.publicIds}")
    private int[] publicIds;
    /**
     * 前台服务
     */
    @Value("${facilityIds.frontDeskIds}")
    private int[] frontDeskIds;
    /**
     * 娱乐活动浏览
     */
    @Value("${facilityIds.playIds}")
    private int[] playIds;
    /**
     * 无障碍设施服务
     */
    @Value("${facilityIds.accessibilityIds}")
    private int[] accessibilityIds;
    /**
     * 通用设施
     */
    @Value("${facilityIds.beCurrentIds}")
    private int[] beCurrentIds;
    /**
     * 商务服务
     */
    @Value("${facilityIds.businessIds}")
    private int[] businessIds;
    /**
     * 餐饮服务
     */
    @Value("${facilityIds.restaurantServerIds}")
    private int[] restaurantServerIds;
    /**
     * 康体设施
     */
    @Value("${facilityIds.wellnessIds}")
    private int[] wellnessIds;
    /**
     * 儿童设施
     */
    @Value("${facilityIds.childrenIds}")
    private int[] childrenIds;
    /**
     * 其他服务
     */
    @Value("${facilityIds.otherIds}")
    private int[] otherIds;

    /**
     * 洗浴用品
     */
    @Value("${facilityIds.toiletriesIds}")
    private int[] toiletriesIds;
    /**
     * 客房设施
     */
    @Value("${facilityIds.guestRoomFacilitiesIds}")
    private int[] guestRoomFacilitiesIds;
    /**
     * 媒体科技
     */
    @Value("${facilityIds.technologyIds}")
    private int[] technologyIds;
    /**
     * 厨房用品
     */
    @Value("${facilityIds.kitchenSuppliesIds}")
    private int[] kitchenSuppliesIds;
    /**
     * 特色设施
     */
    @Value("${facilityIds.specialFacilitiesIds}")
    private int[] specialFacilitiesIds;
    /**
     * 室外景观
     */
    @Value("${facilityIds.outdoorLandscapeIds}")
    private int[] outdoorLandscapeIds;
    /**
     * 无障碍设施
     */
    @Value("${facilityIds.barrierFreeFacilitiesIds}")
    private int[] barrierFreeFacilitiesIds;
    /**
     * 儿童设施服务
     */
    @Value("${facilityIds.childrenFacilityServicesIds}")
    private int[] childrenFacilityServicesIds;
    /**
     * 便利设施
     */
    @Value("${facilityIds.amenityIds}")
    private int[] amenityIds;
    /**
     * 其他
     */
    @Value("${facilityIds.otherServiceIds}")
    private int[] otherServiceIds;
    /**
     * 食品饮品
     */
    @Value("${facilityIds.foodAndBeveragesIds}")
    private int[] foodAndBeveragesIds;
    /**
     * 浴室
     */
    @Value("${facilityIds.bathroomIds}")
    private int[] bathroomIds;

    // 酒店信息配置

    @Value("${b2b.domian}")
    private String b2bDomain;
    /**
     * 基础信息路径
     */
    @Value("${server.baseHotelUrl}")
    private String baseHotelUrl;
}
