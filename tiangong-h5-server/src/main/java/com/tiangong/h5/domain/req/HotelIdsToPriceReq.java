package com.tiangong.h5.domain.req;

import com.tiangong.dto.product.request.RoomGuestNumber;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/14 14:48
 */
@Data
public class HotelIdsToPriceReq {

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 酒店id
     */
//    @NotEmpty(message = "酒店id不能为空")
//    private List<Integer> hotelIds;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 酒店id
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;

    /**
     * 只查钟点房	Integer	否	1是0否 默认0
     */
    private Integer onlyHourRoom;

    /*B2B实时查询参数-start*/

    /**
     * 供应类型 1:国内 2:海外
     */
    private String supplyType;

    /**
     * 请求来源
     * 1：Dhub
     * 2：B2B
     * 默认：Dhub
     */
    private Integer source;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;
    /*B2B实时查询参数-end*/

    /**
     * 登陆账号
     */
    private String userAccount;
}
