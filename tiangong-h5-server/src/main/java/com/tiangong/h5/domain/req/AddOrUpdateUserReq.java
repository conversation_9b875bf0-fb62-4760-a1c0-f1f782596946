package com.tiangong.h5.domain.req;

import com.tiangong.dto.common.BasePO;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/11/3 16:16
 */
@Data
@SensitiveClass
public class AddOrUpdateUserReq extends BasePO {

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名称
     */
    @NotEmpty(message = "EMPTY_PARAM_USERNAME")
    private String userName;

    /**
     * 用户账号
     */
    @NotEmpty(message = "EMPTY_PARAM_USERACCOUNT")
    private String userAccount;

    /**
     * 用户手机号
     */
    @NotEmpty(message = "EMPTY_PARAM_TEL")
    private String userTel;

    /**
     * 用户邮箱
     */
    @Email(message = "PARAM_FORMAT_EMAIL_ERROR")
    private String email;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 是否启用 0否 1是
     */
    @NotNull(message = "EMPTY_PARAM_AGENTCODE")
    private Integer availableStatus;

    /**
     * 用户身份，详见UserTypeEnum
     */
    private Integer userType;

}
