package com.tiangong.h5.domain.req;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/11/3 16:04
 */
@Data
public class LoginReq {

    /**
     * 用户账号
     */
    @NotEmpty(message = "EMPTY_PARAM_USERACCOUNT")
    private String userAccount;

    /**
     * 密码
     */
    private String userPassword;

    /**
     * 验证码
     */
    private String verificationCode;

    /**
     * 登录类型 1密码登录 2验证码登录 详情见LoginTypeEnum
     */
    @NotNull(message = "EMPTY_PARAM_LOGINTYPE")
    private Integer loginType;

    /**
     * 客户ip地址
     */
    private String ipAddress;
}
