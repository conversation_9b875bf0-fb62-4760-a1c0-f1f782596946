package com.tiangong.h5.domain.resp;

import com.tiangong.dto.sensitive.Sensitive;
import com.tiangong.dto.sensitive.SensitiveTypeEnum;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/11/6 15:23
 */
@Data
@SensitiveClass
public class OrgInfoResp {
    /**
     * 企业名称
     */
    private String orgName;

    /**
     * 企业编码
     */
    private String orgCode;

    /**
     * 企业域名
     */
    private String orgDomain;

    /**
     * 企业电话
     */
    private String orgTel;

    /**
     * 结算类型
     */
    private Integer settlementType;

    /**
     * 结算币种
     */
    private Integer settlementCurrency;

    /**
     * 币种名
     */
    private String currencyName;

    /**
     * 国家名
     */
    private String countryName;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 企业地址
     */
    private String orgAddress;

    /**
     * 管理员姓名
     */
    private String adminName;

    /**
     * 管理员账号
     */
    private String adminAccount;

    /**
     * 管理员手机号
     */
    @EncryptField
    private String adminTel;

}
