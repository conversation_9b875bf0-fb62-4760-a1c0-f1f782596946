package com.tiangong.h5.domain.resp;

import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/11/3 16:16
 */
@Data
@SensitiveClass
public class UserListResp {

    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户手机号
     */
    private String userTel;

    /**
     * 手机号码区号
     */
    private String countryCode;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 是否是管理员 1是
     */
    private Integer isSuperAdmin;

    /**
     * 是否启用 0否 1是
     */
    private Integer availableStatus;
}
