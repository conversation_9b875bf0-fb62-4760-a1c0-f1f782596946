package com.tiangong.h5.domain.req;

import com.tiangong.dto.common.BusinessRequest;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.request.CreateOrderPriceItem;
import com.tiangong.dto.order.request.GuestInfo;
import com.tiangong.dto.order.request.OrderTrafficJson;
import com.tiangong.dto.product.HourlyRoomDetail;
import com.tiangong.dto.product.response.BedInfoDto;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/30 11:14
 */
@Data
public class AddOrderReq extends BusinessRequest {
    /**
     * 酒店id
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 房型id
     */
    @NotNull(message = "EMPTY_PARAM_ROOMID")
    private Long roomId;

    /**
     * 价格计划id
     */
    @NotEmpty(message = "EMPTY_PARAM_RATEPLANID")
    private String ratePlanId;

    /**
     * 入住日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;

    /**
     * 预订间数--------
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private Integer roomQty;

    /**
     * 床型明细
     */
    private List<BedInfoDto> bedInfos;

    /**
     * 床型描述
     * 不涉及多语言
     * 2024-07-29 author:湫
     * qc 973 【天宫&B2B-EPS】产品的床型信息传递
     */
    private String bedInfoDesc;

    /**
     * 总金额------------------
     */
    @NotNull(message = "EMPTY_PARAM_TOTALAMOUNT")
    private BigDecimal orderAmt;


    /**
     * 订单每日价格详情
     */
    @Valid
    @Size(min = 1, message = "EMPTY_PARAM_PRICEITEMS")
    private List<CreateOrderPriceItem> priceItems;

    /**
     * 入住人列表
     */
    private List<GuestInfo> guestInfos;

    /**
     * 入住人，按每间房传入
     */
    private List<OrderRoomGuestDTO> roomGuestList;

    /**
     * 订单联系人---------
     */
    @NotEmpty(message = "EMPTY_PARAM_LINKMAN")
    private String contactName;

    /**
     * 联系人电话-------
     */
    @NotEmpty(message = "EMPTY_PARAM_LINKPHONE")
    private String contactPhone;

    /**
     * 手机号区号
     */
    @NotEmpty(message = "EMPTY_PARAM_LINKCOUNTRYCODE")
    private String linkCountryCode;

    /**
     * 邮箱----------
     */
    @NotEmpty(message = "EMPTY_PARAM_EMAIL")
    private String contactEmail;

    /**
     * 到店时间
     */
    @NotEmpty(message = "EMPTY_PARAM_ARRIVETIME")
    private String arriveTime;

    /**
     * 特殊要求------
     */
    private String specialRequest;


    /**
     * 是否到店付(1-是，0或其它-否)
     */
    @NotNull(message = "EMPTY_PARAM_PAYATHOTELFLAG")
    private Integer payAtHotelFlag;

    /**
     * 是否担保标志 ,1担保 0未担保
     */
    private Integer guaranteeFlag;


    /**
     * 入住人数
     */
    @NotNull(message = "EMPTY_PARAM_GUESTQUANTITY")
    private Integer guestQuantity;

    /**
     * 是否钟点房	Integer	1是0否 空代表否
     */
    private int hourlyRoom;

    /**
     * 钟点房日期对象
     */
    private HourlyRoomDetail hourlyRoomDetail;

    /**
     * 下单人
     */
    private String createdBy;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplyCode;

    /**
     * 供应商类型
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYTYPE")
    private String supplyType;

    private String language;

    /**
     * 用户类型 1商户 2管理员 3员工
     */
    private Integer userType;

    /**
     * 下单人
     */
    private String userAccount;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 订单支付状态
     */
    @NotNull(message = "EMPTY_PARAM_PAYSTATUS")
    private Integer payStatus;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 出行类型，1-因公，2-因私 非必填
     */
    private Integer travelType;

    /**
     * 客户下属单位名称
     */
    private String sdistributorName;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 全部床型
     */
    private String allBedType;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 价格礼包
     */
    private String giftPacks;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    /**
     * 交通方式
     */
    private OrderTrafficJson orderTrafficJson;

    /**
     * 国籍
     */
    private String nationality;
}
