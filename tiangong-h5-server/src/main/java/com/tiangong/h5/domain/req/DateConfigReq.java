package com.tiangong.h5.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/1/23 11:13
 */
@Data
public class DateConfigReq {

    /**
     * 1国内 2海外
     */
    @NotNull(message = "EMPTY_PARAM_DOMESTICOROVERSEAS")
    private Integer supplyType;

    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;
}
