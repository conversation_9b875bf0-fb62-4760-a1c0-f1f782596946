package com.tiangong.h5.domain.req;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/11/6 10:07
 */
@Data
public class UpdatePhoneReq {


    @NotEmpty(message = "EMPTY_PARAM_TEL")
    private String userTel;

    @NotEmpty(message = "EMPTY_PARAM_VERIFICATIONCODE")
    private String verificationCode;

    @NotEmpty(message = "EMPTY_PARAM_OLDTEL")
    private String oldUserTel;

    /**
     * 用户id
     */
    private Integer userId;

    private Integer userType;
}
