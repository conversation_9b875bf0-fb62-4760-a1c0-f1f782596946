package com.tiangong.h5.domain.req;

import com.tiangong.dto.common.BusinessRequest;
import com.tiangong.dto.product.request.RoomGuestNumber;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/30 14:37
 */
@Data
public class CheckBookingReq extends BusinessRequest {
    /**
     * 酒店id
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 房型id
     */
    @NotNull(message = "EMPTY_PARAM_ROOMID")
    private Long roomId;

    /**
     * 价格计划id
     */
    @NotNull(message = "EMPTY_PARAM_RATEPLANID")
    private String ratePlanId;

    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;

    /**
     * 间数
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private Integer roomQty;

    /**
     * 供应商编码
     */
    private String supplyCode;

    /**
     * 入住人数
     */
    private Integer guestQuantity;

    /**
     * 供应商类型 1:国内 2:海外
     */
    private String supplyType;

    /**
     * 请求来源
     * 1：Dhub
     * 2：B2B
     * 默认：Dhub
     */
    private Integer source;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;

    /**
     * 售价
     */
    private BigDecimal orderAmt;

    /**
     * 登陆账号
     */
    private String userAccount;

    /**
     * 国籍
     */
    private String nationality;
}
