package com.tiangong.h5.domain.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/11/6 14:45
 */
@Data
public class OrgCenterResp {

    /**
     * 企业名称
     */
    private String orgName;

    /**
     * 企业编码
     */
    private String orgCode;

    /**
     * 结算类型
     */
    private Integer settlementType;

    /**
     * 结算币种
     */
    private Integer settlementCurrency;

    /**
     * 币名
     */
    private String currencyName;

    /**
     * 国家名
     */
    private String countryName;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 总信用额度
     */
    private BigDecimal creditLine;

    /**
     * 剩余信用额度
     */
    private BigDecimal balance;

    /**
     * 用户数量
     */
    private Integer userNum;
}
