package com.tiangong.h5.service;

import com.alibaba.fastjson.JSONObject;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dis.entity.vo.hotel.HotelListPageResponse;
import com.tiangong.dto.hotel.AddressRespDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.h5.domain.req.HotelIdsToPriceReq;
import com.tiangong.h5.domain.req.HotelListPageRequest;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @create 2023/11/9 14:59
 */
public interface HotelService {

    /**
     * 查询酒店基础信息列表
     *
     * @param req 酒店列表分页请求，包含酒店基础信息查询和分页参数
     * @return 返回酒店列表分页响应的响应结果
     */
    Response<HotelListPageResponse> queryHotelBaseList(HotelListPageRequest req);

    /**
     * 查询目的地
     *
     * @param map 参数映射，包含目的地查询参数
     * @return 返回地址响应DTO列表的响应结果
     */
    Response<List<AddressRespDTO>> getAddress(Map<String, String> map);

    /**
     * 查询目的地酒店
     *
     * @param req 目的地请求，包含目的地酒店查询参数
     * @return 返回ES酒店DTO列表的响应结果
     */
    Response<List<EsHotelDto>> searchDestinationHotel(DestinationReq req);

    /**
     * 查询酒店房型信息
     *
     * @param request 产品详情请求，包含酒店房型查询参数
     * @return 返回酒店房型JSON对象列表的响应结果
     */
    Response<List<JSONObject>> queryHotelBaseProducts(ProductDetailRequest request);

    /**
     * 查询酒店详细信息
     *
     * @param request 酒店信息请求，包含酒店详情查询参数
     * @return 返回酒店详细信息JSON对象的响应结果
     */
    Response<JSONObject> queryHotelDetail(HotelInfoReq request);

    /**
     * 根据酒店id获取起价
     *
     * @param req 酒店ID到价格请求，包含酒店ID列表
     * @return 返回酒店最低价格响应的响应结果
     */
    Response<HotelLowestPriceResponse> hotelIdsToPrice(HotelIdsToPriceReq req);
}
