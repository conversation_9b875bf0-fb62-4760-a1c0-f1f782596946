package com.tiangong.h5.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.h5.domain.po.UserPO;
import com.tiangong.h5.domain.req.*;
import com.tiangong.h5.domain.resp.UserInfo;
import com.tiangong.h5.domain.resp.UserListResp;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/3 15:59
 */
public interface UserService extends IService<UserPO> {

    /**
     * 登录
     */
    Response<Map<String, Object>> login(HttpServletRequest request, LoginReq req) throws Exception;

    /**
     * 获取验证码
     */
    Response<Object> getCode(SendCode sendCode) throws Exception;

    /**
     * 忘记密码
     */
    Response<Object> forgetPassword(ForgetPasswordReq req) throws Exception;

    /**
     * 修改密码
     */
    void updatePassword(UpdatePasswordReq req);

    /**
     * 修改邮箱
     */
    void updateEmail(UpdateEmailReq req);

    /**
     * 修改手机号
     */
    void updatePhone(UpdatePhoneReq req) throws Exception;

    /**
     * 获取用户列表
     */
    PaginationSupportDTO<UserListResp> userList(UserListReq req);

    /**
     * 修改用户
     */
    void updateUser(AddOrUpdateUserReq req) throws Exception;

    /**
     * 删除用户
     */
    void delUser(DelUserReq req);

    /**
     * 重置密码
     */
    void resetPassword(ResetPasswordReq req);

    /**
     * 新增用户
     */
    void addUser(AddOrUpdateUserReq req) throws Exception;

    /**
     * 账号或手机号验重
     */
    Boolean userAccountExist(String userAccount, Integer userId) throws Exception;

    /**
     * 获取登录用户的基础信息
     */
    UserInfo userInfo(UserInfoReq req);

    /**
     * 退出登录
     */
    void loginBack(String token);
}
