package com.tiangong.h5.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.exception.SysException;
import com.tiangong.h5.domain.po.UserAgentLogPO;
import com.tiangong.h5.domain.po.UserPO;
import com.tiangong.h5.domain.req.AddUserAgentLogReq;
import com.tiangong.h5.domain.req.AgentFinanceReq;
import com.tiangong.h5.domain.req.AgentListReq;
import com.tiangong.h5.domain.req.DateConfigReq;
import com.tiangong.h5.domain.resp.AgentFinanceResp;
import com.tiangong.h5.enums.ErrorEnum;
import com.tiangong.h5.mapper.AgentMapper;
import com.tiangong.h5.mapper.OrganizationMapper;
import com.tiangong.h5.mapper.UserAgentLogMapper;
import com.tiangong.h5.mapper.UserMapper;
import com.tiangong.h5.service.AgentService;
import com.tiangong.sensitive.EncryptUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/6 13:56
 */
@Service
public class AgentServiceImpl implements AgentService {

    @Resource
    private AgentMapper agentMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserAgentLogMapper userAgentLogMapper;

    @Resource
    private OrganizationMapper organizationMapper;

    @Override
    public Map<String, Object> getAgentList(AgentListReq req) throws Exception {
        //判断用户是否是商户
        UserPO userPo = userMapper.selectOne(new LambdaQueryWrapper<UserPO>()
                .eq(UserPO::getUserAccount, req.getUserAccount())
                .or()
                .eq(UserPO::getUserTel, EncryptUtil.encrypt(req.getUserAccount()))
                .last(" limit 1")
        );
        if (userPo == null) {
            throw new SysException(ErrorEnum.USER_ACCOUNT_IS_NOT_EXIST.type, ErrorEnum.USER_ACCOUNT_IS_NOT_EXIST.content);
        }

        Map<String, Object> map = new HashMap<>(2);
        //不是商户号登录不需要选择客户列表 agentState: 1表示客户登录 2表示商户登录
        if (!CompanyDTO.COMPANY_CODE.equals(userPo.getOrgCode())) {
            map.put("agentState", 1);
            map.put("agentList", agentMapper.agentListByAgentCode(userPo.getOrgCode()));
            map.put("agentLogList", null);
            return map;
        }

        //是商户号的话就返回客户列表
        map.put("agentState", 2);
        map.put("agentList", agentMapper.agentListByCompanyCode(req.getCompanyCode()));
        map.put("agentLogList", agentMapper.agentListByAgentCodeLog(userPo.getUserId()));

        return map;
    }

    @Override
    public AgentFinanceResp getAgentFinance(AgentFinanceReq req) {
        AgentFinanceResp financeResp = agentMapper.agentFinanceByAgentCode(req.getAgentCode());
        if (financeResp == null) {
            throw new SysException(ErrorEnum.AGENT_CODE_ERROR.type, ErrorEnum.AGENT_CODE_ERROR.content);
        }
        financeResp.setSaleCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(financeResp.getSettlementCurrency())));
        return financeResp;
    }

    @Override
    public void addUserAgentLog(AddUserAgentLogReq req) {
        UserAgentLogPO userAgentLogPo = userAgentLogMapper.selectOne(new LambdaQueryWrapper<UserAgentLogPO>()
                .eq(UserAgentLogPO::getUserId, req.getUserId())
                .eq(UserAgentLogPO::getAgentCode, req.getAgentCode())
                .last("limit 1")
        );
        if (userAgentLogPo == null) {
            userAgentLogPo = new UserAgentLogPO();
            userAgentLogPo.setUserId(req.getUserId());
            userAgentLogPo.setAgentCode(req.getAgentCode());
            userAgentLogPo.setUpdatedDt(new Date());
            userAgentLogMapper.insert(userAgentLogPo);
        } else {
            userAgentLogPo.setUpdatedDt(new Date());
            userAgentLogMapper.updateById(userAgentLogPo);
        }
    }

    @Override
    public Integer getDataConfig(DateConfigReq dateConfigReq) {
        return organizationMapper.getQuoteSwitch(dateConfigReq.getAgentCode());
    }
}
