package com.tiangong.h5.service;

import com.tiangong.h5.domain.req.AddUserAgentLogReq;
import com.tiangong.h5.domain.req.AgentFinanceReq;
import com.tiangong.h5.domain.req.AgentListReq;
import com.tiangong.h5.domain.req.DateConfigReq;
import com.tiangong.h5.domain.resp.AgentFinanceResp;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/6 13:55
 */
public interface AgentService {

    /**
     * 根据商家编码获取客户列表
     * 查询指定商家下的所有可用客户列表，支持分页和搜索功能
     * @param req 客户列表查询请求对象，包含商家编码、分页参数等
     * @return 客户列表数据，包含客户基本信息和分页信息
     * @throws Exception 查询过程中可能抛出的异常
     */
    Map<String, Object> getAgentList(AgentListReq req) throws Exception;

    /**
     * 根据客户编码获取客户财务信息
     * 查询指定客户的财务相关信息，如余额、额度、结算方式等
     * @param req 客户财务查询请求对象，包含客户编码
     * @return 客户财务信息响应对象
     */
    AgentFinanceResp getAgentFinance(AgentFinanceReq req);

    /**
     * 新增用户选择客户的记录
     * 记录用户选择客户的操作日志，用于后续的客户推荐和历史记录
     * @param req 用户客户选择记录请求对象，包含用户ID和客户编码
     */
    void addUserAgentLog(AddUserAgentLogReq req);

    /**
     * 获取日期配置范围
     * 根据配置类型获取对应的日期范围限制，如查询天数限制等
     * @param dateConfigReq 日期配置查询请求对象，包含配置类型
     * @return 日期范围配置值
     */
    Integer getDataConfig(DateConfigReq dateConfigReq);
}
