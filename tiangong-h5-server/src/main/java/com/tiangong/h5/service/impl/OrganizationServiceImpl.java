package com.tiangong.h5.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.exception.SysException;
import com.tiangong.h5.domain.po.UserPO;
import com.tiangong.h5.domain.resp.OrgCenterResp;
import com.tiangong.h5.domain.resp.OrgInfoResp;
import com.tiangong.h5.enums.ErrorEnum;
import com.tiangong.h5.mapper.OrganizationMapper;
import com.tiangong.h5.mapper.UserMapper;
import com.tiangong.h5.service.OrganizationService;
import com.tiangong.util.StrUtilX;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2023/11/6 14:44
 */
@Service
public class OrganizationServiceImpl implements OrganizationService {

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private UserMapper userMapper;

    @Override
    public OrgCenterResp orgCenter(String agentCode) {
        if (agentCode.equals(CompanyDTO.COMPANY_CODE)) {
            throw new SysException(ErrorEnum.AGENT_CODE_IS_ERROR.type, ErrorEnum.AGENT_CODE_IS_ERROR.content);
        }
        OrgCenterResp orgCenterResp = organizationMapper.organizationCenter(agentCode);

        //获取该客户下的所有可登录用户
        Integer userNum = userMapper.selectCount(new LambdaQueryWrapper<UserPO>().eq(UserPO::getOrgCode, agentCode));
        orgCenterResp.setUserNum(userNum);

        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgCenterResp.getSettlementCurrency()));
        if (StrUtilX.isEmpty(currency)) {
            orgCenterResp.setCurrencyName("CNY");
        } else {
            orgCenterResp.setCurrencyName(currency);
        }

        return orgCenterResp;
    }

    @Override
    public OrgInfoResp orgInfo(String agentCode) {
        OrgInfoResp orgInfoResp = organizationMapper.organizationInfo(agentCode);
        //获取币种
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgInfoResp.getSettlementCurrency()));
        if (StrUtilX.isEmpty(currency)) {
            orgInfoResp.setCurrencyName("CNY");
        } else {
            orgInfoResp.setCurrencyName(currency);
        }
        return orgInfoResp;
    }
}
