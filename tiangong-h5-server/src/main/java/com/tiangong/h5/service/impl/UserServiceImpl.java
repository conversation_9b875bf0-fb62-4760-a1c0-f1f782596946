package com.tiangong.h5.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dis.remote.SysConfigRemote;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.common.SysConfigDTO;
import com.tiangong.dto.common.SysConfigReq;
import com.tiangong.enums.AvailableEnum;
import com.tiangong.enums.EmployeeAccountStatusEnum;
import com.tiangong.enums.UserTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.h5.config.SettingsConstant;
import com.tiangong.h5.convert.DTOConvert;
import com.tiangong.h5.domain.po.UserPO;
import com.tiangong.h5.domain.req.*;
import com.tiangong.h5.domain.resp.LoginResp;
import com.tiangong.h5.domain.resp.UserInfo;
import com.tiangong.h5.domain.resp.UserListResp;
import com.tiangong.h5.enums.ErrorEnum;
import com.tiangong.h5.enums.LoginTypeEnum;
import com.tiangong.h5.mapper.AgentMapper;
import com.tiangong.h5.mapper.UserMapper;
import com.tiangong.h5.service.UserService;
import com.tiangong.h5.util.ValidatedFilter;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.req.SendPhoneReq;
import com.tiangong.sensitive.EncryptUtil;
import com.tiangong.util.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2023/11/3 16:00
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserPO> implements UserService {

    /**
     * 中国电话区号
     */
    private static final String CHINA_PHONE_CODE = "86";

    @Autowired
    private TokenManager tokenManager;

    @Autowired
    private SendCodeUtil sendCodeUtil;

    @Resource
    private AgentMapper agentMapper;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private SysConfigRemote sysConfigRemote;

    @Override
    public Response<Map<String, Object>> login(HttpServletRequest request, LoginReq req) throws Exception {
        //判断用户是否存在
        UserPO one = this.getOne(new LambdaQueryWrapper<UserPO>()
//                .eq(UserPO::getAccountStatus, EmployeeAccountStatusEnum.VALID.no)
//                .eq(UserPO::getAvailableStatus, AvailableEnum.Start.key)
//                .eq(UserPO::getActive, 1)
                        .eq(UserPO::getUserAccount, req.getUserAccount())
                        .or()
                        .eq(UserPO::getUserTel, EncryptUtil.encrypt(req.getUserAccount()))
                        .last("limit 1")
        );
        if (one == null) {
            return Response.error(ErrorEnum.USER_ACCOUNT_IS_NOT_EXIST.type, ErrorEnum.USER_ACCOUNT_IS_NOT_EXIST.content);
        }
        if (one.getAccountStatus() != EmployeeAccountStatusEnum.VALID.no || one.getAvailableStatus().intValue() != AvailableEnum.Start.key.intValue() || one.getActive() != 1) {
            return Response.error(ErrorEnum.ACCOUNT_IS_DISABLE.type, ErrorEnum.ACCOUNT_IS_DISABLE.content);
        }

        //判断客户是否被禁用
        if (!CompanyDTO.COMPANY_CODE.equals(one.getOrgCode())) {
            String agentStatusByAgentCode = agentMapper.getAgentStatusByAgentCode(one.getOrgCode());
            if (agentStatusByAgentCode == null) {
                return Response.error(ErrorEnum.AGENT_IS_DISABLE.type, ErrorEnum.AGENT_IS_DISABLE.content);
            }
        }

        //判断用户是否锁定了
        String value = RedisTemplateX.get(RedisKey.USER_IS_LOCK.concat(one.getUserTel()));
        if (value != null) {
            return Response.error(ErrorEnum.USER_IS_LOCK.type, ErrorEnum.USER_IS_LOCK.content);
        }

        //区分密码登录和验证码登录
        if (req.getLoginType() == LoginTypeEnum.PASSWORD_LOGIN.type) {
            if (StringUtils.isEmpty(req.getUserPassword())) {
                return Response.error(ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.type, ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.content);
            }

            //校验密码
            String encryptUserPassword = SM4Utils.encrypt(req.getUserPassword(), Sm4O.defaultKey);
            if (!one.getUserPwd().equals(encryptUserPassword)) {
                //记录密码错误次数--统一使用用户手机号来存储
                ValidatedFilter.loginFailRecord(one.getUserTel());

                //记录日志
                return Response.error(ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.type, ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.content);
            }

        } else if (req.getLoginType() == LoginTypeEnum.CODE_LOGIN.type) {
            if (StringUtils.isEmpty(req.getVerificationCode())) {
                return Response.error(ErrorEnum.CODE_IS_NOT_NULL.type, ErrorEnum.CODE_IS_NOT_NULL.content);
            }

            //校验验证码
            String code = RedisTemplateX.get(RedisKey.CODE_KEY.concat(req.getUserAccount()));
            if (code == null) {
                return Response.error(ErrorEnum.CODE_IS_NOT_EXIST.type, ErrorEnum.CODE_IS_NOT_EXIST.content);
            }
            if (!code.equals(req.getVerificationCode())) {
                //记录验证码错误次数--统一使用用户手机号来存储
                ValidatedFilter.loginFailRecord(one.getUserTel());

                //记录日志
                return Response.error(ErrorEnum.CODE_IS_ERROR.type, ErrorEnum.CODE_IS_ERROR.content);
            }

            //验证通过，清除验证码
            RedisTemplateX.delete(RedisKey.CODE_KEY.concat(req.getUserAccount()));
        } else {
            return Response.error(ErrorEnum.LOGIN_TYPE_ERROR.type, ErrorEnum.LOGIN_TYPE_ERROR.content);
        }

        //登录成功
        LoginResp loginResp = this.getBaseMapper().queryUserLogin(one.getUserAccount());


        //判断用户身份
        if (CompanyDTO.COMPANY_CODE.equals(loginResp.getAgentCode())) {
            loginResp.setUserType(UserTypeEnum.SUPER_ADMIN.type);
        } else if (loginResp.getIsSuperAdmin() != null && loginResp.getIsSuperAdmin() == 1) {
            loginResp.setUserType(UserTypeEnum.ADMIN.type);
        } else {
            loginResp.setUserType(UserTypeEnum.NORMAL.type);
        }

        // 创建用户基础信息
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(loginResp.getUserId());
        loginUser.setUserName(loginResp.getUserName());
        loginUser.setUserAccount(loginResp.getUserAccount());
        loginUser.setCompanyCode(CompanyDTO.COMPANY_CODE);
        loginUser.setIsSuperAdmin(loginResp.getIsSuperAdmin());
        loginUser.setUserType(loginResp.getUserType());

        String user = JSON.toJSONString(loginUser);
        // 生成令牌
        String token = tokenManager.createToken(user);


        // 没有用到roles，存ip
        if (settingsConstant.getAccountToken() == 0) {
            String result = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, one.getUserAccount()));
            RedisTemplateX.delete(result);

        }
        RedisTemplateX.setAndExpire(RedisKey.LOGIN_TOKEN + token, IpUtil.getIpAddress(request), tokenManager.getTokenTime());
        RedisTemplateX.setAndExpire(StrUtilX.concat(RedisKey.MULTI_USER_LOGIN, IpUtil.getIpAddress(request)), one.getUserAccount(), tokenManager.getTokenTime());
        RedisTemplateX.setAndExpire(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, one.getUserAccount()), token, tokenManager.getTokenTime());

        Map<String, Object> authInfo = new LinkedHashMap<String, Object>(2) {{
            put("token", token);
            put("user", loginResp);
        }};

        // 登录成功， 上次密码错误次数
        RedisTemplateX.delete(RedisKey.PASSWORD_ERROR_NUM.concat(one.getUserTel()));
        return Response.success(authInfo);
    }

    @Override
    public Response<Object> getCode(SendCode sendCode) throws Exception {
//        Response response = new Response(ResultCodeEnum.SUCCESS.code);
        UserPO userPO = this.getBaseMapper().selectOne(new LambdaQueryWrapper<UserPO>()
                        .eq(UserPO::getUserTel, EncryptUtil.encrypt(sendCode.getUserTel()))
//                .eq(UserPO::getAccountStatus, EmployeeAccountStatusEnum.VALID.no)
//                .eq(UserPO::getAvailableStatus, AvailableEnum.Start.key)
//                .eq(UserPO::getActive, 1)
                        .last(" limit 1")
        );
        if (userPO == null) {
            return Response.error(ErrorEnum.USER_PHONE_IS_NOT_EXIST.type, ErrorEnum.USER_PHONE_IS_NOT_EXIST.content);
        }

        if (userPO.getAccountStatus() != EmployeeAccountStatusEnum.VALID.no || userPO.getAvailableStatus().intValue() != AvailableEnum.Start.key.intValue() || userPO.getActive() != 1) {
            return Response.error(ErrorEnum.PHONE_IS_DISABLE.type, ErrorEnum.PHONE_IS_DISABLE.content);
        }

        String getCodeLimit = RedisTemplateX.get(RedisKey.GET_CODE_LIMIT.concat(sendCode.getUserTel()));
        if (getCodeLimit != null) {
            return Response.error(ErrorEnum.CODE_IS_LIMIT.type, ErrorEnum.CODE_IS_LIMIT.content);
        }

        String key = RedisKey.CODE_KEY.concat(sendCode.getUserTel());
        SysConfigReq sysConfigReq = new SysConfigReq();
        sysConfigReq.setStrKey("enableSendMsg");
        Response<SysConfigDTO> enableSendMsg = sysConfigRemote.querySysConfigByKey(sysConfigReq);
        System.out.println("查询发送短信数据是：" + enableSendMsg);
        int smsSend = 0;
        if (enableSendMsg.isSuccess()) {
            if (enableSendMsg.getModel() != null) {
                smsSend = Integer.parseInt(enableSendMsg.getModel().getStrValue());
            }
        }


        if (smsSend == 1) {
            String[] userPhone = sendCode.getUserTel().split("-");
            String phone = sendCode.getUserTel();
            if (userPhone.length > 1) {
                phone = userPhone[1];
            }
            String code = NumberUtil.getCode();
            //封装发送短信实体
            SendPhoneReq req = new SendPhoneReq();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", code);
            req.setPhone(phone);
            req.setJson(jsonObject);
            if (CHINA_PHONE_CODE.equals(userPhone[0]) || userPhone.length == 1) {
                req.setType(1);
                Boolean send = sendCodeUtil.sendSms(req);
                if (!send) {
                    return Response.error(ErrorEnum.SEND_CODE_IS_ERROR.type, ErrorEnum.SEND_CODE_IS_ERROR.content);
                }
            } else {
                req.setType(2);
                req.setPhone(sendCode.getUserTel());
                Boolean send = sendCodeUtil.sendOSea(req);
                if (!send) {
                    return Response.error(ErrorEnum.SEND_CODE_IS_ERROR.type, ErrorEnum.SEND_CODE_IS_ERROR.content);
                }
            }
            RedisTemplateX.setAndExpire(key, code, tokenManager.getCodeTime());

            //设置1分钟内只能获取一次验证码
            RedisTemplateX.setAndExpire(RedisKey.GET_CODE_LIMIT.concat(sendCode.getUserTel()), "1", 60);
        } else {
            RedisTemplateX.setAndExpire(key, "123456", 5 * 60);
        }

        return Response.success();
    }

    @Override
    public Response<Object> forgetPassword(ForgetPasswordReq req) throws Exception {
        UserPO userPO = this.getBaseMapper().selectOne(new LambdaQueryWrapper<UserPO>()
                .eq(UserPO::getUserTel, EncryptUtil.encrypt(req.getUserTel()))
                .last(" limit 1"));
        if (userPO == null) {
            return Response.error(ErrorEnum.USER_PHONE_IS_NOT_EXIST.type, ErrorEnum.USER_PHONE_IS_NOT_EXIST.content);
        }

        //校验验证码
        String code = RedisTemplateX.get(RedisKey.CODE_KEY.concat(req.getUserTel()));
        if (code == null) {
            return Response.error(ErrorEnum.CODE_IS_NOT_EXIST.type, ErrorEnum.CODE_IS_NOT_EXIST.content);
        }
        if (!code.equals(req.getVerificationCode())) {
            return Response.error(ErrorEnum.CODE_IS_ERROR.type, ErrorEnum.CODE_IS_ERROR.content);
        }

        //修改密码
        String encryptUserPassword = SM4Utils.encrypt(req.getUserPassword(), Sm4O.defaultKey);
        UserPO po = new UserPO();
        po.setUserId(userPO.getUserId());
        po.setUserPwd(encryptUserPassword);
        po.setSignaturePwd(SM3Utils.encrypt(encryptUserPassword));
        po.setUpdatePwdTime(new Date());
        this.getBaseMapper().updateUser(userPO);

        //删除缓存中的验证码
        RedisTemplateX.delete(RedisKey.CODE_KEY.concat(req.getUserTel()));

        return Response.success();
    }

    @Override
    public void updatePassword(UpdatePasswordReq req) {
        //校验旧密码
        UserPO byId = this.getById(req.getUserId());
        String encryptUserPassword = SM4Utils.encrypt(req.getOldPassword(), Sm4O.defaultKey);
        if (!byId.getUserPwd().equals(encryptUserPassword)) {
            throw new SysException(ErrorEnum.PASSWORD_IS_ERROR.type, ErrorEnum.PASSWORD_IS_ERROR.content);
        }
        //修改密码-新密码
        String newPassword = SM4Utils.encrypt(req.getNewPassword(), Sm4O.defaultKey);

        //密码不能修改原来设置的密码
        if (byId.getUserPwd().equals(newPassword)) {
            throw new SysException(ErrorEnum.PASSWORD_IS_SAME.type, ErrorEnum.PASSWORD_IS_SAME.content);
        }

        UserPO userPO = new UserPO();
        userPO.setUserId(byId.getUserId());
        userPO.setUserPwd(newPassword);
        userPO.setSignaturePwd(SM3Utils.encrypt(newPassword));
        this.getBaseMapper().updateUser(userPO);
    }

    @Override
    public void updateEmail(UpdateEmailReq req) {
        if (req.getUserType().intValue() == UserTypeEnum.SUPER_ADMIN.type) {
            throw new SysException(ErrorEnum.NOT_AUDIT_UPDATE.type, ErrorEnum.NOT_AUDIT_UPDATE.content);
        }
        UserPO byId = new UserPO();
        byId.setUserId(req.getUserId());
        //修改邮箱
        byId.setEmail(req.getEmail());
        this.getBaseMapper().updateUser(byId);
    }

    @Override
    public void updatePhone(UpdatePhoneReq req) throws Exception {
        if (req.getUserType().intValue() == UserTypeEnum.SUPER_ADMIN.type) {
            throw new SysException(ErrorEnum.NOT_AUDIT_UPDATE.type, ErrorEnum.NOT_AUDIT_UPDATE.content);
        }
        //校验新手机号和旧手机号是否一致
        if (req.getOldUserTel().equals(req.getUserTel())) {
            throw new SysException(ErrorEnum.OLD_USER_TEL_EQUALS_NEW_USER_TEL.type, ErrorEnum.OLD_USER_TEL_EQUALS_NEW_USER_TEL.content);
        }

        UserPO one = this.getOne(new LambdaQueryWrapper<UserPO>()
                .eq(UserPO::getUserTel, EncryptUtil.encrypt(req.getOldUserTel()))
                .last("limit 1"));
        //判断原手机号是否存在
        if (one == null) {
            throw new SysException(ErrorEnum.OLD_USER_TEL_ERROR.type, ErrorEnum.OLD_USER_TEL_ERROR.content);
        }
        //判断是否是本人的
        if (one.getUserId().intValue() != req.getUserId().intValue()) {
            throw new SysException(ErrorEnum.NOT_USER_SELF.type, ErrorEnum.NOT_USER_SELF.content);
        }

        //验重新手机号
        UserPO userPO = this.getOne(new LambdaQueryWrapper<UserPO>()
                .eq(UserPO::getUserTel, EncryptUtil.encrypt(req.getUserTel()))
                .last("limit 1"));

        //判断修改的手机号和原手机号是否一致
        if (userPO != null) {
            throw new SysException(ErrorEnum.USER_TEL_IS_ALREADY.type, ErrorEnum.USER_TEL_IS_ALREADY.content);
        }

        //校验验证码
        String key = RedisKey.CODE_KEY.concat(req.getOldUserTel());
        String code = RedisTemplateX.get(key);
        if (!req.getVerificationCode().equals(code)) {
            throw new SysException(ErrorEnum.CODE_IS_ERROR.type, ErrorEnum.CODE_IS_ERROR.content);
        }
        UserPO po = new UserPO();
        po.setUserId(req.getUserId());
        po.setUserTel(req.getUserTel());
        this.getBaseMapper().updateUser(po);
    }

    @Override
    public PaginationSupportDTO<UserListResp> userList(UserListReq req) {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<UserPO>().eq(UserPO::getOrgCode, req.getAgentCode());
        if (!StringUtils.isEmpty(req.getUserName())) {
            wrapper.like(UserPO::getUserName, req.getUserName());
        }
        if (req.getAvailableStatus() != null) {
            wrapper.eq(UserPO::getAvailableStatus, req.getAvailableStatus());
        }
        wrapper.orderByDesc(UserPO::getIsSuperAdmin);
        wrapper.orderByDesc(UserPO::getUserId);
        IPage<UserPO> iPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<UserPO> page = this.getBaseMapper().selectPage(iPage, wrapper);
        List<UserListResp> respList = new ArrayList<>();

        if (page.getRecords() != null && page.getRecords().size() > 0) {
            for (UserPO userPO : page.getRecords()) {
                UserListResp userListResp = DTOConvert.INSTANCE.UserListRespConvert(userPO);
                respList.add(userListResp);
            }
        }
        PaginationSupportDTO<UserListResp> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(respList);
        paginationSupport.setPageSize((int) page.getSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage((int) page.getPages());
        paginationSupport.setCurrentPage((int) page.getCurrent());
        return paginationSupport;
    }

    @Override
    public void updateUser(AddOrUpdateUserReq req) throws Exception {
        if (req.getUserId() == null) {
            throw new SysException(ErrorEnum.USER_ID_IS_NOT_NULL.type, ErrorEnum.USER_ID_IS_NOT_NULL.content);
        }

        //判断是否有权限修改
        if (req.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }

        //判断修改的是否是超级管理员
        UserPO byId = this.getById(req.getUserId());
        if (byId.getIsSuperAdmin() == 1) {
            throw new SysException(ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.type, ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.content);
        }
        //验重--先验重账号
        Boolean exist = userAccountExist(req.getUserAccount(), req.getUserId());
        if (exist) {
            // 在验重手机号
            exist = userAccountExist(req.getUserTel(), req.getUserId());
            if (!exist) {
                throw new SysException(ErrorEnum.USER_TEL_IS_ALREADY.type, ErrorEnum.USER_TEL_IS_ALREADY.content);
            }
        } else {
            throw new SysException(ErrorEnum.USER_ACCOUNT_IS_ALREADY.type, ErrorEnum.USER_ACCOUNT_IS_ALREADY.content);
        }
        //修改
        UserPO po = DTOConvert.INSTANCE.UserPOConvert(req);
        this.getBaseMapper().updateUser(po);
        if (req.getAvailableStatus().intValue() == AvailableEnum.End.key) {
            String token = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, byId.getUserAccount()));
            RedisTemplateX.delete(token);
        }
    }

    @Override
    public void delUser(DelUserReq req) {
        //判断是否有权限删除
        if (req.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }

        //判断删除的是否是超级管理员
        UserPO byId = this.getById(req.getUserId());
        if (byId.getIsSuperAdmin() == 1) {
            throw new SysException(ErrorEnum.ADMIN_ACCOUNT_IS_NOT_DEL.type, ErrorEnum.ADMIN_ACCOUNT_IS_NOT_DEL.content);
        }

        this.removeById(req.getUserId());
        String token = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, byId.getUserAccount()));
        RedisTemplateX.delete(token);
    }

    @Override
    public void resetPassword(ResetPasswordReq req) {
        //判断是否有权限重置
        if (req.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }
        //判断修改的是否是超级管理员
        UserPO byId = this.getById(req.getUserId());
        if (byId.getIsSuperAdmin() == 1) {
            throw new SysException(ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.type, ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.content);
        }
        //重置密码为Mongoso2018
        String password = SM4Utils.encrypt(CompanyDTO.PWD, Sm4O.defaultKey);
        UserPO userPO = new UserPO();
        userPO.setUserId(req.getUserId());
        userPO.setUserPwd(password);
        userPO.setSignaturePwd(SM3Utils.encrypt(password));
        userPO.setUpdatedBy(req.getUpdatedBy());
        this.getBaseMapper().updateUser(userPO);
    }

    @Override
    public void addUser(AddOrUpdateUserReq req) throws Exception {
        if (StringUtils.isEmpty(req.getAgentCode())) {
            throw new SysException(ErrorEnum.AGENT_CODE_IS_NOT_NULL.type, ErrorEnum.AGENT_CODE_IS_NOT_NULL.content);
        }
        //判断是否有权限新增
        if (req.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }

        //验重--先验重账号
        Boolean exist = userAccountExist(req.getUserAccount(), req.getUserId());
        if (exist) {
            //在验重手机号
            exist = userAccountExist(req.getUserTel(), req.getUserId());
            if (!exist) {
                throw new SysException(ErrorEnum.USER_PHONE_IS_ALREADY.type, ErrorEnum.USER_PHONE_IS_ALREADY.content);
            }
        } else {
            throw new SysException(ErrorEnum.USER_ACCOUNT_IS_ALREADY.type, ErrorEnum.USER_ACCOUNT_IS_ALREADY.content);
        }

        UserPO userPO = DTOConvert.INSTANCE.UserPOConvert(req);
        String password = SM4Utils.encrypt(CompanyDTO.PWD, Sm4O.defaultKey);
        userPO.setUserPwd(password);
        userPO.setSignaturePwd(SM3Utils.encrypt(password));
        userPO.setUpdatePwdTime(new Date());
        userPO.setOrgCode(req.getAgentCode());
        userPO.setIsSuperAdmin(0);
        userPO.setActive(1);
        userPO.setAccountStatus(EmployeeAccountStatusEnum.VALID.no);

        this.save(userPO);
    }

    @Override
    public Boolean userAccountExist(String userAccount, Integer userId) throws Exception {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<UserPO>();
//        if (userId != null) {
        wrapper.eq(UserPO::getUserAccount, userAccount)
                .or()
                .eq(UserPO::getUserTel, EncryptUtil.encrypt(userAccount));

        wrapper.last("limit 1");
        //验重
        UserPO one = this.getOne(wrapper);
        return (one == null || userId != null) && (one == null || one.getUserId().intValue() == userId.intValue());
    }

    @Override
    public UserInfo userInfo(UserInfoReq req) {
        UserInfo userInfo = this.getBaseMapper().queryUserInfo(req.getUserId());
        //判断用户身份
        if (CompanyDTO.COMPANY_CODE.equals(userInfo.getAgentCode())) {
            //返回客户管理员数据
            userInfo = this.getBaseMapper().queryUserInfoByAgentCode(req.getAgentCode());
            if (userInfo == null) {
                throw new SysException(ErrorEnum.AGENT_CODE_ERROR.type, ErrorEnum.AGENT_CODE_ERROR.content);
            }
            userInfo.setUserType(UserTypeEnum.SUPER_ADMIN.type);
        } else if (userInfo.getIsSuperAdmin() != null && userInfo.getIsSuperAdmin() == 1) {
            userInfo.setUserType(UserTypeEnum.ADMIN.type);
        } else {
            userInfo.setUserType(UserTypeEnum.NORMAL.type);
        }
        return userInfo;
    }

    @Override
    public void loginBack(String token) {
        RedisTemplateX.delete(token);
    }
}
