package com.tiangong.h5.convert;

import com.tiangong.dto.hotel.HotelPageReq;
import com.tiangong.dto.order.request.CheckBookingRequest;
import com.tiangong.dto.order.request.CreateOrderRequest;
import com.tiangong.dto.order.response.OrderRoomDetailDto;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.PriceItem;
import com.tiangong.dto.product.RoomItemDetail;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.h5.domain.po.UserPO;
import com.tiangong.h5.domain.req.*;
import com.tiangong.h5.domain.resp.UserListResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 类型转换
 */
@Mapper
public interface DTOConvert {

    DTOConvert INSTANCE = Mappers.getMapper(DTOConvert.class);

    /**
     * 将酒店ID到价格请求转换为酒店最低价格请求
     *
     * @param request 酒店ID到价格请求对象
     * @return 返回转换后的酒店最低价格请求
     */
    HotelLowestPriceRequest HotelLowestPriceConvert(HotelIdsToPriceReq request);

    /**
     * 将新增订单请求转换为创建订单请求
     *
     * @param req 新增订单请求对象
     * @return 返回转换后的创建订单请求
     */
    CreateOrderRequest CreateOrderConvert(AddOrderReq req);

    /**
     * 将检查预订请求转换为检查预订请求
     *
     * @param req 检查预订请求对象
     * @return 返回转换后的检查预订请求
     */
    CheckBookingRequest CheckOrderConvert(CheckBookingReq req);

    /**
     * 将订单房间详情DTO转换为房间项目详情
     *
     * @param detailDto 订单房间详情DTO对象
     * @return 返回转换后的房间项目详情
     */
    RoomItemDetail RoomConvert(OrderRoomDetailDto detailDto);

    /**
     * 将价格信息详情转换为价格项目
     *
     * @param infoDetail 价格信息详情对象
     * @return 返回转换后的价格项目
     */
    PriceItem PriceConvert(PriceInfoDetail infoDetail);

    /**
     * 将用户持久化对象转换为用户列表响应
     *
     * @param userPO 用户持久化对象
     * @return 返回转换后的用户列表响应
     */
    UserListResp UserListRespConvert(UserPO userPO);

    /**
     * 将新增或更新用户请求转换为用户持久化对象
     *
     * @param req 新增或更新用户请求对象
     * @return 返回转换后的用户持久化对象
     */
    UserPO UserPOConvert(AddOrUpdateUserReq req);

    /**
     * 将酒店列表分页请求转换为酒店分页请求
     *
     * @param hotelListPageRequest 酒店列表分页请求对象
     * @return 返回转换后的酒店分页请求
     */
    HotelPageReq hotelListPageConvert(HotelListPageRequest hotelListPageRequest);
}
