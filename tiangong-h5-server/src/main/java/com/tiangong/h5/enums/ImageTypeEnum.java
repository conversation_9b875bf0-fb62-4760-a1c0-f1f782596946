package com.tiangong.h5.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 图片类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ImageTypeEnum {

    ROOM(1, "房型"),
    EXTERIOR(2, "外景"),
    LOBBY(3, "大堂"),
    FACILITY(4, "设施"),
    OTHER(5, "其它"),
    BANQUET_HALL(6, "宴会厅"),
    MEETING_ROOM(7, "会议厅"),
    TICKET(8, "门票"),
    OTHER_SERVICE(9, "其它服务");

    private final Integer code;
    private final String desc;
}
