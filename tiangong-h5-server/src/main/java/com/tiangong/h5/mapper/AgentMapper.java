package com.tiangong.h5.mapper;

import com.tiangong.h5.domain.resp.AgentFinanceResp;
import com.tiangong.h5.domain.resp.AgentListResp;
import com.tiangong.h5.domain.resp.UserAgentLogResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/6 13:51
 */
@Mapper
public interface AgentMapper {

    /**
     * 根据商户编码查询客户列表
     *
     * @param companyCode
     * @return
     */
    List<AgentListResp> agentListByCompanyCode(@Param("companyCode") String companyCode);

    /**
     * 根据客户列表查询客户信息
     *
     * @param agentCode
     * @return
     */
    List<AgentListResp> agentListByAgentCode(@Param("agentCode") String agentCode);

    /**
     * 根据用户id查询选择过的客户列表记录
     * @param userId
     * @return
     */
    List<UserAgentLogResp> agentListByAgentCodeLog(@Param("userId") Integer userId);

    /**
     * 根据客户编码获取客户财政信息
     *
     * @param agentCode
     * @return
     */
    AgentFinanceResp agentFinanceByAgentCode(@Param("agentCode") String agentCode);

    /**
     * 判断客户是否被禁用
     * @param agentCode
     * @return
     */
    String getAgentStatusByAgentCode(@Param("agentCode") String agentCode);
}
