package com.tiangong.h5.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.h5.domain.po.UserPO;
import com.tiangong.h5.domain.resp.LoginResp;
import com.tiangong.h5.domain.resp.UserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @create 2023/11/3 15:59
 */
@Mapper
public interface UserMapper extends BaseMapper<UserPO> {

    LoginResp queryUserLogin(@Param("userAccount") String userAccount);

    UserInfo queryUserInfo(@Param("userId") Integer userId);

    UserInfo queryUserInfoByAgentCode(@Param("agentCode") String agentCode);

    int updateUser(UserPO userPO);
}
