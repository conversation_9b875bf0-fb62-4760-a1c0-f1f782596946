package com.tiangong.h5.util;

import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ValidatedFilter {

    /**
     * 同一用户不同用户登录记录key
     */
    public static final String MULTI_USER_LOGIN_ACCOUNT = "AUTH:MULTI_USER_LOGIN_ACCOUNT:";

    /**
     * 连续登录失败记录
     */
    public static void loginFailRecord(String userTel) {
        String key = RedisKey.PASSWORD_ERROR_NUM.concat(userTel);
        String value = RedisTemplateX.get(key);
        if (value == null) {
            RedisTemplateX.setAndExpire(key, "1", 60 * 60 * 24);
        } else {
            int maxCount = RedisKey.PASSWORD_ERROR_MAX_NUM;
            int count = Integer.parseInt(RedisTemplateX.get(key)) + 1;
            if (count < maxCount) {
                //没有到最大值，则加1
                RedisTemplateX.incr(key, 1);
            } else {
                //锁定 30分钟
                RedisTemplateX.setAndExpire(RedisKey.USER_IS_LOCK.concat(userTel), "1", 60 * 5);
            }
        }
    }
}
