<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.h5.mapper.UserMapper">

    <select id="queryUserLogin" resultType="com.tiangong.h5.domain.resp.LoginResp" parameterType="java.lang.String">
        SELECT
            user_id,
            user_name,
            user_account,
            au.org_code AS agentCode,
            oo.org_name,
            au.user_tel as userTel,
            au.email,
            au.is_super_admin
        FROM
            t_auth_user au
            LEFT JOIN t_org_organization oo ON au.org_code = oo.org_code
        WHERE
            au.user_account = #{userAccount}
            AND au.available_status = 1
            AND au.active = 1
            AND au.account_status = 0
            AND oo.available_status = 1
    </select>

    <select id="queryUserInfo" resultType="com.tiangong.h5.domain.resp.UserInfo" parameterType="java.lang.Integer">
        SELECT
            user_id,
            user_name,
            user_account,
            au.org_code AS agentCode,
            au.user_tel as userTel,
            au.email,
            au.is_super_admin
        FROM
            t_auth_user au
        WHERE
            au.user_id = #{userId}
            AND au.available_status = 1
            AND au.active = 1
            AND au.account_status = 0
    </select>

    <select id="queryUserInfoByAgentCode" resultType="com.tiangong.h5.domain.resp.UserInfo" parameterType="java.lang.String">
        SELECT
            user_id,
            user_name,
            user_account,
            au.org_code AS agentCode,
            au.user_tel as userTel,
            au.email,
            au.is_super_admin
        FROM
            t_auth_user au
        WHERE
            au.org_code = #{agentCode}
            AND au.is_super_admin = 1
            AND au.available_status = 1
            AND au.active = 1
            AND au.account_status = 0
    </select>


    <update id="updateUser" parameterType="com.tiangong.h5.domain.po.UserPO">
        update t_auth_user
        <set>
            <if test="userPwd != null and userPwd != '' ">
                user_pwd = #{userPwd},
            </if>
            <if test="signaturePwd != null and signaturePwd != '' ">
                signature_pwd = #{signaturePwd},
            </if>
            <if test="userAccount != null and userAccount != '' ">
                user_account = #{userAccount},
            </if>
            <if test="userName != null and userName != '' ">
                user_name = #{userName},
            </if>
            <if test="userTel != null and userTel != '' ">
                user_tel = #{userTel},
            </if>
            <if test="availableStatus != null">
                available_status = #{availableStatus},
            </if>
            <if test="createdBy != null and createdBy != '' ">
                created_by = #{createdBy},
            </if>
            <if test="updatedBy != null and updatedBy != '' ">
                updated_by = #{updatedBy},
            </if>
            <if test="email != null and email != '' ">
                email = #{email},
            </if>
        </set>
         where user_id = #{userId}
    </update>

</mapper>