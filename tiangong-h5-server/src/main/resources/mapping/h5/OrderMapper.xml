<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.h5.mapper.OrderMapper">

    <update id="updateVoucher" parameterType="java.lang.Object">
        update o_order set voucher_url = #{voucherUrl} where order_code = #{orderCode}
    </update>

    <select id="queryVoucher" parameterType="java.lang.String" resultType="com.tiangong.h5.domain.dto.QueryVoucherDTO">
        select voucher_url, contact_email from o_order where order_code = #{orderCode}
    </select>


</mapper>