package com.tiangong.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退改工单任务明细表
 */
@Data
@TableName("t_order_change_word_order_task_price_item")
public class OrderChangeWordOrderTaskPriceItemPO {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 明细id
     */
    private Integer itemId;

    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 退订费
     */
    private BigDecimal refundPrice;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
