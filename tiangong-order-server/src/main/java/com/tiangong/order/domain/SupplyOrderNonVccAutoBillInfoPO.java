package com.tiangong.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/10 11:08
 * @Description:
 */

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("o_supply_order_non_vcc_auto_bill_info")
public class SupplyOrderNonVccAutoBillInfoPO extends Model<SupplyOrderNonVccAutoBillInfoPO> {

    private static final long serialVersionUID = 1L;

    /**
     * id号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 供货单ID
     */
    private Long supplyOrderId;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 天宫付款方式 0-酒店给我方发送付款链接 1-银行转账 2-WorldFirst账户收款
     */
    private Integer paymentMethod;

    /**
     * 付款链接发送邮箱 (英文逗号进行分隔，最多支持3个邮箱)
     */
    private String paymentLinkEmails;

    /**
     * 酒店付款联系人
     */
    private String hotelPaymentContactPerson;

    /**
     * 酒店收款开户行
     */
    private String hotelCollectionBank;

    /**
     * 酒店收款账户名
     */
    private String hotelCollectionAccountName;

    /**
     * 酒店收款账号
     */
    private String hotelCollectionAccountNumber;

    /**
     * WorldFirst账户号
     */
    private String worldFirstAccountNumber;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 数据创建人
     */
    private String createdBy;

    /**
     * 数据创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

}
