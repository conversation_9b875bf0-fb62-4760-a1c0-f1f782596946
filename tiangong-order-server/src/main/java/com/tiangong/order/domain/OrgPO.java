package com.tiangong.order.domain;

import com.tiangong.dto.common.BasePO;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2019/6/18 21:02
 **/
@Data
@SensitiveClass
@Table(name = "t_org_organization")
public class OrgPO extends BasePO {
    /**
     * 机构Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer orgId;
    /**
     * 机构名称
     */
    @Column(name = "org_name")
    private String orgName;
    /**
     * 机构编码
     */
    @Column(name = "org_code")
    private String orgCode;
    /**
     * 机构类型
     * 1-包房商，2-个人
     */
    @Column(name = "org_type")
    private Integer orgType;
    /**
     * 企业域名
     */
    @Column(name = "org_domian")
    private String orgDomian;
    /**
     * 企业电话
     */
    @EncryptField
    @Column(name = "org_tel")
    private String orgTel;
    /**
     * 企业地址
     */
    @Column(name = "org_address")
    private String orgAddress;
    /**
     * 企业简介
     */
    @Column(name = "org_summary")
    private String orgSummary;
    /**
     * /**
     * 数据类型
     * 1-供应商，2-客户，3-运营商
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 启用状态
     */
    @Column(name = "available_status")
    private Integer availableStatus;
    /**
     * 酒店信息权限
     */
    @Column(name = "hotel_info_permissions")
    private Integer hotelInfoPermissions;

    /**
     * 公章url
     */
    private Integer officialSealId;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 是否为共享供应商(0 否, 1 是)
     */
    private Integer isShareSupplier;
}
