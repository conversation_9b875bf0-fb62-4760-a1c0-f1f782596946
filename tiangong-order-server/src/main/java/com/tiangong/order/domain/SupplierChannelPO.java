package com.tiangong.order.domain;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 订单自动设置
 *
 * <AUTHOR>
 * @date 2019/10/8 11:12
 **/
@Data
@Table(name = "t_org_order_automatic")
public class SupplierChannelPO extends BasePO {
    /**
     * 订单自动发单ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 商家编码
     */
    private String company_code;
    /**
     * 间隔时间
     */
    private String date_Interval;
    /**
     * 渠道编码
     */
    private String channel_code;

}
