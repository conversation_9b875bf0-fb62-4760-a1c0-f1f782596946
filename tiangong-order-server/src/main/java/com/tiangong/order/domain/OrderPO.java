package com.tiangong.order.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "o_order")
public class OrderPO extends BasePO {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 订单编码
     */
    @Column(name = "order_code")
    private String orderCode;

    /**
     * 订单确认状态
     * @see com.tiangong.enums.OrderStatusEnum
     */
    @Column(name = "order_confirmation_status")
    private Integer orderConfirmationStatus;

    /**
     * 底价币种
     */
    @Column(name = "base_currency")
    private Integer baseCurrency;

    /**
     * 售价币种
     */
    @Column(name = "sale_currency")
    private Integer saleCurrency;

    /**
     * 订单金额
     */
    @Column(name = "order_amt")
    private BigDecimal orderAmt;

    /**
     * 售价
     */
    @Column(name = "sale_price")
    private BigDecimal salePrice;

    /**
     * 底价
     */
    @Column(name = "base_price")
    private BigDecimal basePrice;

    /**
     * 结算方式：0月结 1半月结 2周结 3单结 4日结
     */
    @Column(name = "settlement_type")
    private Integer settlementType;

    /**
     * 渠道编码
     */
    @Column(name = "channel_code")
    private String channelCode;

    /**
     * 分销商编码
     */
    @Column(name = "agent_code")
    private String agentCode;

    /**
     * 分销商名称
     */
    @Column(name = "agent_name")
    private String agentName;

    /**
     * 合作商编码
     */
    @Column(name = "partner_code")
    private String partnerCode;

    /**
     * 联系人名称
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Column(name = "contact_email")
    private String contactEmail;

    /**
     * 联系人电话区号
     */
    @Column(name = "contact_country_code")
    private String contactCountryCode;

    /**
     * 渠道订单号
     */
    @Column(name = "channel_order_code")
    private String channelOrderCode;

    /**
     * 订单归属人帐号
     */
    @Column(name = "order_owner_user")
    private String orderOwnerUser;

    /**
     * 订单归属人名称
     */
    @Column(name = "order_owner_name")
    private String orderOwnerName;

    /**
     * 锁单人帐号
     */
    @Column(name = "lock_user")
    private String lockUser;

    /**
     * 锁单人名称
     */
    @Column(name = "lock_name")
    private String lockName;

    /**
     * 锁单时间
     */
    @Column(name = "lock_time")
    private Date lockTime;

    /**
     * 总利润
     */
    private BigDecimal profit;

    /**
     * 是否手工单
     */
    @Column(name = "is_manual_order")
    private Integer isManualOrder;

    /**
     * 是否代下单
     */
    @Column(name = "is_substituted")
    private Integer isSubstituted;

    /**
     * 商家编码
     */
    @Column(name = "company_code")
    private String companyCode;


    /**
     * 业务经理
     */
    @Column(name = "merchant_bm")
    private String merchantBm;

    /**
     * 退改费
     */
    @Column(name = "refund_fee")
    private BigDecimal refundFee;

    /**
     * 国家编码
     */
    @Column(name = "country_code")
    private String countryCode;

    /**
     * 省份编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @Column(name = "city_name")
    private String cityName;

    /**
     * 酒店id
     */
    @Column(name = "hotel_id")
    private Long hotelId;

    /**
     * 酒店名称
     */
    @Column(name = "hotel_name")
    private String hotelName;

    /**
     * 房型id
     */
    private Integer roomId;

    /**
     * 房型名称，多个以逗号隔开
     */
    @Column(name = "room_name")
    private String roomName;

    /**
     * 产品名称，多个以逗号隔开
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 第一个产品的入住日期
     */
    @Column(name = "start_date")
    private Date startDate;

    /**
     * 第一个产品的离店日期
     */
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 第一个产品的入住日期
     */
    @Column(name = "start_time")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 第一个产品的离店日期
     */
    @Column(name = "end_time")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 间数
     */
    @Column(name = "room_qty")
    private Integer roomQty;

    /**
     * 支付方式（0:预付 1:面付无佣金）
     * @see com.tiangong.enums.PayMethodEnum
     */
    @Column(name = "pay_method")
    private Integer payMethod;

    /**
     * 所有入住人名单，多个以逗号隔开
     */
    private String guest;

    /**
     * 床型编码
     */
    @Column(name = "bed_type")
    private String bedType;

    /**
     * 早餐数量
     */
    @Column(name = "breakfast_qty")
    private Integer breakfastQty;

    /**
     * 供货单状态，多个供货单状态已逗号隔开，0未发单，1已发预订单，2已重发预订单，3已发修改单，4已发取消单
     */
    @Column(name = "supply_order_confirmation_status")
    private String supplyOrderConfirmationStatus;

    /**
     * 确认时间
     */
    @Column(name = "confirm_time")
    private Date confirmTime;

    /**
     * 标星：0未标，1已标
     */
    @Column(name = "marked_status")
    private Integer markedStatus;

    /**
     * 修改状态：0无，1取消中，2修改中，3再次确认，4退款申请，5待确认异常，6取消异常
     */
    @Column(name = "modification_status")
    private Integer modificationStatus;

    /**
     * 是否即时确认： 0否 ,1是
     */
    @Column(name = "instantConfirmation_status")
    private Integer instantConfirmationStatus;

    /**
     * 入住成人数
     */
    @Column(name = "adult_qty")
    private Integer adultQty;

    /**
     * 入住儿童数
     */
    @Column(name = "children_qty")
    private Integer childrenQty;

    /**
     * 入住年龄
     */
    @Column(name = "children_age")
    private String childrenAge;

    /**
     * 售价汇率
     */
    @Column(name = "sale_rate")
    private BigDecimal saleRate;

    /**
     * 产品id
     */
    @Column(name = "product_id")
    private String productId;

    /**
     * 是否为问题单
     */
    private Integer isAbnormal;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 是否担保
     * 订单是否担保
     * 1 担保
     * 0 未担保
     * 默认为未担保
     */
    @Column(name = "guarantee_flag")
    private Integer guaranteeFlag;


    /**
     * 转换后的产品id
     */
    @Column(name = "conversion_product_id")
    private String conversionProductId;

    /**
     * 最早到店时间
     */
    @Column(name = "earliest_arrive_time")
    private String earliestArriveTime;

    /**
     * 最晚到店时间
     */
    @Column(name = "latest_arrive_time")
    private String latestArriveTime;

    /**
     * 产品标签（0或者null：无标签  1：协议价格标签）
     */
    @Column(name = "product_label")
    private Integer productLabel;

    /**
     * 产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    @Column(name = "product_label_type")
    private Integer productLabelType;

    /**
     * 是否终点房1是0否
     */
    @Column(name = "hourly")
    private Integer hourly;

    /**
     * 附加项费用
     */
    @Column(name = "additional_charges")
    private BigDecimal additionalCharges;

    /**
     * 是否是凌晨房订单(0:否,1:是)
     */
    @Column(name = "is_early_morning_room_order")
    private Integer isEarlyMorningRoomOrder;

    /**
     * 开票模式 1商家开票 2酒店前台开票
     */
    @Column(name = "invoice_model")
    private Integer invoiceModel;

    /**
     * 发票类型：1普票 2专票
     */
    @Column(name = "invoice_type")
    private Integer invoiceType;

    /**
     * 是否含窗：0-无窗1-部分有窗2-有窗4-内窗5-天窗
     */
    @Column(name = "window_detail")
    private Integer windowDetail;

    /**
     * 到店另付价格
     */
    @Column(name = "pay_at_hotel_fee")
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付价格（客户币种金额）
     */
    @Column(name = "pay_at_hotel_agent_currency_fee")
    private BigDecimal payAtHotelAgentCurrencyFee;

    /**
     * 到店另付币种
     */
    @Column(name = "pay_at_hotel_currency")
    private Integer payAtHotelCurrency;

    /**
     * 语言
     * zh_CN("zh_CN","zh-CN", "简体中文")
     * en_US("en_US","en-US","英语")
     */
    @Column(name = "language")
    private String language;


    /**
     * 下单人
     */
    @Column(name = "user_account")
    private String userAccount;

    /**
     * 订单支付状态
     */
    @Column(name = "pay_status")
    private Integer payStatus;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    @Column(name = "is_vip_order")
    private String isVipOrder;

    /**
     * 出行类型，1-因公，2-因私 非必填
     */
    @Column(name = "travel_type")
    private Integer travelType;

    /**
     * 客户下属单位名称
     */
    @Column(name = "sdistributor_name")
    private String sdistributorName;

    @Column(name = "confirmation_code")
    private String confirmationCode;

    /**
     * 快速处理标签开关
     */
    @Column(name = "quick_processing_switch")
    private Integer quickProcessingSwitch;

    /**
     * 全部床型
     */
    @Column(name = "all_bed_type")
    private String allBedType;

    /**
     * 国家名称
     */
    @Column(name = "country_name")
    private String countryName;

    /**
     * 省份名称
     */
    @Column(name = "province_name")
    private String provinceName;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    @Column(name = "supplier_label")
    private Integer supplierLabel;

    /**
     * 价格礼包
     */
    @Column(name = "gift_packs")
    private String giftPacks;

    /**
     * 结算模式：0挂账 1预存
     */
    @Column(name = "settled_type")
    private Integer settledType;

    /**
     * 剩余额度
     */
    @Column(name = "remaining_balance")
    private BigDecimal remainingBalance;

    /**
     * 商家币种转客户币种汇率
     */
    @Column(name = "org_to_agent_rate")
    private BigDecimal orgToAgentRate;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    @Column(name = "bed_type_diff")
    private Integer bedTypeDiff;

    /**
     * 国籍
     */
    @Column(name = "nationality")
    private String nationality;

    /**
     * 是否混合支付 0-否 1-是
     */
    @Column(name = "is_mixed_pay")
    private Integer isMixedPay;

    /**
     * 是否退改 0-否 1-是
     */
    @Column(name = "is_refund")
    private Integer isRefund;
    /**
     * 是否中国订单1是0否
     */
    @Column(name = "cn_type")
    private Integer cnType;

    /**
     * 推送订单状态异常 1 异常
     */
    @Column(name = "PUSH_ORDER_STATUS")
    private Integer pushOrderStatus;

    /**
     * 扣减额度账户
     */
    @Column(name = "subtract_line_account")
    private String subtractLineAccount;
}