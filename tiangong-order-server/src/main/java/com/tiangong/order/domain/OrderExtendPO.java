package com.tiangong.order.domain;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.*;

@Data
@Table(name = "o_order_extend")
public class OrderExtendPO extends BasePO {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 订单Id
     */
    @Column(name = "order_id")
    private Integer orderId;

    /**
     * 订单编码
     */
    @Column(name = "order_code")
    private String orderCode;

    /**
     * 客人特殊要求
     */
    @Column(name = "special_request")
    private String specialRequest;

    /**
     * 客人特殊要求
     */
    @Column(name = "is_show_on_supply_order")
    private Integer isShowOnSupplyOrder;

    /**
     * 取消原因
     */
    @Column(name = "cancelled_reason")
    private String cancelledReason;

    /**
     * 床型，0:单床，1：大床，2：双床，3：三床，4：四床，多个床型用逗号隔开
     */
    @Column(name = "bed_type")
    private String bedType;

    /**
     * 床型描述
     * 不涉及多语言
     * 2024-07-29 author:湫
     * qc 973 【天宫&B2B-EPS】产品的床型信息传递
     */
    @Column(name = "bed_info_desc")
    private String bedInfoDesc;

    /**
     * 确认号
     */
    @Column(name = "confirmation_code")
    private String confirmationCode;

    /**
     * 取消条款
     */
    @Column(name = "cancellation_term")
    private String cancellationTerm;

    /**
     * 其它条款
     */
    @Column(name = "other_term")
    private String otherTerm;

    /**
     * 交通信息
     */
    @Column(name = "order_traffic_json")
    private String orderTrafficJson;

}