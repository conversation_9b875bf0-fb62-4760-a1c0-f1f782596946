package com.tiangong.order.domain.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 退改工单任务明细表
 */
@Data
public class OrderChangeWordOrderTaskPriceItemResp {

    /**
     * id
     */
    private Integer id;

    /**
     * 明细id
     */
    private Integer itemId;

    /**
     * 售卖日期
     */
    private String saleDate;

    /**
     * 房费
     */
    private BigDecimal roomPrice;

    /**
     * 退订费
     */
    private BigDecimal refundPrice;
}
