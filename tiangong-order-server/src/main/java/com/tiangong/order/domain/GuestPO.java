package com.tiangong.order.domain;

import com.tiangong.dto.common.BasePO;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.*;

@Data
@Table(name = "o_guest")
@SensitiveClass
public class GuestPO extends BasePO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Integer orderId;

    /**
     * 入住人名称
     */
    private String name;

    /**
     * 英文-名
     */
    private String firstName;

    /**
     * 英文-姓
     */
    private String lastName;

    /**
     * 国籍
     */
    private Integer nationality;

    /**
     * 手机号码
     */
    @EncryptField
    private String mobileNo;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 证件类型
     */
    private Integer idCardType;

    /**
     * 证件号
     */
    @EncryptField
    private String idCardNo;

    /**
     * 会员卡号
     */
    private String membershipCardNumber;

    /**
     * 房间号
     */
    private Integer roomNumber;

    /**
     * 华住需要 入住人是否获得积分 0否 1是
     */
    private Integer earnPoints;

    /**
     * 成人数
     */
    private Integer adultQty;

    /**
     * 儿童数
     */
    private Integer childrenQty;

    /**
     * 儿童年龄
     */
    private String childrenAge;
    /**
     * VIP等级
     */
    private Integer vipLevel;

}