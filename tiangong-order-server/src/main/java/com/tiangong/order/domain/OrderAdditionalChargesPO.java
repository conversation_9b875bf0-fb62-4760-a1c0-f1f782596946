package com.tiangong.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单附加项费用
 *
 * <AUTHOR>
 * @Date 2023/8/19 15:18
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("o_order_additional_charges")
public class OrderAdditionalChargesPO extends Model<OrderAdditionalChargesPO> {

    private static final long serialVersionUID = 1L;

    /**
     * id号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 附加费类型
     * 0:其他
     */
    private Integer additionalChargesType;
    /**
     * 附加费名称
     */
    private String additionalChargesName;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 订单编号
     */
    private String orderCode;
    /**
     * 日期
     */
    private Date additionalChargesDate;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 附加费
     */
    private BigDecimal additionalCharges;
    /**
     * 组号
     */
    private String groupNumber;
    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

}
