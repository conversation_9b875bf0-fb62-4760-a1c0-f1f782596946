package com.tiangong.order.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class DelOrderFileReq {

    private String orderCode;

    @NotNull(message = "EMPTY_PARAM_ID")
    private Integer id;

    @NotNull(message = "EMPTY_PARAM_ORDERID")
    private Integer orderId;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 订单归属人
     */
    private String orderOwnerName;
}
