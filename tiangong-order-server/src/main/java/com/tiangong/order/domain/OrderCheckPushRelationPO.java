package com.tiangong.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * 入住明细推送状态关联表
 *
 * <AUTHOR>
 * @date ： 2024/3/13
 */
@Data
@Table(name = "o_order_check_push_relation")
public class OrderCheckPushRelationPO {
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @TableId(value = "order_id", type = IdType.AUTO)
    private Long orderId;
    /**
     * 订单编码 天宫订单编码
     */
    private String orderCode;
    /**
     * 推送时间
     */
    private Date pushDt;
}
