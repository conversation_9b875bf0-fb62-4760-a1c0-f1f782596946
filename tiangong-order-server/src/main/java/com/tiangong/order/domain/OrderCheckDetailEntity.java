package com.tiangong.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单入住明细
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:26:35
 */
@Data
@TableName("o_order_check_detail")
public class OrderCheckDetailEntity extends Model<OrderCheckDetailEntity> {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 订单编码
     */
    private String orderCode;
    /**
     * 入住信息Id
     */
    private Integer checkInfoId;
    /**
     * 日期
     */
    private Date saleDate;
    /**
     * 房费
     */
    private BigDecimal salePrice;
    /**
     * 退订费
     */
    private BigDecimal refundPrice;
    /**
     * 供货单号
     */
    private String supplyOrderCode;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;

}
