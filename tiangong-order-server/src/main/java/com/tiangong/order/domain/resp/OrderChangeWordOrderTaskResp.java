package com.tiangong.order.domain.resp;

import com.tiangong.file.resp.FileResp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderChangeWordOrderTaskResp {
    /**
     * 退改任务单号
     */
    private String taskOrderCode;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 任务类型(1退房任务)
     */
    private Integer taskType;

    /**
     * 退款类型(0整单取消 1部分取消)
     */
    private Integer refundType;

    /**
     * 状态(0待处理 1跟进中 2已完成)
     */
    private Integer status;

    /**
     * 跟进人
     */
    private String followUp;

    /**
     * 退改原因
     */
    private String changeReason;

    /**
     * 退改时间
     */
    private Date refundDt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理结果(1同意 2拒绝)
     */
    private Integer handleResult;

    /**
     * 我司退客户金额
     */
    private BigDecimal agentRefund;

    /**
     * 我司退客户金额币种
     */
    private String agentRefundCurrency;

    /**
     * 供应商退我司金额
     */
    private BigDecimal supplyRefund;

    /**
     * 供应商退我司金额币种
     */
    private BigDecimal supplyRefundCurrency;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private String updatedDt;

    /**
     * 附件
     */
    private List<FileResp> fileList;

    /**
     * 详情信息
     */
    private List<OrderChangeWordOrderTaskItemResp> itemList;
}
