package com.tiangong.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 订单退款任务日志
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@TableName("o_order_refund_task_log")
public class OrderRefundTaskLogPO extends Model<OrderRefundTaskLogPO> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 退款任务ID
     */
    private Integer refundTaskId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 删除状态
     */
    private Integer deleted;

}
