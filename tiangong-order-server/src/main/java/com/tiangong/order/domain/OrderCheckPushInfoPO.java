package com.tiangong.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * 入住明细推送表
 *
 * <AUTHOR>
 * @date ： 2024/3/13
 */
@Data
@Table(name = "o_order_check_push_info")
public class OrderCheckPushInfoPO {
    private static final long serialVersionUID = 1L;

    /**
     * 入住明细推送信息ID 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单编码 天宫订单编码
     */
    private String orderCode;
    /**
     * 入住日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDate;
    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkOutDate;
    /**
     * 分销商订单编码
     */
    private String channelOrderCode;
    /**
     * 分销商编码
     */
    private String agentCode;
    /**
     * 分销商名称
     */
    private String agentName;
    /**
     * 酒店名称 国内酒店传中文酒店名称，否则传英文酒店名称
     */
    private String hotelName;
    /**
     * 所有入住人。多个用英文逗号隔开
     */
    private String guest;
    /**
     * 推送状态 ['推送失败','入住明细异常','未填写入住明细','新建','已推送']
     */
    private Integer pushStatus;
    /**
     * 房型名称
     */
    private String roomName;
    /**
     * 价格计划名称
     */
    private String ratePlanName;
    /**
     * 渠道编码 ['平安']
     */
    private Integer channelCode;
    /**
     * 付款方式 ['预付','现付']
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 创建人 下单人
     */
    private String createdBy;
    /**
     * 创建时间 下单时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}
