package com.tiangong.order.dto;

import com.tiangong.dto.common.CompanyDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ProductDetailRequest {

    /**
     * 分销商编码 即客户
     */
    @NotNull(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 酒店id
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;

    /**
     * 间数
     */
    private Integer roomQty;


    /**
     * 入住总人数
     */
    //private Integer guestQuantity;


    /**
     * 运营商编码
     */
    private String companyCode = CompanyDTO.COMPANY_CODE;

    /**
     * 登陆账号
     */
    private String userAccount;

    /**
     * 语言
     */
    private String language;
}
