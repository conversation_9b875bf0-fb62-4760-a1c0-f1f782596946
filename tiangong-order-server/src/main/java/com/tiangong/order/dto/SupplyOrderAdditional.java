package com.tiangong.order.dto;

import com.tiangong.order.domain.SupplyOrderPO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SupplyOrderAdditional {
    private String supplyOrderCode;// 供货单编码
    private BigDecimal supplyOrderAdditionalCharges;// 供货单附加费
    private Date firstDate;// 第一天
    private SupplyOrderPO supplyOrderPO;// 供货单信息
    private BigDecimal sumBasePrice;// 底价
}
