package com.tiangong.order.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BaiWangOpenInvoiceResultDTO {
    /**
     * 发票号
     */
    private String invoiceNo;
    /**
     * 开票流水号
     */
    private String serialNo;
    /**
     * 销售方税号
     */
    private String taxNo;
    /**
     * 销售方名称
     */
    private String taxOrgName;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 合计税额
     */
    private BigDecimal taxAmt;
}
