package com.tiangong.order.dto;

import com.tiangong.order.domain.SupplyOrderFinancePO;
import com.tiangong.order.domain.SupplyOrderPO;
import lombok.Data;

import java.util.List;

@Data
public class AssemblySupplyOrderDTO {

    /**
     * 供货单
     */
    private SupplyOrderPO supplyOrder;

    /**
     * 供货单财务信息
     */
    private SupplyOrderFinancePO supplyOrderFinance;

    /**
     * 供货产品
     */
    private List<AssemblySupplyProductDTO> supplyProductList;

    /**
     * 供货单奖励财务信息
     */
    private SupplyOrderFinancePO supplyRewardFinance;

    /**
     * 供货单返佣财务信息
     */
    private SupplyOrderFinancePO supplyRebateFinance;

}
