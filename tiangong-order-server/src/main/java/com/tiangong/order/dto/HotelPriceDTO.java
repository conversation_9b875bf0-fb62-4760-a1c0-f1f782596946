package com.tiangong.order.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class HotelPriceDTO {

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店星级
     */
    private Integer hotelStar;

    /**
     * 酒店地址
     */
    private String hotelAddress;


    /**
     * 售卖价格
     */
    private BigDecimal lowestPrice;

    /**
     * 酒店主图
     */
    private String mainUrl;

    /**
     * 行政区
     */
    private String districtName;

    /**
     * 商业区
     */
    private String businessName;

    /**
     * 评分
     */
    private String hotelScore;

}
