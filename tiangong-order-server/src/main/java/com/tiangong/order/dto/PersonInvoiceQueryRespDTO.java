package com.tiangong.order.dto;

import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class PersonInvoiceQueryRespDTO implements Serializable {
    /**
     * 发票id
     */
    private Integer id;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 酒店名
     */
    private String hotelName;
    /**
     * 入住人
     */
    private String guest;
    /**
     * 入住时间
     */
    private String startTime;
    /**
     * 离开时间
     */
    private String endTime;
    /**
     * 应收
     */
    private String orderAmt;
    /**
     * 实收
     */
    private String receivedAmt;
    /**
     * 支付方式
     */
    private Integer isMixedPay;
    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 开票时间
     */
    private String invoiceDate;
    /**
     * 申请时间
     */
    private String applyDate;

    /**
     * 发送状态(0 未发送, 1 已发送)
     */
    private Integer sendStatus;
    /**
     * 开票状态(0 待开票, 1 已开票)
     */
    private Integer invoiceStatus;
    /**
     * 票单/发票号
     */
    private String invoiceNo;
    /**
     * 发票文件名
     */
    private String invoiceFileName;
    /**
     * 发票url 多个逗号隔开
     */
    private String invoiceUrls;
    /**
     * 售价币种
     */
    private Integer saleCurrency;
    /**
     * 商家币种
     */
    private String receivedAmtOrgCurrency;
}
