package com.tiangong.order.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CallbackBillBackInfoRequestDTO {

    /**
     * 付款主体ID
     */
    private String paySubjectId;

    /**
     * vcc 卡号
     */
    private String vccCardNumber;

    /**
     * vcc 创建时间
     */
    private String vccCreateTime;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 业务单号
     */
    private String businessCode;

    /**
     * 交易标题
     */
    private String transactionTitle;

    /**
     * 交易内容
     */
    private String transactionContent;

    /**
     * 业务应付金额
     */
    private BigDecimal businessPayableAmount;

    /**
     * 业务应付币种
     */
    private String businessPayableCurrency;

    /**
     * 开卡金额
     */
    private BigDecimal cardOpeningAmount;

    /**
     * 开卡金额币种
     */
    private String cardOpeningCurrency;

    /**
     * vcc卡币种
     */
    private String vccCardCurrency;

    /**
     * 卡内余额
     */
    private String cardBalance;

    /**
     * 刷卡次数
     */
    private Integer vccCardAvailableCount;

    /**
     * 有效开始日期
     */
    private String validStartDt;

    /**
     * 有效结束日期
     */
    private String validEndDt;

    /**
     * vcc卡状态
     */
    private String vccCardStatus;

    /**
     * vcc卡外部确认号
     */
    private String externalConfirmNum;

    /**
     * vcc卡内部确认号
     */
    private String internalConfirmNum;
}