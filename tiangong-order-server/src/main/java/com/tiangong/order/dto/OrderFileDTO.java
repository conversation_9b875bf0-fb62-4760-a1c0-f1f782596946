package com.tiangong.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class OrderFileDTO {

    private Integer id;

    private String fileUrl;

    private String fileName;

    private String suffixName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;

    private String createdBy;
}
