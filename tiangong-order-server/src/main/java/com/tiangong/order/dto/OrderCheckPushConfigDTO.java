package com.tiangong.order.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ： 2024/3/18
 */
@Data
public class OrderCheckPushConfigDTO {

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 渠道编码
     */
    private Integer channelCode;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 分销商编码
     */
    private String agentCode;
    /**
     * 商家编码 天宫商家编码
     */
    private String merchantCode;
    /**
     * 订单类型 ['预付','现付','预付+现付']
     */
    private Integer orderType;
    /**
     * 验签公钥
     */
    private String publicKey;
    /**
     * 验签私钥
     */
    private String privateKey;
    /**
     * 加解/秘密钥
     */
    private String secretKey;
    /**
     * 平安向量值
     */
    private String ivParameter;
    /**
     * 入住明细推送接口
     */
    private String orderCheckDetailNotifyUrl;
    /**
     * 是否有效 ['无效','有效']
     */
    private Integer isActive;
    /**
     * 创建人 下单人
     */
    private String createdBy;
    /**
     * 创建时间 下单时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}
