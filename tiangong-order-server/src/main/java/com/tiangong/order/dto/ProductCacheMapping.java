package com.tiangong.order.dto;

import com.tiangong.dto.product.HourlyRoomInfo;
import com.tiangong.util.Md5Util;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品id缓存信息
 */
@Data
@Slf4j
public class ProductCacheMapping {

    /**
     * 产品id
     */
    private String productId;

    /**
     * 是否到店付：1-是，0或其它-否， 默认0
     */
    private Integer payAtHotelFlag;

    /**
     * 提示信息，带有 html 标签，展示时需支持按html 标签
     */
//    private String reminder;

    /**
     * 是否钟点房1是0否 默认日历房
     */
    private int hourly;

    /**
     * 钟点房对象
     */
    private HourlyRoomInfo hourlyRoomInfo;

    /**
     * 获取当前对象的唯一ID
     *
     * @return
     */
    public String getMd5Uuid() {
        try {
            return Md5Util.md5Encode(this.getProductId() + this.getPayAtHotelFlag() + this.getHourly());
        } catch (Exception e) {
            log.error("getMd5Uuid error", e);
        }
        return null;
    }
}
