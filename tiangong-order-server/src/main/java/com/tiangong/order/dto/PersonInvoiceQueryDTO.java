package com.tiangong.order.dto;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

@Data
public class PersonInvoiceQueryDTO extends BasePage {
    /**
     * 申请日期开始
     */
    private String startApplyDate;
    /**
     * 申请日期结束
     */
    private String endApplyDate;
    /**
     * 下单日期开始
     */
    private String startDate;
    /**
     * 下单日期结束
     */
    private String endDate;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 发送状态
     */
    private Integer sendStatus;
    /**
     * 发票状态
     */
    private Integer invoiceStatus;
    /**
     * 入住人
     */
    private String guest;
    /**
     * 酒店id
     */
    private String hotelId;
    /**
     * 查询口径 0-下单日期、1-入住日期、2-离店日期
     */
    private Integer dateQueryType;
}
