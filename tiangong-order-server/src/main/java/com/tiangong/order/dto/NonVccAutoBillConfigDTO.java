package com.tiangong.order.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/10 14:20
 * @Description:
 */

@Data
public class NonVccAutoBillConfigDTO implements Serializable {

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 天宫付款方式 0-酒店给我方发送付款链接 1-银行转账 2-WorldFirst账户收款
     */
    private Integer paymentMethod;

    /**
     * 付款链接发送邮箱 (英文逗号进行分隔，最多支持3个邮箱)
     */
    private String paymentLinkEmails;

    /**
     * 酒店付款联系人
     */
    private String hotelPaymentContactPerson;

    /**
     * 酒店收款开户行
     */
    private String hotelCollectionBank;

    /**
     * 酒店收款账户名
     */
    private String hotelCollectionAccountName;

    /**
     * 酒店收款账号
     */
    private String hotelCollectionAccountNumber;

    /**
     * WorldFirst账户号
     */
    private String worldFirstAccountNumber;

    /**
     * 备注
     */
    private String remarks;

}
