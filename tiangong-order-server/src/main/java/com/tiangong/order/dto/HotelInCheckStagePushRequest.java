package com.tiangong.order.dto;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ： 2024/3/14
 */
@Data
public class HotelInCheckStagePushRequest {

    /**
     * 请求时间，格式：yyyyMMddhhmmss
     */
    private String reqTime;

    /**
     * 请求流水号，保持唯一性
     */
    private String reqNum;

    /**
     * 请求业务报文,报文为加密报文
     */
    private String bizContent;
    //private Map<String,String> bizContent;

    /**
     * 对解密后的业务报文+reqTime进行SHA256WithRSA签名
     */
    private String bizSign;
}
