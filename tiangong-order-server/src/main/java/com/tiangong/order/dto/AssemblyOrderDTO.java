package com.tiangong.order.dto;

import com.tiangong.order.domain.*;
import lombok.Data;

import java.util.List;

@Data
public class AssemblyOrderDTO {

    /**
     * 订单信息
     */
    private OrderPO order;

    /**
     * 订单扩展信息
     */
    private OrderExtendPO orderExtendPO;

    /**
     * 订单附加服务费信息
     */
    private OrderAdditionalChargesPO orderAdditionalChargesPO;

    /**
     * 订单财务信息
     */
    private OrderFinancePO orderFinance;

    /**
     * 价格明细
     */
    private List<OrderProductPricePO> orderProductPriceList;

    /**
     * 入住人
     */
    private List<GuestPO> guestList;

    /**
     * 供货单
     */
    private List<AssemblySupplyOrderDTO> supplyOrderList;

    /**
     * 订单担保条款
     */
    private OrderRestrictEntity orderRestrictEntity;

    /**
     * 订单产品提示信息
     */
    private List<OrderProductTipPO> orderProductTips;

    /**
     * 排查问题
     */
    private String requestId;

}
