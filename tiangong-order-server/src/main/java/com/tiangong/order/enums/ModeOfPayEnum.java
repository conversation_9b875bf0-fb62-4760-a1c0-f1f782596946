package com.tiangong.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ModeOfPayEnum {

    BILLBACK(0, "BillBack", 2),
    DYNAMICVCC(1, "动态VCC", 1),
    FIXEDVCC(2, "固定VCC", null);

    private final Integer code;
    private final String desc;
    private final Integer bankCode;

    public static ModeOfPayEnum getModeOfPayEnum(Integer code) {
        if (code != null) {
            for (ModeOfPayEnum modeOfPayEnum : ModeOfPayEnum.values()) {
                if (modeOfPayEnum.getCode().equals(code)) {
                    return modeOfPayEnum;
                }
            }
        }
        return null;
    }
}