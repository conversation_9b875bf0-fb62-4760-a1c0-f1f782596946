package com.tiangong.order.util;



import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 自定义线程池
 *
 * <AUTHOR>
 * @date ： 2023/9/15
 */
@Slf4j
public class ExecutorUtil {

    public static ArrayBlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(1000);

    public static ThreadPoolExecutor executorService = new ThreadPoolExecutor(5, 10,
            60, TimeUnit.SECONDS, queue) {
        @Override
        public void execute(Runnable command) {
            try {
                super.execute(command);
            } catch (RejectedExecutionException e) {
                // 当队列已满时，调用该方法的线程会被阻塞
                try {
                    // 将任务放入队列中，进行阻塞等待
                    getQueue().put(command);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    log.error("执行推送入住明细任务失败", e);
                }
            }
        }
    };

}