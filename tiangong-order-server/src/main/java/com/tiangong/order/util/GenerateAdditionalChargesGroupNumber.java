package com.tiangong.order.util;

import com.tiangong.keys.RedisKey;
import com.tiangong.util.StrUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/8/20 14:23
 * @Description:
 */
@Component
public class GenerateAdditionalChargesGroupNumber {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public String generateAdditionalChargesGroupNumber() {

        String result = null;
        for (int i = 0; i < 100; i++) {
            Random random = new Random();
            LocalDateTime time = LocalDateTime.now();
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd");
            StringBuilder resultTmp = new StringBuilder(dtf.format(time)).append((System.currentTimeMillis() / 1000) % 1000000).append(random.nextInt(1000));
            result = resultTmp.toString();

            Boolean isExist = stringRedisTemplate.opsForValue().setIfAbsent(StrUtilX.concat(RedisKey.GENERATE_EXPORT_CODE, result), "making", 10, TimeUnit.SECONDS);
            if (Boolean.TRUE.equals(isExist)) {
                return result;
            }
        }
        return result;
    }

}
