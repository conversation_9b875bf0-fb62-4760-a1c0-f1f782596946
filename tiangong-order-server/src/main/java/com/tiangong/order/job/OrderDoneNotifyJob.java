package com.tiangong.order.job;


import com.tiangong.enums.ChannelEnum;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.domain.SupplyOrderPO;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.order.remote.request.QueryOrderReq;
import com.tiangong.order.service.OrderService;
import com.tiangong.order.service.SupplyOrderService;
import com.tiangong.util.PageExecuteHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 每日16:00更新离店当天的订单,供货单为已完成
 * 并推送给b2b分销商客户
 */
@Slf4j
@Component
public class OrderDoneNotifyJob {

    @Autowired
    private OrderService orderService;

    @Autowired
    private SupplyOrderService supplyOrderService;

    @XxlJob("orderDoneNotify")
    public void orderDoneNotify() {
        try {
            XxlJobHelper.log("执行订单,供货单状态更新推送分销商定时任务开始");
            // 分页查询符合条件的订单
            QueryOrderReq queryOrderReq = new QueryOrderReq();
//            queryOrderReq.setChannelCode(ChannelEnum.B2B.value);
            queryOrderReq.setEndTime(new Date());
            queryOrderReq.setOrderConfirmationStatus(ConfirmationStatusEnum.CONFIRMED.key);
            queryOrderReq.setPageSize(100);
            PageExecuteHelper.executePageEach(queryOrderReq, orderService::queryPage, this::updateAndPush);
            XxlJobHelper.log("执行订单,供货单状态更新推送分销商定时任务结束");
        } catch (Exception e) {
            log.error("执行订单,供货单状态更新推送分销商定时任务异常", e);
            XxlJobHelper.log("执行订单,供货单状态更新推送分销商定时任务异常", e);
        }
    }

    /**
     * 更新订单供货单状态并推送渠道
     */
    @Transactional
    public void updateAndPush(List<OrderPO> list) {
        //更新符合条件的订单,供货单
        Map<Integer, OrderPO> orderMap = list.stream().collect(Collectors.toConcurrentMap(OrderPO::getId, Function.identity(), (k1, k2) -> k2));
        List<Integer> orderIds = list.stream().map(OrderPO::getId).collect(Collectors.toList());
        List<SupplyOrderPO> supplyOrderPOS = supplyOrderService.querySupplyOrder(orderIds);
        List<SupplyOrderPO> supplyOrderList = supplyOrderPOS.stream()
                .filter(e ->
                {
                    if (!Objects.equals(e.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
                        orderMap.remove(e.getOrderId());
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());
        Map<Integer, Integer> maps = supplyOrderList.stream().collect(Collectors.toMap(SupplyOrderPO::getId, SupplyOrderPO::getOrderId, (k1, k2) -> k2));
        if (MapUtils.isNotEmpty(maps) && MapUtils.isNotEmpty(orderMap)) {
            orderService.batchUpdateOrderStatusDone(new LinkedList<>(maps.values()));
            supplyOrderService.batchupdateSupplyOrder(new LinkedList<>(maps.keySet()));
            orderMap.forEach((key, orderPO) -> orderService.pushOrderStatusDone(orderPO));
        }
    }

}
