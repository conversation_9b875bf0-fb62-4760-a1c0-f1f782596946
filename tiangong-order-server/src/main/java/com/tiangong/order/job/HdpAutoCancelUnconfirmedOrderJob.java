package com.tiangong.order.job;

import cn.hutool.json.JSONUtil;
import com.tiangong.common.Response;
import com.tiangong.enums.ChannelEnum;
import com.tiangong.enums.SlsEnum;
import com.tiangong.enums.SlsLogLevelEnum;
import com.tiangong.order.config.SettingsConstant;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.order.remote.request.CancelOtaOrderDTO;
import com.tiangong.order.remote.request.QueryOrderReq;
import com.tiangong.order.remote.response.OtaOrderDTO;
import com.tiangong.order.service.BookingService;
import com.tiangong.order.service.OrderService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.PageExecuteHelper;
import com.tiangong.util.SlsLoggerUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * hdp待确认订单未支付超时自动取消订单任务
 */
@Slf4j
@Component
public class HdpAutoCancelUnconfirmedOrderJob {

    @Autowired
    private OrderService orderService;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private BookingService bookingService;

    @Autowired
    private SettingsConstant settingsConstant;

    @XxlJob("hdpAutoCancelUnconfirmedOrderJob")
    public void hdpAutoCancelUnconfirmedOrderJob() {
        try {
            XxlJobHelper.log("执行hdp待确认订单未支付超时自动取消订单任务开始");
            // 分页查询符合条件的订单
            QueryOrderReq queryOrderReq = new QueryOrderReq();
            queryOrderReq.setChannelCode(ChannelEnum.HDP.value);
            Date date = DateUtilX.getDate(new Date(), 0, 0, settingsConstant.getHdpAutoCancelUnconfirmedOrderMinute());
            queryOrderReq.setCreatedDt(date);
            queryOrderReq.setOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
            queryOrderReq.setPageSize(10);
            PageExecuteHelper.executePageEach(queryOrderReq, orderService::queryPage, this::cancelOrder);
            XxlJobHelper.log("执行hdp待确认订单未支付超时自动取消订单任务结束");
        } catch (Exception e) {
            log.error("执行hdp待确认订单未支付超时自动取消订单任务异常", e);
            XxlJobHelper.log("执行hdp待确认订单未支付超时自动取消订单任务异常", e);
        }
    }

    /**
     * 更新订单供货单状态并推送渠道
     */
    public void cancelOrder(List<OrderPO> list) {
        if (CollUtilX.isEmpty(list)) {
            return;
        }
        for (OrderPO orderPO : list) {
            Date start = new Date();
            String req = null;
            String resp = null;
            try {
                CancelOtaOrderDTO cancelOtaOrderDTO = new CancelOtaOrderDTO();
                cancelOtaOrderDTO.setOrderId(orderPO.getOrderCode());
                cancelOtaOrderDTO.setAgentCode(orderPO.getAgentCode());
                cancelOtaOrderDTO.setChannelCode(ChannelEnum.HDP.value);
                cancelOtaOrderDTO.setCancelReason("未支付自动取消");
                req = JSONUtil.toJsonStr(cancelOtaOrderDTO);
                Response<OtaOrderDTO> response = bookingService.cancelOtaOrder(cancelOtaOrderDTO);
                resp = JSONUtil.toJsonStr(response);
            } catch (Exception e) {
                log.error("执行hdp待确认订单未支付超时自动取消订单任务，取消订单异常", e);
            } finally {
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
                map.put(SlsEnum.NAME.getType(), "hdp待确认订单未支付超时自动取消订单任务");
                map.put(SlsEnum.MESSAGE.getType(), "未支付自动取消");
                map.put("request", req);
                map.put("response", resp);
                map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
                slsLoggerUtil.saveLog(map, "cancelOtaOrder", "tiangong-order-server");
            }
        }
    }

}
