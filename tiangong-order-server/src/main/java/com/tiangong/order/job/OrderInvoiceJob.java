package com.tiangong.order.job;

import com.alibaba.fastjson.JSON;
import com.tiangong.common.Response;
import com.tiangong.constanct.MqConstants;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.InvoiceStatusEnum;
import com.tiangong.exception.ServiceException;
import com.tiangong.invoice.dto.AutoCreateAgentInvoiceDTO;
import com.tiangong.order.config.SettingsConstant;
import com.tiangong.order.domain.OrderInvoicePO;
import com.tiangong.order.dto.OrderInvoiceAutoDTO;
import com.tiangong.order.dto.OrderInvoiceDownloadDTO;
import com.tiangong.order.mapper.OrderInvoiceMapper;
import com.tiangong.order.mq.OrderInvoiceMQListenerBase;
import com.tiangong.order.remote.response.OrderInvoiceWithCompanyDO;
import com.tiangong.order.service.PersonInvoiceService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.CommonConstants;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 订单发票任务
 */
@Slf4j
@Component
public class OrderInvoiceJob {

    @Autowired
    private OrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private PersonInvoiceService personInvoiceService;
    @Autowired
    private SettingsConstant settingsConstant;
    /**
     * 自动下载全版发票任务
     * 查询符合条件的订单发票并进行自动下载
     * 条件：
     * 1、订单开票状态是开票成功待下载
     * 2、调用过自动开票接口成功的
     */
    @XxlJob("autoDownLoadOrderInvoiceJob")
    public void autoDownLoadOrderInvoiceJob() {
        try {
            XxlJobHelper.log("执行处理自动下载全版发票任务开始");
            String orderCode = XxlJobHelper.getJobParam();
            if (!CommonConstants.YES.equals(settingsConstant.getAutoInvoiceEnabled())) {
                log.info("没有符合条件的订单发票需要自动下载,开票配置未开启");
                return;
            }
            // 查询符合条件的订单发票 查近3个月的即可
            List<OrderInvoicePO> orderInvoiceList = queryAutoDownloadInvoiceList();
            if (StrUtilX.isNotEmpty(orderCode) && CollUtilX.isNotEmpty(orderInvoiceList)) {
                orderInvoiceList = orderInvoiceList.stream().filter(e -> e.getOrderCode().equals(orderCode)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(orderInvoiceList)) {
                log.info("没有符合条件的订单发票需要自动下载");
                XxlJobHelper.log("没有符合条件的订单发票需要自动下载");
                XxlJobHelper.log("执行处理自动下载全版发票任务结束");
                return;
            }
            log.info("自动下载全版发票任务查询到符合条件的订单发票：{}", orderInvoiceList.stream().map(OrderInvoicePO::getOrderCode).collect(Collectors.toList()));
            XxlJobHelper.log("查询到符合条件的订单发票数量：" + orderInvoiceList.size());

            // 处理每个订单发票，发送MQ消息
            for (OrderInvoicePO orderInvoice : orderInvoiceList) {
                try {
                    // 创建MQ消息
                    OrderInvoiceDownloadDTO downloadDTO = new OrderInvoiceDownloadDTO();
                    downloadDTO.setInvoiceId(orderInvoice.getId());
                    downloadDTO.setOrderCode(orderInvoice.getOrderCode());
                    downloadDTO.setInvoiceNo(orderInvoice.getInvoiceNo());
                    downloadDTO.setMessageId(UUID.randomUUID().toString());

                    // 构建MQ消息
                    Message<String> message = MessageBuilder.withPayload(JSON.toJSONString(downloadDTO))
                            .setHeader("messageId", downloadDTO.getMessageId())
                            .setHeader(OrderInvoiceMQListenerBase.SERVICE_NAME, OrderInvoiceMQListenerBase.ORDER_INVOICE_DOWNLOAD_MQ_LISTENER)
                            .build();

                    // 发送MQ消息
                    rocketMQTemplate.syncSend(MqConstants.ORDER_INVOICE_MQ, message);

                    XxlJobHelper.log("发送订单发票自动下载消息成功，订单号：" + orderInvoice.getOrderCode() +
                            "，发票号：" + orderInvoice.getInvoiceNo() + "，消息ID：" + downloadDTO.getMessageId());
                } catch (Exception e) {
                    log.error("发送订单发票自动下载消息异常，订单号：{}，发票ID：{}", orderInvoice.getOrderCode(), orderInvoice.getId(), e);
                    XxlJobHelper.log("发送订单发票自动下载消息异常，订单号：" + orderInvoice.getOrderCode() +
                            "，发票ID：" + orderInvoice.getId() + "，异常信息：" + e.getMessage());
                }
            }

            XxlJobHelper.log("执行处理自动下载全版发票任务结束");
        } catch (Exception e) {
            log.error("执行处理自动下载全版发票任务异常", e);
            XxlJobHelper.log("执行处理自动下载全版发票任务异常：" + e.getMessage());
        }
    }

    /**
     * 自动开票任务
     * 查询符合条件的订单发票并进行自动开票
     * 条件：
     * 1、订单开票状态是待开票
     * 2、订单是已确认或已完成
     * 3、且离店时间是近3个月的日期（当天往前的三个月）
     * 4、申请开票金额需要小于等于订单待开票金额
     * 5、发票类型为电子普票、电子专票
     */
    @XxlJob("autoOpenOrderInvoiceJob")
    public void autoOpenOrderInvoiceJob() {
        try {
            String orderCode = XxlJobHelper.getJobParam();
            XxlJobHelper.log("执行处理自动开票任务开始");
            if (!CommonConstants.YES.equals(settingsConstant.getAutoInvoiceEnabled())) {
                log.info("没有符合条件的订单发票需要自动开票,开票配置未开启");
                return;
            }
            // 查询符合条件的订单发票（包含商家编码）
            List<OrderInvoiceWithCompanyDO> allOrderInvoiceList = queryAutoInvoiceList();
            if (StrUtilX.isNotEmpty(orderCode) && CollUtilX.isNotEmpty(allOrderInvoiceList)) {
                allOrderInvoiceList = allOrderInvoiceList.stream().filter(e -> e.getOrderCode().equals(orderCode)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(allOrderInvoiceList)) {
                log.info("没有符合条件的订单发票");
                XxlJobHelper.log("没有符合条件的订单发票");
                XxlJobHelper.log("执行处理自动开票任务结束");
                return;
            }
            log.info("自动开票任务查询到符合条件的订单发票：{}", allOrderInvoiceList.stream().map(OrderInvoiceWithCompanyDO::getOrderCode).collect(Collectors.toList()));
            for (OrderInvoiceWithCompanyDO orderInvoice : allOrderInvoiceList) {
                try {
                    // 创建MQ消息
                    OrderInvoiceAutoDTO orderInvoiceAutoDTO = new OrderInvoiceAutoDTO();
                    orderInvoiceAutoDTO.setOrderCode(orderInvoice.getOrderCode());
                    orderInvoiceAutoDTO.setOrderInvoiceId(orderInvoice.getId());
                    orderInvoiceAutoDTO.setCompanyCode(orderInvoice.getCompanyCode());
                    orderInvoiceAutoDTO.setMessageId(UUID.randomUUID().toString());
                    // 构建MQ消息
                    Message<String> message = MessageBuilder.withPayload(JSON.toJSONString(orderInvoiceAutoDTO))
                            .setHeader("messageId", orderInvoiceAutoDTO.getMessageId())
                            .setHeader(OrderInvoiceMQListenerBase.SERVICE_NAME, OrderInvoiceMQListenerBase.ORDER_INVOICE_AUTO_MQ_LISTENER)
                            .build();

                    // 发送MQ消息
                    rocketMQTemplate.syncSend(MqConstants.ORDER_INVOICE_MQ, message);

                    XxlJobHelper.log("发送订单发票自动开票消息成功，订单号：" + orderInvoice.getOrderCode() +
                            "，商家编码：" + orderInvoice.getCompanyCode() + "，消息ID：" + orderInvoiceAutoDTO.getMessageId());
                } catch (Exception e) {
                    log.error("发送订单发票自动开票消息异常，订单号：{}，商家编码：{}", orderInvoice.getOrderCode(), orderInvoice.getCompanyCode(), e);
                    XxlJobHelper.log("发送订单发票自动开票消息异常，订单号：" + orderInvoice.getOrderCode() +
                            "，商家编码：" + orderInvoice.getCompanyCode() + "，异常信息：" + e.getMessage());
                }
            }
            XxlJobHelper.log("执行处理自动开票任务结束");
        } catch (Exception e) {
            log.error("执行处理自动开票任务异常", e);
            XxlJobHelper.log("执行处理自动开票任务异常", e);
        }
    }

    /**
     * 查询符合自动开票条件的订单发票列表
     * 条件：
     * 1、订单开票状态是待开票
     * 2、订单是已确认或已完成
     * 3、且离店时间是近3个月的日期（当天往前的三个月）
     * 4、发票类型为电子普票、电子专票
     * 5、自动开票接口调用状态为0或null
     *
     * @return 符合条件的订单发票列表（包含商家编码）
     */
    private List<OrderInvoiceWithCompanyDO> queryAutoInvoiceList() {
        return personInvoiceService.queryAutoInvoiceList(null, false);
    }

    /**
     * 查询符合自动下载条件的订单发票列表
     * 条件：
     * 1、开票状态为开票成功待下载
     * 2、调用过自动发票接口
     *
     * @return 符合条件的订单发票列表
     */
    private List<OrderInvoicePO> queryAutoDownloadInvoiceList() {
        // 设置查询参数
        List<Integer> invoiceStatusList = Collections.singletonList(InvoiceStatusEnum.INVOICE_SUCCESS_WAIT_DOWNLOAD.getNo()); // 开票成功待下载

        // 调用Mapper方法查询符合条件的订单发票
        List<OrderInvoicePO> orderInvoiceList = orderInvoiceMapper.queryAutoDownloadInvoiceList(
                invoiceStatusList
        );

        if (CollectionUtils.isEmpty(orderInvoiceList)) {
            return Collections.emptyList();
        }
        return orderInvoiceList;
    }

    /**
     * 自动创建票单任务
     */
    @XxlJob("autoCreateAgentInvoiceJob")
    public void autoCreateAgentInvoiceJob() {
        try {
            XxlJobHelper.log("执行自动创建票单任务开始");
            String orderCode = XxlJobHelper.getJobParam();
            if (!CommonConstants.YES.equals(settingsConstant.getAutoInvoiceEnabled())) {
                log.info("没有符合条件的订单发票需要自动创建票单,开票配置未开启");
                return;
            }
            //查询符合条件的发票
            List<OrderInvoiceWithCompanyDO> orderInvoiceWithCompanyDOS = queryAutoCreateBillList();
            if (StrUtilX.isNotEmpty(orderCode) && CollUtilX.isNotEmpty(orderInvoiceWithCompanyDOS)) {
                orderInvoiceWithCompanyDOS = orderInvoiceWithCompanyDOS.stream().filter(e -> e.getOrderCode().equals(orderCode)).collect(Collectors.toList());
            }
            if (CollUtilX.isEmpty(orderInvoiceWithCompanyDOS)) {
                log.info("自动创建票单任务没有符合条件的订单发票");
                XxlJobHelper.log("没有符合条件的订单发票");
                return;
            }
            log.info("自动创建票单任务查询到符合条件的订单发票：{}", orderInvoiceWithCompanyDOS.stream().map(OrderInvoiceWithCompanyDO::getOrderCode).collect(Collectors.toList()));
            Date createTime = new Date();
            for (OrderInvoiceWithCompanyDO orderInvoiceWithCompanyDO : orderInvoiceWithCompanyDOS) {
                try {
                    // 创建MQ消息
                    AutoCreateAgentInvoiceDTO autoCreateAgentInvoiceDTO = new AutoCreateAgentInvoiceDTO();
                    autoCreateAgentInvoiceDTO.setOrderInvoiceId(orderInvoiceWithCompanyDO.getId());
                    autoCreateAgentInvoiceDTO.setOrderCode(orderInvoiceWithCompanyDO.getOrderCode());
                    autoCreateAgentInvoiceDTO.setInvoiceNo(orderInvoiceWithCompanyDO.getInvoiceNo());
                    autoCreateAgentInvoiceDTO.setInvoiceCode(orderInvoiceWithCompanyDO.getInvoiceCode());
                    autoCreateAgentInvoiceDTO.setCompanyCode(orderInvoiceWithCompanyDO.getCompanyCode());
                    autoCreateAgentInvoiceDTO.setMessageId(UUID.randomUUID().toString());
                    autoCreateAgentInvoiceDTO.setCreateTime(createTime);
                    autoCreateAgentInvoiceDTO.setAgentCode(orderInvoiceWithCompanyDO.getAgentCode());
                    autoCreateAgentInvoiceDTO.setAgentName(orderInvoiceWithCompanyDO.getAgentName());
                    autoCreateAgentInvoiceDTO.setInvoiceAmt(orderInvoiceWithCompanyDO.getInvoiceAmount());
                    autoCreateAgentInvoiceDTO.setOrderId(orderInvoiceWithCompanyDO.getOrderId());
                    if (StrUtilX.isEmpty(autoCreateAgentInvoiceDTO.getOrderCode())) {
                        throw new ServiceException("订单编号为空,自动创建票单任务结束");
                    }
                    // 构建MQ消息
                    Message<String> message = MessageBuilder.withPayload(JSON.toJSONString(autoCreateAgentInvoiceDTO))
                            .setHeader("messageId", autoCreateAgentInvoiceDTO.getMessageId())
                            .setHeader(OrderInvoiceMQListenerBase.SERVICE_NAME, OrderInvoiceMQListenerBase.AUTO_CREATE_AGENT_INVOICE_MQ_LISTENER)
                            .build();

                    // 发送MQ消息
                    rocketMQTemplate.syncSend(MqConstants.ORDER_INVOICE_MQ, message);

                    XxlJobHelper.log("发送自动创建分销商发票消息成功，订单号：" + orderInvoiceWithCompanyDO.getOrderCode() +
                            "，发票号：" + orderInvoiceWithCompanyDO.getInvoiceNo() +
                            "，消息ID：" + autoCreateAgentInvoiceDTO.getMessageId());
                } catch (Exception e) {
                    log.error("发送自动创建分销商发票消息异常，订单号：{}，发票ID：{}",
                            orderInvoiceWithCompanyDO.getOrderCode(), orderInvoiceWithCompanyDO.getId(), e);
                    XxlJobHelper.log("发送自动创建分销商发票消息异常，订单号：" + orderInvoiceWithCompanyDO.getOrderCode() +
                            "，发票ID：" + orderInvoiceWithCompanyDO.getId() +
                            "，异常信息：" + e.getMessage());
                }
            }
            XxlJobHelper.log("执行自动创建票单任务结束");
        } catch (Exception e) {
            log.error("执行自动创建票单任务异常", e);
            XxlJobHelper.log("执行自动创建票单任务异常：" + e.getMessage());
        }
    }

    /**
     * 查询符合自动开票条件的订单发票列表
     * 条件：
     * 1、订单开票状态是下载成功待创建票单
     * 2、订单是已确认或已完成
     * 3、且离店时间是近3个月的日期（当天往前的三个月）
     * 4、发票类型为电子普票、电子专票
     *
     * @return 符合条件的订单发票列表（包含商家编码）
     */
    private List<OrderInvoiceWithCompanyDO> queryAutoCreateBillList() {
        return personInvoiceService.queryAutoCreateBillList(null, false);
    }
}
