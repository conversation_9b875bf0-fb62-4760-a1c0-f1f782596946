package com.tiangong.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baiwang.open.entity.request.OutputEinvoiceQueryRequest;
import com.baiwang.open.entity.request.OutputFormatQueryQdInvoiceRequest;
import com.baiwang.open.entity.request.OutputInvoiceIssueRequest;
import com.baiwang.open.entity.request.node.OutputEinvoiceQueryInvoiceQueryParam;
import com.baiwang.open.entity.request.node.OutputFormatQueryQdInvoiceData;
import com.baiwang.open.entity.request.node.OutputInvoiceIssueInvoiceDetail;
import com.baiwang.open.entity.request.node.OutputInvoiceIssuePreInvoice;
import com.baiwang.open.entity.response.OutputEinvoiceQueryResponse;
import com.baiwang.open.entity.response.OutputFormatQueryQdInvoiceResponse;
import com.baiwang.open.entity.response.OutputInvoiceIssueResponse;
import com.baiwang.open.entity.response.node.OutputEinvoiceQuery;
import com.baiwang.open.entity.response.node.OutputInvoiceIssueInvoiceResult;
import com.baiwang.open.exception.BWOpenException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.agent.remote.AgentConfigRemote;
import com.tiangong.cloud.common.enums.BaseEnum;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.InvoiceNotifyPushRequest;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.DisB2BRemote;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.InvoiceStatusEnum;
import com.tiangong.enums.InvoiceTypeEnum;
import com.tiangong.enums.TicketTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.file.remote.FileRemote;
import com.tiangong.file.resp.FileResp;
import com.tiangong.invoice.dto.AgentInvoiceBaseRespDTO;
import com.tiangong.invoice.dto.InvoiceAttchDTO;
import com.tiangong.invoice.enums.InvoiceSourceTypeEnum;
import com.tiangong.invoice.remote.AgentInvoiceRemote;
import com.tiangong.invoice.req.AgentInvoiceAditReq;
import com.tiangong.invoice.req.AgentInvoiceBaseReqDTO;
import com.tiangong.invoice.req.AutoCreateAgentBillReq;
import com.tiangong.invoice.req.InvoiceableAgentInfoQueryReq;
import com.tiangong.invoice.resp.AutoCreateAgentBillResp;
import com.tiangong.invoice.resp.InvoiceableAgentInfoResp;
import com.tiangong.order.config.SettingsConstant;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.OrderInvoicePO;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.dto.*;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.order.enums.OrderInvoiceErrorTypeEnum;
import com.tiangong.order.mapper.OrderInvoiceMapper;
import com.tiangong.order.mapper.OrderMapper;
import com.tiangong.order.mapper.OrgMapper;
import com.tiangong.order.mapper.PersonInvoiceMapper;
import com.tiangong.order.remote.response.OrderInvoiceDTO;
import com.tiangong.order.remote.response.OrderInvoiceWithCompanyDO;
import com.tiangong.order.service.BaiWangInvoiceService;
import com.tiangong.order.service.OrderService;
import com.tiangong.order.service.PersonInvoiceService;
import com.tiangong.order.service.common.MongodbCommonService;
import com.tiangong.order.service.common.OrderCommonService;
import com.tiangong.organization.remote.CompanyRemote;
import com.tiangong.organization.remote.dto.AgentAddDTO;
import com.tiangong.organization.remote.dto.CompanyAddDTO;
import com.tiangong.organization.remote.dto.CompanySelectDTO;
import com.tiangong.req.MailAttachVO;
import com.tiangong.req.SendEmailReq;
import com.tiangong.util.*;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PersonInvoiceServiceImpl implements PersonInvoiceService {

    @Autowired
    private PersonInvoiceMapper personInvoiceMapper;
    @Autowired
    private DisB2BRemote disB2BRemote;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private OrderInvoiceMapper orderInvoiceMapper;
    @Autowired
    private SettingsConstant settingsConstant;
    @Autowired
    private OrderCommonService orderCommonService;
    @Autowired
    private SendCodeUtil sendCodeUtil;
    @Autowired
    private BaiWangInvoiceService baiWangInvoiceService;
    @Autowired
    private CompanyRemote companyRemote;
    @Autowired
    private AgentInvoiceRemote agentInvoiceRemote;
    @Autowired
    private FileRemote fileRemote;
    @Autowired
    private OrderService orderService;
    @Autowired
    private AgentConfigRemote agentConfigRemote;
    @Autowired
    private MongodbCommonService mongodbCommonService;
    private final static String EXP_INVOICE_MSG_FORMAT = "订单号{}自动开票出现异常，请查看订单日志后进行对应处理，并检查是否已处理。";

    @Override
    public PaginationSupportDTO<PersonInvoiceQueryRespDTO> queryOrderInvoiceList(PersonInvoiceQueryDTO request) {

        // 创建分页对象
        IPage<PersonInvoiceQueryRespDTO> page = new Page<>(request.getCurrentPage(), request.getPageSize());

        // 查询发票列表（分页）
        IPage<PersonInvoiceQueryRespDTO> invoicePage = personInvoiceMapper.queryOrderInvoiceList(page, request);

        List<PersonInvoiceQueryRespDTO> invoiceList = invoicePage.getRecords();
        if (CollectionUtils.isEmpty(invoiceList)) {
            return new PaginationSupportDTO<>();
        }
        List<Integer> orderIds = new ArrayList<>();
        List<String> orderCodes = new ArrayList<>();
        // 获取订单ID列表
        for (PersonInvoiceQueryRespDTO personInvoiceQueryRespDTO : invoiceList) {
            if (ObjectUtil.isNotEmpty(personInvoiceQueryRespDTO.getOrderId())) {
                orderIds.add(personInvoiceQueryRespDTO.getOrderId());
            }
            if (StringUtils.isNotEmpty(personInvoiceQueryRespDTO.getOrderCode())) {
                orderCodes.add(personInvoiceQueryRespDTO.getOrderCode());
            }
        }
        if (!CollectionUtils.isEmpty(orderIds)) {
            // 查询订单实收金额
            List<PersonInvoiceQueryRespDTO> receivedAmtList = personInvoiceMapper.queryOrderReceivedAmt(orderIds);

            if (!CollectionUtils.isEmpty(receivedAmtList)) {
                // 构建订单ID与实收金额的映射
                Map<Integer, String> orderReceivedAmtMap = receivedAmtList.stream()
                        .collect(Collectors.toMap(
                                PersonInvoiceQueryRespDTO::getOrderId,
                                PersonInvoiceQueryRespDTO::getReceivedAmt,
                                (v1, v2) -> v1));
                for (PersonInvoiceQueryRespDTO invoice : invoiceList) {
                    // 设置实收金额
                    String receivedAmt = orderReceivedAmtMap.get(invoice.getOrderId());
                    if (!StringUtils.isEmpty(receivedAmt)) {
                        invoice.setReceivedAmt(receivedAmt);
                    }

                }

            }
        }
        if (CollUtil.isNotEmpty(orderCodes)) {
            List<OrderRateDto> orderRateDtos = mongodbCommonService.queryOrderRateFromMongoDb(orderCodes);
            if (CollUtil.isNotEmpty(orderRateDtos)) {
                Map<String, OrderRateDto> orderRateMap = orderRateDtos.stream()
                        .collect(Collectors.toMap(OrderRateDto::getOrderCode, orderRateDto -> orderRateDto));
                for (PersonInvoiceQueryRespDTO invoice : invoiceList) {
                    OrderRateDto orderRateDto = orderRateMap.get(invoice.getOrderCode());
                    if (orderRateDto != null) {
                        invoice.setReceivedAmtOrgCurrency(orderRateDto.getReceivableOrgCurrencyAmt()==null?"":orderRateDto.getReceivableOrgCurrencyAmt()
                                .setScale(2, RoundingMode.DOWN).toPlainString());
                    }
                }
            }
        }
        // 使用 PaginationSupportDTO 的工具方法将 IPage 转换为 PaginationSupportDTO
        PaginationSupportDTO<PersonInvoiceQueryRespDTO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(invoicePage);

        return paginationSupportDTO;
    }

    @Override
    public Response<Boolean> pushInvoiceStatusNotify(OrderInvoiceDTO invoiceDTO, String operator, boolean checkFail) {
        // 开票完成后操作
        // 判断是否开票状态为已开票且附件发送状态为未发送
        boolean isInvoiced = pushStatusCondition(invoiceDTO.getInvoiceStatus());
        final Integer sendFail = 2;
        final Integer unSend = 0;
        boolean isAttachNotSent = checkFail ? unSend.equals(invoiceDTO.getSendStatus()) || sendFail.equals(invoiceDTO.getSendStatus()) : unSend.equals(invoiceDTO.getSendStatus());
//        boolean needPush = isInvoiced && isAttachNotSent;
        if (!isInvoiced) {
            return Response.failure(ErrorCodeEnum.PUSH_ORDER_STATUS_UN_SUPPORT);
        }
        if (!isAttachNotSent) {
            return Response.failure(ErrorCodeEnum.PUSH_SEND_STATUS_UN_SUPPORT);
        }
        if (!CommonConstants.YES.equals(invoiceDTO.getAutoOpenInvoiceStatus())) {
            return Response.failure(ErrorCodeEnum.INVOICE_UN_OPEN_INVOICE_TYPE);
        }
        if (!CommonConstants.YES.equals(invoiceDTO.getFileUrlStatus()) || StrUtilX.isEmpty(invoiceDTO.getInvoiceUrls())) {
            return Response.failure(ErrorCodeEnum.INVOICE_UN_DOWNLOAD_INVOICE_TYPE);
        }
        String webRequestId = SlsLoggerUtil.getWebRequestId();
        if (webRequestId == null) {
            webRequestId = IdWorker.getIdStr();
        }
        if (StrUtilX.isEmpty(invoiceDTO.getOrderCode())) {
            throw new SysException("订单号不能为空");
        }
        // 查询订单信息，获取订单ID
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderCode(invoiceDTO.getOrderCode());
        OrderPO order = orderMapper.selectOne(orderPO);
        if (order == null) {
            log.error("推送发票状态通知失败：未找到订单信息，订单编号：{}requestId:{}", invoiceDTO.getOrderCode(), webRequestId);
            throw new SysException("订单不能为空");
        }
        OrderInvoicePO updateInvoicePO = new OrderInvoicePO();
        updateInvoicePO.setId(invoiceDTO.getId());
        if (!CommonConstants.YES.equals(invoiceDTO.getAutoOpenInvoiceStatus()) || !CommonConstants.YES.equals(invoiceDTO.getFileUrlStatus()) || StrUtilX.isEmpty(invoiceDTO.getInvoiceUrls())) {
            updateInvoicePO.setEEmailSendFailedStatus(1);
            updateInvoicePO.setEEmailSendFailedMessage(getExceptionMsg(order.getOrderCode()));
            updateInvoicePO.setSendStatus(2);
            // 记录订单日志
            orderCommonService.saveLog(
                    order.getId(),
                    operator,
                    order.getOrderCode(),
                    "推送开票状态失败，原因:推送失败，请线下同步发票信息，订单进入订单发票异常中，可点击已处理进行消费"
            );
            return Response.failure(ErrorCodeEnum.INVOICE_STATUS_SEND_FAIL);
        }
        AgentAddDTO agentAddDTO = new AgentAddDTO();
        agentAddDTO.setAgentCode(order.getAgentCode());
        String partnerCodeByAgentCode = order.getPartnerCode();
        if (partnerCodeByAgentCode == null) {
            partnerCodeByAgentCode = orgMapper.getPartnerCodeByAgentCode(order.getAgentCode());
        }
        if (partnerCodeByAgentCode == null) {
            throw new SysException(ErrorCodeEnum.PARTNER_CODE_NO_EXIST_TYPE);
        }
        Response<AgentConfigDTO> agentConfig = agentConfigRemote.getAgentConfig(partnerCodeByAgentCode);
        if (agentConfig == null || !agentConfig.isSuccess()) {
            throw new SysException("推送发票状态失败 :调用客户合作编码配置失败");
        }
        String invoiceNotifyUrl = ObjectUtil.isEmpty(agentConfig.getModel()) ? null : agentConfig.getModel().getInvoiceNotifyUrl();
        CompanyAddDTO companyAddDTO = new CompanyAddDTO();
        companyAddDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        String customerTel = "";
        Response<CompanySelectDTO> companyResp = companyRemote.queryCompanyDetail(companyAddDTO);
        if (companyResp != null && companyResp.getModel() != null) {
            customerTel = companyResp.getModel().getCustomerTel();
        }
        // 构建 InvoiceNotifyPushRequest 对象
        InvoiceNotifyPushRequest request = new InvoiceNotifyPushRequest();
        request.setOrderCode(invoiceDTO.getOrderCode());
        request.setApplyId(invoiceDTO.getApplyId());
        request.setInvoiceNo(invoiceDTO.getInvoiceNo());
        LocalDateTime invoiceDate = LocalDateTimeUtil.parseDate(invoiceDTO.getInvoiceDate(), "yyyy-MM-dd").atTime(0, 0, 0);
        request.setInvoiceDate(LocalDateTimeUtil.format(invoiceDate, "yyyy-MM-dd HH:mm:ss"));
        request.setInvoiceStatus(InvoiceStatusEnum.INVOICED.no);
        request.setInvoiceRemark(invoiceDTO.getInvoiceRemark());
        request.setInvoiceAmount(invoiceDTO.getInvoiceAmount());
        request.setInvoiceUrls(invoiceDTO.getInvoiceUrls() == null ? "" : invoiceDTO.getInvoiceUrls());
        request.setRequestId(webRequestId);
        request.setPartnerCode(partnerCodeByAgentCode);

        Boolean fail = false;
        // 调用推送接口
        try {
            //发票来源非通过接口获取
            if (StrUtil.isEmpty(invoiceDTO.getApplyId()) && StrUtil.isNotEmpty(invoiceNotifyUrl)) {
                updateInvoicePO.setEEmailSendFailedStatus(1);
                updateInvoicePO.setEEmailSendFailedMessage(getExceptionMsg(order.getOrderCode()));
                updateInvoicePO.setSendStatus(2);
                // 记录订单日志
                orderCommonService.saveLog(
                        order.getId(),
                        operator,
                        order.getOrderCode(),
                        "推送失败，原因:该发票是人工手动新增，请线下同步发票信息，订单进入订单发票异常列中，可点击已处理进行消费"
                );
                orderInvoiceMapper.updateByPrimaryKeySelective(updateInvoicePO);
                return Response.failure(ErrorCodeEnum.PUSH_FAIL_BY_MANUAL);
            }
            Response<Object> response = disB2BRemote.pushInvoiceStatusNotify(request);
            if (response != null && response.isSuccess()) {
                //调用成功处理
                successInvokePush(invoiceDTO, order, webRequestId, updateInvoicePO, customerTel, fail, operator);
            } else {
                fail = true;
                //调用失败处理
                failInvokePush(invoiceDTO, response, order, webRequestId, updateInvoicePO, customerTel, fail, operator);
            }
        } catch (Exception e) {
            log.error(StrUtil.format("发票状态通知推送异常，订单编号：{}requestId:{}", invoiceDTO.getOrderCode(), webRequestId), e);
            fail = true;
            exceptionInvokePush(invoiceDTO, order, updateInvoicePO, operator);
        }
        orderInvoiceMapper.updateByPrimaryKeySelective(updateInvoicePO);
        if (fail) {
            return Response.failure(ErrorCodeEnum.INVOICE_STATUS_SEND_FAIL);
        }
        return Response.success(true);
    }

    private void exceptionInvokePush(OrderInvoiceDTO invoiceDTO, OrderPO order, OrderInvoicePO updateInvoicePO, String operator) {
        // 接口调用异常，记录日志
        orderCommonService.saveLog(
                order.getId(),
                operator,
                order.getOrderCode(),
                "发票状态通知推送异常，原因：推送失败，请线下同步发票信息，订单进入订单发票异常列中，可点击已处理进行消费");
        updateInvoicePO.setEPushAddressInvalidStatus(1);
        updateInvoicePO.setEPushAddressInvalidMessage(getExceptionMsg(order.getOrderCode()));
        updateInvoicePO.setSendStatus(2);
    }

    private static String getExceptionMsg(String orderCode) {
//        return StrUtil.format(EXP_INVOICE_MSG_FORMAT, "发票可能存在申请开票失败或下载发票失败或推送发票状态失败或发送客户邮箱失败，请查看订单日志，并检查是否已处理。");
        return StrUtil.format(EXP_INVOICE_MSG_FORMAT, orderCode);
    }

    private void failInvokePush(OrderInvoiceDTO invoiceDTO, Response<Object> response, OrderPO order, String webRequestId, OrderInvoicePO updateInvoicePO, String customerTel, Boolean fail, String operator) {
        log.warn("发票状态通知推送失败，订单编号：{}，响应：{} requestId:{}", invoiceDTO.getOrderCode(), JSONUtil.toJsonStr(response), webRequestId);
        // 客户未配置地址错误
        if (DhubReturnCodeEnum.INVOICE_URL_NO_EXIST.getCode().equals(response.getFailReason())) {
            // 设置客户未配置电子发票推送地址异常状态
            updateInvoicePO.setEPushAddressConfigStatus(1);
            updateInvoicePO.setEPushAddressConfigMessage(getExceptionMsg(order.getOrderCode()));
            updateInvoicePO.setSendStatus(2);
            // 记录订单日志
            orderCommonService.saveLog(
                    order.getId(),
                    operator,
                    order.getOrderCode(),
                    "推送开票状态失败,原因:客户未配置电子发票推送接口地址,请线下同步发票信息,订单进入订单发票异常中,可点击已处理进行消费");
        } else {
            fail = true;
            updateInvoicePO.setEPushAddressInvalidStatus(1);
            updateInvoicePO.setEPushAddressInvalidMessage(getExceptionMsg(order.getOrderCode()));
            updateInvoicePO.setSendStatus(2);
            // 记录订单日志
            orderCommonService.saveLog(
                    order.getId(),
                    operator,
                    order.getOrderCode(),
                    "推送开票状态失败，原因:推送失败，请线下同步发票信息，订单进入订单发票异常中，可点击已处理进行消费"
            );
        }

    }

    private void successInvokePush(OrderInvoiceDTO invoiceDTO, OrderPO order, String webRequestId, OrderInvoicePO updateInvoicePO, String customerTel, Boolean fail, String operator) {
        log.info("发票状态通知推送成功，订单编号：{}requestId:{}", invoiceDTO.getOrderCode(), webRequestId);
        // 接口调用成功，记录日志
        orderCommonService.saveLog(
                order.getId(),
                operator,
                order.getOrderCode(),
                "推送开票状态成功"
        );
        // 邮件发送逻辑
        emailSend(invoiceDTO, order, webRequestId, updateInvoicePO, customerTel, fail, operator);
    }

    private void emailSend(OrderInvoiceDTO invoiceDTO, OrderPO order, String webRequestId, OrderInvoicePO updateInvoicePO, String customerTel, Boolean fail, String operator) {
        TicketTypeEnum ticketTypeEnum = TicketTypeEnum.getTicketTypeEnum(invoiceDTO.getTicketType(), invoiceDTO.getInvoiceType());
        if (TicketTypeEnum.EMAIL.equals(ticketTypeEnum) && StrUtil.isNotEmpty(invoiceDTO.getEmail())) {
            // 向客户邮件地址发送邮件
            try {
                // 发送邮件的实现逻辑
                boolean successSend = sendInvoiceToEmail(invoiceDTO, order, webRequestId, updateInvoicePO, customerTel);
                if (successSend) {
                    // 成功记录订单日志
                    successSendEmail(order, updateInvoicePO, operator);
                } else {
                    fail = true;
                    exceptionSendEmail(invoiceDTO, order, webRequestId, updateInvoicePO, operator);
                }
            } catch (Exception e) {
                fail = true;
                log.error("发票发送客户邮箱失败，订单编号：{} requestId:{}", invoiceDTO.getOrderCode(), webRequestId, e);
                exceptionSendEmail(invoiceDTO, order, webRequestId, updateInvoicePO, operator);
            }
        } else {
            updateInvoicePO.setSendStatus(1); // 已发送
        }
    }

    private boolean sendInvoiceToEmail(OrderInvoiceDTO invoiceDTO, OrderPO order, String webRequestId, OrderInvoicePO updateInvoicePO, String customerTel) throws Exception {
        // 获取发送邮件内容
        String content = getInvoiceEmailContent(invoiceDTO, order, webRequestId, updateInvoicePO, customerTel);

        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setEmail(invoiceDTO.getEmail());
        sendEmailReq.setType(1);
        sendEmailReq.setTopic("电子发票邮件");
        sendEmailReq.setContent(content);
        MailAttachVO mailAttachVO = new MailAttachVO("发票附件.pdf", invoiceDTO.getInvoiceUrls());
        // 发送邮件
        return sendCodeUtil.sendEmail(sendEmailReq, 1, mailAttachVO);
    }

    //
    private String getInvoiceEmailContent(OrderInvoiceDTO invoiceDTO, OrderPO order, String webRequestId, OrderInvoicePO updateInvoicePO, String customerTel) throws Exception {
        Configuration cfg = new Configuration();
        cfg.setClassForTemplateLoading(this.getClass(), "/templates"); // 模板位置
        cfg.setDefaultEncoding("UTF-8");
        Template template = cfg.getTemplate("emailOrderInvoice.ftl");
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("invoiceTypeName", InvoiceTypeEnum.getDesc(invoiceDTO.getInvoiceType()));
        dataModel.put("invoiceSaleName", invoiceDTO.getSaleTaxOrgName());
        dataModel.put("invoiceCode", "");
        dataModel.put("invoiceNo", invoiceDTO.getInvoiceNo());
        dataModel.put("customerTel", customerTel == null ? "" : customerTel);
        StringWriter writer = new StringWriter();
        template.process(dataModel, writer);
        return writer.toString();
    }

    private void successSendEmail(OrderPO order, OrderInvoicePO updateInvoicePO, String operator) {
        orderCommonService.saveLog(
                order.getId(),
                operator,
                order.getOrderCode(),
                "发票发送客户邮箱成功"
        );
        // 查询发票订单信息
        updateInvoicePO.setSendStatus(1);
    }

    private void exceptionSendEmail(OrderInvoiceDTO invoiceDTO, OrderPO order, String webRequestId, OrderInvoicePO updateInvoicePO, String operator) {
        // 发送邮件失败，记录日志
        orderCommonService.saveLog(
                order.getId(),
                operator,
                order.getOrderCode(),
                "发票发送客户邮箱失败，原因：发票发送客户邮箱失败，请线下同步发票信息，订单进入订单发票异常中，可点击已处理进行消费"
        );
        updateInvoicePO.setEEmailSendFailedStatus(1);
        updateInvoicePO.setEEmailSendFailedMessage(getExceptionMsg(order.getOrderCode()));
        updateInvoicePO.setSendStatus(2);
    }

    @Override
    public Boolean updateOrderInvoiceErrorNormal(UpdateOrderInvoiceErrorDTO request) {
        // 查询订单发票
        OrderInvoicePO orderInvoicePO = orderInvoiceMapper.selectByPrimaryKey(request.getOrderInvoiceId());
        if (orderInvoicePO == null) {
            log.error("更新订单发票异常状态失败，未找到发票记录，ID：{}", request.getOrderInvoiceId());
            return false;
        }

        // 创建更新对象，只更新相关字段
        OrderInvoicePO updatePO = new OrderInvoicePO();
        updatePO.setId(orderInvoicePO.getId());

        // 根据异常类型更新相应字段
        int result;
        switch (OrderInvoiceErrorTypeEnum.getByCode(request.getErrorType())) {
            case CREATE_BILL_ERROR: // 创建票单错误
                updatePO.setECreateBillStatus(0);
                updatePO.setECreateBillMessage("");
                break;
            case PUSH_ADDRESS_CONFIG_ERROR: // 客户未配置电子发票推送地址
                updatePO.setEPushAddressConfigStatus(0);
                updatePO.setEPushAddressConfigMessage("");
                break;
            case PUSH_ADDRESS_INVALID_ERROR: // 客户配置地址错误
                updatePO.setEPushAddressInvalidStatus(0);
                updatePO.setEPushAddressInvalidMessage("");
                break;
            case INVOICE_FILE_MISSING_ERROR: // 手工推送发票失败
                updatePO.setEInvoiceFileMissingStatus(0);
                updatePO.setEInvoiceFileMissingMessage("");
                break;
            case INVOICE_APPLY_FAILED_ERROR: // 申请开票失败
                updatePO.setEInvoiceApplyFailedStatus(0);
                updatePO.setEInvoiceApplyFailedMessage("");
                break;
            case FILE_DOWNLOAD_FAILED_ERROR: // 下载开票文件失败
                updatePO.setEFileDownloadFailedStatus(0);
                updatePO.setEFileDownloadFailedMessage("");
                break;
            case EMAIL_SEND_FAILED_ERROR: // 发票发送客户邮箱失败
                updatePO.setEEmailSendFailedStatus(0);
                updatePO.setEEmailSendFailedMessage("");

                break;
            default:
                log.error("更新订单发票异常状态失败，异常类型不存在：{}", request.getErrorType());
                return false;
        }
        result = orderInvoiceMapper.updateByPrimaryKeySelective(updatePO);
        log.info("更新订单发票异常状态结果：{}, 发票ID：{}, 异常类型：{}", result > 0, request.getOrderInvoiceId(), request.getErrorType());
        return result > 0;
    }

    @Override
    public OrderInvoiceBtnDTO getOrderInvoiceBtn(Integer orderInvoiceId) {
        OrderInvoiceBtnDTO btnDTO = new OrderInvoiceBtnDTO();
        btnDTO.setOpenBtn(0);
        btnDTO.setDownloadBtn(0);
        btnDTO.setOpenBtn(0);
        btnDTO.setCreateBillBtn(0);
        if (orderInvoiceId == null) {
            log.info("获取发票操作按钮显隐状态失败，发票订单ID为空");
            return createDefaultBtnDTO();
        }
        if (settingsConstant.getAutoInvoiceEnabled() == null || settingsConstant.getAutoInvoiceEnabled() != 1) {
            return createDefaultBtnDTO();
        }
        // 查询发票订单信息
        OrderInvoicePO orderInvoicePO = orderInvoiceMapper.selectByPrimaryKey(orderInvoiceId);
        if (orderInvoicePO == null) {
            log.info("获取发票操作按钮显隐状态失败，未找到发票记录，ID：{}", orderInvoiceId);
            return createDefaultBtnDTO();
        }
        if (openInvoiceCondition(orderInvoicePO.getInvoiceStatus())) {
            btnDTO.setOpenBtn(1);
        }
        if (pushStatusCondition(orderInvoicePO.getInvoiceStatus())) {
            btnDTO.setPushBtn(1);
        }
        if (downLoadCondition(orderInvoicePO.getInvoiceStatus())) {
            btnDTO.setDownloadBtn(1);
        }
        if (InvoiceStatusEnum.CREATE_BILL_FAILED.getNo() == orderInvoicePO.getInvoiceStatus() ||
                InvoiceStatusEnum.DOWNLOAD_SUCCESS_WAIT_CREATE_BILL.getNo() == orderInvoicePO.getInvoiceStatus()) {
            btnDTO.setCreateBillBtn(1);
        }
        return btnDTO;
    }

    private static boolean downLoadCondition(Integer status) {
        return InvoiceStatusEnum.INVOICED.getNo() == status ||
                InvoiceStatusEnum.DOWNLOAD_FAILED.getNo() == status ||
                InvoiceStatusEnum.INVOICE_SUCCESS_WAIT_DOWNLOAD.getNo() == status;
    }

    private static boolean pushStatusCondition(Integer status) {
        return InvoiceStatusEnum.INVOICED.getNo() == status;
    }

    private static boolean openInvoiceCondition(Integer status) {
        return InvoiceStatusEnum.WILL_INVOICE.getNo() == status ||
                InvoiceStatusEnum.INVOICE_FAILED.getNo() == status;
    }

    /**
     * 创建默认按钮显隐DTO（全部不显示）
     */
    private OrderInvoiceBtnDTO createDefaultBtnDTO() {
        OrderInvoiceBtnDTO btnDTO = new OrderInvoiceBtnDTO();
        btnDTO.setOpenBtn(0);
        btnDTO.setPushBtn(0);
        btnDTO.setDownloadBtn(0);
        return btnDTO;
    }

    @Override
    public Response<Boolean> pushInvoiceStatusNotifyById(Integer orderInvoiceId, String operator, boolean checkFail) {
        OrderInvoicePO orderInvoicePO = orderInvoiceMapper.selectByPrimaryKey(orderInvoiceId);
        OrderInvoiceDTO orderInvoiceDTO = ProductSalePriceConvert.INSTANCE.orderInvoiceConvert(orderInvoicePO);
        return pushInvoiceStatusNotify(orderInvoiceDTO, operator, checkFail);
    }

    @Override
    public Response<String> downLoadAutoOpenInvoicePdf(Integer invoiceId, String operator) {
        String webRequestId = SlsLoggerUtil.getWebRequestId();
        if (webRequestId == null) {
            webRequestId = IdWorker.getIdStr();
        }
        OrderInvoicePO orderInvoicePO = orderInvoiceMapper.selectByPrimaryKey(invoiceId);
        OrderInvoiceDTO orderInvoiceDTO = ProductSalePriceConvert.INSTANCE.orderInvoiceConvert(orderInvoicePO);

        if (!downLoadCondition(orderInvoicePO.getInvoiceStatus())) {
            throw new SysException("不符合开票条件,请检查发票状态");
        }
        if (CommonConstants.YES.equals( orderInvoicePO.getFileUrlStatus()) && StrUtil.isNotEmpty(orderInvoicePO.getInvoiceUrls())) {
            return Response.success(orderInvoiceDTO.getInvoiceUrls());
        }
        if (StrUtil.isEmpty(orderInvoicePO.getInvoiceNo())) {
            throw new SysException(ErrorCodeEnum.INVOICE_NUMBER_EMPTY);
        }
        // 查询订单信息，获取订单ID
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderCode(orderInvoiceDTO.getOrderCode());
        OrderPO order = orderMapper.selectOne(orderPO);
        //未调用自动开票接口
        if (BaseEnum.YES.getKey() != orderInvoiceDTO.getAutoOpenInvoiceStatus()) {
            downLoadInvoiceFail(invoiceId, operator, order, "下载发票失败，该发票非系统自动开票");
            return Response.failure(ErrorCodeEnum.INVOICE_UN_OPEN_INVOICE_DOWNLOAD_FAIL);
        }
        OutputFormatQueryQdInvoiceRequest queryQdInvoiceRequest = new OutputFormatQueryQdInvoiceRequest();
        OutputFormatQueryQdInvoiceData outputFormatQueryQdInvoiceData = new OutputFormatQueryQdInvoiceData();
        queryQdInvoiceRequest.setData(outputFormatQueryQdInvoiceData);
        outputFormatQueryQdInvoiceData.setInvoiceNo(orderInvoiceDTO.getInvoiceNo());
        OutputFormatQueryQdInvoiceResponse response;
        try {
            response = baiWangInvoiceService.queryQdInvoice(queryQdInvoiceRequest, webRequestId);
            if (response.isSuccess()) {
                //第一次调失败 自动重试一次
                if(StrUtilX.isEmpty(response.getResponse().getPdfUrl())){
                    response = baiWangInvoiceService.queryQdInvoice(queryQdInvoiceRequest, webRequestId);
                    if(!response.isSuccess()){
                        downLoadInvoiceFail(invoiceId, operator, order, response.getErrorResponse().getMessage());
                        return Response.failure(ErrorCodeEnum.INVOICE_FILE_DOWNLOAD_FAIL);
                    }
                }
                String url = downloadInvoiceSuccess(orderInvoiceDTO, operator, response, order);
                return Response.success(url);
            } else {
                downLoadInvoiceFail(invoiceId, operator, order, response.getErrorResponse().getMessage());
                return Response.failure(ErrorCodeEnum.INVOICE_FILE_DOWNLOAD_FAIL);
            }
        } catch (Exception e) {
            String exFailReason = "";
            if (e instanceof BWOpenException) {
                BWOpenException be = (BWOpenException) e;
                exFailReason = be.getMessage() + "," + be.getSubMessage();
            } else {
                exFailReason = e.getMessage();
            }
            downLoadInvoiceFail(invoiceId, operator, order, exFailReason);
            if(e instanceof SysException){
                SysException sysException=(SysException)e;
                return Response.failure(sysException.getCode(), sysException.getMessage(),"");
            }
            return Response.failure(ErrorCodeEnum.INVOICE_FILE_DOWNLOAD_FAIL);
        }
    }

    private void downLoadInvoiceFail(Integer invoiceId, String operator, OrderPO order, String failReason) {
        //失败回填
        OrderInvoiceDTO updateOrderInvoiceDTO = new OrderInvoiceDTO();
        updateOrderInvoiceDTO.setId(invoiceId);
        updateOrderInvoiceDTO.setEFileDownloadFailedStatus(1);
        updateOrderInvoiceDTO.setEFileDownloadFailedMessage(getExceptionMsg(order.getOrderCode()));
        updateOrderInvoiceDTO.setInvoiceStatus(InvoiceStatusEnum.DOWNLOAD_FAILED.getNo());
        orderService.updateOrderInvoice(updateOrderInvoiceDTO, null);
        //失败处理
        orderCommonService.saveLog(
                order.getId(),
                operator,
                order.getOrderCode(),
                StrUtil.format("下载发票失败，原因：{}，请手动点击申请发票或线下核对，订单进入订单发票异常列中，可点击已处理进行消费", failReason)
        );
    }

    private String downloadInvoiceSuccess(OrderInvoiceDTO orderInvoiceDTO, String operator, OutputFormatQueryQdInvoiceResponse response, OrderPO order) {
        //成功回填
        OrderInvoiceDTO updateOrderInvoiceDTO = new OrderInvoiceDTO();
        updateOrderInvoiceDTO.setId(orderInvoiceDTO.getId());
        String pdfUrl = response.getResponse().getPdfUrl();
        if (StrUtil.isEmpty(pdfUrl)) {
            throw new SysException("下载发票文件失败");
        }
        //上传到我们的oss中
        byte[] pdfBytes = HttpUtil.downloadBytes(pdfUrl);
        String fileName = "发票文件" + orderInvoiceDTO.getSerialNo() + ".pdf";
        MultipartFile multipartFile = new MockMultipartFile(
                "file",
                fileName,
                "application/pdf",
                pdfBytes
        );
        Response<FileResp> upload = fileRemote.upload(multipartFile);
        if (!upload.isSuccess()) {
            throw new SysException("发票上传附件失败");
        }
        FileResp model = upload.getModel();
        updateOrderInvoiceDTO.setInvoiceUrls(model.getFileUrl());
        updateOrderInvoiceDTO.setInvoiceStatus(InvoiceStatusEnum.DOWNLOAD_SUCCESS_WAIT_CREATE_BILL.getNo());
        updateOrderInvoiceDTO.setFileUrlStatus(1);
        updateOrderInvoiceDTO.setInvoiceFileName(fileName);
        updateOrderInvoiceDTO.setOperator(operator);
        orderInvoiceDTO.setInvoiceUrls(model.getFileUrl());
        //创建销项发票
        createAgentInvoice(orderInvoiceDTO, order.getCompanyCode(), model, order, operator);
        orderService.updateOrderInvoice(updateOrderInvoiceDTO, null);
        orderCommonService.saveLog(
                order.getId(),
                operator,
                order.getOrderCode(),
                StrUtil.format("订单号：{}创建销项发票成功", order.getOrderCode())
        );
        orderCommonService.saveLog(
                order.getId(),
                operator,
                order.getOrderCode(),
                StrUtil.format("订单号：{}下载发票成功", order.getOrderCode())
        );
        return model.getFileUrl();
    }

    private void createAgentInvoice(OrderInvoiceDTO orderInvoiceDTO, String companyCode, FileResp model, OrderPO order, String operator) {
        AgentInvoiceBaseReqDTO agentInvoiceBaseReqDTO = new AgentInvoiceBaseReqDTO();
        agentInvoiceBaseReqDTO.setInvoiceNum(orderInvoiceDTO.getInvoiceNo());
        Response<AgentInvoiceBaseRespDTO> invoiceDetail = agentInvoiceRemote.getInvoiceDetailByInvoiceNo(agentInvoiceBaseReqDTO);
        if (invoiceDetail.isSuccess()) {
            if (invoiceDetail.getModel() == null) {
                // 创建新的发票请求对象
                AgentInvoiceAditReq agentInvoiceAditReq = new AgentInvoiceAditReq();

                // 填充基本信息
                agentInvoiceAditReq.setMerchantCode(companyCode);
                agentInvoiceAditReq.setCreatedBy(operator);
                agentInvoiceAditReq.setUpdatedBy(operator);
                agentInvoiceAditReq.setInvoiceSourceType(InvoiceSourceTypeEnum.SYSTEM_SYNC.getKey()); // 自动开票

                // 填充发票信息
                agentInvoiceAditReq.setInvoiceNum(orderInvoiceDTO.getInvoiceNo());
                agentInvoiceAditReq.setInvoiceCode(orderInvoiceDTO.getInvoiceCode());
                agentInvoiceAditReq.setInvoiceDate(orderInvoiceDTO.getInvoiceDate());
                if (InvoiceTypeEnum.ELECTRONIC_COMMON_INVOICE.getNo() == orderInvoiceDTO.getInvoiceType()) {
                    agentInvoiceAditReq.setInvoiceType(3);
                }
                if (InvoiceTypeEnum.ELECTRONIC_SPECIAL_INVOICE.getNo() == orderInvoiceDTO.getInvoiceType()) {
                    agentInvoiceAditReq.setInvoiceType(4);
                }

                // 填充金额信息
                agentInvoiceAditReq.setInvoiceAmt(orderInvoiceDTO.getInvoiceAmount());
                agentInvoiceAditReq.setInvoiceTaxRate(orderInvoiceDTO.getInvoiceTaxRate() == null ? BigDecimal.ZERO : new BigDecimal(orderInvoiceDTO.getInvoiceTaxRate()));
                BigDecimal rateAmount = orderInvoiceDTO.getTaxAmt() == null ? BigDecimal.ZERO : orderInvoiceDTO.getTaxAmt();
                agentInvoiceAditReq.setTotalTaxAmt(rateAmount);
                agentInvoiceAditReq.setTotalAmt(orderInvoiceDTO.getInvoiceAmount().subtract(rateAmount));

                // 填充购买方信息
                agentInvoiceAditReq.setPurchaserName(orderInvoiceDTO.getInvoiceTitle());
                agentInvoiceAditReq.setPurchaserRegisterNum(orderInvoiceDTO.getTaxNumber());

                // 填充销售方信息
                agentInvoiceAditReq.setSellerName(orderInvoiceDTO.getSaleTaxOrgName());
                agentInvoiceAditReq.setSellerRegisterNum(orderInvoiceDTO.getSaleTaxNo());

                // 填充其他信息
                agentInvoiceAditReq.setInvoiceContent(StrUtilX.isEmpty(orderInvoiceDTO.getInvoiceRemark()) ? "自动开票" : orderInvoiceDTO.getInvoiceRemark());
                agentInvoiceAditReq.setCheckCode("");
                agentInvoiceAditReq.setRemark(StrUtil.isEmpty(orderInvoiceDTO.getInvoiceRemark()) ? "" : orderInvoiceDTO.getInvoiceRemark());
                agentInvoiceAditReq.setThirdId(orderInvoiceDTO.getSerialNo());
                // 如果有发票URL，添加附件信息
                if (StringUtils.isNotBlank(orderInvoiceDTO.getInvoiceUrls())) {
                    List<InvoiceAttchDTO> attachList = new ArrayList<>();
                    InvoiceAttchDTO attachDTO = new InvoiceAttchDTO();
                    attachDTO.setFileId(model.getFileId());
                    attachList.add(attachDTO);
                    agentInvoiceAditReq.setUrl(attachList);
                }

                // 调用远程接口添加发票
                Response<Object> response = agentInvoiceRemote.invoiceAdd(agentInvoiceAditReq);
                if (response.isSuccess()) {
                    log.info("创建分销商发票成功，发票号：{}", orderInvoiceDTO.getInvoiceNo());
                } else {
                    log.error("创建分销商发票失败，发票号：{}，失败原因：{}", orderInvoiceDTO.getInvoiceNo(), response.getFailReason());
                    throw new SysException(ErrorCodeEnum.DOWNLOAD_FAIL_BY_EXIST_INVOICE);
                }
            } else {
                orderCommonService.saveLog(
                        order.getId(),
                        operator,
                        order.getOrderCode(),
                        StrUtil.format("创建发票失败，原因：{}", "发票号码已存在")
                );
                throw new SysException(ErrorCodeEnum.DOWNLOAD_FAIL_BY_EXIST_INVOICE);
            }
            return;
        }
        throw new SysException(ErrorCodeEnum.DOWNLOAD_FAIL_BY_EXIST_INVOICE);

    }

    @Override
    public void autoOpenInvoice(Integer invoiceId, String operator) {
        OrderInvoicePO orderInvoicePO = orderInvoiceMapper.selectByPrimaryKey(invoiceId);
        OrderInvoiceDTO orderInvoiceDTO = ProductSalePriceConvert.INSTANCE.orderInvoiceConvert(orderInvoicePO);
        // 查询订单信息，获取订单ID
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderCode(orderInvoiceDTO.getOrderCode());
        OrderPO order = orderMapper.selectOne(orderPO);
        if (BaseEnum.NO.getKey() != orderInvoiceDTO.getAutoOpenInvoiceStatus()) {
            failAutoInvoice(invoiceId, operator, order, orderInvoicePO.getOrderCode(), "自动开票接口已调用，不能重复调用");
        }
        //开票
        try {
            Response<BaiWangOpenInvoiceResultDTO> baiWangOpenInvoiceResultDTOResponse = autoOpenInvoice(orderInvoiceDTO);
            //开具成功
            if (baiWangOpenInvoiceResultDTOResponse.isSuccess()) {
                BaiWangOpenInvoiceResultDTO model = baiWangOpenInvoiceResultDTOResponse.getModel();
                OrderInvoiceDTO updateOrderInvoiceDTO = new OrderInvoiceDTO();
                updateOrderInvoiceDTO.setId(invoiceId);
                updateOrderInvoiceDTO.setInvoiceStatus(InvoiceStatusEnum.INVOICE_SUCCESS_WAIT_DOWNLOAD.getNo());
                updateOrderInvoiceDTO.setAutoOpenInvoiceStatus(1);
                updateOrderInvoiceDTO.setInvoiceNo(model.getInvoiceNo());
                updateOrderInvoiceDTO.setInvoiceCode(model.getInvoiceCode());
                updateOrderInvoiceDTO.setInvoiceDate(LocalDate.now().toString());
                updateOrderInvoiceDTO.setSaleTaxNo(model.getTaxNo());
                updateOrderInvoiceDTO.setSaleTaxOrgName(model.getTaxOrgName());
                updateOrderInvoiceDTO.setOperator(operator);
                updateOrderInvoiceDTO.setTaxAmt(model.getTaxAmt());
                orderService.updateOrderInvoice(updateOrderInvoiceDTO, null);
                //记录日志
                orderCommonService.saveLog(
                        order.getId(),
                        operator,
                        orderInvoicePO.getOrderCode(),
                        StrUtil.format("订单号：{}开票成功", order.getOrderCode())
                );
                return;
            }
            //开具失败
            failAutoInvoice(invoiceId, operator, order, orderInvoicePO.getOrderCode(), baiWangOpenInvoiceResultDTOResponse.getFailReason());
        } catch (Exception e) {
            log.error("发起开票申请异常", e);
            failAutoInvoice(invoiceId, operator, order, orderInvoicePO.getOrderCode(), e.getMessage());
        }
    }

    private void failAutoInvoice(Integer invoiceId, String operator, OrderPO order, String orderCode, String failReason) {
        //失败处理
        OrderInvoiceDTO updateOrderInvoiceDTO = new OrderInvoiceDTO();
        updateOrderInvoiceDTO.setId(invoiceId);
        updateOrderInvoiceDTO.setInvoiceStatus(InvoiceStatusEnum.INVOICE_FAILED.getNo());
        //开票失败更新流水号
        updateOrderInvoiceDTO.setSerialNo(IdWorker.getIdStr());
        updateOrderInvoiceDTO.setEInvoiceApplyFailedStatus(1);
        updateOrderInvoiceDTO.setEInvoiceApplyFailedMessage(getExceptionMsg(orderCode));
        updateOrderInvoiceDTO.setOperator(operator);
        orderService.updateOrderInvoice(updateOrderInvoiceDTO, null);
        //记录日志
        orderCommonService.saveLog(
                order.getId(),
                operator,
                orderCode,
                StrUtil.format("申请开票失败，原因：{}，请手动点击申请发票或线下核对，订单进入订单发票异常列中，可点击已处理进行消费", failReason)
        );
    }

    private Response<BaiWangOpenInvoiceResultDTO> autoOpenInvoice(OrderInvoiceDTO orderInvoiceDTO) {
        String serialNo = orderInvoiceDTO.getSerialNo();
        if (StrUtil.isEmpty(serialNo)) {
            throw new SysException("开票失败,开票流水号为空");
        }
        String webRequestId = SlsLoggerUtil.getWebRequestId();
        if (webRequestId == null) {
            webRequestId = IdWorker.getIdStr();
        }
        //查询发票
        OutputEinvoiceQueryRequest queryRequest = new OutputEinvoiceQueryRequest();
        OutputEinvoiceQueryInvoiceQueryParam data1 = new OutputEinvoiceQueryInvoiceQueryParam();
        queryRequest.setData(data1);
        data1.setSerialNo(orderInvoiceDTO.getSerialNo());
        OutputEinvoiceQueryResponse invoiceDetail = baiWangInvoiceService.getInvoiceDetail(queryRequest, webRequestId);
        //查询成功并且存在发票
        if (invoiceDetail.isSuccess() && CollUtil.isNotEmpty(invoiceDetail.getResponse())) {
            OutputEinvoiceQuery outputEinvoiceQuery = invoiceDetail.getResponse().get(0);
            String invoiceStatus = outputEinvoiceQuery.getInvoiceStatus();
            //00为成功标识
            if ("00".equals(invoiceStatus)) {
                BaiWangOpenInvoiceResultDTO result = new BaiWangOpenInvoiceResultDTO();
                result.setInvoiceNo(outputEinvoiceQuery.getInvoiceNo());
                result.setSerialNo(outputEinvoiceQuery.getSerialNo());
                result.setTaxNo(settingsConstant.getBaiwangTaxNo());
                result.setTaxOrgName(settingsConstant.getBaiwangOrgName());
                result.setTaxAmt(outputEinvoiceQuery.getInvoiceTotalTax());
                return Response.success(result);
            }
        }
        BigDecimal taxRate = orderInvoiceDTO.getInvoiceTaxRate() == null ? BigDecimal.ZERO : new BigDecimal(orderInvoiceDTO.getInvoiceTaxRate()).divide(new BigDecimal("100"));
        OutputInvoiceIssueRequest request = new OutputInvoiceIssueRequest();
        OutputInvoiceIssuePreInvoice data = new OutputInvoiceIssuePreInvoice();
        request.setData(data);
        OutputInvoiceIssueInvoiceDetail detail = new OutputInvoiceIssueInvoiceDetail();

        data.setSerialNo(serialNo);
        data.setBuyerName(orderInvoiceDTO.getInvoiceTitle());
        data.setPriceTaxMark("1");
        detail.setGoodsLineNo(1);
        detail.setGoodsTotalPrice(orderInvoiceDTO.getInvoiceAmount());
        detail.setGoodsName("住宿费");
        if (StrUtil.isNotEmpty(orderInvoiceDTO.getAccountBank()) && StrUtil.isNotEmpty(orderInvoiceDTO.getAccountNo())) {
            data.setBuyerBankAccount(orderInvoiceDTO.getAccountBank() + " " + orderInvoiceDTO.getAccountNo());
        } else {
            data.setBuyerBankAccount("");
        }
        if (StrUtil.isNotEmpty(orderInvoiceDTO.getCompanyPhone()) && StrUtil.isNotEmpty(orderInvoiceDTO.getRegisterAddr())) {
            String companyPhone = orderInvoiceDTO.getCompanyPhone().contains("-") ? StrUtil.subAfter(orderInvoiceDTO.getCompanyPhone(), "-", false) : orderInvoiceDTO.getCompanyPhone();
            data.setBuyerAddressPhone(orderInvoiceDTO.getRegisterAddr() + " " + companyPhone);
        } else {
            data.setBuyerAddressPhone("");
        }
        if (ObjectUtil.isEmpty(orderInvoiceDTO.getInvoiceAmount())) {
            throw new SysException("开票金额不能为空");
        }
        data.setInvoiceDetailsList(Collections.singletonList(detail));

        switch (InvoiceTypeEnum.getByCode(orderInvoiceDTO.getInvoiceType())) {
            case ELECTRONIC_COMMON_INVOICE:
                //开增值税电子发票
                detail.setGoodsTaxRate(taxRate);
                data.setInvoiceTypeCode("02");
                data.setBuyerTaxNo(orderInvoiceDTO.getTaxNumber());
                if (StrUtil.isEmpty(orderInvoiceDTO.getTaxNumber())) {
                    throw new SysException("税号不能为空");
                }
                break;
            case ELECTRONIC_SPECIAL_INVOICE:
                data.setInvoiceTypeCode("01");
                data.setBuyerTaxNo(orderInvoiceDTO.getTaxNumber());
                if (StrUtil.isEmpty(orderInvoiceDTO.getTaxNumber())) {
                    throw new SysException("税号不能为空");
                }
                //税率计算
                detail.setGoodsTaxRate(taxRate);
                break;
            default:
                throw new SysException(ErrorCodeEnum.INVOICE_NOT_MEET_AUTO_CONDITION);
        }
        Response<BaiWangOpenInvoiceResultDTO> response = new Response<>();
        System.out.println(JSONUtil.toJsonStr(request));
        OutputInvoiceIssueResponse outputInvoiceIssueResponse = null;
        try {
            outputInvoiceIssueResponse = baiWangInvoiceService.invoiceOpen(request, webRequestId);
            //失败请求处理
            if (!outputInvoiceIssueResponse.getSuccess()) {
                response.setFailCode(ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode);
                response.setFailReason(ErrorCodeEnum.OUTER_IF_EXCEPTION.errorDesc);
                response.setResult(0);
                return Response.error(ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, outputInvoiceIssueResponse.getErrorResponse().getMessage());
            }
            //成功开票处理
            if (CollUtil.isNotEmpty(outputInvoiceIssueResponse.getResponse().getSuccess())) {
                OutputInvoiceIssueInvoiceResult outputInvoiceIssueInvoiceResult = outputInvoiceIssueResponse.getResponse().getSuccess().get(0);
                BaiWangOpenInvoiceResultDTO result = new BaiWangOpenInvoiceResultDTO();
                result.setInvoiceNo(outputInvoiceIssueInvoiceResult.getInvoiceNo());
                result.setSerialNo(outputInvoiceIssueInvoiceResult.getSerialNo());
                result.setTaxNo(settingsConstant.getBaiwangTaxNo());
                result.setTaxOrgName(settingsConstant.getBaiwangOrgName());
                result.setInvoiceCode(outputInvoiceIssueInvoiceResult.getInvoiceCode());
                result.setTaxAmt(outputInvoiceIssueInvoiceResult.getInvoiceTotalTax());
                return Response.success(result);
            }
            //没有成功条数表示开票失败
            return Response.error(ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, "未返回成功票单信息");
        } catch (Exception e) {
            if (e instanceof BWOpenException) {
                BWOpenException be = (BWOpenException) e;
                return Response.error(ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, be.getMessage() + "," + be.getSubMessage());
            }
            //异常请求处理
            return Response.error(ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, "请求超时/请求失败");
        }
    }

    @Override
    public void queryAutoInvoice(Integer invoiceId) {
        OrderInvoicePO orderInvoicePO = orderInvoiceMapper.selectByPrimaryKey(invoiceId);
        OrderInvoiceDTO orderInvoiceDTO = ProductSalePriceConvert.INSTANCE.orderInvoiceConvert(orderInvoicePO);
        OutputEinvoiceQueryRequest outputEinvoiceQueryRequest = new OutputEinvoiceQueryRequest();
        OutputEinvoiceQueryInvoiceQueryParam data = new OutputEinvoiceQueryInvoiceQueryParam();
        outputEinvoiceQueryRequest.setData(data);
        data.setSerialNo(orderInvoiceDTO.getSerialNo());
        baiWangInvoiceService.getInvoiceDetail(outputEinvoiceQueryRequest, IdWorker.getIdStr());
    }

    @Override
    public Boolean autoOpenOrderInvoiceCreate(Integer orderInvoiceId, String operator, String companyCode) {
        String webRequestId = SlsLoggerUtil.getWebRequestId();
        if (webRequestId == null) {
            webRequestId = IdWorker.getIdStr();
        }
        OrderInvoicePO queryOne = new OrderInvoicePO();
        queryOne.setId(orderInvoiceId);
        OrderInvoicePO orderInvoice = orderInvoiceMapper.selectOne(queryOne);
        boolean canOpenType = openInvoiceCondition(orderInvoice.getInvoiceStatus()) && (InvoiceTypeEnum.ELECTRONIC_COMMON_INVOICE.getNo() == orderInvoice.getInvoiceType() || InvoiceTypeEnum.ELECTRONIC_SPECIAL_INVOICE.getNo() == orderInvoice.getInvoiceType());
        if (!canOpenType) {
            throw new SysException(ErrorCodeEnum.INVOICE_UN_SUPPORT_STATUS_TYPE);
        }
        // 查询待开票金额
        List<InvoiceableAgentInfoResp> invoiceableAgentInfoList = queryInvoiceAbleAmount(orderInvoice.getOrderCode(), companyCode);

        // 判断是否有待开票金额信息
        if (CollectionUtils.isEmpty(invoiceableAgentInfoList)) {
            log.warn("没有待开票金额信息，订单号：{}，商家编码：{}，消息ID：{}",
                    orderInvoice.getOrderCode(), companyCode, webRequestId);
            throw new SysException(ErrorCodeEnum.INVOICE_NOT_MEET_AUTO_CONDITION);
        }

        // 取第一条待开票金额信息
        InvoiceableAgentInfoResp invoiceableAgentInfo = invoiceableAgentInfoList.get(0);
        BigDecimal waitingInvoiceAmt = invoiceableAgentInfo.getWaitingInvoiceAmt();

        // 获取本次开票金额
        BigDecimal currentInvoiceAmt = orderInvoice.getInvoiceAmount();

        // 判断本次开票金额是否小于等于待开票金额
        boolean canInvoice = currentInvoiceAmt.compareTo(waitingInvoiceAmt) <= 0;
        if (!canInvoice) {
            log.info("订单发票自动开票金额判断，订单号：{}，商家编码：{}，待开票金额：{}，本次开票金额：{}，是否可以开票：{},消息ID:{}",
                    orderInvoice.getOrderCode(), companyCode, waitingInvoiceAmt, currentInvoiceAmt, canInvoice, webRequestId);
            throw new SysException(ErrorCodeEnum.INVOICE_AMOUNT_EXCEED_UNINVOICED_AMOUNT);
        }
        //开票逻辑 更新为开票中
        OrderInvoiceDTO updateInvoiceDTO = new OrderInvoiceDTO();
        updateInvoiceDTO.setId(orderInvoice.getId());
        updateInvoiceDTO.setInvoiceStatus(InvoiceStatusEnum.INVOICING.getNo());
        updateInvoiceDTO.setOperator(operator);
        updateInvoiceDTO.setOrderCode(orderInvoice.getOrderCode());
        Integer count = orderService.updateOrderInvoice(updateInvoiceDTO, null);
        if (count > 0) {
            autoOpenInvoice(orderInvoice.getId(), operator);
        }
        return true;
    }

    @Override
    public List<OrderInvoiceWithCompanyDO> queryAutoCreateBillList(Integer id, boolean checkFail) {
        // 计算3个月前的日期
        LocalDate threeMonthsAgoLocal = LocalDate.now().minusMonths(3);
        LocalDate currentDateLocal = LocalDate.now();
        // 转换为Date对象，并设置为0点0分00秒
        Date threeMonthsAgo = Date.from(threeMonthsAgoLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date currentDate = Date.from(currentDateLocal.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1));

        // 设置查询参数
        List<Integer> invoiceStatusList = checkFail ? Arrays.asList(InvoiceStatusEnum.DOWNLOAD_SUCCESS_WAIT_CREATE_BILL.getNo(), InvoiceStatusEnum.CREATE_BILL_FAILED.getNo())
                : Arrays.asList(InvoiceStatusEnum.DOWNLOAD_SUCCESS_WAIT_CREATE_BILL.getNo()); // 下载成功待创建票单
        List<Integer> invoiceTypeList = Arrays.asList(InvoiceTypeEnum.ELECTRONIC_COMMON_INVOICE.getNo(), InvoiceTypeEnum.ELECTRONIC_SPECIAL_INVOICE.getNo()); // 电子普票和电子专票
        List<Integer> confirmStatusList = Arrays.asList(ConfirmationStatusEnum.CONFIRMED.key, ConfirmationStatusEnum.DONE.key); // 已确认和已完成

        // 调用Mapper方法查询符合条件的订单发票
        List<OrderInvoiceWithCompanyDO> list = orderInvoiceMapper.queryAutoInvoiceList(
                invoiceStatusList,
                invoiceTypeList,
                confirmStatusList,
                threeMonthsAgo,
                currentDate,
                null, 1, id);
        return list;
    }

    @Override
    public List<OrderInvoiceWithCompanyDO> queryAutoInvoiceList(Integer id, boolean checkFail) {
        // 计算3个月前的日期
        LocalDate threeMonthsAgoLocal = LocalDate.now().minusMonths(3);
        LocalDate currentDateLocal = LocalDate.now();
        // 转换为Date对象，并设置为0点0分00秒
        Date threeMonthsAgo = Date.from(threeMonthsAgoLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date currentDate = Date.from(currentDateLocal.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1));

        // 设置查询参数
        List<Integer> invoiceStatusList = checkFail ? Arrays.asList(InvoiceStatusEnum.WILL_INVOICE.getNo(), InvoiceStatusEnum.INVOICE_FAILED.getNo())
                : Arrays.asList(InvoiceStatusEnum.WILL_INVOICE.getNo()); // 待开票
        List<Integer> invoiceTypeList = Arrays.asList(InvoiceTypeEnum.ELECTRONIC_COMMON_INVOICE.getNo(), InvoiceTypeEnum.ELECTRONIC_SPECIAL_INVOICE.getNo()); // 电子普票和电子专票
        List<Integer> confirmStatusList = Arrays.asList(ConfirmationStatusEnum.CONFIRMED.key, ConfirmationStatusEnum.DONE.key); // 已确认和已完成


        // 调用Mapper方法查询符合条件的订单发票
        List<OrderInvoiceWithCompanyDO> orderInvoiceList = orderInvoiceMapper.queryAutoInvoiceList(
                invoiceStatusList,
                invoiceTypeList,
                confirmStatusList,
                threeMonthsAgo,
                currentDate,
                null, BaseEnum.NO.getKey(), id
        );

        if (CollectionUtils.isEmpty(orderInvoiceList)) {
            return Collections.emptyList();
        }
        return orderInvoiceList;
    }

    @Override
    public Response<Boolean> autoCreateAgentInvoice(Integer orderInvoiceId, String userName) {
        String webRequestId = SlsLoggerUtil.getWebRequestId();
        if (webRequestId == null) {
            webRequestId = UUID.randomUUID().toString();
        }
        List<OrderInvoiceWithCompanyDO> orderInvoiceWithCompanyDOS = orderInvoiceMapper.queryAutoInvoiceList(null, null, null, null, null, null, null
                , orderInvoiceId);
        if (CollUtilX.isEmpty(orderInvoiceWithCompanyDOS)) {
            throw new SysException(ErrorCodeEnum.UNKNOWN_EXCEPTION);
        }
        OrderInvoiceWithCompanyDO orderInvoiceWithCompanyDO = orderInvoiceWithCompanyDOS.get(0);
        if (ObjectUtil.isEmpty(orderInvoiceWithCompanyDO)) {
            throw new SysException(ErrorCodeEnum.INVOICE_CREATE_BILL_FAIL);
        }
        validAutoCreateAgentInvoice(orderInvoiceWithCompanyDO);
        return autoCreateAgentInvoice(orderInvoiceWithCompanyDO.getOrderCode(),
                orderInvoiceWithCompanyDO.getInvoiceNo(), orderInvoiceWithCompanyDO.getCompanyCode(), webRequestId,
                orderInvoiceWithCompanyDO.getAgentCode(), orderInvoiceWithCompanyDO.getAgentName(), orderInvoiceWithCompanyDO.getInvoiceAmount(), userName, orderInvoiceWithCompanyDO.getOrderId());
    }

    @Override
    public Response<Boolean> autoCreateAgentInvoice(String orderCode, String invoiceNo, String companyCode, String requestId, String agentCode, String agentName, BigDecimal invoiceAmt, String operator, Integer orderId) {
        AutoCreateAgentBillReq autoCreateAgentBillReq = new AutoCreateAgentBillReq();
        autoCreateAgentBillReq.setOrderCode(orderCode);
        autoCreateAgentBillReq.setInvoiceNo(invoiceNo);
        autoCreateAgentBillReq.setCompanyCode(companyCode);
        autoCreateAgentBillReq.setRequestId(requestId);
        autoCreateAgentBillReq.setAgentCode(agentCode);
        autoCreateAgentBillReq.setAgentName(agentName);
        autoCreateAgentBillReq.setInvoiceAmt(invoiceAmt);
        autoCreateAgentBillReq.setOperate(operator);
        autoCreateAgentBillReq.setOrderId(orderId);

        Response<AutoCreateAgentBillResp> autoCreateAgentBillRespResponse = agentInvoiceRemote.autoCreateAgentBill(autoCreateAgentBillReq);
        if (!autoCreateAgentBillRespResponse.isSuccess()) {
            throw new SysException(autoCreateAgentBillRespResponse.getFailCode(), autoCreateAgentBillRespResponse.getFailReason());
        }
        AutoCreateAgentBillResp model = autoCreateAgentBillRespResponse.getModel();
        // 查询订单信息
        OrderPO orderQuery = new OrderPO();
        orderQuery.setId(model.getOrderId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(orderQuery);
        if (orderPO == null) {
            throw new SysException("订单不存在");
        }
        //接收日志记录
        if (StrUtilX.isNotEmpty(model.getFailReason())) {
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    model.getOperator(),
                    orderPO.getOrderOwnerName(),
                    model.getOrderCode(),
                    model.getFailReason()
            );
        }

        // 更新订单发票状态
        OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
        orderInvoiceDTO.setOrderCode(model.getOrderCode());
        orderInvoiceDTO.setInvoiceStatus(model.getInvoiceStatus());
        orderInvoiceDTO.setOperator(model.getOperator());
        if (model.getInvoiceStatus() != null && InvoiceStatusEnum.CREATE_BILL_FAILED.getNo() == model.getInvoiceStatus()) {
            orderInvoiceDTO.setECreateBillMessage(StrUtil.format("订单号{}自动开票出现异常，请查看订单日志后进行对应处理，并检查是否已处理。", orderPO.getOrderCode()));
            orderInvoiceDTO.setECreateBillStatus(1);
        }
        if (model.getInvoiceStatus() != null && InvoiceStatusEnum.INVOICE_BINDING_FAILED.getNo() == model.getInvoiceStatus()) {
            orderInvoiceDTO.setECreateBillMessage(StrUtil.format("订单号{}自动开票出现异常，请查看订单日志后进行对应处理，并检查是否已处理。", orderPO.getOrderCode()));
            orderInvoiceDTO.setECreateBillStatus(1);
        }
        Integer result = orderService.updateOrderInvoice(orderInvoiceDTO, null);
        return model.getResult() == 1 ? Response.success(true) : Response.failure(model.getErrorCode(), model.getErrorMsg(), false);
    }

    private void validAutoCreateAgentInvoice(OrderInvoiceWithCompanyDO orderInvoiceWithCompanyDO) {

        if (!CommonConstants.YES.equals(orderInvoiceWithCompanyDO.getAutoOpenInvoiceStatus())) {
            throw new SysException(ErrorCodeEnum.CREATE_BILL_UN_OPEN_INVOICE_FAIL);
        }
        if (!CommonConstants.YES.equals(orderInvoiceWithCompanyDO.getFileUrlStatus()) || StrUtil.isEmpty(orderInvoiceWithCompanyDO.getInvoiceUrls())) {
            throw new SysException(ErrorCodeEnum.CREATE_BILL_UN_OPEN_INVOICE_DOWNLOAD_FAIL);
        }
        // 计算3个月前的日期
        LocalDate threeMonthsAgoLocal = LocalDate.now().minusMonths(3);
        LocalDate currentDateLocal = LocalDate.now();
        if (orderInvoiceWithCompanyDO.getEndTime() == null || orderInvoiceWithCompanyDO.getEndTime().compareTo(threeMonthsAgoLocal) < 0 || orderInvoiceWithCompanyDO.getEndTime().compareTo(currentDateLocal) > 0) {
            throw new SysException(ErrorCodeEnum.CREATE_BILL_UN_SUPPORT_ORDER_ENT_TIME_TYPE);
        }
        Integer CNY = 0;
        if (!CNY.equals(orderInvoiceWithCompanyDO.getSaleCurrency())) {
            throw new SysException(ErrorCodeEnum.CREATE_BILL_UN_SUPPORT_ORDER_SALE_CURRENCY_TYPE);
        }
        List<Integer> confirmStatusList = Arrays.asList(ConfirmationStatusEnum.CONFIRMED.key, ConfirmationStatusEnum.DONE.key); // 已确认和已完成
        if (!confirmStatusList.contains(orderInvoiceWithCompanyDO.getConfirmationStatus())) {
            throw new SysException(ErrorCodeEnum.CREATE_BILL_UN_SUPPORT_ORDER_STATUS_TYPE);
        }
    }

    /**
     * 查询待开票金额
     *
     * @param orderCode    订单编号（可以只传一个，接口会汇总返回结果）
     * @param merchantCode 商家编码
     * @return 待开票金额信息列表
     */
    private List<InvoiceableAgentInfoResp> queryInvoiceAbleAmount(String orderCode, String merchantCode) {
        try {
            // 创建查询参数
            InvoiceableAgentInfoQueryReq req = new InvoiceableAgentInfoQueryReq();
            req.setMerchantCode(merchantCode);
            req.setOrderCode(orderCode); // 只传一个订单编号，接口会汇总返回结果

            // 调用远程接口查询待开票金额
            Response<List<InvoiceableAgentInfoResp>> response = agentInvoiceRemote.queryInvoiceableAgentInfoList(req);
            if (response != null && response.isSuccess() && response.getModel() != null) {
                return response.getModel();
            } else {
                log.error("查询待开票金额失败：{}", response != null ? response.getFailReason() : "返回结果为空");
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("查询待开票金额异常", e);
            return Collections.emptyList();
        }
    }
}