package com.tiangong.order.service.common;

import com.tiangong.dis.entity.mongo.HotelInfoDB;
import com.tiangong.order.dto.OrderRateDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class MongodbCommonService {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 根据酒店ID获取MongoDb缓存中的酒店封装对象 减少数据库请求 返回的是 com.tiangong.dis.entity.vo.hotel.HotelInfo 字符串
     * 注意判断非空 使用字符串返回 可以转成Json 不用引用对象
     * HotelInfo hotelInfo = JSONObject.parseObject(hotelInfoDB.getHotelInfo(), HotelInfo.class);
     *
     * @param hotelId
     * @return
     */
    public String queryHotelFromMongoDb(Long hotelId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(hotelId));
        HotelInfoDB hotelInfoDB = mongoTemplate.findOne(query, HotelInfoDB.class);
        if (hotelInfoDB != null) {
            //com.tiangong.dis.entity.vo.hotel.HotelInfo
            //HotelInfo hotelInfo = JSONObject.parseObject(hotelInfoDB.getHotelInfo(), HotelInfo.class);
            return hotelInfoDB.getHotelInfo();
        }
        return null;
    }

    /**
     * 查询订单金额汇率转换后的数据
     */
    public OrderRateDto queryOrderRateFromMongoDb(String orderCode) {
        Query query = new Query();
        query.addCriteria(Criteria.where("orderCode").is(orderCode));
        OrderRateDto reportOrderReport = mongoTemplate.findOne(query, OrderRateDto.class, "report_orderReport");
        return reportOrderReport;
    }
    /**
     * 查询订单金额汇率转换后的数据
     */
    public List<OrderRateDto> queryOrderRateFromMongoDb(List<String> orderCode) {
        Query query = new Query();
        query.addCriteria(Criteria.where("orderCode").in(orderCode));
        query.fields().include("receivableOrgCurrencyAmt");
        query.fields().include("orderCode");
        List<OrderRateDto> reportOrderReport = mongoTemplate.find(query, OrderRateDto.class, "report_orderReport");
        return reportOrderReport;
    }

}
