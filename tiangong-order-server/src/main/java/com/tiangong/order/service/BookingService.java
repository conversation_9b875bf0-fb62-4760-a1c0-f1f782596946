package com.tiangong.order.service;

import com.tiangong.common.Response;
import com.tiangong.dto.order.CreditLineOrderDTO;
import com.tiangong.order.dto.OrderCodeDTO;
import com.tiangong.order.remote.dto.PreBookDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.OtaOrderDTO;
import com.tiangong.product.dto.ProductSalePriceDTO;

import java.util.List;

public interface BookingService {

    /**
     * 新建手工单
     */
    String addManualOrder(AddManualOrderDTO request);

    /**
     * 新建OTA订单
     */
    String addOTAOrder(AddManualOrderDTO request);

    /**
     * 试预定
     */
    ProductSalePriceDTO checkPreBook(PreBookDTO prebookDTO);

    /**
     * 查询ota订单详情
     */
    OtaOrderDTO queryOtaOrder(QueryOtaOrderDTO queryOtaOrderDTO);

    /**
     * 取消ota订单
     */
    Response<OtaOrderDTO> cancelOtaOrder(CancelOtaOrderDTO cancelOtaOrderDTO);

    /**
     * 新建帮客户订单
     */
    OrderCodeDTO addHelpOrder(AddManualOrderDTO request);

    /**
     * 根据订单号自动扣除额度
     */
    void autoDeductRefundCreditLineByOrderCode(String OrderCode);

    /**
     * 修改订单状态以及扣除额度
     */
    CreditLineOrderDTO updatePayStatusAndDeductRefundCreditLineByOrderCode(UpdatePayStatusAndDeductRefundCreditLineDTO request);

    /**
     * 添加供货单
     */
    void addSupplyOrder(AddManualOrderDTO request);

    /**
     * 添加手工供货单
     */
    void addManualSupplyOrder(AddManualOrderDTO request);

    /**
     * 校验供货单入住天数
     * 当 所有未取消供货单间夜 > 订单间夜 返回1
     */
    Integer checkSupplyOrderNightQty(AddManualOrderDTO request);
}
