package com.tiangong.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.B2BOrderStatusPushRequest;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.finance.OrgDTO;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.domain.OrderRequestPO;
import com.tiangong.order.domain.SupplyOrderPO;
import com.tiangong.order.dto.SupplyOrderContext;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.order.enums.SendingResultEnum;
import com.tiangong.order.enums.SendingTypeEnum;
import com.tiangong.order.enums.SupplyOrderTypeEnum;
import com.tiangong.order.remote.request.SaveSupplyResultDTO;
import com.tiangong.order.service.OrderSendingStrategy;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.supply.direct.remote.request.CancelOrderRequest;
import com.tiangong.supply.direct.remote.response.CancelOrderResponse;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

import static com.tiangong.util.DateUtilX.hour_format;

/**
 * 直连取消单策略
 */
@Slf4j
public class DirectCancelStrategy implements OrderSendingStrategy {

    @Override
    public void execute(SupplyOrderContext context) {
        // 构建请求参数
        CancelOrderRequest request = buildCancelRequest(context);
        // 发送请求
        Response<CancelOrderResponse> response = context.getSupplyDirectShubOrderRemote().cancelSupplierOrder(request);
        // 处理响应
        handleCancelResponse(context, response);
    }

    /**
     * 构建取消请求
     */
    private CancelOrderRequest buildCancelRequest(SupplyOrderContext context) {
        CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
        cancelOrderRequest.setCancelReason(context.getRequest().getRemark());
        cancelOrderRequest.setCancelRequestUser(context.getRequest().getOperator());
        cancelOrderRequest.setSupplyOrderCode(context.getSupplyOrder().getSupplyOrderCode());
        cancelOrderRequest.setSupplierOrderCode(context.getSupplyOrder().getSupplierOrderCode());
        cancelOrderRequest.setUserAccount(context.getOrder().getUserAccount());
        cancelOrderRequest.setRequestId(context.getRequest().getRequestId());
        return cancelOrderRequest;
    }

    /**
     * 处理取消响应
     */
    private void handleCancelResponse(SupplyOrderContext context, Response<CancelOrderResponse> response) {
        CancelOrderResponse cancelResponse = response.getModel();

        // 记录供应商返回日志
        String operation = String.format("发单给供应商，供应商返回：%s（供货单：%s，发送方式：%s，发单类型：%s）",
                response.isSuccess() ? response.getFailReason() : "失败，原因：" + response.getFailReason(),
                context.getSupplyOrder().getSupplyOrderCode(),
                SendingTypeEnum.getValueByKey(context.getRequest().getSendingType()),
                SupplyOrderTypeEnum.getValueByKey(context.getRequest().getSupplyOrderType()));
        context.getOrderCommonService().saveOrderLog(
                context.getSupplyOrder().getOrderId(),
                context.getRequest().getOperator(),
                context.getRequest().getOrderOwnerName(),
                context.getSupplyOrder().getSupplyOrderCode(),
                operation,
                response.isError() ? context.getRequest().getSupplyOrderType() : null
        );

        if (response.isSuccess() &&
                (ConfirmationStatusEnum.UNCONFIRM.key.equals(cancelResponse.getOrderConfirmationStatus()) ||
                        ConfirmationStatusEnum.CANCELED.key.equals(cancelResponse.getOrderConfirmationStatus()) ||
                        ConfirmationStatusEnum.CONFIRMED.key.equals(cancelResponse.getOrderConfirmationStatus()))) {

            // 立即取消成功
            if (cancelResponse.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                handleImmediateCancelSuccess(context, cancelResponse);
            }
            // 取消被拒绝
            else if (cancelResponse.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.CONFIRMED.key)) {
                handleCancelRejected(context, response, cancelResponse);
            }
            // 取消已提交（待确认）
            else {
                handleCancelSubmitted(context);
            }
            context.setSendingResult(SendingResultEnum.SUCCESS.key);
        }
        // 取消异常
        else {
            handleCancelException(context, response);
        }
    }

    /**
     * 处理立即取消成功
     */
    private void handleImmediateCancelSuccess(SupplyOrderContext context, CancelOrderResponse response) {
        // 保存成功日志
        context.getOrderCommonService().saveOrderLog(
                context.getSupplyOrder().getOrderId(),
                Constant.SYSTEM,
                context.getRequest().getOrderOwnerName(),
                context.getSupplyOrder().getSupplyOrderCode(),
                "调用供应商接口取消订单，确认供应商取消成功",
                null
        );

        // 构造确认结果DTO
        SaveSupplyResultDTO saveDto = new SaveSupplyResultDTO();
        saveDto.setSupplyOrderId(context.getSupplyOrder().getId());
        saveDto.setSupplyOrderCode(context.getSupplyOrder().getSupplyOrderCode());
        saveDto.setConfirmationStatus(ConfirmationStatusEnum.CANCELED.key);
        saveDto.setSupplierConfirmer(Constant.SYSTEM);
        saveDto.setOperator(Constant.SYSTEM);
        saveDto.setOrderOwnerName(context.getRequest().getOrderOwnerName());

        // 处理退款金额
        if (context.getRequest().getRefundFee() != null) {
            saveDto.setRefundFee(context.getRequest().getRefundFee());
        } else if (response.getRefundFee() != null) {
            BigDecimal refundFee = calculateRefundFee(context, response);
            saveDto.setRefundFee(refundFee);
            saveDto.setRefundFeeCurrency(response.getRefundFeeCurrency());
            saveDto.setSupplyRefundFee(response.getRefundFee());
        }

        // 更新供货单状态
        context.getSupplyOrderService().saveSupplyResult(saveDto);

        // 设置响应成功
        context.getResponse().setResult(ResultCodeEnum.SUCCESS.code);

        // 完成取消任务
        completeCancelTask(context, 1);
    }

    /**
     * 计算退款金额
     */
    private BigDecimal calculateRefundFee(SupplyOrderContext context, CancelOrderResponse response) {
        if (response.getRefundFee() == null) {
            return BigDecimal.ZERO;
        }

        // 币种转换
        if (response.getRefundFeeCurrency() != null &&
                !response.getRefundFeeCurrency().equals(context.getSupplyOrder().getBaseCurrency())) {
            BigDecimal orgRate = getRateToOrgCurrency(response.getRefundFeeCurrency());
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            BigDecimal rateToAgentRate = RedisUtil.getRateToTargetCurrency(
                    Integer.parseInt(orgDTO.getOrgCurrency()),
                    CompanyDTO.COMPANY_CODE,
                    context.getSupplyOrder().getBaseCurrency());
            return CommonTgUtils.setScale(
                    response.getRefundFee().multiply(orgRate).multiply(rateToAgentRate),
                    2, 1);
        }
        return response.getRefundFee();
    }

    /**
     * 获取币种对CNY的汇率
     *
     * @param originalCurrency 币种
     * @return 汇率
     */
    private BigDecimal getRateToOrgCurrency(Integer originalCurrency) {
        return RedisUtil.getRateToOrgCurrency(originalCurrency, CompanyDTO.COMPANY_CODE);
    }

    /**
     * 完成取消任务
     */
    private void completeCancelTask(SupplyOrderContext context, int handleResult) {
        Example example = new Example(OrderRequestPO.class);
        example.createCriteria()
                .andEqualTo("orderId", context.getSupplyOrder().getOrderId());

        OrderRequestPO update = new OrderRequestPO();
        update.setHandleResult(handleResult);
        context.getOrderRequestMapper().updateByExampleSelective(update, example);
    }

    /**
     * 处理取消被拒绝
     */
    private void handleCancelRejected(SupplyOrderContext context,
                                      Response<CancelOrderResponse> response,
                                      CancelOrderResponse cancelResponse) {
        // 构造失败原因
        String failReason = buildFailReason(response, cancelResponse);

        // 设置响应失败
        context.getResponse().setResult(ResultCodeEnum.FAILURE.code);
        context.getResponse().setFailCode(response.getFailCode());
        context.getResponse().setFailReason(failReason);;

        // 处理一单一供的情况
        if (isSingleSupplyOrder(context)) {
            // 标记取消任务失败
            completeCancelTask(context, 2);

            // 恢复订单状态
            resetOrderModificationStatus(context);

            // 推送下游系统
            pushCancelRejection(context, failReason);
        }
    }

    /**
     * 判断是否为一单一供
     */
    private boolean isSingleSupplyOrder(SupplyOrderContext context) {
        Example example = new Example(SupplyOrderPO.class);
        example.createCriteria()
                .andEqualTo("orderId", context.getOrder().getId());
        return context.getSupplyOrderMapper().selectCountByExample(example) == 1;
    }

    /**
     * 重置订单修改状态
     */
    private void resetOrderModificationStatus(SupplyOrderContext context) {
        if (context.getOrder().getModificationStatus() == 1 ||
                context.getOrder().getModificationStatus() == 6) {
            OrderPO update = new OrderPO();
            update.setId(context.getOrder().getId());
            update.setModificationStatus(0);
            context.getOrderMapper().updateByPrimaryKeySelective(update);
        }
    }

    /**
     * 推送取消失败原因
     */
    private void pushCancelRejection(SupplyOrderContext context, String reason) {
        B2BOrderStatusPushRequest pushRequest = new B2BOrderStatusPushRequest();
        pushRequest.setCoOrderCode(context.getOrder().getChannelOrderCode());
        pushRequest.setFcOrderCode(context.getOrder().getOrderCode());
        pushRequest.setOrderStatus(7); // 取消失败状态
        pushRequest.setMessage(reason);
        pushRequest.setAgentCode(context.getOrder().getAgentCode());
        pushRequest.setPartnerCode(context.getOrder().getPartnerCode());

        // 异步执行通知下游
        CompletableFuture.runAsync(() -> {
            try {
                context.getDisB2BRemote().orderStatusPush(pushRequest);
            } catch (Exception e) {
                log.error("调用渠道取消订单结果异常 request:{}", JSONObject.toJSONString(pushRequest), e);
            }
        });
    }

    /**
     * 处理取消已提交
     */
    private void handleCancelSubmitted(SupplyOrderContext context) {
        // 记录提交成功日志
        context.getOrderCommonService().saveOrderLog(
                context.getSupplyOrder().getOrderId(),
                context.getRequest().getOperator(),
                context.getRequest().getOrderOwnerName(),
                context.getSupplyOrder().getSupplyOrderCode(),
                "调用供应商接口取消订单，供应商返回提交成功",
                null
        );

        // 设置响应成功
        context.getResponse().setResult(ResultCodeEnum.SUCCESS.code);
    }

    /**
     * 构造失败原因
     */
    private void handleCancelException(SupplyOrderContext context, Response<CancelOrderResponse> response) {
        if (ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode.equals(response.getFailCode()) ||
                ErrorCodeEnum.CONNECT_TIME_OUT.errorCode.equals(response.getFailCode()) ||
                ErrorCodeEnum.SUPPLY_RETURN_RESULT_EMPTY.errorCode.equals(response.getFailCode()) ||
                ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorCode.equals(response.getFailCode())) {
            // 记录异常日志
            String errorContent = "申请退款供货单：于" +
                    DateUtilX.dateToString(DateUtilX.getCurrentDate(), hour_format) +
                    "向" + context.getSupplyOrder().getSupplierName() + "申请取消订单异常。";

            context.getOrderCommonService().saveOrderLog(
                    context.getOrder().getId(),
                    Constant.SYSTEM,
                    context.getRequest().getOrderOwnerName(),
                    context.getSupplyOrder().getSupplyOrderCode(),
                    errorContent,
                    null
            );

            // 标记订单异常状态
            OrderPO orderUpdate = new OrderPO();
            orderUpdate.setId(context.getOrder().getId());
            orderUpdate.setModificationStatus(6); // 取消异常状态
            context.getOrderMapper().updateByPrimaryKeySelective(orderUpdate);

            // 标记供货单异常
            SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
            supplyOrderUpdate.setId(context.getSupplyOrder().getId());
            supplyOrderUpdate.setCancelExceptionFlag(1);
            supplyOrderUpdate.setCancelExceptionContext(errorContent);
            context.getSupplyOrderMapper().updateByPrimaryKeySelective(supplyOrderUpdate);
        }

        // 设置响应失败
        context.getResponse().setResult(ResultCodeEnum.FAILURE.code);
        context.getResponse().setFailCode(response.getFailCode());
        context.getResponse().setFailReason(response.getFailReason());

        context.setSendingResult(ResultCodeEnum.FAILURE.code);
    }

    /**
     * 构造失败原因
     */
    private String buildFailReason(Response<CancelOrderResponse> response, CancelOrderResponse cancelResponse) {
        StringBuilder reason = new StringBuilder();
        if (StrUtilX.isNotEmpty(response.getFailReason())) {
            reason.append(response.getFailReason());
        }
        if (StrUtilX.isNotEmpty(cancelResponse.getReason())) {
            reason.append(cancelResponse.getReason());
        }
        return reason.length() > 0 ? reason.toString() : "取消失败 供应商返回为空";
    }
}
