package com.tiangong.order.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.finance.enums.BusinessTypeEnum;
import com.tiangong.finance.remote.workorder.FinanceNofityRemote;
import com.tiangong.finance.remote.workorder.request.NotifyCollectionDTO;
import com.tiangong.finance.remote.workorder.request.NotifyItemDTO;
import com.tiangong.finance.remote.workorder.request.NotifyPaymentDTO;
import com.tiangong.finance.remote.workorder.request.WorkOrderAttchDTO;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.GuestPO;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.domain.SupplyOrderFinancePO;
import com.tiangong.order.domain.SupplyOrderPO;
import com.tiangong.order.dto.SupplyOrderAmtDTO;
import com.tiangong.order.mapper.GuestMapper;
import com.tiangong.order.mapper.OrderMapper;
import com.tiangong.order.mapper.SupplyOrderFinanceMapper;
import com.tiangong.order.mapper.SupplyOrderMapper;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.OnTimeSupplyOrderDTO;
import com.tiangong.order.remote.response.SupplyOrderFinanceDTO;
import com.tiangong.order.service.SupplyOrderFinanceService;
import com.tiangong.order.service.common.OrderCommonService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SupplyOrderFinanceServiceImpl implements SupplyOrderFinanceService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private SupplyOrderMapper supplyOrderMapper;

    @Autowired
    private SupplyOrderFinanceMapper supplyOrderFinanceMapper;

    @Autowired
    private GuestMapper guestMapper;



    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private FinanceNofityRemote financeNofityRemote;

    @Override
    public PaginationSupportDTO<OnTimeSupplyOrderDTO> queryOnTimeSupplyOrderList(QueryOnTimeSupplyOrderListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<OnTimeSupplyOrderDTO> list = supplyOrderMapper.queryOnTimeSupplyOrderList(request);
        PageInfo<OnTimeSupplyOrderDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<OnTimeSupplyOrderDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Transactional
    @Override
    public Response<Object> notifyCollectionOfSupplyOrder(NotifyCollectionOfSupplyOrderDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());

        // 1.创建工单
        NotifyCollectionDTO notifyCollectionDTO = new NotifyCollectionDTO();
        BeanUtils.copyProperties(request, notifyCollectionDTO);
        //fixme mapper类型转换BUG
//        NotifyCollectionDTO notifyCollectionDTO = ProductSalePriceConvert.INSTANCE.notifyColleConvert(request);
        notifyCollectionDTO.setBusinessType(BusinessTypeEnum.SUPPLYORDER.key);
        notifyCollectionDTO.setNotifyItemDTOList(Collections.singletonList(new NotifyItemDTO(
                supplyOrderPO.getSupplyOrderCode(),
                request.getAmt()
        )));
        notifyCollectionDTO.setCollectionAmt(request.getAmt());
        notifyCollectionDTO.setOrgCode(supplyOrderPO.getSupplierCode());
        notifyCollectionDTO.setOrgName(supplyOrderPO.getSupplierName());
        notifyCollectionDTO.setCompanyCode(orderPO.getCompanyCode());
        notifyCollectionDTO.setContent("供货单款");
        notifyCollectionDTO.setCurrency(supplyOrderPO.getBaseCurrency());
        notifyCollectionDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        if (CollUtilX.isNotEmpty(request.getPhotoList())) {
            List<WorkOrderAttchDTO> photoList = ProductSalePriceConvert.INSTANCE.workOrderAttchDTOListConvert(request.getPhotoList());
            notifyCollectionDTO.setPhotoList(photoList);
        }
        notifyCollectionDTO.setCreatedBy(request.getOperator());
        Response<Object> response = financeNofityRemote.notifyCollection(notifyCollectionDTO);

        if (response.getResult().equals(ResultCodeEnum.SUCCESS.code)) {
            // 2.更新已通知金额
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(request.getSupplyOrderId());
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            supplyOrderFinanceUpdate.setUnconfirmedReceivedAmt(supplyOrderFinancePO.getUnconfirmedReceivedAmt().add(request.getAmt()));
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
        }

        // 3. 记日志
        StringBuilder content = new StringBuilder();
        content.append("供货单").append(supplyOrderPO.getSupplyOrderCode()).append("通知财务收款，通知金额：").append(request.getAmt())
                .append("，通知结果：")
                .append(response.getResult().equals(ResultCodeEnum.SUCCESS.code) ? "成功" : "失败");
        if (response.getResult().equals(ResultCodeEnum.FAILURE.code)) {
            content.append("，失败原因：").append(response.getFailReason());
        }
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                content.toString()
        );

        return response;
    }

    @Transactional
    @Override
    public Response<Integer> notifyPaymentOfSupplyOrder(NotifyPaymentOfSupplyOrderDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());

        // 1.创建工单
        NotifyPaymentDTO notifyPaymentDTO = new NotifyPaymentDTO();
        BeanUtils.copyProperties(request, notifyPaymentDTO);
        //fixme mapper类型转换BUG
//        NotifyPaymentDTO notifyPaymentDTO = ProductSalePriceConvert.INSTANCE.notifyPaymentSupplyOrderConvert(request);
        notifyPaymentDTO.setBusinessType(BusinessTypeEnum.SUPPLYORDER.key);
        notifyPaymentDTO.setNotifyItemDTOList(Collections.singletonList(new NotifyItemDTO(
                supplyOrderPO.getSupplyOrderCode(),
                request.getAmt()
        )));
        notifyPaymentDTO.setPaymentAmt(request.getAmt());
        notifyPaymentDTO.setOrgCode(supplyOrderPO.getSupplierCode());
        notifyPaymentDTO.setOrgName(supplyOrderPO.getSupplierName());
        notifyPaymentDTO.setCompanyCode(orderPO.getCompanyCode());
        notifyPaymentDTO.setContent("供货单款");
        notifyPaymentDTO.setCurrency(supplyOrderPO.getBaseCurrency());
        notifyPaymentDTO.setCreatedBy(request.getOperator());
        notifyPaymentDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        if (CollUtilX.isNotEmpty(request.getPhotoList())) {
            List<WorkOrderAttchDTO> photoList = ProductSalePriceConvert.INSTANCE.workOrderAttchDTOListConvert(request.getPhotoList());
            notifyPaymentDTO.setPhotoList(photoList);
        }
        Response<Integer> response = financeNofityRemote.notifyPayment(notifyPaymentDTO);

        if (response.getResult().equals(ResultCodeEnum.SUCCESS.code)) {
            // 2.更新已通知金额
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(request.getSupplyOrderId());
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            supplyOrderFinanceUpdate.setUnconfirmedPaidAmt(supplyOrderFinancePO.getUnconfirmedPaidAmt().add(request.getAmt()));
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
        }

        // 3. 记日志
        StringBuilder content = new StringBuilder();
        content.append("供货单(").append(supplyOrderPO.getSupplyOrderCode()).append(")通知财务付款，通知金额：").append(request.getAmt());
//                .append("，通知结果：")
//                .append(response.getResult().equals(ResultCodeEnum.SUCCESS.code) ? "成功" : "失败");
//        if (response.getResult().equals(ResultCodeEnum.FAILURE.code)) {
//            content.append("，失败原因：" + response.getFailReason());
//        }
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                content.toString()
        );

        return response;
    }

    @Transactional
    @Override
    public Response<Object> notifyCollectionOfSupplyOrderList(NotifyCollectionOfSupplyOrderListDTO request) {
        List<NotifyItemDTO> notifyItemDTOList = new ArrayList<>();
        List<SupplyOrderAmtDTO> supplyOrderAmtDTOList = supplyOrderFinanceMapper.querySupplyOrderAmt(new SupplyOrderIdListDTO(request.getSupplyOrderIdList()));
        for (SupplyOrderAmtDTO supplyOrderAmtDTO : supplyOrderAmtDTOList) {
            BigDecimal collectionAmt = BigDecimal.ZERO.subtract(supplyOrderAmtDTO.getSupplyOrderAmt()
                    .subtract(supplyOrderAmtDTO.getPaidAmt())
                    .subtract(supplyOrderAmtDTO.getUnconfirmedPaidAmt())
                    .add(supplyOrderAmtDTO.getUnconfirmedReceivedAmt()));
            notifyItemDTOList.add(new NotifyItemDTO(
                    supplyOrderAmtDTO.getSupplyOrderCode(),
                    collectionAmt
            ));
        }

        // 2.创建工单
        NotifyCollectionDTO notifyCollectionDTO = new NotifyCollectionDTO();
        BeanUtils.copyProperties(request, notifyCollectionDTO);
        //fixme mapper类型转换BUG
//        NotifyCollectionDTO notifyCollectionDTO = ProductSalePriceConvert.INSTANCE.notifyConvert(request);

        notifyCollectionDTO.setCollectionAmt(request.getAmt());
        notifyCollectionDTO.setBusinessType(BusinessTypeEnum.SUPPLYORDER.key);
        notifyCollectionDTO.setNotifyItemDTOList(notifyItemDTOList);
        notifyCollectionDTO.setOrgCode(supplyOrderAmtDTOList.get(0).getSupplierCode());
        notifyCollectionDTO.setOrgName(supplyOrderAmtDTOList.get(0).getSupplierName());
        notifyCollectionDTO.setCompanyCode(supplyOrderAmtDTOList.get(0).getCompanyCode());
        notifyCollectionDTO.setContent("供货单款");
        notifyCollectionDTO.setCurrency(supplyOrderAmtDTOList.get(0).getCurrency());
        if (CollUtilX.isNotEmpty(request.getPhotoList())) {
            List<WorkOrderAttchDTO> photoList = ProductSalePriceConvert.INSTANCE.workOrderAttchDTOListConvert(request.getPhotoList());
            notifyCollectionDTO.setPhotoList(photoList);
        }
        notifyCollectionDTO.setCreatedBy(request.getOperator());
        notifyCollectionDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        Response<Object> response = financeNofityRemote.notifyCollection(notifyCollectionDTO);

        for (SupplyOrderAmtDTO supplyOrderAmtDTO : supplyOrderAmtDTOList) {
            BigDecimal collectionAmt = BigDecimal.ZERO.subtract(supplyOrderAmtDTO.getSupplyOrderAmt()
                    .subtract(supplyOrderAmtDTO.getPaidAmt())
                    .subtract(supplyOrderAmtDTO.getUnconfirmedPaidAmt())
                    .add(supplyOrderAmtDTO.getUnconfirmedReceivedAmt()));

            if (response.getResult().equals(ResultCodeEnum.SUCCESS.code)) {
                // 2.更新已通知金额
                SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
                supplyOrderFinanceUpdate.setId(supplyOrderAmtDTO.getSupplyOrderFinanceId());
                supplyOrderFinanceUpdate.setUnconfirmedReceivedAmt(supplyOrderAmtDTO.getUnconfirmedReceivedAmt().add(collectionAmt));
                supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
            }

            // 3. 记日志
            StringBuilder content = new StringBuilder();
            content.append("供货单").append(supplyOrderAmtDTO.getSupplyOrderCode()).append("通知财务收款，通知金额：").append(collectionAmt)
                    .append("，通知结果：")
                    .append(response.getResult().equals(ResultCodeEnum.SUCCESS.code) ? "成功" : "失败");
            if (response.getResult().equals(ResultCodeEnum.FAILURE.code)) {
                content.append("，失败原因：").append(response.getFailReason());
            }
            orderCommonService.saveOrderLog(
                    supplyOrderAmtDTO.getOrderId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderAmtDTO.getOrderCode(),
                    content.toString()
            );
        }

        return response;
    }

    @Transactional
    @Override
    public Response<Integer> notifyPaymentOfSupplyOrderList(NotifyPaymentOfSupplyOrderListDTO request) {
        List<NotifyItemDTO> notifyItemDTOList = new ArrayList<>();
        List<SupplyOrderAmtDTO> supplyOrderAmtDTOList = supplyOrderFinanceMapper.querySupplyOrderAmt(new SupplyOrderIdListDTO(request.getSupplyOrderIdList()));
        for (SupplyOrderAmtDTO supplyOrderAmtDTO : supplyOrderAmtDTOList) {
            BigDecimal paymentAmt = supplyOrderAmtDTO.getSupplyOrderAmt()
                    .subtract(supplyOrderAmtDTO.getPaidAmt())
                    .subtract(supplyOrderAmtDTO.getUnconfirmedPaidAmt())
                    .add(supplyOrderAmtDTO.getUnconfirmedReceivedAmt());
            notifyItemDTOList.add(new NotifyItemDTO(
                    supplyOrderAmtDTO.getSupplyOrderCode(),
                    paymentAmt
            ));
        }

        // 2.创建工单
        NotifyPaymentDTO notifyPaymentDTO = new NotifyPaymentDTO();
        BeanUtils.copyProperties(request, notifyPaymentDTO);
        //fixme mapper类型转换BUG
//        NotifyPaymentDTO notifyPaymentDTO = ProductSalePriceConvert.INSTANCE.notifyPaymentDTO(request);
        notifyPaymentDTO.setBusinessType(BusinessTypeEnum.ORDER.key);
        notifyPaymentDTO.setPaymentAmt(request.getAmt());
        notifyPaymentDTO.setNotifyItemDTOList(notifyItemDTOList);
        notifyPaymentDTO.setOrgCode(supplyOrderAmtDTOList.get(0).getSupplierCode());
        notifyPaymentDTO.setOrgName(supplyOrderAmtDTOList.get(0).getSupplierName());
        notifyPaymentDTO.setCompanyCode(supplyOrderAmtDTOList.get(0).getCompanyCode());
        notifyPaymentDTO.setContent("供货单款");
        notifyPaymentDTO.setCurrency(supplyOrderAmtDTOList.get(0).getCurrency());
        notifyPaymentDTO.setCreatedBy(request.getOperator());
        notifyPaymentDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        if (CollUtilX.isNotEmpty(request.getPhotoList())) {
            List<WorkOrderAttchDTO> photoList = ProductSalePriceConvert.INSTANCE.NotifyAttchDTOConvert(request.getPhotoList());

            notifyPaymentDTO.setPhotoList(photoList);
        }
        Response<Integer> response = financeNofityRemote.notifyPayment(notifyPaymentDTO);

        for (SupplyOrderAmtDTO supplyOrderAmtDTO : supplyOrderAmtDTOList) {
            BigDecimal paymentAmt = supplyOrderAmtDTO.getSupplyOrderAmt()
                    .subtract(supplyOrderAmtDTO.getPaidAmt())
                    .subtract(supplyOrderAmtDTO.getUnconfirmedPaidAmt())
                    .add(supplyOrderAmtDTO.getUnconfirmedReceivedAmt());

            if (response.getResult().equals(ResultCodeEnum.SUCCESS.code)) {
                // 2.更新已通知金额
                SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
                supplyOrderFinanceUpdate.setId(supplyOrderAmtDTO.getSupplyOrderFinanceId());
                supplyOrderFinanceUpdate.setUnconfirmedPaidAmt(supplyOrderAmtDTO.getUnconfirmedPaidAmt().add(paymentAmt));
                supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
            }

            // 3. 记日志
            StringBuilder content = new StringBuilder();
            content.append("供货单").append(supplyOrderAmtDTO.getSupplyOrderCode()).append("通知财务收款，通知金额：").append(paymentAmt)
                    .append("，通知结果：")
                    .append(response.getResult().equals(ResultCodeEnum.SUCCESS.code) ? "成功" : "失败");
            if (response.getResult().equals(ResultCodeEnum.FAILURE.code)) {
                content.append("，失败原因：").append(response.getFailReason());
            }
            orderCommonService.saveOrderLog(
                    supplyOrderAmtDTO.getOrderId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderAmtDTO.getOrderCode(),
                    content.toString()
            );
        }

        return response;
    }

    @Override
    public Response<BigDecimal> notifyCollectionPreviewOfSupplyOrderList(SupplyOrderIdListDTO request) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        List<SupplyOrderAmtDTO> supplyOrderAmtDTOList = supplyOrderFinanceMapper.querySupplyOrderAmt(new SupplyOrderIdListDTO(request.getSupplyOrderIdList()));
        for (SupplyOrderAmtDTO supplyOrderAmtDTO : supplyOrderAmtDTOList) {
            BigDecimal collectionAmt = BigDecimal.ZERO.subtract(supplyOrderAmtDTO.getSupplyOrderAmt()
                    .subtract(supplyOrderAmtDTO.getPaidAmt())
                    .subtract(supplyOrderAmtDTO.getUnconfirmedPaidAmt())
                    .add(supplyOrderAmtDTO.getUnconfirmedReceivedAmt()));
            totalAmt = totalAmt.add(collectionAmt);
        }
        return Response.success(totalAmt);
    }

    @Override
    public Response<BigDecimal> notifyPaymentPreviewOfSupplyOrderList(SupplyOrderIdListDTO request) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        List<SupplyOrderAmtDTO> supplyOrderAmtDTOList = supplyOrderFinanceMapper.querySupplyOrderAmt(new SupplyOrderIdListDTO(request.getSupplyOrderIdList()));
        for (SupplyOrderAmtDTO supplyOrderAmtDTO : supplyOrderAmtDTOList) {
            BigDecimal paymentAmt = supplyOrderAmtDTO.getSupplyOrderAmt()
                    .subtract(supplyOrderAmtDTO.getPaidAmt())
                    .subtract(supplyOrderAmtDTO.getUnconfirmedPaidAmt())
                    .add(supplyOrderAmtDTO.getUnconfirmedReceivedAmt());
            totalAmt = totalAmt.add(paymentAmt);
        }
        return Response.success(totalAmt);
    }

    /**
     * 查询供货单财务相关信息【提供自助结算模块查询】
     * @param request 请求参数
     * @return 返回供货单财务相关信息
     */
    @Override
    public List<SupplyOrderFinanceDTO> querySupplyOrderFinanceInfo(SupplyOrderInfoListDTO request) {
        return supplyOrderFinanceMapper.querySupplyOrderFinanceInfo(request);
    }


    /**
     * 根据订单id查询入住人详细信息
     */
    @Override
    public List<GuestPO> queryGuestNameList(List<Integer> orderIds) {
        Example guestExample = new Example(GuestPO.class);
        Example.Criteria guestExampleCriteria = guestExample.createCriteria();
        guestExampleCriteria.andIn("orderId",orderIds);
        return guestMapper.selectByExample(guestExample);
    }
}