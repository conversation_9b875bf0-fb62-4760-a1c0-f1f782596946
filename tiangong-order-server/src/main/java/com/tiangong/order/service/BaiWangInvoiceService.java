package com.tiangong.order.service;

import com.baiwang.open.entity.request.OutputEinvoiceQueryRequest;
import com.baiwang.open.entity.request.OutputFormatQueryQdInvoiceRequest;
import com.baiwang.open.entity.request.OutputInvoiceIssueRequest;
import com.baiwang.open.entity.request.OutputInvoiceQueryRequest;
import com.baiwang.open.entity.response.OutputEinvoiceQueryResponse;
import com.baiwang.open.entity.response.OutputFormatQueryQdInvoiceResponse;
import com.baiwang.open.entity.response.OutputInvoiceIssueResponse;
import com.baiwang.open.entity.response.OutputInvoiceQueryResponse;

/**
 * <AUTHOR>
 * @Date 2025/5/7
 * @Description: 百望云发票服务
 */
public interface BaiWangInvoiceService {
    /**
     * 开票
     *
     * @param request   查询参数
     * @param requestId 请求id
     * @return
     */
    OutputInvoiceIssueResponse invoiceOpen(OutputInvoiceIssueRequest request, String requestId);

    /**
     * 查询全版发票
     *
     * @param request   查询参数
     * @param requestId 请求id
     * @return
     */
    OutputFormatQueryQdInvoiceResponse queryQdInvoice(OutputFormatQueryQdInvoiceRequest request, String requestId);

    /**
     * 发票查询
     *
     * @param request   查询参数
     * @param requestId 请求id
     * @return
     */
    OutputEinvoiceQueryResponse getInvoiceDetail(OutputEinvoiceQueryRequest request, String requestId);
}
