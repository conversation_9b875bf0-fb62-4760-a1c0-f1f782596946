package com.tiangong.order.service.impl;

import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.order.enums.SendingTypeEnum;
import com.tiangong.order.enums.SupplyOrderTypeEnum;
import com.tiangong.order.service.OrderSendingStrategy;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 策略工厂实现
 */
public class OrderSendingStrategyFactory {

    private static final Map<String, OrderSendingStrategy> strategies = new ConcurrentHashMap<>();

    static {
        // 注册直连策略
        strategies.put(key(SendingTypeEnum.DIRECT_LINK.key, SupplyOrderTypeEnum.BOOK.key),
                new DirectBookingStrategy());
        strategies.put(key(SendingTypeEnum.DIRECT_LINK.key, SupplyOrderTypeEnum.RESEND.key),
                new DirectBookingStrategy());
        strategies.put(key(SendingTypeEnum.DIRECT_LINK.key, SupplyOrderTypeEnum.CANCEL.key),
                new DirectCancelStrategy());
    }

    /**
     * 根据key获取对应的策略
     */
    public static OrderSendingStrategy getStrategy(Integer sendingType, Integer orderType) {
        String key = key(sendingType, orderType);
        OrderSendingStrategy strategy = strategies.get(key);
        if (strategy == null) {
            throw new SysException(ErrorCodeEnum.UNSUPPORTED_ORDER_OPERATION);
        }
        return strategy;
    }

    /**
     * 组合key
     */
    private static String key(Integer sendingType, Integer orderType) {
        return sendingType + "-" + orderType;
    }
}