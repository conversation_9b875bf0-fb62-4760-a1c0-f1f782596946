package com.tiangong.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.Response;
import com.tiangong.common.remote.SequenceRemote;
import com.tiangong.dis.dto.OrderDeductionPushRequest;
import com.tiangong.dis.remote.DisB2BRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.order.config.SettingsConstant;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.domain.OrderProductPricePO;
import com.tiangong.order.domain.OrderRefundTaskLogPO;
import com.tiangong.order.domain.OrderRefundTaskPO;
import com.tiangong.order.dto.*;
import com.tiangong.order.enums.ApiNameEnum;
import com.tiangong.order.mapper.OrderMapper;
import com.tiangong.order.mapper.OrderProductPriceMapper;
import com.tiangong.order.mapper.OrderRefundTaskLogMapper;
import com.tiangong.order.mapper.OrderRefundTaskMapper;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.service.OrderRefundTaskService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.math.BigDecimal;
import java.net.ConnectException;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderRefundTaskServiceImpl extends ServiceImpl<OrderRefundTaskMapper, OrderRefundTaskPO> implements OrderRefundTaskService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private SequenceRemote sequenceRemote;

    @Autowired
    private OrderProductPriceMapper orderProductPriceMapper;

    @Autowired
    private OrderRefundTaskLogMapper orderRefundTaskLogMapper;

    @Autowired
    private SettingsConstant orderRefundConfig;

    @Autowired
    private OrderRefundTaskMapper orderRefundTaskMapper;

    @Autowired
    private DisB2BRemote disB2BRemote;

    @Override
    @Transactional
    public Response<Object> saveOrderRefundTask(OrderRefundTaskDTO request) {
        if (request.getTaskType() == 0 && request.getRefundType() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 订单不存在
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        if (null == orderPO) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }

//        OrderRefundTaskPO refundTask = new OrderRefundTaskPO();
        //退款金额
        BigDecimal refundAmount = request.getRefundAmount() == null ? BigDecimal.ZERO : request.getRefundAmount();

        //按天退款明细
        String dailyRefund = null;

        //判断是否保存订单明细
        if (request.getRefundType() != null && request.getRefundType() == OrderRefundTypeEnum.COMMON.no) {
            //过滤价格为0.00的数据
            List<OrderDailyRefundInfo> dailyRefundInfoList = new ArrayList<>();
            for (OrderRefundDetailDTO orderRefundDetailDTO : request.getRefundDetailList()) {
                OrderDailyRefundInfo dailyRefundInfo = new OrderDailyRefundInfo();
                //价格为0.00,则不记录
                if (orderRefundDetailDTO.getRefundAmount().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                //预定日期
                dailyRefundInfo.setCheckinDate(orderRefundDetailDTO.getRefundDate());
                dailyRefundInfo.setRefundAmt(orderRefundDetailDTO.getRefundAmount());
                dailyRefundInfoList.add(dailyRefundInfo);
            }
            dailyRefund = JSON.toJSONString(dailyRefundInfoList);
        }

        //入库
//        BeanUtils.copyProperties(request, refundTask);
        OrderRefundTaskPO refundTask = ProductSalePriceConvert.INSTANCE.refundTaskConvert(request);
        refundTask.setCreatedDt(new Date());
        refundTask.setRefundAmount(refundAmount);
        refundTask.setDailyRefundInfo(dailyRefund);
        refundTask.setIsPush(RefundTaskPushEnum.NO_PUSH.key);
        refundTask.setRefundState(OrderRefundStateEnum.NEW.no);
        refundTask.setRefundTaskType(OrderRefundTaskTypeEnum.MANUAL.key);
        //任务编码生成
        String key = SystemCodeEnum.SEQ_ORDER_REFUND_TASK_CODE.code;
        if (request.getTaskType() == 1) {
            key = SystemCodeEnum.SEQ_ORDER_DEDUCTION_TASK_CODE.code;
        }
        String code = RedisTemplateX.lRightPop(key);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", key);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(key);
        }
        refundTask.setTaskCode(code);
        this.baseMapper.insert(refundTask);

        //记录日志
        OrderRefundTaskLogPO refundTaskLog = new OrderRefundTaskLogPO();
        refundTaskLog.setRefundTaskId(refundTask.getId());
        refundTaskLog.setCreatedBy(refundTask.getCreatedBy());
        refundTaskLog.setCreatedDt(new Date());
        if (refundTask.getTaskType() == 0) {
            refundTaskLog.setContent("新建退款通知任务");
        } else {
            refundTaskLog.setContent("新建担保扣款通知任务");
        }
        orderRefundTaskLogMapper.insert(refundTaskLog);

        return Response.success();
    }

    @Override
    public Response<OrderRefundDTO> queryOrderRefundInfo(QueryOrderRefundDTO request) {
        // 订单是否存在
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderCode(request.getOrderCode());
        orderPO = orderMapper.selectOne(orderPO);
        if (null == orderPO) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }

        //退款任务
        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setOrderId(orderPO.getId());
        orderRefundDTO.setOrderCode(orderPO.getOrderCode());
        orderRefundDTO.setAgentCode(orderPO.getAgentCode());
        orderRefundDTO.setCurrency(orderPO.getSaleCurrency());


        //查询订单价格明细
        Example orderProductPriceExample = new Example(OrderProductPricePO.class);
        Example.Criteria orderProductPriceCriteria = orderProductPriceExample.createCriteria();
        orderProductPriceCriteria.andEqualTo("orderId", orderPO.getId());
        List<OrderProductPricePO> orderProductPricePOList = orderProductPriceMapper.selectByExample(orderProductPriceExample);
        log.info("orderProductPricePOList:{}", JSON.toJSONString(orderProductPricePOList));
        Map<String, BigDecimal> priceMap = new HashMap<>();
        for (OrderProductPricePO orderProductPricePO : orderProductPricePOList) {
            //售卖日期为空是房间的税
            if (orderProductPricePO.getSalePrice() == null) {
                continue;
            }
            String saleDate = DateUtilX.dateToString(orderProductPricePO.getSaleDate());
            priceMap.merge(saleDate, orderProductPricePO.getSalePrice(), BigDecimal::add);
        }

        //订单总额
        BigDecimal refundAmount = BigDecimal.ZERO;
        //组装每日金额售价
        List<OrderRefundDetailDTO> detailList = new ArrayList<>();
        for (String date : priceMap.keySet()) {
            OrderRefundDetailDTO detailDTO = new OrderRefundDetailDTO();
            detailDTO.setRefundDate(date);
            detailDTO.setRefundAmount(priceMap.get(date));
            refundAmount = refundAmount.add(priceMap.get(date));
            detailList.add(detailDTO);
        }
        orderRefundDTO.setRefundAmount(refundAmount);


        //判断类型，仅 任务类型=在线退款通知 且 退款类 =普通退款（平安），
        // 才需返回订单每日明细
        if (request.getTaskType() == 0 && request.getRefundType() == OrderRefundTypeEnum.COMMON.no) {

            // 根据 时间 排序
            Collections.sort(detailList, new Comparator<OrderRefundDetailDTO>() {
                @Override
                public int compare(OrderRefundDetailDTO item1, OrderRefundDetailDTO item2) {
                    return item1.getRefundDate().compareTo(item2.getRefundDate());
                }
            });

            orderRefundDTO.setRefundDetailList(detailList);
        }
        return Response.success(orderRefundDTO);
    }

    @Override
    public Response<PaginationSupportDTO<OrderRefundTaskDetailDTO>> queryOrderRefundTaskPage(QueryOrderRefundTaskDTO request) {
        IPage<OrderRefundTaskDetailDTO> iPage = new Page<>(request.getCurrentPage(), request.getPageSize());
        IPage<OrderRefundTaskDetailDTO> page = this.baseMapper.queryOrderRefundTaskPage(iPage, request);

        PaginationSupportDTO<OrderRefundTaskDetailDTO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(page);
        return Response.success(paginationSupportDTO);
    }

    @Override
    public Response<OrderRefundTaskDetailDTO> queryOrderRefundTaskDetail(QueryOrderRefundDTO request) {
        OrderRefundTaskPO orderRefundTaskPO = this.baseMapper.selectById(request.getRefundTaskId());
//        OrderRefundTaskDetailDTO detailDTO = new OrderRefundTaskDetailDTO();
//        BeanUtils.copyProperties(orderRefundTaskPO, detailDTO);
        OrderRefundTaskDetailDTO detailDTO = ProductSalePriceConvert.INSTANCE.detailDTOConvert(orderRefundTaskPO);
        detailDTO.setRefundTaskId(orderRefundTaskPO.getId());
        return Response.success(detailDTO);
    }

    @Override
    public Response<PageVo> queryOrderRefundTaskLogPage(QueryOrderRefundDTO request) {
        IPage<OrderRefundTaskLogPO> iPage = new Page<>(request.getCurrentPage(), request.getPageSize());

        QueryWrapper<OrderRefundTaskLogPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OrderRefundTaskLogPO::getRefundTaskId, request.getRefundTaskId())
                .orderByDesc(OrderRefundTaskLogPO::getCreatedDt);
        //分页查询日志
        IPage<OrderRefundTaskLogPO> ipage = orderRefundTaskLogMapper.selectPage(iPage, wrapper);
        //转换响应参数
        List<OrderRefundTaskLogDTO> collect = ipage.getRecords().stream().map((item) -> {
            OrderRefundTaskLogDTO resp = new OrderRefundTaskLogDTO();
            resp.setContent(item.getContent());
            resp.setOperator(item.getCreatedBy());
            resp.setOperatedTime(DateUtilX.dateToString(item.getCreatedDt(), DateUtilX.hour_format));
            return resp;
        }).collect(Collectors.toList());

        return Response.success(PageVo.result(ipage, collect));
    }

    @Override
    public Response<String> notifyOrderRefund(QueryOrderRefundDTO request) {
        String retReason;

        OrderRefundTaskPO refundTask = this.baseMapper.selectById(request.getRefundTaskId());
        if (null == refundTask) {
            log.error("查询订单退款任务失败!" + request.getRefundTaskId());
            throw new SysException(ErrorCodeEnum.TASK_NOT_EXIST);
        }
        if (refundTask.getRefundState() != OrderRefundStateEnum.NEW.no) {
            log.error("退款任务已处理!" + request.getRefundTaskId());
            throw new SysException(ErrorCodeEnum.ORDER_TASK_PROCESSED);
        }

        OrderPO orderPO = orderMapper.selectByPrimaryKey(refundTask.getOrderId());
        // 判断订单是否存在
        if (null == orderPO) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }

        //日志
        OrderRefundTaskLogPO refundTaskLog = new OrderRefundTaskLogPO();
        refundTaskLog.setRefundTaskId(refundTask.getId());
        refundTaskLog.setCreatedBy(request.getCreatedBy());
        refundTaskLog.setCreatedDt(new Date());

        // 平安业务独立处理逻辑
        // 非普通退款,走平安接口
        if (null != refundTask.getRefundType() && refundTask.getRefundType() != OrderRefundTypeEnum.REFUND.no) {
            // 处理平安业务处理返回结果
            String resultMsg = orderRefundByPingAn(refundTask, orderPO);
            // resultMsg存在值,说明接口存在问题
            if (null != resultMsg) {
                retReason = resultMsg;
                refundTaskLog.setContent(retReason);
                orderRefundTaskLogMapper.insert(refundTaskLog);
                return Response.error(ErrorCodeEnum.PUSH_FAIL.errorCode, retReason);
            }
            retReason = "通知平安退款-退款申请提交成功";
            refundTask.setRefundState(OrderRefundStateEnum.SUCCESS.no);
            refundTask.setIsPush(RefundTaskPushEnum.PUSH.key);
            refundTask.setUpdatedBy(request.getCreatedBy());
            // 更新退款任务
            this.baseMapper.updateById(refundTask);
            //插入日志
            refundTaskLog.setContent(retReason);
            orderRefundTaskLogMapper.insert(refundTaskLog);
            return Response.success(retReason);
        }

        String path = orderRefundConfig.getOrderRefundPushURL();
        StringBuilder urlBuilder = new StringBuilder(path + "/order/pushOrderRefund");
        urlBuilder.append("?OrderID=").append(orderPO.getOrderCode());
        urlBuilder.append("&CustomerOrderID=").append(orderPO.getChannelOrderCode());
        urlBuilder.append("&DistributorCode=").append(orderPO.getChannelCode());
        urlBuilder.append("&RefundAmout=").append(refundTask.getRefundAmount());
        urlBuilder.append("&TaskID=").append(refundTask.getTaskCode());

        log.info("开始调用接口执行推送订单退款任务，推送请求为：" + urlBuilder.toString());
        String response = "";
        try {
            DefaultHttpClient httpclient = new DefaultHttpClient();
            HttpGet httpGet = new HttpGet(urlBuilder.toString());
            httpclient.getParams().setParameter("http.connection.timeout", 30000);
            httpclient.getParams().setParameter("http.socket.timeout", 30000);
            httpclient.setHttpRequestRetryHandler(new HttpRequestRetryHandler() {
                public boolean retryRequest(IOException exception, int executionCount, HttpContext context) {
                    if (executionCount >= 2) {
                        return false;
                    } else if (exception instanceof InterruptedIOException) {
                        return false;
                    } else if (exception instanceof UnknownHostException) {
                        return false;
                    } else if (exception instanceof ConnectException) {
                        return false;
                    } else if (exception instanceof SSLException) {
                        return false;
                    } else {
                        HttpRequest request = (HttpRequest) context.getAttribute("http.request");
                        return !(request instanceof HttpEntityEnclosingRequest);
                    }
                }
            });
            HttpResponse resp = httpclient.execute(httpGet);
            if (200 == resp.getStatusLine().getStatusCode()) {
                HttpEntity entity = resp.getEntity();
                response = EntityUtils.toString(entity, "UTF-8");
            }
        } catch (Exception e) {
            log.error("调用远程接口发生未知异常", e);
            retReason = "通知退款-调用接口失败,失败原因：接口请求异常";
            refundTaskLog.setContent(retReason);
            orderRefundTaskLogMapper.insert(refundTaskLog);
            return Response.error(ErrorCodeEnum.PUSH_FAIL.errorCode, retReason);
        }

        log.info("操作订单返回结果为：" + response);
        if (StrUtilX.isEmpty(response)) {
            retReason = "通知退款-调用接口失败,失败原因：未知异常";
            refundTaskLog.setContent(retReason);
            orderRefundTaskLogMapper.insert(refundTaskLog);
            return Response.error(ErrorCodeEnum.PUSH_FAIL.errorCode, retReason);
        } else {
            String[] results = response.split("@");
            if (!"0".equals(results[0]) || !"1".equals(results[2])) {
                retReason = "通知退款-调用接口失败,失败原因：未知异常";
                refundTaskLog.setContent(retReason);
                orderRefundTaskLogMapper.insert(refundTaskLog);
                return Response.error(ErrorCodeEnum.PUSH_FAIL.errorCode, retReason);
            }
            retReason = "通知退款-退款申请提交成功";
            refundTask.setRefundState(OrderRefundStateEnum.SUCCESS.no);
            refundTask.setIsPush(RefundTaskPushEnum.PUSH.key);
            refundTask.setUpdatedBy(request.getCreatedBy());
            this.baseMapper.updateById(refundTask);

        }
        refundTaskLog.setContent(retReason);
        orderRefundTaskLogMapper.insert(refundTaskLog);
        return Response.success(retReason);
    }

    @Override
    public Response<String> notifyOrderDeduction(QueryOrderRefundDTO request) {
        String retReason;

        OrderRefundTaskPO refundTask = this.baseMapper.selectById(request.getRefundTaskId());
        if (null == refundTask) {
            log.error("查询订单退款任务失败!" + request.getRefundTaskId());
            throw new SysException(ErrorCodeEnum.TASK_NOT_EXIST);
        }
        if (refundTask.getRefundState() != OrderRefundStateEnum.NEW.no) {
            log.error("退款任务已处理!" + request.getRefundTaskId());
            throw new SysException(ErrorCodeEnum.ORDER_TASK_PROCESSED);
        }

        OrderPO orderPO = orderMapper.selectByPrimaryKey(refundTask.getOrderId());

        //日志
        OrderRefundTaskLogPO refundTaskLog = new OrderRefundTaskLogPO();
        refundTaskLog.setRefundTaskId(refundTask.getId());
        refundTaskLog.setCreatedBy(request.getCreatedBy());
        refundTaskLog.setCreatedDt(new Date());

        //查询密钥以及推送接口地址
        AgentApiConfigDTO apiConfigDTO = orderRefundTaskMapper.queryAgentApiConfigByAgentCode(orderPO.getAgentCode());

        if (Objects.nonNull(apiConfigDTO) && StrUtilX.isEmpty(apiConfigDTO.getWarrantiesNotifyUrl()) || StrUtilX.isEmpty(apiConfigDTO.getSecretKey())) {
            log.info("扣款通知推送接口地址或密钥未配置:{}", JSON.toJSONString(apiConfigDTO));
            retReason = "扣款通知推送接口地址或密钥未配置";
            refundTaskLog.setContent(retReason);
            orderRefundTaskLogMapper.insert(refundTaskLog);
            return Response.error(ErrorCodeEnum.PUSH_FAIL.errorCode, retReason);
        }

        OrderDeductionPushRequest pushRequest = new OrderDeductionPushRequest();
        try {
            pushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
            pushRequest.setFcOrderCode(orderPO.getOrderCode());
            pushRequest.setReqNum(refundTask.getTaskCode());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            pushRequest.setReqTime(sdf.format(new Date()));
            pushRequest.setNoshowAmount(refundTask.getRefundAmount().toString());
            //币种
            pushRequest.setCurrency(SettlementCurrencyEnum.getCodeByKey(refundTask.getCurrency()+""));
            pushRequest.setPartnerCode(apiConfigDTO.getPartnerCode());

            //请求dhub
            Response<Object> orderDeductionPushResponse = disB2BRemote.orderDeductionPush(pushRequest);
            if (orderDeductionPushResponse.getResult() == 1) {
                retReason = "通知扣款-扣款申请提交成功";
                refundTask.setRefundState(OrderRefundStateEnum.SUCCESS.no);
                refundTask.setIsPush(RefundTaskPushEnum.PUSH.key);
                refundTask.setUpdatedBy(request.getCreatedBy());
                this.baseMapper.updateById(refundTask);
            } else {
                retReason = "通知扣款-调用接口失败，" + orderDeductionPushResponse.getFailReason();
                refundTaskLog.setContent(retReason);
                orderRefundTaskLogMapper.insert(refundTaskLog);
                return Response.error(ErrorCodeEnum.PUSH_FAIL.errorCode, retReason);
            }
        } catch (Exception e) {
            log.error("通知扣款推送失败，调用远程接口前系统异常.baseRequest:" + JSON.toJSONString(pushRequest), e);
            retReason = "通知扣款-调用远程接口前异常";
            refundTaskLog.setContent(retReason);
            orderRefundTaskLogMapper.insert(refundTaskLog);
            return Response.error(ErrorCodeEnum.PUSH_FAIL.errorCode, retReason);
        }
        refundTaskLog.setContent(retReason);
        orderRefundTaskLogMapper.insert(refundTaskLog);
        return Response.success(retReason);
    }

    @Override
    @Transactional
    public Response<String> cancelOrderRefundTask(QueryOrderRefundDTO request) {
        String retReason;
        OrderRefundTaskPO refundTask = this.baseMapper.selectById(request.getRefundTaskId());

        //日志
        OrderRefundTaskLogPO refundTaskLog = new OrderRefundTaskLogPO();
        refundTaskLog.setRefundTaskId(refundTask.getId());
        refundTaskLog.setCreatedBy(request.getCreatedBy());
        refundTaskLog.setCreatedDt(new Date());
        try {
            // 已取消的任务，不能重复取消
            if (OrderRefundStateEnum.CANCEL.no == refundTask.getRefundState()) {
                if (refundTask.getTaskType() == 0) {
                    retReason = "撤销退款任务无效!任务已取消";
                } else {
                    retReason = "撤销扣款任务无效!任务已取消";
                }
                refundTaskLog.setContent(retReason);
                orderRefundTaskLogMapper.insert(refundTaskLog);
                return Response.error(ErrorCodeEnum.CANCEL_FAIL.errorCode, retReason);
            }
            //已处理的任务，不可重复处理
            if (refundTask.getRefundState() != OrderRefundStateEnum.NEW.no) {
                log.error("退款任务已处理!" + request.getRefundTaskId());
                return Response.error(ErrorCodeEnum.ORDER_TASK_PROCESSED.errorCode, ErrorCodeEnum.ORDER_TASK_PROCESSED.errorDesc);
            }

            refundTask.setRefundState(OrderRefundStateEnum.CANCEL.no);
            refundTask.setUpdatedBy(request.getCreatedBy());
            refundTask.setUpdatedDt(new Date());

            this.baseMapper.updateById(refundTask);
            if (refundTask.getTaskType() == 0) {
                retReason = "撤销退款任务成功!任务取消成功!";
            } else {
                retReason = "撤销扣款任务成功!任务取消成功!";
            }
            refundTaskLog.setContent(retReason);
            orderRefundTaskLogMapper.insert(refundTaskLog);
        } catch (Exception e) {
            if (refundTask.getTaskType() == 0) {
                retReason = "撤销退款任务失败!失败原因:" + e.getMessage();
            } else {
                retReason = "撤销扣款任务失败!失败原因:" + e.getMessage();
            }
            log.error(retReason, e);
            refundTaskLog.setContent(retReason);
            orderRefundTaskLogMapper.insert(refundTaskLog);
            return Response.error(ErrorCodeEnum.CANCEL_FAIL.errorCode, retReason);
        }
        return Response.success(retReason);
    }

    /**
     * 平安退款业务处理
     *
     * @param refundTask 退款任务
     * @param orderPO    退款订单信息
     * @return 返回对象
     */
    public String orderRefundByPingAn(OrderRefundTaskPO refundTask, OrderPO orderPO) {
        try {
            log.info("平安退款入口--------start-------------");
            //平安退款参数
            JSONObject bizObject = new JSONObject();
            bizObject.put("reqTime", String.valueOf(System.currentTimeMillis()));
            bizObject.put("reqNum", UUID.randomUUID().toString().replaceAll("-", ""));
            PingAnRefundInfo pingAnRefundInfo = new PingAnRefundInfo();

            //平安订单号orderId  对应加力天宫 channelOrderCode
            pingAnRefundInfo.setOrderId(orderPO.getChannelOrderCode());

            //退款类型  refundType
            Integer refundType = refundTask.getRefundType();
            pingAnRefundInfo.setRefundType(OrderRefundTypeEnum.getCode(refundTask.getRefundType()));

            //渠道退款流水号  channelRefundId
            pingAnRefundInfo.setChannelRefundId(refundTask.getTaskCode());

            //判断普通退款,需要退款明细
            if (refundType == OrderRefundTypeEnum.COMMON.no) {
                pingAnRefundInfo.setRefundMode(RefundModeEnum.DAILY.key);
                //获取日期退款详细
                String dailyRefundList = refundTask.getDailyRefundInfo();
                List<OrderDailyRefundInfo> list = JSON.parseObject(dailyRefundList, new TypeReference<List<OrderDailyRefundInfo>>() {
                });
                pingAnRefundInfo.setDailyRefundList(list);
            } else {
                //其他类型都是按订单退款
                pingAnRefundInfo.setRefundMode(RefundModeEnum.ORDER.key);
            }

            //罚金
            if (refundType == OrderRefundTypeEnum.FINE.no) {
                String fineAmt = String.valueOf(refundTask.getRefundAmount());
                pingAnRefundInfo.setFineAmt(fineAmt);
            }

            //组装参数
            String requestStr = getPingAnRequestStr(bizObject, pingAnRefundInfo);
            //请求调用平安接口

            return orderRefundByPingAn(requestStr);
        } catch (Exception e) {
            log.error("调用平安接口异常：" + e.getMessage() + "," + e);
            return "调用平安接口异常：" + e.getMessage();
        }
    }

    /**
     * 参数组装
     */
    private String getPingAnRequestStr(JSONObject bizObject, PingAnRefundInfo pingAnRefundInfoVO) {
        //参数拼接
        String request = JSON.toJSONString(pingAnRefundInfoVO);

        bizObject.put("bizContent", request);
        //参数加密,并赋值bizSign
        bizObject.put("bizSign", PingAnRsaUtil.sign(orderRefundConfig.getHanglvShaPrivateKey(), request));

        //处理入参参数
        return bizObject.toJSONString();
    }

    /**
     * 请求调用平安接口
     */
    private String orderRefundByPingAn(String requestStr) {
        //调用平安接口
        String responseStr = HttpUtilX.post(orderRefundConfig.getHanglvUrl(), requestStr);
        log.info("调用平安接口，参数：{" + requestStr + "}，返回：{" + responseStr + "}");

        if (StrUtilX.isNotEmpty(responseStr)) {
            Map<String, String> responseMap = JSON.parseObject(responseStr, new TypeReference<Map<String, String>>() {
            });

            //【异常流程】请求接口，异常处理，返回数据判断
            if (null == responseMap.get("respCode") || !responseMap.get("respCode").equals("000000")) {
                //异常直接返回错误信息
                return "通知平安退款-调用接口失败,失败原因：" + responseMap.get("respCode");
            }

            //解析成功返回的内容信息
            String biz = responseMap.get("bizContent");
            Map<String, String> bizMap = JSON.parseObject(biz, new TypeReference<Map<String, String>>() {
            });
            //【异常流程】业务数据返回结果respCode如果不为000000,则也算失败
            if (null == bizMap.get("respCode") || !bizMap.get("respCode").equals("000000")) {
                //异常直接返回错误信息
                return "通知平安退款-调用接口失败,失败原因：" + bizMap.get("respCode") + "," + bizMap.get("respMsg");
            }
        }
        return null;
    }

}
