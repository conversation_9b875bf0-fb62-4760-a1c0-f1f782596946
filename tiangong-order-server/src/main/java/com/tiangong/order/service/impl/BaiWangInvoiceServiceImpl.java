package com.tiangong.order.service.impl;

import cn.hutool.json.JSONUtil;
import com.baiwang.open.client.BWRestClient;
import com.baiwang.open.client.IBWClient;
import com.baiwang.open.client.login.BopLoginClient;
import com.baiwang.open.client.login.BopLoginResponse;
import com.baiwang.open.client.login.PasswordLoginClient;
import com.baiwang.open.client.login.PasswordLoginConfig;
import com.baiwang.open.entity.request.OutputEinvoiceQueryRequest;
import com.baiwang.open.entity.request.OutputFormatQueryQdInvoiceRequest;
import com.baiwang.open.entity.request.OutputInvoiceIssueRequest;
import com.baiwang.open.entity.response.OutputEinvoiceQueryResponse;
import com.baiwang.open.entity.response.OutputFormatQueryQdInvoiceResponse;
import com.baiwang.open.entity.response.OutputInvoiceIssueResponse;
import com.baiwang.open.entity.response.node.OutputInvoiceIssueInvoiceResult;
import com.baiwang.open.exception.BWOpenException;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.config.SettingsConstant;
import com.tiangong.order.service.BaiWangInvoiceService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 百望云发票服务实现类
 */
@Service
@Slf4j
public class BaiWangInvoiceServiceImpl implements BaiWangInvoiceService {
    @Autowired
    private SettingsConstant settingsConstant;

    /**
     * token缓存时间（秒）
     * 百望云token有效期为8小时，这里设置为6小时，提前1小时刷新
     */
    private static final long TOKEN_EXPIRE_TIME = 6 * 60 * 60;

    String getBaiWangToken(String requestId) {
        return getBaiWangToken(requestId, false);
    }

    private String getBaiWangToken(String requestId, boolean regen) {
        String stringBuilder = settingsConstant.getBaiwangUrl() +
                settingsConstant.getBaiwangUsername() +
                settingsConstant.getBaiwangPassword() +
                settingsConstant.getBaiwangAppKey() +
                settingsConstant.getBaiwangAppSecret() +
                settingsConstant.getBaiwangSalt() +
                settingsConstant.getBaiwangTaxNo();
        String suffix = Md5Util.md5Encode(stringBuilder);
        // 先从Redis获取token
        String key = RedisKey.BAIWANG_TOKEN + suffix;
        String token = RedisTemplateX.get(key);

        // 如果Redis中存在且不为空，直接返回
        if (StringUtils.isNotBlank(token) && !regen) {
            log.info("从Redis缓存获取百望云token成功 requestId:{} token:{}", requestId, token);
            return token;
        }

        // Redis中不存在，重新获取token
        log.info("Redis中不存在百望云token或已过期，重新获取requestId:{}", requestId);
        try {
            token = getBaiWangTokenReal(requestId);
            // 将token存入Redis，设置过期时间
            if (StringUtils.isNotBlank(token)) {
                RedisTemplateX.setAndExpire(key, token, TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                log.info("获取百望云token成功，已缓存到Redis，过期时间：{}秒 requestId:{}", TOKEN_EXPIRE_TIME, requestId);
            } else {
                log.error("获取百望云token失败，返回token为空 requestId:{}", requestId);
            }
            return token;
        } catch (BWOpenException e) {
            log.error("获取百望云token异常，错误码：{}，错误信息：{}，子错误码：{}，子错误信息：{} requestId:{}",
                    e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage(), requestId);
            throw e;
        }
    }

    @Override
    public OutputFormatQueryQdInvoiceResponse queryQdInvoice(OutputFormatQueryQdInvoiceRequest request, String requestId) {
        request.setTaxNo(settingsConstant.getBaiwangTaxNo());
        log.info("调用百望云全版查询接口请求参数:{} requestId:{}", JSONUtil.toJsonStr(request), requestId);
        if (settingsConstant.getBaiwangMockDownLoadPdf()) {
            return mockQueryQdInvoice();
        }
        String token = getBaiWangToken(requestId);
        try {
            IBWClient client = getIbwClient();
            OutputFormatQueryQdInvoiceResponse response = retryHandler(() -> client.outputFormat().queryQdInvoice(request, token), requestId);
            log.info("调用百望云全版查询接口请求响应:{} requestId:{}", JSONUtil.toJsonStr(response), requestId);
            return response;
        } catch (Exception e) {
            log.error("调用百望云全版查询接口异常 requestId:{}", requestId, e);
            throw e;
        }
    }

    private OutputFormatQueryQdInvoiceResponse mockQueryQdInvoice() {
        if (settingsConstant.getBaiwangMockDownLoadPdfFail()) {
            return null;
        }
        OutputFormatQueryQdInvoiceResponse bean = JSONUtil.toBean("{\"response\":{\"pdfUrl\":\"http://fp.baiwang.com/format/d?d=555672B202579A69371944025A7420E66F2439DFFB6EED1E55C809049A7EF0CB\",\"ofdUrl\":\"http://fp.baiwang.com/format/d?d=555672B202579A69371944025A7420E6416BD3B92DBD690C02D0A74D5E697F23\",\"xmlUrl\":\"http://fp.baiwang.com/format/d?d=555672B202579A69371944025A7420E649FE22BAA844C8CBAFB23ACB474FB579\"},\"success\":true,\"method\":\"baiwang.output.format.queryQdInvoice\",\"requestId\":\"c77cd54d-7c48-4301-bddd-a1e50da76dd8\"}\n",
                OutputFormatQueryQdInvoiceResponse.class);
        return bean;
    }

    @Override
    public OutputInvoiceIssueResponse invoiceOpen(OutputInvoiceIssueRequest request, String requestId) {
        request.setTaxNo(settingsConstant.getBaiwangTaxNo());
        log.info("调用百望云开票接口请求参数:{} requestId:{}", JSONUtil.toJsonStr(request), requestId);
        if (settingsConstant.getBaiwangMockIssue()) {
            return mockInvoiceOpen(request, requestId);
        }
        String token = getBaiWangToken(requestId);
        try {
            IBWClient client = getIbwClient();
            OutputInvoiceIssueResponse response = retryHandler(() -> client.outputInvoice().issue(request, token), requestId);
            log.info("调用百望云开票接口请求响应:{} requestId:{}", response, requestId);
            return response;
        } catch (Exception e) {
            log.error("调用百望云开票接口异常 requestId:{}", requestId, e);
            throw e;
        }
    }

    private OutputInvoiceIssueResponse mockInvoiceOpen(OutputInvoiceIssueRequest request, String requestId) {
        if (settingsConstant.getBaiwangMockIssueFail()) {
            return JSONUtil.toBean("{\"invoiceTypeCode\":\"026\",\"serialNo\":\"1920681426113114113\",\"buyerTaxNo\":\"440582199703192032\",\"buyerName\":\"郑创楷\",\"buyerAddressPhone\":\"\",\"buyerBankAccount\":\"\",\"invoiceTotalPriceTax\":1.00,\"invoiceDetailsList\":[{\"goodsLineNo\":1,\"goodsName\":\"*旅游服务*代订房费\",\"goodsTotalPrice\":1.00,\"goodsTaxRate\":0}]}} >> 响应报文:{\"method\":\"baiwang.output.invoice.issue\",\"requestId\":\"a1f0921b-2891-4a14-87f2-c63cc0619e6e\",\"errorResponse\":{\"code\":100006,\"message\":\"远程服务错误\",\"subCode\":\"20023\",\"subMessage\":\"未获取到电票开票点信息！\"},\"success\":false}",
                    OutputInvoiceIssueResponse.class);
        }
        String invoiceTypeCode = request.getData().getInvoiceTypeCode();
        if (invoiceTypeCode.equals("01")) {
            OutputInvoiceIssueResponse bean = JSONUtil.toBean("{\"success\":true,\"method\":\"baiwang.output.invoice.issue\",\"requestId\":\"7b84002d-a81d-48a8-a5d1-bc6c63786fac\",\"response\":{\"success\":[{\"invoiceNo\":\"25942000000019621210\",\"invoiceDate\":\"********163518\",\"invoiceQrCode\":\"01,31,,25942000000019621210,2.12,********,,BCE5,\",\"invoiceTypeCode\":\"01\",\"serialNo\":\"1920759292024705025\",\"invoiceTotalPrice\":2.00,\"invoiceTotalPriceTax\":2.12,\"invoiceTotalTax\":0.12,\"mulPurchaserMark\":\"N\",\"invoiceDetailsList\":[{\"goodsLineNo\":1,\"goodsName\":\"*旅游服务*住宿费\",\"goodsCode\":\"307030100000000000001\",\"goodsTotalPrice\":2.00,\"goodsTaxRate\":0.06,\"goodsTotalTax\":0.12}]}]}}"
                    , OutputInvoiceIssueResponse.class);
            //随意生成一个发票号
            OutputInvoiceIssueInvoiceResult outputInvoiceIssueInvoiceResult = bean.getResponse().getSuccess().get(0);
            outputInvoiceIssueInvoiceResult.setInvoiceNo(IdWorker.getIdStr());
            //计算未税金额
            BigDecimal goodsTaxRate = request.getData().getInvoiceDetailsList().get(0).getGoodsTaxRate();
            BigDecimal goodsTotalPrice = request.getData().getInvoiceDetailsList().get(0).getGoodsTotalPrice();
            outputInvoiceIssueInvoiceResult.setInvoiceTotalTax(goodsTotalPrice.multiply(goodsTaxRate).setScale(2,BigDecimal.ROUND_HALF_DOWN));
            return bean;
        }
        if (invoiceTypeCode.equals("02")) {
            OutputInvoiceIssueResponse bean = JSONUtil.toBean("{\"success\":true,\"method\":\"baiwang.output.invoice.issue\",\"requestId\":\"c52f7080-f8f3-4977-8c90-412ce0620198\",\"response\":{\"success\":[{\"invoiceNo\":\"25942000000019568137\",\"invoiceDate\":\"********152043\",\"invoiceQrCode\":\"01,32,,25942000000019568137,1.00,********,,9FF2,\",\"invoiceTypeCode\":\"02\",\"serialNo\":\"1920740677749673986\",\"invoiceTotalPrice\":1.00,\"invoiceTotalPriceTax\":1.00,\"invoiceTotalTax\":0.00,\"mulPurchaserMark\":\"N\",\"invoiceDetailsList\":[{\"goodsLineNo\":1,\"goodsName\":\"*旅游服务*住宿费\",\"goodsCode\":\"307030100000000000001\",\"goodsTotalPrice\":1.00,\"goodsTaxRate\":0,\"goodsTotalTax\":0.00}]}]}}\n"
                    , OutputInvoiceIssueResponse.class);
            OutputInvoiceIssueInvoiceResult outputInvoiceIssueInvoiceResult = bean.getResponse().getSuccess().get(0);
            outputInvoiceIssueInvoiceResult.setInvoiceNo(IdWorker.getIdStr());
            //计算未税金额
            BigDecimal goodsTaxRate = request.getData().getInvoiceDetailsList().get(0).getGoodsTaxRate();
            BigDecimal goodsTotalPrice = request.getData().getInvoiceDetailsList().get(0).getGoodsTotalPrice();
            outputInvoiceIssueInvoiceResult.setInvoiceTotalTax(goodsTotalPrice.multiply(goodsTaxRate).setScale(2,BigDecimal.ROUND_HALF_DOWN));
            return bean;
        }
        return null;
    }

    @Override
    public OutputEinvoiceQueryResponse getInvoiceDetail(OutputEinvoiceQueryRequest request, String requestId) {
        request.setTaxNo(settingsConstant.getBaiwangTaxNo());
        log.info("调用百望云查询发票接口请求参数：{} requestId:{}", JSONUtil.toJsonStr(request), requestId);
        if (settingsConstant.getBaiwangMockIssue()) {
            return mockInvoiceDetail(request);
        }

        String token = getBaiWangToken(requestId);
        try {
            IBWClient client = getIbwClient();
            OutputEinvoiceQueryResponse response = retryHandler(() -> client.outputEinvoice().query(request, token), requestId);
            log.info("调用百望云查询发票接口响应结果：{} requestId:{}", JSONUtil.toJsonStr(response), requestId);
            return response;
        } catch (Exception e) {
            log.error("调用百望云查询发票接口异常requestId:{}", requestId, e);
            throw e;
        }
    }

    private OutputEinvoiceQueryResponse mockInvoiceDetail(OutputEinvoiceQueryRequest request) {
        OutputEinvoiceQueryResponse outputEinvoiceQueryResponse = new OutputEinvoiceQueryResponse();
        outputEinvoiceQueryResponse.setSuccess(true);
        outputEinvoiceQueryResponse.setResponse(Collections.emptyList());
        return outputEinvoiceQueryResponse;
    }

    private IBWClient getIbwClient() {
        return new BWRestClient(settingsConstant.getBaiwangUrl(), settingsConstant.getBaiwangAppKey(), settingsConstant.getBaiwangAppSecret()); // 初始化一个客户端
    }

    private String getBaiWangTokenReal(String requestId) throws BWOpenException {
        String token;
        // 构建登录配置
        PasswordLoginConfig loginConfig = new PasswordLoginConfig();
        loginConfig.setUrl(settingsConstant.getBaiwangUrl());
        loginConfig.setClientId(settingsConstant.getBaiwangAppKey());
        loginConfig.setClientSecret(settingsConstant.getBaiwangAppSecret());
        loginConfig.setUsername(settingsConstant.getBaiwangUsername());
        loginConfig.setPassword(settingsConstant.getBaiwangPassword());
        loginConfig.setUserSalt(settingsConstant.getBaiwangSalt());
        // 创建登录客户端
        BopLoginClient loginClient = new PasswordLoginClient(loginConfig);
        log.info("调用百望token requestId:{}请求体{}", requestId, JSONUtil.toJsonStr(loginConfig));
        // 获取token
        BopLoginResponse loginResponse = loginClient.login();
        log.info("调用百望token requestId:{}响应体{}", requestId, loginResponse);
        token = loginResponse.getResponse().getAccessToken();
        return token;
    }

    private <T> T retryHandler(Supplier<T> supplier, String requestId) {
        boolean retry = false;
        try {
            return supplier.get();
        } catch (Exception e) {
            //如果是token失效，则重新获取token 其他抛出异常
            if (e instanceof BWOpenException && ((BWOpenException) e).getCode().equals("100002")) {
                log.info("百望token失效，重新获取token");
                retry = true;
            } else {
                throw e;
            }
        }
        //重新获取token 并调用一次
        getBaiWangToken(requestId, true);
        log.info("百望token失效，重新获取token成功");
        return supplier.get();
    }
}
