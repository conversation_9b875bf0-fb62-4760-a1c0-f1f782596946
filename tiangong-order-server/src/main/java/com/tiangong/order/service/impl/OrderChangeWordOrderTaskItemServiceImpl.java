package com.tiangong.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.order.domain.OrderChangeWordOrderTaskItemPO;
import com.tiangong.order.mapper.OrderChangeWordOrderTaskItemMapper;
import com.tiangong.order.service.OrderChangeWordOrderTaskItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderChangeWordOrderTaskItemServiceImpl extends ServiceImpl<OrderChangeWordOrderTaskItemMapper, OrderChangeWordOrderTaskItemPO> implements OrderChangeWordOrderTaskItemService {

}