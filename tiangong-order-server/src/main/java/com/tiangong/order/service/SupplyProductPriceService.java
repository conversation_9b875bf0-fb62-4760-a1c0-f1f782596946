package com.tiangong.order.service;

import com.tiangong.order.remote.request.SupplyOrderInfoListDTO;
import com.tiangong.order.remote.response.SupplyProductDailyPriceDTO;

import java.util.List;

/**
 * 供应商产品每日价格服务
 * <AUTHOR>
 */
public interface SupplyProductPriceService{

    /**
     * 查询供应商每日价格明细
     * @param request 供货单ids
     * @return 供应商每日价格明细
     */
    List<SupplyProductDailyPriceDTO> querySupplyOrderPriceList(SupplyOrderInfoListDTO request);
}
