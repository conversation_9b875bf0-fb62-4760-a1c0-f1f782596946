package com.tiangong.order.service;

import com.tiangong.common.Response;
import com.tiangong.order.remote.request.CancelSupplyOrderWorkOrderDTO;
import com.tiangong.order.remote.request.ConfirmSupplyOrderWorkOrderDTO;

public interface SupplyOrderFinanceHandle {

    /**
     * 确认工单
     */
    Response<Object> confirmSupplyOrderWorkOrder(ConfirmSupplyOrderWorkOrderDTO request);

    /**
     * 取消工单
     */
    Response<Object> cancelSupplyOrderWorkOrder(CancelSupplyOrderWorkOrderDTO request);
}
