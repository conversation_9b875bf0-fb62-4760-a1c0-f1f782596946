package com.tiangong.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.order.response.OrderRoomDetailDto;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.SupplyHotelIncrease;
import com.tiangong.dto.product.SupplyIncrease;
import com.tiangong.dto.product.TaxDetailDto;
import com.tiangong.enums.SaleAdjustmentTypeEnum;
import com.tiangong.finance.OrgDTO;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.product.dto.ProductSalePriceDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 用途：TODO
 *
 * @author：Owen
 */
@Slf4j
@Service("increaseUtil")
public class IncreaseUtil {

    /**
     * 计算分销商加幅
     */
    public void agentIncrease(OrderRoomDetailDto roomItemDetail, AgentAccountConfig agentAccountConfig, SupplierAddDTO supplierIncreaseConfig, SupplyIncrease agentSupplyIncrease, SupplyHotelIncrease supplyHotelIncrease, String checkInDate) {
        try {
            // 客户不存在直接结束
            if (!Objects.nonNull(agentAccountConfig)) {
                return;
            }
            Integer settlementCurrency;// 结算币种
            if (Objects.nonNull(agentAccountConfig.getSettlementCurrency())) {
                settlementCurrency = agentAccountConfig.getSettlementCurrency();
            } else {
                settlementCurrency = Integer.valueOf(CompanyDTO.DEFAULT_COIN_KEY);
            }
            // 获取商家币种
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            // 查询汇率
            BigDecimal rate = BigDecimal.ONE;
            BigDecimal payInStoreRate = null;
            if (CollUtilX.isNotEmpty(roomItemDetail.getPriceInfoDetails())) {
                PriceInfoDetail priceInfoDetail = roomItemDetail.getPriceInfoDetails().get(0);
                rate = RedisUtil.getRateToTargetCurrency(priceInfoDetail.getBaseCurrency(), CompanyDTO.COMPANY_CODE, Integer.parseInt(orgDTO.getOrgCurrency()));
            }
            // 商家转客户汇率
            int orgCurrency = Integer.parseInt(orgDTO.getOrgCurrency());
            BigDecimal orgToAgentRate = RedisUtil.getRateToTargetCurrency(orgCurrency, CompanyDTO.COMPANY_CODE, agentAccountConfig.getSettlementCurrency());
            // 判断币种是否一致
            if (agentAccountConfig.getSettlementCurrency().equals(roomItemDetail.getSupplyPayInStoreCurrency())) {
                roomItemDetail.setPayInStorePriceAgentCurrencyAmt(roomItemDetail.getSupplyPayInStorePrice());
            } else {
                if (roomItemDetail.getPayInStoreCurrency() != null && !roomItemDetail.getPayInStoreCurrency().equals(settlementCurrency)) {
                    payInStoreRate = RedisUtil.getRateToTargetCurrency(roomItemDetail.getPayInStoreCurrency(), CompanyDTO.COMPANY_CODE, Integer.parseInt(orgDTO.getOrgCurrency()));
                }
                // 计算到店另付费用转客户币种
                if (roomItemDetail.getPayInStorePrice() != null) {
                    BigDecimal payInStorePriceAgentCurrencyAmt;
                    if (payInStoreRate != null) {
                        payInStorePriceAgentCurrencyAmt = CommonTgUtils.setScale(roomItemDetail.getPayInStorePrice().multiply(payInStoreRate).multiply(orgToAgentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                    } else {
                        payInStorePriceAgentCurrencyAmt = CommonTgUtils.setScale(roomItemDetail.getPayInStorePrice(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                        ;
                    }
                    roomItemDetail.setPayInStorePriceAgentCurrencyAmt(payInStorePriceAgentCurrencyAmt);
                }
            }

            // 遍历价格
            for (PriceInfoDetail priceItem : roomItemDetail.getPriceInfoDetails()) {
                // 设置结算币种
                priceItem.setSettlementCurrency(settlementCurrency);
                // 每次税费是单独计算
                if (priceItem.getDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto taxDetail = roomItemDetail.getTaxDetail();
                    // 设置供应商价格，入库时用
                    taxDetail.setSupplyRoomPrice(taxDetail.getRoomPrice());
                    taxDetail.setSupplySalesTax(taxDetail.getSalesTax());
                    taxDetail.setSupplyTaxFee(taxDetail.getTaxFee());
                    taxDetail.setSupplyOtherTax(taxDetail.getOtherTax());

                    // 币种不一样
                    if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                        // 转换汇率 供应商转商家
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                    }
                    // 币种不一样
                    if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                        // 转换汇率 商家转客户
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                    }

                    // 统一进位
                    taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }

                // 每日信息
                TaxDetailDto taxDetail = priceItem.getTaxDetail();

                // 设置供应商价格，入库时用
                taxDetail.setSupplyRoomPrice(taxDetail.getRoomPrice());
                taxDetail.setSupplySalesTax(taxDetail.getSalesTax());
                taxDetail.setSupplyTaxFee(taxDetail.getTaxFee());
                taxDetail.setSupplyOtherTax(taxDetail.getOtherTax());
                // 计算底价
                priceItem.setBasePrice(BigDecimal.ZERO.add(CommonTgUtils.formatBigDecimal(taxDetail.getTaxFee())).
                        add(CommonTgUtils.formatBigDecimal(taxDetail.getSalesTax())).
                        add(CommonTgUtils.formatBigDecimal(taxDetail.getOtherTax())).
                        add(CommonTgUtils.formatBigDecimal(taxDetail.getRoomPrice())));

                // 币种不一样
                if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                    // 转换汇率
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), rate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                }

                // 底价
                BigDecimal basePrice = BigDecimal.ZERO;
                basePrice = basePrice.add(CommonTgUtils.formatBigDecimal(taxDetail.getRoomPrice()));

                // 供应商加幅值
                BigDecimal baseSupplierAdjustment = BigDecimal.ZERO;
                if (Objects.nonNull(supplierIncreaseConfig)) {
                    baseSupplierAdjustment = CommonTgUtils.adjustmentAmt(basePrice, supplierIncreaseConfig.getAdjustmentType(), supplierIncreaseConfig.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (supplierIncreaseConfig.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, supplierIncreaseConfig.getAdjustmentType())
                            && supplierIncreaseConfig.getLowestIncreaseAmt() != null && baseSupplierAdjustment.compareTo(supplierIncreaseConfig.getLowestIncreaseAmt()) < 0) {
                        baseSupplierAdjustment = supplierIncreaseConfig.getLowestIncreaseAmt();
                    }
                }

                // 分销商-供应商-酒店加幅值
                BigDecimal baseAgentHotelAdjustment = BigDecimal.ZERO;
                if(supplyHotelIncrease != null){
                    // 优先酒店级别加幅
                    // 供应商加幅后的价格
                    BigDecimal supplierPrice = baseSupplierAdjustment.add(taxDetail.getRoomPrice());
                    baseAgentHotelAdjustment = CommonTgUtils.adjustmentAmt(supplierPrice, supplyHotelIncrease.getAdjustmentType(), supplyHotelIncrease.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (supplyHotelIncrease.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, supplyHotelIncrease.getAdjustmentType())
                            && supplyHotelIncrease.getLowestIncreaseAmt() != null && baseAgentHotelAdjustment.compareTo(supplyHotelIncrease.getLowestIncreaseAmt()) < 0) {
                        baseAgentHotelAdjustment = supplyHotelIncrease.getLowestIncreaseAmt();
                    }
                } else if (agentSupplyIncrease != null) {
                    // 分销商级别加幅
                    // 供应商加幅后的价格
                    BigDecimal supplierPrice = baseSupplierAdjustment.add(taxDetail.getRoomPrice());
                    baseAgentHotelAdjustment = CommonTgUtils.adjustmentAmt(supplierPrice, agentSupplyIncrease.getAdjustmentType(), agentSupplyIncrease.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (agentSupplyIncrease.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, agentSupplyIncrease.getAdjustmentType())
                            && agentSupplyIncrease.getLowestIncreaseAmt() != null && baseAgentHotelAdjustment.compareTo(agentSupplyIncrease.getLowestIncreaseAmt()) < 0) {
                        baseAgentHotelAdjustment = agentSupplyIncrease.getLowestIncreaseAmt();
                    }
                }

                // 折扣
                BigDecimal discount = BigDecimal.ZERO;
                BigDecimal supplyDiscount = BigDecimal.ZERO;

                // 其他税费
                BigDecimal otherTax = CommonTgUtils.formatBigDecimal(taxDetail.getOtherTax());

                if (baseSupplierAdjustment.compareTo(BigDecimal.ZERO) < 0) {
                    discount = discount.add(baseSupplierAdjustment);
                } else {
                    otherTax = otherTax.add(baseSupplierAdjustment);
                }

                if (baseAgentHotelAdjustment.compareTo(BigDecimal.ZERO) < 0) {
                    discount = discount.add(baseAgentHotelAdjustment);
                } else {
                    otherTax = otherTax.add(baseAgentHotelAdjustment);
                }

                // 其他税 = 其他税 + 供应商加幅值 + 分销商加幅值 (加幅金额算到其他税里面)
                taxDetail.setOtherTax(otherTax);
                taxDetail.setDiscount(discount);
                taxDetail.setSupplyDiscount(supplyDiscount);

                // 币种不一样
                if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                    // 转换汇率 商家转客户
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), orgToAgentRate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                    taxDetail.setDiscount(CommonTgUtils.setRate(taxDetail.getDiscount(), orgToAgentRate));
                }

                // 统一进位
                taxDetail.setRoomPrice(CommonTgUtils.setScale(taxDetail.getRoomPrice(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setDiscount(CommonTgUtils.setScale(taxDetail.getDiscount(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));

                // 售价 = 房费 + 税费(销售税、税费、其他税)
                priceItem.setSalePrice(taxDetail.getRoomPrice().add(taxDetail.getSalesTax()).add(taxDetail.getTaxFee()).add(taxDetail.getOtherTax()).add(taxDetail.getDiscount()));
                // 按次税费，计算在首日价格里
                if (priceItem.getDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto roomTaxDetail = roomItemDetail.getTaxDetail();
                    priceItem.setSalePrice(priceItem.getSalePrice().add(roomTaxDetail.getSalesTax()).add(roomTaxDetail.getTaxFee()).add(roomTaxDetail.getOtherTax()));
                    priceItem.setBasePrice(priceItem.getBasePrice().add(roomTaxDetail.getSupplySalesTax()).add(roomTaxDetail.getSupplyTaxFee()).add(roomTaxDetail.getSupplyOtherTax()));
                }
            }
            log.info("计算分销商加幅时结束的数据是----：{}", roomItemDetail);
        } catch (Exception e) {
            log.error("agentIncrease error!", e);
        }

    }

    /**
     * 计算分销商加幅-现付
     */
    public void agentIncrease(OrderRoomDetailDto roomItemDetail, AgentAccountConfig agentAccountConfig, String checkInDate) {
        try {
            // 客户不存在直接结束
            if (!Objects.nonNull(agentAccountConfig)) {
                return;
            }
            Integer settlementCurrency;// 结算币种
            if (Objects.nonNull(agentAccountConfig.getSettlementCurrency())) {
                settlementCurrency = agentAccountConfig.getSettlementCurrency();
            } else {
                settlementCurrency = Integer.valueOf(CompanyDTO.DEFAULT_COIN_KEY);
            }
            // 获取商家币种
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            // 查询汇率
            BigDecimal rate = BigDecimal.ONE;
            BigDecimal payInStoreRate = BigDecimal.ONE;
            if (CollUtilX.isNotEmpty(roomItemDetail.getPriceInfoDetails())) {
                PriceInfoDetail priceInfoDetail = roomItemDetail.getPriceInfoDetails().get(0);
                rate = RedisUtil.getRateToTargetCurrency(priceInfoDetail.getBaseCurrency(), CompanyDTO.COMPANY_CODE, Integer.parseInt(orgDTO.getOrgCurrency()));
            }
            if (roomItemDetail.getPayInStoreCurrency() != null) {
                payInStoreRate = RedisUtil.getRateToTargetCurrency(roomItemDetail.getPayInStoreCurrency(), CompanyDTO.COMPANY_CODE, Integer.parseInt(orgDTO.getOrgCurrency()));
            }
            // 商家转客户汇率
            int orgCurrency = Integer.parseInt(orgDTO.getOrgCurrency());
            BigDecimal orgToAgentRate = RedisUtil.getRateToTargetCurrency(orgCurrency, CompanyDTO.COMPANY_CODE, agentAccountConfig.getSettlementCurrency());
            // 计算到店另付费用转客户币种
            if (roomItemDetail.getPayInStorePrice() != null) {
                BigDecimal payInStorePriceAgentCurrencyAmt = CommonTgUtils.setScale(roomItemDetail.getPayInStorePrice().multiply(payInStoreRate).multiply(orgToAgentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                roomItemDetail.setPayInStorePriceAgentCurrencyAmt(payInStorePriceAgentCurrencyAmt);
            }
            for (PriceInfoDetail priceItem : roomItemDetail.getPriceInfoDetails()) {
                // 设置结算币种
                priceItem.setSettlementCurrency(settlementCurrency);
                // 每次税费是单独计算
                if (priceItem.getDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto taxDetail = roomItemDetail.getTaxDetail();
                    // 设置供应商价格，入库时用
                    taxDetail.setSupplyRoomPrice(taxDetail.getRoomPrice());
                    taxDetail.setSupplySalesTax(taxDetail.getSalesTax());
                    taxDetail.setSupplyTaxFee(taxDetail.getTaxFee());
                    taxDetail.setSupplyOtherTax(taxDetail.getOtherTax());
                    taxDetail.setDiscount(BigDecimal.ZERO);

                    // 币种不一样
                    if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                        // 转换汇率 供应商转商家
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                    }
                    // 币种不一样
                    if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                        // 转换汇率 商家转客户
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                    }

                    // 统一进位
                    taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }

                TaxDetailDto taxDetail = priceItem.getTaxDetail();
                // 设置供应商价格，入库时用
                taxDetail.setSupplyRoomPrice(taxDetail.getRoomPrice());
                taxDetail.setSupplySalesTax(taxDetail.getSalesTax());
                taxDetail.setSupplyTaxFee(taxDetail.getTaxFee());
                taxDetail.setSupplyOtherTax(taxDetail.getOtherTax());

                // 币种不一样
                if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                    // 转换汇率 供应商转商家
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), rate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                }
                // 币种不一样
                if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                    // 转换汇率 商家转客户
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), orgToAgentRate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                }

                // 统一进位
                taxDetail.setRoomPrice(CommonTgUtils.setScale(taxDetail.getRoomPrice(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));

                // 售价 = 房费 + 税费(销售税、税费、其他税)
                priceItem.setSalePrice(taxDetail.getRoomPrice().add(taxDetail.getSalesTax()).add(taxDetail.getTaxFee()).add(taxDetail.getOtherTax()));
                // 按次税费，计算在首日价格里
                if (priceItem.getDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto roomTaxDetail = roomItemDetail.getTaxDetail();
                    priceItem.setSalePrice(priceItem.getSalePrice().add(roomTaxDetail.getSalesTax()).add(roomTaxDetail.getTaxFee()).add(roomTaxDetail.getOtherTax()));
                }
            }
        } catch (Exception e) {
            log.error("agentIncrease error!", e);
        }
    }

    /**
     * 封装每日售价
     */
    public void assemblyDaysSalePrice(ProductSalePriceDTO productSalePriceDTO, AgentAccountConfig agentAccountConfig) {
        try {
            Map<String, List<PriceInfoDetail>> salePriceMap = new HashMap<>();
            TaxDetailDto totalTax = new TaxDetailDto();//产品级别税费
            for (OrderRoomDetailDto roomItemDetail : productSalePriceDTO.getRoomItemDetails()) {
                if (null != roomItemDetail.getTaxDetail()) {//按次税费
                    totalTax.setSalesTax(CommonTgUtils.formatBigDecimal(totalTax.getSalesTax()).add(CommonTgUtils.formatBigDecimal(roomItemDetail.getTaxDetail().getSalesTax())));
                    totalTax.setTaxFee(CommonTgUtils.formatBigDecimal(totalTax.getTaxFee()).add(CommonTgUtils.formatBigDecimal(roomItemDetail.getTaxDetail().getTaxFee())));
                    totalTax.setOtherTax(CommonTgUtils.formatBigDecimal(totalTax.getOtherTax()).add(CommonTgUtils.formatBigDecimal(roomItemDetail.getTaxDetail().getOtherTax())));
                }
                for (PriceInfoDetail priceItem : roomItemDetail.getPriceInfoDetails()) {
                    if (null == salePriceMap.get(priceItem.getDate())) {
                        List<PriceInfoDetail> priceItems = new ArrayList<>();
                        priceItems.add(priceItem);
                        salePriceMap.put(priceItem.getDate(), priceItems);
                    } else {
                        salePriceMap.get(priceItem.getDate()).add(priceItem);
                    }

                    if (null != priceItem.getTaxDetail()) {//每日税费
                        totalTax.setSalesTax(CommonTgUtils.formatBigDecimal(totalTax.getSalesTax()).add(CommonTgUtils.formatBigDecimal(priceItem.getTaxDetail().getSalesTax())));
                        totalTax.setTaxFee(CommonTgUtils.formatBigDecimal(totalTax.getTaxFee()).add(CommonTgUtils.formatBigDecimal(priceItem.getTaxDetail().getTaxFee())));
                        totalTax.setOtherTax(CommonTgUtils.formatBigDecimal(totalTax.getOtherTax()).add(CommonTgUtils.formatBigDecimal(priceItem.getTaxDetail().getOtherTax())));
                    }
                }
            }

            // 每日价格均价总价
            BigDecimal totalDayPrice = BigDecimal.ZERO;
            // 每日总价
            BigDecimal totalPrice = BigDecimal.ZERO;

            List<PriceInfoDetail> priceItemList = new ArrayList<>();

            for (String saleDate : salePriceMap.keySet()) {
                TaxDetailDto taxDetailDto = new TaxDetailDto();
                PriceInfoDetail priceItem = ProductSalePriceConvert.INSTANCE.priceInfoDetail(salePriceMap.get(saleDate).get(0));
                BigDecimal totalSalePrice = BigDecimal.ZERO;
                for (PriceInfoDetail priceItem1 : salePriceMap.get(saleDate)) {
                    totalSalePrice = totalSalePrice.add(priceItem1.getSalePrice());

                    if (null != priceItem1.getTaxDetail()) {
                        taxDetailDto.setSalesTax(CommonTgUtils.formatBigDecimal(taxDetailDto.getSalesTax()).add(CommonTgUtils.formatBigDecimal(priceItem1.getTaxDetail().getSalesTax())));
                        taxDetailDto.setTaxFee(CommonTgUtils.formatBigDecimal(taxDetailDto.getTaxFee()).add(CommonTgUtils.formatBigDecimal(priceItem1.getTaxDetail().getTaxFee())));
                        taxDetailDto.setOtherTax(CommonTgUtils.formatBigDecimal(taxDetailDto.getOtherTax()).add(CommonTgUtils.formatBigDecimal(priceItem1.getTaxDetail().getOtherTax())));
                    }
                }
                totalPrice = totalPrice.add(totalSalePrice);

                // 价格为每日价格均价

                priceItem.setSalePrice(CommonTgUtils.divideSetScale(totalSalePrice, new BigDecimal(salePriceMap.get(saleDate).size()), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                // 税费为每日税费
                priceItem.setTaxDetail(taxDetailDto);
                totalDayPrice = totalDayPrice.add(priceItem.getSalePrice());
                priceItemList.add(priceItem);
            }
            productSalePriceDTO.setTotalSalePrice(totalPrice);
            productSalePriceDTO.setTaxDetail(totalTax);
            productSalePriceDTO.setPriceList(priceItemList);
            log.info("productSalePriceDTO:{},priceItemList:{}", JSON.toJSONString(productSalePriceDTO), JSON.toJSONString(priceItemList));
        } catch (Exception e) {
            log.error("assemblyDaysSalePrice error!", e);
        }

    }

    /**
     * 获取供应商基础加幅
     */
    public SupplierAddDTO getSupplierIncrease(String supplyCode) {
        return StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplyCode), SupplierAddDTO.class);
    }

    /**
     * 获取分销商-供应商加幅
     */
    public SupplyIncrease getSupplyIncrease(String agentCode, String supplyCode) {
        String key = agentCode.concat("_").concat(supplyCode);
        return StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_INCREASE_KEY, key), SupplyIncrease.class);
    }

    /**
     * 获取分销商-供应商-酒店加幅
     */
    public SupplyHotelIncrease getSupplyHotelIncrease(String agentCode, String supplyCode, Long hotelId) {
        String key = agentCode.concat("_").concat(supplyCode).concat("_").concat(hotelId.toString());
        return StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_HOTEL_INCREASE_KEY, key), SupplyHotelIncrease.class);
    }
}
