package com.tiangong.order.service.impl;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.remote.DisB2BRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.OrderCheckInfoRequest;
import com.tiangong.dto.order.response.OrderFeeDetailDTO;
import com.tiangong.dto.order.response.TaxDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.CheckStatusEnum;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.*;
import com.tiangong.order.dto.OrderCheckPushInfoDTO;
import com.tiangong.order.dto.SupplyOrderAdditional;
import com.tiangong.order.enums.AdditionalChargesTypeEnum;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.order.mapper.*;
import com.tiangong.order.remote.dto.OrderCheckDetailDTO;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;
import com.tiangong.order.remote.response.PriceResponseDTO;
import com.tiangong.order.service.OrderCheckInfoService;
import com.tiangong.order.service.common.OrderCommonService;
import com.tiangong.order.util.GenerateAdditionalChargesGroupNumber;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.AgentCreditLineDTO;
import com.tiangong.organization.remote.dto.AgentCreditLineResultDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 订单入住信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:26:35
 */
@Slf4j
@Service
public class OrderCheckInfoServiceImpl extends ServiceImpl<OrderCheckInfoMapper, OrderCheckInfoEntity> implements OrderCheckInfoService {

    @Autowired
    private OrderCheckInfoMapper orderCheckInfoMapper;

    @Autowired
    private OrderCheckDetailMapper orderCheckDetailMapper;

    @Resource
    private DisB2BRemote disB2BRemote;

    @Resource
    private OrderMapper orderMapper;

    @Autowired
    private SupplyProductPriceMapper supplyProductPriceMapper;

    @Autowired
    private SupplyOrderMapper supplyOrderMapper;

    @Autowired
    private OrderProductPriceMapper orderProductPriceMapper;

    @Autowired
    private SupplyProductMapper supplyProductMapper;

    @Autowired
    private SupplyOrderAdditionalChargesMapper supplyOrderAdditionalChargesMapper;

    @Autowired
    private OrderAdditionalChargesMapper orderAdditionalChargesMapper;

    @Autowired
    private GenerateAdditionalChargesGroupNumber generateAdditionalChargesGroupNumber;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private OrderFinanceMapper orderFinanceMapper;

    @Autowired
    private AgentRemote agentRemote;

    @Autowired
    private SupplyOrderFinanceMapper supplyOrderFinanceMapper;

    @Autowired
    private OrderCheckPushInfoMapper orderCheckPushInfoMapper;

    private final Integer FIXED_LOG_COUNT = 15;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SupplyOrderAmtLogMapper supplyOrderAmtLogMapper;

    /**
     * 订单入住信息新增或更新
     */
    @Override
    @Transactional
    public int saveOrUpdateOrderCheckInfo(List<OrderCheckInfoDTO> orderCheckInfoDTOList, String operator, boolean isSync) {
        // 如果是同步操作存在入住明细则不新增
        if (isSync) {
            Integer count = orderCheckInfoMapper.selectCount(new QueryWrapper<OrderCheckInfoEntity>()
                    .eq("supply_order_code", orderCheckInfoDTOList.get(0).getOrderCode()).eq("deleted", 0));
            if (count != null && count > 0) {
                return 1;
            }
        }

        String dtfStr = "yyyy-MM-dd";
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(dtfStr);
        // 根据订单编码查找对应的订单
        Example orderExample = new Example(OrderPO.class);
        orderExample.createCriteria().andEqualTo("orderCode", orderCheckInfoDTOList.get(0).getOrderCode());
        OrderPO orderPO = orderMapper.selectOneByExample(orderExample);

        // 根据订单id查询供货单信息
        Example supplyOrderExample = new Example(SupplyOrderPO.class);
        supplyOrderExample.createCriteria().andEqualTo("orderId", orderPO.getId());
        List<SupplyOrderPO> supplyOrderPOS = supplyOrderMapper.selectByExample(supplyOrderExample);
        if (CollUtilX.isEmpty(supplyOrderPOS)) {
            throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST);
        }
        Map<String, SupplyOrderPO> supplyOrderMap = new HashMap<>();
        int supplyOrderConfirmationNum = 0;// 供货单已确认或已完成数量
        for (SupplyOrderPO supplyOrderPO : supplyOrderPOS) {
            supplyOrderMap.put(supplyOrderPO.getSupplyOrderCode(), supplyOrderPO);
            if (supplyOrderPO.getConfirmationStatus() != null && (Objects.equals(ConfirmationStatusEnum.CONFIRMED.key, supplyOrderPO.getConfirmationStatus()) ||
                    Objects.equals(ConfirmationStatusEnum.DONE.key, supplyOrderPO.getConfirmationStatus()))) {
                supplyOrderConfirmationNum++;
            }
        }

        // 获取每个日期对应的订单售价
        Map<String, BigDecimal> orderProductPricePOMap = new HashMap<>();
        BigDecimal sumOrderAmt = new BigDecimal(0);
        Map<String, BigDecimal> getSaleDateAndOrderProductPriceMap = getSaleDateAndOrderProductPrice(orderProductPricePOMap, orderPO, sumOrderAmt);
        sumOrderAmt = getSaleDateAndOrderProductPriceMap.get("sumOrderAmt") == null ? new BigDecimal(0) : getSaleDateAndOrderProductPriceMap.get("sumOrderAmt");

        // 存放供货单信息
        Map<String, SupplyOrderPO> supplyOrderPOMap = new HashMap<>();
        // 存放每个供货单入住明细信息
        Map<String, List<OrderCheckInfoDTO>> supplyOrderCheckInfoMap = new HashMap<>();
        for (OrderCheckInfoDTO orderCheckInfoDTO : orderCheckInfoDTOList) {
            if (StrUtilX.isEmpty(orderCheckInfoDTO.getSupplyOrderCode())) {
                throw new SysException(ParamErrorEnum.SUPPLY_ORDER_ISEMPTY);
            }
            if (!supplyOrderPOMap.containsKey(orderCheckInfoDTO.getSupplyOrderCode())) {
                // 根据供货单编码查找对应的供货单
                SupplyOrderPO supplyOrderPO = supplyOrderMap.get(orderCheckInfoDTO.getSupplyOrderCode());
                if (supplyOrderPO == null) {
                    throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST);
                }
                supplyOrderPOMap.put(orderCheckInfoDTO.getSupplyOrderCode(), supplyOrderPO);
            }
            if (supplyOrderCheckInfoMap.containsKey(orderCheckInfoDTO.getSupplyOrderCode())) {
                supplyOrderCheckInfoMap.get(orderCheckInfoDTO.getSupplyOrderCode()).add(orderCheckInfoDTO);
            } else {
                List<OrderCheckInfoDTO> dtoList = new ArrayList<>();
                dtoList.add(orderCheckInfoDTO);
                supplyOrderCheckInfoMap.put(orderCheckInfoDTO.getSupplyOrderCode(), dtoList);
            }
        }

        // 订单天数
        int dayNum = DateUtilX.getDateList(orderPO.getStartDate(), orderPO.getEndDate()).size() - 1;

        // 遍历供货单
        BigDecimal orderAdditionalCharges = new BigDecimal(0);// 订单附加费
        Boolean isCompute = false;// 标识是否新增附加项
        List<SupplyOrderAdditional> supplyOrderAdditionalList = new ArrayList<>();// 需要新增附加费的供货单
        BigDecimal sumSupplyOrderAmt = BigDecimal.ZERO;// 总供货单应付金额
        BigDecimal sumSupplyOrderCommissionAmt = BigDecimal.ZERO;// 总供货单佣金
        BigDecimal basePriceTotal = BigDecimal.ZERO;// 总供货单底价金额
        BigDecimal sumOrderAdditionalCharges = BigDecimal.ZERO;// 其他供货单不新增附加费金额
        Integer orderAdditionalChange = null;// 订单附加费是否变更
        for (Map.Entry<String, List<OrderCheckInfoDTO>> supplyOrderCheckInfo : supplyOrderCheckInfoMap.entrySet()) {
            String supplyOrderCode = supplyOrderCheckInfo.getKey();
            List<OrderCheckInfoDTO> supllyOrderCheckInfoDTOList = supplyOrderCheckInfo.getValue();
            SupplyOrderPO supplyOrderPO = supplyOrderPOMap.get(supplyOrderCheckInfo.getKey());

            // 不是已确认或已完成状态不写入住明细
            if (!((Objects.equals(orderPO.getOrderConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key) && Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) ||
                    (Objects.equals(orderPO.getOrderConfirmationStatus(), ConfirmationStatusEnum.DONE.key) && Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.DONE.key)))) {
                log.error("订单入住信息新增或更新，订单或供货单状态不对，supplyOrderCode={}，orderConfirmationStatus={}，supplyOrderConfirmationStatus={}",
                        supplyOrderPO.getSupplyOrderCode(), orderPO.getOrderConfirmationStatus(), supplyOrderPO.getConfirmationStatus());
                return -1;
            }
            // 计算总应付
            sumSupplyOrderAmt = sumSupplyOrderAmt.add(supplyOrderPO.getSupplyOrderAmt());
            sumSupplyOrderCommissionAmt = sumSupplyOrderCommissionAmt.add(supplyOrderPO.getCommission() == null ? BigDecimal.ZERO : supplyOrderPO.getCommission());

            // 查询当前供货单对应的产品
            Example supplyProductExample = new Example(SupplyProductPO.class);
            supplyProductExample.createCriteria().andEqualTo("supplyOrderId", supplyOrderPO.getId());
            List<SupplyProductPO> supplyProductPOList = supplyProductMapper.selectByExample(supplyProductExample);
            Integer supplyOrderRoomQty = supplyProductPOList.get(0).getRoomQty() == null ? 1 : supplyProductPOList.get(0).getRoomQty();

            // 前端传入间数和后端存储的间数是否一样
            if (!supplyOrderRoomQty.equals(supllyOrderCheckInfoDTOList.size())) {
                log.error("订单入住信息新增或更新，传入间数和存储的间数不一样，supplyOrderCode={}，supplyOrderRoomQty={}, infoSize={}",
                        supplyOrderCode, supplyOrderRoomQty, supllyOrderCheckInfoDTOList.size());
                return -1;
            }

            // 提取供货单(可能存在一单多供)的每个日期对应的产品底价
            // Map<日期,底价>
            Map<String, BigDecimal> baseDateAndSalePriceMap = new HashMap<>();
            BigDecimal sumAdditionalCharges = new BigDecimal(0);
            BigDecimal sumBasePrice = new BigDecimal(0);
            Map<String, BigDecimal> resultMap = getSaleDateAndSupplyOrderProductPrice(orderPO, supplyOrderPO, orderCheckInfoDTOList,
                    baseDateAndSalePriceMap, dtf, sumAdditionalCharges, sumBasePrice, supplyOrderRoomQty, operator, isSync);
            sumAdditionalCharges = resultMap.get("sumAdditionalCharges") == null ? new BigDecimal(0) : resultMap.get("sumAdditionalCharges");
            sumBasePrice = resultMap.get("sumBasePrice") == null ? new BigDecimal(0) : resultMap.get("sumBasePrice");

            basePriceTotal = basePriceTotal.add(sumBasePrice);
            Date firstDate = supllyOrderCheckInfoDTOList.get(0).getCheckInDetailList().get(0).getSaleDate();

            BigDecimal supplyOrderAdditionalCharges = new BigDecimal(0);

            SupplyOrderAdditional supplyOrderAdditional = new SupplyOrderAdditional();
            if (sumAdditionalCharges.compareTo(sumBasePrice) < 0) {//供货单入住明细金额之和 < 供货单底价之和
                // 间数和天数要和订单一致才生成附加费
                isCompute = true;
                for (OrderCheckDetailDTO orderCheckDetailDTO : supllyOrderCheckInfoDTOList.get(0).getCheckInDetailList()) {
                    if (firstDate.after(orderCheckDetailDTO.getSaleDate())) {
                        firstDate = orderCheckDetailDTO.getSaleDate();
                    }
                }

                // 需要生成附加费供货单
                supplyOrderAdditional.setSupplyOrderCode(supplyOrderCode);
                supplyOrderAdditional.setFirstDate(firstDate);
                supplyOrderAdditional.setSupplyOrderPO(supplyOrderPO);
                supplyOrderAdditional.setSumBasePrice(sumBasePrice);
                // 判断订单附加费是否需要变动
                if (supllyOrderCheckInfoDTOList.size() == orderPO.getRoomQty() &&
                        CollUtilX.isNotEmpty(supllyOrderCheckInfoDTOList.get(0).getCheckInDetailList()) &&
                        supllyOrderCheckInfoDTOList.get(0).getCheckInDetailList().size() == dayNum && supplyOrderConfirmationNum == 1) {
                    orderAdditionalChange = 1;
                }
                supplyOrderAdditionalList.add(supplyOrderAdditional);
            } else {
                sumOrderAdditionalCharges = sumOrderAdditionalCharges.add(supplyOrderPO.getOrderAdditionalCharges() == null ? BigDecimal.ZERO : supplyOrderPO.getOrderAdditionalCharges());
            }

            if (sumAdditionalCharges.compareTo(sumBasePrice) != 0){
                SupplyOrderAmtLogPO po = new SupplyOrderAmtLogPO();
                po.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyOrderPO.getBaseCurrency()));
                String content = RedisTemplateX.get(RedisKey.LOG_CODE+"O000013")+":"+RedisTemplateX.get(RedisKey.LOG_CODE+"O000014")+" "+supplyOrderPO.getSupplyOrderCode()+","
                        +RedisTemplateX.get(RedisKey.LOG_CODE+"O000015")+" "+currency + " " + sumAdditionalCharges.stripTrailingZeros().toPlainString()+","
                        +RedisTemplateX.get(RedisKey.LOG_CODE+"O000016");
                po.setContent(content);
                po.setCreatedDt(new Date());
                po.setOrderCode(orderPO.getOrderCode());
                supplyOrderAmtLogMapper.insert(po);
                //设置该供货单为入住明细不符的单
                Example updateSupplyOrderExample = new Example(SupplyOrderPO.class);
                updateSupplyOrderExample.createCriteria().andEqualTo("id", supplyOrderPO.getId());
                SupplyOrderPO updateSupplyOrderPO = new SupplyOrderPO();
                updateSupplyOrderPO.setIsCheckInDetailAbnormal(1);//供货单入住明显异常记录
                supplyOrderMapper.updateByExampleSelective(updateSupplyOrderPO, updateSupplyOrderExample);
            }

            // 数据入库
            for (OrderCheckInfoDTO orderCheckInfoDTO : supllyOrderCheckInfoDTOList) {
                if (null == orderCheckInfoDTO.getId()) {// 新增
                    OrderCheckInfoEntity entity = ProductSalePriceConvert.INSTANCE.OrderCheckInfoEntityConvert(orderCheckInfoDTO);
                    entity.setCreatedDt(new Date());
                    entity.setCreatedBy(operator);
                    entity.setCurrency(supplyOrderPO.getBaseCurrency());
                    int insert = orderCheckInfoMapper.insert(entity);
                    if (insert == 1) {
                        // 新增入住明细
                        for (OrderCheckDetailDTO orderCheckDetailDTO : orderCheckInfoDTO.getCheckInDetailList()) {
                            if (null == orderCheckDetailDTO.getOrderCode()) {
                                orderCheckDetailDTO.setOrderCode(orderCheckInfoDTO.getOrderCode());
                            }
//                            OrderCheckDetailEntity entity1 = new OrderCheckDetailEntity();
//                            BeanUtils.copyProperties(orderCheckDetailDTO, entity1);
                            OrderCheckDetailEntity entity1 = ProductSalePriceConvert.INSTANCE.OrderCheckDetailEntityConvert(orderCheckDetailDTO);
                            entity1.setCreatedDt(new Date());
                            entity1.setCheckInfoId(entity.getId());// 入住信息Id
                            entity1.setCreatedBy(operator);
                            orderCheckDetailMapper.insert(entity1);

                            Map<String, BigDecimal> resultMapTmp = getOrderAdditionalChargesAndSupplyOrderAdditionalCharges(isCompute, orderCheckDetailDTO,
                                    baseDateAndSalePriceMap, orderProductPricePOMap, supplyOrderAdditionalCharges, orderAdditionalCharges, supplyOrderPO, orderPO.getAgentCode());
                            supplyOrderAdditionalCharges = resultMapTmp.get("supplyOrderAdditionalCharges") == null ? new BigDecimal(0) : resultMapTmp.get("supplyOrderAdditionalCharges");
                            orderAdditionalCharges = resultMapTmp.get("orderAdditionalCharges") == null ? new BigDecimal(0) : resultMapTmp.get("orderAdditionalCharges");
                        }
                    }
                } else {// 更新
//                    OrderCheckInfoEntity entity = new OrderCheckInfoEntity();
//                    BeanUtils.copyProperties(orderCheckInfoDTO, entity);
                    OrderCheckInfoEntity entity = ProductSalePriceConvert.INSTANCE.OrderCheckInfoEntityConvert(orderCheckInfoDTO);
                    entity.setUpdatedDt(new Date());
                    entity.setUpdatedBy(operator);
                    entity.setCurrency(supplyOrderPO.getBaseCurrency());
                    orderCheckInfoMapper.updateById(entity);
                    // 先删除，再新增
                    orderCheckDetailMapper.deleteByCheckInfoId(entity.getId());
                    for (OrderCheckDetailDTO orderCheckDetailDTO : orderCheckInfoDTO.getCheckInDetailList()) {
                        if (null == orderCheckDetailDTO.getOrderCode()) {
                            orderCheckDetailDTO.setOrderCode(orderCheckInfoDTO.getOrderCode());
                        }
                        // 新增入住明细
//                        OrderCheckDetailEntity entityAdd = new OrderCheckDetailEntity();
//                        BeanUtils.copyProperties(orderCheckDetailDTO, entityAdd);
                        OrderCheckDetailEntity entityAdd = ProductSalePriceConvert.INSTANCE.OrderCheckDetailEntityConvert(orderCheckDetailDTO);
                        entityAdd.setCreatedDt(new Date());
                        entityAdd.setCreatedBy(operator);
                        entityAdd.setCheckInfoId(entity.getId());// 入住信息Id
                        orderCheckDetailMapper.insert(entityAdd);

                        Map<String, BigDecimal> resultMapTmp = getOrderAdditionalChargesAndSupplyOrderAdditionalCharges(isCompute, orderCheckDetailDTO,
                                baseDateAndSalePriceMap, orderProductPricePOMap, supplyOrderAdditionalCharges, orderAdditionalCharges, supplyOrderPO, orderPO.getAgentCode());
                        supplyOrderAdditionalCharges = resultMapTmp.get("supplyOrderAdditionalCharges") == null ? new BigDecimal(0) : resultMapTmp.get("supplyOrderAdditionalCharges");
                        orderAdditionalCharges = resultMapTmp.get("orderAdditionalCharges") == null ? new BigDecimal(0) : resultMapTmp.get("orderAdditionalCharges");
                    }
                }
            }
            supplyOrderAdditional.setSupplyOrderAdditionalCharges(supplyOrderAdditionalCharges);
            log.info("是否生成附加费，supplyOrderCode={}, sumAdditionalCharges={}, sumBasePrice={}, supplyOrderAdditionalCharges={}, orderAdditionalCharges={}",
                    supplyOrderCode, sumAdditionalCharges, sumBasePrice, JSONUtils.toJSONString(supplyOrderAdditionalCharges), JSONUtils.toJSONString(orderAdditionalCharges));
        }

        // 判断是否生成附加费
        if (isCompute) {
            updateAdditionalChargesAboutOrderAndSupplyOrder(supplyOrderAdditionalList, orderAdditionalCharges, orderPO, sumOrderAmt, sumSupplyOrderAmt, sumSupplyOrderCommissionAmt, basePriceTotal, sumOrderAdditionalCharges, orderAdditionalChange, operator, isSync);
        }

        try {
            Map<String, Object> map = new HashMap<>();
            map.put("order_code", orderPO.getOrderCode());
            List<OrderCheckInfoEntity> orderCheckInfoEntities = orderCheckInfoMapper.selectByMap(map);
            List<Integer> checkInfoIds = orderCheckInfoEntities.stream().map(OrderCheckInfoEntity::getId).collect(Collectors.toList());
            QueryWrapper<OrderCheckDetailEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in(CollUtilX.isNotEmpty(checkInfoIds), "check_info_id", checkInfoIds);
            List<OrderCheckDetailEntity> orderCheckDetailEntities = orderCheckDetailMapper.selectList(queryWrapper);
            OrderCheckDetailInfoRequest b2BOrderCheckDetails = assembleOrderDetails(orderCheckInfoEntities, orderCheckDetailEntities, orderPO);
            log.info("notifyAgent--b2BOrderCheckDetails:{}", JSON.toJSON(b2BOrderCheckDetails));
            // 异步通知入住明细给分销商
            CompletableFuture.runAsync(() -> disB2BRemote.orderCheckDetailsNotify(b2BOrderCheckDetails));
        } catch (Exception e) {
            log.error("推送入住明细系统异常,error:", e);
        }

        try {
            // 加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        return 1;
    }

    /**
     * 更新订单和供货单的附加项费用
     *
     * @param supplyOrderAdditionalList   需要新增附加费供货单信息
     * @param orderAdditionalCharges      此供货单对应的订单附加项费用
     * @param orderPO                     订单信息
     * @param sumOrderAmt                 订单总售价
     * @param sumSupplyOrderAmt           供货单总应付金额
     * @param basePriceTotal              供货单总底价
     * @param orderAdditionalChange       订单附加费是否变动
     * @param sumOrderAdditionalCharges   订单附加费总额
     * @param sumSupplyOrderCommissionAmt 售价之和
     */
    private void updateAdditionalChargesAboutOrderAndSupplyOrder(List<SupplyOrderAdditional> supplyOrderAdditionalList,
                                                                 BigDecimal orderAdditionalCharges,
                                                                 OrderPO orderPO,
                                                                 BigDecimal sumOrderAmt,
                                                                 BigDecimal sumSupplyOrderAmt,
                                                                 BigDecimal basePriceTotal,
                                                                 BigDecimal sumSupplyOrderCommissionAmt,
                                                                 BigDecimal sumOrderAdditionalCharges,
                                                                 Integer orderAdditionalChange,
                                                                 String operator,
                                                                 boolean isSync) {
        LoginUser loginUser = null;
        // 同步操作
        if (isSync) {
            loginUser = new LoginUser();
            loginUser.setFullUserName(operator);
        } else {
            loginUser = TokenManager.getUser(request);
        }
        // 总供货单附加费
        BigDecimal supplyOrderAdditionalChargesSum = BigDecimal.ZERO;
        String groupNumber = null;
        for (SupplyOrderAdditional supplyOrderAdditional : supplyOrderAdditionalList) {
            SupplyOrderPO supplyOrderPO = supplyOrderAdditional.getSupplyOrderPO();
            StringBuilder logsStr = new StringBuilder("因为入住明细总金额小于供货单房费，所以生成")
                    .append("【")
                    .append(AdditionalChargesTypeEnum.OTHER.value)
                    .append("】")
                    .append("供货单")
                    .append(supplyOrderPO.getSupplyOrderCode())
                    .append("的附加费为");

            groupNumber = generateAdditionalChargesGroupNumber.generateAdditionalChargesGroupNumber();

            // 修改供货单应付金额和附加项费用
            BigDecimal supplyOrderAdditionalChargesTmp = supplyOrderAdditional.getSupplyOrderAdditionalCharges().negate();
            BigDecimal orderAdditionalChargesTmp = orderAdditionalCharges.negate();
            sumOrderAdditionalCharges = sumOrderAdditionalCharges.add(orderAdditionalChargesTmp);
            logsStr.append(supplyOrderAdditionalChargesTmp)
                    .append(",此供货单对应所产生的订单附加费为")
                    .append(orderAdditionalChargesTmp);

            // 供货单附加费
            List<SupplyOrderAdditionalChargesPO> supplyOrderAdditionalChargesList = supplyOrderAdditionalChargesMapper.selectList(new QueryWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                    .eq(SupplyOrderAdditionalChargesPO::getSupplyOrderId, supplyOrderPO.getId())
                    .eq(SupplyOrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.OTHER.key));
            if (CollUtilX.isNotEmpty(supplyOrderAdditionalChargesList) && supplyOrderAdditionalChargesList.size() == 1) {
                supplyOrderAdditionalChargesMapper.update(null, new UpdateWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                        .set(SupplyOrderAdditionalChargesPO::getQuantity, 1)
                        .set(SupplyOrderAdditionalChargesPO::getAdditionalChargesDate, supplyOrderAdditional.getFirstDate())
                        .set(SupplyOrderAdditionalChargesPO::getAdditionalCharges, supplyOrderAdditionalChargesTmp)
                        .set(SupplyOrderAdditionalChargesPO::getUpdatedBy, loginUser.getFullUserName())
                        .set(SupplyOrderAdditionalChargesPO::getUpdatedDt, new Date())
                        .eq(SupplyOrderAdditionalChargesPO::getSupplyOrderId, supplyOrderPO.getId())
                        .eq(SupplyOrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.OTHER.key));
            } else {
                if (CollUtilX.isNotEmpty(supplyOrderAdditionalChargesList) && supplyOrderAdditionalChargesList.size() > 1) {//已经存在多个日期的多条数据，直接插入当前日期的第一条
                    supplyOrderAdditionalChargesMapper.delete(new QueryWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                            .eq(SupplyOrderAdditionalChargesPO::getSupplyOrderId, supplyOrderPO.getId())
                            .eq(SupplyOrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.OTHER.key));
                }
                SupplyOrderAdditionalChargesPO supplyOrderAdditionalChargesPO = SupplyOrderAdditionalChargesPO.builder()
                        .groupNumber(groupNumber)
                        .additionalChargesType(AdditionalChargesTypeEnum.OTHER.key)
                        .additionalChargesName("退款或提前离店")
                        .orderId(orderPO.getId())
                        .supplyOrderId(supplyOrderPO.getId())
                        .supplyOrderCode(supplyOrderPO.getSupplyOrderCode())
                        .additionalChargesDate(supplyOrderAdditional.getFirstDate())
                        .quantity(1)
                        .additionalCharges(supplyOrderAdditionalChargesTmp)
                        .createdBy(loginUser.getFullUserName())
                        .createdDt(new Date())
                        .updatedBy(loginUser.getFullUserName())
                        .updatedDt(new Date())
                        .build();
                supplyOrderAdditionalChargesMapper.insert(supplyOrderAdditionalChargesPO);
            }

            //将所有的附加项进行合计得到最终的的订单附加项费用和供货单附加项费用
            //“其他”只会存在一条，并且其他只能有一份
            //供货单
            //查找此供货单对应的所有附加项，然后进行合计
            List<SupplyOrderAdditionalChargesPO> supplyOrderAdditionalChargesTotalList = supplyOrderAdditionalChargesMapper.selectList(new QueryWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                    .eq(SupplyOrderAdditionalChargesPO::getSupplyOrderId, supplyOrderPO.getId()));
            BigDecimal supplyOrderAdditionalChargesTotal = new BigDecimal(0);
            for (SupplyOrderAdditionalChargesPO supplyOrderAdditionalChargesPO : supplyOrderAdditionalChargesTotalList) {
                if (supplyOrderAdditionalChargesPO.getAdditionalCharges() != null && supplyOrderAdditionalChargesPO.getQuantity() != null) {
                    supplyOrderAdditionalChargesTotal = supplyOrderAdditionalChargesTotal.add(supplyOrderAdditionalChargesPO.getAdditionalCharges()
                            .multiply(BigDecimal.valueOf(supplyOrderAdditionalChargesPO.getQuantity().doubleValue())));
                }
            }
            supplyOrderAdditionalChargesSum = supplyOrderAdditionalChargesSum.subtract(supplyOrderAdditionalChargesTotal);

            Example updateSupplyOrderExample = new Example(SupplyOrderPO.class);
            updateSupplyOrderExample.createCriteria().andEqualTo("id", supplyOrderPO.getId());
            SupplyOrderPO updateSupplyOrderPO = new SupplyOrderPO();
            // 现付订单不修改供货单应付金额
            if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                // 供货单应付金额 = 供货单底价之和 + 附加项费用(负值) - 供应商返佣
                updateSupplyOrderPO.setSupplyOrderAmt(supplyOrderAdditional.getSumBasePrice().add(supplyOrderAdditionalChargesTotal).subtract(supplyOrderPO.getCommission() == null ? new BigDecimal(0) : supplyOrderPO.getCommission()));
            }
            updateSupplyOrderPO.setAdditionalCharges(supplyOrderAdditionalChargesTotal);
            updateSupplyOrderPO.setOrderAdditionalCharges(orderAdditionalChargesTmp);
            //updateSupplyOrderPO.setIsCheckInDetailAbnormal(1);//供货单入住明显异常记录
            supplyOrderMapper.updateByExampleSelective(updateSupplyOrderPO, updateSupplyOrderExample);

            BigDecimal supplyOrderAmt = supplyOrderAdditional.getSumBasePrice().add(supplyOrderAdditionalChargesTotal)
                    .subtract(supplyOrderPO.getCommission() == null ? new BigDecimal(0) : supplyOrderPO.getCommission());
            if (supplyOrderAmt.compareTo(supplyOrderPO.getSupplyOrderAmt()) != 0) {
                // 更新订单未结算金额和结算状态
                SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
                supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
                supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
                SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
                SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
                supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
                // 如果原来供货单是取消状态
                if (supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key) || PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt().subtract(supplyOrderFinancePO.getPaidAmt() == null ? new BigDecimal(0) : supplyOrderFinancePO.getPaidAmt()));
                } else {
                    supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderAmt.subtract(supplyOrderFinancePO.getPaidAmt() == null ? new BigDecimal(0) : supplyOrderFinancePO.getPaidAmt()));
                }

                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    supplyOrderFinanceUpdate.setSettlementStatus(1);
                } else {
                    supplyOrderFinanceUpdate.setSettlementStatus(0);
                }
                // 更新对账状态
                if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                        // 如果未收金额为0，则改为已对账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        // 如果未收金额不为0，则改为可出账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                }
                supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
            }

            // 记录日志
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    loginUser.getUserName(),
                    "",
                    supplyOrderPO.getSupplyOrderCode(),
                    logsStr.toString()
            );
        }

        // 判断订单附加费是否变动
        if (orderAdditionalChange != null && orderAdditionalChange == 1) {
            // 订单附加费
            List<OrderAdditionalChargesPO> orderAdditionalChargesList = orderAdditionalChargesMapper.selectList(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                    .eq(OrderAdditionalChargesPO::getOrderId, orderPO.getId())
                    .eq(OrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.OTHER.key));
            if (CollUtilX.isNotEmpty(orderAdditionalChargesList) && orderAdditionalChargesList.size() == 1) {
                orderAdditionalChargesMapper.update(null, new UpdateWrapper<OrderAdditionalChargesPO>().lambda()
                        .set(OrderAdditionalChargesPO::getQuantity, 1)
                        .set(OrderAdditionalChargesPO::getAdditionalChargesDate, orderPO.getStartDate())
                        .set(OrderAdditionalChargesPO::getAdditionalCharges, sumOrderAdditionalCharges)
                        .set(OrderAdditionalChargesPO::getUpdatedBy, loginUser.getFullUserName())
                        .set(OrderAdditionalChargesPO::getUpdatedDt, new Date())
                        .eq(OrderAdditionalChargesPO::getOrderId, orderPO.getId())
                        .eq(OrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.OTHER.key));
            } else {
                if (CollUtilX.isNotEmpty(orderAdditionalChargesList) && orderAdditionalChargesList.size() > 1) {//已经存在多个日期的多条数据，直接插入当前日期的第一条
                    orderAdditionalChargesMapper.delete(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                            .eq(OrderAdditionalChargesPO::getOrderId, orderPO.getId())
                            .eq(OrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.OTHER.key));
                }
                OrderAdditionalChargesPO orderAdditionalChargesPO = OrderAdditionalChargesPO.builder()
                        .groupNumber(groupNumber)
                        .additionalChargesType(AdditionalChargesTypeEnum.OTHER.key)
                        .additionalChargesName("退款或提前离店")
                        .orderId(orderPO.getId())
                        .orderCode(orderPO.getOrderCode())
                        .additionalChargesDate(orderPO.getStartDate())
                        .quantity(1)
                        .additionalCharges(sumOrderAdditionalCharges)
                        .createdBy(loginUser.getFullUserName())
                        .createdDt(new Date())
                        .updatedBy(loginUser.getFullUserName())
                        .updatedDt(new Date())
                        .build();
                orderAdditionalChargesMapper.insert(orderAdditionalChargesPO);
            }

            // 查找此订单对应的所有附加项，然后进行合计
            List<OrderAdditionalChargesPO> orderAdditionalChargesTotalList = orderAdditionalChargesMapper.selectList(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                    .eq(OrderAdditionalChargesPO::getOrderId, orderPO.getId()));
            BigDecimal orderAdditionalChargesTotal = new BigDecimal(0);
            for (OrderAdditionalChargesPO orderAdditionalChargesPO : orderAdditionalChargesTotalList) {
                if (orderAdditionalChargesPO.getAdditionalCharges() != null && orderAdditionalChargesPO.getQuantity() != null) {
                    orderAdditionalChargesTotal = orderAdditionalChargesTotal.add(orderAdditionalChargesPO.getAdditionalCharges()
                            .multiply(BigDecimal.valueOf(orderAdditionalChargesPO.getQuantity().doubleValue())));
                }
            }
            BigDecimal orderAmt = sumOrderAmt.add(orderAdditionalChargesTotal);

            // 修改订单
            Example updateOrderExample = new Example(OrderPO.class);
            updateOrderExample.createCriteria().andEqualTo("id", orderPO.getId());
            OrderPO updateOrder = new OrderPO();
            // 现付订单不修改订单应收
            if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                // 订单应收 = 售价之和 + 附加项费用(负值)
                updateOrder.setOrderAmt(sumOrderAmt.add(orderAdditionalChargesTotal));
                // 利润 = 订单应收 - 所有供货单应付
                updateOrder.setProfit(sumOrderAmt.add(orderAdditionalChargesTotal).subtract(basePriceTotal.add(supplyOrderAdditionalChargesSum)
                        .subtract(sumSupplyOrderCommissionAmt)).subtract(sumSupplyOrderAmt));
            }
            updateOrder.setAdditionalCharges(orderAdditionalChargesTotal);
            orderMapper.updateByExampleSelective(updateOrder, updateOrderExample);

            // 修改订单财务
            if (orderAmt.compareTo(orderPO.getOrderAmt()) != 0 && !orderPO.getOrderConfirmationStatus().equals(OrderStatusEnum.CANCELED.no)) {//非取消状态，才修改订单结算金额和状态等
                // 更新订单未结算金额和结算状态
                OrderFinancePO orderFinanceQuery = new OrderFinancePO();
                orderFinanceQuery.setOrderId(orderPO.getId());
                OrderFinancePO orderFinancePO = orderFinanceMapper.selectOne(orderFinanceQuery);
                OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
                orderFinanceUpdate.setId(orderFinancePO.getId());
                if (orderPO.getOrderConfirmationStatus() == 2 || PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {//已取消
                    orderFinanceUpdate.setUnreceivedAmt(orderPO.getOrderAmt().subtract(orderFinancePO.getReceivedAmt() == null ? new BigDecimal(0) : orderFinancePO.getReceivedAmt()));
                } else {
                    orderFinanceUpdate.setUnreceivedAmt(orderAmt.subtract(orderFinancePO.getReceivedAmt() == null ? new BigDecimal(0) : orderFinancePO.getReceivedAmt()));
                }
                if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                    orderFinanceUpdate.setSettlementStatus(1);
                } else {
                    orderFinanceUpdate.setSettlementStatus(0);
                }
                orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);

                if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
                    // 非单结更新对账状态
                    if (!Objects.equals(orderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                        if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                            // 如果未收金额为0，则改为已对账
                            orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                        } else {
                            // 如果未收金额不为0，则改为可出账
                            orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                        }
                        orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);
                    }

                    // 不是现付才退额度
                    if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                        // 非单结订单扣退额度
                        AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                        if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                            agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                        } else {
                            agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                        }
                        agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                        // 原订单金额减去修改后金额等于扣退额度i

                        BigDecimal oldOrderAmtSubtractNewOrderAmt = orderPO.getOrderAmt().subtract(orderAmt);
                        if (oldOrderAmtSubtractNewOrderAmt.compareTo(BigDecimal.ZERO) < 0) {
                            agentCreditLineDTO.setDeductRefundCreditLine(oldOrderAmtSubtractNewOrderAmt.negate());
                        } else {
                            agentCreditLineDTO.setDeductRefundCreditLine(oldOrderAmtSubtractNewOrderAmt);
                        }
                        Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
                        if (!creditLineResponse.isSuccess()) {
                            throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                        }
                    }
                }
            }
        }
    }

    /**
     * 当附加项费用小于底价时，计算对应的附加费用
     *
     * @param isCompute                    是否进行计算
     * @param orderCheckDetailDTO          入住明细
     * @param baseDateAndSalePriceMap      某个日期所对应的底价
     * @param orderProductPricePOMap       某个日期所对应售价
     * @param supplyOrderAdditionalCharges 供货单附加项费用
     * @param orderAdditionalCharges       订单附加项费用
     */
    private Map<String, BigDecimal> getOrderAdditionalChargesAndSupplyOrderAdditionalCharges(Boolean isCompute,
                                                                                             OrderCheckDetailDTO orderCheckDetailDTO,
                                                                                             Map<String, BigDecimal> baseDateAndSalePriceMap,
                                                                                             Map<String, BigDecimal> orderProductPricePOMap,
                                                                                             BigDecimal supplyOrderAdditionalCharges,
                                                                                             BigDecimal orderAdditionalCharges,
                                                                                             SupplyOrderPO supplyOrderPO,
                                                                                             String agentCode) {
        Map<String, BigDecimal> result = new HashMap<>();
        if (isCompute) {
            String date = DateUtilX.dateToString(orderCheckDetailDTO.getSaleDate(), "yyyy-MM-dd");
            BigDecimal additionalChargesTmp = new BigDecimal(0);
            additionalChargesTmp = additionalChargesTmp.add(orderCheckDetailDTO.getSalePrice() == null ? new BigDecimal(0) : orderCheckDetailDTO.getSalePrice())
                    .add(orderCheckDetailDTO.getRefundPrice() == null ? new BigDecimal(0) : orderCheckDetailDTO.getRefundPrice());
            BigDecimal basePrice = baseDateAndSalePriceMap.get(date) == null ? new BigDecimal(0) : baseDateAndSalePriceMap.get(date);
            BigDecimal salePrice = orderProductPricePOMap.get(date) == null ? new BigDecimal(0) : orderProductPricePOMap.get(date);
            if (additionalChargesTmp.compareTo(basePrice) < 0) {//附加项费用<底价
                supplyOrderAdditionalCharges = supplyOrderAdditionalCharges.add(basePrice.subtract(additionalChargesTmp));

                // 订单附加费需要转汇率
                BigDecimal supplierToAgentRate = supplyOrderPO.getSupplierToAgentRate();
                if (supplierToAgentRate != null && supplierToAgentRate.compareTo(BigDecimal.ZERO) != 0) {
                    additionalChargesTmp = CommonTgUtils.setRate(additionalChargesTmp, supplierToAgentRate);
                }
                //查询分销商--从缓存里面获取
                String agent = (String) RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode);
                AgentAccountConfig agentAccountConfig = JSON.parseObject(agent, AgentAccountConfig.class);
                if (agentAccountConfig == null) {
                    log.error("操作入住明细，客户信息不存在，agentCode={}", agentCode);
                    throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
                }
                BigDecimal price = CommonTgUtils.setScale(salePrice.subtract(additionalChargesTmp), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                orderAdditionalCharges = orderAdditionalCharges.add(price);
            }
            result.put("supplyOrderAdditionalCharges", supplyOrderAdditionalCharges);
            result.put("orderAdditionalCharges", orderAdditionalCharges);
        }
        return result;
    }


    /**
     * 获取每个日期对应的订单售价
     *
     * @param orderProductPricePOMap 每个日期对应的订单售价
     * @param orderPO                订单
     * @param sumOrderAmt            售价之和
     */
    private Map<String, BigDecimal> getSaleDateAndOrderProductPrice(Map<String, BigDecimal> orderProductPricePOMap, OrderPO orderPO, BigDecimal sumOrderAmt) {
        Example orderProductPriceExample = new Example(OrderProductPricePO.class);
        orderProductPriceExample.setOrderByClause("sale_date");
        Example.Criteria orderProductPriceCriteria = orderProductPriceExample.createCriteria();
        orderProductPriceCriteria.andEqualTo("orderId", orderPO.getId());
        List<OrderProductPricePO> orderProductPricePOList = orderProductPriceMapper.selectByExample(orderProductPriceExample);
        for (OrderProductPricePO orderProductPricePO : orderProductPricePOList) {
            BigDecimal orderProductPrice = orderProductPricePO.getSalePrice() == null ? new BigDecimal(0) : orderProductPricePO.getSalePrice();
            orderProductPricePOMap.put(DateUtilX.dateToString(orderProductPricePO.getSaleDate(), "yyyy-MM-dd"), orderProductPrice);
//            sumOrderAmt = sumOrderAmt.add(orderProductPrice.multiply(BigDecimal.valueOf(orderPO.getRoomQty() == null ? 1 : orderPO.getRoomQty().doubleValue())));
            sumOrderAmt = sumOrderAmt.add(orderProductPrice);
        }
        Map<String, BigDecimal> result = new HashMap<>();
        result.put("sumOrderAmt", sumOrderAmt);
        return result;
    }

    /**
     * 提取出不同供货单(可能存在一单多供)的每个日期对应的产品底价
     *
     * @param orderPO                 订单
     * @param supplyOrderPO           供货单
     * @param orderCheckInfoDTOList   入住明细
     * @param baseDateAndSalePriceMap <日期,底价>
     * @param dtf                     日期格式
     * @param sumAdditionalCharges    总的入住明细金额
     * @param sumBasePrice            供货单底价之和
     * @param supplyOrderRoomQty      间数
     */
    private Map<String, BigDecimal> getSaleDateAndSupplyOrderProductPrice(OrderPO orderPO,
                                                                          SupplyOrderPO supplyOrderPO,
                                                                          List<OrderCheckInfoDTO> orderCheckInfoDTOList,
                                                                          Map<String, BigDecimal> baseDateAndSalePriceMap,
                                                                          DateTimeFormatter dtf,
                                                                          BigDecimal sumAdditionalCharges,
                                                                          BigDecimal sumBasePrice,
                                                                          Integer supplyOrderRoomQty,
                                                                          String operator,
                                                                          boolean isSync) {
        List<String> checkInDetailsLogsList = new ArrayList<>();
        LoginUser loginUser = null;
        // 同步操作
        if (isSync) {
            loginUser = new LoginUser();
            loginUser.setUserName(operator);
        } else {
            loginUser = TokenManager.getUser(request);
        }

        StringBuilder checkInDetailsLogs = new StringBuilder();
        if (CollUtilX.isNotEmpty(orderCheckInfoDTOList) && orderCheckInfoDTOList.get(0).getId() == null) {
            checkInDetailsLogs = new StringBuilder("(" + supplyOrderPO.getSupplyOrderCode() + ")新增入住明细。");
        } else if (CollUtilX.isNotEmpty(orderCheckInfoDTOList) && orderCheckInfoDTOList.get(0).getId() != null) {
            checkInDetailsLogs = new StringBuilder("(" + supplyOrderPO.getSupplyOrderCode() + ")修改入住明细。");
        }

        //查询供货单价格明细
        Example supplyProductPriceExample = new Example(SupplyProductPricePO.class);
        Example.Criteria supplyProductPriceCriteria = supplyProductPriceExample.createCriteria();
        supplyProductPriceCriteria.andEqualTo("supplyOrderId", supplyOrderPO.getId());
        List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.selectByExample(supplyProductPriceExample);
        //费用按房间转Map
        Map<Integer, List<SupplyProductPricePO>> supplyFeeMap = supplyProductPricePOList.stream()
                .collect(Collectors.groupingBy(SupplyProductPricePO::getRoomNumber));
        Map<String, PriceResponseDTO> supplyPriceMap = new HashMap<>();
        for (Integer roomNumber : supplyFeeMap.keySet()) {
            //按次税费
            TaxDTO timesTaxDTO = new TaxDTO();
            List<OrderFeeDetailDTO> orderFeeDetailDTOList = new ArrayList<>();
            for (SupplyProductPricePO supplyProductPricePO : supplyFeeMap.get(roomNumber)) {
                if (null != supplyProductPricePO.getSaleDate()) {
                    String key = DateUtilX.dateToString(supplyProductPricePO.getSaleDate());
                    //设置按日价格
                    if (null == supplyPriceMap.get(key)) {
                        PriceResponseDTO priceResponseDTO = new PriceResponseDTO();
                        priceResponseDTO.setSaleDate(key);
                        priceResponseDTO.setBasePrice(supplyProductPricePO.getBasePrice());
                        priceResponseDTO.setSalePrice(supplyProductPricePO.getBasePrice());
                        priceResponseDTO.setBreakfastNum(orderPO.getBreakfastQty());
                        supplyPriceMap.put(DateUtilX.dateToString(supplyProductPricePO.getSaleDate()), priceResponseDTO);
                    } else {
                        supplyPriceMap.get(key).setSalePrice(supplyPriceMap.get(key).getSalePrice().add(supplyProductPricePO.getBasePrice()));
                    }
                }
            }

            //按次税费设置在首日,计算每日价格
            for (OrderFeeDetailDTO orderFeeDetailDTO : orderFeeDetailDTOList) {
                if (orderFeeDetailDTO.getSaleDate().equals(DateUtilX.dateToString(supplyOrderPO.getStartDate()))) {
                    orderFeeDetailDTO.setTimesTaxFeeList(timesTaxDTO);
                    orderFeeDetailDTO.setTimesTaxFee(BigDecimal.ZERO.
                            add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getExtraTaxFee())).
                            add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getTax())).
                            add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getSalesTax())));
                }
            }
        }

        for (PriceResponseDTO priceResponseDTO : supplyPriceMap.values()) {
            BigDecimal basePrice = priceResponseDTO.getBasePrice() == null ? new BigDecimal(0) : priceResponseDTO.getBasePrice();
            baseDateAndSalePriceMap.put(priceResponseDTO.getSaleDate(), basePrice);
            log.info("sumBasePrice============sumBasePrice={},basePrice={},supplyOrderRoomQty={}", sumBasePrice, basePrice, supplyOrderRoomQty);
            sumBasePrice = sumBasePrice.add(basePrice.multiply(BigDecimal.valueOf(supplyOrderRoomQty.doubleValue())));
        }

        SimpleDateFormat sdf = new SimpleDateFormat("MM/dd");
        SimpleDateFormat yearAndMonthAndDate = new SimpleDateFormat("yyyy-MM-dd");
        Integer logCount = 0;
        for (OrderCheckInfoDTO orderCheckInfoDTO : orderCheckInfoDTOList) {
            checkInDetailsLogs.append("入住人:")
                    .append(orderCheckInfoDTO.getGuestName() == null ? "null" : orderCheckInfoDTO.getGuestName())
                    .append(",房间号:")
                    .append(orderCheckInfoDTO.getRoomNumber() == null ? "null" : orderCheckInfoDTO.getRoomNumber())
                    .append(",入住情况:")
                    .append(CheckInstateEnum.getDescByName(orderCheckInfoDTO.getCheckInState()))
                    .append(",")
                    .append(yearAndMonthAndDate.format(orderCheckInfoDTO.getStartDate()))
                    .append("入住,")
                    .append(yearAndMonthAndDate.format(orderCheckInfoDTO.getEndDate()))
                    .append("离店,每日价格(房费|退订费)(");
            Integer checkInDetailListSize = orderCheckInfoDTO.getCheckInDetailList().size();
            Integer count = 0;
            for (OrderCheckDetailDTO orderCheckDetailDTO : orderCheckInfoDTO.getCheckInDetailList()) {
                sumAdditionalCharges = sumAdditionalCharges.add(orderCheckDetailDTO.getSalePrice() == null ? new BigDecimal(0) : orderCheckDetailDTO.getSalePrice())
                        .add(orderCheckDetailDTO.getRefundPrice() == null ? new BigDecimal(0) : orderCheckDetailDTO.getRefundPrice());
                checkInDetailsLogs.append(sdf.format(orderCheckDetailDTO.getSaleDate()))
                        .append(":")
                        .append(orderCheckDetailDTO.getSalePrice())
                        .append("|")
                        .append(orderCheckDetailDTO.getRefundPrice())
                        .append(";");
                logCount++;
                count++;
                if (logCount.equals(FIXED_LOG_COUNT)) {//达到指定数量，就生成一条日志记录
                    if (count.equals(checkInDetailListSize)) {//如果生成的刚好是最后一天则要补上“);"
                        checkInDetailsLogs.append(");");
                    }
                    logCount = 0;
                    if (!checkInDetailsLogsList.isEmpty()) {//非第一条日志,需要在日志前面补上"【接上条日志】"
                        checkInDetailsLogsList.add("【接入住明细日志】" + checkInDetailsLogs);
                        checkInDetailsLogs = new StringBuilder();
                    } else {
                        checkInDetailsLogsList.add(checkInDetailsLogs.toString());
                        checkInDetailsLogs = new StringBuilder();
                    }
                } else {
                    if (count.equals(checkInDetailListSize)) {//未达到指定数量，但是刚好是最后一天
                        checkInDetailsLogs.append(");");
                    }
                }
            }
        }
        if (StrUtilX.isNotEmpty(checkInDetailsLogs.toString())) {
            checkInDetailsLogsList.add(checkInDetailsLogs.toString());
        }

        //记录日志
        for (String checkInDetailsLog : checkInDetailsLogsList) {
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    loginUser.getUserName(),
                    loginUser.getUserAccount(),
                    supplyOrderPO.getSupplyOrderCode(),
                    checkInDetailsLog
            );
        }
        Map<String, BigDecimal> result = new HashMap<>();
        result.put("sumAdditionalCharges", sumAdditionalCharges);
        result.put("sumBasePrice", sumBasePrice);
        return result;
    }

    /**
     * 通知分销商
     */
    @Override
    public void notifyAgent(String orderCode) {
        try {
            Example example = new Example(OrderPO.class);
            example.createCriteria().andEqualTo("orderCode", orderCode);
            OrderPO orderPO = orderMapper.selectOneByExample(example);
            Map<String, Object> map = new HashMap<>();
            map.put("order_code", orderCode);
            List<OrderCheckInfoEntity> orderCheckInfoEntities = orderCheckInfoMapper.selectByMap(map);
            List<Integer> checkInfoIds = orderCheckInfoEntities.stream().map(OrderCheckInfoEntity::getId).collect(Collectors.toList());
            QueryWrapper<OrderCheckDetailEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in(CollUtilX.isNotEmpty(checkInfoIds), "check_info_id", checkInfoIds);
            List<OrderCheckDetailEntity> orderCheckDetailEntities = orderCheckDetailMapper.selectList(queryWrapper);
            OrderCheckDetailInfoRequest b2BOrderCheckDetails = assembleOrderDetails(orderCheckInfoEntities, orderCheckDetailEntities, orderPO);
            disB2BRemote.orderCheckDetailsNotify(b2BOrderCheckDetails);
        } catch (Exception e) {
            log.error("推送入住明细系统异常,error:", e);
        }
    }

    /**
     * 根据订单号查询入住明细
     *
     * @param orderCode
     * @return
     */
    @Override
    public List<OrderCheckInfoDTO> queryOrderCheckDetail(String orderCode) {
        return orderCheckInfoMapper.orderCheckInfoList(orderCode);
    }

    /**
     * 根据订单号查询入住明细
     *
     * @param orderCheckInfoRequest
     * @return
     */
    @Override
    public List<OrderCheckInfoDTO> queryOrderCheckDetailDhub(OrderCheckInfoRequest orderCheckInfoRequest) {
        // 查询订单是否存在
        OrderPO queryOrder = new OrderPO();
        if (StrUtilX.isNotEmpty(orderCheckInfoRequest.getFcOrderCode())) {
            queryOrder.setOrderCode(orderCheckInfoRequest.getFcOrderCode());
        }
        if (StrUtilX.isNotEmpty(orderCheckInfoRequest.getCoOrderCode())) {
            queryOrder.setChannelOrderCode(orderCheckInfoRequest.getCoOrderCode());
        }
        if (StrUtilX.isNotEmpty(orderCheckInfoRequest.getAgentCode())) {
            queryOrder.setAgentCode(orderCheckInfoRequest.getAgentCode());
        }
        if (StrUtilX.isNotEmpty(orderCheckInfoRequest.getChannelCode())) {
            queryOrder.setChannelCode(orderCheckInfoRequest.getChannelCode());
        }
        OrderPO orderPO = orderMapper.selectOne(queryOrder);
        if (orderPO == null) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }
        return orderCheckInfoMapper.orderCheckInfoListDhub(orderCheckInfoRequest);
    }

    @Override
    public PaginationSupportDTO<OrderCheckPushInfoPO> queryOrderCheckPushInfoPage(OrderCheckPushInfoDTO orderCheckPushInfoDTO) {
        IPage<OrderCheckPushInfoPO> page = new Page<>(orderCheckPushInfoDTO.getCurrentPage(), orderCheckPushInfoDTO.getPageSize());
        IPage<OrderCheckPushInfoPO> checkPushInfoPOList = orderCheckPushInfoMapper.queryOrderCheckPushInfoPage(page, orderCheckPushInfoDTO);

        PaginationSupportDTO<OrderCheckPushInfoPO> paginationSupport = new PaginationSupportDTO<>();
        PaginationSupportDTO paginationSupportDTO = paginationSupport.getPaginationSupportDTO(checkPushInfoPOList);
        return paginationSupportDTO;
    }

    /**
     * 组装订单入住
     *
     * @param order
     * @return
     */
    private OrderCheckDetailInfoRequest assembleOrderDetails(List<OrderCheckInfoEntity> orderCheckInfoEntities, List<OrderCheckDetailEntity> orderCheckDetailEntities, OrderPO order) {
        String orderCheckInState = "0";
        OrderCheckDetailInfoRequest orderCheckDetailInfoRequest = new OrderCheckDetailInfoRequest();
        orderCheckDetailInfoRequest.setAgentCode(order.getAgentCode());
        orderCheckDetailInfoRequest.setFcOrderCode(order.getOrderCode());
        orderCheckDetailInfoRequest.setCoOrderCode(order.getChannelOrderCode());
        orderCheckDetailInfoRequest.setOrderSalePrice(order.getOrderAmt());
        List<String> checkInStates = Lists.newArrayList();
        if (CollUtilX.isEmpty(orderCheckDetailEntities)) {
            orderCheckDetailInfoRequest.setOrderCheckInState("0");
            return orderCheckDetailInfoRequest;
        }
        BigDecimal sumAdditionalCharges = new BigDecimal(0);
        for (OrderCheckDetailEntity orderCheckDetailEntity : orderCheckDetailEntities) {
            sumAdditionalCharges = sumAdditionalCharges.add(orderCheckDetailEntity.getSalePrice() == null ? new BigDecimal(0) : orderCheckDetailEntity.getSalePrice())
                    .add(orderCheckDetailEntity.getRefundPrice() == null ? new BigDecimal(0) : orderCheckDetailEntity.getRefundPrice());
        }
        Map<Integer, List<OrderCheckDetailEntity>> map = orderCheckDetailEntities.stream().collect(Collectors.groupingBy(OrderCheckDetailEntity::getCheckInfoId));
        List<OrderCheckDetails> checkDetails = orderCheckInfoEntities.stream().map(orderCheckInfoEntity ->
        {
            OrderCheckDetails orderCheckDetails = new OrderCheckDetails();
            orderCheckDetails.setCheckInDate(DateUtilX.dateToString(orderCheckInfoEntity.getStartDate()));
            orderCheckDetails.setCheckOutDate(DateUtilX.dateToString(orderCheckInfoEntity.getEndDate()));
            orderCheckDetails.setRoomNumber(orderCheckInfoEntity.getRoomNumber());
            orderCheckDetails.setCheckInState(CheckInstateEnum.getValueByName(orderCheckInfoEntity.getCheckInState()));
            checkInStates.add(orderCheckInfoEntity.getCheckInState());
            if (StrUtilX.isNotEmpty(orderCheckInfoEntity.getGuestName())) {
                String[] split = orderCheckInfoEntity.getGuestName().split(",");
                List<GuestInfo> guestInfos = Arrays.stream(split).map(name ->
                {
                    GuestInfo guestInfo = new GuestInfo();
                    guestInfo.setGuestName(name);
                    return guestInfo;
                }).collect(Collectors.toList());
                orderCheckDetails.setGuestInfos(guestInfos);
            }
            if (MapUtils.isNotEmpty(map) && Objects.nonNull(map.get(orderCheckInfoEntity.getId()))) {
                List<OrderCheckDetailEntity> checkDetailEntities = map.get(orderCheckInfoEntity.getId());
                List<OrderCheckDetailPriceItem> priceItems = checkDetailEntities.stream().map(detail ->
                {
                    OrderCheckDetailPriceItem orderCheckDetailPriceItem = new OrderCheckDetailPriceItem();
                    orderCheckDetailPriceItem.setSaleDate(DateUtilX.dateToString(detail.getSaleDate()));
                    orderCheckDetailPriceItem.setSalePrice(detail.getSalePrice());
                    orderCheckDetailPriceItem.setRefundPrice(detail.getRefundPrice());
                    return orderCheckDetailPriceItem;
                }).collect(Collectors.toList());
                orderCheckDetails.setPriceItems(priceItems);
            }
            return orderCheckDetails;
        }).collect(Collectors.toList());
        if (sumAdditionalCharges.compareTo(order.getOrderAmt()) == 0) {
            orderCheckInState = "1";
        } else if (sumAdditionalCharges.compareTo(order.getOrderAmt()) != 0) {
            orderCheckInState = "2";
        }
        orderCheckDetailInfoRequest.setOrderCheckInState(orderCheckInState);
        orderCheckDetailInfoRequest.setCheckDetails(checkDetails);
        return orderCheckDetailInfoRequest;
    }

    /**
     * 订单入住信息列表,按订单编号查询
     */
    @Override
    public List<OrderCheckInfoDTO> orderCheckInfoList(String orderCode) {
        return orderCheckInfoMapper.orderCheckInfoList(orderCode);
    }

}