package com.tiangong.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.constanct.MqConstants;
import com.tiangong.dis.dto.ProductBasePriceAndRoomStatusDTO;
import com.tiangong.dis.dto.ProductRestrictDTO;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.hotel.base.RoomtypeDTO;
import com.tiangong.dto.order.*;
import com.tiangong.dto.order.CancelPenalties;
import com.tiangong.dto.order.CancelRestriction;
import com.tiangong.dto.order.request.PriceRequestDTO;
import com.tiangong.dto.product.*;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.response.TipInfosDTO;
import com.tiangong.enums.*;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.CheckStatusEnum;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.*;
import com.tiangong.order.dto.*;
import com.tiangong.order.dto.OrderCodeDTO;
import com.tiangong.order.enums.*;
import com.tiangong.order.mapper.*;
import com.tiangong.order.remote.dto.PreBookDTO;
import com.tiangong.order.remote.dto.SupplierListDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.OrderInvoiceDTO;
import com.tiangong.order.remote.response.OtaOrderDTO;
import com.tiangong.order.service.BookingService;
import com.tiangong.order.service.OrderProductTipService;
import com.tiangong.order.service.OrderService;
import com.tiangong.order.service.SupplyOrderService;
import com.tiangong.order.service.common.OrderCommonService;
import com.tiangong.order.util.DistributedCodeGenerator;
import com.tiangong.order.util.GenerateAdditionalChargesGroupNumber;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.product.dto.*;
import com.tiangong.product.remote.ProductRemote;
import com.tiangong.product.remote.ProductSaleRemote;
import com.tiangong.product.remote.QuotaRemote;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.remote.OrderNotifyRemote;
import com.tiangong.request.OrderNotifyMessageReq;
import com.tiangong.supply.direct.remote.SupplyDirectShubOrderRemote;
import com.tiangong.supply.direct.remote.SupplyDirectShubProductRemote;
import com.tiangong.supply.direct.remote.dto.*;
import com.tiangong.supply.direct.remote.request.ChildrenInfo;
import com.tiangong.supply.direct.remote.request.PreBookingRequest;
import com.tiangong.supply.direct.remote.request.QueryProductInfoRequest;
import com.tiangong.supply.direct.remote.request.RoomGuestNumber;
import com.tiangong.supply.direct.remote.response.PreBookingResponse;
import com.tiangong.util.*;
import com.tiangong.vip.dto.AgentVipClientLevelResultDTO;
import com.tiangong.vip.dto.AgentVipClientResultDTO;
import com.tiangong.vip.remote.AgentVipClientRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.tiangong.util.DateUtilX.hour_format;


@Service
@Slf4j
public class BookingServiceImpl implements BookingService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private GuestMapper guestMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private OrderExtendMapper orderExtendMapper;

    @Autowired
    private OrderProductPriceMapper orderProductPriceMapper;

    @Autowired
    private SupplyOrderMapper supplyOrderMapper;

    @Autowired
    private SupplyProductMapper supplyProductMapper;

    @Autowired
    private SupplyProductPriceMapper supplyProductPriceMapper;

    @Autowired
    private OrderFinanceMapper orderFinanceMapper;

    @Autowired
    private SupplyOrderFinanceMapper supplyOrderFinanceMapper;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private QuotaRemote quotaRemote;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ProductSaleRemote productSaleRemote;

    @Autowired
    private ProductRemote productRemote;

    @Autowired
    private OrderService orderService;

    @Autowired
    private SupplyOrderService supplyOrderService;

//    @Autowired
//    private SupplyDirectOrderRemote supplyDirectOrderRemote;
//
//    @Autowired
//    private SupplyDirectProductRemote supplyDirectProductRemote;

    @Autowired
    private SupplyDirectShubOrderRemote supplyDirectShubOrderRemote;

    @Autowired
    private SupplyDirectShubProductRemote supplyDirectShubProductRemote;

    @Autowired
    private OrderRestrictMapper orderRestrictMapper;

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private AgentCreditLineMapper agentCreditLineMapper;

    @Autowired
    private IncreaseUtil increaseUtil;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private OrderNotifyRemote orderNotifyRemote;

    @Autowired
    private OrderProductTipService orderProductTipService;

    @Autowired
    private GenerateAdditionalChargesGroupNumber generateAdditionalChargesGroupNumber;

    @Autowired
    private OrderAdditionalChargesMapper orderAdditionalChargesMapper;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private AgentVipClientRemote vipClientRemote;

    @Autowired
    private AgentRemote agentRemote;

    @Autowired
    private SupplyOrderNonVccAutoBillInfoMapper supplyOrderNonVccAutoBillInfoMapper;

    @Override
    @Transactional
    public String addManualOrder(AddManualOrderDTO request) {
        if (request.getStartDate().equals(request.getEndDate())) {
            throw new SysException(ErrorCodeEnum.CHECK_DATE_ERROR);
        }
        Integer existOrderIdCount = orderMapper.checkExistOrder(request);

        if (existOrderIdCount > 0) {
            throw new SysException(ErrorCodeEnum.EXIST_ORDER);
        }

        //查询分销商
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID);
        }
        //组装订单数据
        AssemblyOrderDTO assemblyOrderDTO = this.assemblyOrderData(request, agentAccountConfig.getSettlementType(), agentAccountConfig);
        //非凌晨房订单
        if (assemblyOrderDTO.getOrder() != null) {
            assemblyOrderDTO.getOrder().setIsEarlyMorningRoomOrder(0);
        }
        //保存订单
        Integer orderId = this.saveOrder(assemblyOrderDTO, false);
        if (orderId <= 0) {
            throw new SysException(ErrorCodeEnum.CREATE_ORDER_IS_ERROR);
        }
        //保配额
        try {
            // 2019/5/6 调扣配额接口
            if (StrUtilX.isNotEmpty(request.getProductId())) {
                QuotaDTO quotaDTO = new QuotaDTO();
                quotaDTO.setOrderCode(assemblyOrderDTO.getOrder().getOrderCode());
                quotaDTO.setOrderId(orderId);
                quotaDTO.setProductId(Integer.valueOf(request.getProductId()));
                quotaDTO.setSupplierCode(request.getSupplierCode());
                quotaDTO.setQuota(-request.getRoomQty());//扣配额
                quotaDTO.setSupplyOrderCode(assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getSupplyOrderCode());
                quotaDTO.setSupplyOrderId(assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getId());
                Map<String, String> map = new HashMap<>();
                map.put("begin", request.getStartDate());
                map.put("end", request.getEndDate());
                List<String> list = orderMapper.queryBetweenDate(map);
                StringBuilder saleDate = new StringBuilder();
                for (String s : list) {
                    saleDate.append(s).append(",");
                }
                quotaDTO.setSaleDate(saleDate.substring(0, saleDate.length() - 1));
                quotaRemote.modifyQuota(quotaDTO);
            }
        } catch (Exception e) {
            log.error("扣配额异常！订单ID：" + orderId, e);
        }

        OrderPO orderPO = orderMapper.selectByPrimaryKey(orderId);

        // 非单结自动扣额度
        if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key && !PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
            AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
            if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
            } else {
                agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
            }
            agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
            agentCreditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt().negate());
            agentCreditLineDTO.setCreatedBy(request.getOperator());
            Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
            if (!creditLineResponse.isSuccess()) {
                throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
            }
        }

        //message.append("all/订单号：").append(orderPO.getOrderCode()+"<br>");
        String message = "客户-" + agentAccountConfig.getAgentName() +
                "下了一条" + request.getStartDate() + "入住" + request.getHotelName() +
                request.getRoomQty() + "间" + DateUtilX.getDay(orderPO.getStartDate(), orderPO.getEndDate()) + "晚的订单,请尽快处理!";

        Map<String, Object> content = new HashMap<>();
        content.put("orderCode", orderPO.getOrderCode());
        content.put("content", message);
        content.put("messageType", MessageTypeEnum.NEW_ORDER.key);
        JSONObject json = new JSONObject(content);
        stringRedisTemplate.convertAndSend(RedisKey.orderInfoKey, request.getCompanyCode() + "/" + JSON.toJSONString(json));
//        /**如果开通自动发单服务直接发单*/
//        try {
//            asyncSendOrder(assemblyOrderDTO);
//        }catch (Exception e){
//            log.error("自动发单功能异常",e);
//            response.setFailReason("自动发单功能异常");
//        }
        return orderPO.getOrderCode();
    }

    /**
     * 新建OTA订单
     */
    @Override
    @Transactional
    public String addOTAOrder(AddManualOrderDTO request) {
        Integer existOrderIdCount = orderMapper.checkExistOrder(request);

        if (existOrderIdCount > 0) {
            throw new SysException(ErrorCodeEnum.EXIST_ORDER);
        }

        //查询分销商--从缓存里面获取
        String agent = (String) RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode());
        AgentAccountConfig agentAccountConfig = JSON.parseObject(agent, AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.CHANNEL_IS_NOT_EXIST);
        }

        request.setAgentCode(agentAccountConfig.getAgentCode());
        request.setAgentName(agentAccountConfig.getAgentName());

        // 校验入住日期
        boolean weeHoursFlag = checkCheckInDate(request.getSupplierCode(), request.getStartDate());

        // 调用产品接口，查询底价
        ProductSalePriceDTO productSalePriceDTO = null;
        String supplierCode = null;
        Map<String, String> product = new HashMap<>();
        product.put("productId", request.getProductId());
        // 查询本地数据
        Response<ProductDTO> productResp = productRemote.queryProduct(product);
        ProductDTO productInfo = null;
        if (productResp.isSuccess() && Objects.nonNull(productResp.getModel())) {
            productInfo = productResp.getModel();
            QueryProductRequestDTO queryProductRequestDTO = new QueryProductRequestDTO();
            queryProductRequestDTO.setProductId(request.getProductId());
            queryProductRequestDTO.setStartDate(request.getStartDate());
            queryProductRequestDTO.setEndDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(request.getEndDate()), -1, 0)));
            queryProductRequestDTO.setCompanyCode(request.getCompanyCode());
            queryProductRequestDTO.setChannelCode(request.getChannelCode());
            queryProductRequestDTO.setAgentCode(request.getAgentCode());
            queryProductRequestDTO.setLanguage(request.getLanguage());
            Response<ProductSalePriceDetailDTO> productResponse = productSaleRemote.querySalePriceList(queryProductRequestDTO);

            if (null != productResponse && productResponse.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != productResponse.getModel()) {
                productSalePriceDTO = ProductSalePriceConvert.INSTANCE.convert(productResponse.getModel());
                if (CollectionUtil.isEmpty(productSalePriceDTO.getRoomItemDetails())) {
                    List<com.tiangong.dto.order.response.OrderRoomDetailDto> orderRoomDetailDtos = convertOrderRoomDetail2(request.getRoomGuestList(), productResponse.getModel(), productSalePriceDTO);
                    productSalePriceDTO.setRoomItemDetails(orderRoomDetailDtos);
                }
                if (CollUtilX.isNotEmpty(productSalePriceDTO.getCancelRestrictions()) && ChannelEnum.DHUB.key.equals(request.getChannelCode())) {
                    for (CancelRestriction cancelRestriction : productSalePriceDTO.getCancelRestrictions()) {
                        if (cancelRestriction.getCancelPenaltiesType() != null && (PenaltiesTypeEnum.NIGHTS.getKey().equals(cancelRestriction.getCancelPenaltiesType()) || PenaltiesTypeEnum.FIRSTNIGHTPERCENTAGE.getKey().equals(cancelRestriction.getCancelPenaltiesType()))) {
                            // 当罚金类型为首晚、首晚百分比时，转换取消类型为3
                            cancelRestriction.setCancelRestrictionType(3);
                            cancelRestriction.setCancelPenalties(null);
                        }
                    }
                }
                supplierCode = productSalePriceDTO.getSupplierCode();
            }

            if (productSalePriceDTO == null || CollUtilX.isEmpty(productSalePriceDTO.getPriceList())) {
                throw new SysException(ErrorCodeEnum.PRODUCT_IS_NOT_EXIST);
            }
        } else {
            // 供应商直连订单
            QueryProductInfoRequest queryProductInfoRequest = new QueryProductInfoRequest();
            queryProductInfoRequest.setSpHotelId(String.valueOf(request.getHotelId()));
            queryProductInfoRequest.setGuestQuantity(request.getAdultQty());
            queryProductInfoRequest.setCheckInDate(request.getStartDate());
            queryProductInfoRequest.setCheckOutDate(request.getEndDate());
            queryProductInfoRequest.setSupplyCodes(Collections.singleton(request.getSupplierCode()));
            queryProductInfoRequest.setSpRoomId(request.getRoomId());
            queryProductInfoRequest.setRoomQty(request.getRoomQty());
            queryProductInfoRequest.setSpProductId(request.getProductId());
            queryProductInfoRequest.setSupplyType(request.getSupplyType());
            List<RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
            for (OrderRoomGuestDTO orderRoomGuestDTO : request.getRoomGuestList()) {
                RoomGuestNumber roomGuestNumber = new RoomGuestNumber();
                roomGuestNumber.setRoomIndex(orderRoomGuestDTO.getRoomNumber());
                roomGuestNumber.setAdultNum(orderRoomGuestDTO.getAdultQty());
                if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                    List<ChildrenInfo> childrenInfoList = new ArrayList<>();
                    String[] ageArr = orderRoomGuestDTO.getChildrenAge().split(",");
                    for (String age : ageArr) {
                        ChildrenInfo childrenInfo = new ChildrenInfo();
                        childrenInfo.setChildrenAge(Integer.valueOf(age));
                        childrenInfoList.add(childrenInfo);
                    }
                    roomGuestNumber.setChildrenInfos(childrenInfoList);
                }
                roomGuestNumbers.add(roomGuestNumber);
            }
            queryProductInfoRequest.setRoomGuestNumbers(roomGuestNumbers);
            if (StrUtilX.isNotEmpty(request.getHourlyTime())) {
                queryProductInfoRequest.setOnlyHourRoom(1);
            }
            queryProductInfoRequest.setRequestId(request.getRequestId());
            queryProductInfoRequest.setCurrency(agentAccountConfig.getSettlementCurrency());
            queryProductInfoRequest.setNationality(request.getNationality());
            Response<List<ProductMiddleDto>> queryProductResponse = supplyDirectShubProductRemote.queryProductInfo(queryProductInfoRequest);
            if (queryProductResponse.isSuccess() && CollUtilX.isNotEmpty(queryProductResponse.getModel())) {
                List<ProductMiddleDto> productMiddles = queryProductResponse.getModel();
                productSalePriceDTO = assemblySupplyProduct(productMiddles, request, agentAccountConfig);
            } else {
                throw new SysException(ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT);
            }
        }


        if (StrUtilX.isNotEmpty(request.getChannelCode()) && request.getChannelCode().equals(ChannelEnum.B2B.key) && StrUtilX.isEmpty(request.getOperator())) {
            request.setOperator(request.getAgentName());
        } else if (!(StrUtilX.isNotEmpty(request.getChannelCode()) && request.getChannelCode().equals(ChannelEnum.B2B.key) && StrUtilX.isNotEmpty(request.getOperator()))) {
            /**
             * 在dhub 创建的订单，之前是设置成Dhub，现在改成了传入的参数中的订单联系人，对应Dhub的创建订单接口中请求对象中的属性：linkMan，在这里转化成了contactName，
             * else 里面的是原来的逻辑 ， if 条件下是在不影响原来逻辑上写的  ,created_by 值的设置是在组装参数时是根据  request 中的operator进行设置的
             */
            if(request.getChannelCode().equals(ChannelEnum.getValueByKey(request.getChannelCode()))){
                request.setOperator(request.getContactName());
            }else {
                request.setOperator(ChannelEnum.getValueByKey(request.getChannelCode()));
            }
        }
        boolean equalDateFlag = false;
        if (StrUtilX.isNotEmpty(request.getHourlyTime())) {//钟点房的结束时间为和入住时间一致
            String[] arr = request.getHourlyTime().split("\\--");
            if (request.getStartDate().equals(arr[1].substring(0, 10))) {//代表当天
                equalDateFlag = true;
            }
        }
        if (Objects.isNull(request.getPayMethod())) {
            if (productSalePriceDTO.getPayAtHotelFlag() == null) {
                request.setPayMethod(PayMethodEnum.PREPAY.key);
            } else {
                request.setPayMethod(productSalePriceDTO.getPayAtHotelFlag());
            }
        } else {
            if (request.getPayMethod().intValue() != productSalePriceDTO.getPayAtHotelFlag()) {
                log.error("订单支付类型错误!");
                throw new SysException(ErrorCodeEnum.ORDER_PAY_METHOD_IS_ERROR);
            }
        }
        if (SettlementTypeEnum.SINGLE.key != agentAccountConfig.getSettlementType() && request.getPayMethod() != 1) {//不是单结,判断剩余额度是否足够
            BigDecimal balance = agentAccountConfig.getBalance();
            if (CommonTgUtils.formatBigDecimal(request.getOrderAmt()).compareTo(CommonTgUtils.formatBigDecimal(balance)) > 0) {//订单金额小于信用额，提示信用额不足
                log.error("信用额度不足,无法下单!");
                throw new SysException(ErrorCodeEnum.INSUFFICIENT_AMOUNT);
            }
        }

        // 查询酒店基本信息
        HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
        hotelInfoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
        hotelInfoReq.setLanguageType(request.getLanguage());
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.location);
        settings.add(BaseHotelInfoUrl.rooms);
        hotelInfoReq.setSettings(settings);
        hotelInfoReq.setRequestId(request.getRequestId());
        Response<HotelInfoCollectionDTO> hotelInfoResponse = hotelRemote.queryHotelInfo(hotelInfoReq);

        //组装订单数据
        AssemblyOrderDTO assemblyOrderDTO = this.assemblyOTAOrderData(request, productSalePriceDTO, agentAccountConfig, hotelInfoResponse.getModel());
        // 判断是否是凌晨房
        if (weeHoursFlag) {
            assemblyOrderDTO.getOrder().setIsEarlyMorningRoomOrder(1);
        }
        //保存订单
        Integer orderId = this.saveOrder(assemblyOrderDTO, equalDateFlag);
        if (orderId <= 0) {
            throw new SysException(ErrorCodeEnum.CREATE_ORDER_IS_ERROR);
        }
        //保配额
        //['自签','酒店','API']
        if (productResp.isSuccess() && Objects.nonNull(productInfo)) {
            try {
                // 调扣配额接口
                if (request.getProductId() != null) {
                    QuotaDTO quotaDTO = new QuotaDTO();
                    quotaDTO.setOrderCode(assemblyOrderDTO.getOrder().getOrderCode());
                    quotaDTO.setOrderId(orderId);
                    quotaDTO.setProductId(Integer.valueOf(request.getProductId()));
                    quotaDTO.setSupplierCode(supplierCode);
                    quotaDTO.setQuota(-request.getRoomQty());//扣配额
                    quotaDTO.setSupplyOrderCode(assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getSupplyOrderCode());
                    quotaDTO.setSupplyOrderId(assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getId());
                    Map<String, String> map = new HashMap<>();
                    map.put("begin", request.getStartDate());
                    map.put("end", request.getEndDate());
                    List<String> list = orderMapper.queryBetweenDate(map);
                    StringBuilder saleDate = new StringBuilder();
                    for (String s : list) {
                        saleDate.append(s).append(",");
                    }
                    quotaDTO.setSaleDate(saleDate.substring(0, saleDate.length() - 1));
                    quotaRemote.modifyQuota(quotaDTO);
                }
            } catch (Exception e) {
                log.error("扣配额异常！订单ID：" + orderId, e);
            }
        }

        OrderPO orderPO = orderMapper.selectByPrimaryKey(orderId);

        // 保存发票信息
        saveOrderInvoice(orderPO.getOrderCode(), request.getOrderInvoiceDTO(), request.getOperator());

        // 非单结自动扣额度
        if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key && !PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
            AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
            if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
            } else {
                agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
            }
            agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
            agentCreditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt().negate());
            agentCreditLineDTO.setCreatedBy(request.getOperator());
            Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
            if (!creditLineResponse.isSuccess()) {
                throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
            }
        }
        //推送消息提醒
        String message = "客户-" + agentAccountConfig.getAgentName() +
                "企业-" + assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getSupplierName() +
                "下了一条" + request.getStartDate() + "入住" + productSalePriceDTO.getHotelName() +
                request.getRoomQty() + "间" + DateUtilX.getDay(orderPO.getStartDate(), orderPO.getEndDate()) + "晚的订单,请尽快处理!";

        Map<String, Object> content = new HashMap<>();
        content.put("orderCode", orderPO.getOrderCode());
        content.put("content", message);
        content.put("messageType", MessageTypeEnum.NEW_ORDER.key);
        JSONObject json = new JSONObject(content);
        stringRedisTemplate.convertAndSend(RedisKey.orderInfoKey, request.getCompanyCode() + "/" + JSON.toJSONString(json));

        // 推送消息到前端进行弹窗提醒
        CompletableFuture.runAsync(() -> {
            StringBuilder guestInfos = new StringBuilder();
            if (CollUtilX.isNotEmpty(assemblyOrderDTO.getGuestList())) {
                for (GuestPO guestPO : assemblyOrderDTO.getGuestList()) {
                    guestInfos.append(guestPO.getName()).append(",");
                }
                guestInfos.deleteCharAt(guestInfos.length() - 1);
            }
            String checkInDate = null;
            if (orderPO.getStartDate() != null) {
                checkInDate = DateUtilX.dateToString(orderPO.getStartDate(), "yyyy-MM-dd");
            }
            double nightQty = 0;
            if (orderPO.getStartDate() != null && orderPO.getEndDate() != null) {
                nightQty = (double) DateUtilX.getDay(orderPO.getStartDate(), orderPO.getEndDate());
            }
            if (orderPO.getHourly() == 1) {
                nightQty = 0.5;
            }
            OrderNotifyMessageReq orderNotifyMessageReq = OrderNotifyMessageReq.builder()
                    .orderNotifyType(OrderNotifyType.NEW_ORDER.getType())
                    .orderCode(orderPO.getOrderCode())
                    .agentName(orderPO.getAgentName())
                    .guestInfos(guestInfos.toString())
                    .checkInDate(checkInDate)
                    .roomQty(orderPO.getRoomQty())
                    .nightQty(nightQty)
                    .build();
            orderNotifyRemote.sendOrderNotifyMessageToAllUsers(orderNotifyMessageReq);
        });

        // 处理支付超时自动取消
        handlePaymentOvertimeCancel(orderPO);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 如果开通自动发单服务直接发单
                try {
                    if (orderPO.getIsAbnormal() == null || orderPO.getIsAbnormal() == 0) {
                        if (request.getPayStatus() != null && request.getPayStatus().equals(PayStatusEnum.PAY.getNo())) {
                            assemblyOrderDTO.setRequestId(request.getRequestId());
                            asyncSendOrder(assemblyOrderDTO);
                        }
                    } else {
                        log.info("问题单，orderCode={}，isAbnormal={}", orderPO.getOrderCode(), orderPO.getIsAbnormal());
                    }
                } catch (Exception e) {
                    log.error("自动发单功能异常", e);
                }
            }
        });
        return orderPO.getOrderCode();
    }

    /**
     * 校验入住日期
     *
     * @param supplyCode  供应商编码
     * @param checkInDate 入住日期
     * @return 是否是凌晨房
     */
    private boolean checkCheckInDate(String supplyCode, String checkInDate) {
        // 查询供应商缓存
        AddSupplierReq supplier = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplyCode).toString(), AddSupplierReq.class);
        if (supplier == null) {
            log.error("供应商缓存不存在，supplyCode={}", supplyCode);
            throw new SysException(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST);
        }
        // 比较当前时间
        Date currentDate = DateUtilX.getCurrentDate();
        long day = DateUtilX.getDay(DateUtilX.stringToDate(checkInDate), currentDate);
        if (day > 1) {
            throw new SysException(ErrorCodeEnum.CHECK_DATE_ERROR);
        } else if (day == 1) {
            // 不是凌晨房不能预订昨天数据
            if (supplier.getIsSupportEarlyMorningRoomOrder() == null || supplier.getIsSupportEarlyMorningRoomOrder() == 0) {
                throw new SysException(ErrorCodeEnum.CHECK_DATE_ERROR);
            } else {
                // 获取当前时间
                LocalTime now = LocalTime.now();
                // 定义时间范围的开始和结束时间
                LocalTime startTime = LocalTime.MIDNIGHT;// 0点
                LocalTime endTime = LocalTime.of(6, 0);// 6点

                // 判断当前时间是否在时间范围内
                boolean isInTimeRange = now.isAfter(startTime) && now.isBefore(endTime);
                if (!isInTimeRange) {
                    throw new SysException(ErrorCodeEnum.CHECK_DATE_ERROR);
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    @Transactional
    public OrderCodeDTO addHelpOrder(AddManualOrderDTO request) {
        Integer existOrderIdCount = orderMapper.checkExistOrder(request);

        if (existOrderIdCount > 0) {
            throw new SysException(ErrorCodeEnum.EXIST_ORDER);
        }

        // 查询分销商
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID);
        }
        if (SettlementTypeEnum.SINGLE.key != agentAccountConfig.getSettlementType()) {//单结,判断剩余额度是否足够
            BigDecimal balance = agentAccountConfig.getBalance();
            if (request.getOrderAmt().compareTo(balance) > 0) {//订单金额小于信用额，提示信用额不足
                log.error("信用额度不足,无法下单!");
                throw new SysException(ErrorCodeEnum.INSUFFICIENT_AMOUNT);
            }
        }

        // 调用产品接口，查询底价
        ProductSalePriceDTO productSalePriceDTO = null;
        String supplierCode = null;
        Map<String, String> product = new HashMap<>();
        product.put("productId", request.getProductId());
        Response<ProductDTO> product1 = productRemote.queryProduct(product);
        if (product1.getResult() != null && product1.getResult().equals(ResultCodeEnum.SUCCESS.code) && Objects.nonNull(product1.getModel())) {
            QueryProductRequestDTO queryProductRequestDTO = new QueryProductRequestDTO();
            queryProductRequestDTO.setProductId(request.getProductId());
            queryProductRequestDTO.setStartDate(request.getStartDate());
            queryProductRequestDTO.setEndDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(request.getEndDate()), -1, 0)));
            queryProductRequestDTO.setCompanyCode(request.getCompanyCode());
            queryProductRequestDTO.setChannelCode(request.getChannelCode());
            queryProductRequestDTO.setLanguage(request.getLanguage());
            Response<ProductSalePriceDetailDTO> productResponse = productSaleRemote.querySalePriceList(queryProductRequestDTO);

            if (null != productResponse && productResponse.getResult().equals(ResultCodeEnum.SUCCESS.code)
                    && null != productResponse.getModel()) {
                productSalePriceDTO = ProductSalePriceConvert.INSTANCE.convert(productResponse.getModel());
                if (CollectionUtil.isEmpty(productSalePriceDTO.getRoomItemDetails())) {
                    List<com.tiangong.dto.order.response.OrderRoomDetailDto> orderRoomDetailDtos = convertOrderRoomDetail2(request.getRoomGuestList(), productResponse.getModel(), productSalePriceDTO);
                    productSalePriceDTO.setRoomItemDetails(orderRoomDetailDtos);
                }
                supplierCode = productSalePriceDTO.getSupplierCode();
            }

            if (productSalePriceDTO == null || CollUtilX.isEmpty(productSalePriceDTO.getPriceList())) {
                throw new SysException(ErrorCodeEnum.PRODUCT_IS_NOT_EXIST);
            }
        } else {
            // 供应商直连订单
            QueryProductInfoRequest queryProductInfoRequest = new QueryProductInfoRequest();
            queryProductInfoRequest.setSpHotelId(String.valueOf(request.getHotelId()));
            queryProductInfoRequest.setGuestQuantity(request.getAdultQty());
            queryProductInfoRequest.setCheckInDate(request.getStartDate());
            if (StrUtilX.isNotEmpty(request.getHourlyTime())) {
                queryProductInfoRequest.setOnlyHourRoom(1);
            }
            queryProductInfoRequest.setCheckOutDate(request.getEndDate());
            queryProductInfoRequest.setSupplyCodes(Collections.singleton(request.getSupplierCode()));
            queryProductInfoRequest.setSpRoomId(request.getRoomId());
            queryProductInfoRequest.setRoomQty(request.getRoomQty());
            queryProductInfoRequest.setSpProductId(request.getProductId());
            queryProductInfoRequest.setRequestId(request.getRequestId());
            queryProductInfoRequest.setCurrency(agentAccountConfig.getSettlementCurrency());
            Response<List<ProductMiddleDto>> queryProductResponse = supplyDirectShubProductRemote.queryProductInfo(queryProductInfoRequest);
            if (Objects.nonNull(queryProductResponse) && Objects.equals(queryProductResponse.getResult(), ResultCodeEnum.SUCCESS.code)) {
                productSalePriceDTO = assemblySupplyProduct(queryProductResponse.getModel(), request, agentAccountConfig);
            } else {
                throw new SysException(ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT);
            }
        }
        if (StrUtilX.isNotEmpty(request.getChannelCode()) && request.getChannelCode().equals(ChannelEnum.B2B.key) && StrUtilX.isEmpty(request.getOperator())) {
            request.setOperator(request.getAgentName());
        } else if (!(StrUtilX.isNotEmpty(request.getChannelCode()) && request.getChannelCode().equals(ChannelEnum.B2B.key) && StrUtilX.isNotEmpty(request.getOperator()))) {
            request.setOperator(ChannelEnum.getValueByKey(request.getChannelCode()));
        }
        boolean equalDateFlag = false;
        if (StrUtilX.isNotEmpty(request.getHourlyTime())) {//钟点房的结束时间为和入住时间一致
            String[] arr = request.getHourlyTime().split("\\--");
            if (request.getStartDate().equals(arr[1].substring(0, 10))) {//代表当天
                equalDateFlag = true;
            }
        }

        // 查询酒店基本信息
        HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
        hotelInfoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
        hotelInfoReq.setLanguageType(request.getLanguage());
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.location);
        settings.add(BaseHotelInfoUrl.rooms);
        hotelInfoReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> hotelInfoResponse = hotelRemote.queryHotelInfo(hotelInfoReq);

        //组装订单数据
        AssemblyOrderDTO assemblyOrderDTO = this.assemblyOTAOrderData(request, productSalePriceDTO, agentAccountConfig, hotelInfoResponse.getModel());
        //凌晨房订单
        LocalDate today = LocalDate.now();
        LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (today.isAfter(startDate)) {
            if (assemblyOrderDTO.getOrder() != null) {
                assemblyOrderDTO.getOrder().setIsEarlyMorningRoomOrder(1);
            }
        }
        //保存订单
        Integer orderId = this.saveOrder(assemblyOrderDTO, equalDateFlag);
        if (orderId <= 0) {
            throw new SysException(ErrorCodeEnum.CREATE_ORDER_IS_ERROR);
        }
        //保配额
        if (product1.getResult() != null && product1.getResult().equals(ResultCodeEnum.SUCCESS.code) && Objects.nonNull(product1.getModel())) {
            try {
                // 2019/5/6 调扣配额接口
                if (request.getProductId() != null) {
                    QuotaDTO quotaDTO = new QuotaDTO();
                    quotaDTO.setOrderCode(assemblyOrderDTO.getOrder().getOrderCode());
                    quotaDTO.setOrderId(orderId);
                    quotaDTO.setProductId(Integer.valueOf(request.getProductId()));
                    quotaDTO.setSupplierCode(supplierCode);
                    quotaDTO.setQuota(-request.getRoomQty());//扣配额
                    quotaDTO.setSupplyOrderCode(assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getSupplyOrderCode());
                    quotaDTO.setSupplyOrderId(assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getId());
                    Map<String, String> map = new HashMap<>();
                    map.put("begin", request.getStartDate());
                    map.put("end", request.getEndDate());
                    List<String> list = orderMapper.queryBetweenDate(map);
                    StringBuilder saleDate = new StringBuilder();
                    for (String s : list) {
                        saleDate.append(s).append(",");
                    }
                    quotaDTO.setSaleDate(saleDate.substring(0, saleDate.length() - 1));
                    quotaRemote.modifyQuota(quotaDTO);
                }
            } catch (Exception e) {
                log.error("扣配额异常！订单ID：" + orderId, e);
            }
        }

        OrderPO orderPO = orderMapper.selectByPrimaryKey(orderId);

        // 保存发票信息
        saveOrderInvoice(orderPO.getOrderCode(), request.getOrderInvoiceDTO(), request.getOperator());

        // 非单结自动扣额度
        if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
            //当前自签产品默认预付，但是支付方式为空，需要扣额度；
            //直连供应商预付产品，需要扣额度
            if (null == request.getPayMethod() || request.getPayMethod().equals(1)) {
                AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                    agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                } else {
                    agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                }
                agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                agentCreditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt().negate());
                agentCreditLineDTO.setCreatedBy(request.getOperator());
                Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
                if (!creditLineResponse.isSuccess()) {
                    throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                }
            }
        }
        //推送消息提醒
        String message = "客户-" + agentAccountConfig.getAgentName() +
                "企业-" + assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getSupplierName() +
                "下了一条" + request.getStartDate() + "入住" + productSalePriceDTO.getHotelName() +
                request.getRoomQty() + "间" + DateUtilX.getDay(orderPO.getStartDate(), orderPO.getEndDate()) + "晚的订单,请尽快处理!";

        Map<String, Object> content = new HashMap<>();
        content.put("orderCode", orderPO.getOrderCode());
        content.put("content", message);
        content.put("messageType", MessageTypeEnum.NEW_ORDER.key);
        JSONObject json = new JSONObject(content);
        stringRedisTemplate.convertAndSend(RedisKey.orderInfoKey, request.getCompanyCode() + "/" + JSON.toJSONString(json));

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 如果开通自动发单服务直接发单
                try {
                    if (orderPO.getIsAbnormal() == null || orderPO.getIsAbnormal() == 0) {
                        if (request.getPayStatus() != null && request.getPayStatus().equals(PayStatusEnum.PAY.getNo())) {
                            assemblyOrderDTO.setRequestId(request.getRequestId());
                            asyncSendOrder(assemblyOrderDTO);
                        }
                    } else {
                        log.info("问题单，orderCode={}，isAbnormal={}", orderPO.getOrderCode(), orderPO.getIsAbnormal());
                    }
                } catch (Exception e) {
                    log.error("自动发单功能异常", e);
                }
            }
        });
        OrderCodeDTO codeDTO = new OrderCodeDTO();
        codeDTO.setOrderCode(orderPO.getOrderCode());
        codeDTO.setSupplyOrderCode(assemblyOrderDTO.getSupplyOrderList().get(0).getSupplyOrder().getSupplyOrderCode());
        return codeDTO;
    }

    /**
     * 组装售卖产品
     */
    private ProductSalePriceDTO assemblySupplyProduct(List<ProductMiddleDto> productMiddleDtos, AddManualOrderDTO request, AgentAccountConfig agentAccountConfig) {
        //查询分销商-供应商加幅
        SupplyIncrease supplyIncrease = increaseUtil.getSupplyIncrease(request.getAgentCode(), request.getSupplierCode());
        //查询分销商-供应商-酒店加幅
        SupplyHotelIncrease supplyHotelIncrease = increaseUtil.getSupplyHotelIncrease(request.getAgentCode(), request.getSupplierCode(), request.getHotelId());

        //查询供应商加幅
        SupplierAddDTO supplierIncrease = increaseUtil.getSupplierIncrease(request.getSupplierCode());

        ProductSalePriceDTO salePriceDTO = new ProductSalePriceDTO();
        String productId = request.getProductId().substring(0, request.getProductId().lastIndexOf("#"));
        request.setProductId(productId);
        Optional<ProductSalePriceDTO> productSalePrice = getProductSalePriceDTO(productMiddleDtos, request, productId);
        if (!productSalePrice.isPresent()) {
            salePriceDTO.setHotelId(request.getHotelId());
            salePriceDTO.setHotelName(request.getHotelName());
            salePriceDTO.setCurrency(agentAccountConfig.getSettlementCurrency());//币种默认人民币
            salePriceDTO.setRoomId(request.getRoomId());
            salePriceDTO.setRoomName(request.getRoomName());
            salePriceDTO.setSupplierCode(request.getSupplierCode());
            salePriceDTO.setSupplierName(request.getSupplierName());
            salePriceDTO.setProductName(request.getSpProductId());
            salePriceDTO.setProductId(productId);

            List<com.tiangong.dto.product.PriceInfoDetail> priceItemList = new ArrayList<>();
            if (CollUtilX.isNotEmpty(request.getPriceList())) {
                for (PriceRequestDTO productInfoDTO : request.getPriceList()) {
                    com.tiangong.dto.product.PriceInfoDetail priceItem = new com.tiangong.dto.product.PriceInfoDetail();
                    priceItem.setBasePrice(productInfoDTO.getSalePrice());
                    priceItem.setRoomStatus(RoomStateEnum.HAVA_ROOM.key);//默认有房
                    priceItem.setDate(productInfoDTO.getSaleDate());

                    ProductSalePriceItemDTO productSalePriceItemDTO = new ProductSalePriceItemDTO();
                    productSalePriceItemDTO.setBasePrice(productInfoDTO.getSalePrice());
                    productSalePriceItemDTO.setQuota(1);
                    productSalePriceItemDTO.setRemainingQuota(1);
                    productSalePriceItemDTO.setOverDraftStatus(0);
                    productSalePriceItemDTO.setRoomStatus(RoomStateEnum.HAVA_ROOM.key);//默认有房
                    productSalePriceItemDTO.setSaleDate(productInfoDTO.getSaleDate());
                    //早餐和取消条款取首日
                    if (request.getStartDate().equals(productInfoDTO.getSaleDate())) {
                        salePriceDTO.setBreakfastQty(0);//默认无早
                        salePriceDTO.setCancelRestrictType(1);//默认提前0天18点可以取消
                        salePriceDTO.setCancelRestrictDays(0);
                        salePriceDTO.setCancelRestrictTime("18:00");
                    }
                    productSalePriceItemDTO.setSalePrice(productInfoDTO.getSalePrice());
                    productSalePriceItemDTO.setProfit(new BigDecimal("0"));
                    priceItemList.add(priceItem);
                }
            }
            salePriceDTO.setPriceList(priceItemList);
            log.info("供应商查无产品组装默认产品：{}", JSON.toJSONString(salePriceDTO));
        } else {
            salePriceDTO = productSalePrice.get();
            if (CollUtilX.isNotEmpty(salePriceDTO.getCancelRestrictions())) {
                for (CancelRestriction cancelRestriction : salePriceDTO.getCancelRestrictions()) {
                    if (Objects.nonNull(cancelRestriction.getCancelRestrictionDay())) {
                        cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(request.getStartDate()), -cancelRestriction.getCancelRestrictionDay(), 0)));
                    }
                }
            }
            salePriceDTO.setCancelRestrictions(salePriceDTO.getCancelRestrictions());
            salePriceDTO.setInvoiceModel(salePriceDTO.getInvoiceModel());
            salePriceDTO.setInvoiceType(salePriceDTO.getInvoiceType());
            salePriceDTO.setGuaranteeType(salePriceDTO.getGuaranteeType());
            salePriceDTO.setGuaranteeCondition(salePriceDTO.getGuaranteeCondition());
            salePriceDTO.setGuaranteeFeeType(salePriceDTO.getGuaranteeFeeType());
            salePriceDTO.setGuaranteeDesc(salePriceDTO.getGuaranteeDesc());
            salePriceDTO.setRoomItemDetails(salePriceDTO.getRoomItemDetails());
            salePriceDTO.setPayAtHotelFlag(salePriceDTO.getPayAtHotelFlag());

            if (CollUtilX.isNotEmpty(salePriceDTO.getRoomItemDetails())) {
                for (com.tiangong.dto.order.response.OrderRoomDetailDto roomItemDetail : salePriceDTO.getRoomItemDetails()) {
                    // 预付产品加幅，到店付产品不加幅
                    if (Objects.isNull(salePriceDTO.getPayAtHotelFlag()) || Objects.equals(salePriceDTO.getPayAtHotelFlag(), 0)) {
                        // 预付
                        increaseUtil.agentIncrease(roomItemDetail, agentAccountConfig, supplierIncrease, supplyIncrease, supplyHotelIncrease, request.getStartDate());
                    } else {
                        // 现付
                        increaseUtil.agentIncrease(roomItemDetail, agentAccountConfig, request.getStartDate());
                    }
                }
            }
        }
        salePriceDTO.setSupplyType(2);
        // 设置发票信息
        AddSupplierReq supplier = JSON.parseObject(String.valueOf(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, request.getSupplierCode())), AddSupplierReq.class);
        if (supplier != null) {
            // 发票模式
            salePriceDTO.setInvoiceModel(supplier.getInvoiceModel());
            // 发票类型
            salePriceDTO.setInvoiceType(supplier.getInvoiceType());
            salePriceDTO.setQuickProcessingSwitch(supplier.getQuickProcessingSwitch() != null ? supplier.getQuickProcessingSwitch() : 0);
        }

        return salePriceDTO;
    }

    /**
     * 组装每日明细
     */
    private Optional<ProductSalePriceDTO> getProductSalePriceDTO(List<ProductMiddleDto> productMiddleDtos, AddManualOrderDTO request, String productId) {
        return productMiddleDtos.stream().filter(e -> Objects.equals(productId, e.getSpPricePlanId()))
                .map(productMiddleDto -> {
                    ProductSalePriceDTO productSalePriceDTO = new ProductSalePriceDTO();
//                    productSalePriceDTO.setBedType(productMiddleDto.getBedType());
                    productSalePriceDTO.setHotelId(productMiddleDto.getHotelId());
                    productSalePriceDTO.setHotelName(productMiddleDto.getHotelName());
                    productSalePriceDTO.setRoomId(productMiddleDto.getRoomId());
                    productSalePriceDTO.setRoomName(productMiddleDto.getRoomName());
                    productSalePriceDTO.setProductId(productId);
                    productSalePriceDTO.setProductName(productMiddleDto.getSpPricePlanName());
                    productSalePriceDTO.setCurrency(productMiddleDto.getCurrency());
                    productSalePriceDTO.setSupplierCode(request.getSupplierCode());
                    productSalePriceDTO.setSupplierName(request.getSupplierName());
                    productSalePriceDTO.setPayAtHotelFlag(productMiddleDto.getPayAtHotelFlag());
                    if (CollUtilX.isNotEmpty(productMiddleDto.getRoomItemDetails())) {
                        RoomItemDetailDto roomItemDetail = productMiddleDto.getRoomItemDetails().stream().findFirst().get();
                        if (CollUtilX.isNotEmpty(roomItemDetail.getProductDetails())) {
                            List<CancelRestriction> collect = roomItemDetail.getProductDetails().get(0).getCancelRestrictions().stream().map(cancelRestriction -> {
                                        CancelRestriction cancel = new CancelRestriction();
                                        cancel.setCancelRestrictionType(cancelRestriction.getCancelRestrictionType());
                                        cancel.setCancelRestrictionDay(cancelRestriction.getCancelRestrictionDay());
                                        cancel.setCancelRestrictionTime(cancelRestriction.getCancelRestrictionTime());
                                        cancel.setCancellationPrice(cancelRestriction.getCancellationPrice());
                                        cancel.setSupplyCancelRestrictionRemark(cancelRestriction.getCancelRestrictionRemark());
                                        if (Objects.nonNull(cancelRestriction.getCancelRestrictionDay())) {
                                            cancel.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(roomItemDetail.getProductDetails().get(0).getSaleDate()), -cancelRestriction.getCancelRestrictionDay(), 0)));
                                        }
                                        if (CollUtilX.isNotEmpty(cancelRestriction.getCancelPenalties())) {
                                            boolean flag = false;
                                            List<CancelPenalties> cancelPenaltiesList = new ArrayList<>();
                                            for (com.tiangong.supply.direct.remote.dto.CancelPenalties cancelPenalty : cancelRestriction.getCancelPenalties()) {
                                                CancelPenalties cancelPenalties = new CancelPenalties();
                                                cancelPenalties.setStartDate(cancelPenalty.getStartDate());
                                                cancelPenalties.setEndData(cancelPenalty.getEndData());
                                                cancelPenalties.setPenaltiesType(cancelPenalty.getPenaltiesType());
                                                cancelPenalties.setPenaltiesValue(cancelPenalty.getPenaltiesValue());
                                                cancelPenalties.setCurrency(cancelPenalty.getCurrency());
                                                cancelPenaltiesList.add(cancelPenalties);

                                                if (ChannelEnum.DHUB.key.equals(request.getChannelCode()) && cancelPenalty.getPenaltiesType() != null && (PenaltiesTypeEnum.NIGHTS.getKey().equals(cancelPenalty.getPenaltiesType()) || PenaltiesTypeEnum.FIRSTNIGHTPERCENTAGE.getKey().equals(cancelPenalty.getPenaltiesType()))) {
                                                    flag = true;
                                                }
                                            }
                                            cancel.setCancelPenalties(cancelPenaltiesList);

                                            // 当罚金类型为首晚、首晚百分比时，转换取消类型为3
                                            if (flag) {
                                                cancel.setCancelRestrictionType(3);
                                                cancel.setCancelPenalties(null);
                                            }
                                        }
                                        return cancel;
                                    }
                            ).collect(Collectors.toList());
                            productSalePriceDTO.setCancelRestrictions(collect);

                            // 设置担保条款
                            ProductDetailMiddleDto productDetailMiddleDto = roomItemDetail.getProductDetails().get(0);
                            productSalePriceDTO.setGuaranteeType(productDetailMiddleDto.getGuaranteeType());
                            productSalePriceDTO.setGuaranteeCondition(productDetailMiddleDto.getGuaranteeCondition());
                            productSalePriceDTO.setGuaranteeFeeType(productDetailMiddleDto.getGuaranteeFeeType());
                        }
                    }
                    //每间房报价
                    productSalePriceDTO.setRoomItemDetails(convertOrderRoomDetail(productMiddleDto.getRoomItemDetails()));
                    //提示信息
                    if (CollUtilX.isNotEmpty(productMiddleDto.getTips())) {
                        List<TipInfosDTO> tips = ProductSalePriceConvert.INSTANCE.TipInfosDTOConvert(productMiddleDto.getTips());
                        productSalePriceDTO.setTips(tips);
                    }
                    return productSalePriceDTO;
                }).findFirst();
    }

    /**
     * 供应端每间房报价转订单每间房报价
     */
    private List<com.tiangong.dto.order.response.OrderRoomDetailDto> convertOrderRoomDetail(List<RoomItemDetailDto> roomItemDetails) {
        List<com.tiangong.dto.order.response.OrderRoomDetailDto> orderRoomDetailDtos = new ArrayList<>();
        for (RoomItemDetailDto roomItemDetailDto : roomItemDetails) {
            com.tiangong.dto.order.response.OrderRoomDetailDto orderRoomDetailDto = JSON.parseObject(JSON.toJSONString(roomItemDetailDto), com.tiangong.dto.order.response.OrderRoomDetailDto.class);
            if (CollUtilX.isNotEmpty(roomItemDetailDto.getProductDetails())) {
                List<com.tiangong.dto.product.PriceInfoDetail> priceInfoDetails = new ArrayList<>();
                for (ProductDetailMiddleDto productDetailMiddleDto : roomItemDetailDto.getProductDetails()) {
                    com.tiangong.dto.product.PriceInfoDetail priceInfoDetail = JSON.parseObject(JSON.toJSONString(productDetailMiddleDto), com.tiangong.dto.product.PriceInfoDetail.class);
                    priceInfoDetail.setDate(productDetailMiddleDto.getSaleDate());
                    priceInfoDetail.setBaseCurrency(productDetailMiddleDto.getCurrency());
                    priceInfoDetails.add(priceInfoDetail);
                }
                orderRoomDetailDto.setPriceInfoDetails(priceInfoDetails);
            }
            orderRoomDetailDtos.add(orderRoomDetailDto);
        }
        return orderRoomDetailDtos;
    }

    /**
     * 保存订单发票
     */
    private void saveOrderInvoice(String orderCode, OrderInvoiceDTO orderInvoiceDTO, String operator) {
        if (orderInvoiceDTO != null) {
            orderInvoiceDTO.setOrderCode(orderCode);
            orderInvoiceDTO.setOperator(operator);
            try {
                orderService.modifyOrderInvoice(orderInvoiceDTO);
            } catch (Exception e) {
                log.error("新增发票订单失败", e);
            }
        }
    }

    /**
     * 异步处理发单
     */
    private void asyncSendOrder(AssemblyOrderDTO assemblyOrderDTO) {
        CompletableFuture.runAsync(() -> {
            for (AssemblySupplyOrderDTO assemblySupplyOrderDTO : assemblyOrderDTO.getSupplyOrderList()) {
                try {
                    // 1.首先获取到供货单的信息
                    SupplyOrderPO supplyOrder = assemblySupplyOrderDTO.getSupplyOrder();
                    OrderPO order = assemblyOrderDTO.getOrder();
                    // 2.通过供货单信息去查询供应商是否开通发单设置
                    Map<String, String> request = new HashMap<>();
                    request.put("supplierCode", supplyOrder.getSupplierCode());
                    request.put("available", "1");//直接查询有设置直接发单{是否自动发单，是:1;否:0}
                    request.put("companyCode", order.getCompanyCode());
                    List<SupplierListDTO> supplierListDTOList = orderMapper.orderAutomaticList(request);
                    log.info("自动发单，参数：{}", JSON.toJSONString(supplierListDTOList));
                    // 判断该供应商是否开通自动发单
                    if (CollUtilX.isEmpty(supplierListDTOList)) {
                        log.error("该供应商没有开通自动发单:" + supplyOrder.getSupplierName() + "供应商家编码为:" + supplyOrder.getSupplierCode() + "请求参数:" + request + ",返回参数" + JSON.toJSONString(supplierListDTOList));
                    } else {
                        for (SupplierListDTO supplierListDTO : supplierListDTOList) {
                            // 判断商家是否相等
                            if (!order.getCompanyCode().equals(supplierListDTO.getCompanyCode())) {
                                log.error("商家不一致不自动发单:" + order.getCompanyCode() + "商家编码:" + supplierListDTO.getCompanyCode());
                            }

                            // 查询供应商code是否一致
                            if (!supplierListDTO.getSupplierCode().equals(supplyOrder.getSupplierCode())) {
                                log.error("该供应商没有开通自动发单:" + supplyOrder.getSupplierName() + "供应商家编码为:" + supplyOrder.getSupplierCode());
                                continue;
                            }
                            // 判断创建供货单的时间是否在发单时间范围内 ==0所有时间不用判断时间
                            if (supplierListDTO.getSuppliertimeType() == null || supplierListDTO.getSuppliertimeType() != 0) {
                                String startTime = supplierListDTO.getStartTime();
                                String endTime = supplierListDTO.getEndTime();
                                //非空判断
                                if (StrUtilX.isNotEmpty(startTime) && StrUtilX.isNotEmpty(endTime)) {
                                    String time = DateUtilX.splitHoursAndSecond(supplyOrder.getCreatedDt());
                                    if (!DateUtilX.betweenTime(time, startTime, endTime)) {
                                        log.error("该供应商不在自动发单的有效时间内:" + supplyOrder.getSupplierName() + ";有效时间范围为:" + startTime + "_" + endTime + ";供货单创建时间" + supplyOrder.getCreatedDt());
                                        continue;
                                    }
                                }
                            }

                            //判断发单方式
                            //@2024年0925版本增加电邮发单方式
                            if (supplierListDTO.getOrderMethod().equals(OrderMethodEnum.ORDER_METHOD_ENUM_2.key)) {
                                SendToSupplierDTO sendToSupplierDTO = new SendToSupplierDTO();
                                sendToSupplierDTO.setSupplyOrderId(supplyOrder.getId());
                                sendToSupplierDTO.setOperator(Constant.SYSTEM);
                                sendToSupplierDTO.setAutoSendOrder(true);  //自动发单
                                sendToSupplierDTO.setIsAttachment(1);      //带附件
                                sendToSupplierDTO.setSupplyOrderType(0);   //预定单
                                sendToSupplierDTO.setRequestId(assemblyOrderDTO.getRequestId());
                                log.info("邮件自动发单开始:{},请求参数：{}", supplyOrder.getId(), JSON.toJSONString(sendToSupplierDTO));
                                Response<Object> response = supplyOrderService.sendToEmail(sendToSupplierDTO, EmailSendingTypeEnum.BOOKING_ORDER.key);
                                log.info("邮件自动发单完成:{},返回参数：{}", supplyOrder.getId(), JSON.toJSONString(response));
                                break;
                            } else {
                                // 设置发单请求参数调用发单接口
                                SendToSupplierDTO sendToSupplierDTO = new SendToSupplierDTO();
                                sendToSupplierDTO.setSupplyOrderId(supplyOrder.getId());
                                sendToSupplierDTO.setOperator(Constant.SYSTEM);
                                sendToSupplierDTO.setSupplyOrderType(supplyOrder.getSendingStatus());
                                sendToSupplierDTO.setAutoSendOrder(true);
                                sendToSupplierDTO.setRequestId(assemblyOrderDTO.getRequestId());
                                log.info("自动发单开始:{},请求参数：{}", supplyOrder.getId(), JSON.toJSONString(sendToSupplierDTO));
                                Response<Object> sendSupplyOrderResponse = supplyOrderService.sendToSupplier(sendToSupplierDTO);
                                log.info("自动发单完成:{},返回参数：{}", supplyOrder.getId(), JSON.toJSONString(sendSupplyOrderResponse));
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("异步处理发单异常，supplyOrderCode={}", assemblySupplyOrderDTO.getSupplyOrder());
                }
            }
        });
    }

    @Override
    public ProductSalePriceDTO checkPreBook(PreBookDTO prebookDTO) {
        ProductSalePriceDTO productSalePriceDTO = null;
        // 查询分销商
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, prebookDTO.getAgentCode()), AgentAccountConfig.class);
        if (Objects.isNull(agentAccountConfig) || agentAccountConfig.getAvailableStatus().equals(0)) {
            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID);
        }

        //校验黑白名单
        List<String> supplyCodes = getAgentSupplyAvailable(prebookDTO.getAgentCode());
        if (Objects.isNull(supplyCodes) || !supplyCodes.contains(prebookDTO.getSupplyCode())) {
            throw new SysException(ErrorCodeEnum.SUPPLY_NOT_EXIST);
        }

        // 校验入住日期
        checkCheckInDate(prebookDTO.getSupplyCode(), prebookDTO.getCheckInDate());

        Map<String, String> productMap = new HashMap<>();
        String productId = prebookDTO.getProductId();
        productMap.put("productId", productId);
        Response<ProductDTO> productResponse = productRemote.queryProductById(productMap);
        ProductDTO productById = productResponse.getModel();
        if (null != productById) {
            List<String> productAndsaleDates = new ArrayList<>();
            Date startDate = DateUtilX.stringToDate(prebookDTO.getCheckInDate(), "yyyy-MM-dd");
            for (int i = 0; i < 31; i++) {
                Date endDate = DateUtilX.addDate(startDate, i);
                String endDateStr = DateUtilX.dateToString(endDate, "yyyy-MM-dd");
                if (endDateStr.equals(prebookDTO.getCheckOutDate())) {
                    break;
                } else {
                    //12328_2022-07-02
                    productAndsaleDates.add(productId + "_" + endDateStr);
                }
            }
            List<ProductBasePriceAndRoomStatusDTO> productBasePriceAndRoomStatusDTOS = StrUtilX.parseObject(RedisTemplateX.hMultiGet(RedisKey.productBasePriceAndRoomStatusKey, productAndsaleDates),
                    new TypeReference<List<ProductBasePriceAndRoomStatusDTO>>() {
                    });
            if (com.tiangong.util.CollUtilX.isEmpty(productBasePriceAndRoomStatusDTOS)) {
                throw new SysException(ErrorCodeEnum.NOT_PRODUCT_MAPPING);
            }
            boolean validFlag = true;
            for (ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO : productBasePriceAndRoomStatusDTOS) {
                if (productBasePriceAndRoomStatusDTO.getQuota() == null) {
                    productBasePriceAndRoomStatusDTO.setQuota(0);
                }
                if (productBasePriceAndRoomStatusDTO.getOverDraftStatus() == null) {
                    productBasePriceAndRoomStatusDTO.setOverDraftStatus(0);
                }
                if (productBasePriceAndRoomStatusDTO.getRoomStatus() != null && productBasePriceAndRoomStatusDTO.getRoomStatus() == 1) {
                    //开房
                } else {
                    validFlag = false;
                    break;
                }
                if (productBasePriceAndRoomStatusDTO.getOverDraftStatus().equals(0) && productBasePriceAndRoomStatusDTO.getQuota().equals(0)) {
                    validFlag = false;
                    break;
                }
            }
            if (!validFlag) {
                throw new SysException(ErrorCodeEnum.QUOTA_ENOUGH);
            }

            if (null == productById.getSupplyType() || !productById.getSupplyType().equals(2)) {
                if (StrUtilX.isEmpty(prebookDTO.getChannelCode())) {
                    prebookDTO.setChannelCode("B2B");
                }
                QueryProductRequestDTO queryProductRequestDTO = new QueryProductRequestDTO();
                queryProductRequestDTO.setProductId(prebookDTO.getProductId());
                queryProductRequestDTO.setStartDate(prebookDTO.getCheckInDate());
                queryProductRequestDTO.setEndDate(DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(prebookDTO.getCheckOutDate()), -1)));
                queryProductRequestDTO.setChannelCode(prebookDTO.getChannelCode());
                queryProductRequestDTO.setCompanyCode(prebookDTO.getCompanyCode());
                queryProductRequestDTO.setAgentCode(prebookDTO.getAgentCode());
                queryProductRequestDTO.setLanguage(prebookDTO.getLanguage());
                Response<ProductSalePriceDetailDTO> resp = productSaleRemote.querySalePriceList(queryProductRequestDTO);
                if (resp.isError()) {
                    throw new SysException(resp.getFailCode(), resp.getFailReason());
                }
                productSalePriceDTO = ProductSalePriceConvert.INSTANCE.convert(resp.getModel());

                // 自签产品默认可定
                productSalePriceDTO.setCanBook(CanBookEnum.CAN_BOOK.value);
                if (CollUtilX.isNotEmpty(productSalePriceDTO.getPriceList())) {
                    for (com.tiangong.dto.product.PriceInfoDetail productSalePriceItemDTO : productSalePriceDTO.getPriceList()) {
                        productSalePriceItemDTO.setBreakfastNum(productSalePriceDTO.getBreakfastQty());//设置每日早餐数量
                    }
                }

                // 取消条款
                if (StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, prebookDTO.getProductId()), ProductRestrictDTO.class) != null) {
                    ProductRestrictDTO productRestrictDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, String.valueOf(prebookDTO.getProductId())), ProductRestrictDTO.class);
                    Integer cancelRestrictionType = null;
                    List<CancelRestriction> cancelRestrictions = Lists.newArrayList();
                    CancelRestriction cancelRestriction = new CancelRestriction();
                    if (null == productRestrictDTO) {
                        cancelRestrictionType = 3;
                        cancelRestriction.setCancelRestrictionDay(0);
                        cancelRestriction.setCancelRestrictionTime("2359");
                    } else {
                        if (Objects.equals(productRestrictDTO.getCancellationType(), 0)) {
                            cancelRestrictionType = 1;
                        } else if (Objects.equals(productRestrictDTO.getCancellationType(), 1)) {
                            cancelRestrictionType = 2;
                            if (Objects.nonNull(productRestrictDTO.getCancellationAdvanceDays())) {
                                cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(prebookDTO.getCheckInDate()), -productRestrictDTO.getCancellationAdvanceDays(), 0)));
                            }
                        }
                        cancelRestriction.setCancelRestrictionDay(productRestrictDTO.getCancellationAdvanceDays());
                        String cancellationDueTime = Objects.isNull(productRestrictDTO.getCancellationDueTime()) ? null : productRestrictDTO.getCancellationDueTime().replace(":", "");
                        cancelRestriction.setCancelRestrictionTime(cancellationDueTime);
                    }

                    cancelRestriction.setCancelRestrictionType(cancelRestrictionType);
                    cancelRestrictions.add(cancelRestriction);
                    productSalePriceDTO.setCancelRestrictions(cancelRestrictions);
                }

                // 产品停售状态
                ProductDTO productDTO = orderMapper.queryOnSaleProduct(prebookDTO);
                if (productDTO == null) {
                    throw new SysException(ErrorCodeEnum.PRODUCT_DISABLE);
                }

                QueryProductRestrictDTO queryProductRestrictDTO = new QueryProductRestrictDTO();
                queryProductRestrictDTO.setProductId(prebookDTO.getProductId());
                queryProductRestrictDTO.setStartDate(prebookDTO.getCheckInDate());
                queryProductRestrictDTO.setEndDate(DateUtilX.dateToString(DateUtilX.addDate(
                        DateUtilX.stringToDate(prebookDTO.getCheckOutDate()), -1)));
                Response<List<ProductDTO>> res = productRemote.queryProductRestrictList(queryProductRestrictDTO);

                if (res.getResult() == 1) {
                    List<ProductDTO> productDTOS = res.getModel();
                    int i = 1;// 可以预定
                    for (ProductDTO product : productDTOS) {
                        // 最晚预定的时间
                        if (null != product.getReservationAdvanceDays()) {
                            Date bookingDate = DateUtilX.addDate(DateUtilX.stringToDate(product.getSaleDate()), (-product.getReservationAdvanceDays()));
                            String preBookTime = DateUtilX.dateToString(bookingDate);
                            preBookTime = preBookTime + " " + (StrUtilX.isNotEmpty(product.getReservationDueTime()) ?
                                    product.getReservationDueTime() : "23:59");

                            Date preBookingTime = DateUtilX.stringToDate(preBookTime, "yyyy-MM-dd HH:mm");
                            if (preBookingTime != null && preBookingTime.getTime() < new Date().getTime()) {
                                i = 0;
                            }
                        }

                        long bookingDay = DateUtilX.getDay(DateUtilX.stringToDate(prebookDTO.getCheckInDate()), DateUtilX.stringToDate(prebookDTO.getCheckOutDate()));
                        if (product.getReservationLimitNights() != null) {
                            if (product.getComparisonType() == 0 && (int) bookingDay < product.getReservationLimitNights()) { //大于等于
                                i = 0;
                            } else if (product.getComparisonType() == 1 && (int) bookingDay > product.getReservationLimitNights()) {
                                i = 0;
                            } else if (product.getComparisonType() == 2 && !product.getReservationLimitNights().equals((int) bookingDay)) { //等于
                                i = 0;
                            }
                        }

                        if (product.getReservationLimitRooms() != null && prebookDTO.getRoomQty() < product.getReservationLimitRooms()) {
                            i = 0;
                        }

                        if (i == 0) {
                            // 组装条款
                            String reason = assemblyTram(product);
                            throw new SysException(ErrorCodeEnum.ERROR_RESTRICT.errorCode, reason);
                        }
                    }
                }

                BigDecimal totalPrice = BigDecimal.ZERO;
                int flag = 0; //可定
                if (resp.getResult().equals(ResultCodeEnum.SUCCESS.code)) {
                    List<com.tiangong.dto.product.PriceInfoDetail> productSalePriceItemDTOS = productSalePriceDTO.getPriceList();
                    if (CollUtilX.isNotEmpty(productSalePriceItemDTOS)) {
                        for (com.tiangong.dto.product.PriceInfoDetail productSalePriceItemDTO : productSalePriceItemDTOS) {
                            if (productSalePriceItemDTO.getRoomStatus() == 0) { //关房
                                flag = 10000;
                            }
                            productSalePriceItemDTO.setCanBook(1);
                            BigDecimal salePrice = Objects.isNull(productSalePriceItemDTO.getSalePrice()) ? productSalePriceItemDTO.getBasePrice() : productSalePriceItemDTO.getSalePrice();
                            productSalePriceItemDTO.setSalePrice(salePrice);
                            totalPrice = totalPrice.add(salePrice.multiply(new BigDecimal(prebookDTO.getRoomQty())));
                        }
                    } else {
                        throw new SysException(ErrorCodeEnum.NO_SALE_PRICE);
                    }
                }

                if (flag > 0) {
                    throw new SysException(ErrorCodeEnum.QUOTA_ENOUGH);
                }

                if (prebookDTO.getTotalPrice() != null) {
                    if (totalPrice.compareTo(prebookDTO.getTotalPrice()) != 0) {
                        throw new SysException(ErrorCodeEnum.PRICE_NOT_ACCURATE);
                    }
                } else {
                    productSalePriceDTO.setTotalSalePrice(totalPrice);
                }

                if (CollectionUtil.isEmpty(productSalePriceDTO.getRoomItemDetails())) {
                    List<com.tiangong.dto.order.response.OrderRoomDetailDto> orderRoomDetailDtos = convertOrderRoomDetail(prebookDTO, resp.getModel(), productSalePriceDTO, productById);
                    productSalePriceDTO.setRoomItemDetails(orderRoomDetailDtos);
                }
            }

        } else {
            // 直连供应商试预定
            productSalePriceDTO = directPreBooking(prebookDTO, agentAccountConfig);
        }

        return productSalePriceDTO;
    }


    private List<com.tiangong.dto.order.response.OrderRoomDetailDto> convertOrderRoomDetail2(List<OrderRoomGuestDTO> roomGuestList, ProductSalePriceDetailDTO productSalePriceDetailDTO, ProductSalePriceDTO productSalePriceDTO) {
        List<com.tiangong.dto.order.response.OrderRoomDetailDto> orderRoomDetails = new ArrayList<>();

        for (OrderRoomGuestDTO roomGuestNumber : roomGuestList) {
            com.tiangong.dto.order.response.OrderRoomDetailDto orderRoomDetailDto = new com.tiangong.dto.order.response.OrderRoomDetailDto();
            orderRoomDetailDto.setRoomIndex(roomGuestNumber.getRoomNumber());
            if (productSalePriceDetailDTO.getRoomTaxDetail() != null) {
                RoomTaxDetail roomTaxDetail = productSalePriceDetailDTO.getRoomTaxDetail();
                com.tiangong.dto.product.TaxDetailDto taxDetail = ProductSalePriceConvert.INSTANCE.TaxDetailDtoConvert(roomTaxDetail);
                if (roomTaxDetail.getPayInStoreCurrency() != null) {
                    orderRoomDetailDto.setPayInStoreCurrency(roomTaxDetail.getPayInStoreCurrency());
                    orderRoomDetailDto.setPayInStorePrice(roomTaxDetail.getPayInStorePrice());
                    orderRoomDetailDto.setSupplyPayInStorePrice(roomTaxDetail.getPayInStorePrice());
                    orderRoomDetailDto.setSupplyPayInStoreCurrency(roomTaxDetail.getPayInStoreCurrency());
                }
                taxDetail.setSupplySalesTax(roomTaxDetail.getSupplySalesTax());
                taxDetail.setSupplyTaxFee(roomTaxDetail.getSupplyTaxFee());
                taxDetail.setSupplyOtherTax(roomTaxDetail.getSupplyOtherTax());
                taxDetail.setDiscount(roomTaxDetail.getDiscount());
                orderRoomDetailDto.setTaxDetail(taxDetail);
            }
            // 税费明细
            orderRoomDetailDto.setAdultNum(roomGuestNumber.getAdultQty());
            orderRoomDetailDto.setChildAges(roomGuestNumber.getChildrenAge());
            // 每日价格明细
            convertPriceItem(productSalePriceDTO.getPriceList(), orderRoomDetailDto);
            orderRoomDetails.add(orderRoomDetailDto);
        }
        return orderRoomDetails;
    }

    private List<com.tiangong.dto.order.response.OrderRoomDetailDto> convertOrderRoomDetail(PreBookDTO preBook, ProductSalePriceDetailDTO productSalePriceDetailDTO, ProductSalePriceDTO productSalePriceDTO, ProductDTO product) {
        List<com.tiangong.dto.order.response.OrderRoomDetailDto> orderRoomDetails = new ArrayList<>();

        for (com.tiangong.dto.product.request.RoomGuestNumber roomGuestNumber : preBook.getRoomGuestNumbers()) {
            com.tiangong.dto.order.response.OrderRoomDetailDto orderRoomDetailDto = new com.tiangong.dto.order.response.OrderRoomDetailDto();
            orderRoomDetailDto.setRoomIndex(roomGuestNumber.getRoomIndex());
            // 儿童信息
            if (CollUtilX.isNotEmpty(roomGuestNumber.getChildrenInfos())) {
                StringBuilder childrenStr = new StringBuilder();
                for (int i = 0; i < roomGuestNumber.getChildrenInfos().size(); i++) {
                    childrenStr.append(roomGuestNumber.getChildrenInfos().get(i).getChildrenAge());
                    if (i != roomGuestNumber.getChildrenInfos().size() - 1) {
                        childrenStr.append(",");
                    }
                }
                orderRoomDetailDto.setChildAges(childrenStr.toString());
            }
            if (productSalePriceDetailDTO.getRoomTaxDetail() != null) {
                RoomTaxDetail roomTaxDetail = productSalePriceDetailDTO.getRoomTaxDetail();
                com.tiangong.dto.product.TaxDetailDto taxDetail = ProductSalePriceConvert.INSTANCE.TaxDetailDtoConvert(roomTaxDetail);
                if (roomTaxDetail.getPayInStoreCurrency() != null) {
                    orderRoomDetailDto.setPayInStoreCurrency(roomTaxDetail.getPayInStoreCurrency());
                    orderRoomDetailDto.setPayInStorePrice(roomTaxDetail.getPayInStorePrice());
                    orderRoomDetailDto.setSupplyPayInStorePrice(roomTaxDetail.getPayInStorePrice());
                    orderRoomDetailDto.setSupplyPayInStoreCurrency(roomTaxDetail.getPayInStoreCurrency());
                }
                orderRoomDetailDto.setTaxDetail(taxDetail);
            }
            // 税费明细
            orderRoomDetailDto.setAdultNum(roomGuestNumber.getAdultNum());
            // 每日价格明细
            convertPriceItem(productSalePriceDTO.getPriceList(), orderRoomDetailDto);
            orderRoomDetails.add(orderRoomDetailDto);
        }
        return orderRoomDetails;
    }


    private void convertPriceItem(List<PriceInfoDetail> priceItems, com.tiangong.dto.order.response.OrderRoomDetailDto orderRoomDetailDto) {
        if (CollUtilX.isNotEmpty(priceItems)) {
            List<PriceInfoDetail> priceInfoDetails = new ArrayList<>();
            for (PriceInfoDetail preBookSaleItem : priceItems) {
                PriceInfoDetail priceInfoDetail = new PriceInfoDetail();
                priceInfoDetail.setDate(preBookSaleItem.getDate());
                priceInfoDetail.setBasePrice(preBookSaleItem.getBasePrice());
                priceInfoDetail.setBasePrice(preBookSaleItem.getSalePrice());
                priceInfoDetail.setBreakfastType(preBookSaleItem.getBreakfastType());
                priceInfoDetail.setBreakfastNum(preBookSaleItem.getBreakfastNum());
                priceInfoDetail.setQuotaNum(preBookSaleItem.getQuotaNum());
                priceInfoDetail.setCanOverDraft(preBookSaleItem.getCanOverDraft());
                priceInfoDetail.setBaseCurrency(preBookSaleItem.getBaseCurrency());
                priceInfoDetail.setSettlementCurrency(preBookSaleItem.getSettlementCurrency());
                // 含义一致
                priceInfoDetail.setRoomStatus(preBookSaleItem.getRoomStatus());
                priceInfoDetail = ProductSalePriceConvert.INSTANCE.PriceInfoDetailConvert(preBookSaleItem);
                priceInfoDetails.add(priceInfoDetail);
            }
            orderRoomDetailDto.setPriceInfoDetails(priceInfoDetails);
        }
    }


    /**
     * 组装预定条款
     */
    private String assemblyTram(ProductDTO product) {
        return "条款限制，" + "产品需提前" + (product.getReservationAdvanceDays() == null ? 0 : product.getReservationAdvanceDays()) + "天" + product.getReservationDueTime() + "之前下单," +
                "预订间数大于等于" + (product.getReservationLimitRooms() == null ? 0 : product.getReservationLimitRooms()) + "间" + "," +
                "预订天数" + ComparisonTypeEnum.getDesc(product.getComparisonType()) + (product.getReservationLimitNights() == null ? 0 : product.getReservationLimitNights()) + "天";
    }

    /**
     * 直连供应商试预定
     */
    private ProductSalePriceDTO directPreBooking(PreBookDTO prebookDTO, AgentAccountConfig agentAccountConfig) {
        // 直连供应商
        PreBookingRequest preBookingRequest = new PreBookingRequest();
        preBookingRequest.setHotelId(prebookDTO.getHotelId().toString());
        preBookingRequest.setRoomId(prebookDTO.getRoomId().toString());
        preBookingRequest.setSpProductId(prebookDTO.getProductId());
        preBookingRequest.setSupplyCode(prebookDTO.getSupplyCode());
        preBookingRequest.setCheckInDate(prebookDTO.getCheckInDate());
        preBookingRequest.setCheckOutDate(prebookDTO.getCheckOutDate());
        preBookingRequest.setSupplyType(prebookDTO.getSupplyType());
        preBookingRequest.setRoomNum(prebookDTO.getRoomGuestNumbers().size());
//        preBookingRequest.setGuestCount(prebookDTO.getGuestQuantity());
        preBookingRequest.setRoomGuestNumbers(JSON.parseArray(JSON.toJSONString(prebookDTO.getRoomGuestNumbers()), RoomGuestNumber.class));
        preBookingRequest.setHourly(prebookDTO.getHourly());
        preBookingRequest.setUserAccount(prebookDTO.getUserAccount());
        preBookingRequest.setRequestId(prebookDTO.getRequestId());
        preBookingRequest.setCurrency(agentAccountConfig.getSettlementCurrency());
        preBookingRequest.setNationality(prebookDTO.getNationality());
        preBookingRequest.setRequestId(prebookDTO.getRequestId());
        Response<PreBookingResponse> response = supplyDirectShubOrderRemote.preBooking(preBookingRequest);
        PreBookingResponse preBookingResponse = response.getModel();
        if (response.isError()) {
            throw new SysException(response.getFailCode(), response.getFailReason());
        } else if (preBookingResponse == null || preBookingResponse.getCanBook() == null) {
            throw new SysException(ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR);
        } else if (preBookingResponse.getCanBook().equals(CanBookEnum.CAN_NOT_BOOK.value)) {
            throw new SysException(ErrorCodeEnum.SUPPLY_RETURN_CANNOT_BOOK);
        }
        ProductSalePriceDTO productSalePriceDTO = new ProductSalePriceDTO();
        productSalePriceDTO.setHotelId(prebookDTO.getHotelId());
        productSalePriceDTO.setRoomId(prebookDTO.getRoomId());
        productSalePriceDTO.setProductId(prebookDTO.getProductId());
        productSalePriceDTO.setSupplierCode(prebookDTO.getSupplyCode());

        List<com.tiangong.dto.order.response.OrderRoomDetailDto> roomItemDetails = Lists.newArrayList();
        if (CollUtilX.isNotEmpty(preBookingResponse.getCancelRestrictions())) {
            List<CancelRestriction> list = preBookingResponse.getCancelRestrictions().stream().map(e -> {
                        CancelRestriction cancelRestriction = new CancelRestriction();
                        cancelRestriction.setCancelRestrictionType(e.getCancelRestrictionType());
                        cancelRestriction.setCancelRestrictionDay(e.getCancelRestrictionDay());
                        cancelRestriction.setCancelRestrictionTime(e.getCancelRestrictionTime());
                        if (Objects.nonNull(e.getCancelRestrictionDay())) {
                            cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(prebookDTO.getCheckInDate()), -cancelRestriction.getCancelRestrictionDay(), 0)));
                        }
                        cancelRestriction.setCancellationPrice(e.getCancellationPrice());
                        return cancelRestriction;
                    }
            ).collect(Collectors.toList());
            productSalePriceDTO.setCancelRestrictions(list);
        }
        productSalePriceDTO.setInvoiceModel(preBookingResponse.getInvoiceModel());
        productSalePriceDTO.setInvoiceType(preBookingResponse.getInvoiceType());
        productSalePriceDTO.setGuaranteeType(preBookingResponse.getGuaranteeType());
        productSalePriceDTO.setGuaranteeCondition(preBookingResponse.getGuaranteeCondition());
        productSalePriceDTO.setGuaranteeFeeType(preBookingResponse.getGuaranteeFeeType());
        productSalePriceDTO.setGuaranteeDesc(preBookingResponse.getGuaranteeDesc());
        productSalePriceDTO.setHasIdCard(preBookingResponse.getHasIdCard());
        if (preBookingResponse.getPayMethod() != null && preBookingResponse.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            productSalePriceDTO.setPayAtHotelFlag(1);
        }
        if (preBookingResponse.getHourlyRoom() == 1) {
            preBookingResponse.setHourlyRoom(1);
            preBookingResponse.setHourlyRoomInfo(preBookingResponse.getHourlyRoomInfo());
            productSalePriceDTO.setHourlyRoom(1);
            productSalePriceDTO.setHourlyRoomInfo(preBookingResponse.getHourlyRoomInfo());
        }
        productSalePriceDTO.setTips(preBookingResponse.getTips());

        List<OrderRoomDetailDto> orderRoomDetails = preBookingResponse.getOrderRoomDetails();
        if (CollUtilX.isNotEmpty(orderRoomDetails)) {
            //查询加幅
            SupplyIncrease supplyIncrease = increaseUtil.getSupplyIncrease(prebookDTO.getAgentCode(), prebookDTO.getSupplyCode());
            //查询分销商-供应商-酒店加幅
            SupplyHotelIncrease supplyHotelIncrease = increaseUtil.getSupplyHotelIncrease(prebookDTO.getAgentCode(), prebookDTO.getSupplyCode(), prebookDTO.getHotelId());
            SupplierAddDTO supplierIncreaseConfig = increaseUtil.getSupplierIncrease(prebookDTO.getSupplyCode());

            roomItemDetails = orderRoomDetails.stream().map(priceInfoDetail -> {
                com.tiangong.dto.order.response.OrderRoomDetailDto roomItemDetail = convertRoomItemDetail(priceInfoDetail, agentAccountConfig);
                // 预付产品加幅，到店付产品不加幅
                if (Objects.isNull(productSalePriceDTO.getPayAtHotelFlag()) || Objects.equals(productSalePriceDTO.getPayAtHotelFlag(), 0)) {
                    // 预付
                    increaseUtil.agentIncrease(roomItemDetail, agentAccountConfig, supplierIncreaseConfig, supplyIncrease, supplyHotelIncrease, prebookDTO.getCheckInDate());
                } else {
                    // 现付
                    increaseUtil.agentIncrease(roomItemDetail, agentAccountConfig, prebookDTO.getCheckInDate());
                }
                return roomItemDetail;
            }).collect(Collectors.toList());

            // 不可定日期
            Set<String> notBookDateList = new HashSet<>();
            // 获取时间列表
            List<Date> dateList = DateUtilX.getDateList(DateUtilX.stringToDate(prebookDTO.getCheckInDate()), DateUtilX.getDate(DateUtilX.stringToDate(prebookDTO.getCheckOutDate()), -1, 0));

            // 判断是否可定
            int sum = 0;
            int canBookCount = 0;
            for (OrderRoomDetailDto roomItemDetail : orderRoomDetails) {
                sum = sum + dateList.size();
                Map<String, com.tiangong.supply.direct.remote.dto.PriceInfoDetail> priceItemMap = new HashMap<>();
                for (com.tiangong.supply.direct.remote.dto.PriceInfoDetail priceItem : roomItemDetail.getPriceInfoDetails()) {
                    priceItemMap.put(priceItem.getDate(), priceItem);
                }
                for (Date date : dateList) {
                    String dateStr = DateUtilX.dateToString(date);
                    if (priceItemMap.containsKey(dateStr)) {
                        com.tiangong.supply.direct.remote.dto.PriceInfoDetail priceItem = priceItemMap.get(dateStr);
                        if (priceItem.getRoomStatus() != null && priceItem.getRoomStatus() == RoomStateEnum.HAVA_ROOM.key &&
                                (priceItem.getCanOverDraft() == null || priceItem.getCanOverDraft() == 0)) {
                            if (priceItem.getQuotaNum() != null && priceItem.getQuotaNum() >= 0 &&
                                    (prebookDTO.getRoomQty() == null || priceItem.getQuotaNum().compareTo(prebookDTO.getRoomQty()) >= 0)) {
                                canBookCount++;
                            } else {
                                // 不可订日期
                                notBookDateList.add(dateStr);
                            }
                        } else {
                            canBookCount++;
                        }
                    } else {
                        // 不可订日期
                        notBookDateList.add(dateStr);
                    }
                }
            }
            if (sum == 0 || canBookCount == 0) {// 不可订
                productSalePriceDTO.setCanBook(CanBookEnum.CAN_NOT_BOOK.value);
            } else if (sum == canBookCount) {// 可订
                // 因为条款没有判断，需要判断总状态是否可订
                if (preBookingResponse.getCanBook() != null && preBookingResponse.getCanBook() == 1) {
                    productSalePriceDTO.setCanBook(CanBookEnum.CAN_BOOK.value);
                } else {
                    productSalePriceDTO.setCanBook(CanBookEnum.CAN_NOT_BOOK.value);
                }
            } else {// 部分可订
                productSalePriceDTO.setCanBook(CanBookEnum.CAN_PART_BOOK.value);
                productSalePriceDTO.setNotBookDateList(notBookDateList);// 不可定日期
            }

            //按间价格
            productSalePriceDTO.setRoomItemDetails(roomItemDetails);

            //组装每日售价
            increaseUtil.assemblyDaysSalePrice(productSalePriceDTO, agentAccountConfig);
        }
        // 试预订校验信用额度 2025-05-29 author: 李佳兴 去除直连产品试预定校验信用额度 QC6997 客户进行试预订时不需要校验客户的信用额度是否充足（目前自签产品没有进行校验
        //prebookCheckBalance(productSalePriceDTO, agentAccountConfig, preBookingResponse.getPayMethod(), prebookDTO.getRoomQty());
        return productSalePriceDTO;
    }

    /**
     *  校验信用额度
     * @param totalPrice 订单总金额
     * @param agentAccountConfig 分销商信息
     * @param payMethod 支付方式
     */
    private void checkBalance(BigDecimal totalPrice, AgentAccountConfig agentAccountConfig, Integer payMethod) {
        if (SettlementTypeEnum.SINGLE.key != agentAccountConfig.getSettlementType() && (Objects.isNull(payMethod) || payMethod != 1)) {//非单结，非到店付,判断剩余额度是否足够
            BigDecimal balance = agentAccountConfig.getBalance();

            // 订单金额小于信用额，提示信用额不足
            if (totalPrice.compareTo(balance) > 0) {
                throw new SysException(ErrorCodeEnum.INSUFFICIENT_AMOUNT.errorCode, ErrorCodeEnum.INSUFFICIENT_AMOUNT.errorDesc);
            }
        }
    }

    /**
     * 转换供应端每间价格到订单
     */
    private com.tiangong.dto.order.response.OrderRoomDetailDto convertRoomItemDetail(OrderRoomDetailDto orderRoomDetailDto, AgentAccountConfig agentAccountConfig) {
        com.tiangong.dto.order.response.OrderRoomDetailDto roomItemDetail = new com.tiangong.dto.order.response.OrderRoomDetailDto();
        roomItemDetail.setRoomIndex(orderRoomDetailDto.getRoomIndex());
        roomItemDetail.setAdultNum(orderRoomDetailDto.getAdultNum());
        roomItemDetail.setChildAges(orderRoomDetailDto.getChildAges());
        roomItemDetail.setPayInStoreCurrency(orderRoomDetailDto.getPayInStoreCurrency());
        roomItemDetail.setPayInStorePrice(orderRoomDetailDto.getPayInStorePrice());
        roomItemDetail.setTaxDetail(JSON.parseObject(JSON.toJSONString(orderRoomDetailDto.getTaxDetail()), com.tiangong.dto.product.TaxDetailDto.class));

        if (CollUtilX.isNotEmpty(orderRoomDetailDto.getPriceInfoDetails())) {
            List<com.tiangong.dto.product.PriceInfoDetail> priceItems = orderRoomDetailDto.getPriceInfoDetails().stream().map(this::convertPriceItem).collect(Collectors.toList());
            roomItemDetail.setPriceInfoDetails(priceItems);
        }

        return roomItemDetail;
    }

    /**
     * 转换PriceItem
     */
    private com.tiangong.dto.product.PriceInfoDetail convertPriceItem(com.tiangong.supply.direct.remote.dto.PriceInfoDetail priceInfoDetail) {
        com.tiangong.dto.product.PriceInfoDetail priceItem = new com.tiangong.dto.product.PriceInfoDetail();
        priceItem.setDate(priceInfoDetail.getDate());
        priceItem.setBasePrice(priceInfoDetail.getPrice());
        priceItem.setBaseCurrency(priceInfoDetail.getCurrency());
        priceItem.setRoomStatus(priceInfoDetail.getRoomStatus());
        priceItem.setCanOverDraft(priceInfoDetail.getCanOverDraft());
        priceItem.setBreakfastNum(priceInfoDetail.getBreakfastNum());
        priceItem.setBreakfastType(priceInfoDetail.getBreakfastType());
        priceItem.setQuotaNum(priceInfoDetail.getQuotaNum());
        priceItem.setTaxDetail(JSON.parseObject(JSON.toJSONString(priceInfoDetail.getTaxDetail()), com.tiangong.dto.product.TaxDetailDto.class));
        return priceItem;
    }

    @Override
    public OtaOrderDTO queryOtaOrder(QueryOtaOrderDTO queryOtaOrderDTO) {
        return orderMapper.queryOtaOrder(queryOtaOrderDTO);
    }

    /**
     * 组装手工单参数
     */
    private AssemblyOrderDTO assemblyOrderData(AddManualOrderDTO request, Integer settlementType, AgentAccountConfig agentAccountConfig) {
        // 获取商家币种
        com.tiangong.finance.OrgDTO orgDTO = CommonInitializer.getOrgInfo();
        int orgCurrency = Integer.parseInt(orgDTO.getOrgCurrency());

        //查询汇率
        BigDecimal baseRate = (null == request.getBaseCurrency() ? BigDecimal.ONE : RedisUtil.getRateToOrgCurrency(request.getBaseCurrency(), CompanyDTO.COMPANY_CODE));
        BigDecimal saleRate = (null == request.getSaleCurrency() ? BigDecimal.ONE : RedisUtil.getRateToOrgCurrency(request.getSaleCurrency(), CompanyDTO.COMPANY_CODE));
        BigDecimal orgToAgentRate = (null == request.getSaleCurrency() ? BigDecimal.ONE : RedisUtil.getRateToTargetCurrency(orgCurrency, CompanyDTO.COMPANY_CODE, request.getSaleCurrency()));

        if (baseRate == null || saleRate == null || orgToAgentRate == null) {
            throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorCode, ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorDesc);
        }

        //组装订单
        AssemblyOrderDTO assemblyOrderDTO = new AssemblyOrderDTO();
        String currentDate = DateUtilX.dateToString(new Date(), hour_format);
        //查询佣金
        BigDecimal commission = new BigDecimal(0);
        long day = DateUtilX.getDay(DateUtilX.stringToDate(request.getStartDate()), DateUtilX.stringToDate(request.getEndDate()));
        boolean instantConfirm = true;
        for (int i = 0; i < day; i++) {
            ProductBasePriceAndRoomStatusDTO psDto = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productBasePriceAndRoomStatusKey,
                    request.getProductId() + "_" + DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(request.getStartDate()), i))), ProductBasePriceAndRoomStatusDTO.class);
            commission = commission.add(psDto == null ? new BigDecimal(0) : (psDto.getCommission() == null ? BigDecimal.ZERO : psDto.getCommission()));

            if (null == psDto || null == psDto.getQuota() || psDto.getQuota() < request.getRoomQty()) {
                instantConfirm = false;
            }
        }

        //佣金需要乘以间数
        commission = commission.multiply(new BigDecimal(request.getRoomQty()));
        OrderPO orderPO = new OrderPO();
        OrderExtendPO orderExtendPO = new OrderExtendPO();
        BeanUtils.copyProperties(request, orderPO);
        BeanUtils.copyProperties(request, orderExtendPO);
//        OrderPO orderPO = ProductSalePriceConvert.INSTANCE.OrderPOConvert(request);
        //fixme mapper类型转换BUG
//        OrderExtendPO orderExtendPO = ProductSalePriceConvert.INSTANCE.orderExtendPOConvert(request);
        // 价格计划名称存在中文直接转换成指定英文
        if (LanguageTypeEnum.en_US.getValue().equals(request.getLanguage()) && StrUtilX.isNotEmpty(orderPO.getProductName())) {
            if (StrUtilX.notIsEnglishAndSpace(orderPO.getProductName())) {
                orderPO.setProductName("Business travel rate");
            }
        }
        if (orderPO.getSaleCurrency() == null) {
            //设置默认币种 0
            orderPO.setSaleCurrency(Integer.parseInt(CommonInitializer.getOrgInfo().getOrgCurrency()));
        }
        if (StrUtilX.isNotEmpty(request.getHourlyTime())) {
            orderPO.setHourly(1);
            String[] arr = request.getHourlyTime().split("\\--");
            orderPO.setStartTime(DateUtilX.stringToDate(arr[0], hour_format));
            orderPO.setEndTime(DateUtilX.stringToDate(arr[1], hour_format));
        } else {
            orderPO.setHourly(0);
        }

        if (StrUtilX.isNotEmpty(request.getArrivalTime())) {
            String[] split = request.getArrivalTime().split("~");
            orderPO.setEarliestArriveTime(split[0]);///
            orderPO.setLatestArriveTime(split[1]);
        }
//        //到店付设置支付状态为空
//        if (request.getPayMethod() != null && request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
//            orderPO.setPayStatus(null);
//        }
        // 设置产品类型 为空默认预付
        if (request.getPayMethod() != null) {
            orderPO.setPayMethod(request.getPayMethod());
        } else {
            orderPO.setPayMethod(PayMethodEnum.PREPAY.key);
        }
        // 如果是现付，订单金额为0
        if (orderPO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            orderPO.setOrderAmt(BigDecimal.ZERO);
        }
        //设置供应商标签
        Object supplierLabel = RedisTemplateX.hGet(RedisKey.AGENT_SUPPLIER_LABEL, request.getAgentCode() + "_" + request.getSupplierCode());
        if (Objects.nonNull(supplierLabel)) {
            orderPO.setSupplierLabel(Integer.parseInt(supplierLabel.toString()));
        }
        orderPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
        orderPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
        orderPO.setIsManualOrder(1);
        orderPO.setIsSubstituted(1);
        orderExtendPO.setIsShowOnSupplyOrder(0);
        if (StrUtilX.isNotEmpty(orderExtendPO.getSpecialRequest())) {
            orderExtendPO.setIsShowOnSupplyOrder(1);
        }
        orderPO.setOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
        orderPO.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key.toString());
        orderPO.setSalePrice(request.getOrderAmt());
        orderPO.setRefundFee(BigDecimal.ZERO);
        orderPO.setMarkedStatus(0);
        orderPO.setModificationStatus(0);
        orderPO.setProductLabel(0);
        orderPO.setSaleRate(saleRate);
        orderPO.setOrgToAgentRate(orgToAgentRate);// 商家币种转客户币种汇率
        // 分割价格价格计划ID获取协议产品
        if (StrUtilX.isNotEmpty(request.getConversionProductId())) {
            String[] s = request.getConversionProductId().split("_");
            if (s.length >= 5) {
                orderPO.setProductLabel(Integer.valueOf(s[4]));
            }
        }
        orderPO.setCreatedBy(request.getOperator());
        orderPO.setCreatedDt(currentDate);
        orderPO.setProfit(request.getOrderAmt().subtract(request.getSupplyOrderAmt().subtract(commission).multiply(baseRate).setScale(2, RoundingMode.UP)));
        //即时确认逻辑
        if (instantConfirm) {
            orderPO.setInstantConfirmationStatus(1);
        } else {
            orderPO.setInstantConfirmationStatus(0);
        }

        //查询酒店基本信息
        // 2019/5/6 调酒店基本信息接口
        HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
        hotelInfoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
        hotelInfoReq.setLanguageType(StrUtilX.isNotEmpty(request.getLanguage()) ? request.getLanguage() : LanguageTypeEnum.en_US.getValue());
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.location);
        settings.add(BaseHotelInfoUrl.rooms);
        hotelInfoReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> hotelInfoResponse = hotelRemote.queryHotelInfo(hotelInfoReq);
        if (Objects.nonNull(hotelInfoResponse)) {
            HotelInfoCollectionDTO hotelInfo = hotelInfoResponse.getModel();
            if (Objects.nonNull(hotelInfo)) {
                orderPO.setHotelName(hotelInfo.getHotelName());
                if (Objects.nonNull(hotelInfo.getLocation())) {
                    orderPO.setCityCode(hotelInfo.getLocation().getCity());
                    orderPO.setCityName(hotelInfo.getLocation().getCityName());
                    orderPO.setCountryCode(hotelInfo.getLocation().getCountry());
                    orderPO.setCnType(getOrderCnType(orderPO.getCountryCode()));
                    orderPO.setProvinceCode(hotelInfo.getLocation().getProvince());
                }

                if (CollUtilX.isNotEmpty(hotelInfo.getRooms())) {
                    for (RoomtypeDTO room : hotelInfo.getRooms()) {
                        if (room.getRoomtypeId().intValue() == request.getRoomId()) {
                            orderPO.setRoomName(room.getRoomtypeName());
                            if (StrUtilX.isNotEmpty(room.getRoomtypeEngName())) {
                                orderPO.setRoomName(orderPO.getRoomName() + "(" + room.getRoomtypeEngName() + ")");
                            }
                            orderPO.setWindowDetail(room.getWindowDetail());
                        }
                    }
                }
            }
        }

        // 查询分销商
        if (settlementType != null) {
            orderPO.setSettlementType(settlementType);
        } else {
            orderPO.setSettlementType(SettlementTypeEnum.MONTH.key);
        }

        // 设置扣减额度账户
        if (StrUtilX.isNotEmpty(agentAccountConfig.getLineAccount())) {
            orderPO.setSubtractLineAccount(agentAccountConfig.getLineAccount());
        } else {
            orderPO.setSubtractLineAccount(agentAccountConfig.getAgentCode());
        }

        // 设置条款
        if (CollUtilX.isNotEmpty(request.getCancelRestrictions())) {
            // 设置条款币种
            Integer restrictionCurrency = request.getProductCurrency() == null ? agentAccountConfig.getSettlementCurrency() : request.getProductCurrency();
            request.getCancelRestrictions().forEach(item -> {
                if (Objects.nonNull(item.getCancelRestrictionDay())) {
                    item.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(request.getStartDate()), -item.getCancelRestrictionDay(), 0)));
                }
                // 取消罚金处理
                if (item.getCancelPenaltiesType() != null && item.getCancelPenaltiesValue() != null) {
                    List<CancelPenalDTO> cancelPenalties = new ArrayList<>();
                    CancelPenalDTO cancelPenalDTO = new CancelPenalDTO();
                    cancelPenalDTO.setPenaltiesType(item.getCancelPenaltiesType());
                    cancelPenalDTO.setStartDate(DateUtilX.dateToString((DateUtilX.getDate(DateUtilX.stringToDate(request.getStartDate()),
                            -item.getCancelRestrictionDay())), "yyyy/MM/dd") + " " + item.getCancelRestrictionTime());
                    cancelPenalDTO.setEndData(cancelPenalDTO.getStartDate());
                    if (item.getCancelPenaltiesType().equals(2) && restrictionCurrency != null && restrictionCurrency.compareTo(agentAccountConfig.getSettlementCurrency()) != 0) {
                        // 转换商家币种
                        BigDecimal orgRate = BigDecimal.ONE;
                        if (new BigDecimal(restrictionCurrency).compareTo(new BigDecimal(orgCurrency)) != 0) {
                            orgRate = RedisUtil.getRateToOrgCurrency(restrictionCurrency, CompanyDTO.COMPANY_CODE);
                            if (orgRate == null) {
                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                            }
                        }
                        // 转换汇率
                        BigDecimal cancelPenaltiesAmt = CommonTgUtils.setRate(BigDecimal.valueOf(item.getCancelPenaltiesValue()), orgRate);
                        // 转换客户币种
                        BigDecimal agentRate = BigDecimal.ONE;
                        if (new BigDecimal(orgCurrency).compareTo(new BigDecimal(agentAccountConfig.getSettlementCurrency())) != 0) {
                            agentRate = RedisUtil.getRateToTargetCurrency(orgCurrency, CompanyDTO.COMPANY_CODE, agentAccountConfig.getSettlementCurrency());
                            if (agentRate == null) {
                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                            }
                        }
                        // 转换汇率
                        cancelPenaltiesAmt = CommonTgUtils.setRate(cancelPenaltiesAmt, agentRate);
                        // 根据客户配置进位
                        BigDecimal cancelPenaltiesValue = CommonTgUtils.setScale(cancelPenaltiesAmt, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                        cancelPenalDTO.setPenaltiesValue(String.valueOf(cancelPenaltiesValue));
                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentAccountConfig.getSettlementCurrency())));
                    } else {
                        cancelPenalDTO.setPenaltiesValue(String.valueOf(item.getCancelPenaltiesValue()));
                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(restrictionCurrency)));
                    }
                    cancelPenalties.add(cancelPenalDTO);
                    item.setCancelRestrictionRemark(JSONUtil.toJsonStr(cancelPenalties));
                }
                if (item.getCancelPenaltiesType() != null && item.getCancelPenaltiesValue() != null) {
                    StringBuilder stringBuilder = new StringBuilder();
                    if (item.getCancelPenaltiesType().equals(4)) {
                        if (item.getCancelPenaltiesValue().equals(100.0)) {
                            stringBuilder.append("取消或更改,收取首晚房费");
                        } else {
                            stringBuilder.append("取消或更改,扣首晚房费的").append(item.getCancelPenaltiesValue()).append("%，作为退订费。");
                        }
                    } else if (item.getCancelPenaltiesType().equals(3)) {
                        stringBuilder.append("取消或更改,扣全额房费的").append(item.getCancelPenaltiesValue()).append("%，作为退订费");
                    }
                    item.setCancelRestrictionRemark(stringBuilder.toString());
                }
            });
            orderExtendPO.setCancellationTerm(JSONUtil.toJsonStr(request.getCancelRestrictions()));
        }
        orderPO.setContactName(request.getContactName());
        orderPO.setContactPhone(request.getContactPhone());

        OrderFinancePO orderFinancePO = new OrderFinancePO();
        orderFinancePO.setReceivedAmt(BigDecimal.ZERO);
        orderFinancePO.setUnreceivedAmt(orderPO.getOrderAmt());
        orderFinancePO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
        orderFinancePO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
        orderFinancePO.setSettlementStatus(0);
        orderFinancePO.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
        orderFinancePO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
        orderFinancePO.setFinanceLockStatus(0);
        orderFinancePO.setCreatedBy(request.getOperator());
        orderFinancePO.setCreatedDt(currentDate);
        assemblyOrderDTO.setOrderFinance(orderFinancePO);

        // 设置入住人
        assemblyOrderDTO.setGuestList(new ArrayList<>());
        StringBuilder guestSb = new StringBuilder();
        int adultQty = 0;
        int childrenQty = 0;
        //房间人数
        Map<Integer, OrderRoomGuestDTO> roomGuestQtyMap = new HashMap<>();
        if (CollUtilX.isNotEmpty(request.getRoomGuestList())) {
            assemblyOrderDTO.setGuestList(new ArrayList<>());

            for (OrderRoomGuestDTO guest : request.getRoomGuestList()) {
                roomGuestQtyMap.put(guest.getRoomNumber(), guest);
                if (guest.getAdultQty() != null) {
                    adultQty = adultQty + guest.getAdultQty();
                }
                if (guest.getChildrenQty() != null) {
                    childrenQty = childrenQty + guest.getChildrenQty();
                }
                for (OrderGuestDTO orderGuestDTO : guest.getOrderGuestList()) {
                    if (StrUtilX.isNotEmpty(orderGuestDTO.getName())) {
                        guestSb.append(orderGuestDTO.getName()).append(",");
                    } else {
                        guestSb.append(orderGuestDTO.getLastName()).append("/").append(orderGuestDTO.getFirstName()).append(",");
                    }

                    GuestPO guestPO = new GuestPO();
                    guestPO.setRoomNumber(guest.getRoomNumber());
                    guestPO.setAdultQty(guest.getAdultQty());
                    guestPO.setChildrenQty(guest.getChildrenQty());
                    //累计成人数量

                    guestPO.setChildrenAge(guest.getChildrenAge());
                    guestPO.setOrderId(orderGuestDTO.getOrderId());
                    guestPO.setFirstName(orderGuestDTO.getFirstName());
                    guestPO.setLastName(orderGuestDTO.getLastName());
                    guestPO.setName(orderGuestDTO.getName());

                    if (StrUtilX.isEmpty(orderGuestDTO.getName())) {
                        guestPO.setName(orderGuestDTO.getLastName() + "/" + orderGuestDTO.getFirstName());
                    }
                    guestPO.setNationality(orderGuestDTO.getNationality());
                    guestPO.setMobileNo(orderGuestDTO.getMobileNo());
                    guestPO.setIdCardType(orderGuestDTO.getIdCardType());
                    guestPO.setIdCardNo(orderGuestDTO.getIdCardNo());
                    guestPO.setMembershipCardNumber(orderGuestDTO.getMembershipCardNumber());
//                guestPO.setRoomNumber(guest.getRoomNumber());
                    guestPO.setEarnPoints(orderGuestDTO.getEarnPoints());
                    assemblyOrderDTO.getGuestList().add(guestPO);
                }
            }
            guestSb.deleteCharAt(guestSb.length() - 1);
        }
        orderPO.setGuest(guestSb.toString().replaceAll("/", ""));
        orderPO.setAdultQty(null != request.getAdultQty() ? request.getAdultQty() : adultQty);
        orderPO.setChildrenQty(null != request.getChildrenQty() ? request.getChildrenQty() : childrenQty);

        //组装供货单
        AssemblySupplyOrderDTO assemblySupplyOrderDTO = new AssemblySupplyOrderDTO();
        assemblyOrderDTO.setSupplyOrderList(Collections.singletonList(assemblySupplyOrderDTO));
        SupplyOrderPO supplyOrderPO = new SupplyOrderPO();
        BeanUtils.copyProperties(request, supplyOrderPO);

//        SupplyOrderPO supplyOrderPO = ProductSalePriceConvert.INSTANCE.supplyOrderPOConvert(request);
        // 现付类型供货单金额为0
        if (request.getPayMethod() != null && request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            supplyOrderPO.setSupplyOrderAmt(BigDecimal.ZERO);
        }
        supplyOrderPO.setBasePrice(request.getSupplyOrderAmt());
        supplyOrderPO.setCommission(commission);
        supplyOrderPO.setRefundFee(BigDecimal.ZERO);
        supplyOrderPO.setStartDate(orderPO.getStartDate());
        supplyOrderPO.setEndDate(orderPO.getEndDate());
        supplyOrderPO.setStartTime(orderPO.getStartTime());
        supplyOrderPO.setEndTime(orderPO.getEndTime());

        supplyOrderPO.setConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
        supplyOrderPO.setSendingStatus(SendingStatusEnum.UNSEND.key);
        supplyOrderPO.setCreatedBy(request.getOperator());
        supplyOrderPO.setCreatedDt(currentDate);
        supplyOrderPO.setSupplierOrderCode(request.getSupplierOrderCode());

        // 查询供应商缓存
        AddSupplierReq supplier = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, request.getSupplierCode()).toString(), AddSupplierReq.class);
        if (supplier == null) {
            log.error("供应商缓存不存在，supplyCode={}", request.getSupplierCode());
            throw new SysException(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorCode, ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorDesc);
        }
        if (null != supplier.getPurchaseManagerId()) {
            supplyOrderPO.setMerchantPm(supplier.getPurchaseManagerId().toString());
        }
        supplyOrderPO.setSettlementType(supplier.getSettlementType());
        supplyOrderPO.setRate(baseRate);
        supplyOrderPO.setRewardAmt(BigDecimal.ZERO);
        supplyOrderPO.setRebateAmt(BigDecimal.ZERO);
        // 设置房间号信息
        Set<Long> collect = request.getRoomGuestList().stream().filter(item -> item.getRoomNumber() != null).map(item -> Long.valueOf(item.getRoomNumber())).collect(Collectors.toSet());
        supplyOrderPO.setRoomNumbers(StrUtilX.listToString(collect));
        assemblySupplyOrderDTO.setSupplyOrder(supplyOrderPO);

        SupplyOrderFinancePO supplyOrderFinancePO = new SupplyOrderFinancePO();
        supplyOrderFinancePO.setPaidAmt(BigDecimal.ZERO);
        supplyOrderFinancePO.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt());
        supplyOrderFinancePO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
        supplyOrderFinancePO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
        supplyOrderFinancePO.setSettlementStatus(0);
        supplyOrderFinancePO.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
        supplyOrderFinancePO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
        supplyOrderFinancePO.setFinanceLockStatus(0);
        supplyOrderFinancePO.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        supplyOrderFinancePO.setCreatedBy(request.getOperator());
        supplyOrderFinancePO.setCreatedDt(currentDate);
        assemblySupplyOrderDTO.setSupplyOrderFinance(supplyOrderFinancePO);
        //供应商奖励
        SupplyOrderFinancePO supplyRewardFinancePO = ProductSalePriceConvert.INSTANCE.supplyRewardFinancePOConvert(supplyOrderFinancePO);
        supplyRewardFinancePO.setUnpaidAmt(BigDecimal.ZERO);
        supplyRewardFinancePO.setFinanceType(StatementTypeEnum.REWARD_AMT.key);
        assemblySupplyOrderDTO.setSupplyRewardFinance(supplyRewardFinancePO);
        //供应商返佣
        SupplyOrderFinancePO supplyRebateFinancePO = ProductSalePriceConvert.INSTANCE.supplyRewardFinancePOConvert(supplyRewardFinancePO);
        supplyRebateFinancePO.setFinanceType(StatementTypeEnum.REBATE_AMT.key);
        assemblySupplyOrderDTO.setSupplyRebateFinance(supplyRebateFinancePO);

        //组装供货产品
        AssemblySupplyProductDTO assemblySupplyProductDTO = new AssemblySupplyProductDTO();
        assemblySupplyOrderDTO.setSupplyProductList(Collections.singletonList(assemblySupplyProductDTO));
        SupplyProductPO supplyProductPO = new SupplyProductPO();
        BeanUtils.copyProperties(request, supplyProductPO);
        //fixme mapper类型转换BUG
//        SupplyProductPO supplyProductPO = ProductSalePriceConvert.INSTANCE.SupplyProductPOConvert(request);
        supplyProductPO.setStartDate(orderPO.getStartDate());
        supplyProductPO.setEndDate(orderPO.getEndDate());
        supplyProductPO.setGuest(guestSb.toString());
        supplyProductPO.setBasePriceTotalAmt(request.getSupplyOrderAmt());
        supplyProductPO.setCommission(commission);
        supplyProductPO.setCancellationTerm(orderExtendPO.getCancellationTerm());
        supplyProductPO.setCreatedBy(request.getOperator());
        supplyProductPO.setCreatedDt(currentDate);
        assemblySupplyProductDTO.setSupplyProduct(supplyProductPO);

        //组装价格
        assemblySupplyProductDTO.setSupplyProductPriceList(new ArrayList<>());
        assemblyOrderDTO.setOrderProductPriceList(new ArrayList<>());

        BigDecimal payAtHotelFeeTotal = BigDecimal.ZERO;
        BigDecimal payAtHotelAgentCurrencyFeeTotal = BigDecimal.ZERO;
        if (CollUtilX.isNotEmpty(request.getOrderRoomPriceList())) {
            for (OrderRoomPriceDTO orderRoomPriceDTO : request.getOrderRoomPriceList()) {
                if (orderRoomPriceDTO.getPayAtHotelFee() != null) {
                    payAtHotelFeeTotal = payAtHotelFeeTotal.add(orderRoomPriceDTO.getPayAtHotelFee());
                    orderPO.setPayAtHotelCurrency(orderRoomPriceDTO.getPayAtHotelCurrency());
                }
                if (orderRoomPriceDTO.getPayAtHotelAgentCurrencyFee() != null) {
                    payAtHotelAgentCurrencyFeeTotal = payAtHotelAgentCurrencyFeeTotal.add(orderRoomPriceDTO.getPayAtHotelAgentCurrencyFee());
                }
                assemblyOrderPrice(assemblyOrderDTO, assemblySupplyProductDTO,
                        null == request.getSaleCurrency() ? 0 : request.getSaleCurrency(),
                        null == supplier.getSettlementCurrency() ? 0 : supplier.getSettlementCurrency(),
                        orderRoomPriceDTO, orderRoomPriceDTO.getRoomNumber(),
                        roomGuestQtyMap.get(orderRoomPriceDTO.getRoomNumber()));
            }
        }
        orderPO.setPayAtHotelFee(payAtHotelFeeTotal);
        orderPO.setPayAtHotelAgentCurrencyFee(payAtHotelAgentCurrencyFeeTotal);
        orderExtendPO.setCreatedDt(currentDate);
        orderExtendPO.setCreatedBy(orderPO.getCreatedBy());
        assemblyOrderDTO.setOrderExtendPO(orderExtendPO);
        assemblyOrderDTO.setOrder(orderPO);
        return assemblyOrderDTO;
    }

    /**
     * 根据订单国家编码 生产订单是否中国订单属性
     */
    private static int getOrderCnType(String countryCode) {
        //CN 77 104 176
        if (StringUtils.isNotEmpty(countryCode) && (countryCode.equals("CN") || countryCode.equals("77") || countryCode.equals("104") || countryCode.equals("176"))) {
            return 1;
        }
        return 0;
    }

    /**
     * 手工单组装价格
     */
    private void assemblyOrderPrice(AssemblyOrderDTO assemblyOrderDTO, AssemblySupplyProductDTO assemblySupplyProductDTO,
                                    Integer saleCurrency, Integer baseCurrency, OrderRoomPriceDTO orderRoomPriceDTO, Integer roomNumber,
                                    OrderRoomGuestDTO orderRoomGuestDTO) {
        for (PriceRequestDTO priceDTO : orderRoomPriceDTO.getPriceList()) {
            OrderProductPricePO orderProductPricePO = new OrderProductPricePO();
            orderProductPricePO.setSaleDate(DateUtilX.stringToDate(priceDTO.getSaleDate()));
            orderProductPricePO.setSalePrice(priceDTO.getSalePrice());
            orderProductPricePO.setRoomPrice(priceDTO.getSalePrice());
            orderProductPricePO.setRoomNumber(roomNumber);
            orderProductPricePO.setCurrency(saleCurrency);
            orderProductPricePO.setDiscount(BigDecimal.ZERO);
            orderProductPricePO.setBreakfastType(priceDTO.getBreakfastType());
            orderProductPricePO.setBreakfastQty(priceDTO.getBreakfastNum());
            String roomPersons = null;
            if (null != orderRoomGuestDTO) {
                roomPersons = orderRoomGuestDTO.getAdultQty().toString();
                if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                    roomPersons += ("," + orderRoomGuestDTO.getChildrenAge());
                }
            }
            orderProductPricePO.setRoomPersons(roomPersons);
            assemblyOrderDTO.getOrderProductPriceList().add(orderProductPricePO);

            SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
            supplyProductPricePO.setSaleDate(DateUtilX.stringToDate(priceDTO.getSaleDate()));
            supplyProductPricePO.setBasePrice(priceDTO.getBasePrice());
            supplyProductPricePO.setRoomPrice(priceDTO.getBasePrice());
            supplyProductPricePO.setRoomNumber(roomNumber);
            supplyProductPricePO.setCurrency(baseCurrency);
            supplyProductPricePO.setDiscount(BigDecimal.ZERO);
            supplyProductPricePO.setBreakfastType(priceDTO.getBreakfastType());
            supplyProductPricePO.setBreakfastQty(priceDTO.getBreakfastNum());
            supplyProductPricePO.setRoomPersons(roomPersons);
            assemblySupplyProductDTO.getSupplyProductPriceList().add(supplyProductPricePO);
        }

        if (Objects.nonNull(orderRoomPriceDTO.getPayAtHotelFee()) && Objects.nonNull(orderRoomPriceDTO.getPayAtHotelCurrency())) {
            OrderProductPricePO orderProductPricePO = new OrderProductPricePO();
            orderProductPricePO.setRoomNumber(roomNumber);
            orderProductPricePO.setPayAtHotelFee(orderRoomPriceDTO.getPayAtHotelFee());
            orderProductPricePO.setPayAtHotelAgentCurrencyFee(orderRoomPriceDTO.getPayAtHotelAgentCurrencyFee());
            orderProductPricePO.setPayAtHotelCurrency(orderRoomPriceDTO.getPayAtHotelCurrency());
            orderProductPricePO.setCurrency(saleCurrency);
            String roomPersons = null;
            if (null != orderRoomGuestDTO) {
                roomPersons = orderRoomGuestDTO.getAdultQty().toString();
                if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                    roomPersons += ("," + orderRoomGuestDTO.getChildrenAge());
                }
            }
            orderProductPricePO.setRoomPersons(roomPersons);
            assemblyOrderDTO.getOrderProductPriceList().add(orderProductPricePO);

            SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
            supplyProductPricePO.setRoomNumber(roomNumber);
            supplyProductPricePO.setCurrency(baseCurrency);
            supplyProductPricePO.setRoomPersons(roomPersons);
            supplyProductPricePO.setPayAtHotelFee(orderRoomPriceDTO.getPayAtHotelFee());
            supplyProductPricePO.setPayAtHotelCurrency(orderRoomPriceDTO.getPayAtHotelCurrency());
            assemblySupplyProductDTO.getSupplyProductPriceList().add(supplyProductPricePO);
        }

    }

    /**
     * 组装订单数据
     */
    private AssemblyOrderDTO assemblyOTAOrderData(AddManualOrderDTO request, ProductSalePriceDTO productSalePriceDTO, AgentAccountConfig accountConfig, HotelInfoCollectionDTO hotelInfo) {
        // 客户币种转商家币种汇率
        BigDecimal agentToOrgRate = null;
        // 供应商币种转商家币种汇率
        BigDecimal supplierToOrgRate = null;
        // 商家币种转客户币种汇率
        BigDecimal orgToAgentRate = null;

        // 获取商家币种
        com.tiangong.finance.OrgDTO orgDTO = CommonInitializer.getOrgInfo();

        // 组装订单
        AssemblyOrderDTO assemblyOrderDTO = new AssemblyOrderDTO();
        String currentDate = DateUtilX.dateToString(new Date(), hour_format);
        AssemblySupplyOrderDTO assemblySupplyOrderDTO = new AssemblySupplyOrderDTO();
        AssemblySupplyProductDTO assemblySupplyProductDTO = new AssemblySupplyProductDTO();
        // 查询佣金
        BigDecimal commission = new BigDecimal(0);
        long day = DateUtilX.getDay(DateUtilX.stringToDate(request.getStartDate()), DateUtilX.stringToDate(request.getEndDate()));
        for (int i = 0; i < day; i++) {
            ProductBasePriceAndRoomStatusDTO psDto = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productBasePriceAndRoomStatusKey,
                    request.getProductId() + "_" + DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(request.getStartDate()), i))), ProductBasePriceAndRoomStatusDTO.class);
            commission = commission.add(psDto == null ? new BigDecimal(0) : (psDto.getCommission() == null ? BigDecimal.ZERO : psDto.getCommission()));
        }
        //佣金需要乘以间数
        commission = commission.multiply(new BigDecimal(request.getRoomQty()));
        OrderPO orderPO = new OrderPO();
        OrderExtendPO orderExtendPO = new OrderExtendPO();
        BeanUtils.copyProperties(request, orderPO);
        BeanUtils.copyProperties(request, orderExtendPO);
        //fixme mapper类型转换BUG，入住时间转换错误
//        OrderPO orderPO = ProductSalePriceConvert.INSTANCE.OrderPOConvert(request);
        //fixme mapper类型转换BUG
//        OrderExtendPO orderExtendPO = ProductSalePriceConvert.INSTANCE.orderExtendPOConvert(request);
        //设置订单出行方式
        orderExtendPO.setOrderTrafficJson(JSONObject.toJSONString(request.getOrderTrafficJson()));

        //设置发票模式
//        orderPO.setInvoiceModel(productSalePriceDTO.getInvoiceModel());
//        orderPO.setInvoiceType(productSalePriceDTO.getInvoiceType());
        orderPO.setAgentName(accountConfig.getAgentName());
        orderPO.setContactEmail(request.getContactEmail());
        if (CollUtilX.isNotEmpty(request.getBedInfos())) {
            BedTypesDetailDto bedTypesDetailDto = new BedTypesDetailDto();
            List<BedInfoDto> bedInfos = ProductSalePriceConvert.INSTANCE.bedInfoDtoConvert(request.getBedInfos());
            bedTypesDetailDto.setBedInfos(bedInfos);
            orderPO.setBedType(JSONUtil.toJsonStr(bedTypesDetailDto));
        }

        // 原金额，比对金额用
        BigDecimal orderAmt = orderPO.getOrderAmt();
        // 快速处理标签开关
        orderPO.setQuickProcessingSwitch(productSalePriceDTO.getQuickProcessingSwitch());

        // 售价币种通过分销商编码获取
        if (orderPO.getSaleCurrency() == null) {
            orderPO.setSaleCurrency(accountConfig.getSettlementCurrency());
        }
        // 如果是现付，订单金额为0
        if (request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            orderPO.setOrderAmt(BigDecimal.ZERO);
            //如果到店付，发票模式统一为酒店前台开票
            orderPO.setInvoiceModel(2);
        } else {
            orderPO.setPayMethod(request.getPayMethod());
        }

        // 服务费（属于附加费）
        if (request.getServiceCharge() != null && request.getServiceCharge().compareTo(BigDecimal.ZERO) != 0){
            // 订单应收需加上附加费
            orderPO.setAdditionalCharges(request.getServiceCharge());
            orderPO.setOrderAmt(orderPO.getOrderAmt().add(request.getServiceCharge()));
        }

        //设置剩余额度
        orderPO.setRemainingBalance(accountConfig.getBalance().subtract(orderPO.getOrderAmt()));
        if (CollUtilX.isNotEmpty(request.getBedInfos())) {
            BedTypesDetailDto bedTypesDetailDto = new BedTypesDetailDto();
            List<BedInfoDto> bedInfos = ProductSalePriceConvert.INSTANCE.bedInfoDtoConvert(request.getBedInfos());
            bedTypesDetailDto.setBedInfos(bedInfos);
            orderExtendPO.setBedType(JSONUtil.toJsonStr(bedTypesDetailDto));
        }
        //床型描述 author:湫 2024-07-29 qc973 【天宫&B2B-EPS】产品的床型信息传递
        if (StrUtilX.isNotEmpty(request.getBedInfoDesc())) {
            orderExtendPO.setBedInfoDesc(request.getBedInfoDesc());
        }
        orderPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
        orderPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
        orderPO.setConversionProductId(request.getConversionProductId());
//        ProductCacheMapping productCacheMapping = getProductCacheMapping(orderPO.getConversionProductId());
//        orderPO.setHourly(productCacheMapping.getHourly());
        if (orderPO.getHourly() != null && orderPO.getHourly() == 1) {
            String[] arr = request.getHourlyTime().split("\\--");
            orderPO.setStartTime(DateUtilX.stringToDate(arr[0], hour_format));
            orderPO.setEndTime(DateUtilX.stringToDate(arr[1], hour_format));
        }
        orderPO.setPayMethod(request.getPayMethod());
//        //到店付设置支付状态为空
//        if (request.getPayMethod() != null && request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
//            orderPO.setPayStatus(null);
//        }
        // 默认设置为未支付
        if (orderPO.getPayStatus() == null) {
            orderPO.setPayStatus(PayStatusEnum.NONE.getNo());
        }
        orderPO.setGuaranteeFlag(request.getGuaranteeFlag());
        orderPO.setIsManualOrder(0);
        if (request.getIsSubstituted() != null) {
            orderPO.setIsSubstituted(request.getIsSubstituted());
        } else {
            orderPO.setIsSubstituted(0);
        }
        orderPO.setProductLabel(0);
        if (StrUtilX.isNotEmpty(request.getArrivalTime())) {
            String[] split = request.getArrivalTime().split("~");
            orderPO.setEarliestArriveTime(split[0]);///
            orderPO.setLatestArriveTime(split[1]);
        }
        orderPO.setInstantConfirmationStatus(0);//是否即时确认，默认为0
        orderExtendPO.setIsShowOnSupplyOrder(0);
        if (StrUtilX.isNotEmpty(orderExtendPO.getSpecialRequest())) {
            orderExtendPO.setIsShowOnSupplyOrder(1);
        }
        orderPO.setOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
        orderPO.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key.toString());
        // 如果是现付，售价取供应商原始价格 下面会修改
        orderPO.setSalePrice(request.getOrderAmt());
        orderPO.setRefundFee(BigDecimal.ZERO);
        orderPO.setMarkedStatus(0);
        orderPO.setModificationStatus(0);
        if (StrUtilX.isNotEmpty(request.getOperator())) {
            orderPO.setCreatedBy(request.getOperator());
        } else {
            orderPO.setCreatedBy(accountConfig.getAgentName());
        }

        orderPO.setCreatedDt(currentDate);

        // 设置结算模式
        orderPO.setSettledType(accountConfig.getSettledType());

        // 设置酒店相关信息
        if (hotelInfo != null) {
            orderPO.setHotelName(hotelInfo.getHotelName());
            if (Objects.nonNull(hotelInfo.getLocation())) {
                orderPO.setCityCode(hotelInfo.getLocation().getCity());
                orderPO.setCityName(hotelInfo.getLocation().getCityName());
                orderPO.setCountryCode(hotelInfo.getLocation().getCountry());
                orderPO.setCnType(getOrderCnType(orderPO.getCountryCode()));
                orderPO.setProvinceCode(hotelInfo.getLocation().getProvince());
                orderPO.setCountryName(hotelInfo.getLocation().getCountryName());
                orderPO.setProvinceName(hotelInfo.getLocation().getProvinceName());
            }

            if (CollUtilX.isNotEmpty(hotelInfo.getRooms())) {
                for (RoomtypeDTO room : hotelInfo.getRooms()) {
                    if (room.getRoomtypeId().intValue() == request.getRoomId()) {
                        orderPO.setRoomName(room.getRoomtypeName());
                        if (StrUtilX.isNotEmpty(room.getRoomtypeEngName())) {
                            orderPO.setRoomName(orderPO.getRoomName() + "(" + room.getRoomtypeEngName() + ")");
                        }
                        orderPO.setWindowDetail(room.getWindowDetail());
                        break;
                    }
                }
            }
        }
        if (StringUtils.isEmpty(orderPO.getRoomName())) {
            orderPO.setRoomName(productSalePriceDTO.getRoomName());
        }

        // 设置支付方式
        orderPO.setSettlementType(accountConfig.getSettlementType());

        //若是订单里面有客人的手机号的话，就传客人的手机号
        if (StrUtilX.isEmpty(orderPO.getContactPhone())) {
            if (accountConfig.getSaleManagerId() != null) {
                orderPO.setMerchantBm(accountConfig.getSaleManagerId().toString());
            }
            orderPO.setContactName(accountConfig.getLinkman());
            orderPO.setContactPhone(accountConfig.getLinkmanTel());
        }

        // 设置扣减额度账户
        if (StrUtilX.isNotEmpty(accountConfig.getLineAccount())) {
            orderPO.setSubtractLineAccount(accountConfig.getLineAccount());
        } else {
            orderPO.setSubtractLineAccount(accountConfig.getAgentCode());
        }

        //查询条款
        StringBuilder cancellationTerm = new StringBuilder();
        if (request.getCancellationType() != null || StrUtilX.isNotEmpty(request.getCancelAdvanceTime())) {
            if (request.getCancellationType() != null && request.getCancellationType() == 0) {
                cancellationTerm.append("一经预订不可取消");
            } else {
                cancellationTerm.append("在入住前").append(request.getCancelAdvanceTime()).append("后取消订单,取消扣全款。");
            }
            orderExtendPO.setCancellationTerm(cancellationTerm.toString());
        } else {
            StringBuilder otherTerm = new StringBuilder();
            Map<String, String> product = new HashMap<>();
            product.put("productId", request.getProductId());
            Response<ProductDTO> product1 = productRemote.queryProduct(product);
            if (product1.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != product1.getModel()) {
                ProductDTO productDTOS = product1.getModel();
                if (productDTOS.getSupplyType() != null && productDTOS.getSupplyType() == 2) {
                    productDTOS = assemblyRestrict(productDTOS.getProductId(), request.getStartDate(), request.getEndDate());
                }

                if (null != productDTOS.getCancellationType()) {
                    int cancellationType = productDTOS.getCancellationType();
                    if (cancellationType == 0) {
                        cancellationTerm.append("一经预订不可取消");
                    } else {
                        cancellationTerm.append("在入住前").append(productDTOS.getCancellationAdvanceDays()).append("天").append(productDTOS.getCancellationDueTime());
                        cancellationTerm.append("后取消订单,取消扣全款。");
                        cancellationTerm.append("扣费说明:");
                        cancellationTerm.append(productDTOS.getCancellationDeductionTerm());
                    }
                    otherTerm.append("需提前").append(productDTOS.getReservationAdvanceDays() == null ? 0 : productDTOS.getReservationAdvanceDays()).append("天").append(productDTOS.getReservationDueTime()).append(",");
                    otherTerm.append("需预订大于等于").append(productDTOS.getReservationLimitRooms() == null ? 0 : productDTOS.getReservationLimitRooms()).append("间").append(",");
                    otherTerm.append("需预订").append(ComparisonTypeEnum.getDesc(productDTOS.getComparisonType())).append(productDTOS.getReservationLimitNights() == null ? 0 : productDTOS.getReservationLimitNights()).append("天");
                    orderExtendPO.setCancellationTerm(cancellationTerm.toString());
                    orderExtendPO.setOtherTerm(otherTerm.toString());
                }
            }
        }

        int orgCurrency = Integer.parseInt(orgDTO.getOrgCurrency());
        if (CollUtilX.isNotEmpty(productSalePriceDTO.getCancelRestrictions())) {
            for (CancelRestriction cancelRestriction : productSalePriceDTO.getCancelRestrictions()) {
                if (Objects.equals(2, cancelRestriction.getCancelRestrictionType()) || Objects.equals(3, cancelRestriction.getCancelRestrictionType())) {
                    if (null != cancelRestriction.getCancelRestrictionDay()) {
                        cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(request.getStartDate()), -cancelRestriction.getCancelRestrictionDay(), 0)));
                    }
                }
                // 时间存在 ：处理
                if (StrUtil.isNotBlank(cancelRestriction.getCancelRestrictionTime()) && cancelRestriction.getCancelRestrictionTime().contains(":")) {
                    cancelRestriction.setCancelRestrictionTime(cancelRestriction.getCancelRestrictionTime().replace(":", ""));
                }
                if (CollUtilX.isNotEmpty(cancelRestriction.getCancelPenalties())) {
                    List<CancelPenalDTO> dtoList = cancelRestriction.getCancelPenalties().stream().map(item -> {
                        CancelPenalDTO cancelPenalDTO = new CancelPenalDTO();
                        cancelPenalDTO.setPenaltiesType(item.getPenaltiesType());
//                        cancelPenalDTO.setPenaltiesValue(item.getPenaltiesValue());
                        cancelPenalDTO.setStartDate(item.getStartDate());
                        cancelPenalDTO.setEndData(item.getEndData());
//                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getCurrency())));

                        if (item.getPenaltiesType().equals(2) && item.getCurrency() != null && item.getCurrency().compareTo(accountConfig.getSettlementCurrency()) != 0) {
                            // 转换商家币种
                            BigDecimal orgRate = BigDecimal.ONE;
                            if (new BigDecimal(item.getCurrency()).compareTo(new BigDecimal(orgCurrency)) != 0) {
                                orgRate = RedisUtil.getRateToOrgCurrency(item.getCurrency(), CompanyDTO.COMPANY_CODE);
                                if (orgRate == null) {
                                    throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                                }
                            }
                            // 转换汇率
                            BigDecimal cancelPenaltiesAmt = CommonTgUtils.setRate(new BigDecimal(item.getPenaltiesValue()), orgRate);
                            // 转换客户币种
                            BigDecimal agentRate = BigDecimal.ONE;
                            if (new BigDecimal(orgCurrency).compareTo(new BigDecimal(accountConfig.getSettlementCurrency())) != 0) {
                                agentRate = RedisUtil.getRateToTargetCurrency(orgCurrency, CompanyDTO.COMPANY_CODE, accountConfig.getSettlementCurrency());
                                if (agentRate == null) {
                                    throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                                }
                            }
                            // 转换汇率
                            cancelPenaltiesAmt = CommonTgUtils.setRate(cancelPenaltiesAmt, agentRate);
                            // 根据客户配置进位
                            BigDecimal cancelPenaltiesValue = CommonTgUtils.setScale(cancelPenaltiesAmt, accountConfig.getDecimalPlaces(), accountConfig.getRoundingType());
                            cancelPenalDTO.setPenaltiesValue(String.valueOf(cancelPenaltiesValue));
                            cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(accountConfig.getSettlementCurrency())));
                        } else {
                            cancelPenalDTO.setPenaltiesValue(String.valueOf(item.getPenaltiesValue()));
                            cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getCurrency())));
                        }
                        return cancelPenalDTO;
                    }).collect(Collectors.toList());
                    cancelRestriction.setCancelRestrictionRemark(JSONUtil.toJsonStr(dtoList));
                }
            }
            orderExtendPO.setCancellationTerm(JSONUtil.toJsonStr(productSalePriceDTO.getCancelRestrictions()));
        }

        if (productSalePriceDTO.getGuaranteeType() != null && productSalePriceDTO.getGuaranteeType() != 0) {
            OrderRestrictEntity entity = new OrderRestrictEntity();
            entity.setOrderCode(orderPO.getOrderCode());
            entity.setGuaranteeType(productSalePriceDTO.getGuaranteeType());
            entity.setGuaranteeFeeType(productSalePriceDTO.getGuaranteeFeeType());
            entity.setGuaranteeContent(productSalePriceDTO.getGuaranteeDesc());
            entity.setGuaranteeCondition(productSalePriceDTO.getGuaranteeCondition());
            entity.setCreatedBy(orderPO.getCreatedBy());
            entity.setCreatedDt(DateUtilX.stringToDate(currentDate, hour_format));
            entity.setDeleted(0);
            assemblyOrderDTO.setOrderRestrictEntity(entity);
        }

        // 服务费
        if(request.getServiceCharge() != null && request.getServiceCharge().compareTo(BigDecimal.ZERO) != 0){
            String groupNumber = generateAdditionalChargesGroupNumber.generateAdditionalChargesGroupNumber();

            //服务费（附加费）
            OrderAdditionalChargesPO orderAdditionalChargesPO = OrderAdditionalChargesPO.builder()
                    .groupNumber(groupNumber)
                    .additionalChargesType(AdditionalChargesTypeEnum.SERVICE_CHARGE.key)
                    .additionalChargesName(AdditionalChargesTypeEnum.SERVICE_CHARGE.value)
                    .orderId(orderPO.getId())
                    .orderCode(orderPO.getOrderCode())
                    .additionalChargesDate(orderPO.getStartDate())
                    .quantity(1)
                    .additionalCharges(request.getServiceCharge())
                    .createdBy(orderPO.getCreatedBy())
                    .createdDt(new Date())
                    .build();
            assemblyOrderDTO.setOrderAdditionalChargesPO(orderAdditionalChargesPO);
        }

        OrderFinancePO orderFinancePO = new OrderFinancePO();
        orderFinancePO.setReceivedAmt(BigDecimal.ZERO);
        if (request.getPayMethod() != null && request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            orderFinancePO.setUnreceivedAmt(BigDecimal.ZERO);
        } else {
            orderFinancePO.setUnreceivedAmt(orderPO.getOrderAmt());
        }
        orderFinancePO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
        orderFinancePO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
        orderFinancePO.setSettlementStatus(0);
        orderFinancePO.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
        orderFinancePO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
        orderFinancePO.setFinanceLockStatus(0);
        orderFinancePO.setCreatedBy(orderPO.getCreatedBy());
        orderFinancePO.setCreatedDt(currentDate);
        assemblyOrderDTO.setOrderFinance(orderFinancePO);

        //设置入住人
        StringBuilder guestSb = new StringBuilder();
        int adultQty = 0;
        if (CollUtilX.isNotEmpty(request.getRoomGuestList())) {
            assemblyOrderDTO.setGuestList(new ArrayList<>());
            for (OrderRoomGuestDTO guest : request.getRoomGuestList()) {
                if (guest.getAdultQty() != null) {
                    adultQty = adultQty + guest.getAdultQty();
                }
                int i = 0;
                for (OrderGuestDTO orderGuestDTO : guest.getOrderGuestList()) {
                    if (StrUtilX.isNotEmpty(orderGuestDTO.getName())) {
                        guestSb.append(orderGuestDTO.getName()).append(",");
                    } else {
                        guestSb.append(orderGuestDTO.getLastName()).append("/").append(orderGuestDTO.getFirstName()).append(",");
                    }
                    GuestPO guestPO = new GuestPO();
                    guestPO.setRoomNumber(guest.getRoomNumber());
                    if (i == 0) {//儿童数量和成人数量放在第一个入住人实体里面
                        guestPO.setAdultQty(guest.getAdultQty());
                        guestPO.setChildrenQty(guest.getChildrenQty());
                        guestPO.setChildrenAge(guest.getChildrenAge());
                        i++;
                    }
                    guestPO.setOrderId(orderGuestDTO.getOrderId());
                    guestPO.setFirstName(orderGuestDTO.getFirstName());
                    guestPO.setLastName(orderGuestDTO.getLastName());
                    guestPO.setName(orderGuestDTO.getName());

                    if (StrUtilX.isEmpty(orderGuestDTO.getName())) {
                        guestPO.setName(orderGuestDTO.getLastName() + "/" + orderGuestDTO.getFirstName());
                    }
                    guestPO.setNationality(orderGuestDTO.getNationality());
                    guestPO.setMobileNo(orderGuestDTO.getMobileNo());
                    guestPO.setCountryCode(orderGuestDTO.getCountryCode());
                    guestPO.setIdCardType(orderGuestDTO.getIdCardType());
                    guestPO.setIdCardNo(orderGuestDTO.getIdCardNo());
                    guestPO.setMembershipCardNumber(orderGuestDTO.getMembershipCardNumber());
//                guestPO.setRoomNumber(guest.getRoomNumber());
                    guestPO.setEarnPoints(orderGuestDTO.getEarnPoints());
                    assemblyOrderDTO.getGuestList().add(guestPO);
                }
            }
            guestSb.deleteCharAt(guestSb.length() - 1);
        }
        orderPO.setGuest(guestSb.toString().replaceAll("/", ""));
        orderPO.setAdultQty(adultQty);

        // 查询供应商缓存
        AddSupplierReq supplier = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, request.getSupplierCode()).toString(), AddSupplierReq.class);
        if (supplier == null) {
            log.error("供应商缓存不存在，supplyCode={}", request.getSupplierCode());
            throw new SysException(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorCode, ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorDesc);
        }

        if (orderPO.getInvoiceType() == null) {
            orderPO.setInvoiceType(supplier.getInvoiceType());
        }

        if (orderPO.getInvoiceModel() == null) {
            orderPO.setInvoiceModel(supplier.getInvoiceModel());
        }

        //组装供货单
        //计算供应商价格
        BigDecimal supplyOrderAmt = BigDecimal.ZERO;
        BigDecimal realSalePrice = BigDecimal.ZERO;

        //组装价格
        assemblySupplyProductDTO.setSupplyProductPriceList(new ArrayList<>());
        assemblyOrderDTO.setOrderProductPriceList(new ArrayList<>());
        if (CollUtilX.isNotEmpty(productSalePriceDTO.getRoomItemDetails())) {
            // 计算一次汇率
            supplierToOrgRate = RedisUtil.getRateToOrgCurrency(productSalePriceDTO.getRoomItemDetails().get(0).getPriceInfoDetails().get(0).getBaseCurrency(), CompanyDTO.COMPANY_CODE);
            agentToOrgRate = RedisUtil.getRateToOrgCurrency(productSalePriceDTO.getRoomItemDetails().get(0).getPriceInfoDetails().get(0).getSettlementCurrency(), CompanyDTO.COMPANY_CODE);
            orgToAgentRate = RedisUtil.getRateToTargetCurrency(orgCurrency, CompanyDTO.COMPANY_CODE, productSalePriceDTO.getRoomItemDetails().get(0).getPriceInfoDetails().get(0).getSettlementCurrency());

            if (agentToOrgRate == null || supplierToOrgRate == null || orgToAgentRate == null) {
                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorCode, ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorDesc);
            }

            orderPO.setBreakfastQty(productSalePriceDTO.getRoomItemDetails().get(0).getPriceInfoDetails().get(0).getBreakfastNum());
            assemblyOrderPrice(assemblyOrderDTO, assemblySupplyProductDTO, productSalePriceDTO.getRoomItemDetails(), request.getRoomGuestList(), orderPO);
        }

        for (com.tiangong.dto.order.response.OrderRoomDetailDto productSalePriceItemDTO : productSalePriceDTO.getRoomItemDetails()) {
            for (com.tiangong.dto.product.PriceInfoDetail priceInfoDetail : productSalePriceItemDTO.getPriceInfoDetails()) {
                if (Objects.nonNull(priceInfoDetail.getTaxDetail())) {
                    supplyOrderAmt = supplyOrderAmt.add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyRoomPrice()))
                            .add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyTaxFee()))
                            .add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplySalesTax()))
                            .add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyOtherTax()));
                } else {
                    supplyOrderAmt = supplyOrderAmt.add(priceInfoDetail.getBasePrice());
                }

                //按次税费
                if (priceInfoDetail.getDate().equals(request.getStartDate()) && Objects.nonNull(productSalePriceItemDTO.getTaxDetail())) {
                    supplyOrderAmt = supplyOrderAmt.add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getSupplyTaxFee())).
                            add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getSupplyOtherTax())).
                            add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getSupplySalesTax()));

//                    realSalePrice = realSalePrice.add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getTaxFee())).
//                            add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getOtherTax())).
//                            add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getSalesTax()));
                }

                log.info("salePrice:" + priceInfoDetail.getSalePrice() + ", roomQty:" + JSON.toJSONString(request.getRoomQty()));
                BigDecimal salePrice = Objects.isNull(priceInfoDetail.getSalePrice()) ? priceInfoDetail.getBasePrice() : priceInfoDetail.getSalePrice();
                realSalePrice = CommonTgUtils.setScale(realSalePrice.add(salePrice), accountConfig.getDecimalPlaces(), accountConfig.getRoundingType());
            }
        }

        orderPO.setOrgToAgentRate(orgToAgentRate);// 商家币种转客户币种汇率
        orderPO.setProductName(productSalePriceDTO.getProductName());
        // 价格计划名称存在中文直接转换成指定英文
        if (LanguageTypeEnum.en_US.getValue().equals(request.getLanguage()) && StrUtilX.isNotEmpty(orderPO.getProductName())) {
            if (StrUtilX.notIsEnglishAndSpace(orderPO.getProductName())) {
                orderPO.setProductName("Business travel rate");
            }
        }
        orderPO.setSaleRate(agentToOrgRate);
        BigDecimal supplyOrderAmtTemp = BigDecimal.ZERO;
        // 判断币种是否一致
        if (orderPO.getSaleCurrency().equals(productSalePriceDTO.getCurrency())) {
            supplyOrderAmtTemp = supplyOrderAmt.subtract(commission);
        } else {
            supplyOrderAmtTemp = supplyOrderAmt.subtract(commission).multiply(supplierToOrgRate).multiply(orgToAgentRate);
        }

        orderPO.setProfit(orderPO.getSalePrice().subtract(CommonTgUtils.setScale(supplyOrderAmtTemp, accountConfig.getDecimalPlaces(), accountConfig.getRoundingType())));
        // 现付需要存底价和底价币种
        if (Objects.equals(orderPO.getPayMethod(), PayMethodEnum.PAY.key)) {
            orderPO.setBasePrice(supplyOrderAmt);
            orderPO.setBaseCurrency(productSalePriceDTO.getCurrency());
        }
        assemblyOrderDTO.setOrder(orderPO);
        orderExtendPO.setCreatedDt(currentDate);
        orderExtendPO.setCreatedBy(orderPO.getCreatedBy());
        assemblyOrderDTO.setOrderExtendPO(orderExtendPO);

        assemblyOrderDTO.setSupplyOrderList(Collections.singletonList(assemblySupplyOrderDTO));
        SupplyOrderPO supplyOrderPO = new SupplyOrderPO();
        BeanUtils.copyProperties(request, supplyOrderPO);
        //fixme mapper类型转换BUG
//        SupplyOrderPO supplyOrderPO = ProductSalePriceConvert.INSTANCE.supplyOrderPOConvert(request);
        supplyOrderPO.setSupplierCode(productSalePriceDTO.getSupplierCode());
        supplyOrderPO.setSupplierName(productSalePriceDTO.getSupplierName());
        supplyOrderPO.setBaseCurrency(productSalePriceDTO.getCurrency());
        // 现付类型、供货单金额= -佣金
        if (request.getPayMethod() != null && request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            if (commission.compareTo(BigDecimal.ZERO) != 0) {
                supplyOrderPO.setSupplyOrderAmt(commission.negate());// -佣金
            } else {
                supplyOrderPO.setSupplyOrderAmt(BigDecimal.ZERO);
            }
        } else {
            supplyOrderPO.setSupplyOrderAmt(supplyOrderAmt.subtract(commission));// 供货单金额等于底价-佣金
        }
        supplyOrderPO.setHotelName(orderPO.getHotelName());
        supplyOrderPO.setRoomName(orderPO.getRoomName());
        supplyOrderPO.setProductName(productSalePriceDTO.getProductName());
        supplyOrderPO.setBasePrice(supplyOrderAmt);
        supplyOrderPO.setRefundFee(BigDecimal.ZERO);
        supplyOrderPO.setStartDate(orderPO.getStartDate());
        supplyOrderPO.setEndDate(orderPO.getEndDate());
        supplyOrderPO.setStartTime(orderPO.getStartTime());
        supplyOrderPO.setEndTime(orderPO.getEndTime());
        supplyOrderPO.setConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
        supplyOrderPO.setSendingStatus(SendingStatusEnum.UNSEND.key);
        supplyOrderPO.setCreatedBy(orderPO.getCreatedBy());
        supplyOrderPO.setCreatedDt(currentDate);
        supplyOrderPO.setCommission(commission);
        supplyOrderPO.setBreakfastQty(orderPO.getBreakfastQty());
        supplyOrderPO.setRate(supplierToOrgRate);
        // 判断币种是否一致
        if (orderPO.getSaleCurrency().equals(supplyOrderPO.getBaseCurrency())) {
            supplyOrderPO.setSupplierToAgentRate(BigDecimal.ONE);
        } else {
            if (supplierToOrgRate != null) {
                supplyOrderPO.setSupplierToAgentRate(supplierToOrgRate.multiply(orgToAgentRate));
            }
        }
        supplyOrderPO.setOrgToAgentRate(orgToAgentRate);
        supplyOrderPO.setRewardAmt(BigDecimal.ZERO);
        supplyOrderPO.setRebateAmt(BigDecimal.ZERO);
        if (Objects.equals(2, productSalePriceDTO.getSupplyType())) {
            supplyOrderPO.setIsSpProduct(1);
        }
        supplyOrderPO.setSupplierName(supplier.getSupplierName());
        supplyOrderPO.setSettlementType(supplier.getSettlementType());
        if (CollUtilX.isNotEmpty(request.getBedInfos())) {
            BedTypesDetailDto bedTypesDetailDto = new BedTypesDetailDto();
            List<BedInfoDto> bedInfos = ProductSalePriceConvert.INSTANCE.bedInfoDtoConvert(request.getBedInfos());
            bedTypesDetailDto.setBedInfos(bedInfos);
            supplyOrderPO.setBedType(JSONUtil.toJsonStr(bedTypesDetailDto));
        }

        // 设置房间号信息
        Set<Long> collect = request.getRoomGuestList().stream().filter(item -> item.getRoomNumber() != null).map(item -> Long.valueOf(item.getRoomNumber())).collect(Collectors.toSet());
        supplyOrderPO.setRoomNumbers(StrUtilX.listToString(collect));

        assemblySupplyOrderDTO.setSupplyOrder(supplyOrderPO);

        SupplyOrderFinancePO supplyOrderFinancePO = new SupplyOrderFinancePO();
        supplyOrderFinancePO.setPaidAmt(BigDecimal.ZERO);
        if (request.getPayMethod() != null && request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            supplyOrderFinancePO.setUnpaidAmt(BigDecimal.ZERO);
        } else {
            supplyOrderFinancePO.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt());
        }
        supplyOrderFinancePO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
        supplyOrderFinancePO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
        supplyOrderFinancePO.setSettlementStatus(0);
        supplyOrderFinancePO.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
        supplyOrderFinancePO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
        supplyOrderFinancePO.setFinanceLockStatus(0);
        supplyOrderFinancePO.setCreatedBy(orderPO.getCreatedBy());
        supplyOrderFinancePO.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        supplyOrderFinancePO.setCreatedDt(currentDate);
        assemblySupplyOrderDTO.setSupplyOrderFinance(supplyOrderFinancePO);
        //供货单奖励
        SupplyOrderFinancePO supplyRewardFinancePO = ProductSalePriceConvert.INSTANCE.supplyRewardFinancePOConvert(supplyOrderFinancePO);
        supplyRewardFinancePO.setUnpaidAmt(BigDecimal.ZERO);
        supplyRewardFinancePO.setFinanceType(StatementTypeEnum.REWARD_AMT.key);
        assemblySupplyOrderDTO.setSupplyRewardFinance(supplyRewardFinancePO);
        //供货单奖励
        SupplyOrderFinancePO supplyRebateFinancePO = ProductSalePriceConvert.INSTANCE.supplyRebateFinancePOConvert(supplyRewardFinancePO);
        supplyRebateFinancePO.setFinanceType(StatementTypeEnum.REBATE_AMT.key);
        assemblySupplyOrderDTO.setSupplyRebateFinance(supplyRebateFinancePO);

        //组装供货产品
        assemblySupplyOrderDTO.setSupplyProductList(Collections.singletonList(assemblySupplyProductDTO));
        SupplyProductPO supplyProductPO = new SupplyProductPO();
        BeanUtils.copyProperties(request, supplyProductPO);
        //fixme mapper类型转换BUG
//        SupplyProductPO supplyProductPO = ProductSalePriceConvert.INSTANCE.SupplyProductPOConvert(request);
        //供应商产品Id，此处酒店id和房型id写死 // FIXME: 2020/8/3
        supplyProductPO.setRoomName(orderPO.getRoomName());
        //自签产品不写spProductId
        if (request.getProductId().contains("-") || request.getProductId().contains("_")) {
            supplyProductPO.setProductId(productSalePriceDTO.getProductId());
            supplyProductPO.setSpProductId(request.getProductId());
        } else {
            supplyProductPO.setProductId(productSalePriceDTO.getProductId());
        }

        supplyProductPO.setStartDate(orderPO.getStartDate());
        supplyProductPO.setEndDate(orderPO.getEndDate());
        supplyProductPO.setGuest(guestSb.toString());
        supplyProductPO.setBasePriceTotalAmt(supplyOrderAmt);
        supplyProductPO.setCommission(commission);
        supplyProductPO.setBreakfastQty(orderPO.getBreakfastQty());
        supplyProductPO.setCancellationTerm(orderExtendPO.getCancellationTerm());
        supplyProductPO.setCreatedBy(orderPO.getCreatedBy());
        supplyProductPO.setCreatedDt(currentDate);
        assemblySupplyProductDTO.setSupplyProduct(supplyProductPO);

//        if (CollUtilX.isNotEmpty(request.getPriceList())) {
//            for (PriceRequestDTO priceDTO : request.getPriceList()) {
//                OrderProductPricePO orderProductPricePO = new OrderProductPricePO();
//                orderProductPricePO.setSaleDate(DateUtil.stringToDate(priceDTO.getSaleDate()));
//                orderProductPricePO.setSalePrice(priceDTO.getSalePrice());
//                assemblyOrderDTO.getOrderProductPriceList().add(orderProductPricePO);
//
//                SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
//                supplyProductPricePO.setSaleDate(DateUtil.stringToDate(priceDTO.getSaleDate()));
//                supplyProductPricePO.setBasePrice(basePriceMap.get(priceDTO.getSaleDate()));
//                assemblySupplyProductDTO.getSupplyProductPriceList().add(supplyProductPricePO);
//            }
//        }

        //比较下单价格是否>=真实价格
        if (realSalePrice.compareTo(orderAmt.add(request.getDiscountAmount())) > 0) {
            log.info("realSalePrice:" + realSalePrice + ", orderAmt:" + orderPO.getOrderAmt());
            orderPO.setIsAbnormal(1);
        }

        // 提示信息
        if (CollUtilX.isNotEmpty(productSalePriceDTO.getTips())) {
            List<OrderProductTipPO> tipPOS = new ArrayList<>();
            for (TipInfosDTO dto : productSalePriceDTO.getTips()) {
                if (CollUtilX.isEmpty(dto.getTipsDetails())) {
                    continue;
                }
                List<OrderProductTipPO> pos = dto.getTipsDetails().stream().map(item -> {
                    OrderProductTipPO tipPO = new OrderProductTipPO();
                    tipPO.setOrderCode(orderPO.getOrderCode());
                    tipPO.setProductId(orderPO.getProductId());
                    tipPO.setTipType(dto.getTipsType());
                    tipPO.setTitle(item.getTitle());
                    tipPO.setContent(item.getDetails());
                    tipPO.setCreatedBy(orderPO.getCreatedBy());
                    tipPO.setCreatedDt(DateUtilX.stringToDate(currentDate, DateUtilX.hour_format));
                    return tipPO;
                }).collect(Collectors.toList());
                tipPOS.addAll(pos);
            }
            if (CollUtilX.isNotEmpty(tipPOS)) {
                assemblyOrderDTO.setOrderProductTips(tipPOS);
            }
        }

        return assemblyOrderDTO;
    }

    /**
     * 组装价格
     */
    private void assemblyOrderPrice(AssemblyOrderDTO assemblyOrderDTO, AssemblySupplyProductDTO assemblySupplyProductDTO,
                                    List<com.tiangong.dto.order.response.OrderRoomDetailDto> priceDTOList,
                                    List<OrderRoomGuestDTO> roomGuestList, OrderPO orderPO) {
        Map<Integer, OrderRoomGuestDTO> guestMap = new HashMap<>();
        int i = 1;
        for (OrderRoomGuestDTO orderRoomGuestDTO : roomGuestList) {
            guestMap.put((null == orderRoomGuestDTO.getRoomNumber() ? i++ : orderRoomGuestDTO.getRoomNumber()), orderRoomGuestDTO);
        }

        for (com.tiangong.dto.order.response.OrderRoomDetailDto priceDTO : priceDTOList) {
            OrderProductPricePO orderProductPricePOTimes = new OrderProductPricePO();//按次费用

            //按次税费+到店支付费用
            if (Objects.nonNull(priceDTO.getTaxDetail()) || null != priceDTO.getPayInStorePrice()) {
                orderProductPricePOTimes.setRoomNumber(priceDTO.getRoomIndex());
                orderProductPricePOTimes.setCurrency(priceDTO.getPriceInfoDetails().get(0).getSettlementCurrency());
                orderProductPricePOTimes.setPayAtHotelCurrency(priceDTO.getPayInStoreCurrency());
                orderProductPricePOTimes.setPayAtHotelFee(priceDTO.getPayInStorePrice());
                orderPO.setPayAtHotelFee(CommonTgUtils.formatBigDecimal(orderPO.getPayAtHotelFee()).add(CommonTgUtils.formatBigDecimal(priceDTO.getPayInStorePrice())));
                orderPO.setPayAtHotelAgentCurrencyFee(CommonTgUtils.formatBigDecimal(orderPO.getPayAtHotelAgentCurrencyFee()).add(CommonTgUtils.formatBigDecimal(priceDTO.getPayInStorePriceAgentCurrencyAmt())));
                orderPO.setPayAtHotelCurrency(priceDTO.getPayInStoreCurrency());

                SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
                supplyProductPricePO.setRoomNumber(priceDTO.getRoomIndex());
                supplyProductPricePO.setCurrency(priceDTO.getPriceInfoDetails().get(0).getBaseCurrency());

                if (Objects.nonNull(priceDTO.getTaxDetail())) {
                    orderProductPricePOTimes.setExtraTaxFee(priceDTO.getTaxDetail().getOtherTax());
                    orderProductPricePOTimes.setSalesTax(priceDTO.getTaxDetail().getSalesTax());
                    orderProductPricePOTimes.setTax(priceDTO.getTaxDetail().getTaxFee());
                    orderProductPricePOTimes.setRoomPrice(priceDTO.getTaxDetail().getRoomPrice());

                    supplyProductPricePO.setExtraTaxFee(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplyOtherTax()));
                    supplyProductPricePO.setSalesTax(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplySalesTax()));
                    supplyProductPricePO.setTax(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplyTaxFee()));
                    supplyProductPricePO.setRoomPrice(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplyRoomPrice()));
                }

                supplyProductPricePO.setPayAtHotelCurrency(priceDTO.getPayInStoreCurrency());
                supplyProductPricePO.setPayAtHotelFee(priceDTO.getPayInStorePrice());
                assemblySupplyProductDTO.getSupplyProductPriceList().add(supplyProductPricePO);
            }

            //每日费用
            for (com.tiangong.dto.product.PriceInfoDetail priceInfoDetail : priceDTO.getPriceInfoDetails()) {
                OrderProductPricePO orderProductPricePO = new OrderProductPricePO();
                orderProductPricePO.setSaleDate(DateUtilX.stringToDate(priceInfoDetail.getDate()));
                orderProductPricePO.setSalePrice(priceInfoDetail.getSalePrice());
                orderProductPricePO.setCurrency(priceInfoDetail.getSettlementCurrency());
                orderProductPricePO.setPayAtHotelFee(priceDTO.getPayInStorePrice());
                orderProductPricePO.setPayAtHotelCurrency(priceDTO.getPayInStoreCurrency());
                orderProductPricePO.setPayAtHotelAgentCurrencyFee(priceDTO.getPayInStorePriceAgentCurrencyAmt());
                orderProductPricePO.setRoomNumber(priceDTO.getRoomIndex());
                orderProductPricePO.setBreakfastType(priceInfoDetail.getBreakfastType());
                orderProductPricePO.setBreakfastQty(priceInfoDetail.getBreakfastNum());
                String roomPersons = null;
                OrderRoomGuestDTO orderRoomGuestDTO = guestMap.get(priceDTO.getRoomIndex());
                if (null != orderRoomGuestDTO && null != orderRoomGuestDTO.getAdultQty()) {
                    roomPersons = orderRoomGuestDTO.getAdultQty().toString();
                    if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                        roomPersons += ("," + orderRoomGuestDTO.getChildrenAge());
                    }
                }
                orderProductPricePO.setRoomPersons(roomPersons);

                SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
                supplyProductPricePO.setSaleDate(DateUtilX.stringToDate(priceInfoDetail.getDate()));
                supplyProductPricePO.setBasePrice(priceInfoDetail.getBasePrice());
                supplyProductPricePO.setRoomNumber(priceDTO.getRoomIndex());
                supplyProductPricePO.setCurrency(priceInfoDetail.getBaseCurrency());
                supplyProductPricePO.setRoomPersons(roomPersons);
                supplyProductPricePO.setBreakfastType(priceInfoDetail.getBreakfastType());
                supplyProductPricePO.setBreakfastQty(priceInfoDetail.getBreakfastNum());

                if (Objects.nonNull(priceInfoDetail.getTaxDetail())) {
                    orderProductPricePO.setExtraTaxFee(priceInfoDetail.getTaxDetail().getOtherTax());
                    orderProductPricePO.setSalesTax(priceInfoDetail.getTaxDetail().getSalesTax());
                    orderProductPricePO.setTax(priceInfoDetail.getTaxDetail().getTaxFee());
                    orderProductPricePO.setDiscount(priceInfoDetail.getTaxDetail().getDiscount());
                    orderProductPricePO.setRoomPrice(priceInfoDetail.getTaxDetail().getRoomPrice());

                    supplyProductPricePO.setExtraTaxFee(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyOtherTax()));
                    supplyProductPricePO.setSalesTax(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplySalesTax()));
                    supplyProductPricePO.setTax(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyTaxFee()));
                    supplyProductPricePO.setDiscount(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyDiscount()));
                    supplyProductPricePO.setRoomPrice(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyRoomPrice()));
                }

                assemblyOrderDTO.getOrderProductPriceList().add(orderProductPricePO);
                assemblySupplyProductDTO.getSupplyProductPriceList().add(supplyProductPricePO);
            }

            if (Objects.nonNull(orderProductPricePOTimes.getRoomNumber())) {
                // 按次加幅 按次税费加幅值 = 按次的费用 - 房费（空） - 销售税 - 税费 - 其他税费和服务费
                orderProductPricePOTimes.setRoomPrice(orderProductPricePOTimes.getRoomPrice());
                orderProductPricePOTimes.setSalesTax(orderProductPricePOTimes.getSalesTax());
                orderProductPricePOTimes.setTax(orderProductPricePOTimes.getTax());
                orderProductPricePOTimes.setExtraTaxFee(orderProductPricePOTimes.getExtraTaxFee());
                assemblyOrderDTO.getOrderProductPriceList().add(orderProductPricePOTimes);
            }
        }
    }

    /**
     * 组装取消条款
     */
    private ProductDTO assemblyRestrict(Integer productId, String startDate, String endDate) {
        Set<String> saleDateList = DateUtilX.getDateListToString(DateUtilX.stringToDate(startDate), DateUtilX.addDate(DateUtilX.stringToDate(endDate), -1));

        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(productId);
        if (CollUtilX.isNotEmpty(saleDateList)) {

            int i = 0; // 标记是否值
            Integer cancellationType = null;
            Integer cancellationAdvanceDays = null;
            String cancellationDueTime = "";
            String cancellationDeductionTerm = "";
            for (String saleDate : saleDateList) {
                ProductRestrictDTO productRestrictDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, StrUtilX.concat(String.valueOf(productId), "_", saleDate)),
                        ProductRestrictDTO.class);
                if (null != productRestrictDTO) {
                    if (i == 0) {
                        cancellationType = productRestrictDTO.getCancellationType();
                        cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays() == null ? 0 : productRestrictDTO.getCancellationAdvanceDays();
                        cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                        cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                        i = 1;
                    } else {
                        if (cancellationType > productRestrictDTO.getCancellationType()) {
                            cancellationType = productRestrictDTO.getCancellationType();
                            cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays() == null ? 0 : productRestrictDTO.getCancellationAdvanceDays();
                            cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                            cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                        } else if (cancellationType.equals(productRestrictDTO.getCancellationType())) {
                            // 当前条款
                            int nowDay = productRestrictDTO.getCancellationAdvanceDays() != null ? productRestrictDTO.getCancellationAdvanceDays() : 0;
                            int nowHours = StrUtilX.isNotEmpty(productRestrictDTO.getCancellationDueTime()) ? Integer.parseInt(productRestrictDTO.getCancellationDueTime().split(":")[0]) : 0;

                            // 筛选的条款
                            int beforeDay = cancellationAdvanceDays != null ? cancellationAdvanceDays : 0;
                            int beforeHours = StrUtilX.isNotEmpty(cancellationDueTime) ? Integer.parseInt(cancellationDueTime.split(":")[0]) : 0;
                            int beforeMinute = StrUtilX.isNotEmpty(cancellationDueTime) && cancellationDueTime.contains(":") ?
                                    Integer.parseInt(cancellationDueTime.split(":")[1]) : 0;

                            // 天数越大越严格
                            // 小时越小越严格
                            // 分钟越小越严格
                            if (beforeDay < nowDay || beforeHours > nowHours || beforeMinute > nowHours) {
                                cancellationType = productRestrictDTO.getCancellationType();
                                cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays();
                                cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                                cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                            }
                        }
                    }
                }
            }

            productDTO.setCancellationType(cancellationType);
            productDTO.setCancellationAdvanceDays(cancellationAdvanceDays);
            productDTO.setCancellationDeductionTerm(cancellationDeductionTerm);
            productDTO.setCancellationDueTime(cancellationDueTime);
        }
        return productDTO;
    }

    /**
     * 保存订单
     */
    private Integer saveOrder(AssemblyOrderDTO orderDTO, boolean equalDateFlag) {
        //获取订单编码
//        String orderCode = this.getOrderCode();
//        if (null == orderCode) {
//            return 0;
//        }
        String orderCode = DistributedCodeGenerator.generateOrderCode();
        orderDTO.getOrder().setOrderCode(orderCode);
        if (StrUtilX.isEmpty(orderDTO.getOrder().getChannelOrderCode())) {
            orderDTO.getOrder().setChannelOrderCode(orderCode);
        }
        if (orderDTO.getOrder().getHourly() == null) {
            orderDTO.getOrder().setHourly(0);
        }
        if (orderDTO.getOrder().getHourly() == 1) {//钟点房个没有这两个属性
            orderDTO.getOrder().setEarliestArriveTime(null);
            orderDTO.getOrder().setLatestArriveTime(null);
        }
        if (equalDateFlag) {
            orderDTO.getOrder().setEndDate(orderDTO.getOrder().getStartDate());
        }
        // 设置VIP订单标识（在订单保存前判断）
        setOrderVipStatus(orderDTO);

        orderMapper.insert(orderDTO.getOrder());
        Integer orderId = orderDTO.getOrder().getId();
        orderDTO.getOrderFinance().setOrderId(orderId);
        orderDTO.getOrderFinance().setOrderCode(orderDTO.getOrder().getOrderCode());
        orderDTO.getOrderExtendPO().setOrderId(orderId);
        orderDTO.getOrderExtendPO().setOrderCode(orderDTO.getOrder().getOrderCode());
        if (orderDTO.getOrderAdditionalChargesPO() != null){
            orderDTO.getOrderAdditionalChargesPO().setOrderId(orderId);
            orderDTO.getOrderAdditionalChargesPO().setOrderCode(orderDTO.getOrder().getOrderCode());
            orderAdditionalChargesMapper.insert(orderDTO.getOrderAdditionalChargesPO());
        }
        orderExtendMapper.insert(orderDTO.getOrderExtendPO());
        orderFinanceMapper.insert(orderDTO.getOrderFinance());
        if (CollUtilX.isNotEmpty(orderDTO.getOrderProductPriceList())) {
            orderDTO.getOrderProductPriceList().forEach(orderProductPricePO -> {
                orderProductPricePO.setOrderId(orderId);
            });
            orderProductPriceMapper.insertList(orderDTO.getOrderProductPriceList());
        }
        orderDTO.getGuestList().forEach(guestPO -> {
            guestPO.setOrderId(orderId);
        });

        // 设置客人 VIP 等级（在客人数据保存前设置）
        setGuestVipLevels(orderDTO);

        guestMapper.insertList(orderDTO.getGuestList());
        for (AssemblySupplyOrderDTO supplyOrderDTO : orderDTO.getSupplyOrderList()) {
            supplyOrderDTO.getSupplyOrder().setOrderId(orderId);
            //获取供货单编码
//            String supplyOrderCode = RedisTemplateX.lRightPop(SystemCodeEnum.SUPPLYORDERCODE.code);
//            if (null == supplyOrderCode) {
//                supplyOrderCode = getNewSyncSupplyCode();
//            }
            supplyOrderDTO.getSupplyOrder().setSupplyOrderCode(DistributedCodeGenerator.generateSupplyOrderCode());
            if (equalDateFlag) {
                supplyOrderDTO.getSupplyOrder().setEndDate(orderDTO.getOrder().getEndDate());
            }
            supplyOrderMapper.insert(supplyOrderDTO.getSupplyOrder());
            Integer supplyOrderId = supplyOrderDTO.getSupplyOrder().getId();
            SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(supplyOrderId);
            supplyOrderDTO.getSupplyOrderFinance().setSupplyOrderId(supplyOrderId);
            supplyOrderDTO.getSupplyOrderFinance().setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            supplyOrderFinanceMapper.insert(supplyOrderDTO.getSupplyOrderFinance());
            supplyOrderDTO.getSupplyRewardFinance().setSupplyOrderId(supplyOrderId);
            supplyOrderDTO.getSupplyRewardFinance().setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            supplyOrderFinanceMapper.insert(supplyOrderDTO.getSupplyRewardFinance());
            supplyOrderDTO.getSupplyRebateFinance().setSupplyOrderId(supplyOrderId);
            supplyOrderDTO.getSupplyRebateFinance().setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            supplyOrderFinanceMapper.insert(supplyOrderDTO.getSupplyRebateFinance());
            for (AssemblySupplyProductDTO supplyProductDTO : supplyOrderDTO.getSupplyProductList()) {
                supplyProductDTO.getSupplyProduct().setSupplyOrderId(supplyOrderId);
                supplyProductDTO.getSupplyProduct().setOrderId(orderId);
                supplyProductDTO.getSupplyProduct().setProductName(supplyOrderDTO.getSupplyOrder().getProductName());
                if (equalDateFlag) {
                    supplyProductDTO.getSupplyProduct().setEndDate(orderDTO.getOrder().getEndDate());
                }
                supplyProductMapper.insert(supplyProductDTO.getSupplyProduct());
                Integer supplyProductId = supplyProductDTO.getSupplyProduct().getId();
                supplyProductDTO.getSupplyProductPriceList().forEach(supplyProductPricePO -> {
                    supplyProductPricePO.setSupplyOrderId(supplyOrderId);
                    supplyProductPricePO.setSupplyProductId(supplyProductId);
                    supplyProductPricePO.setDiscount(BigDecimal.ZERO);
                });
                supplyProductPriceMapper.insertList(supplyProductDTO.getSupplyProductPriceList());
            }

            // 判断此供货单是不是非VCC自助出账的供货单
            NonVccAutoBillConfigDTO nonVccAutoBillConfigDTO = supplyOrderMapper.selectNonVccAutoBillConfigBySupplierCodeAndHotelId(supplyOrderPO.getSupplierCode(), supplyOrderPO.getHotelId());
            if (nonVccAutoBillConfigDTO != null) {
                // 非VCC自助出账的供货单，需要插入非VCC自助出账的供货单信息
                SupplyOrderNonVccAutoBillInfoPO supplyOrderNonVccAutoBillInfoPO = ProductSalePriceConvert.INSTANCE.supplyOrderNonVccAutoBillInfoPOConvert(nonVccAutoBillConfigDTO);
                supplyOrderNonVccAutoBillInfoPO.setSupplyOrderId(Long.valueOf(supplyOrderPO.getId()));
                supplyOrderNonVccAutoBillInfoPO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                supplyOrderNonVccAutoBillInfoPO.setCreatedBy(orderDTO.getOrder().getCreatedBy());
                supplyOrderNonVccAutoBillInfoPO.setCreatedDt(new Date());
                supplyOrderNonVccAutoBillInfoPO.setUpdatedBy(orderDTO.getOrder().getCreatedBy());
                supplyOrderNonVccAutoBillInfoPO.setUpdatedDt(new Date());
                supplyOrderNonVccAutoBillInfoMapper.insert(supplyOrderNonVccAutoBillInfoPO);
            }
        }

        //插入担保条款
        if (null != orderDTO.getOrderRestrictEntity()) {
            orderDTO.getOrderRestrictEntity().setOrderCode(orderDTO.getOrder().getOrderCode());
            orderRestrictMapper.insert(orderDTO.getOrderRestrictEntity());
        }

        // 插入订单产品提示信息
        if (CollUtilX.isNotEmpty(orderDTO.getOrderProductTips())) {
            for (OrderProductTipPO orderProductTip : orderDTO.getOrderProductTips()) {
                orderProductTip.setOrderCode(orderDTO.getOrder().getOrderCode());
            }
            orderProductTipService.saveBatch(orderDTO.getOrderProductTips());
        }

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderDTO.getOrder().getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        return orderDTO.getOrder().getId();
    }

    @Override
    @Transactional
    public Response<OtaOrderDTO> cancelOtaOrder(CancelOtaOrderDTO cancelOtaOrderDTO) {
        Date start = new Date();
        QueryOtaOrderDTO queryOtaOrderDTO = new QueryOtaOrderDTO();
        queryOtaOrderDTO.setChannelCode(cancelOtaOrderDTO.getChannelCode());
        queryOtaOrderDTO.setChannelOrderCode(cancelOtaOrderDTO.getChannelOrderCode());
        queryOtaOrderDTO.setOrderId(cancelOtaOrderDTO.getOrderId());
        queryOtaOrderDTO.setAgentCode(cancelOtaOrderDTO.getAgentCode());
        OtaOrderDTO otaOrderDTO = queryOtaOrder(queryOtaOrderDTO);
        saveSlsLog("排查慢问题：下游取消订单，查询订单详情", start, cancelOtaOrderDTO.getRequestId());
        if (otaOrderDTO == null) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        } else {
            CompletableFuture.runAsync(() -> {
                if (otaOrderDTO.getOrderId() != null) {
                    Example example = new Example(GuestPO.class);
                    example.createCriteria().andEqualTo("orderId", otaOrderDTO.getOrderId());
                    List<GuestPO> guestPOS = guestMapper.selectByExample(example);
                    StringBuilder guestInfos = new StringBuilder();
                    if (CollUtilX.isNotEmpty(guestPOS)) {
                        for (GuestPO guestPO : guestPOS) {
                            guestInfos.append(guestPO.getName()).append(",");
                        }
                        guestInfos.deleteCharAt(guestInfos.length() - 1);
                    }
                    OrderNotifyMessageReq orderNotifyMessageReq = OrderNotifyMessageReq.builder()
                            .orderNotifyType(OrderNotifyType.APPLY_CANCEL_ORDER.getType())
                            .orderCode(otaOrderDTO.getOrderCode())
                            .agentName(otaOrderDTO.getAgentName())
                            .guestInfos(guestInfos.toString())
                            .build();
                    orderNotifyRemote.sendOrderNotifyMessageToAllUsers(orderNotifyMessageReq);
                }
            });

            // 订单已取消直接返回成功
            if (otaOrderDTO.getStatus() == 2) {
                otaOrderDTO.setCancelStatus(CancelOrderEnum.CANCEL_SUCCESS.key);
                return Response.success(otaOrderDTO);
            }

            // checkMap: key=checkCancel 取消类型 null:满足条款 1:不满足条款 2:可以取消，但需要支付费用 key=type 退订费类型 1:全部费用 2:百分比 3:指定金额 4:晚数 key=value 退订费值，可能是固定金额、百分百值
            Map<String, String> checkMap = new HashMap<>();
            if (otaOrderDTO.getStatus() == 1) { //当已确认的时候，则需要判断条款
                checkMap = judgeCancelOrder(otaOrderDTO);
            }
            if (cancelOtaOrderDTO.getHardCancel() != null && cancelOtaOrderDTO.getHardCancel() == 1) {
                Date start1 = new Date();
                //增加到取消申请
                addOrderRequest(1, cancelOtaOrderDTO.getCancelReason(), 1, otaOrderDTO.getOrderId(), 0, cancelOtaOrderDTO.getChannelCode(), 0, cancelOtaOrderDTO.getRequestId());
                saveSlsLog("排查慢问题：下游取消订单，增加到取消申请", start1, cancelOtaOrderDTO.getRequestId());
                otaOrderDTO.setCancelStatus(CancelOrderEnum.CANCEL_SUCCESS.key);
            } else if (StrUtilX.isEmpty(checkMap.get("checkCancel"))) { // 条款满足
                Date start4 = new Date();
                // 判断是否可以直接取消
                Integer orderId = Integer.valueOf(otaOrderDTO.getOrderId());
                // 查询供货单信息
                SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
                supplyOrderQuery.setOrderId(orderId);
                List<SupplyOrderPO> supplyOrderPOS = supplyOrderMapper.select(supplyOrderQuery);
                saveSlsLog("排查慢问题：下游取消订单，查询供货单", start4, cancelOtaOrderDTO.getRequestId());
                // 校验是否立即取消
                if (checkDownCancel(supplyOrderPOS)) {
                    Date start2 = new Date();
                    CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
                    cancelOrderDTO.setOrderId(orderId);
//                    cancelOrderDTO.setCancelledReason(cancelOtaOrderDTO.getCancelReason());
                    // dhub的取消原因对应天宫的取消内容
                    cancelOrderDTO.setCancelledContent(cancelOtaOrderDTO.getCancelReason());
                    cancelOrderDTO.setOperator(cancelOtaOrderDTO.getChannelCode());
                    cancelOrderDTO.setChannelCode(cancelOtaOrderDTO.getChannelCode());
                    // 先取消供货单，再取消订单
                    // 录取消供货单结果
                    if (com.tiangong.util.CollUtilX.isNotEmpty(supplyOrderPOS)) {
                        for (SupplyOrderPO supplyOrderPO : supplyOrderPOS) {
                            // 判断供货单是否未发单、待确认状态
                            if (supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key) &&
                                    supplyOrderPO.getSendingStatus().equals(SendingStatusEnum.UNSEND.key)) {
                                SaveSupplyResultDTO saveSupplyResultDTO = new SaveSupplyResultDTO();
                                saveSupplyResultDTO.setSupplyOrderId(supplyOrderPO.getId());
                                saveSupplyResultDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                                saveSupplyResultDTO.setConfirmationStatus(ConfirmationStatusEnum.CANCELED.key);
                                saveSupplyResultDTO.setRefundFee(BigDecimal.ZERO);
                                saveSupplyResultDTO.setRefusedReason(cancelOtaOrderDTO.getCancelReason());
                                saveSupplyResultDTO.setOperator(cancelOtaOrderDTO.getChannelCode());
                                supplyOrderService.saveSupplyResult(saveSupplyResultDTO);
                            }
                        }
                    }
                    saveSlsLog("排查慢问题：下游取消订单，录取消供货单结果", start2, cancelOtaOrderDTO.getRequestId());

                    Date start3 = new Date();
                    // 调用取消订单接口
                    cancelOrderDTO.setRequestId(cancelOtaOrderDTO.getRequestId());
                    cancelOrderDTO.setNotCheckCancelStatus(true);
                    orderService.cancelOrder(cancelOrderDTO, 2, true);
                    otaOrderDTO.setCancelStatus(CancelOrderEnum.CANCEL_SUCCESS.key);
                    saveSlsLog("排查慢问题：下游取消订单，调用取消订单接口", start3, cancelOtaOrderDTO.getRequestId());
                } else {
                    Date start1 = new Date();
                    addOrderRequest(0, cancelOtaOrderDTO.getCancelReason(), 1, otaOrderDTO.getOrderId(), 0, cancelOtaOrderDTO.getChannelCode(), 0, cancelOtaOrderDTO.getRequestId());
                    otaOrderDTO.setCancelStatus(CancelOrderEnum.ASYNC_CANCEL.key);
                    saveSlsLog("排查慢问题：下游取消订单，增加到取消申请", start1, cancelOtaOrderDTO.getRequestId());
                }
                saveSlsLog("排查慢问题：下游取消订单，条款满足", start4, cancelOtaOrderDTO.getRequestId());
            } else {
                Date start2 = new Date();
                // 不满足取消条款的,记录取消申请,记录不满足条款取消日志
                addOrderRequest(0, cancelOtaOrderDTO.getCancelReason(), 1, otaOrderDTO.getOrderId(), 0, cancelOtaOrderDTO.getChannelCode(), 1, cancelOtaOrderDTO.getRequestId());
                otaOrderDTO.setCancelStatus(CancelOrderEnum.ASYNC_CANCEL.key);
                saveSlsLog("排查慢问题：下游取消订单，不满足取消条款的,记录取消申请", start2, cancelOtaOrderDTO.getRequestId());
            }
            return Response.success(otaOrderDTO);
        }
    }

    /**
     * 保存日志
     */
    private void saveSlsLog(String message, Date start, String logId) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "cancelOtaOrder");
        map.put("logFlag", "排查慢问题");
        map.put("logId", logId);
        map.put(SlsEnum.MESSAGE.getType(), message);
        map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
        slsLoggerUtil.saveLog(map, "cancelOtaOrder", "tiangong-order-server");
    }

    /**
     * 校验是否立即取消
     */
    private boolean checkDownCancel(List<SupplyOrderPO> supplyOrderPOS) {
        // 判断是否所有供货单都是未发单或已取消
        return supplyOrderPOS.stream().allMatch(supplyOrderPO ->
                (supplyOrderPO.getConfirmationStatus() != null && supplyOrderPO.getSendingStatus() != null &&
                        ((supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key) && supplyOrderPO.getSendingStatus().equals(SendingStatusEnum.UNSEND.key)) ||
                                supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key))));
    }

    /**
     * 增加取消申请
     *
     * @param hardCancel     是否强制取消， 1为强制取消
     * @param reason         取消原因
     * @param resultType     处理结果   0未处理，1同意处理，2拒绝处理
     * @param orderId        订单id
     * @param requestType    订单申请类型，0取消单申请，1修改单申请，2二次确认, 3退款通知
     * @param overTimeCancel 超时取消
     */
    private void addOrderRequest(Integer hardCancel, String reason, Integer resultType, String orderId, Integer requestType,
                                 String channelCode, Integer overTimeCancel, String requestId) {
        AddOrderRequestDTO addOrderRequestDTO = new AddOrderRequestDTO();
        addOrderRequestDTO.setOrderId(Integer.valueOf(orderId));
        addOrderRequestDTO.setRequestType(requestType);
        addOrderRequestDTO.setResponseType(resultType);
        addOrderRequestDTO.setHardCancel(hardCancel);
        addOrderRequestDTO.setHandlerReason(reason);
        addOrderRequestDTO.setOperator(channelCode);
        addOrderRequestDTO.setOverTimeCancel(overTimeCancel);
        addOrderRequestDTO.setRequestId(requestId);

        orderService.addOrderRequest(addOrderRequestDTO);
    }


    /**
     * 判断该产品是否可以取消
     */
    private Map<String, String> judgeCancelOrder(OtaOrderDTO orderDTO) {
        Map<String, String> checkMap = new HashMap<>();
        // 没有条款默认啥都行
        if (StrUtilX.isEmpty(orderDTO.getCancellationTerm())) {
            return checkMap;
        }
        // 转换成取消对象
        List<CancelRestriction> cancelRestrictions = JSON.parseObject(orderDTO.getCancellationTerm(), new TypeReference<List<CancelRestriction>>() {
        });
        if (CollUtilX.isEmpty(cancelRestrictions)) {
            return checkMap;
        }
        // 获取客户信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, orderDTO.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST.errorCode, ErrorCodeEnum.AGENT_INFO_NOT_EXIST.errorDesc);
        }
        Date lastDate = null;// 最大权重的条款时间
        Date currentDate = DateUtilX.getCurrentDate();// 当前时间
        for (CancelRestriction cancelRestriction : cancelRestrictions) {
            // 判断条款是为不可取消
            if (cancelRestriction.getCancelRestrictionType() != null && cancelRestriction.getCancelRestrictionType().compareTo(1) == 0) {
                // 当时间小于离店日期+1，并且是全球分销商时不拦截条款
                if (agentAccountConfig.getDomesticOrOverseas() != null && agentAccountConfig.getDomesticOrOverseas() == 2 &&
                        currentDate.compareTo(DateUtilX.getAddDay(DateUtilX.stringToDate(orderDTO.getCheckOutDate()), 1)) < 0) {
                    return checkMap;
                }
                checkMap.put("checkCancel", "1");// 标识不可取消
                return checkMap;
            }
            // 提前时间
            String cancelRestrictionTime = cancelRestriction.getCancelRestrictionTime();
            // 条款日期
            String cancelRestrictionDate = cancelRestriction.getCancelRestrictionDate();
            if (StrUtilX.isNotEmpty(cancelRestrictionDate)) {
                Date restrictDate = DateUtilX.stringToDate(cancelRestrictionDate);
                // 判断小时和分钟是否为空
                if (StrUtilX.isNotEmpty(cancelRestrictionTime)) {
                    int hour = Integer.parseInt(cancelRestrictionTime.substring(0, 2));
                    int minute = Integer.parseInt(cancelRestrictionTime.substring(2));
                    restrictDate = DateUtilX.setTime(DateUtilX.stringToDate(cancelRestrictionDate), hour, minute, 0);
                }
                // 比较上个条款时间和当前条款时间
                if (lastDate == null || lastDate.compareTo(restrictDate) > 0) {
                    lastDate = restrictDate;
                }
            }
        }
        // 判断时间
        if (lastDate != null && currentDate.compareTo(lastDate) > 0) {
            checkMap.put("checkCancel", "1");// 标识不可取消
            // 当时间小于离店日期+1，并且是全球分销商时不拦截条款
            if (agentAccountConfig.getDomesticOrOverseas() != null && agentAccountConfig.getDomesticOrOverseas() == 2 &&
                    currentDate.compareTo(DateUtilX.getAddDay(DateUtilX.stringToDate(orderDTO.getCheckOutDate()), 1)) < 0) {
                checkMap.remove("checkCancel");
            }
            return checkMap;
        }
        return checkMap;
    }



    /**
     * 根据分销商编码查询供应商白名单
     */
    private List<String> getAgentSupplyAvailable(String agentCode) {
        String supplyCodes = (String) RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_KEY, agentCode);
        return Optional.ofNullable(supplyCodes).map(e -> e.split(",")).map(Arrays::asList).orElse(new LinkedList<>());
    }

    @Override
    public void autoDeductRefundCreditLineByOrderCode(String orderCode) {
        Example example = new Example(OrderPO.class);
        example.createCriteria().andEqualTo("orderCode", orderCode);
        List<OrderPO> orderPOS = orderMapper.selectByExample(example);
        OrderPO orderPO = null;
        if (com.tiangong.util.CollUtilX.isNotEmpty(orderPOS)) {
            orderPO = orderPOS.get(0);
        }
        // 非单结自动扣额度
        if (orderPO != null && orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key && !PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
            AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
            if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
            } else {
                agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
            }
            agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
            agentCreditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt().negate());
            agentCreditLineDTO.setCreatedBy(Constant.SYSTEM);
            Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
            if (!creditLineResponse.isSuccess()) {
                throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
            }
        }
    }

    /**
     * 修改订单状态以及扣除额度
     */
    @Transactional
    @Override
    public CreditLineOrderDTO updatePayStatusAndDeductRefundCreditLineByOrderCode(UpdatePayStatusAndDeductRefundCreditLineDTO request) {
        Example example = new Example(OrderPO.class);
        if (!StringUtils.isEmpty(request.getFcOrderCode())) {
            example.createCriteria().andEqualTo("orderCode", request.getFcOrderCode()).andEqualTo("agentCode", request.getAgentCode()).andEqualTo("channelCode", request.getChannelCode());
        } else if (!StringUtils.isEmpty(request.getOrderCode())) {
            example.createCriteria().andEqualTo("channelOrderCode", request.getOrderCode()).andEqualTo("agentCode", request.getAgentCode()).andEqualTo("channelCode", request.getChannelCode());
        } else {
            throw new SysException(ErrorCodeEnum.ORDER_CODE_OR_FC_ORDER_CODE_NOT_EMPTY.errorCode, ErrorCodeEnum.ORDER_CODE_OR_FC_ORDER_CODE_NOT_EMPTY.errorDesc);
        }
        // 2024-09-02 author:湫  订单存在，但是已支付 再次调用支付接口 不应该抛出订单不存在异常。当订单为已支付状态,返回已支付
//        example.createCriteria().andEqualTo("payStatus", PayStatusEnum.NONE.getNo());
        List<OrderPO> orderPOS = orderMapper.selectByExample(example);
        OrderPO orderPO = null;
        if (orderPOS != null && orderPOS.size() > 0) {
            orderPO = orderPOS.get(0);
        } else {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }
        if (Objects.equals(orderPO.getPayStatus(), PayStatusEnum.PAY.getNo())) {
            throw new SysException(ErrorCodeEnum.ORDER_PAY_IS_ALREADY.errorCode, ErrorCodeEnum.ORDER_PAY_IS_ALREADY.errorDesc);
        }
        if (orderPO.getOrderAmt() != null && request.getPayAmount() != null && orderPO.getOrderAmt().compareTo(request.getPayAmount()) != 0) {
            throw new SysException(ErrorCodeEnum.PAY_AMOUNT_NEQ_ORDER_AMT.errorCode, ErrorCodeEnum.PAY_AMOUNT_NEQ_ORDER_AMT.errorDesc);
        }

        Example updateOrderExample = new Example(OrderPO.class);
        if (!StringUtils.isEmpty(request.getFcOrderCode())) {
            updateOrderExample.createCriteria().andEqualTo("orderCode", request.getFcOrderCode());
        }
        if (!StringUtils.isEmpty(request.getOrderCode())) {
            updateOrderExample.createCriteria().andEqualTo("channelOrderCode", request.getOrderCode());
        }
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setPayStatus(PayStatusEnum.PAY.getNo());
        int updateRows = orderMapper.updateByExampleSelective(orderUpdate, updateOrderExample);
        if (updateRows != 1) {
            throw new SysException(ErrorCodeEnum.UPDATE_PAY_STATUS_ERROR.errorCode, ErrorCodeEnum.UPDATE_PAY_STATUS_ERROR.errorDesc);
        }
        CreditLineOrderDTO dto = new CreditLineOrderDTO();
        dto.setCoOrderCode(orderPO.getChannelOrderCode());
        dto.setFcOrderCode(orderPO.getOrderCode());

        // 记录日志，订单支付
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                ChannelEnum.DHUB.key,
                ChannelEnum.DHUB.key,
                orderPO.getOrderCode(),
                "订单支付状态：由待支付改为已支付"
        );

        // 组装自动发单参数
        AssemblyOrderDTO assemblyOrderDTO = convertSendPayOrder(orderPO);

        // 注册事务同步器
        OrderPO finalOrderPO = orderPO;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                try {
                    // 问题单或已支付不自动发单
                    if (assemblyOrderDTO != null && (finalOrderPO.getIsAbnormal() == null || finalOrderPO.getIsAbnormal() == 0) &&
                            (finalOrderPO.getPayStatus() == null || !Objects.equals(finalOrderPO.getPayStatus(), PayStatusEnum.PAY.getNo()))) {
                        asyncSendOrder(assemblyOrderDTO);
                    } else {
                        log.info("问题单" + JSON.toJSONString(finalOrderPO));
                        log.info("订单orderCode：" + finalOrderPO.getOrderCode() + "为异常单，不自动发单");
                    }
                } catch (Exception e) {
                    log.error("自动发单功能异常", e);
                }
            }
        });

        return dto;
    }

    /**
     * 订单支付自动发单参数组装
     * @param orderPO
     * @return
     */
    private AssemblyOrderDTO convertSendPayOrder(OrderPO orderPO) {
        AssemblyOrderDTO assemblyOrderDTO = new AssemblyOrderDTO();
        List<AssemblySupplyOrderDTO> supplyOrderDTOS = new ArrayList<>();

        // 查询未发单供货单
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(orderPO.getId());
        supplyOrderQuery.setSendingStatus(SendingStatusEnum.UNSEND.key);
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);
        if (CollUtilX.isEmpty(supplyOrderPOList)){
            return null;
        }

        // 组装自动发单参数
        for (SupplyOrderPO supplyOrderPO : supplyOrderPOList) {
            AssemblySupplyOrderDTO assemblySupplyOrderDTO = new AssemblySupplyOrderDTO();
            assemblySupplyOrderDTO.setSupplyOrder(supplyOrderPO);
            supplyOrderDTOS.add(assemblySupplyOrderDTO);
        }

        assemblyOrderDTO.setOrder(orderPO);
        assemblyOrderDTO.setSupplyOrderList(supplyOrderDTOS);
        String uuId = UUID.randomUUID().toString();
        assemblyOrderDTO.setRequestId(uuId);
        return assemblyOrderDTO;
    }

    @Override
    public void addSupplyOrder(AddManualOrderDTO request) {
        // 校验参数
        checkAddSupplyOrderParam(request);

        //查询分销商
        AgentAccountConfig agentAccountConfig = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()).toString(), AgentAccountConfig.class);
        if (agentAccountConfig.getDomesticOrOverseas() != null && agentAccountConfig.getDomesticOrOverseas().compareTo(2) == 0) {
            request.setSupplyType("2");// 全球
        } else {
            request.setSupplyType("1");// 国内
        }

        // 设置产品id
        request.setProductId(ProductMappingCache.getOriginalProductId(request.getProductId()));
        request.setSpProductId(request.getProductId());

        // 试预订
        PreBookDTO preBookDTO = new PreBookDTO();
        preBookDTO.setCheckInDate(request.getStartDate());// 入住日期
        preBookDTO.setCheckOutDate(request.getEndDate());// 离店日期
        preBookDTO.setHotelId(request.getHotelId());// 酒店id
        preBookDTO.setRoomId(request.getRoomId());// 房间id
        preBookDTO.setProductId(request.getProductId());// 产品id
        preBookDTO.setRoomQty(request.getRoomQty());// 房间数
        preBookDTO.setTotalPrice(request.getTotalSalePrice());// 总价
        preBookDTO.setCompanyCode(request.getCompanyCode());// 商家编码
        preBookDTO.setChannelCode(request.getChannelCode());// 渠道编码
        preBookDTO.setAgentCode(request.getAgentCode());// 分销商编码
        preBookDTO.setAgentName(request.getAgentName());// 分销商名称
        preBookDTO.setBreakfast(request.getBreakfastQty());// 早餐数

        preBookDTO.setSupplyCode(request.getSupplierCode());// 供应商编码
        preBookDTO.setGuestQuantity(request.getGuestQuantity());// 入住人数
//        preBookDTO.setHourly();// 是否钟点房1是0否 默认日历房
        preBookDTO.setSupplyType(request.getSupplyType());// 供应方式 1国内 2海外
        List<RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
        for (OrderRoomGuestDTO orderRoomGuestDTO : request.getRoomGuestList()) {
            RoomGuestNumber roomGuestNumber = new RoomGuestNumber();
            roomGuestNumber.setRoomIndex(orderRoomGuestDTO.getRoomNumber());
            roomGuestNumber.setAdultNum(orderRoomGuestDTO.getAdultQty());
            if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                List<ChildrenInfo> childrenInfoList = new ArrayList<>();
                String[] ageArr = orderRoomGuestDTO.getChildrenAge().split(",");
                for (String age : ageArr) {
                    ChildrenInfo childrenInfo = new ChildrenInfo();
                    childrenInfo.setChildrenAge(Integer.valueOf(age));
                    childrenInfoList.add(childrenInfo);
                }
                roomGuestNumber.setChildrenInfos(childrenInfoList);
            }
            roomGuestNumbers.add(roomGuestNumber);
        }
        List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbersList = ProductSalePriceConvert.INSTANCE.roomGuestNumberConvert(roomGuestNumbers);
        preBookDTO.setRoomGuestNumbers(roomGuestNumbersList);// 房间人数信息
        String uuId = UUID.randomUUID().toString();
        request.setRequestId(uuId);
        preBookDTO.setRequestId(uuId);

        // 试预订
        this.checkPreBook(preBookDTO);

        // 产品信息
        ProductSalePriceDTO productSalePriceDTO;

        // 查询本地数据
        Map<String, String> product = new HashMap<>();
        product.put("productId", request.getProductId());
        Response<ProductDTO> product1 = productRemote.queryProduct(product);
        if (Objects.nonNull(product1) && product1.getResult() != null && product1.getResult().equals(ResultCodeEnum.SUCCESS.code) && Objects.nonNull(product1.getModel())) {
            QueryProductRequestDTO queryProductRequestDTO = new QueryProductRequestDTO();
            queryProductRequestDTO.setProductId(request.getProductId());
            queryProductRequestDTO.setStartDate(request.getStartDate());
            queryProductRequestDTO.setEndDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(request.getEndDate()), -1, 0)));
            queryProductRequestDTO.setCompanyCode(request.getCompanyCode());
            queryProductRequestDTO.setChannelCode(request.getChannelCode());
            queryProductRequestDTO.setAgentCode(request.getAgentCode());
            queryProductRequestDTO.setLanguage(request.getLanguage());
            Response<ProductSalePriceDetailDTO> productResponse = productSaleRemote.querySalePriceList(queryProductRequestDTO);
            if (productResponse.isError()) {
                throw new SysException(productResponse.getFailCode(), productResponse.getFailReason());
            }
            if (null == productResponse.getModel()) {
                throw new SysException(ErrorCodeEnum.PRODUCT_IS_NOT_EXIST);
            }
            productSalePriceDTO = ProductSalePriceConvert.INSTANCE.convert(productResponse.getModel());
            if (CollectionUtil.isEmpty(productSalePriceDTO.getRoomItemDetails())) {
                List<com.tiangong.dto.order.response.OrderRoomDetailDto> orderRoomDetailDtos = convertOrderRoomDetail2(request.getRoomGuestList(), productResponse.getModel(), productSalePriceDTO);
                productSalePriceDTO.setRoomItemDetails(orderRoomDetailDtos);
            }
        } else {
            // 获取供应商产品信息
            QueryProductInfoRequest queryProductInfoRequest = new QueryProductInfoRequest();
            queryProductInfoRequest.setSpHotelId(String.valueOf(request.getHotelId()));
            queryProductInfoRequest.setGuestQuantity(request.getAdultQty());
            queryProductInfoRequest.setCheckInDate(request.getStartDate());
            queryProductInfoRequest.setCheckOutDate(request.getEndDate());
            queryProductInfoRequest.setSupplyCodes(Collections.singleton(request.getSupplierCode()));
            queryProductInfoRequest.setSpRoomId(request.getRoomId());
            queryProductInfoRequest.setRoomQty(request.getRoomQty());
            queryProductInfoRequest.setSpProductId(request.getProductId());
            queryProductInfoRequest.setSupplyType(request.getSupplyType());
            queryProductInfoRequest.setRoomGuestNumbers(roomGuestNumbers);
            if (StrUtilX.isNotEmpty(request.getHourlyTime())) {
                queryProductInfoRequest.setOnlyHourRoom(1);
            }
            queryProductInfoRequest.setRequestId(request.getRequestId());
            queryProductInfoRequest.setCurrency(agentAccountConfig.getSettlementCurrency());

            Response<List<ProductMiddleDto>> queryProductResponse = supplyDirectShubProductRemote.queryProductInfo(queryProductInfoRequest);
            if (Objects.nonNull(queryProductResponse) && Objects.equals(queryProductResponse.getResult(), ResultCodeEnum.SUCCESS.code)) {
                productSalePriceDTO = assemblySupplyProduct(queryProductResponse.getModel(), request, agentAccountConfig);
            } else {
                throw new SysException(ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorCode, ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorDesc);
            }
        }

        // 价格明细是否为空
        if (CollUtilX.isEmpty(productSalePriceDTO.getRoomItemDetails())) {
            throw new SysException(ErrorCodeEnum.NO_SALE_PRICE.errorCode, ErrorCodeEnum.NO_SALE_PRICE.errorDesc);
        }

        // 查询订单信息
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        if (orderPO == null) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }
        // 校验产品类型
        if (orderPO.getPayMethod() != null && !orderPO.getPayMethod().equals(productSalePriceDTO.getPayAtHotelFlag())) {
            throw new SysException(ErrorCodeEnum.PRODUCT_TYPE_UNLIKE.errorCode, ErrorCodeEnum.PRODUCT_TYPE_UNLIKE.errorDesc);
        }

        // 校验币种
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(request.getOrderId());
        List<SupplyOrderPO> supplyOrderPOS = supplyOrderMapper.select(supplyOrderQuery);
        if (CollUtilX.isEmpty(supplyOrderPOS)) {
            throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST.errorCode, ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST.errorDesc);
        }
        Integer baseCurrency = supplyOrderPOS.get(0).getBaseCurrency();
        if (productSalePriceDTO.getCurrency() != null && productSalePriceDTO.getCurrency().compareTo(baseCurrency) != 0) {
            throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_CURRENCY_UNLIKE.errorCode, ErrorCodeEnum.SUPPLY_ORDER_CURRENCY_UNLIKE.errorDesc);
        }

        // 组装供货单信息
        AssemblyOrderDTO assemblyOrderDTO = assemblySupplyOrderData(request, productSalePriceDTO, agentAccountConfig);

        // 保存供货单信息
        saveSupplyOrder(assemblyOrderDTO, orderPO.getId(), orderPO.getOrderCode(), request.getOperator(), request.getOrderOwnerName());
    }

    /**
     * 保存供货单信息
     */
    private void saveSupplyOrder(AssemblyOrderDTO assemblyOrderDTO,
                                 Integer orderId,
                                 String orderCode,
                                 String operator,
                                 String orderOwnerName) {
        List<String> supplyOrderCodeList = new ArrayList<>();
        // 添加供货单
        for (AssemblySupplyOrderDTO supplyOrderDTO : assemblyOrderDTO.getSupplyOrderList()) {
            supplyOrderDTO.getSupplyOrder().setOrderId(orderId);
            // 获取供货单编码
//            String supplyOrderCode = RedisTemplateX.lRightPop(SystemCodeEnum.SUPPLYORDERCODE.code);
//            if (null == supplyOrderCode) {
//                supplyOrderCode = getNewSyncSupplyCode();
//            }
            String supplyOrderCode = DistributedCodeGenerator.generateSupplyOrderCode();
            supplyOrderCodeList.add(supplyOrderCode);
            supplyOrderDTO.getSupplyOrder().setSupplyOrderCode(supplyOrderCode);
            supplyOrderMapper.insert(supplyOrderDTO.getSupplyOrder());
            Integer supplyOrderId = supplyOrderDTO.getSupplyOrder().getId();
            SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(supplyOrderId);
            supplyOrderDTO.getSupplyOrderFinance().setSupplyOrderId(supplyOrderId);
            supplyOrderDTO.getSupplyOrderFinance().setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            supplyOrderFinanceMapper.insert(supplyOrderDTO.getSupplyOrderFinance());
            supplyOrderDTO.getSupplyRewardFinance().setSupplyOrderId(supplyOrderId);
            supplyOrderDTO.getSupplyRewardFinance().setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            supplyOrderFinanceMapper.insert(supplyOrderDTO.getSupplyRewardFinance());
            supplyOrderDTO.getSupplyRebateFinance().setSupplyOrderId(supplyOrderId);
            supplyOrderDTO.getSupplyRebateFinance().setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            supplyOrderFinanceMapper.insert(supplyOrderDTO.getSupplyRebateFinance());
            for (AssemblySupplyProductDTO supplyProductDTO : supplyOrderDTO.getSupplyProductList()) {
                supplyProductDTO.getSupplyProduct().setSupplyOrderId(supplyOrderId);
                supplyProductDTO.getSupplyProduct().setOrderId(orderId);
                supplyProductDTO.getSupplyProduct().setProductName(supplyOrderDTO.getSupplyOrder().getProductName());
                supplyProductDTO.getSupplyProduct().setEndDate(supplyProductDTO.getSupplyProduct().getEndDate());
                supplyProductMapper.insert(supplyProductDTO.getSupplyProduct());
                Integer supplyProductId = supplyProductDTO.getSupplyProduct().getId();
                supplyProductDTO.getSupplyProductPriceList().forEach(supplyProductPricePO -> {
                    supplyProductPricePO.setSupplyOrderId(supplyOrderId);
                    supplyProductPricePO.setSupplyProductId(supplyProductId);
                });
                supplyProductPriceMapper.insertList(supplyProductDTO.getSupplyProductPriceList());
            }
        }

        try {
            // 加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderCode);
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        // 记日志
        for (String supplyOrderCode : supplyOrderCodeList) {
            try {
                orderCommonService.saveOrderLog(
                        orderId,
                        operator,
                        orderOwnerName,
                        supplyOrderCode,
                        "新增供货单:" + supplyOrderCode
                );
            } catch (Exception e) {
                log.error("addSupplyOrder--supplyOrderCode:{}--error:{}", supplyOrderCode, e.getMessage());
            }
        }
    }

    /**
     * 校验添加供货单参数
     */
    private void checkAddSupplyOrderParam(AddManualOrderDTO request) {
        if (request.getOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERID);
        }
        if (request.getHotelId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_HOTELID);
        }
        if (StrUtilX.isEmpty(request.getHotelName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_HOTELNAME);
        }
        if (request.getRoomId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMID);
        }
        if (StrUtilX.isEmpty(request.getRoomName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMNAME);
        }
        if (request.getProductId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SPPRODUCTID);
        }
        if (StrUtilX.isEmpty(request.getStartDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKINDATE);
        }
        if (StrUtilX.isEmpty(request.getEndDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKOUTDATE);
        }
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        if (StrUtilX.isEmpty(request.getSupplierCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
        }
        if (request.getRoomQty() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMNUM);
        }
        if (request.getGuestQuantity() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_GUESTQUANTITY);
        }
        if (CollUtilX.isEmpty(request.getRoomGuestList())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMCHECKDETAILS);
        }
    }

    /**
     * 组装供货单信息
     */
    private AssemblyOrderDTO assemblySupplyOrderData(AddManualOrderDTO request, ProductSalePriceDTO productSalePriceDTO, AgentAccountConfig accountConfig) {
        // 供应商对CNY汇率
        BigDecimal supplierToOrgRate = null;
        // 商家币种转客户币种汇率
        BigDecimal orgToAgentRate = null;

        // 获取商家币种
        com.tiangong.finance.OrgDTO orgDTO = CommonInitializer.getOrgInfo();


        // 组装供货单
        AssemblyOrderDTO assemblyOrderDTO = new AssemblyOrderDTO();
        String currentDate = DateUtilX.dateToString(new Date(), hour_format);
        AssemblySupplyOrderDTO assemblySupplyOrderDTO = new AssemblySupplyOrderDTO();
        AssemblySupplyProductDTO assemblySupplyProductDTO = new AssemblySupplyProductDTO();

        // 查询佣金
        BigDecimal commission = new BigDecimal(0);
        long day = DateUtilX.getDay(DateUtilX.stringToDate(request.getStartDate()), DateUtilX.stringToDate(request.getEndDate()));
        for (int i = 0; i < day; i++) {
            ProductBasePriceAndRoomStatusDTO psDto = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productBasePriceAndRoomStatusKey,
                    request.getProductId() + "_" + DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(request.getStartDate()), i))), ProductBasePriceAndRoomStatusDTO.class);
            commission = commission.add(psDto == null ? new BigDecimal(0) : (psDto.getCommission() == null ? BigDecimal.ZERO : psDto.getCommission()));
        }
        // 佣金需要乘以间数
        commission = commission.multiply(new BigDecimal(request.getRoomQty()));

        // 查询供应商缓存
        AddSupplierReq supplier = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, request.getSupplierCode()).toString(), AddSupplierReq.class);
        if (supplier == null) {
            log.error("供应商缓存不存在，supplyCode={}", request.getSupplierCode());
            throw new SysException(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorCode, ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorDesc);
        }
        String operator = null;
        if (StrUtilX.isNotEmpty(request.getOperator())) {
            operator = request.getOperator();
        } else {
            operator = accountConfig.getAgentName();
        }

        // 组装供货单
        // 计算供应商价格
        BigDecimal supplyOrderAmt = BigDecimal.ZERO;

        //组装价格
        assemblySupplyProductDTO.setSupplyProductPriceList(new ArrayList<>());
        assemblyOrderDTO.setOrderProductPriceList(new ArrayList<>());
        Integer breakfastNum = null;// 早餐数量
        if (CollUtilX.isNotEmpty(productSalePriceDTO.getRoomItemDetails())) {
            // 计算一次汇率
            supplierToOrgRate = RedisUtil.getRateToOrgCurrency(productSalePriceDTO.getRoomItemDetails().get(0).getPriceInfoDetails().get(0).getBaseCurrency(), CompanyDTO.COMPANY_CODE);
            orgToAgentRate = RedisUtil.getRateToTargetCurrency(Integer.parseInt(orgDTO.getOrgCurrency()), CompanyDTO.COMPANY_CODE, productSalePriceDTO.getRoomItemDetails().get(0).getPriceInfoDetails().get(0).getSettlementCurrency());
            breakfastNum = productSalePriceDTO.getRoomItemDetails().get(0).getPriceInfoDetails().get(0).getBreakfastNum();

            if (supplierToOrgRate == null || orgToAgentRate == null) {
                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorCode, ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorDesc);
            }

            // 组装供货单产品价格
            assemblySupplyOrderPrice(assemblySupplyProductDTO, productSalePriceDTO.getRoomItemDetails(), request);
        }

        for (com.tiangong.dto.order.response.OrderRoomDetailDto productSalePriceItemDTO : productSalePriceDTO.getRoomItemDetails()) {
            for (com.tiangong.dto.product.PriceInfoDetail priceInfoDetail : productSalePriceItemDTO.getPriceInfoDetails()) {

                if (Objects.nonNull(priceInfoDetail.getTaxDetail())) {
                    supplyOrderAmt = supplyOrderAmt.add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyRoomPrice()))
                            .add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyTaxFee()))
                            .add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplySalesTax()))
                            .add(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyOtherTax()));
                } else {
                    supplyOrderAmt = supplyOrderAmt.add(priceInfoDetail.getBasePrice());
                }

                //按次税费
                if (priceInfoDetail.getDate().equals(request.getStartDate()) && Objects.nonNull(productSalePriceItemDTO.getTaxDetail())) {
                    supplyOrderAmt = supplyOrderAmt.add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getSupplyTaxFee())).
                            add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getSupplyOtherTax())).
                            add(CommonTgUtils.formatBigDecimal(productSalePriceItemDTO.getTaxDetail().getSupplySalesTax()));
                }
            }
        }

        assemblyOrderDTO.setSupplyOrderList(Collections.singletonList(assemblySupplyOrderDTO));
        SupplyOrderPO supplyOrderPO = new SupplyOrderPO();
        BeanUtils.copyProperties(request, supplyOrderPO);
        //fixme mapper类型转换BUG
//        SupplyOrderPO supplyOrderPO = ProductSalePriceConvert.INSTANCE.supplyOrderPOConvert(request);
        supplyOrderPO.setSupplierCode(productSalePriceDTO.getSupplierCode());
        supplyOrderPO.setSupplierName(productSalePriceDTO.getSupplierName());
        supplyOrderPO.setBaseCurrency(productSalePriceDTO.getCurrency());
        // 现付类型、供货单金额= -佣金
        if (productSalePriceDTO.getPayAtHotelFlag() != null && productSalePriceDTO.getPayAtHotelFlag().equals(PayMethodEnum.PAY.key)) {
            if (commission.compareTo(BigDecimal.ZERO) != 0) {
                supplyOrderPO.setSupplyOrderAmt(commission.negate());// -佣金
            } else {
                supplyOrderPO.setSupplyOrderAmt(BigDecimal.ZERO);
            }
        } else {
            supplyOrderPO.setSupplyOrderAmt(supplyOrderAmt.subtract(commission));// 供货单金额等于底价-佣金
        }
        supplyOrderPO.setHotelName(request.getHotelName());
        supplyOrderPO.setRoomName(request.getRoomName());
        supplyOrderPO.setProductName(productSalePriceDTO.getProductName());
        supplyOrderPO.setBasePrice(supplyOrderAmt);
        supplyOrderPO.setRefundFee(BigDecimal.ZERO);
        supplyOrderPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
        supplyOrderPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
        if (StrUtilX.isNotEmpty(request.getHourlyTime())) {
            String[] arr = request.getHourlyTime().split("\\--");
            supplyOrderPO.setStartTime(DateUtilX.stringToDate(arr[0], hour_format));
            supplyOrderPO.setEndTime(DateUtilX.stringToDate(arr[1], hour_format));
        }
        supplyOrderPO.setConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
        supplyOrderPO.setSendingStatus(SendingStatusEnum.UNSEND.key);
        supplyOrderPO.setCreatedBy(operator);
        supplyOrderPO.setCreatedDt(currentDate);
        supplyOrderPO.setCommission(commission);
        supplyOrderPO.setBreakfastQty(breakfastNum);
        supplyOrderPO.setRate(supplierToOrgRate);
        if (supplierToOrgRate != null) {
            supplyOrderPO.setSupplierToAgentRate(supplierToOrgRate.multiply(orgToAgentRate));
        }
        // 判断币种是否一致
        if (accountConfig.getSettlementCurrency() != null && accountConfig.getSettlementCurrency().equals(supplyOrderPO.getBaseCurrency())) {
            supplyOrderPO.setSupplierToAgentRate(BigDecimal.ONE);
        } else {
            if (supplierToOrgRate != null) {
                supplyOrderPO.setSupplierToAgentRate(supplierToOrgRate.multiply(orgToAgentRate));
            }
        }
        supplyOrderPO.setOrgToAgentRate(orgToAgentRate);
        supplyOrderPO.setRewardAmt(BigDecimal.ZERO);
        supplyOrderPO.setRebateAmt(BigDecimal.ZERO);
        if (Objects.equals(2, productSalePriceDTO.getSupplyType())) {
            supplyOrderPO.setIsSpProduct(1);
        }
        supplyOrderPO.setSupplierName(supplier.getSupplierName());
        supplyOrderPO.setSettlementType(supplier.getSettlementType());
        if (CollUtilX.isNotEmpty(request.getBedInfos())) {
            BedTypesDetailDto bedTypesDetailDto = new BedTypesDetailDto();
            List<BedInfoDto> bedInfos = ProductSalePriceConvert.INSTANCE.bedInfoDtoConvert(request.getBedInfos());
            bedTypesDetailDto.setBedInfos(bedInfos);
            supplyOrderPO.setBedType(JSONUtil.toJsonStr(bedTypesDetailDto));
        }

        // 设置房间号信息
        Set<Long> collect = request.getRoomGuestList().stream().filter(item -> item.getRoomNumber() != null).map(item -> Long.valueOf(item.getRoomNumber())).collect(Collectors.toSet());
        supplyOrderPO.setRoomNumbers(StrUtilX.listToString(collect));

        assemblySupplyOrderDTO.setSupplyOrder(supplyOrderPO);

        SupplyOrderFinancePO supplyOrderFinancePO = new SupplyOrderFinancePO();
        supplyOrderFinancePO.setPaidAmt(BigDecimal.ZERO);
        if (request.getPayMethod() != null && request.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            supplyOrderFinancePO.setUnpaidAmt(BigDecimal.ZERO);
        } else {
            supplyOrderFinancePO.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt());
        }
        supplyOrderFinancePO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
        supplyOrderFinancePO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
        supplyOrderFinancePO.setSettlementStatus(0);
        supplyOrderFinancePO.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
        supplyOrderFinancePO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
        supplyOrderFinancePO.setFinanceLockStatus(0);
        supplyOrderFinancePO.setCreatedBy(operator);
        supplyOrderFinancePO.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        supplyOrderFinancePO.setCreatedDt(currentDate);
        assemblySupplyOrderDTO.setSupplyOrderFinance(supplyOrderFinancePO);
        // 供货单奖励
        SupplyOrderFinancePO supplyRewardFinancePO = ProductSalePriceConvert.INSTANCE.supplyRewardFinancePOConvert(supplyOrderFinancePO);
        supplyRewardFinancePO.setUnpaidAmt(BigDecimal.ZERO);
        supplyRewardFinancePO.setFinanceType(StatementTypeEnum.REWARD_AMT.key);
        assemblySupplyOrderDTO.setSupplyRewardFinance(supplyRewardFinancePO);
        // 供货单奖励
        SupplyOrderFinancePO supplyRebateFinancePO = ProductSalePriceConvert.INSTANCE.supplyRebateFinancePOConvert(supplyRewardFinancePO);
        supplyRebateFinancePO.setFinanceType(StatementTypeEnum.REBATE_AMT.key);
        assemblySupplyOrderDTO.setSupplyRebateFinance(supplyRebateFinancePO);

        // 组装供货产品
        assemblySupplyOrderDTO.setSupplyProductList(Collections.singletonList(assemblySupplyProductDTO));
        SupplyProductPO supplyProductPO = new SupplyProductPO();
        BeanUtils.copyProperties(request, supplyProductPO);
        //fixme mapper类型转换BUG
//        SupplyProductPO supplyProductPO = ProductSalePriceConvert.INSTANCE.SupplyProductPOConvert(request);
        supplyProductPO.setRoomName(request.getRoomName());
        // 自签产品不写spProductId
        if (request.getProductId().contains("-") || request.getProductId().contains("_")) {
            supplyProductPO.setProductId(productSalePriceDTO.getProductId());
            supplyProductPO.setSpProductId(request.getProductId());
        } else {
            supplyProductPO.setProductId(productSalePriceDTO.getProductId());
        }

        supplyProductPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
        supplyProductPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
        // 设置入住人
        StringBuilder guestSb = new StringBuilder();
        if (CollUtilX.isNotEmpty(request.getRoomGuestList())) {
            for (OrderRoomGuestDTO guest : request.getRoomGuestList()) {
                for (OrderGuestDTO orderGuestDTO : guest.getOrderGuestList()) {
                    if (StrUtilX.isNotEmpty(orderGuestDTO.getName())) {
                        guestSb.append(orderGuestDTO.getName()).append(",");
                    } else {
                        guestSb.append(orderGuestDTO.getLastName()).append("/").append(orderGuestDTO.getFirstName()).append(",");
                    }
                }
            }
            guestSb.deleteCharAt(guestSb.length() - 1);
        }
        supplyProductPO.setGuest(guestSb.toString());
        supplyProductPO.setBasePriceTotalAmt(supplyOrderAmt);
        supplyProductPO.setCommission(commission);
        supplyProductPO.setCancellationTerm(JSON.toJSONString(productSalePriceDTO.getCancelRestrictions()));
        supplyProductPO.setBreakfastQty(breakfastNum);
        supplyProductPO.setCreatedBy(operator);
        supplyProductPO.setCreatedDt(currentDate);
        assemblySupplyProductDTO.setSupplyProduct(supplyProductPO);

        return assemblyOrderDTO;
    }

    /**
     * 组装供货单产品价格
     */
    private void assemblySupplyOrderPrice(AssemblySupplyProductDTO assemblySupplyProductDTO,
                                          List<com.tiangong.dto.order.response.OrderRoomDetailDto> priceDTOList,
                                          AddManualOrderDTO request) {
        Map<Integer, OrderRoomGuestDTO> guestMap = new HashMap<>();
        int i = 1;
        for (OrderRoomGuestDTO orderRoomGuestDTO : request.getRoomGuestList()) {
            guestMap.put((null == orderRoomGuestDTO.getRoomNumber() ? i++ : orderRoomGuestDTO.getRoomNumber()), orderRoomGuestDTO);
        }

        for (com.tiangong.dto.order.response.OrderRoomDetailDto priceDTO : priceDTOList) {
            //按次税费+到店支付费用
            if (Objects.nonNull(priceDTO.getTaxDetail()) || null != priceDTO.getPayInStorePrice()) {
                SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
                supplyProductPricePO.setRoomNumber(priceDTO.getRoomIndex());
                supplyProductPricePO.setCurrency(priceDTO.getPriceInfoDetails().get(0).getBaseCurrency());

                if (Objects.nonNull(priceDTO.getTaxDetail())) {

                    supplyProductPricePO.setExtraTaxFee(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplyOtherTax()));
                    supplyProductPricePO.setSalesTax(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplySalesTax()));
                    supplyProductPricePO.setTax(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplyTaxFee()));
                    supplyProductPricePO.setRoomPrice(CommonTgUtils.formatBigDecimal(priceDTO.getTaxDetail().getSupplyRoomPrice()));
                }

                supplyProductPricePO.setPayAtHotelCurrency(priceDTO.getPayInStoreCurrency());
                supplyProductPricePO.setPayAtHotelFee(priceDTO.getPayInStorePrice());
                assemblySupplyProductDTO.getSupplyProductPriceList().add(supplyProductPricePO);
            }

            //每日费用
            for (com.tiangong.dto.product.PriceInfoDetail priceInfoDetail : priceDTO.getPriceInfoDetails()) {
                String roomPersons = null;
                OrderRoomGuestDTO orderRoomGuestDTO = guestMap.get(priceDTO.getRoomIndex());
                if (null != orderRoomGuestDTO && null != orderRoomGuestDTO.getAdultQty()) {
                    roomPersons = orderRoomGuestDTO.getAdultQty().toString();
                    if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                        roomPersons += ("," + orderRoomGuestDTO.getChildrenAge());
                    }
                }

                SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
                supplyProductPricePO.setSaleDate(DateUtilX.stringToDate(priceInfoDetail.getDate()));
                supplyProductPricePO.setBasePrice(priceInfoDetail.getBasePrice());
                supplyProductPricePO.setRoomNumber(priceDTO.getRoomIndex());
                supplyProductPricePO.setCurrency(priceInfoDetail.getBaseCurrency());
                supplyProductPricePO.setRoomPersons(roomPersons);
                supplyProductPricePO.setBreakfastType(priceInfoDetail.getBreakfastType());
                supplyProductPricePO.setBreakfastQty(priceInfoDetail.getBreakfastNum());

                if (Objects.nonNull(priceInfoDetail.getTaxDetail())) {
                    supplyProductPricePO.setExtraTaxFee(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyOtherTax()));
                    supplyProductPricePO.setSalesTax(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplySalesTax()));
                    supplyProductPricePO.setTax(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyTaxFee()));
                    supplyProductPricePO.setRoomPrice(CommonTgUtils.formatBigDecimal(priceInfoDetail.getTaxDetail().getSupplyRoomPrice()));
                }

                assemblySupplyProductDTO.getSupplyProductPriceList().add(supplyProductPricePO);
            }
        }
    }

    @Override
    public void addManualSupplyOrder(AddManualOrderDTO request) {
        // 校验参数
        checkAddManualSupplyOrderParam(request);

        // 查询分销商
        AgentAccountConfig agentAccountConfig = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()).toString(), AgentAccountConfig.class);

        // 查询订单信息
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        if (orderPO == null) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }

        // 构建产品对象
        ProductSalePriceDTO productSalePriceDTO = buildFinalProduct(request);

        // 组装供货单信息
        AssemblyOrderDTO assemblyOrderDTO = assemblySupplyOrderData(request, productSalePriceDTO, agentAccountConfig);

        // 保存供货单信息
        saveSupplyOrder(assemblyOrderDTO, orderPO.getId(), orderPO.getOrderCode(), request.getOperator(), request.getOrderOwnerName());
    }

    @Override
    public Integer checkSupplyOrderNightQty(AddManualOrderDTO request) {
        // 校验参数
        checkSupplyOrderNightQtyParam(request);

        // 查询订单信息
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        if (orderPO == null) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }

        // 查询供货单信息
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(request.getOrderId());
        List<SupplyOrderPO> supplyOrderPOS = supplyOrderMapper.select(supplyOrderQuery);
        if (CollUtilX.isEmpty(supplyOrderPOS)) {
            return 0;
        }

        // 供货单总间夜
        BigDecimal supplyOrderRoomNights = new BigDecimal(DateUtilX.getDay(DateUtilX.stringToDate(request.getStartDate()), DateUtilX.stringToDate(request.getEndDate()))).multiply(new BigDecimal(request.getRoomQty()));
        for (SupplyOrderPO supplyOrderPO : supplyOrderPOS) {
            if (supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                continue;
            }
            supplyOrderRoomNights = supplyOrderRoomNights.add(new BigDecimal(DateUtilX.getDay(supplyOrderPO.getStartDate(), supplyOrderPO.getEndDate())).multiply(new BigDecimal(supplyOrderPO.getRoomQty())));
        }

        // 判断供货单总间夜是否大于订单总间夜
        if (supplyOrderRoomNights.compareTo(new BigDecimal(DateUtilX.getDay(orderPO.getStartDate(), orderPO.getEndDate())).multiply(new BigDecimal(orderPO.getRoomQty()))) > 0) {
            return 1;
        }
        return 0;
    }

    /**
     * 校验检查供货单间夜参数
     */
    private void checkSupplyOrderNightQtyParam(AddManualOrderDTO request) {
        if (request.getOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERID);
        }
        if (StrUtilX.isEmpty(request.getStartDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKINDATE);
        }
        if (StrUtilX.isEmpty(request.getEndDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKOUTDATE);
        }
        if (request.getRoomQty() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMNUM);
        }
    }

    /**
     * 校验添加手工供货单参数
     */
    private void checkAddManualSupplyOrderParam(AddManualOrderDTO request) {
        if (request.getOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERID);
        }
        if (request.getHotelId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_HOTELID);
        }
        if (StrUtilX.isEmpty(request.getHotelName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_HOTELNAME);
        }
        if (request.getRoomId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMID);
        }
        if (StrUtilX.isEmpty(request.getRoomName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMNAME);
        }
        if (request.getProductId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SPPRODUCTID);
        }
        if (StrUtilX.isEmpty(request.getStartDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKINDATE);
        }
        if (StrUtilX.isEmpty(request.getEndDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKOUTDATE);
        }
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        if (StrUtilX.isEmpty(request.getSupplierCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
        }
        if (request.getRoomQty() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMNUM);
        }
        if (request.getPurchaseType() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_PURCHASETYPE);
        }
        if (CollUtilX.isEmpty(request.getRoomGuestList())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMCHECKDETAILS);
        }
        if (CollUtilX.isEmpty(request.getCancelRestrictions())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CANCELRESTRICTIONS);
        }
        if (CollUtilX.isEmpty(request.getRoomItemDetails())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CREATEORDERDETAILS);
        }
    }

    /**
     * 构建产品对象
     */
    private ProductSalePriceDTO buildFinalProduct(AddManualOrderDTO request) {
        ProductSalePriceDTO productSalePriceDTO = new ProductSalePriceDTO();
        productSalePriceDTO.setProductId(request.getProductId());
        productSalePriceDTO.setProductName(request.getProductName());
        productSalePriceDTO.setSupplierCode(request.getSupplierCode());
        productSalePriceDTO.setSupplierName(request.getSupplierName());
        productSalePriceDTO.setCurrency(request.getBaseCurrency());
        productSalePriceDTO.setCancelRestrictions(request.getCancelRestrictions());
        if (CollUtilX.isNotEmpty(request.getCancelRestrictions())) {
            request.getCancelRestrictions().forEach(item -> {
                if (Objects.nonNull(item.getCancelRestrictionDay())) {
                    item.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(request.getStartDate()), -item.getCancelRestrictionDay(), 0)));
                }
                if (item.getCancelPenaltiesType() != null) {
                    StringBuilder stringBuilder = new StringBuilder();
                    if (item.getCancelPenaltiesType().equals(4)) {
                        if (item.getCancelPenaltiesValue().equals(100.0)) {
                            stringBuilder.append("取消或更改,收取首晚房费");
                        } else {
                            stringBuilder.append("取消或更改,扣首晚房费的").append(item.getCancelPenaltiesValue()).append("%，作为退订费。");
                        }
                    } else if (item.getCancelPenaltiesType().equals(3)) {
                        stringBuilder.append("取消或更改,扣全额房费的").append(item.getCancelPenaltiesValue()).append("%，作为退订费");
                    }
                    item.setCancelRestrictionRemark(stringBuilder.toString());
                }
            });
        }
        request.getRoomItemDetails().forEach(item -> item.getPriceInfoDetails().forEach(priceInfoDetail -> {
            com.tiangong.dto.product.TaxDetailDto taxDetail = new com.tiangong.dto.product.TaxDetailDto();
            taxDetail.setSupplyRoomPrice(priceInfoDetail.getBasePrice());
            priceInfoDetail.setTaxDetail(taxDetail);
        }));
        productSalePriceDTO.setRoomItemDetails(request.getRoomItemDetails());
        return productSalePriceDTO;
    }

    /**
     * 处理支付超时自动取消
     */
    private void handlePaymentOvertimeCancel(OrderPO orderPO) {
        try {
            String agentCode = orderPO.getAgentCode();
            // 只处理待支付的订单
            if (orderPO.getPayStatus() != null && orderPO.getPayStatus().equals(PayStatusEnum.NONE.getNo())
                    && orderPO.getOrderConfirmationStatus() != null
                    && orderPO.getOrderConfirmationStatus() == OrderStatusEnum.CONFIRMING.no) {
                // 从Redis获取客户配置
                Object configJson = RedisTemplateX.hashGet(RedisKey.AGENT_PAYMENT_OVERTIME_CONFIG, agentCode);

                if (Objects.nonNull(configJson)) {
                    PaymentOvertimeCancelConfigDTO config = JSONUtil.toBean(JSONUtil.toJsonStr(configJson), PaymentOvertimeCancelConfigDTO.class);

                    // 如果开启了支付超时自动取消
                    if (config.getPaymentOvertimeCancelSwitch() != null && config.getPaymentOvertimeCancelSwitch() == 1
                            && config.getPaymentOvertimeCancelTime() != null && config.getPaymentOvertimeCancelTime() > 0) {

                        // 构建延迟消息
                        PaymentOvertimeCancelMessageDTO message = new PaymentOvertimeCancelMessageDTO();
                        message.setOrderId(String.valueOf(orderPO.getId()));
                        message.setOrderCode(orderPO.getOrderCode());
                        message.setAgentCode(agentCode);
                        message.setCreateTime(System.currentTimeMillis());

                        // 延迟时间转换为毫秒
                        long delayMillis = (long) (config.getPaymentOvertimeCancelTime() * 60 * 60 * 1000);

                        // 发送延迟消息
                        rocketMQTemplate.syncSendDelayTimeMills(
                                MqConstants.ORDER_PAYMENT_OVERTIME_CANCEL_TOPIC,
                                JSONUtil.toJsonStr(message),
                                delayMillis
                        );

                        log.info("发送订单支付超时自动取消延迟消息，orderCode={}，agentCode={}，延迟时间={}小时",
                                orderPO.getOrderCode(), agentCode, config.getPaymentOvertimeCancelTime());
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理订单支付超时自动取消失败，orderCode={}", orderPO.getOrderCode(), e);
        }
    }

    /**
     * 设置订单VIP标识
     *
     * @param orderDTO 订单组装对象
     */
    private void setOrderVipStatus(AssemblyOrderDTO orderDTO) {
        try {
            // 1. 如果订单已经手动标记为VIP，则保持VIP状态
            if (StrUtilX.isNotEmpty(orderDTO.getOrder().getIsVipOrder()) && "1".equals(orderDTO.getOrder().getIsVipOrder())) {
                log.info("订单已手动标记为VIP，保持VIP状态，订单编码: {}", orderDTO.getOrder().getOrderCode());
                return;
            }
            AgentVipClientResultDTO vipClientResultDTO = new AgentVipClientResultDTO();
            if (CollUtilX.isEmpty(orderDTO.getGuestList())) {
                vipClientResultDTO.setAgentCode(orderDTO.getOrder().getAgentCode());
                vipClientResultDTO.setVipName("");

            } else {
                vipClientResultDTO.setAgentCode(orderDTO.getOrder().getAgentCode());
                vipClientResultDTO.setVipName(orderDTO.getGuestList().stream().filter(e->StrUtilX.isNotEmpty(e.getName()))
                        .map(GuestPO::getName).collect(Collectors.joining(",")));
            }
            Response<List<AgentVipClientResultDTO>> listResponse = vipClientRemote.vipSearch(Collections.singletonList(vipClientResultDTO));
            if(listResponse!=null&&listResponse.isSuccess()){
                if (listResponse.getModel().get(0).getIsVip() != null && listResponse.getModel().get(0).getIsVip() == 1) {
                    orderDTO.getOrder().setIsVipOrder("1");
                }
                return;
            }
            orderDTO.getOrder().setIsVipOrder("0");
        } catch (Exception e) {
            orderDTO.getOrder().setIsVipOrder("0");
            log.error("设置VIP订单标识失败，订单编码: {}", orderDTO.getOrder().getOrderCode(), e);
        }
    }

    /**
     * 设置客人 VIP 等级
     *
     * @param orderDTO 订单组装对象
     */
    private void setGuestVipLevels(AssemblyOrderDTO orderDTO) {
        try {
            // 检查客人列表是否为空
            if (CollUtilX.isEmpty(orderDTO.getGuestList())) {
                log.info("订单无客人信息，跳过VIP等级设置，订单编码: {}", orderDTO.getOrder().getOrderCode());
                return;
            }
            // 构建VIP等级查询请求参数
            List<AgentVipClientLevelResultDTO> vipLevelQueryList = orderDTO.getGuestList().stream()
                    .filter(guest -> StrUtilX.isNotEmpty(guest.getName())) // 过滤空姓名
                    .map(guest -> {
                        AgentVipClientLevelResultDTO vipClientLevelResultDTO = new AgentVipClientLevelResultDTO();
                        vipClientLevelResultDTO.setAgentCode(orderDTO.getOrder().getAgentCode());
                        vipClientLevelResultDTO.setVipName(guest.getName());
                        return vipClientLevelResultDTO;
                    })
                    .collect(Collectors.toList());

            if (CollUtilX.isEmpty(vipLevelQueryList)) {
                log.info("没有有效的客人姓名，跳过VIP等级设置，订单编码: {}", orderDTO.getOrder().getOrderCode());
                return;
            }

            // 调用VIP等级查询服务
            Response<List<AgentVipClientLevelResultDTO>> vipLevelResponse = vipClientRemote.vipLevelSearch(vipLevelQueryList);

            if (vipLevelResponse == null || !vipLevelResponse.isSuccess()) {
                log.warn("VIP等级查询服务调用失败，订单编码: {}, 响应: {}",
                        orderDTO.getOrder().getOrderCode(), vipLevelResponse);
                return;
            }

            List<AgentVipClientLevelResultDTO> vipLevelResults = vipLevelResponse.getModel();
            if (CollUtilX.isEmpty(vipLevelResults)) {
                log.info("VIP等级查询结果为空，订单编码: {}", orderDTO.getOrder().getOrderCode());
                return;
            }

            // 构建VIP等级映射：客户编码+客人姓名 -> VIP等级
            Map<String, Integer> vipLevelMap = vipLevelResults.stream()
                    .filter(vip -> vip.getLevel() != null) // 只处理有等级的VIP
                    .collect(Collectors.toMap(
                            vip -> buildVipKey(vip.getAgentCode(), vip.getVipName()),
                            AgentVipClientLevelResultDTO::getLevel,
                            (oldVal, newVal) -> newVal // 如果有重复 key，使用新值
                    ));

            log.info("构建VIP等级映射完成，订单编码: {}, VIP数量: {}",
                    orderDTO.getOrder().getOrderCode(), vipLevelMap.size());

            // 为客人设置VIP等级
            for (GuestPO guest : orderDTO.getGuestList()) {
                if (StrUtilX.isEmpty(guest.getName())) {
                    guest.setVipLevel(null);
                    continue;
                }

                String vipKey = buildVipKey(orderDTO.getOrder().getAgentCode(), guest.getName());
                Integer vipLevel = vipLevelMap.get(vipKey);

                guest.setVipLevel(vipLevel);

                if (vipLevel != null) {
                    log.info("设置客人 VIP 等级: 姓名={}, VIP等级={}", guest.getName(), vipLevel);
                }
            }

        } catch (Exception e) {
            log.error("设置客人 VIP 等级失败，订单编码: {}", orderDTO.getOrder().getOrderCode(), e);
            // 异常情况下不影响订单创建，只记录日志
        }
    }

    /**
     * 构建VIP映射的唯一键
     *
     * @param agentCode 客户编码
     * @param guestName 客人姓名
     * @return 唯一键
     */
    private String buildVipKey(String agentCode, String guestName) {
        return agentCode + "_" + (guestName != null ? guestName.trim() : "");
    }
}
