package com.tiangong.order.server;

import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.BaseRequest;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.order.request.OrderCheckInfoRequest;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.remote.HotelHeatRemote;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.domain.OrderCheckPushInfoPO;
import com.tiangong.order.domain.OrderRequestPO;
import com.tiangong.order.dto.OrderCheckPushConfigDTO;
import com.tiangong.order.dto.OrderCheckPushInfoDTO;
import com.tiangong.order.mapper.OrderRequestMapper;
import com.tiangong.order.remote.dto.*;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.AdditionalChargesQueryResponseDTO;
import com.tiangong.order.remote.response.OrderInvoiceDTO;
import com.tiangong.order.service.HotelOrderCheckPushService;
import com.tiangong.order.service.OrderCheckInfoService;
import com.tiangong.order.service.OrderService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@RestController
@Slf4j
public class OrderServer extends BaseController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderRequestMapper orderRequestMapper;

    @Autowired
    private OrderCheckInfoService orderCheckInfoService;

    @Autowired
    private HotelOrderCheckPushService paHotelOrderCheckPushService;

    @Autowired
    private HotelHeatRemote hotelHeatRemote;

    /**
     * 确认订单
     */
    @PostMapping("/order/confirmOrder")
    @PreAuthorize("@syyo.check('order')")
    @SlsLog(level = "info", name = "查询", message = "confirmOrder", topic = "/order/confirmOrder", source = "tiangong-order-server")
    public Response<Object> confirmOrder(@RequestBody ConfirmOrderDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        request.setCompanyCode(getCompanyCode());
        return orderService.confirmOrder(request);
    }

    /**
     * 取消订单
     */
    @PostMapping("/order/cancelOrder")
    @PreAuthorize("@syyo.check('order')")
    @SlsLog(level = "info", name = "查询", message = "cancelOrder", topic = "/order/cancelOrder", source = "tiangong-order-server")
    public Response<Object> cancelOrder(@RequestBody CancelOrderDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.cancelOrder(request, 1, true);
        return Response.success();
    }

    /**
     * 推送订单状态
     */
    @PostMapping("/order/pushOrderStatus")
    @PreAuthorize("@syyo.check('order')")
    @SlsLog(level = "info", name = "查询", message = "pushOrderStatus", topic = "/order/pushOrderStatus", source = "tiangong-order-server")
    public Response<Object> pushOrderStatus(@RequestBody PushOrderStatusDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        return orderService.pushOrderStatus(request);
    }

    /**
     * 订单取消申请
     */
    @PostMapping("/order/addOrderRequest")
    @PreAuthorize("@syyo.check('order')")
    @SlsLog(level = "info", name = "查询", message = "addOrderRequest", topic = "/order/addOrderRequest", source = "tiangong-order-server")
    public Response<Object> addOrderRequest(@RequestBody AddOrderRequestDTO request) {
        if (StrUtilX.isEmpty(request.getRequestId())) {
            request.setRequestId(UUID.randomUUID().toString());
        }
        return orderService.addOrderRequest(request);
    }

    /**
     * 修改入住人
     */
    @PostMapping("/order/modifyGuest")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyGuest(@RequestBody OrderGuestDTO request) {
        request.setUpdatedBy(super.getUserName());
        orderService.modifyGuest(request);
        return Response.success();
    }

    /**
     * 修改房型
     */
    @PostMapping("/order/modifyRoom")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyRoom(@RequestBody ModifyRoomDTO request) {
        request.setOperator(getUserName());
        request.setLoginName(getLoginName());
        orderService.modifyRoom(request);
        return Response.success();
    }

    /**
     * 修改特殊要求
     */
    @PostMapping("/order/modifySpecialRequirement")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifySpecialRequirement(@RequestBody ModifySpecialRequirementDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.modifySpecialRequirement(request);
        return Response.success();
    }

    /**
     * 修改售价
     */
    @PostMapping("/order/modifySalePrice")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifySalePrice(@RequestBody ModifySalePriceDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.modifySalePrice(request);
        return Response.success();
    }

    /**
     * 修改房费明细
     */
    @PostMapping("/order/modifyOrderRoomPrice")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyOrderRoomPrice(@RequestBody ModifyRoomPriceDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.modifyOrderRoomPrice(request);
        return Response.success();
    }

    /**
     * 修改售价
     */
    @PostMapping("/order/modifyRefundFee")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyRefundFee(@RequestBody ModifyRefundFeeDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.modifyRefundFee(request);
        return Response.success();
    }

    /**
     * 添加备注
     */
    @PostMapping("/order/addRemark")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> addRemark(@RequestBody AddRemarkDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.addRemark(request);
        return Response.success();
    }

    /**
     * 上传附件
     */
    @PostMapping("/order/saveOrderAttachment")
    @AnonymousAccess
    public Response<Object> saveOrderAttachment(@RequestBody SaveOrderAttachmentDTO saveOrderAttachmentDTO) {
        if (StrUtilX.isEmpty(saveOrderAttachmentDTO.getOperator())) {
            saveOrderAttachmentDTO.setOperator(getUserName());
            saveOrderAttachmentDTO.setOrderOwnerName(super.getLoginName());
        }
        orderService.saveOrderAttachment(saveOrderAttachmentDTO);
        return Response.success();
    }

    /**
     * 删除附件
     */
    @PostMapping("/order/deleteOrderAttachment")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> deleteOrderAttachment(@RequestBody OrderAttachmentIdDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.deleteOrderAttachment(request);
        return Response.success();
    }

    /**
     * 删除申请
     */
    @PostMapping("/order/removeOrderRequest")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> removeOrderRequest(@RequestBody HandleOrderRequestDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        Example examples = new Example(OrderRequestPO.class);
        examples.setOrderByClause("created_dt DESC");
        Example.Criteria criterias = examples.createCriteria();
        criterias.andEqualTo("orderId", request.getOrderId());
        List<OrderRequestPO> orderRequestPOs = orderRequestMapper.selectByExample(examples);
        orderService.removeOrderRequest(request, orderRequestPOs);
        return Response.success();
    }

    /**
     * 修改订单房型
     */
    @PostMapping("/order/modifyOrderRoom")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyOrderRoom(@RequestBody ModifyOrderRoomDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        request.setLanguage(getLanguage());
        orderService.modifyOrderRoom(request);
        return Response.success();
    }

    /**
     * 修改订单结算方式
     */
    @PostMapping("/order/modifyOrderSettlementType")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyOrderSettlementType(@RequestBody ModifyOrderSettlementTypeDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.modifyOrderSettlementType(request);
        return Response.success();
    }

    /**
     * 解锁订单
     */
    @PostMapping("/order/lockOrder")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> lockOrder(@RequestBody LockOrderDTO request) {
        request.setOperator(super.getUserName());
        request.setOperatorUser(getLoginName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.lockOrder(request);
        return Response.success();
    }

    /**
     * 标记订单
     */
    @PostMapping("/order/markOrder")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> markOrder(@RequestBody MarkOrderDTO request) {
        request.setOperator(super.getUserName());
        request.setOrderOwnerName(super.getLoginName());
        orderService.markOrder(request);
        return Response.success();
    }

    /**
     * 标记订单
     */
    @PostMapping("/order/modifyChannelOrderCode")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyChannelOrderCode(@RequestBody Map<String, String> request) {
        request.put("operator", super.getUserName());
        request.put("orderOwnerName", super.getLoginName());
        orderService.modifyChannelOrderCode(request);
        return Response.success();
    }

    /**
     * 订单自动发单列表
     */
    @PostMapping("/order/orderAutomaticList")
    @PreAuthorize("@syyo.check('order')")
    public Response<PaginationSupportDTO<SupplierListDTO>> orderAutomaticList(@RequestBody Map<String, String> requestMap) {
        if (requestMap != null) {
            if (StrUtilX.isEmpty(requestMap.get("pageSize"))) {
                requestMap.put("pageSize", String.valueOf(new BaseRequest().getPageSize()));
            }
            if (StrUtilX.isEmpty(requestMap.get("currentPage"))) {
                requestMap.put("currentPage", String.valueOf(new BaseRequest().getCurrentPage()));
            }
        } else {
            requestMap = new HashMap<>();
            requestMap.put("pageSize", "10");
            requestMap.put("currentPage", "1");
        }
        requestMap.put("companyCode", super.getCompanyCode());
        return orderService.orderAutomaticList(requestMap);
    }

    /**
     * 添加自动发单渠道及时间
     */
    @PostMapping("/order/addSupplierAutoChannel")
    @AnonymousAccess
    public Response<Object> addSupplierAutoChannel(@RequestBody AddSupplierAutoChannelDTO channelDTO) {
        channelDTO.setCompanyCode(getCompanyCode());
        orderService.addOrUpdateSupplierAutoChannel(channelDTO);
        return Response.success();
    }

    /**
     * 查询自动发单渠道及时间
     */
    @PostMapping("/order/querySupplierAutoChannel")
    @PreAuthorize("@syyo.check('order')")
    public Response<SupplierAutoChannelDto> querySupplierAutoChannel(@RequestBody SupplierAutoChannelDto supplierAutoChannelDto) {
        return orderService.querySupplierAutoChannel(supplierAutoChannelDto);
    }

    /**
     * 查询自动确认设置
     */
    @RequestMapping("/order/queryAutoConfirmChannel")
    @PreAuthorize("@syyo.check('order')")
    public Response<QueryAutoConfirmChannelDto> queryAutoConfirmChannel() {
        Map<String, String> request = new HashMap<>();
        request.put("companyCode", super.getCompanyCode());
        return orderService.queryAutoConfirmChannel(request);
    }

    /**
     * 查询自动取消设置
     */
    @RequestMapping("/order/queryAutoCancel")
    @PreAuthorize("@syyo.check('order')")
    Response<OrderAutoCancelDTO> queryAutoCancel() {
        Map<String, String> request = new HashMap<>();
        request.put("companyCode", super.getCompanyCode());
        return orderService.queryAutoCancel(request);
    }

    /**
     * 新增修改自动确认设置
     */
    @RequestMapping(value = "/order/modifyAutoConfirmChannel")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyAutoConfirmChannel(@RequestBody QueryAutoConfirmChannelDto autoConfirmChannelDto) {
        autoConfirmChannelDto.setCompanyCode(super.getCompanyCode());
        if (autoConfirmChannelDto.getId() != null) {
            orderService.modifyAutoConfirmChannel(autoConfirmChannelDto);
        } else {
            orderService.addAutoConfirmChannel(autoConfirmChannelDto);
        }
        return Response.success();
    }

    /**
     * 修改自动取消设置
     */
    @RequestMapping(value = "/order/modifyAutoCancel")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyAutoCancel(@RequestBody OrderAutoCancelDTO orderAutoCancelDTO) {
        orderAutoCancelDTO.setCompanyCode(super.getCompanyCode());
        orderService.modifyAutoCancel(orderAutoCancelDTO);
        return Response.success();
    }

    /**
     * 修改发票信息
     */
    @RequestMapping(value = "/order/modifyOrderInvoice", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('order')")
    @AnonymousAccess
    public Response<OrderInvoiceDTO> modifyOrderInvoice(@RequestBody OrderInvoiceDTO orderInvoiceDTO) throws Exception {
        orderInvoiceDTO.setOperator(getUserName());
        orderService.modifyOrderInvoice(orderInvoiceDTO);
        return Response.success(orderInvoiceDTO);
    }

    @PostMapping("/order/applyOrderInvoice")
    @AnonymousAccess
    Response<OrderInvoiceDTO> applyOrderInvoice(@RequestBody OrderInvoiceDTO orderInvoiceDTO) throws Exception {
        orderService.modifyOrderInvoice(orderInvoiceDTO);
        return Response.success(orderInvoiceDTO);
    }

    /**
     * 新增修改订单入住明细
     */
    @RequestMapping(value = "/order/saveOrUpdateOrderCheckInfo")
    @PreAuthorize("@syyo.check('order')")
    @SlsLog(level = "info", name = "查询", message = "saveOrUpdateOrderCheckInfo", topic = "/order/saveOrUpdateOrderCheckInfo", source = "tiangong-order-server")
    public Response<Object> saveOrUpdateOrderCheckInfo(@RequestBody List<OrderCheckInfoDTO> orderCheckInfoDTOList) {
        boolean paramFlag = false;
        if (CollUtilX.isNotEmpty(orderCheckInfoDTOList)) {
            for (OrderCheckInfoDTO orderCheckInfoDTO : orderCheckInfoDTOList) {
                paramFlag = null != orderCheckInfoDTO.getCheckInState()
                        && null != orderCheckInfoDTO.getOrderCode()
                        && null != orderCheckInfoDTO.getStartDate()
                        && null != orderCheckInfoDTO.getEndDate()
                        && null != orderCheckInfoDTO.getGuestName()
                        && CollUtilX.isNotEmpty(orderCheckInfoDTO.getCheckInDetailList())
                        && orderCheckInfoDTO.getCheckInDetailList().get(0).getSaleDate() != null;
            }
        }
        if (paramFlag) {
            int i = orderCheckInfoService.saveOrUpdateOrderCheckInfo(orderCheckInfoDTOList, super.getUserName(), false);
            if (i == -1) {
                throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
            }
        } else {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        return Response.success();
    }

    /**
     * 查询入职明细详情
     */
    @AnonymousAccess
    @PostMapping("/order/queryOrderCheckDetailInfo")
    public Response<List<OrderCheckInfoDTO>> queryOrderCheckDetailInfo(@RequestParam String orderCode) {
        List<OrderCheckInfoDTO> orderCheckInfoDTOList = orderCheckInfoService.queryOrderCheckDetail(orderCode);
        return Response.success(orderCheckInfoDTOList);
    }

    /**
     * 查询入职明细详情
     */
    @AnonymousAccess
    @PostMapping("/order/queryOrderCheckDetailInfoDhub")
    public Response<List<OrderCheckInfoDTO>> queryOrderCheckDetailInfo(@RequestBody OrderCheckInfoRequest orderCheckInfoRequest) {
        List<OrderCheckInfoDTO> orderCheckInfoDTOList = orderCheckInfoService.queryOrderCheckDetailDhub(orderCheckInfoRequest);
        return Response.success(orderCheckInfoDTOList);
    }

    /**
     * 删除订单附件(逻辑删除)
     */
    @RequestMapping(value = "/order/deleteOrderAttachmentByLogic", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<Object> deleteOrderAttachmentByLogic(@RequestBody DeleteOrderAttachmentByLogicDTO deleteOrderAttachmentByLogicDTO) {
        orderService.deleteOrderAttachmentByLogic(deleteOrderAttachmentByLogicDTO);
        return Response.success();
    }

    /**
     * 修改订单附加项费用
     */
    @PostMapping("/order/additionalChargesEdit")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> additionalChargesEdit(@RequestBody List<AdditionalChargesEditChangeDTO> additionalChargesEditChangeDTO) {
        if (CollUtilX.isEmpty(additionalChargesEditChangeDTO) || additionalChargesEditChangeDTO.get(0) == null
                || CollUtilX.isEmpty(additionalChargesEditChangeDTO.get(0).getAdditionalChargesItemList())
                || additionalChargesEditChangeDTO.get(0).getAdditionalChargesItemList().get(0) == null
                || CollUtilX.isEmpty(additionalChargesEditChangeDTO.get(0).getAdditionalChargesItemList().get(0).getAdditionalChargesDateItem())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else {
            //将数据进行转换
            List<AdditionalChargesEditItemDTO> additionalChargesEditItemDTOList = additionalChargesEditChangeDTO.get(0).getAdditionalChargesItemList();
            List<AdditionalChargesEditDTO> additionalChargesEditList = new ArrayList<>();
            AdditionalChargesEditDTO additionalChargesEditDTO = new AdditionalChargesEditDTO();
            List<SupplyOrderAdditionalChargesEditDTO> supplyOrderAdditionalChargesList = new ArrayList<>();
            for (AdditionalChargesEditItemDTO additionalChargesEditItemDTO : additionalChargesEditItemDTOList) {
                AdditionalChargesEditDateItemDTO additionalChargesEditDateItemDTOTmp = additionalChargesEditItemDTO.getAdditionalChargesDateItem().get(0);
                if (additionalChargesEditItemDTO.getAdditionalChargesOrderType() == 0) {//订单
                    additionalChargesEditDTO.setOrderAdditionalCharges(OrderAdditionalChargesEditDTO.builder()
                            .id(additionalChargesEditDateItemDTOTmp.getId())
                            .additionalChargesType(additionalChargesEditDateItemDTOTmp.getAdditionalChargesType())
                            .additionalChargesName(additionalChargesEditDateItemDTOTmp.getAdditionalChargesName())
                            .orderId(additionalChargesEditDateItemDTOTmp.getOrderId())
                            .additionalChargesDate(additionalChargesEditDateItemDTOTmp.getAdditionalChargesDate())
                            .quantity(additionalChargesEditDateItemDTOTmp.getQuantity())
                            .additionalCharges(additionalChargesEditDateItemDTOTmp.getAdditionalCharges()).build());
                } else if (additionalChargesEditItemDTO.getAdditionalChargesOrderType() == 1) {//供货单
                    supplyOrderAdditionalChargesList.add(SupplyOrderAdditionalChargesEditDTO.builder()
                            .id(additionalChargesEditDateItemDTOTmp.getId())
                            .additionalChargesType(additionalChargesEditDateItemDTOTmp.getAdditionalChargesType())
                            .additionalChargesName(additionalChargesEditDateItemDTOTmp.getAdditionalChargesName())
                            .orderId(additionalChargesEditDateItemDTOTmp.getOrderId())
                            .additionalChargesDate(additionalChargesEditDateItemDTOTmp.getAdditionalChargesDate())
                            .quantity(additionalChargesEditDateItemDTOTmp.getQuantity())
                            .additionalCharges(additionalChargesEditDateItemDTOTmp.getAdditionalCharges()).build());
                }
            }
            additionalChargesEditDTO.setSupplyOrderAdditionalChargesList(supplyOrderAdditionalChargesList);
            additionalChargesEditList.add(additionalChargesEditDTO);
            orderService.additionalChargesEdit(additionalChargesEditList, super.getUserName(), super.getLoginName());
        }
        return Response.success();
    }

    /**
     * 查询附加项
     */
    @PostMapping("/order/additionalChargesQuery")
    @PreAuthorize("@syyo.check('order')")
    public Response<List<AdditionalChargesQueryResponseDTO>> additionalChargesQuery(@RequestBody AdditionalChargesQueryDTO additionalChargesQueryDTO) {
        return orderService.additionalChargesQuery(additionalChargesQueryDTO);
    }

    /**
     * 根据订单号修改订单的支付状态
     */
    @AnonymousAccess
    @PostMapping("/order/updatePayStatusByOrderCode")
    public Response<Object> updatePayStatusByOrderCode(@RequestBody UpdatePayStatusByOrderCodeRequest request) {
        orderService.updatePayStatusByOrderCode(request);
        return Response.success();
    }

    /**
     * 查询入住明细分页
     */
    @PostMapping("/order/queryOrderCheckPushInfoPage")
    @PreAuthorize("@syyo.check('order')")
    public Response<PaginationSupportDTO<OrderCheckPushInfoPO>> queryOrderCheckPushInfoPage(@RequestBody OrderCheckPushInfoDTO request) {
        return Response.success(orderCheckInfoService.queryOrderCheckPushInfoPage(request));
    }


    /**
     * 重新推送入住明细
     */
    @PostMapping("/order/rePushOrderCheckPushInfo")
    @PreAuthorize("@syyo.check('order')")
    public Response<Long> rePushOrderCheckPushInfo(@RequestBody OrderCheckPushInfoDTO request) {
        return paHotelOrderCheckPushService.rePushOrderCheckPushInfo(request.getOrderCode());
    }


    /**
     * 查询渠道信息列表
     */
    @PostMapping("/order/queryChannelInfoList")
    @PreAuthorize("@syyo.check('order')")
    public Response<List<OrderCheckPushConfigDTO>> queryChannelInfoList() {
        return paHotelOrderCheckPushService.queryChannelInfoList();
    }

    /**
     * 修改订单担保状态
     */
    @PostMapping("/order/modifyOrderGuaranteeStatus")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> modifyOrderGuaranteeStatus(@RequestBody ModifyOrderGuaranteeStatusDTO request) {
        request.setOperator(getUserName());
        orderService.modifyOrderGuaranteeStatus(request);
        return Response.success();
    }

    /**
     * 计算酒店热度预订间夜分数任务
     */
    @PostMapping("/order/calculateHotelHeatRoomNightScoreTask")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "calculateHotelHeatRoomNightScoreTask", topic = "/order/calculateHotelHeatRoomNightScoreTask", source = "tiangong-order-server")
    public Response<Object> calculateHotelHeatRoomNightScoreTask(@RequestBody Map<String, String> paramMap) {
        orderService.calculateHotelHeatRoomNightScoreTask(paramMap.get("hotelIds"));
        return Response.success();
    }

    /**
     * 初始化需要计算酒店热度预订间夜分数的酒店id到缓存任务
     */
    @PostMapping("/order/initCalculateRoomNightScoreHotelIdToRedisTask")
    @AnonymousAccess
    public Response<Object> initCalculateRoomNightScoreHotelIdToRedisTask() {
        ExecutorService executor = null;
        try {
            XxlJobHelper.log("执行初始化需要计算酒店热度预订间夜分数的酒店id到缓存任务开始");

            // 配置参数
            final int pageSize = 2000;
            final int maxConcurrency = 8;
            final AtomicLong lastId = new AtomicLong(0);
            final RateLimiter rateLimiter = RateLimiter.create(5);
            executor = Executors.newFixedThreadPool(maxConcurrency);

            // 分页循环
            while (true) {
                rateLimiter.acquire();

                // 分页查询
                HotelHeatReq req = new HotelHeatReq();
                req.setLastId(lastId.get());
                req.setBatchSize(pageSize);

                Response<List<String>> response = hotelHeatRemote.queryAllHotelHeatHotelIdPage(req);

                // 错误处理
                if (response.isError()) {
                    log.error("分页查询失败 lastId={}, error={}", lastId, response.getFailReason());
                    XxlJobHelper.log("分页查询失败: " + response.getFailReason());
                    break;
                }

                List<String> hotelIds = response.getModel();
                if (CollUtilX.isEmpty(hotelIds)) break;

                // 提交处理
                executor.submit(() -> processWithRetry(hotelIds));

                // 更新游标
                updateLastIdAtomically(hotelIds, lastId);
            }

            // 等待完成
            executor.shutdown();
            while (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
                XxlJobHelper.log("等待线程池关闭...");
            }
            XxlJobHelper.log("执行初始化需要计算酒店热度预订间夜分数的酒店id到缓存任务结束，最终lastId=" + lastId.get());
        } catch (Exception e) {
            log.error("执行初始化需要计算酒店热度预订间夜分数的酒店id到缓存任务异常", e);
            XxlJobHelper.log("执行初始化需要计算酒店热度预订间夜分数的酒店id到缓存任务异常", e);
        } finally {
            if (executor != null) executor.shutdownNow();
        }
        return Response.success();
    }

    /**
     * 更新lastId
     */
    private void updateLastIdAtomically(List<String> hotelIds, AtomicLong lastId) {
        hotelIds.stream()
                .mapToLong(Long::parseLong)
                .max()
                .ifPresent(max -> {
                    long current;
                    do {
                        current = lastId.get();
                    } while (max > current && !lastId.compareAndSet(current, max));
                });
    }

    /**
     * 处理数据
     */
    private void processWithRetry(List<String> hotelIds) {
        try (RedisConnection conn = RedisTemplateX.getConnectionFactory()) {
            conn.openPipeline();
            byte[] key = RedisKey.CALCULATE_ROOM_NIGHT_SCORE_HOTEL_ID_KEY.getBytes();

            hotelIds.stream()
                    .map(String::getBytes)
                    .forEach(bytes -> conn.sAdd(key, bytes));

            conn.closePipeline();
        } catch (Exception e) {
            log.error("Redis批量写入失败", e);
        }
    }

    /**
     * 新增订单支付明细
     */
    @PostMapping("/order/addOrderPayDetailList")
    @AnonymousAccess
    public Response<Object> addOrderPayDetailList(@RequestBody AddOrderPayDTO request) {
        orderService.addOrderPayDetailList(request);
        return Response.success();
    }

    /**
     * 修改附加费
     */
    @PostMapping("/order/modifyAdditionalCharges")
    @AnonymousAccess
    public Response<Object> modifyAdditionalCharges(@RequestBody List<AdditionalChargesDTO> dtoList) {
        orderService.modifyAdditionalCharges(dtoList);
        return Response.success();
    }

    /**
     * 发送订单确认函
     *
     * @param req
     * @return
     */
    @PostMapping("/order/sendOrderConfirmationLetter")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> sendOrderConfirmationLetter(@RequestBody @Validated SendOrderConfirmationLetterReqDTO req) {
        return orderService.sendOrderConfirmationLetter(req);
    }

    /**
     * 导出订单确认含
     *
     * @param req
     * @return
     */
    @PostMapping("/order/orderConfirmationLetterExport")
    public void orderConfirmationLetterExport(@RequestBody SendOrderConfirmationLetterReqDTO req, HttpServletResponse resp) {
        orderService.orderConfirmationLetterExport(req, resp);
    }
}
