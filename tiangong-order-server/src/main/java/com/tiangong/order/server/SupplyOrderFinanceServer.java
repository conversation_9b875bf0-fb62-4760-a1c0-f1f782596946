package com.tiangong.order.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.GuestPO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.GuestDTO;
import com.tiangong.order.remote.response.OnTimeSupplyOrderDTO;
import com.tiangong.order.remote.response.SupplyOrderFinanceDTO;
import com.tiangong.order.service.SupplyOrderFinanceService;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class SupplyOrderFinanceServer extends BaseController {

    @Autowired
    private SupplyOrderFinanceService supplyOrderFinanceService;

    /**
     * 单结订单查询
     */
    @PostMapping("/order/finance/queryOnTimeSupplyOrderList")
    @PreAuthorize("@syyo.check('order')")
    public Response<PaginationSupportDTO<OnTimeSupplyOrderDTO>> queryOnTimeSupplyOrderList(@RequestBody QueryOnTimeSupplyOrderListDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        if (StrUtilX.isNotEmpty(request.getSettlementStatus()) && request.getSettlementStatus().equals("-1")) {
            request.setSettlementStatus(null);
        }

        if (StrUtilX.isNotEmpty(request.getOverdueStatus()) && request.getOverdueStatus().equals("-1")) {
            request.setOverdueStatus(null);
        }
        return Response.success(supplyOrderFinanceService.queryOnTimeSupplyOrderList(request));
    }

    /**
     * 通知收款
     */
    @PostMapping("/order/finance/notifyCollectionOfSupplyOrder")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> notifyCollectionOfSupplyOrder(@RequestBody NotifyCollectionOfSupplyOrderDTO request) {
        request.setOperator(super.getUserName());
        return supplyOrderFinanceService.notifyCollectionOfSupplyOrder(request);
    }

    /**
     * 通知付款
     */
    @PostMapping("/order/finance/notifyPaymentOfSupplyOrder")
    @PreAuthorize("@syyo.check('order')")
    public Response<Integer> notifyPaymentOfSupplyOrder(@RequestBody NotifyPaymentOfSupplyOrderDTO request) {
        request.setOperator(super.getUserName());
        return supplyOrderFinanceService.notifyPaymentOfSupplyOrder(request);
    }

    /**
     * 合并通知财务收款
     */
    @PostMapping("/order/finance/notifyCollectionOfSupplyOrderList")
    @PreAuthorize("@syyo.check('order')")
    public Response<Object> notifyCollectionOfSupplyOrderList(@RequestBody NotifyCollectionOfSupplyOrderListDTO request) {
        request.setOperator(super.getUserName());
        return supplyOrderFinanceService.notifyCollectionOfSupplyOrderList(request);
    }

    /**
     * 合并通知财务付款
     */
    @PostMapping("/order/finance/notifyPaymentOfSupplyOrderList")
    @PreAuthorize("@syyo.check('order')")
    public Response<Integer> notifyPaymentOfSupplyOrderList(@RequestBody NotifyPaymentOfSupplyOrderListDTO request) {
        request.setOperator(super.getUserName());
        return supplyOrderFinanceService.notifyPaymentOfSupplyOrderList(request);
    }

    /**
     * 合并通知财务收款预览
     */
    @PostMapping("/order/finance/notifyCollectionPreviewOfSupplyOrderList")
    @PreAuthorize("@syyo.check('order')")
    public Response<BigDecimal> notifyCollectionPreviewOfSupplyOrderList(@RequestBody SupplyOrderIdListDTO request) {
        return supplyOrderFinanceService.notifyCollectionPreviewOfSupplyOrderList(request);
    }

    /**
     * 合并通知财务付款预览
     */
    @PostMapping("/order/finance/notifyPaymentPreviewOfSupplyOrderList")
    @PreAuthorize("@syyo.check('order')")
    public Response<BigDecimal> notifyPaymentPreviewOfSupplyOrderList(@RequestBody SupplyOrderIdListDTO request) {
        return supplyOrderFinanceService.notifyPaymentPreviewOfSupplyOrderList(request);
    }

    /**
     * 查询供货单财务相关信息【提供自助结算模块查询】
     */
    @PostMapping("/order/finance/querySupplyOrderFinanceInfo")
    @AnonymousAccess
    public Response<List<SupplyOrderFinanceDTO>> querySupplyOrderFinanceInfo(@RequestBody SupplyOrderInfoListDTO request) {
        return Response.success(supplyOrderFinanceService.querySupplyOrderFinanceInfo(request));
    }

    /**
     * 根据订单id查询入住人详细信息
     */
    @PostMapping("/order/finance/queryGuestNameList")
    @AnonymousAccess
    public Response<List<GuestDTO>> queryGuestNameList(@RequestParam("orderIds") List<Integer> orderIds){
        List<GuestPO> list = supplyOrderFinanceService.queryGuestNameList(orderIds);
        List<GuestDTO> collect = list.stream().map(ProductSalePriceConvert.INSTANCE::guestDTOConvert).collect(Collectors.toList());
        return Response.success(collect);
    }
}
