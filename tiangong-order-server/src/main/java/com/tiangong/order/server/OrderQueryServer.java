package com.tiangong.order.server;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.QueryOrderStatisticsDTOB2BRequest;
import com.tiangong.dto.order.response.OrderStatisticsDTOB2BResponse;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.*;
import com.tiangong.order.service.OrderQueryService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
public class OrderQueryServer extends BaseController {

    @Autowired
    private OrderQueryService orderQueryService;

    /**
     * 查询订单列表
     */
    @PostMapping("/order/queryOrderList")
    @PreAuthorize("@syyo.check('order')")
    @AnonymousAccess
    public Response<PaginationSupportDTO<OrderSimpleDTO>> queryOrderList(@RequestBody QueryOrderListDTO request) {
        try {
            if (null != request.getChannelCode() && request.getChannelCode().equals("All")) {
                request.setChannelCode(null);
            }

            if (null != request.getOrderSettlementType() && request.getOrderSettlementType().equals(-1)) {
                request.setOrderSettlementType(null);
            }

            if (null != request.getMarkedStatus() && request.getMarkedStatus().equals(-1)) {
                request.setMarkedStatus(null);
            }
            if (null != request.getOrderConfirmationStatus() && request.getOrderConfirmationStatus().equals(-1)) {
                request.setOrderConfirmationStatus(null);
            }
            if (null != request.getSupplyOrderConfirmationStatus() && request.getSupplyOrderConfirmationStatus().equals(-1)) {
                request.setSupplyOrderConfirmationStatus(null);
            }
            if (null != request.getOrderSettlementStatus() && request.getOrderSettlementStatus().equals(-1)) {
                request.setOrderSettlementStatus(null);
            }
            if (null != request.getSupplyOrderSettlementType() && request.getSupplyOrderSettlementType().equals(-1)) {
                request.setSupplyOrderSettlementType(null);
            }
            if (null != request.getSupplyOrderSettlementStatus() && request.getSupplyOrderSettlementStatus().equals(-1)) {
                request.setSupplyOrderSettlementStatus(null);
            }
            if (null != request.getInvoiceStatus() && request.getInvoiceStatus().equals(-1)) {
                request.setInvoiceStatus(null);
            }
            if (null != request.getDateQueryType() && request.getDateQueryType().equals(0) && StrUtilX.isNotEmpty(request.getEndDate())) {
                request.setEndDate(DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(request.getEndDate()), 1)));
            }
            if (null != request.getProductType() && request.getProductType().equals(-1)) {
                request.setProductType(null);
            }

            // 于250623-V1.3版本去除，新增根据预定人查询
            //request.setOperator(request.getCreatedBy());
            request.setCompanyCode(getCompanyCode());
            PaginationSupportDTO<OrderSimpleDTO> paginationSupportDTO = orderQueryService.queryOrderList(request);
            return Response.success(paginationSupportDTO);
        } catch (Exception e) {
            log.error("queryOrderList server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单列表统计
     */
    @PostMapping("/order/queryOrderStatistics")
    @PreAuthorize("@syyo.check('order')")
    public Response<OrderStatisticsDTO> queryOrderStatistics() {
        try {
            QueryOrderStatisticsDTO request = new QueryOrderStatisticsDTO();
            request.setOperator(getLoginName());
            request.setCompanyCode(getCompanyCode());
            OrderStatisticsDTO queryOrderStatistics = orderQueryService.queryOrderStatistics(request);
            return Response.success(queryOrderStatistics);
        } catch (Exception e) {
            log.error("queryOrderStatistics server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单列表统计--B2B
     */
    @PostMapping("/order/queryOrderStatisticsB2B")
    @PreAuthorize("@syyo.check('order')")
    public Response<OrderStatisticsDTOB2BResponse> queryOrderStatisticsB2B(@RequestBody QueryOrderStatisticsDTOB2BRequest request) {
        try {
            if (null != request.getDateQueryType() && request.getDateQueryType().equals(0) && StrUtilX.isNotEmpty(request.getEndDate())) {
                request.setEndDate(DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(request.getEndDate()), 1)));
            }
            OrderStatisticsDTOB2BResponse queryOrderStatistics = orderQueryService.queryOrderStatisticsB2B(request);
            return Response.success(queryOrderStatistics);
        } catch (Exception e) {
            log.error("queryOrderStatistics server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单详情
     */
    @PostMapping("/order/queryOrderDetail")
    @AnonymousAccess
    public Response<OrderDTO> queryOrderDetail(@RequestBody OrderCodeDTO request) {
        try {
            // 判断是否页面请求
            if (request.getPageFlag() != null && request.getPageFlag() == 1) {
                request.setOperator(getUserName());
                request.setOrderOwnerName(getLoginName());
            }
            OrderDTO orderDTO = orderQueryService.queryOrderDetail(request);
            if (orderDTO != null) {
                if (StrUtilX.isNotEmpty(orderDTO.getCancellationTerm()) && orderDTO.getCancellationTerm().contains("日")) {
                    String prefixStr = orderDTO.getCancellationTerm().substring(0, orderDTO.getCancellationTerm().indexOf("日") + 1);
                    String suffixStr = orderDTO.getCancellationTerm().substring(orderDTO.getCancellationTerm().indexOf("日") + 1);
                    orderDTO.setCancellationTerm(prefixStr + " " + suffixStr);
                }
                if (CollUtilX.isNotEmpty(orderDTO.getSupplyOrderList())) {
                    List<SupplyOrderDTO> supplyOrderDTOList = new ArrayList<>();
                    for (SupplyOrderDTO supplyOrderDTO : orderDTO.getSupplyOrderList()) {
                        if (StrUtilX.isNotEmpty(supplyOrderDTO.getCancellationTerm()) && supplyOrderDTO.getCancellationTerm().contains("日")) {
                            String prefixStr = supplyOrderDTO.getCancellationTerm().substring(0, supplyOrderDTO.getCancellationTerm().indexOf("日") + 1);
                            String suffixStr = supplyOrderDTO.getCancellationTerm().substring(supplyOrderDTO.getCancellationTerm().indexOf("日") + 1);
                            supplyOrderDTO.setCancellationTerm(prefixStr + " " + suffixStr);
                        }
                        supplyOrderDTOList.add(supplyOrderDTO);
                    }
                    orderDTO.setSupplyOrderList(supplyOrderDTOList);
                }
            } else {
                return Response.error(ErrorCodeEnum.ORDER_NOT_EXISTS.errorCode, ErrorCodeEnum.ORDER_NOT_EXISTS.errorDesc);
            }
            return Response.success(orderDTO);
        } catch (Exception e) {
            log.error("queryOrderDetail server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单备注
     */
    @PostMapping("/order/queryOrderRemark")
    @PreAuthorize("@syyo.check('order')")
    public Response<List<OrderRemarkDTO>> queryOrderRemark(@RequestBody QueryOrderRemarkDTO request) {
        try {
            List<OrderRemarkDTO> orderRemarkDTOList = orderQueryService.queryOrderRemark(request);
            return Response.success(orderRemarkDTOList);
        } catch (Exception e) {
            log.error("queryOrderRemark server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单日志
     */
    @PostMapping("/order/queryOrderLog")
    @PreAuthorize("@syyo.check('order')")
    public Response<List<OrderLogDTO>> queryOrderLog(@RequestBody OrderIdDTO request) {
        try {
            List<OrderLogDTO> orderLogDTOList = orderQueryService.queryOrderLog(request);
            return Response.success(orderLogDTOList);
        } catch (Exception e) {
            log.error("queryOrderLog server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单申请
     */
    @PostMapping("/order/queryOrderRequest")
    @PreAuthorize("@syyo.check('order')")
    public Response<List<OrderRequestDTO>> queryOrderRequest(@RequestBody OrderIdDTO request) {
        try {
            List<OrderRequestDTO> orderRequestDTOList = orderQueryService.queryOrderRequest(request);
            return Response.success(orderRequestDTOList);
        } catch (Exception e) {
            log.error("queryOrderRequest server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单确认信息
     */
    @PostMapping("/order/queryConfirmOrderInfo")
    @PreAuthorize("@syyo.check('order')")
    public Response<String> queryConfirmOrderInfo(@RequestBody QueryConfirmOrderInfoDTO request) {
        try {
            String content = orderQueryService.queryConfirmOrderInfo(request);
            return Response.success(content);
        } catch (Exception e) {
            log.error("queryConfirmOrderInfo server error!", e);
            throw e;
        }
    }

    /**
     * 查询订单价格明细
     */
    @PostMapping("/order/queryOrderPriceItem")
    @PreAuthorize("@syyo.check('order')")
    public Response<List<PriceResponseDTO>> queryOrderPriceItem(@RequestBody OrderIdDTO request) {
        try {
            List<PriceResponseDTO> priceDTOList = orderQueryService.queryOrderPriceItem(request);
            return Response.success(priceDTOList);
        } catch (Exception e) {
            log.error("queryOrderPriceItem server error!", e);
            throw e;
        }
    }

    /**
     * 供货单预览
     */
    @PostMapping("/order/previewSupplyOrder")
    @PreAuthorize("@syyo.check('order')")
    public Response<SupplyOrderPreviewDTO> previewSupplyOrder(@RequestBody SupplyOrderIdDTO request) {
        try {
            SupplyOrderPreviewDTO supplyOrderPreviewDTO = orderQueryService.previewSupplyOrder(request);
            return Response.success(supplyOrderPreviewDTO);
        } catch (Exception e) {
            log.error("previewSupplyOrder server error!", e);
            throw e;
        }
    }

    /**
     * 查询供货单结果
     */
    @PostMapping("/order/querySupplyOrderResult")
    @PreAuthorize("@syyo.check('order')")
    public Response<SupplyResultDTO> querySupplyOrderResult(@RequestBody SupplyOrderIdDTO request) {
        try {
            SupplyResultDTO supplyResultDTO = orderQueryService.querySupplyOrderResult(request);
            return Response.success(supplyResultDTO);
        } catch (Exception e) {
            log.error("querySupplyOrderResult server error!", e);
            throw e;
        }
    }

    /**
     * 查询供货单产品详情
     */
    @PostMapping("/order/querySupplyProduct")
    @PreAuthorize("@syyo.check('order')")
    public Response<SupplyProductDetailDTO> querySupplyProduct(@RequestBody SupplyProductIdDTO request) {
        try {
            SupplyProductDetailDTO supplyProductDetailDTO = orderQueryService.querySupplyProduct(request);
            return Response.success(supplyProductDetailDTO);
        } catch (Exception e) {
            log.error("querySupplyProduct server error!", e);
            throw e;
        }
    }

    /**
     * 查询供货单的供应商列表
     */
    @PostMapping("/order/querySupplier")
    @AnonymousAccess
    public Response<List<FuzzySupplierDTO>> querySupplier(@RequestBody Map<String, String> requestMap) {
        try {
            if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("hotelId"))) {
                if (StrUtilX.isEmpty(requestMap.get("companyCode"))) {
                    requestMap.put("companyCode", getCompanyCode());
                }
                List<FuzzySupplierDTO> fuzzySupplierDTOS = orderQueryService.querySupplier(requestMap);
                return Response.success(fuzzySupplierDTOS);
            } else {
                return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
            }
        } catch (Exception e) {
            log.error("querySupplier 接口异常", e);
            throw e;
        }
    }

    /**
     * 查询订单发票
     */
    @PostMapping("/order/queryOrderInvoice")
    @PreAuthorize("@syyo.check('order')")
    public Response<OrderInvoiceDTO> queryOrderInvoice(@RequestBody QueryOrderInvoiceDTO queryOrderInvoiceDTO) {
        try {
            if (queryOrderInvoiceDTO != null && StrUtilX.isNotEmpty(queryOrderInvoiceDTO.getOrderCode())) {
                OrderInvoiceDTO orderInvoiceDTOS = orderQueryService.queryOrderInvoice(queryOrderInvoiceDTO);
                return Response.success(orderInvoiceDTOS);
            } else {
                return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
            }
        } catch (Exception e) {
            log.error("queryOrderInvoice 接口异常", e);
            throw e;
        }
    }

    @PostMapping("/order/queryOrderInvoiceByOrderCode")
    @AnonymousAccess
    Response<OrderInvoiceDTO> queryOrderInvoiceByOrderCode(@RequestParam String orderCode) {
        QueryOrderInvoiceDTO queryOrderInvoiceDTO = new QueryOrderInvoiceDTO();
        queryOrderInvoiceDTO.setOrderCode(orderCode);
        return Response.success(orderQueryService.queryOrderInvoice(queryOrderInvoiceDTO));

    }

    /**
     * 根据订单号查找订单的基本信息
     */
    @AnonymousAccess
    @PostMapping("/order/queryOrderBasicInfoByOrderCode")
    public Response<OrderDTO> queryOrderBasicInfoByOrderCode(@RequestParam String orderCode) {
        OrderPO orderPO = orderQueryService.queryOrderBasicInfoByOrderCode(orderCode);
        if (orderPO == null) {
            return Response.success(null);
        }
        OrderDTO orderDTO = JSON.parseObject(JSONUtil.toJsonStr(orderPO), new TypeReference<OrderDTO>() {
        });
        return Response.success(orderDTO);
    }

    /**
     * （对外接口）根据订单id查询订单信息
     */
    @AnonymousAccess
    @PostMapping("/order/queryContactOrder")
    @SlsLog(level = "info", name = "查询", message = "queryContactOrder", topic = "/order/queryContactOrder", source = "tiangong-order-server")
    public Response<OrderDTO> queryContactOrder(@RequestParam Integer orderId) {
        return Response.success(orderQueryService.queryContactOrder(orderId));
    }

    /**
     * （对外接口）根据订单编号查询订单详情
     */
    @AnonymousAccess
    @PostMapping("/order/queryOrderDetailByOrderCode")
    Response<OrderDTO> queryOrderDetailByOrderCode(@RequestParam String orderCode) {
        OrderPO orderPO = orderQueryService.queryOrderBasicInfoByOrderCode(orderCode);
        return Response.success(ProductSalePriceConvert.INSTANCE.OrderDTOConvert(orderPO));
    }
}
