package com.tiangong.order.server;

import cn.hutool.core.lang.Validator;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.product.HourlyRoomInfo;
import com.tiangong.dto.product.ProductMappingCache;
import com.tiangong.enums.ChannelEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.order.dto.OrderCodeDTO;
import com.tiangong.order.dto.PackagingInterfaceReq;
import com.tiangong.order.enums.DocumentsTypeEnum;
import com.tiangong.order.remote.dto.PreBookDTO;
import com.tiangong.order.remote.request.AddManualOrderDTO;
import com.tiangong.order.service.BookingService;
import com.tiangong.product.dto.ProductSalePriceDTO;
import com.tiangong.product.dto.ProductSalePriceResultDTO;
import com.tiangong.util.HttpUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 帮客户下单
 */

@Slf4j
@RestController
@RequestMapping("/order")
public class HelpPlaceOrderController extends BaseController {

    @Autowired
    private BookingService bookingService;

    /**
     * 帮客户下单/试预定
     */
    @RequestMapping(value = "/helpCheckPreBook", method = RequestMethod.POST)
    @PreAuthorize("@syyo.check('order')")
    @SlsLog(level = "info", name = "查询", message = "helpCheckPreBook", topic = "/order/helpCheckPreBook", source = "tiangong-order-server")
    public Response<ProductSalePriceResultDTO> checkPreBook(@RequestBody PreBookDTO prebookDTO) {
        String ratePlanId = ProductMappingCache.getOriginalProductId(prebookDTO.getProductId());
        if (StrUtilX.isEmpty(ratePlanId)) {
            log.error("帮客户下单 价格计划id 错误 !");
            throw new SysException(ErrorCodeEnum.NOT_PRODUCT_MAPPING);
        } else {
            prebookDTO.setProductId(ratePlanId);
            prebookDTO.setUserAccount(String.valueOf(getUserAccount()));
            prebookDTO.setRequestId(UUID.randomUUID().toString());
            ProductSalePriceDTO productSalePriceDTO = bookingService.checkPreBook(prebookDTO);
            ProductSalePriceResultDTO productSalePriceResultDTO = new ProductSalePriceResultDTO();
            if (productSalePriceDTO != null) {
                BeanUtils.copyProperties(productSalePriceDTO, productSalePriceResultDTO);
                //fixme mapper类型转换BUG
//                productSalePriceResultDTO = ProductSalePriceConvert.INSTANCE.productSalePriceResultDTO(productSalePriceDTO);
                if (productSalePriceDTO.getHourlyRoom() == 1) {
                    productSalePriceResultDTO.setHourly(1);
                    productSalePriceResultDTO.setHourlyRoomInfo(productSalePriceDTO.getHourlyRoomInfo());
                    Map<String, List<String>> hourlyTimeMap = getHourlyTimeList(prebookDTO.getCheckInDate(), productSalePriceDTO.getHourlyRoomInfo());
                    productSalePriceResultDTO.setHourlyTimeList(hourlyTimeMap.get("hourlyTimeList"));
                    productSalePriceResultDTO.setHourlyTimeShowList(hourlyTimeMap.get("hourlyTimeShowList"));
                }
            }
            return Response.success(productSalePriceResultDTO);
        }
    }

    /**
     * 根据钟点房数据 生产可预定时间列表
     */
    public static Map<String, List<String>> getHourlyTimeList(String checkInDateStr, HourlyRoomInfo hourlyRoomInfo) {
        Map<String, List<String>> hourlyTimeMap = new HashMap<>();
        List<String> hourlyTimeList = new ArrayList<>();
        List<String> hourlyTimeShowList = new ArrayList<>();
        hourlyTimeMap.put("hourlyTimeList", hourlyTimeList);
        hourlyTimeMap.put("hourlyTimeShowList", hourlyTimeShowList);
        try {
            if (hourlyRoomInfo == null) {//无需考虑极端情况 比如开始时间大于24小时
                return hourlyTimeMap;
            }
            int earliestArriveTime = hourlyRoomInfo.getEarliestArriveTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfShow = new SimpleDateFormat("HH:mm");
            Date checkInDate = sdf.parse(checkInDateStr + " 00:00:00");

            Calendar calendar1 = Calendar.getInstance();//开始时间
            calendar1.setTime(checkInDate);
            calendar1.add(Calendar.MINUTE, earliestArriveTime);

            Calendar calendar3 = Calendar.getInstance();//最晚结束时间
            calendar3.setTime(checkInDate);
            int latestLeaveTime = hourlyRoomInfo.getLatestLeaveTime();
            calendar3.add(Calendar.MINUTE, latestLeaveTime);
            if (latestLeaveTime < earliestArriveTime) {
                latestLeaveTime += 24 * 60;
                calendar3.add(Calendar.DAY_OF_YEAR, 1);
            }
            if (calendar3.getTime().before(new Date())) {//最晚已经大于当前时间直接返回
                return hourlyTimeMap;
            }
            Calendar calendar2 = Calendar.getInstance();//循环开始结束时间
            calendar2.setTime(checkInDate);
            calendar2.add(Calendar.MINUTE, earliestArriveTime + hourlyRoomInfo.getDuration() * 60);//设置第一次的结束时间

            String strTmp;
            String strShowTmp;
            boolean endTimeBoo = false;
            double tmpTime = 0;
            String tmpCompareDate;
            boolean tomFlag = false;//开始次日标志
            int time = (((latestLeaveTime - earliestArriveTime) * 2) / 60) - 1;//循环次数
            System.out.println("最开始时间" + sdf.format(calendar1.getTime()) + "  第一次结束时间" + sdf.format(calendar2.getTime()) + " 最晚时间" + sdf.format(calendar3.getTime()));
            for (int i = 0; i < time; i++) {
                if (calendar1.getTime().before(new Date())) {
                    calendar1.add(Calendar.MINUTE, 30);
                    calendar2.add(Calendar.MINUTE, 30);
                    continue;
                }
                if (calendar2.getTime().after(calendar3.getTime())) {//最后时间大于最终时间 后续使用最终时间
                    endTimeBoo = true;
                }
                strShowTmp = "";
                tmpCompareDate = sdf.format(calendar1.getTime()).substring(0, 10);
                if (!tmpCompareDate.equals(checkInDateStr)) {
                    strShowTmp += "次日";
                    tomFlag = true;
                    return hourlyTimeMap;//不能存在次日的情况
                }
                strShowTmp += sdfShow.format(calendar1.getTime()) + "--";
                if (endTimeBoo) {
                    strTmp = sdf.format(calendar1.getTime()) + "--" + sdf.format(calendar3.getTime());
                    if (tomFlag) {
                        strShowTmp += "次日";
                    } else {
                        tmpCompareDate = sdf.format(calendar3.getTime()).substring(0, 10);
                        if (!tmpCompareDate.equals(checkInDateStr)) {
                            strShowTmp += "次日";
                        }
                    }
                    strShowTmp += sdfShow.format(calendar3.getTime());
                    tmpTime = (calendar3.getTimeInMillis() - calendar1.getTimeInMillis()) / 1000d / 60 / 60;//计算入住时间段
                    if (tmpTime % 1.0 == 0) {
                        strShowTmp += "(可入住" + (long) tmpTime + "小时)";
                    } else {
                        strShowTmp += "(可入住" + roundDouble(tmpTime, 2) + "小时)";
                    }
                } else {
                    strTmp = sdf.format(calendar1.getTime()) + "--" + sdf.format(calendar2.getTime());
                    if (tomFlag) {
                        strShowTmp += "次日";
                    } else {
                        tmpCompareDate = sdf.format(calendar2.getTime()).substring(0, 10);
                        if (!tmpCompareDate.equals(checkInDateStr)) {
                            strShowTmp += "次日";
                        }
                    }
                    strShowTmp += sdfShow.format(calendar2.getTime());
                    strShowTmp += "(可入住" + hourlyRoomInfo.getDuration() + "小时)";
                    calendar2.add(Calendar.MINUTE, 30);
                }
                //System.out.println(strTmp);
                System.out.println(strShowTmp);
                hourlyTimeList.add(strTmp);
                hourlyTimeShowList.add(strShowTmp);
                calendar1.add(Calendar.MINUTE, 30);
            }
        } catch (Exception e) {
            log.error("生产可预定时间列表异常", e);
        }
        return hourlyTimeMap;
    }

    public static double roundDouble(double num, int size) {
		/*setScale 第一个参数为保留位数 第二个参数为舍入机制
        BigDecimal.ROUND_DOWN 表示不进位
        BigDecimal.ROUND_UP表示进位*/
        return new BigDecimal(num).setScale(size, RoundingMode.UP).doubleValue();
    }

    /**
     * 帮客户下单/新建订单
     */
    @PostMapping("/helpAddOrder")
    @PreAuthorize("@syyo.check('order')")
    @SlsLog(level = "info", name = "查询", message = "helpAddOrder", topic = "/order/helpAddOrder", source = "tiangong-order-server")
    public Response<OrderCodeDTO> addOTAOrder(@RequestBody AddManualOrderDTO request) {
        try {
            Assert.isTrue(Validator.isMobile(request.getContactPhone()), "手机号不正确");
            request.getGuestList().forEach(item -> {
                item.setIdCardType(DocumentsTypeEnum.ID_CARD.type);
                Assert.notNull(item.getName(), "客人姓名不能为空");
                if (request.getHasIdCard() == 1) {
                    Assert.isTrue(Validator.isCitizenId(item.getIdCardNo()), "身份证不正确");
                }
            });
            String ratePlanId = ProductMappingCache.getOriginalProductId(request.getConversionProductId());
            if (StrUtilX.isEmpty(ratePlanId)) {
                log.error("帮客户下单 价格计划id 错误 !");
                throw new SysException(ErrorCodeEnum.NOT_PRODUCT_MAPPING);
            }
            request.setCompanyCode(CompanyDTO.COMPANY_CODE);
            request.setChannelCode(ChannelEnum.B2B.key);
            request.setProductId(ratePlanId);
            request.setOrderType("1");
            request.setOperator(getUserName());
            request.setRequestId(UUID.randomUUID().toString());
            return Response.success(bookingService.addHelpOrder(request));
        } catch (IllegalArgumentException e) {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, e.getMessage());
        } catch (Exception e) {
            log.error("帮客户下单/新建订单 异常!", e);
            throw e;
        }
    }

    /**
     * 包装接口
     * 前端不能直接调用的，使用后端进行调用
     */
    @PostMapping("/packagingInterface")
    @AnonymousAccess
    public Response<String> packagingInterface(@RequestBody PackagingInterfaceReq req) {
        try {
            String result = HttpUtilX.get(req.getUrl(),null);
            return Response.success(result);
        } catch (Exception e) {
            log.error("获取时间戳系统异常!", e);
            throw e;
        }
    }
}
