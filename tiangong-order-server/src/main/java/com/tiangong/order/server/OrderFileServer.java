package com.tiangong.order.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.order.domain.req.DelOrderFileReq;
import com.tiangong.order.domain.req.QueryOrderFilePageReq;
import com.tiangong.order.domain.req.UploadOrderFileReq;
import com.tiangong.order.dto.OrderFileDTO;
import com.tiangong.order.service.OrderFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("order/file")
public class OrderFileServer extends BaseController {

    @Autowired
    private OrderFileService orderFileService;

//    @PostMapping("addOrderFile")
//    @SlsLog(level = "info", name = "新增订单文件", message = "addOrderFile", topic = "/order/file/addOrderFile", source = "tiangong-order-server")
//    public Response<String> addOrderFile(@RequestBody AddOrderFileReq req){
//        return orderFileService.addOrderFile(req);
//    }

    @PostMapping("queryOrderFilePage")
    public Response<PaginationSupportDTO<OrderFileDTO>> queryOrderFilePage(@Validated @RequestBody QueryOrderFilePageReq req){
        return Response.success(orderFileService.queryOrderFileByOrderCode(req));
    }

    @PostMapping("delOrderFile")
    public Response<String> delOrderFile(@Validated @RequestBody DelOrderFileReq req){
        req.setOperator(super.getUserName());
        req.setOrderOwnerName(super.getLoginName());
        req.setCompanyCode(getCompanyCode());
        orderFileService.delOrderFile(req);
        return Response.success(null);
    }

    @PostMapping("uploadOrderFile")
    public Response<Object> uploadOrderFile(@RequestParam("file") List<MultipartFile> file,
                                            @RequestParam("orderCode") String orderCode,
                                            @RequestParam("orderId") Integer orderId){
        UploadOrderFileReq req = new UploadOrderFileReq();
        req.setFiles(file);
        req.setOrderCode(orderCode);
        req.setOrderId(orderId);
        req.setOperator(super.getUserName());
        req.setOrderOwnerName(super.getLoginName());
        req.setCompanyCode(getCompanyCode());
        return orderFileService.uploadOrderFile(req);
    }
}
