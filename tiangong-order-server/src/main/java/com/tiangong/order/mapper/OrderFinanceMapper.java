package com.tiangong.order.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.order.domain.OrderFinancePO;
import com.tiangong.order.dto.OrderAmtDTO;
import com.tiangong.order.remote.request.OrderIdListDTO;

import java.util.Collection;
import java.util.List;

public interface OrderFinanceMapper extends MyMapper<OrderFinancePO> {

    /**
     * 查询订单金额
     */
    List<OrderAmtDTO> queryOrderAmt(OrderIdListDTO request);

    /**
     * 更新订单锁状态
     *
     * @param orderFinancePO
     */
    void updateLockStatus(OrderFinancePO orderFinancePO);

    /**
     * 批量修改订单财务
     */
    void batchUpOrderFinance(List<OrderFinancePO> updateOrderFinancePOList);
}