package com.tiangong.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.order.domain.OrderChangeWordOrderTaskPO;
import com.tiangong.order.domain.req.OrderChangeWordOrderTaskReq;
import com.tiangong.order.domain.resp.OrderChangeWordOrderTaskResp;

/**
 * 退改工单任务
 */
public interface OrderChangeWordOrderTaskMapper extends BaseMapper<OrderChangeWordOrderTaskPO> {

    /**
     * 查询退改任务详情
     */
    OrderChangeWordOrderTaskResp selectOrderChangeWordOrderTaskDetail(OrderChangeWordOrderTaskReq req);
}