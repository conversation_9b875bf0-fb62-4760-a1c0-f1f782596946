package com.tiangong.order.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.order.domain.SupplyProductPricePO;
import com.tiangong.order.remote.response.PriceResponseDTO;

import java.util.List;

public interface SupplyProductPriceMapper extends MyMapper<SupplyProductPricePO> {

    List<PriceResponseDTO> querySupplyOrderPriceList(Integer supplyOrderId);

    List<PriceResponseDTO> selectSupplyOrderPriceListByOrderCode(String orderCode);
}