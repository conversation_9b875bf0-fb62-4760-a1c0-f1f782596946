package com.tiangong.order.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.order.domain.SupplyOrderFinancePO;
import com.tiangong.order.dto.SupplyOrderAmtDTO;
import com.tiangong.order.remote.request.SupplyOrderIdListDTO;
import com.tiangong.order.remote.request.SupplyOrderInfoListDTO;
import com.tiangong.order.remote.response.SupplyOrderFinanceDTO;

import java.util.List;

public interface SupplyOrderFinanceMapper extends MyMapper<SupplyOrderFinancePO> {

    /**
     * 查询供货单金额
     */
    List<SupplyOrderAmtDTO> querySupplyOrderAmt(SupplyOrderIdListDTO request);

    /**
     * 查询供货单财务相关信息【提供自助结算模块查询】
     */
    List<SupplyOrderFinanceDTO> querySupplyOrderFinanceInfo(SupplyOrderInfoListDTO request);
}