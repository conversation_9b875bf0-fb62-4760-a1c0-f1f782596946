package com.tiangong.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.order.domain.OrderRestrictEntity;
import com.tiangong.order.remote.dto.OrderRestrictDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单条款
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:26:34
 */
public interface OrderRestrictMapper extends BaseMapper<OrderRestrictEntity> {

    List<OrderRestrictDTO> selectRestrict(@Param("orderCode") String orderCode);

}
