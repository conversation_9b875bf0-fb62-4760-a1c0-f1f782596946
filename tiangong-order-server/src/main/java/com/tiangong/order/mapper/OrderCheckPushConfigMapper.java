package com.tiangong.order.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.order.domain.OrderCheckPushConfigPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date ： 2024/3/14
 */
public interface OrderCheckPushConfigMapper extends MyMapper<OrderCheckPushConfigPO> {

    /**
     * 查询入住明细推送配置
     *
     * @param channelCode 渠道编码
     * @return
     */
    OrderCheckPushConfigPO queryOrderCheckPushConfigPO(Integer channelCode);


    /**
     * 查询渠道信息列表
     *
     * @return
     */
    List<OrderCheckPushConfigPO> queryChannelInfoList();


    /**
     * 查询渠道信息列表
     *
     * @param channelCode 渠道编码
     * @return
     */
    List<OrderCheckPushConfigPO> queryChannelInfoList(Integer channelCode);
}
