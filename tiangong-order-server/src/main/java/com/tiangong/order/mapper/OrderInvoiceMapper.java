package com.tiangong.order.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.order.domain.OrderInvoicePO;
import com.tiangong.order.remote.response.OrderInvoiceWithCompanyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface OrderInvoiceMapper extends MyMapper<OrderInvoicePO> {

    /**
     * 查询符合自动开票条件的订单发票列表
     *
     * @param invoiceStatusList 发票状态列表
     * @param invoiceTypeList   发票类型列表
     * @param confirmStatusList 订单确认状态列表
     * @param startDate         离店日期开始
     * @param endDate           离店日期结束
     * @param companyCode       商家编码
     * @return 符合条件的订单发票列表（包含商家编码）
     */
    List<OrderInvoiceWithCompanyDO> queryAutoInvoiceList(
            @Param("invoiceStatusList") List<Integer> invoiceStatusList,
            @Param("invoiceTypeList") List<Integer> invoiceTypeList,
            @Param("confirmStatusList") List<Integer> confirmStatusList,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("companyCode") String companyCode,
            @Param("autoOpenInvoiceStatus") Integer autoOpenInvoiceStatus,
            @Param("orderInvoiceId") Integer orderInvoiceId
    );

    /**
     * 查询符合自动下载条件的订单发票列表
     * 条件：
     * 1、开票状态为开票成功待下载
     * 2、调用过自动发票接口
     *
     * @param invoiceStatusList 发票状态列表
     * @return 符合条件的订单发票列表
     */
    List<OrderInvoicePO> queryAutoDownloadInvoiceList(
            @Param("invoiceStatusList") List<Integer> invoiceStatusList
    );
}
