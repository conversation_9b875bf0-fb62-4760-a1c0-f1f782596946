package com.tiangong.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.dto.order.request.OrderCheckInfoRequest;
import com.tiangong.order.domain.OrderCheckInfoEntity;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单入住信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:26:35
 */
public interface OrderCheckInfoMapper extends BaseMapper<OrderCheckInfoEntity> {

    List<OrderCheckInfoDTO> orderCheckInfoList(@Param("orderCode") String orderCode);

    List<OrderCheckInfoDTO> orderCheckInfoListDhub(OrderCheckInfoRequest orderCheckInfoRequest);

}
