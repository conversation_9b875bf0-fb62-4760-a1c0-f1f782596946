package com.tiangong.order.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.order.domain.SupplyOrderPO;
import com.tiangong.order.dto.NonVccAutoBillConfigDTO;
import com.tiangong.order.remote.dto.InBlankCheckInfoDTO;
import com.tiangong.order.remote.dto.OrderCodeInfoDTO;
import com.tiangong.order.remote.request.QueryOnTimeSupplyOrderListDTO;
import com.tiangong.order.remote.request.SupplyOrderIdDTO;
import com.tiangong.order.remote.response.OnTimeSupplyOrderDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplyOrderMapper extends MyMapper<SupplyOrderPO> {

    /**
     * 单结订单查询
     */
    List<OnTimeSupplyOrderDTO> queryOnTimeSupplyOrderList(QueryOnTimeSupplyOrderListDTO request);

    /**
     * 根据供应商编码查询对应的公章url
     *
     * @param supplierCode
     * @return
     */
    String queryOfficialSealUrl(@Param("supplierCode") String supplierCode);

    /**
     * 批量更新订单状态为已完成
     *
     * @param list
     */
    void batchUpdateOrderStatusDone(@Param("list") List<Integer> list);

    /**
     * 根据订单ID查询供货单号
     *
     * @param orderId
     * @return
     */
    String querySupplyOrderCodeByOrderId(@Param("orderId") Integer orderId);

    /**
     * 获取需要没有填写入住明细的供货单
     */
    List<SupplyOrderDTO> selectInBlankCheckInfoSupplyOrderCodeList(InBlankCheckInfoDTO inBlankCheckInfoDTO);

    /**
     * 查询入住明细异常的订单数量
     * @return
     */
    List<Integer> selectCheckInDetailAbnormalNum();


    void updateSupplierCheckInAbnormal(@Param("supplyOrderCode") String supplyOrderCode);

    /**
     * 根据供货单编码查询订单编码
     */
    List<OrderCodeInfoDTO> selectOrderCodeBySupplyOrderCode(SupplyOrderIdDTO request);

    /**
     * 查询供货单信息
     */
    SupplyOrderDTO selectSupplyOrderInfo(SupplyOrderIdDTO request);

    /**
     * 根据supplierCode和hotelId查询非VCC自动出账配置
     */
    NonVccAutoBillConfigDTO selectNonVccAutoBillConfigBySupplierCodeAndHotelId(@Param("supplierCode") String supplierCode, @Param("hotelId") Long hotelId);

}