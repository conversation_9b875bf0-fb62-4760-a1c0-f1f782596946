package com.tiangong.order.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.order.dto.PersonInvoiceQueryDTO;
import com.tiangong.order.dto.PersonInvoiceQueryRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 个人发票查询Mapper
 */
@Mapper
public interface PersonInvoiceMapper {

    /**
     * 查询个人发票列表（分页）
     * @param page 分页参数
     * @param request 查询条件
     * @return 发票列表（分页）
     */
    IPage<PersonInvoiceQueryRespDTO> queryOrderInvoiceList(IPage<PersonInvoiceQueryRespDTO> page, @Param("request") PersonInvoiceQueryDTO request);

    /**
     * 通过订单ID集合查询订单实收金额
     * @param orderIds 订单ID集合
     * @return 订单实收金额列表
     */
    List<PersonInvoiceQueryRespDTO> queryOrderReceivedAmt(@Param("orderIds") List<Integer> orderIds);
}
