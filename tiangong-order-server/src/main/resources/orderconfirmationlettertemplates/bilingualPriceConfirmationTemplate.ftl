<!DOCTYPE html>
<html lang="zh-en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>确认函 | Booking Voucher</title>
  <style>
    :root {
      /* 颜色变量 */
      --primary-color: #0D529C;
      --secondary-color: #1E2E4A;
      --light-blue: #F5F9FF;
      --text-gray: #6B6B6B;
      --text-dark: #333333;
      --text-light: #FFFFFF;
      --border-color: #EEEEEE;
      --cancel-color: #A60202;
      --cancel-bg: #F9F1F3;
      --link-color: #005FC6;
    }

    /* 基础样式 */
    body {
      margin: 0;
      padding: 0;
    }

    /* 布局类 */
    .flex-row {
      display: flex;
      flex-direction: row;
    }

    .flex-col {
      display: flex;
      flex-direction: column;
    }

    .justify-between {
      justify-content: space-between;
    }

    /* 页面容器 */
    .page {
      width: 1040px;
      box-sizing: border-box;
      background: #fff;
      margin: 0px auto;
    }

    /* 页头 */
    .group {
      background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ea4c5e7fe81f4de5a512ebfce93ea784_mergeImage.png);
      width: 1040px;
      height: 180px;
      display: flex;
      align-items: center;
      padding: 0 60px;
      box-sizing: border-box;
    }

    .image {
      height: 120px;
      width: 160px;
    }

    /* 主容器 */
    .container {
      box-sizing: border-box;
      width: 1040px;
      padding: 50px 60px;
      box-shadow: 0px 0px 20px 0px rgba(13, 82, 156, 0.1);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      position: relative;
    }

    .text-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    /* 分隔线 */
    .line {
      width: 920px;
      height: 3px;
      background: #4A4A4A;
    }

    /* 内容区块 */
    .context {
      margin: 30px 0 40px 0;
    }

    /* 文本样式 */
    .text-base {
      font-family: Alibaba-PuHuiTi, Alibaba-PuHuiTi;
      font-style: normal;
      text-align: left;
    }

    .text_1 {
      height: 40px;
      font-weight: normal;
      font-size: 40px;
      color: #FFFFFF;
      line-height: 40px;
    }

    .text_1_eng {
      font-weight: normal;
      font-size: 24px;
      color: #FFFFFF;
      line-height: 24px;
      /* margin-top: 8px; */
    }

    .text-wrapper_1 {
      display: flex;
      flex-direction: column;
      justify-content: center;  /* 垂直居中 */
    }

    .text_2 {
      font-weight: bold;
      font-size: 32px;
      color: #0D529C;
      line-height: 38px;
    }

    .text_3 {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #000000;
      line-height: 28px;
    }

    .text_4 {
      font-weight: bold;
      font-size: 28px;
      color: #1E2E4A;
      line-height: 28px;
      margin-bottom: 30px;
      display: inline-block;
    }

    .text_5 {
      font-weight: bold;
      font-size: 20px;
      color: #0D529C;
      line-height: 28px;
    }

    .text_6 {
      font-weight: normal;
      font-size: 20px;
      color: #1E2E4A;
      line-height: 28px;
      text-align: justify;
    }

    .paragraph_4 {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #000000;
      line-height: 28px;
      margin-top: 10px;
    }

    /* 信息行 */
    .info-row {
      display: flex;
      align-items: flex-start;
      margin: 20px 0;
      font-size: 14px;
      line-height: 1.5;
    }

    .info-value-row {
      display: flex;
      flex-direction: column;
    }

    .info-label {
      color: #6B6B6B;
      width: 280px;
      min-width: 280px;
      margin-right: 20px;
      flex-shrink: 0;
      font-size: 14px;
      line-height: 20px;
      word-break: break-word;
    }

    .info-value {
      color: #1E2E4A;
      flex: 1;
      word-break: break-word;
      font-size: 14px;
      line-height: 20px;
    }

    .info-highlight {
      color: #0D529C;
      font-weight: bold;
    }

    .info-desc {
      font-size: 12px;
      color: #6B6B6B;
      line-height: 18px;
    }

    /* 费用相关 */
    .fee-container {
      display: flex;
      justify-content: flex-start;
      align-items: start;
      gap: 10px;
    }

    .btn {
      background: #0D529C;
      border-radius: 4px;
      padding: 0 10px;
      color: #FFFFFF;
      font-size: 14px;
      height: 28px;
      line-height: 28px;
      display: flex;
      align-items: center;
      font-weight: bold;
    }

    .fee-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #1E2E4A;
      font-size: 14px;
      padding-bottom: 10px;
    }

    .currency-note {
      color: #999999;
      font-size: 12px;
      text-align: right;
      margin-top: 5px;
    }

    /* 费用表格 */
    .fee-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }

    .fee-table tr {
      border-bottom: 1px dashed #9F9F9F;
    }

    .fee-table td {
      padding: 12px 0;
      font-size: 14px;
      color: #333333;
    }

    .fee-table .fee-label {
      text-align: left;
    }

    .fee-table .fee-value {
      text-align: right;
      font-weight: 500;
    }

    .fee-table .highlight-row {
      background-color: #F5F9FF;
    }

    .fee-table .highlight-row td {
      font-weight: bold;
      font-size: 20px;
      color: #1E2E4A;
      line-height: 20px;
    }

    .fee-table .total-row {
      background-color: #0D529C;
      color: #FFFFFF;
    }

    .fee-table .total-row td {
      padding: 19px 15px;
      font-size: 20px;
      font-weight: bold;
      color: #FFFFFF;
    }

    .child-row td {
      padding-left: 14px;
    }

    .child-row .fee-label {
      color: #6B6B6B;
    }

    /* 通知部分 */
    .notice-section {
      margin-bottom: 25px;
    }

    .notice-category {
      margin: 15px 0 10px 0;
      font-size: 14px;
      color: #9B9B9B;
    }

    .notice-desc {
      margin: 8px 0;
      font-size: 14px;
      line-height: 22px;
      color: #666666;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      max-width: 100%;
    }

    .notice-list {
      margin: 0;
      padding: 0;
      list-style-type: none;
    }

    .notice-item {
      position: relative;
      padding-left: 28px;
      margin: 8px 0;
      font-size: 14px;
      line-height: 1.6;
      color: #666666;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      max-width: 100%;
    }

    .notice-list .notice-item::before {
      content: "•";
      position: absolute;
      left: 0;
      color: #000;
      font-size: 28px;
      line-height: 16px;
      top: 2px;
    }

    .cancel-policy-content {
      background-color: #F9F1F3;
      padding: 15px;
      font-size: 14px;
      line-height: 1.6;
      position: relative;
      color: #A60202;
    }

    /* 页脚 */
    .footer {
      margin-top: 40px;
      padding-top: 20px;
    }

    .phone {
      margin-left: 10px;
      font-family: Poppins, Poppins;
      font-weight: 500;
      font-size: 14px;
      color: #005FC6;
      line-height: 28px;
      text-decoration-line: underline;
    }

    /* 双语部分 */
    .bilingual-section {
      display: flex;
      flex-direction: column;
    }

    .bilingual-title {
      display: flex;
      flex-direction: column;
    }

    .bilingual-content {
      display: flex;
      flex-direction: column;
    }
  </style>
</head>

<body>
  <div class="page">
    <div class="group">
      <div class="text-wrapper_1">
        <span class="text-base text_1">确认函</span>
        <span class="text-base text_1_eng">Booking Voucher</span>
      </div>
    </div>
    <div class="container">
      <div class="header-content">
        <div class="text-content">
          <div class="bilingual-title">
            <span class="text-base text_2">谢谢！您的预订已确认。</span>
            <span class="text-base text_2">Thanks! Your booking is confirmed.</span>
          </div>

          <div class="context">
            <div class="bilingual-content">
              <div class="text_3">
                <span>您于${startDateCN}到${endDateCN}入住${hotelNameCN}的订单已经确认。</span><br />
                <#if cancellationTermLastCN?? && cancellationTermLastCN !="">
                  <span>${cancellationTermLastCN}</span><br />
                </#if>
                <span>入住时请使用订单上的住客姓名或订单上的住客有效证件办理入住。</span><br />
                <#if confirmationCode?has_content> <span>酒店确认号: ${confirmationCode}</span> </#if>
              </div>
              <div class="paragraph_4">
                <span>Your reservation at ${hotelNameEN} for the dates of ${startDateEN} to ${endDateEN} has been
                  confirmed.</span><br />
		   <#if cancellationTermLastEN?? && cancellationTermLastEN !="">
                <span>${cancellationTermLastEN}</span><br />
 		</#if>
                <span>Please check in using the guest name or the valid identification document of the guest as shown on
                  the reservation.</span><br />
                <#if confirmationCode?has_content> <span>Hotel confirmation number: ${confirmationCode}</span> </#if>
              </div>
            </div>
          </div>
        </div>
        <img class="image" referrerpolicy="no-referrer" <#if logoUrl?? && logoUrl != ""> src="${logoUrl}" </#if> />
      </div>

      <div class="line"></div>

      <div class="context">
        <div class="bilingual-title">
          <span class="text-base text_4">酒店信息(Hotel Information)</span>
        </div>
        <span class="text-base text_5">${hotelNameCN}</span>
        <div class="text_5">${hotelNameEN}</div>
        <div class="info-row">
          <span class="text-base info-label">酒店电话（Contact Number）</span>
          <span class="text-base info-value">${hotelPhone}</span>
        </div>
        <div class="info-row">
          <span class="text-base info-label">酒店地址（Address）</span>
          <span class="text-base info-value">
            <#if countryNameCN?has_content>${countryNameCN}</#if>
            <#if cityNameCN?has_content>${cityNameCN}</#if>
            <#if hotelAddressCN?has_content>${hotelAddressCN}</#if>
            （<#if countryNameEN?has_content>${countryNameEN}</#if>
            <#if cityNameEN?has_content>${cityNameEN}</#if>
            <#if hotelAddressEN?has_content>${hotelAddressEN}</#if>）
          </span>
        </div>
      </div>

      <div class="line"></div>

      <div class="context">
        <span class="text-base text_4">订单信息</span>
        <div class="info-row">
          <span class="text-base info-label">订单编号（Order Number）</span>
          <span class="text-base info-value">${orderCode}</span>
        </div>
        <div class="info-row">
          <span class="text-base info-label">入离日期（Check-in/Check-out）</span>
          <div class="info-value-row">
            <span
              class="text-base info-value info-highlight">${checklInDate}(${checkInWeekCN})—${checkInWeekCN}(${checkOutWeekCN})
              共${roomQty}间${nightQty}晚</span>
            <span
              class="text-base info-value info-highlight">${checklInDate}(${checkInWeekEN})—${checklOutDate}(${checkOutWeekEN})
              #{roomQty} Rooms, ${nightQty} Nights</span>
          </div>
        </div>
        <#if roomNameEN?has_content && roomNameEN !="">
          <div class="info-row">
            <span class="text-base info-label">房型名称（Room Type）</span>
            <span class="text-base info-value">${roomNameCN}（${roomNameEN}）</span>
          </div>
        </#if>
        <div class="info-row">
          <span class="text-base info-label">早餐（Breakfast）</span>
          <span class="text-base info-value">${breakFastCN}（${breakFastEN}）</span>
        </div>
        <#list roomGuestList as roomGuest>
          <div class="info-row">
            <span class="text-base info-label">房间 ${roomGuest.roomNumber} (Room ${roomGuest.roomNumber}) | 入住人 (Guest)</span>
            <div class="info-value-row">
              <span class="text-base info-value">${roomGuest.adultQty} 成人
                <#if roomGuest.childrenQty?has_content && roomGuest.childrenQty !=0>
                  , ${roomGuest.childrenQty} 儿童 (
                  <#list roomGuest.childrenAge?split(",") as age>
                    <#-- 输出每个年龄 -->
                      <#if age?number < 1>
                        > 1 岁
                        <#else>
                          ${age} 岁
                      </#if>
                      <#if age_has_next>, </#if>
                  </#list>
                  )  (
                </#if>
                ${roomGuest.adultQty} Adults
                <#if roomGuest.childrenQty?has_content && roomGuest.childrenQty !=0>
                  , ${roomGuest.childrenQty} Children (
                  <#list roomGuest.childrenAge?split(",") as age>
                  <#-- 输出每个年龄 -->
                    <#if age?number < 1>
                      > 1 years-old
                    <#else>
                      ${age} years-old
                    </#if>
                    <#if age_has_next>, </#if>
                  </#list>
                  )
                </#if>
                 ) |
                <#list roomGuest.orderGuestList as guest>
                  <#if guest.firstName?has_content && guest.lastName?has_content>
                    ${guest.lastName}/${guest.firstName} <#if guest_has_next>, </#if>
                  <#else>
                    ${guest.name} <#if guest_has_next>, </#if>
                  </#if>
                </#list>
              </span>
            </div>
          </div>
        </#list>
      </div>

      <div class="line"></div>

      <#if priceShow==1>
        <div class="context">

          <div class="fee-container">
            <span class="text-base text_4">费用说明(Payment Details)</span>
            <#if supplierLabel?? && supplierLabel==6>
              <div class="btn">
                <span>打包价 (Package Price)</span>
              </div>
            </#if>
          </div>


          <div class="fee-info">
            <span>${checklInDate}-${checklOutDate} | ${roomQty}间${nightQty}晚（${roomQty} Rooms, ${nightQty}
              Nights）</span>
          </div>

          <table class="fee-table">
            <tr class="highlight-row">
              <#if payMethod==0>
                <td class="fee-label">预付（Prepaid）</td>
                <td class="fee-value">${saleCurrencyCode}&nbsp;${orderAmt}</td>
                <#else>
                  <td class="fee-label">到店付 (Pay in store)</td>
                  <td class="fee-value">${saleCurrencyCode}&nbsp;${salePrice}</td>
              </#if>


            </tr>
            <#if feeList?? && feeList?size gt 0>
              <#list feeList as fee>
                <tr>
                  <td class="fee-label">房间${fee.roomNumber} (${fee.adultQty}人
                    <#if fee.childrenQty?has_content && fee.childrenQty!=0>
                      , ${fee.childrenQty} 儿童
                    </#if>
                    )
                    Room ${fee.roomNumber} (${fee.adultQty} Guests
                    <#if fee.childrenQty?has_content && fee.childrenQty!=0>
                      , ${fee.childrenQty} Children
                    </#if>
                    )
                  </td>
                  <#assign totalPrice=0>
                    <#if fee.priceList? has_content>
                      <#list fee.priceList as price>
                        <#assign totalPrice=totalPrice + price.salePrice>
                      </#list>
                    </#if>
                    <td class="fee-value">${saleCurrencyCode}&nbsp;${totalPrice}</td>
                </tr>
              </#list>
            </#if>
            <tr>
              <td class="fee-label">您的房间已含税/费（incl.taxes）</td>
              <#if payMethod ==0>
                <td class="fee-value">${saleCurrencyCode}&nbsp;${orderAmt}</td>
                <#else>
                  <td class="fee-value">${saleCurrencyCode}&nbsp;${salePrice}</td>
              </#if>
            </tr>
            <#if totalTax?has_content && totalTax!=0>
              <tr class="child-row">
                <td class="fee-label">税费（Taxes and Fees）</td>
                <td class="fee-value">${saleCurrencyCode}&nbsp;${totalTax}</td>
              </tr>
            </#if>
            <#if saleCurrencyCode?has_content && payAtHotelAgentCurrencyFee?has_content &&
              payAtHotelAgentCurrencyFee!=0>
              <tr class="highlight-row">
                <td class="fee-label">到店另付（Pay upon check-in）</td>
                <td class="fee-value">${saleCurrencyCode}&nbsp;${payAtHotelAgentCurrencyFee}</td>
              </tr>
            </#if>
          </table>
          <#if payAtHotelCurrency?has_content && payAtHotelFee?has_content && payAtHotelFee!=0>
            <div class="currency-note">(${payAtHotelCurrency} ${payAtHotelFee})</div>
          </#if>
          <table class="fee-table">
            <tr class="total-row">
              <td class="fee-label">订单金额（Total）</td>
              <td class="fee-value">${saleCurrencyCode}&nbsp;${orderAmt}</td>
            </tr>
          </table>
        </div>


        <div class="line"></div>

        <div class="context">
          <span class="text-base text_4">税费说明(Taxes and Fees)</span>
          <p class="text-base text_6">
            该费用包括旅行服务供应商（即酒店、租车公司）和/或我们针对您的预订向税务部门支付的税款估算金额（包括但不限于销售、住宿和增值税）。该金额可能还包括我们支付的度假费、清洁费及其他费用和/或我们、酒店供应商和/或您预订的网站为我们和/或他们提供的服务所收取的费用，具体取决于地点、金额及预订方式等因素。有关更多详细信息，请参阅条款和条件。
          </p>
          <p class="text-base text_6">
            This fee includes the estimated amount of taxes that the travel service provider (i.e., hotel, car rental
            company) and/or we pay to the tax authorities in connection with your booking (including but not limited to
            sales, accommodation, and value-added tax). This amount may also include resort fees, cleaning fees, and
            other
            charges paid by us as well as costs and/or service fees charged by us, the hotel supplier, and/or the
            website
            through which you made your booking, depending on factors such as location, amount, and booking method. For
            more detailed information.Please refer to the terms and conditions.
          </p>
        </div>
        <div class="line"></div>
      </#if>


      <#if hotelPolicy?has_content>
        <div class="context">
          <span class="text-base text_4">行程须知(Travel Notice)</span>

          <#-- 定义固定的顺序 -->
            <#assign orderedKeys=["ageLimit", "checkInCheckOut" , "instructions" , "mandatoryFees" , "optionalFees"
              , "hotelPolicy" ]>

              <#-- 按照 orderedKeys 的顺序遍历 -->
                <#list orderedKeys as key>
                  <#-- 检查当前 key 是否存在于 hotelPolicy 中 -->
                    <#if hotelPolicy[key]??>
                      <#-- 根据 key 的值执行不同的逻辑 -->
                        <#if key=="ageLimit" && (hotelPolicy[key].cntext?has_content ||
                          hotelPolicy[key].entext?has_content)>
                          <div class="notice-category">年龄限制（Age Limits）</div>
                          <#if hotelPolicy[key].cntext?has_content> ${hotelPolicy[key].cntext} </#if>
                          <#if hotelPolicy[key].entext?has_content>${hotelPolicy[key].entext} </#if>
                          <#elseif key=="checkInCheckOut" && (hotelPolicy[key].cntext?has_content ||
                            hotelPolicy[key].entext?has_content)>
                            <div class="notice-category">入住离店（Arrival/Leave (Local time)）</div>
                            <#if hotelPolicy[key].cntext?has_content> ${hotelPolicy[key].cntext} </#if>
                            <#if hotelPolicy[key].entext?has_content>${hotelPolicy[key].entext} </#if>
                            <#elseif key=="instructions" && (hotelPolicy[key].cntext?has_content ||
                              hotelPolicy[key].entext?has_content)>
                              <div class="notice-category">入住须知（Check-in Limits）</div>
                              <#if hotelPolicy[key].cntext?has_content> ${hotelPolicy[key].cntext} </#if>
                              <#if hotelPolicy[key].entext?has_content>${hotelPolicy[key].entext} </#if>
                              <#elseif key=="mandatoryFees" && (hotelPolicy[key].cntext?has_content ||
                                hotelPolicy[key].entext?has_content)>
                                <div class="notice-category">额外收费（Optional Extras）</div>
                                <#if hotelPolicy[key].cntext?has_content> ${hotelPolicy[key].cntext} </#if>
                                <#if hotelPolicy[key].entext?has_content>${hotelPolicy[key].entext} </#if>
                                <#elseif key=="optionalFees" && (hotelPolicy[key].cntext?has_content ||
                                  hotelPolicy[key].entext?has_content)>
                                  <div class="notice-category">自费项目（Additional Expenses）</div>
                                  <#if hotelPolicy[key].cntext?has_content> ${hotelPolicy[key].cntext} </#if>
                                  <#if hotelPolicy[key].entext?has_content>${hotelPolicy[key].entext} </#if>
                                  <#elseif key=="hotelPolicy" && (hotelPolicy[key].cntext?has_content ||
                                    hotelPolicy[key].entext?has_content)>
                                    <div class="notice-category">特别入住说明（Special Check-in Limtis）</div>
                                    <#if hotelPolicy[key].cntext?has_content> ${hotelPolicy[key].cntext} </#if>
                                    <#if hotelPolicy[key].entext?has_content>${hotelPolicy[key].entext} </#if>
                        </#if>
                    </#if>
                </#list>
      </#if>

    </div>

    <div class="line"></div>
    <#if customerTel?has_content>
      <div class="footer">
        <span>客服热线（Customer Service Hotline）：</span>
        <span class="phone">${customerTel}</span>
      </div>
    </#if>
  </div>
  </div>
</body>

</html>