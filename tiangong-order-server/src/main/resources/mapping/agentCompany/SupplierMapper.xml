<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.OrgMapper">
    <resultMap id="SupplierList" type="com.tiangong.organization.remote.dto.SupplierSelectDTO">
        <result column="org_id" property="supplierId"/>
        <result column="org_type" property="supplierType"/>
        <result column="org_name" property="supplierName"/>
        <result column="org_code" property="supplierCode"/>
        <result column="user_name" property="adminName"/>
        <result column="user_number" property="adminAccount"/>
        <result column="user_tel" property="adminTel"/>
        <result column="settlement_type" property="settlementType"/>
        <result column="settlement_currency" property="settlementCurrency"/>
        <result column="purchase_manager_id" property="purchaseManagerId"/>
        <result column="purchase_manager_name" property="purchaseManagerName"/>
        <result column="company_signature" property="companySignature"/>
        <result column="official_seal_url" property="officialSealUrl"/>
        <result column="official_seal_id" property="officialSealId"/>
        <result column="hotel_id" property="hotelId"/>
    </resultMap>
    <resultMap id="AgentList" type="com.tiangong.organization.remote.dto.AgentSelectDTO">
        <result column="org_id" property="agentId"/>
        <result column="org_type" property="agentType"/>
        <result column="org_name" property="agentName"/>
        <result column="org_code" property="agentCode"/>
        <result column="org_tel" property="adminTel"/>
        <result column="user_name" property="adminName"/>
        <result column="user_number" property="adminAccount"/>
        <result column="user_tel" property="agentTel"/>
        <result column="settlement_type" property="settlementType"/>
        <result column="credit_line" property="creditLine"/>
        <result column="sale_manager_id" property="saleManagerId"/>
        <result column="sale_manager_name" property="saleManagerName"/>
        <result column="balance" property="balance"/>
        <result column="channel_name" property="channelName"/>
        <result column="channel_code" property="channelCode"/>
    </resultMap>
    <select id="AgentList" resultMap="AgentList">
        SELECT
        too.org_id,
        too.org_type,
        too.org_name,
        too.org_code,
        too.org_tel,
        toca.user_name,
        toca.user_number,
        toca.user_tel,
        toca.settlement_type,
        toca.credit_line,
        toca.balance,
        tau.user_id AS sale_manager_id,
        tau.user_name AS sale_manager_name,
        too.channel_code,
        m.channel_name
        FROM t_org_company_agent toca
        INNER JOIN t_org_organization too
        ON too.org_id = toca.org_id
        LEFT JOIN t_auth_user tau
        ON tau.user_id = toca.sale_manager_id AND tau.org_code = toca.company_code
        LEFT JOIN t_pro_m_channel m ON m.channel_code = too.channel_code AND m.company_code = toca.company_code
        WHERE too.org_code=#{orgCode}
    </select>
    <select id="getAgentCreditLine" resultType="com.tiangong.organization.remote.dto.AgentCreditLineDTO">
        SELECT
        agent_code agentCode,
        order_code orderCode,
        agent_credit_line_id agentCreditLineId,
        d_r_credit_line deductRefundCreditLine
        FROM t_org_agent_credit_line
        where agent_code = #{agentCode}
    </select>

    <select id="getPartnerCodeByAgentCode" resultType="java.lang.String">
        SELECT
        partner_code
        FROM t_org_agent_api_config
        WHERE agent_code = #{agentCode}
    </select>
</mapper>