<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.order.mapper.OrderCheckDetailMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.order.remote.dto.OrderCheckDetailDTO">
        <result property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="checkInfoId" column="check_info_id"/>
        <result property="saleDate" column="sale_date"/>
        <result property="salePrice" column="sale_price"/>
        <result property="refundPrice" column="refund_price"/>
        <result property="supplyOrderCode" column="supply_order_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <delete id="deleteByCheckInfoId">
        DELETE FROM o_order_check_detail
        WHERE check_info_id = #{checkInfoId}
    </delete>

</mapper>