<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.order.mapper.OrderCheckPushInfoMapper">
    <update id="updateOrderCheckPushStatus">
        UPDATE o_order_check_push_info SET push_status = #{pushStatus},error_message = #{errorMsg}
        WHERE order_code = #{orderCode}
    </update>

    <select id="queryOrderCheckPushInfoPage" resultType="com.tiangong.order.domain.OrderCheckPushInfoPO">
        SELECT
            t1.id,
            t1.order_code,
            t1.check_in_date,
            t1.check_out_date,
            t1.channel_order_code,
            t1.agent_code,
            t1.agent_name,
            t1.hotel_name,
            t1.guest,
            t1.push_status,
            t1.room_name,
            t1.rate_plan_name,
            t1.channel_code,
            t1.pay_method,
            t1.error_message,
            t1.created_dt
        FROM o_order_check_push_info t1
        LEFT JOIN o_order t2 on t2.order_code = t1.order_code
        WHERE 1=1
        <if test="req.orderCode != null and req.orderCode != '' ">
            AND t1.order_code LIKE concat('%',#{req.orderCode},'%')
        </if>
        <if test="req.channelOrderCode != null and req.channelOrderCode != '' ">
            AND t1.channel_order_code LIKE concat('%',#{req.channelOrderCode},'%')
        </if>
        <if test="req.agentCode != null and req.agentCode != '' ">
            AND t1.agent_code = #{req.agentCode}
        </if>
        <if test="req.agentName != null and req.agentName != '' ">
            AND t1.agent_name = #{req.agentName}
        </if>
        <if test="req.hotelId != null">
            AND t2.hotel_id = #{req.hotelId}
        </if>
        <if test="req.guest != null and req.guest != '' ">
            AND t1.guest LIKE concat('%',#{req.guest},'%')
        </if>
        <if test="req.checkInDate != null and req.checkInDate != '' and req.checkOutDate != null and req.checkOutDate != ''">
            AND (t1.check_out_date BETWEEN #{req.checkInDate} AND #{req.checkOutDate})
        </if>
        <if test="req.channelCode != null ">
            AND t1.channel_code = #{req.channelCode}
        </if>
        <if test="req.pushStatus != null">
            AND t1.push_status = #{req.pushStatus}
        </if>
        ORDER BY t1.push_status asc, t1.created_dt desc
    </select>

    <select id="queryOrderCheckPushInfo" resultType="com.tiangong.order.dto.OrderCheckPushInfoDTO">
        SELECT id,order_code,check_in_date,check_out_date,channel_order_code,agent_code,agent_name,hotel_name,guest,push_status,room_name,rate_plan_name,channel_code,pay_method,error_message
        FROM o_order_check_push_info WHERE order_code = #{orderCode}
    </select>

    <select id="queryOrderCheckPushInfoList" resultType="com.tiangong.order.domain.OrderCheckPushInfoPO">
        SELECT id,order_code,check_in_date,check_out_date,channel_order_code,agent_code,agent_name,hotel_name,guest,push_status,room_name,rate_plan_name,channel_code,
        pay_method,error_message
        FROM o_order_check_push_info WHERE push_status != 4 AND (check_out_date BETWEEN #{checkInDate} AND #{checkOutDate}) AND channel_code = #{channelCode}
    </select>

</mapper>
