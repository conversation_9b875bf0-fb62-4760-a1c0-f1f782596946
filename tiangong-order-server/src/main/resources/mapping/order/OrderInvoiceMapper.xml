<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.OrderInvoiceMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderInvoicePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="invoice_status" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="invoice_date" jdbcType="VARCHAR" property="invoiceDate"/>
        <result column="invoice_amount" jdbcType="DECIMAL" property="invoiceAmount"/>
        <result column="invoice_type" jdbcType="INTEGER" property="invoiceType"/>
        <result column="invoice_name" jdbcType="VARCHAR" property="invoiceName"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
        <result column="tax_number" jdbcType="VARCHAR" property="taxNumber"/>
        <result column="account_bank" jdbcType="VARCHAR" property="accountBank"/>
        <result column="company_phone" jdbcType="VARCHAR" property="companyPhone"/>
        <result column="register_addr" jdbcType="VARCHAR" property="registerAddr"/>
        <result column="account_no" jdbcType="VARCHAR" property="accountNo"/>
        <result column="invoice_remark" jdbcType="VARCHAR" property="invoiceRemark"/>
        <result column="apply_date" jdbcType="VARCHAR" property="applyDate"/>
        <result column="ticket_type" jdbcType="INTEGER" property="ticketType"/>
        <result column="send_status" jdbcType="INTEGER" property="sendStatus"/>
        <result column="received_name" jdbcType="VARCHAR" property="receivedName"/>
        <result column="received_phone" jdbcType="VARCHAR" property="receivedPhone"/>
        <result column="received_addr" jdbcType="VARCHAR" property="receivedAddr"/>
        <result column="tracking_no" jdbcType="VARCHAR" property="trackingNo"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="invoice_title_type" jdbcType="INTEGER" property="invoiceTitleType"/>
        <result column="invoice_urls" jdbcType="VARCHAR" property="invoiceUrls"/>
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="apply_id" jdbcType="VARCHAR" property="applyId"/>
        <result column="sort_num" jdbcType="VARCHAR" property="sortNum"/>
        <result column="specs" jdbcType="VARCHAR" property="specs"/>
        <result column="per_unit" jdbcType="VARCHAR" property="perUnit"/>
        <result column="count_num" jdbcType="VARCHAR" property="countNum"/>
        <result column="invoice_tax_rate" jdbcType="VARCHAR" property="invoiceTaxRate"/>
<!--        <result column="e_push_address_config_status" jdbcType="INTEGER" property="ePushAddressConfigStatus"/>-->
<!--        <result column="e_push_address_config_message" jdbcType="VARCHAR" property="ePushAddressConfigMessage"/>-->
        <result column="e_create_bill_status" jdbcType="INTEGER" property="eCreateBillStatus"/>
        <result column="e_create_bill_message" jdbcType="VARCHAR" property="eCreateBillMessage"/>
        <result column="e_push_address_invalid_status" jdbcType="INTEGER" property="ePushAddressInvalidStatus"/>
        <result column="e_push_address_invalid_message" jdbcType="VARCHAR" property="ePushAddressInvalidMessage"/>
        <result column="e_invoice_file_missing_status" jdbcType="INTEGER" property="eInvoiceFileMissingStatus"/>
        <result column="e_invoice_file_missing_message" jdbcType="VARCHAR" property="eInvoiceFileMissingMessage"/>
        <result column="e_invoice_apply_failed_status" jdbcType="INTEGER" property="eInvoiceApplyFailedStatus"/>
        <result column="e_invoice_apply_failed_message" jdbcType="VARCHAR" property="eInvoiceApplyFailedMessage"/>
        <result column="e_file_download_failed_status" jdbcType="INTEGER" property="eFileDownloadFailedStatus"/>
        <result column="e_file_download_failed_message" jdbcType="VARCHAR" property="eFileDownloadFailedMessage"/>
        <result column="file_url_status" jdbcType="INTEGER" property="fileUrlStatus"/>
        <result column="auto_open_invoice_status" jdbcType="INTEGER" property="autoOpenInvoiceStatus"/>
        <result column="invoice_file_name" jdbcType="VARCHAR" property="invoiceFileName"/>
        <result column="e_email_send_failed_status" jdbcType="INTEGER" property="eEmailSendFailedStatus"/>
        <result column="e_email_send_failed_message" jdbcType="VARCHAR" property="eEmailSendFailedMessage"/>
        <result column="serial_no" jdbcType="VARCHAR" property="serialNo"/>
    </resultMap>

    <!-- 订单发票信息（包含商家编码）结果映射 -->
    <resultMap id="OrderInvoiceWithCompanyResultMap" type="com.tiangong.order.remote.response.OrderInvoiceWithCompanyDO" extends="BaseResultMap">
        <result column="company_code" property="companyCode"/>
        <result column="order_confirmation_status" property="confirmationStatus"/>
        <result column="end_date" property="endTime"/>
        <result column="sale_currency" property="saleCurrency"/>
        <result column="order_amt" property="orderAmt"/>
    </resultMap>

    <!-- 查询符合自动开票条件的订单发票列表 -->
    <select id="queryAutoInvoiceList" resultMap="OrderInvoiceWithCompanyResultMap">
        SELECT
        oi.*,
        o.company_code,
        o.agent_code,
        o.agent_name,
        o.order_confirmation_status,
        o.end_date,
        o.id as orderId,
        o.sale_currency,
        o.order_amt
        FROM
        o_order_invoice oi
        JOIN
        o_order o ON oi.order_code = o.order_code
        <where>
        <if test="startDate != null and endDate!=null">
            AND o.end_date BETWEEN #{startDate} AND #{endDate}
        </if>

        <if test="invoiceStatusList != null and invoiceStatusList.size > 0">
            AND oi.invoice_status IN
            <foreach collection="invoiceStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="invoiceTypeList != null and invoiceTypeList.size > 0">
            AND oi.invoice_type IN
            <foreach collection="invoiceTypeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="confirmStatusList != null and confirmStatusList.size > 0">
            AND o.order_confirmation_status IN
            <foreach collection="confirmStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="autoOpenInvoiceStatus != null">
            AND oi.auto_open_invoice_status = #{autoOpenInvoiceStatus}
        </if>
        <if test="companyCode != null and companyCode != ''">
            AND o.company_code = #{companyCode}
        </if>
        <if test="orderInvoiceId != null">
            AND oi.id = #{orderInvoiceId}
        </if>
        </where>
    </select>

    <!-- 查询符合自动下载条件的订单发票列表 -->
    <select id="queryAutoDownloadInvoiceList" resultMap="BaseResultMap">
        SELECT
            oi.*
        FROM
            o_order_invoice oi
        WHERE 1=1
        <if test="invoiceStatusList != null and invoiceStatusList.size > 0">
            AND oi.invoice_status IN
            <foreach collection="invoiceStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>
</mapper>
