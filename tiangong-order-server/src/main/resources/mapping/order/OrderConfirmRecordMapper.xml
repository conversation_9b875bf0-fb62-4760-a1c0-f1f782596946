<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.OrderConfirmRecordMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderConfirmRecordPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="confirmation_code" jdbcType="VARCHAR" property="confirmationCode"/>
        <result column="confirmation_content" jdbcType="VARCHAR" property="confirmationContent"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
    </resultMap>

    <select id="queryConfirmingPerson" parameterType="java.lang.Integer"
            resultType="com.tiangong.order.domain.OrderConfirmRecordPO">
        SELECT * FROM o_order_confirm_record
        WHERE order_id = #{orderId}
        ORDER BY id DESC
        LIMIT 1
    </select>
</mapper>