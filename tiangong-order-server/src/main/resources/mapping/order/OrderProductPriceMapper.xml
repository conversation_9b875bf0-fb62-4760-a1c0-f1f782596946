<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.OrderProductPriceMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderProductPricePO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="sale_date" jdbcType="DATE" property="saleDate"/>
        <result column="sale_price" jdbcType="DECIMAL" property="salePrice"/>
    </resultMap>

    <select id="selectOrderProductTotalPrice" resultType="java.math.BigDecimal">
        select sum(t.sale_price) from o_order_product_price t where t.order_id = #{orderId}
    </select>

    <select id="selectOrderPriceListByOrderCode" resultType="com.tiangong.order.remote.response.PriceResponseDTO">
        select
            t1.room_number roomNumber,
            t1.sale_date saleDate,
            t1.sale_price salePrice
        from o_order_product_price t1
        left join o_order t2 on t2.id = t1.order_id
        where t1.sale_date is not null and t2.order_code = #{orderCode}
    </select>
</mapper>