<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.order.mapper.OrderCheckInfoMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="CheckInfoMap" type="com.tiangong.order.remote.dto.OrderCheckInfoDTO">
        <result property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="coOrderCode" column="coOrderCode"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="checkInState" column="check_in_state"/>
        <result property="guestName" column="guest_name"/>
        <result property="currency" column="currency"/>
        <result property="roomNumber" column="room_number"/>
        <result property="supplyOrderCode" column="supply_order_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <collection property="checkInDetailList" javaType="java.util.List" resultMap="DetaiMap"/>
    </resultMap>

    <resultMap id="DetaiMap" type="com.tiangong.order.remote.dto.OrderCheckDetailDTO">
        <result property="id" column="bid"/>
        <result property="orderCode" column="borderCode"/>
        <result property="checkInfoId" column="check_info_id"/>
        <result property="saleDate" column="sale_date"/>
        <result property="salePrice" column="sale_price"/>
        <result property="refundPrice" column="refund_price"/>
        <result property="supplyOrderCode" column="bSupplyOrderCode"/>
        <result property="createdBy" column="bCreatedBy"/>
        <result property="createdDt" column="bCreatedDt"/>
        <result property="updatedBy" column="bUpdatedBy"/>
        <result property="updatedDt" column="bUpdateDt"/>
    </resultMap>

    <select id="orderCheckInfoList" resultMap="CheckInfoMap">
        select
        a.id,
        a.order_code,
        a.start_date,
        a.end_date,
        a.check_in_state,
        a.guest_name,
        a.room_number,
        a.supply_order_code,
        a.currency,
        a.created_by,
        a.created_dt,
        a.updated_by,
        a.updated_dt,
        b.id bid,
        b.order_code borderCode,
        b.check_info_id,
        b.sale_date,
        b.sale_price,
        b.refund_price,
        b.supply_order_code bSupplyOrderCode,
        b.created_by bCreatedBy,
        b.created_dt bCreatedDt,
        b.updated_by bUpdatedBy,
        b.updated_dt bUpdateDt
        from
        o_order_check_info a left join o_order_check_detail b on a.id = b.check_info_id and b.deleted = 0
        where a.order_code = #{orderCode}
        and a.deleted = 0
        order by b.sale_date
    </select>

    <select id="orderCheckInfoListDhub" resultMap="CheckInfoMap">
        select
        a.id,
        a.order_code,
        o.channel_order_code coOrderCode,
        a.start_date,
        a.end_date,
        a.check_in_state,
        a.guest_name,
        a.room_number,
        a.supply_order_code,
        a.currency,
        a.created_by,
        a.created_dt,
        a.updated_by,
        a.updated_dt,
        b.id bid,
        b.order_code borderCode,
        b.check_info_id,
        b.sale_date,
        b.sale_price,
        b.refund_price,
        b.supply_order_code bSupplyOrderCode,
        b.created_by bCreatedBy,
        b.created_dt bCreatedDt,
        b.updated_by bUpdatedBy,
        b.updated_dt bUpdateDt
        from
        o_order_check_info a left join o_order_check_detail b on a.id = b.check_info_id and b.deleted = 0
        left join o_order o on a.order_code = o.order_code
        where 1=1
        <if test="fcOrderCode != null and fcOrderCode != ''">
            and o.order_code = #{fcOrderCode}
        </if>
        <if test="coOrderCode != null and coOrderCode != ''">
            and o.channel_order_code = #{coOrderCode}
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND o.channel_code = #{channelCode}
        </if>
        <if test="agentCode != null and agentCode != ''">
            AND o.agent_code = #{agentCode}
        </if>
        and a.deleted = 0
        order by b.sale_date
    </select>
</mapper>