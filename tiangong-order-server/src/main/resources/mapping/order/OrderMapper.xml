<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.OrderMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="order_confirmation_status" jdbcType="INTEGER" property="orderConfirmationStatus"/>
        <result column="sale_currency" jdbcType="INTEGER" property="saleCurrency"/>
        <result column="order_amt" jdbcType="DECIMAL" property="orderAmt"/>
        <result column="sale_price" jdbcType="DECIMAL" property="salePrice"/>
        <result column="settlement_type" jdbcType="INTEGER" property="settlementType"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="agent_code" jdbcType="VARCHAR" property="agentCode"/>
        <result column="agent_name" jdbcType="VARCHAR" property="agentName"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="channel_order_code" jdbcType="VARCHAR" property="channelOrderCode"/>
        <result column="order_owner_user" jdbcType="VARCHAR" property="orderOwnerUser"/>
        <result column="order_owner_name" jdbcType="VARCHAR" property="orderOwnerName"/>
        <result column="lock_user" jdbcType="VARCHAR" property="lockUser"/>
        <result column="lock_name" jdbcType="VARCHAR" property="lockName"/>
        <result column="lock_time" jdbcType="TIMESTAMP" property="lockTime"/>
        <result column="profit" jdbcType="DECIMAL" property="profit"/>
        <result column="is_manual_order" jdbcType="INTEGER" property="isManualOrder"/>
        <result column="is_substituted" jdbcType="INTEGER" property="isSubstituted"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="merchant_bm" jdbcType="VARCHAR" property="merchantBm"/>
        <result column="refund_fee" jdbcType="DECIMAL" property="refundFee"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="hotel_id" jdbcType="INTEGER" property="hotelId"/>
        <result column="hotel_name" jdbcType="VARCHAR" property="hotelName"/>
        <result column="room_name" jdbcType="VARCHAR" property="roomName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="room_qty" jdbcType="INTEGER" property="roomQty"/>
        <result column="guest" jdbcType="VARCHAR" property="guest"/>
        <result column="breakfast_qty" jdbcType="INTEGER" property="breakfastQty"/>
        <result column="confirmation_code" jdbcType="VARCHAR" property="confirmationCode"/>
        <result column="supply_order_confirmation_status" jdbcType="VARCHAR" property="supplyOrderConfirmationStatus"/>
        <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime"/>
        <result column="marked_status" jdbcType="INTEGER" property="markedStatus"/>
        <result column="modification_status" jdbcType="INTEGER" property="modificationStatus"/>
        <result column="guarantee_flag" jdbcType="INTEGER" property="guaranteeFlag"/>
        <result column="conversion_product_id" jdbcType="VARCHAR" property="conversionProductId"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt"/>
        <result column="hourly" property="hourly" jdbcType="INTEGER"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    </resultMap>

    <resultMap id="OtaOrderMap" type="com.tiangong.order.remote.response.OtaOrderDTO">
        <id column="id" property="orderId"/>
        <id column="order_code" property="orderCode"/>
        <result column="channel_order_code" property="channelOrderCode"/>
        <result column="pay_status" property="payStatus"/>
        <result column="hotel_id" property="hotelId"/>
        <result column="hotel_name" property="hotelName"/>
        <result column="room_id" property="roomId"/>
        <result column="room_name" property="roomName"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="room_qty" property="roomQty"/>
        <result column="sale_price" property="salePrice"/>
        <result column="order_confirmation_status" property="status"/>
        <result column="start_date" property="checkInDate"/>
        <result column="end_date" property="checkOutDate"/>
        <result column="start_time" property="checkInTime"/>
        <result column="end_time" property="checkOutTime"/>
        <result column="earliest_arrive_time" property="earliestArriveTime"/>
        <result column="latest_arrive_time" property="latestArriveTime"/>
        <result column="bed_type" property="bedType"/>
        <result column="request_type" property="modificationStatus"/>
        <result column="breakfast_qty" property="breakfastQty"/>
        <result column="refuse_cancelled_reason" property="refuseCancelledReason"/>
        <result column="cancelled_status" property="cancelledStatus"/>
        <result column="cancellation_term" property="cancellationTerm"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="created_dt" property="createdDate"/>
        <result column="agent_code" property="agentCode"/>
        <result column="hourly" property="hourly"/>
        <result column="sale_currency" property="saleCurrency"/>
        <result column="conversion_product_id" property="conversionProductId"/>
        <result column="agent_name" property="agentName"/>
        <result column="guest" property="guest"/>
        <collection property="confirmNums" ofType="java.lang.String">
            <result column="confirmation_code"/>
        </collection>
    </resultMap>

    <resultMap id="QueryOrderListMap" type="com.tiangong.order.remote.response.OrderSimpleDTO">
        <id column="orderId" property="orderId"/>
        <id column="hotelId" property="hotelId"/>
        <id column="hourly" property="hourly"/>
        <id column="orderCode" property="orderCode"/>
        <id column="provinceCode" property="provinceCode"/>
        <id column="countryCode" property="countryCode"/>
        <id column="cnType" property="cnType"/>
        <result column="createdDt" property="createdDt"/>
        <result column="channelCode" property="channelCode"/>
        <result column="channelName" property="channelName"/>
        <result column="agentName" property="agentName"/>
        <result column="hotelName" property="hotelName"/>
        <result column="roomName" property="roomName"/>
        <result column="productName" property="productName"/>
        <result column="startDate" property="startDate"/>
        <result column="endDate" property="endDate"/>
        <result column="startTime" property="startTime"/>
        <result column="endTime" property="endTime"/>
        <result column="guest" property="guest"/>
        <result column="roomQty" property="roomQty"/>
        <result column="orderAmt" property="orderAmt"/>
        <result column="saleRate" property="saleRate"/>
        <result column="orderSettlementStatus" property="orderSettlementStatus"/>
        <result column="orderSettlementType" property="orderSettlementType"/>
        <result column="saleCurrency" property="saleCurrency"/>
        <result column="orderConfirmationStatus" property="orderConfirmationStatus"/>
        <result column="supplyOrderConfirmationStatus" property="supplyOrderConfirmationStatus"/>
        <result column="orderOwnerName" property="orderOwnerName"/>
        <result column="markedStatus" property="markedStatus"/>
        <result column="confirmationCode" property="confirmationCode"/>
        <result column="lockName" property="lockName"/>
        <result column="markedStatus" property="markedStatus"/>
        <result column="isShownOnSupplyOrder" property="isShownOnSupplyOrder"/>
        <result column="modificationRequestStatus" property="modificationRequestStatus"/>
        <result column="instantConfimationStatus" property="instantConfimationStatus"/>
        <result column="isManualOrder" property="isManualOrder"/>
        <result column="lockStatus" property="lockStatus"/>
        <result column="isAbnormal" property="isAbnormal"/>
        <result column="nightQty" property="nightQty"/>
        <result column="payMethod" property="payMethod"/>
        <result column="earliestArriveTime" property="earliestArriveTime"/>
        <result column="latestArriveTime" property="latestArriveTime"/>
        <result column="isSubstituted" property="isSubstituted"/>
        <result column="productLabel" property="productLabel"/>
        <result column="productLabelType" property="productLabelType"/>
        <result column="payAtHotelCurrency" property="payAtHotelCurrency"/>
        <result column="payAtHotelFee" property="payAtHotelFee"/>
        <result column="isEarlyMorningRoomOrder" property="isEarlyMorningRoomOrder"/>
        <result column="isVipOrder" property="isVipOrder"/>
        <result column="quickProcessingSwitch" property="quickProcessingSwitch"/>
        <result column="isMixedPay" property="isMixedPay"/>
        <result column="isRefund" property="isRefund"/>
        <result column="payStatus" property="payStatus"/>
        <result column="supplierLabel" property="supplierLabel"/>
        <result column="salePrice" property="salePrice"/>
        <result column="taskOrderCode" property="taskOrderCode"/>
        <result column="agentCode" property="agentCode"/>
        <!-- 售价 -->
        <association property="salePrice" select="com.tiangong.order.mapper.OrderProductPriceMapper.selectOrderProductTotalPrice"
                     column="orderId" javaType="java.math.BigDecimal"/>
        <!-- 供货单信息-->
        <collection property="supplyOrderInfos" select="com.tiangong.order.mapper.SupplyOrderMapper.selectSupplyOrderInfos"
                    column="orderId" javaType="ArrayList" ofType="com.tiangong.order.remote.request.SupplyOrderInfo">
            <result column="supplyOrderCode" property="supplyOrderCode"/>
            <result column="supplierName" property="supplierName"/>
            <result column="confirmationStatus" property="confirmationStatus"/>
            <result column="supplyOrderAmt" property="supplyOrderAmt"/>
            <result column="baseCurrency" property="baseCurrency"/>
        </collection>

        <!-- 入住人信息-->
        <collection property="guestList" select="com.tiangong.order.mapper.OrderMapper.selectGuestList"
                    column="orderId" javaType="ArrayList" ofType="com.tiangong.dto.order.OrderGuestDTO">
            <result column="orderId" property="orderId"/>
            <result column="name" property="name"/>
            <result column="firstName" property="firstName"/>
            <result column="lastName" property="lastName"/>
            <result column="nationality" property="nationality"/>
            <result column="mobileNo" property="mobileNo"/>
            <result column="countryCode" property="countryCode"/>
        </collection>
    </resultMap>

    <select id="queryOrderList" parameterType="com.tiangong.order.remote.request.QueryOrderListDTO"
            resultMap="QueryOrderListMap">
        SELECT
        o.id orderId,
        o.hotel_id hotelId,
        o.order_code orderCode,
        o.hourly hourly,
        o.created_dt createdDt,
        o.created_by as bookingPerson,
        o.channel_code channelCode,
        o.agent_name agentName,
        o.agent_code agentCode,
        o.hotel_name hotelName,
        o.room_name roomName,
        o.product_name productName,
        o.start_date startDate,
        o.end_date endDate,
        o.start_time startTime,
        o.end_time endTime,
        o.guest guest,
        o.room_qty roomQty,
        o.country_code countryCode,
        o.cn_type cnType,
        o.province_code provinceCode,
        if(
        (o.hourly=1),
        0.5,
        0+DATEDIFF( o.end_date, o.start_date )
        ) nightQty,
        o.order_amt orderAmt,
        o.sale_rate saleRate,
        o.settlement_type orderSettlementType,
        o.sale_currency saleCurrency,
        o.order_confirmation_status orderConfirmationStatus,
        o.order_owner_name orderOwnerName,
        o.confirmation_code confirmationCode,
        o.lock_name lockName,
        o.is_show_on_supply_order isShownOnSupplyOrder,
        o.marked_status markedStatus,
        o.modification_status modificationRequestStatus,
        o.instantConfirmation_status instantConfimationStatus,
        f.settlement_status orderSettlementStatus,
        o.is_manual_order isManualOrder,
        IF(o.lock_name is NULL, 0, 1) lockStatus,
        IF(o.is_abnormal is NULL, 0, o.is_abnormal) isAbnormal,
        o.channel_name channelName,
        o.pay_method as payMethod,
        o.earliest_arrive_time as earliestArriveTime,
        o.latest_arrive_time as latestArriveTime,
        o.is_substituted as isSubstituted,
        o.product_label as productLabel,
        o.product_label_type as productLabelType,
        IFNULL(o.is_early_morning_room_order, 0) as isEarlyMorningRoomOrder,
        o.pay_at_hotel_fee payAtHotelFee,
        o.pay_at_hotel_currency payAtHotelCurrency,
        o.is_vip_order isVipOrder,
        o.quick_processing_switch quickProcessingSwitch,
        o.is_mixed_pay isMixedPay,
        o.is_refund isRefund,
        o.supplier_label supplierLabel,
        o.pay_status payStatus,
        cwot.task_order_code taskOrderCode
        FROM
        o_order o
        LEFT JOIN o_order_finance f ON o.id = f.order_id
        LEFT JOIN t_order_change_word_order_task cwot ON cwot.order_code = o.order_code
        <if test="isCheckInDetailAbnormal != null and isCheckInDetailAbnormal == 1">
            left join o_supply_order os on o.id = os.order_id
        </if>
        <if test="isOrderInvoiceError != null">
            left join o_order_invoice oi on o.order_code = oi.order_code
        </if>
        WHERE o.company_code=#{companyCode}
        <if test="contactPhone != null and contactPhone != ''">
            and o.contact_phone like concat('%', #{contactPhone}, '%')
        </if>
        <if test="contactCountryCode != null and contactCountryCode != ''">
            and o.contact_country_code = #{contactCountryCode}
        </if>
        <if test="isCheckInDetailAbnormal != null and isCheckInDetailAbnormal == 1">
            AND os.is_check_in_detail_abnormal = 1
        </if>
        <if test="isOrderInvoiceError != null and isOrderInvoiceError == 1">
            AND (oi.e_push_address_config_status = 1 OR oi.e_create_bill_status = 1 OR oi.e_push_address_invalid_status = 1 OR oi.e_invoice_file_missing_status = 1
                 OR oi.e_invoice_apply_failed_status = 1 OR oi.e_file_download_failed_status = 1 OR oi.e_email_send_failed_status = 1)
        </if>
        <if test="isOrderInvoiceError != null and isOrderInvoiceError == 0">
            AND (oi.order_code IS NULL OR (oi.e_push_address_config_status = 0 AND oi.e_create_bill_status = 0 AND oi.e_push_address_invalid_status = 0 AND oi.e_invoice_file_missing_status = 0
                 AND oi.e_invoice_apply_failed_status = 0 AND oi.e_file_download_failed_status = 0 AND oi.e_email_send_failed_status = 0))
        </if>
        <if test="productType != null">
            AND o.hourly = #{productType}
        </if>
        <if test="productLabel != null">
            AND o.product_label = #{productLabel}
        </if>
        <if test="productLabelType != null">
            AND o.product_label_type = #{productLabelType}
        </if>
        <if test="orderCode != null and orderCode !=''">
            AND o.order_code LIKE concat(concat('%',#{orderCode}),'%')
        </if>
        <if test="guest != null and guest !=''">
            AND o.guest LIKE concat(concat('%',#{guest}),'%')
        </if>
        <if test="channelOrderCode != null and channelOrderCode !=''">
            AND o.channel_order_code LIKE concat(concat('%',#{channelOrderCode}),'%')
        </if>
        <if test="payMethod != null">
            AND o.pay_method = #{payMethod}
        </if>
        <if test="payStatus != null">
            AND o.pay_status = #{payStatus}
        </if>
        <if test="searchValue != null and searchValue !=''">
            AND (o.order_code LIKE concat(concat('%',#{searchValue}),'%')
            or o.guest LIKE concat(concat('%',#{searchValue}),'%')
            or o.hotel_name LIKE concat(concat('%',#{searchValue}),'%'))
        </if>
        <choose>
            <when test="orderConfirmationStatus == 5">
                <if test="channel == 0">
                    AND o.modification_status in (1)
                </if>
                <if test="channel == 1">
                    AND o.modification_status in (1,4,6)
                </if>
            </when>
            <when test="orderConfirmationStatus == 0 || orderConfirmationStatus == 1">
                AND o.order_confirmation_status=#{orderConfirmationStatus}
<!--                <if test="channel == 0">-->
<!--                    AND o.modification_status not in (1,4)-->
<!--                </if>-->
                <if test="channel == 1">
                    AND o.modification_status not in (1,4,6)
                </if>
            </when>
            <otherwise>
                <if test="orderConfirmationStatus != null">
                    AND o.order_confirmation_status=#{orderConfirmationStatus}
                </if>
            </otherwise>
        </choose>
        <choose>
            <when test="dateQueryType==1">
                <if test="startDate != null and startDate !=''">
                    AND o.start_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate != null and startDate !=''">
                    AND o.end_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.end_date &lt;= #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null and startDate !=''">
                    AND o.created_dt &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate !=''">
                    AND o.created_dt &lt; #{endDate}
                </if>
            </otherwise>
        </choose>
        <choose>
            <when test="hotelId!=null">
                AND o.hotel_id=#{hotelId}
            </when>
            <otherwise>
                <if test="hotelName!=null and hotelName!=''">
                    AND o.hotel_name LIKE concat(concat('%',#{hotelName}),'%')
                </if>
            </otherwise>
        </choose>
        <if test="supplyOrderConfirmationStatus != null">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.confirmation_status=#{supplyOrderConfirmationStatus})
        </if>
        <if test="channelCode != null and channelCode !=''">
            AND o.channel_code = #{channelCode}
        </if>
        <if test="channelName != null and channelName != '' ">
            AND o.channel_name = #{channelName}
        </if>
        <if test="agentName != null and agentName !=''">
            AND o.agent_name like concat('%', #{agentName},'%')
        </if>
        <if test="agentCode != null and agentCode !=''">
            AND o.agent_code = #{agentCode}
        </if>
        <if test="orderSettlementType != null">
            AND o.settlement_type=#{orderSettlementType}
        </if>
        <if test="orderSettlementStatus != null">
            AND f.settlement_status = #{orderSettlementStatus}
        </if>
        <if test="confirmationCode != null and confirmationCode !=''">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.confirmation_code LIKE concat(concat('%',#{confirmationCode}),'%'))
        </if>
        <if test="markedStatus != null">
            AND o.marked_status=#{markedStatus}
        </if>
        <if test="isMyOrder == 1">
            AND o.order_owner_user=#{operator}
        </if>
        <if test="specialOrderStatus == 0">
            AND o.instantConfirmation_status=1
        </if>
        <if test="specialOrderStatus == 1">
            AND o.modification_status=3
        </if>
        <if test="isAbnormal == 1">
            AND o.is_abnormal=1
        </if>
        <if test="eCancelSupplyOrder == 1">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.e_cancel_supply_order = 1)
        </if>
        <if test="abnormalOrder != null and abnormalOrder == 6 ">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.cancel_exception_flag = 1)
        </if>
        <if test="specialOrderStatus == 2">
            AND o.order_confirmation_status = 2 AND o.modification_status = 0
        </if>
        <if test="operator != null and operator != '' ">
            AND o.created_by like concat('%',#{operator},'%')
        </if>
        <if test="cnType != null">
            AND o.cn_type = #{cnType}
        </if>
        <if test="payStatus != null">
            AND o.pay_status = #{payStatus}
        </if>
        <if test="isVipOrder != null and isVipOrder != '' ">
            AND o.is_vip_order = #{isVipOrder}
        </if>
        <if test="travelType != null">
            AND o.travel_type = #{travelType}
        </if>
        <if test="userAccount != null and userAccount != '' ">
            AND o.user_account = #{userAccount}
        </if>
        <if test="abnormalOrder != null and abnormalOrder == 5 ">
            AND ((o.modification_status = #{abnormalOrder}
                 AND o.order_confirmation_status = 0)
                 OR o.push_order_status = 1)
        </if>
        <if test="supplierLabel != null">
            AND o.supplier_label = #{supplierLabel}
        </if>
        <if test="purchaseManagerId != null ">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.merchant_pm = #{purchaseManagerId})
        </if>
        <if test="supplyOrderSettlementType != null">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.settlement_type = #{supplyOrderSettlementType})
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.supplier_code = #{supplierCode})
        </if>
        <choose>
            <when test="supplyOrderSettlementStatus != null and supplyOrderSettlementStatus != ''">
                <if test="supplyOrderSettlementStatus == 0">
                    AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND EXISTS
                    (select 1 from  o_supply_order_finance sf WHERE t.id = sf.supply_order_id AND
                    sf.settlement_status = 0 AND sf.finance_type = 0))
                </if>
            </when>
        </choose>
        <if test="supplyOrderCode != null and supplyOrderCode !=''">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.supply_order_code like concat(concat(#{supplyOrderCode}),'%'))
        </if>
        <if test="supplierOrderCode != null and supplierOrderCode !=''">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.supplier_order_code like concat(concat(#{supplierOrderCode}),'%'))
        </if>
        <if test="invoiceStatus != null">
            AND EXISTS (SELECT * FROM o_order_invoice ooi WHERE ooi.order_code = o.order_code AND ooi.invoice_status
            = #{invoiceStatus})
        </if>
        <if test="isCheckout != null and isCheckout == 1">
            AND EXISTS (SELECT 1 FROM t_order_change_word_order_task cwot WHERE cwot.order_code = o.order_code and cwot.status != 2)
        </if>
        <if test="isVccInformPayError != null and isVccInformPayError == 1">
            AND EXISTS(select 1 from o_supply_order t WHERE o.id = t.order_id AND t.vcc_send_status = 2)
        </if>
        <if test="supplyOrderSettlementStatus == 1">
            AND NOT EXISTS
            (
            SELECT
            1
            FROM
            o_supply_order_finance sf,
            o_supply_order oo
            WHERE
            oo.id = sf.supply_order_id
            AND oo.order_id = o.id
            AND sf.settlement_status = 0
            AND sf.finance_type = 0
            )
        </if>
        <if test="isManualOrder != null">
            AND o.is_manual_order = #{isManualOrder}
        </if>
        <if test="supplyOrderPaymentMethod != null">
            AND EXISTS
            (
            SELECT
            1
            FROM
            o_supply_order_non_vcc_auto_bill_info t,
            o_supply_order so
            WHERE
                so.order_id = o.id
            AND so.id = t.supply_order_id
            AND t.payment_method = #{supplyOrderPaymentMethod}
            )
        </if>
        <if test="isCheckInDetailAbnormal != null and isCheckInDetailAbnormal == 1">
            group by o.order_code
        </if>
        ORDER BY o.id DESC
    </select>

    <select id="queryOrderStatistics" parameterType="com.tiangong.order.remote.request.QueryOrderStatisticsDTO"
            resultType="com.tiangong.order.remote.response.OrderStatisticsDTO">
        SELECT
        COUNT(1) orderTotal,
        SUM(if(order_confirmation_status=0 AND o.modification_status not in(1,4),1,0))pendingOrderQty,
        SUM(if((select count(1) from o_supply_order os where os.order_id = o.id and os.confirmation_status = 0) > 0,1,0))pendingSupplyOrderQty,
        SUM(if(order_owner_user=#{operator},1,0))myOrderQty,
        SUM(if(marked_status=1,1,0))markedOrderQty,
        SUM(if(instantConfirmation_status=1 AND order_confirmation_status=0,1,0))instantConfimationQty,
        SUM(if(modification_status=3,1,0))doubleConfirmationQty,
        COUNT(if(o.order_confirmation_status = 2 AND o.modification_status= 0, 1, NULL)) refusedOrderQty,
        COUNT(if((o.modification_status = 5 AND o.order_confirmation_status = 0) or (o.push_order_status=1), 1, NULL )) waitConfirmAbnormalOrder,
        SUM(if((select count(1) from o_supply_order os where os.order_id = o.id and os.cancel_exception_flag = 1) > 0,1,0)) cancelAbnormalOrder,
         SUM(if((select count(1) from o_supply_order os where os.order_id = o.id and os.e_cancel_supply_order = 1) > 0,1,0)) eCancelSupplyOrderQty,
        SUM(if((select count(1) from o_supply_order os where os.order_id = o.id and os.vcc_send_status = 2) > 0,1,0)) vccInformPayErrorQty,
        SUM(
            IFNULL(
                (SELECT
                    SUM(
                        IF(oi.e_create_bill_status = 1, 1, 0) +
                        IF(oi.e_push_address_invalid_status = 1, 1, 0) +
                        IF(oi.e_invoice_file_missing_status = 1, 1, 0) +
                        IF(oi.e_invoice_apply_failed_status = 1, 1, 0) +
                        IF(oi.e_file_download_failed_status = 1, 1, 0) +
                        IF(oi.e_email_send_failed_status = 1, 1, 0)
                    )
                FROM o_order_invoice oi
                WHERE oi.order_code = o.order_code
                ),
                0
            )
        ) orderInvoiceErrorQty
         FROM o_order o
         WHERE o.company_code = #{companyCode}
        <!-- COUNT(if(o.modification_status = 6, 1, NULL)) cancelAbnormalOrder,-->
         <!-- AND o.created_dt >= date_add(CURRENT_DATE(), interval -90 day)
         AND o.created_dt &lt;= date_add(CURRENT_DATE(), interval 1 day) -->
    </select>

    <select id="queryOrderStatisticsB2B" parameterType="com.tiangong.dto.order.request.QueryOrderStatisticsDTOB2BRequest"
            resultType="com.tiangong.dto.order.response.OrderStatisticsDTOB2BResponse">
        SELECT
        COUNT(1) orderTotal,
        IFNULL(SUM(if(order_confirmation_status=0 AND modification_status not in(1,4,6),1,0)), 0) pendingOrderQty,
        IFNULL(SUM(if(order_confirmation_status=1 AND modification_status not in(1,4,6),1,0)), 0) confirmOrderQty,
        IFNULL(SUM(if(order_confirmation_status=2,1,0)), 0) cancelOrderQty,
        IFNULL(SUM(if(order_confirmation_status=3,1,0)), 0) successOrderQty
        FROM o_order o
        WHERE o.company_code = #{companyCode}
        <if test="agentCode != null and agentCode != '' ">
            AND o.agent_code = #{agentCode}
        </if>
        <if test="userAccount != null and userAccount != '' ">
            AND o.user_account = #{userAccount}
        </if>
        <if test="channelName != null and channelName != '' ">
            AND o.channel_name = #{channelName}
        </if>
        <if test="startDate != null and startDate != '' ">
            AND o.created_dt &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            AND o.created_dt &lt; #{endDate}
        </if>
        <if test="searchValue != null and searchValue != '' ">
            AND (o.order_code LIKE concat('%',#{searchValue},'%')
            or o.guest LIKE concat('%',#{searchValue},'%')
            or o.hotel_name LIKE concat('%',#{searchValue},'%'))
        </if>
    </select>

    <select id="queryCancelOrderStatisticsB2B" parameterType="com.tiangong.dto.order.request.QueryOrderStatisticsDTOB2BRequest"
            resultType="java.lang.Integer">
        SELECT
        count( 1 ) cancelOrderNum,
        order_confirmation_status
        FROM
        o_order
        WHERE
        company_code = #{companyCode}
        AND modification_status in (1,4,6)
        <if test="agentCode != null and agentCode != '' ">
            AND agent_code = #{agentCode}
        </if>
        <if test="userAccount != null and userAccount != '' ">
            AND user_account = #{userAccount}
        </if>
        <if test="channelName != null and channelName !=''">
            AND channel_name = #{channelName}
        </if>
        <if test="startDate != null and startDate != '' ">
            AND created_dt &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            AND created_dt &lt; #{endDate}
        </if>
        <if test="searchValue != null and searchValue != '' ">
            AND (order_code LIKE concat('%',#{searchValue},'%')
            or guest LIKE concat('%',#{searchValue},'%')
            or hotel_name LIKE concat('%',#{searchValue},'%'))
        </if>
    </select>

    <select id="queryOnTimeOrderList" parameterType="com.tiangong.order.remote.request.QueryOnTimeOrderListDTO"
            resultType="com.tiangong.order.remote.response.OnTimeOrderDTO">
        SELECT
        o.id orderId,
        o.order_code orderCode,
        o.agent_code agentCode,
        o.agent_name agentName,
        o.order_amt receivableAmt,
        f.received_amt receivedAmt,
        f.unreceived_amt unreceivedAmt,
        f.unconfirmed_received_amt unconfirmedReceivedAmt,
        f.unconfirmed_paid_amt unconfirmedPaidAmt,
        f.settlement_date settlementDate,
        if(ISNULL(f.real_settlement_date), if(DATEDIFF(CURDATE(),f.settlement_date)&gt;0,DATEDIFF(CURDATE(),f.settlement_date),0),
        if(DATEDIFF(f.real_settlement_date,f.settlement_date)&gt;0,DATEDIFF(f.real_settlement_date,f.settlement_date),0))
        overdueDays,
        f.settlement_status settlementStatus,
        o.sale_currency saleCurrency
        FROM
        o_order o,o_order_finance f
        WHERE o.id=f.order_id
        AND o.company_code=#{companyCode}
        AND o.settlement_type=3
        <choose>
            <when test="agentCode!=null and agentCode!=''">
                AND o.agent_code=#{agentCode}
            </when>
            <otherwise>
                <if test="agentName != null and agentName != ''">
                    and o.agent_name LIKE concat(concat('%',#{agentName}),'%')
                </if>
            </otherwise>
        </choose>
        <if test="startDate != null and startDate !=''">
            AND f.settlement_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate !=''">
            AND f.settlement_date &lt;= #{endDate}
        </if>
        <if test="orderCode != null and orderCode !=''">
            AND o.order_code LIKE concat(concat('%',#{orderCode}),'%')
        </if>
        <if test="settlementStatus !=null and settlementStatus != ''">
            AND f.settlement_status = #{settlementStatus}
        </if>
        <if test="overdueStatus !=null and overdueStatus != ''">
            <if test="overdueStatus == 0">
                AND DATEDIFF(IFNULL(f.real_settlement_date, CURDATE()),f.settlement_date)&lt;=0
            </if>
            <if test="overdueStatus == 1">
                AND DATEDIFF(IFNULL(f.real_settlement_date, CURDATE()),f.settlement_date)>0
            </if>
        </if>
        AND (f.received_amt != 0 OR f.unreceived_amt != 0)
        ORDER BY o.id DESC
    </select>

    <select id="queryBetweenDate" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT sale_date
        FROM t_pro_sale_date
        WHERE sale_date
        BETWEEN
        #{begin}
        AND
        #{end}
    </select>

    <select id="querySettlementStatus" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT f.settlement_status
        FROM o_order_finance f
        WHERE f.order_id = #{orderId}
    </select>

    <select id="queryOrderRequest" parameterType="java.lang.Integer"
            resultType="com.tiangong.order.remote.response.OrderRequestDTO">
        SELECT o.request_type requestType,
        o.handle_result handledResult
        FROM o_order_request o
        WHERE o.order_id = #{orderId}
    </select>

    <select id="queryOtaOrder" resultMap="OtaOrderMap">
        SELECT
        o.id,
        o.order_confirmation_status,
        o.pay_status,
        o.hourly,
        o.order_code,
        o.hotel_id,
        o.hotel_name,
        o.room_id,
        o.room_name,
        o.product_id,
        o.product_name,
        o.room_qty,
        o.channel_order_code,
        so.confirmation_code,
        o.start_date,
        o.end_date,
        o.start_time,
        o.end_time,
        o.earliest_arrive_time,
        o.latest_arrive_time,
        o.bed_type,
        o.breakfast_qty,
        ou.request_type,
        o.cancelled_reason,
        a.remark refuse_cancelled_reason,
        ou.handle_result cancelled_status,
        ooe.cancellation_term,
        o.sale_price,
        o.contact_name,
        o.contact_phone,
        o.conversion_product_id,
        o.created_dt,
        o.agent_code,
        o.sale_currency,
        o.agent_name,
        o.guest
        FROM o_order o
        LEFT JOIN o_order_request ou ON ou.order_id = o.id AND ou.request_type = 0
        LEFT JOIN o_supply_order so ON so.order_id = o.id
        LEFT JOIN o_order_extend ooe ON ooe.order_id = o.id
        LEFT JOIN (SELECT * FROM o_order_request oor WHERE oor.request_type = 0 AND oor.handle_result = 2)a ON
        a.order_id = o.id
        <where>
            <trim prefixOverrides="AND">
                <if test="channelOrderCode != null and channelOrderCode != ''">
                    AND o.channel_order_code = #{channelOrderCode}
                </if>
                <if test="orderId != null and orderId != ''">
                    AND o.order_code = #{orderId}
                </if>
                <if test="channelCode != null and channelCode != ''">
                    AND o.channel_code = #{channelCode}
                </if>
                <if test="agentCode != null and agentCode != ''">
                    AND o.agent_code = #{agentCode}
                </if>
            </trim>
        </where>
    </select>

    <select id="queryOnSaleProduct" resultType="com.tiangong.product.dto.ProductDTO">
        SELECT p.product_id productId,
        r.comparison_type comparisonType,
        r.reservation_limit_nights reservationLimitNights,
        r.reservation_advance_days reservationAdvanceDays,
        r.reservation_due_time reservationDueTime,
        r.reservation_limit_rooms reservationLimitRooms,
        p.breakfast_qty breakfastQty
        FROM t_pro_product p
        LEFT JOIN t_pro_restrict r ON r.product_id = p.product_id
        WHERE p.active = 1
        AND p.product_id = #{productId}
    </select>
    <!--订单自动设置列表-->
    <select id="orderAutomaticList" resultType="com.tiangong.order.remote.dto.SupplierListDTO"
            parameterType="java.util.Map">
        SELECT t1.org_type AS supplierType,
        t1.org_name AS supplierName,
        t2.is_automatic AS available,
        t2.suppliertime_type AS suppliertimeType,
        t1.org_code AS supplierCode,
        t2.companyCode AS companyCode,
        t2.channel_code AS channelCodeList,
        t2.id AS id,
        t2.supplier_start_time AS startTime,
        t2.supplier_stop_time AS endTime,
        t2.order_method AS orderMethod
        FROM t_org_organization t1
        LEFT JOIN t_org_company_supplier s ON t1.org_id = s.org_id
        LEFT JOIN t_org_order_automatic t2 ON t2.supplier_code = t1.org_code AND t2.companyCode = s.company_code
        where
        s.company_code = #{companyCode}
        AND s.available_status = 1
        <if test='available == "1"'>
            AND t2.is_automatic = #{available}
        </if>
        <if test='available == "0"'>
            AND (t2.is_automatic is null or t2.is_automatic = #{available})
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND t1.org_code = #{supplierCode}
        </if>
        order by t1.org_id desc
    </select>
    <!--添加自动发单渠道及时间-->
    <insert id="addSupplierAutoChannel" parameterType="com.tiangong.order.remote.dto.SupplierAutoChannelDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_org_order_automatic
        (is_automatic, suppliertime_type, supplier_code,
        channel_code, created_by, created_dt, updated_by,
        updated_dt, supplier_start_time, supplier_stop_time, companyCode,order_method,recipient_name,preorder_email,vcc_pay,vcc_email,company_name,remarks)
        VALUES (#{available}, #{suppliertimeType}, #{supplierCode},
        #{channelCodeList}, #{createdBy}, NOW(), #{updatedBy}, NULL,
        #{startTime}, #{endTime}, #{companyCode},#{orderMethod},#{recipientName},#{preorderEmail},#{vccPay},#{vccEmail},#{companyName},#{remarks})
    </insert>
    <!--修改自动发单渠道及时间-->
    <update id="updateSupplierAutoChannel" parameterType="com.tiangong.order.remote.dto.SupplierAutoChannelDto">
        UPDATE t_org_order_automatic t
        SET
        <if test="companyCode != null and companyCode != ''">
            t.companyCode = #{companyCode},
        </if>
        <if test="available != null">
            t.is_automatic = #{available},
        </if>
        <if test="suppliertimeType != null">
            t.suppliertime_type = #{suppliertimeType},
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            t.supplier_code = #{supplierCode},
        </if>
        <if test="channelCodeList != null and channelCodeList != ''">
            t.channel_code = #{channelCodeList},
        </if>
        <if test="startTime != null and startTime != ''">
            t.supplier_start_time = #{startTime},
        </if>
        <if test="endTime != null and endTime != ''">
            t.supplier_stop_time = #{endTime},
        </if>
        <if test="updatedBy != null and updatedBy != ''">
            t.updated_by = #{updatedBy},
        </if>
        <if test="orderMethod != null">
            t.order_method = #{orderMethod},
        </if>
        <if test="recipientName != null and recipientName != ''">
            t.recipient_name = #{recipientName},
        </if>
        <if test="preorderEmail != null and preorderEmail != ''">
            t.preorder_email = #{preorderEmail},
        </if>
        <if test="vccPay != null">
            t.vcc_pay = #{vccPay},
        </if>
        <if test="entrance == null">
            t.vcc_email = #{vccEmail},
            t.remarks = #{remarks},
        </if>
        t.company_name = #{companyName},
        t.updated_dt = SYSDATE()
        WHERE t.id = #{id}
    </update>

    <select id="querySupplierAutoChannel" parameterType="com.tiangong.order.remote.dto.SupplierAutoChannelDto" resultType="com.tiangong.order.remote.dto.SupplierAutoChannelDto">
        SELECT
                t.id as id,
                t.companyCode AS companyCode,
                t.is_automatic AS available,
                t.suppliertime_type AS suppliertimeType,
                t.supplier_code AS supplierCode,
                t.channel_code AS channelCodeList,
                t.supplier_start_time AS startTime,
                t.supplier_stop_time AS endTime,
                t.updated_by AS updatedBy,
                t.order_method AS orderMethod,
                t.recipient_name AS recipientName,
                t.preorder_email AS preorderEmail,
                t.vcc_pay AS vccPay,
                t.vcc_email AS vccEmail,
                t.company_name AS companyName,
                t.remarks AS remarks,
                t.updated_dt AS updatedDt
           FROM t_org_order_automatic t
        WHERE t.supplier_code = #{supplierCode}
    </select>

    <select id="queryAutoConfirmChannel" resultType="com.tiangong.order.remote.request.QueryAutoConfirmChannelDto">
        SELECT t.id AS id,
        t.companyCode AS companyCode,
        t.channels AS channelCodeList,
        t.automatic_status as automaticStatus
        from t_org_order_automatic_confirmation t
        where t.companyCode = #{companyCode}
    </select>

    <select id="queryAutoCancel" resultType="com.tiangong.order.remote.request.OrderAutoCancelDTO">
        SELECT t.id AS id,
        t.companyCode AS companyCode,
        t.automatic_status as automaticStatus
        from t_org_order_automatic_cancel t
        where t.companyCode = #{companyCode}
    </select>

    <update id="modifyAutoCancel" parameterType="com.tiangong.order.remote.request.OrderAutoCancelDTO">
        UPDATE t_org_order_automatic_cancel
        <trim prefix="set" suffixOverrides=",">
            automatic_status = #{automaticStatus}
        </trim>
        WHERE id = #{id}
    </update>

    <update id="modifyAutoConfirmChannel" parameterType="com.tiangong.order.remote.request.QueryAutoConfirmChannelDto">
        UPDATE t_org_order_automatic_confirmation
        <trim prefix="set" suffixOverrides=",">
            channels = #{channelCodeList},
            automatic_status = #{automaticStatus}
        </trim>
        WHERE id = #{id}
    </update>

    <insert id="addAutoConfirmChannel" parameterType="com.tiangong.order.remote.request.QueryAutoConfirmRequestDto">
        INSERT INTO t_org_order_automatic_confirmation (companyCode, channels, automatic_status)
        VALUES (#{companyCode}, #{channelCodeList}, #{automaticStatus})
    </insert>

    <select id="selectCompanyCode" resultType="java.lang.String">
        SELECT distinct channel_code AS channelCode
        FROM t_pro_m_channel
        where active = 1
    </select>

    <select id="querySupplier" resultType="com.tiangong.fuzzyquery.dto.FuzzySupplierDTO">
        SELECT
        r.org_code supplierCode,
        r.org_name supplierName,
        s.settlement_currency settlementCurrency,
        r.hotel_id hotelId,
        s.is_cached IsDirectlyConnectedSupplier,
        s.s_company_id supplierId
        FROM
        t_org_organization r,
        t_org_company_supplier s
        WHERE
        r.org_id = s.org_id
        AND s.company_code = #{companyCode}
        AND s.available_status = 1
        <if test="null != supplierName and supplierName != ''">
            AND r.org_name LIKE "%"#{supplierName}"%"
        </if>
        <if test="null != supplierCode and supplierCode != ''">
            AND r.org_code = #{supplierCode}
        </if>
        <if test="null != hotelId and hotelId != ''">
            AND exists (select 1 from t_pro_product p,t_pro_sale_status ss
            where p.product_id = ss.product_id
            and p.active = 1
            and ss.active = 1
            and p.supplier_code = r.org_code
            and p.hotel_id = #{hotelId}
            and ss.company_code = #{companyCode})
        </if>
        UNION
        SELECT
        r.org_code supplierCode,
        r.org_name supplierName,
        s.settlement_currency settlementCurrency,
        r.hotel_id hotelId,
        s.is_cached IsDirectlyConnectedSupplier,
        s.s_company_id supplierId
        FROM
        t_org_organization r,
        t_org_company_supplier s
        WHERE
        r.org_id = s.org_id
        AND s.company_code = #{companyCode}
        AND s.available_status = 1 AND s.is_cached = 0
        <if test="null != supplierName and supplierName != ''">
            AND r.org_name LIKE "%"#{supplierName}"%"
        </if>
        <if test="null != supplierCode and supplierCode != ''">
            AND r.org_code = #{supplierCode}
        </if>
    </select>

    <select id="checkExistOrder" resultType="java.lang.Integer">
        SELECT count(1)
        FROM o_order o
        WHERE o.channel_code = #{channelCode}
        AND o.channel_order_code = #{channelOrderCode}
    </select>

    <update id="batchUpDateStatusDone">
        update o_order set order_confirmation_status = 3,supply_order_confirmation_status = 3,is_abnormal = 0
        where order_confirmation_status = 1 and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpOrder">
        <foreach collection="list" item="update" separator=";">
            UPDATE o_order
            <set>
                <if test="update.orderAmt != null">
                    order_amt = #{update.orderAmt},
                </if>
                <if test="update.profit != null">
                    profit = #{update.profit},
                </if>
                <if test="update.additionalCharges != null and update.additionalCharges != ''">
                    additional_charges = #{update.additionalCharges},
                </if>
                <if test="update.updatedDt != null">
                    updated_dt = #{update.updatedDt},
                </if>
                <if test="update.updatedBy != null and update.updatedBy != ''">
                    updated_by = #{update.updatedBy},
                </if>
            </set>
            WHERE id = #{update.id}
        </foreach>
    </update>

    <select id="querySupplierAutoChannelId" resultType="java.lang.Integer">
        SELECT id
        FROM t_org_order_automatic
        WHERE supplier_code = #{supplierCode}
    </select>

    <select id="queryOrderByAgentCode" resultType="com.tiangong.order.domain.OrderPO">
        SELECT t1.id,t1.order_code,t1.start_date,t1.end_date,t1.channel_order_code,t1.agent_code,t1.agent_name,t1.hotel_name,t1.guest,t1.room_name,t1.product_name,t1.pay_method
        FROM o_order t1 WHERE NOT EXISTS (SELECT 1 FROM o_order_check_push_relation t2 WHERE t1.id = t2.order_id) AND (t1.end_date BETWEEN #{startDate} AND #{endDate})
                          AND t1.order_confirmation_status IN (1,3) AND t1.agent_code IN
        <foreach collection="agentCode" item="item" open="(" separator="," close=")">
           #{item}
        </foreach>
        <if test="payMethod != null">
            AND t1.pay_method = #{payMethod}
        </if>
    </select>

    <select id="queryOrderCheckInfo" resultType="com.tiangong.order.dto.OrderCheckDTO">
        SELECT t3.sale_price salePrice,t3.sale_date saleDate,t3.refund_price refundPrice,t2.start_date checkInDate,t2.end_date checkOutDate, t2.check_in_state checkInState,t2.id checkInfoId,
               t1.sale_price + IF(t1.additional_charges IS NULL,0,t1.additional_charges) orderSum,t1.pay_method payMethod
        FROM o_order t1,o_order_check_info t2, o_order_check_detail t3
        WHERE t1.order_code = t2.order_code AND t2.id = t3.check_info_id AND t2.deleted = 0 AND t3.deleted = 0 AND t1.order_code = #{orderCode} AND t1.agent_code = #{agentCode}
    </select>

    <select id="selectHotelRoomNightInfo" resultType="com.tiangong.order.dto.HotelHeatRoomNightDTO">
        SELECT t1.hotel_id,
               SUM(
                    IF(
                        (t1.hourly = 1), 0.5 * t1.room_qty,
                        0.0 + TIMESTAMPDIFF(DAY, t1.start_date, t1.end_date) * t1.room_qty
                    )
                ) nightQty
        FROM o_order t1
        WHERE t1.order_confirmation_status = 1
        AND t1.hotel_id IN
        <foreach collection="hotelIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND t1.end_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY t1.hotel_id
    </select>

    <select id="queryOrderIdByOrderCode" resultType="java.lang.Long">
        select o.id
        from o_order o
        where o.order_code = #{orderCode}
    </select>

    <select id="selectContactOrder" resultType="com.tiangong.order.remote.response.OrderDTO">
        select
            t1.agent_code           agentCode,
            t1.contact_phone        contactPhone,
            t1.contact_name         contactName,
            t2.confirmation_code    confirmationCode
        from o_order t1
        left join o_order_extend t2 on t2.order_id = t1.id
        where t1.id = #{id}
    </select>

    <select id="selectGuestList" resultType="com.tiangong.dto.order.OrderGuestDTO">
        SELECT
            o.order_id      orderId,
            o.name          name,
            o.first_name    firstName,
            o.last_name     lastName,
            o.nationality   nationality,
            o.mobile_no     mobileNo,
            o.country_code  countryCode
        FROM o_guest o
        WHERE o.order_id = #{orderId}
    </select>
</mapper>