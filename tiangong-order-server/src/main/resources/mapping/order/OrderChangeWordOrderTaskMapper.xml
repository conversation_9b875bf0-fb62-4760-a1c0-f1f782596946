<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.order.mapper.OrderChangeWordOrderTaskMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderChangeWordOrderTaskPO">
        <result property="taskOrderCode" column="task_order_code"/>
        <result property="orderCode" column="order_code"/>
        <result property="taskType" column="task_type"/>
        <result property="refundType" column="refund_type"/>
        <result property="status" column="status"/>
        <result property="followUp" column="follow_up"/>
        <result property="changeReason" column="change_reason"/>
        <result property="remark" column="remark"/>
        <result property="handleResult" column="handle_result"/>
        <result property="agentRefund" column="agent_refund"/>
        <result property="supplyRefund" column="supply_refund"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <resultMap id="OrderChangeWordOrderTaskDetailMap" type="com.tiangong.order.domain.resp.OrderChangeWordOrderTaskResp">
        <result property="taskOrderCode" column="task_order_code"/>
        <result property="orderCode" column="order_code"/>
        <result property="taskType" column="task_type"/>
        <result property="refundType" column="refund_type"/>
        <result property="status" column="status"/>
        <result property="followUp" column="follow_up"/>
        <result property="changeReason" column="change_reason"/>
        <result property="refundDt" column="refund_dt"/>
        <result property="remark" column="remark"/>
        <result property="handleResult" column="handle_result"/>
        <result property="agentRefund" column="agent_refund"/>
        <result property="supplyRefund" column="supply_refund"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="orderId" column="id"/>
        <result property="agentRefundCurrency" column="sale_currency"/>
        <result property="supplyRefundCurrency" column="base_currency"/>

        <collection property="itemList" select="com.tiangong.order.mapper.OrderChangeWordOrderTaskItemMapper.selectOrderChangeWordOrderTaskItemList"
                    column="task_order_code" ofType="com.tiangong.order.domain.resp.OrderChangeWordOrderTaskItemResp">
            <result property="itemId" column="item_id"/>
            <result property="taskOrderCode" column="task_order_code"/>
            <result property="roomIndex" column="room_index"/>
            <result property="changeDate" column="change_date"/>
            <result property="guestName" column="guest_name"/>
            <result property="agentGuestName" column="agent_guest_name"/>
        </collection>
    </resultMap>

    <select id="selectOrderChangeWordOrderTaskDetail" resultMap="OrderChangeWordOrderTaskDetailMap">
        select
            t1.task_order_code,
            t1.order_code,
            t1.task_type,
            t1.refund_type,
            t1.status,
            t1.follow_up,
            t1.change_reason,
            t1.refund_dt,
            t1.remark,
            t1.handle_result,
            t1.agent_refund,
            t1.supply_refund,
            t1.created_by,
            t1.created_dt,
            t1.updated_by,
            t1.updated_dt,
            t2.id,
            t2.sale_currency,
            (SELECT t3.base_currency FROM o_supply_order t3 WHERE t3.order_id = t2.id LIMIT 1) AS base_currency
        from t_order_change_word_order_task t1
        left join o_order t2 on t2.order_code = t1.order_code
        where 1=1
        <if test="taskOrderCode != null and taskOrderCode != ''">
            and t1.task_order_code = #{taskOrderCode}
        </if>
        <if test="orderCode != null and orderCode != ''">
            and t1.order_code = #{orderCode}
        </if>
    </select>

</mapper>