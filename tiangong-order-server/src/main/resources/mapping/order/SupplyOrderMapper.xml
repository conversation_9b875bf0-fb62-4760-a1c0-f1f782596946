<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.SupplyOrderMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.SupplyOrderPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="supply_order_code" jdbcType="VARCHAR" property="supplyOrderCode"/>
        <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus"/>
        <result column="sending_status" jdbcType="INTEGER" property="sendingStatus"/>
        <result column="base_currency" jdbcType="INTEGER" property="baseCurrency"/>
        <result column="supply_order_amt" jdbcType="DECIMAL" property="supplyOrderAmt"/>
        <result column="commission" jdbcType="DECIMAL" property="commission"/>
        <result column="base_price" jdbcType="DECIMAL" property="basePrice"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="supplier_order_code" jdbcType="VARCHAR" property="supplierOrderCode"/>
        <result column="settlement_type" jdbcType="INTEGER" property="settlementType"/>
        <result column="refund_fee" jdbcType="DECIMAL" property="refundFee"/>
        <result column="merchant_pm" jdbcType="VARCHAR" property="merchantPm"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="purchase_type" jdbcType="INTEGER" property="purchaseType"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="hotel_id" jdbcType="INTEGER" property="hotelId"/>
        <result column="hotel_name" jdbcType="VARCHAR" property="hotelName"/>
        <result column="room_name" jdbcType="VARCHAR" property="roomName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="room_qty" jdbcType="INTEGER" property="roomQty"/>
        <result column="bed_type" jdbcType="VARCHAR" property="bedType"/>
        <result column="breakfast_qty" jdbcType="INTEGER" property="breakfastQty"/>
        <result column="confirmation_code" jdbcType="VARCHAR" property="confirmationCode"/>
        <result column="supplier_confirmer" jdbcType="VARCHAR" property="supplierConfirmer"/>
        <result column="refused_reason" jdbcType="VARCHAR" property="refusedReason"/>
        <result column="confirmation_remark" jdbcType="VARCHAR" property="confirmationRemark"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt"/>
        <result column="is_sp_product" jdbcType="INTEGER" property="isSpProduct"/>
    </resultMap>

    <select id="queryOnTimeSupplyOrderList"
            parameterType="com.tiangong.order.remote.request.QueryOnTimeSupplyOrderListDTO"
            resultType="com.tiangong.order.remote.response.OnTimeSupplyOrderDTO">
        SELECT
        so.id supplyOrderId,
        so.supply_order_code supplyOrderCode,
        so.supplier_name supplierName,
        so.supplier_code supplierCode,
        so.supply_order_amt payableAmt,
        sf.paid_amt paidAmt,
        sf.unpaid_amt unpaidAmt,
        sf.unconfirmed_received_amt unconfirmedReceivedAmt,
        sf.unconfirmed_paid_amt unconfirmedPaidAmt,
        sf.settlement_date settlementDate,
        if(ISNULL(sf.real_settlement_date), if(DATEDIFF(CURDATE(),sf.settlement_date)&gt;0,DATEDIFF(CURDATE(),sf.settlement_date),0),
        if(DATEDIFF(sf.real_settlement_date,sf.settlement_date)&gt;0,DATEDIFF(sf.real_settlement_date,sf.settlement_date),0))
        overdueDays,
        sf.settlement_status settlementStatus,
        so.base_currency baseCurrency
        FROM
        o_order o,o_supply_order so,o_supply_order_finance sf
        WHERE o.id=so.order_id AND so.id=sf.supply_order_id
        AND o.company_code=#{companyCode}
        AND so.settlement_type=3
        AND sf.finance_type = 0
        <choose>
            <when test="supplierCode!=null and supplierCode!=''">
                AND so.supplier_code=#{supplierCode}
            </when>
            <otherwise>
                <if test="supplierName != null and supplierName != ''">
                    and so.supplier_name LIKE concat(concat('%',#{supplierName}),'%')
                </if>
            </otherwise>
        </choose>
        <if test="startDate != null and startDate !=''">
            AND sf.settlement_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate !=''">
            AND sf.settlement_date &lt;= #{endDate}
        </if>
        <if test="supplyOrderCode != null and supplyOrderCode !=''">
            AND so.supply_order_code LIKE concat(concat('%',#{supplyOrderCode}),'%')
        </if>
        <if test="settlementStatus !=null and settlementStatus != ''">
            AND sf.settlement_status = #{settlementStatus}
        </if>
        <if test="overdueStatus !=null and overdueStatus != ''">
            <if test="overdueStatus == 0">
                AND DATEDIFF(IFNULL(sf.real_settlement_date, CURDATE()),sf.settlement_date)&lt;=0
            </if>
            <if test="overdueStatus == 1">
                AND DATEDIFF(IFNULL(sf.real_settlement_date, CURDATE()),sf.settlement_date)>0
            </if>
        </if>
        AND (sf.unpaid_amt != 0 OR sf.paid_amt != 0)
        ORDER BY o.id desc,so.id desc
    </select>

    <select id="queryOfficialSealUrl" resultType="java.lang.String">
        SELECT
        toos.official_seal_url
        FROM t_org_organization too
        LEFT JOIN t_org_official_seal toos ON too.official_seal_id = toos.id
        WHERE toos.active = 1 AND too.org_code = #{supplierCode}
    </select>

    <update id="batchUpdateOrderStatusDone">
        update o_supply_order set confirmation_status = 3
        where confirmation_status = 1 and id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="querySupplyOrderCodeByOrderId" resultType="java.lang.String">
        SELECT supply_order_code
        FROM o_supply_order
        WHERE order_id = #{orderId}
    </select>

    <select id="selectInBlankCheckInfoSupplyOrderCodeList" resultType="com.tiangong.order.remote.response.SupplyOrderDTO">
        SELECT
            t1.supply_order_code    supplyOrderCode,
            t1.supplier_order_code  supplierOrderCode
        FROM o_supply_order t1
        LEFT JOIN o_order_check_info t2 on t1.supply_order_code = t2.supply_order_code and t2.deleted = 0
        LEFT JOIN t_org_organization t3 on t1.supplier_code = t3.org_code
        LEFT JOIN t_org_company_supplier t4 on t3.org_id = t4.org_id
        WHERE t4.is_sync_check_detail = 1 and t1.supplier_order_code &lt;&gt; '' and t2.id is null and (t1.end_date = STR_TO_DATE(#{thisDate}, '%Y-%m-%d') or t1.end_date = DATE_ADD(STR_TO_DATE(#{thisDate}, '%Y-%m-%d'), INTERVAL -1 DAY))
    </select>

    <select id="selectSupplyOrderInfos" resultType="com.tiangong.order.remote.request.SupplyOrderInfo">
        SELECT
            t.supplier_order_code  supplyOrderCode,
            t.supplier_name        supplierName,
            t.confirmation_status  confirmationStatus,
            t.base_currency        baseCurrency,
            t.supply_order_amt     supplyOrderAmt
        FROM o_supply_order t
        WHERE t.order_id = #{orderId}
    </select>

    <select id="selectCheckInDetailAbnormalNum" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            o_supply_order
        WHERE
            is_check_in_detail_abnormal = 1
        GROUP BY
            order_id
    </select>

    <select id="selectOrderCodeBySupplyOrderCode" resultType="com.tiangong.order.remote.dto.OrderCodeInfoDTO">
        SELECT
            t.supply_order_code     supplyOrderCode,
            t1.order_code           orderCode,
            t1.channel_order_code   channelOrderCode,
            t1.agent_code           agentCode
        FROM o_supply_order t
        LEFT JOIN o_order t1 on t1.id = t.order_id
        WHERE 1 = 1
        AND t.supply_order_code IN
        <foreach collection="supplyOrderCodeList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSupplyOrderInfo" resultType="com.tiangong.order.remote.response.SupplyOrderDTO">
        SELECT
            t.id                        supplyOrderId,
            t.supply_order_code         supplyOrderCode,
            t.supplier_order_code       supplierOrderCode,
            t.hotel_id                  hotelId,
            t.hotel_name                hotelName,
            t.order_id                  orderId,
            t1.order_code               orderCode,
            t1.sale_currency            saleCurrency,
            t1.channel_order_code       channelOrderCode,
            t1.contact_name             contactName,
            t1.contact_phone            contactPhone,
            t1.city_code                cityCode,
            t1.agent_code               agentCode,
            t1.agent_name               agentName,
            t.supplier_name             supplierName,
            t.confirmation_status       confirmationStatus,
            t.base_currency             baseCurrency,
            t.supply_order_amt          supplyOrderAmt,
            t.created_dt                createdDt,
            t.start_date                startDate,
            t.end_date                  endDate,
            t.room_numbers              roomNumbers,
            t1.order_amt                orderAmt,
            t1.created_dt               orderCreatedDt
        FROM o_supply_order t
        LEFT JOIN o_order t1 on t1.id = t.order_id
        WHERE t.supply_order_code = #{supplyOrderCode}
    </select>

    <update id="updateSupplierCheckInAbnormal" parameterType="java.lang.String">
        update o_supply_order set is_check_in_detail_abnormal = 0 where supply_order_code = #{supplyOrderCode}
    </update>

    <select id="selectNonVccAutoBillConfigBySupplierCodeAndHotelId" resultType="com.tiangong.order.dto.NonVccAutoBillConfigDTO">
        SELECT
            t.supplier_code supplierCode,
            t.supplier_name supplierName,
            t.hotel_id hotelId,
            t.hotel_name hotelName,
            t.payment_method paymentMethod,
            t.payment_link_emails paymentLinkEmails,
            t.hotel_payment_contact_person hotelPaymentContactPerson,
            t.hotel_collection_bank hotelCollectionBank,
            t.hotel_collection_account_name hotelCollectionAccountName,
            t.hotel_collection_account_number hotelCollectionAccountNumber,
            t.world_first_account_number worldFirstAccountNumber,
            t.remarks remarks
        FROM f_non_vcc_auto_bill_config t
        WHERE t.supplier_code = #{supplierCode} 
            AND t.hotel_id = #{hotelId}
    </select>
</mapper>