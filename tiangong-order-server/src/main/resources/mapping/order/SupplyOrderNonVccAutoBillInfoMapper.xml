<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.order.mapper.SupplyOrderNonVccAutoBillInfoMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.SupplyOrderNonVccAutoBillInfoPO">
        <result property="id" column="id"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="paymentLinkEmails" column="payment_link_emails"/>
        <result property="hotelPaymentContactPerson" column="hotel_payment_contact_person"/>
        <result property="hotelCollectionBank" column="hotel_collection_bank"/>
        <result property="hotelCollectionAccountName" column="hotel_collection_account_name"/>
        <result property="hotelCollectionAccountNumber" column="hotel_collection_account_number"/>
        <result property="worldFirstAccountNumber" column="world_first_account_number"/>
        <result property="remarks" column="remarks"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>


</mapper>