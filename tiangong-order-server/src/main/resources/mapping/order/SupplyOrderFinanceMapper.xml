<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.SupplyOrderFinanceMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.SupplyOrderFinancePO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="supply_order_id" jdbcType="INTEGER" property="supplyOrderId"/>
        <result column="supply_order_code" jdbcType="VARCHAR" property="supplyOrderCode"/>
        <result column="paid_amt" jdbcType="DECIMAL" property="paidAmt"/>
        <result column="unpaid_amt" jdbcType="DECIMAL" property="unpaidAmt"/>
        <result column="unconfirmed_received_amt" jdbcType="DECIMAL" property="unconfirmedReceivedAmt"/>
        <result column="unconfirmed_paid_amt" jdbcType="DECIMAL" property="unconfirmedPaidAmt"/>
        <result column="settlement_status" jdbcType="INTEGER" property="settlementStatus"/>
        <result column="settlement_date" jdbcType="TIMESTAMP" property="settlementDate"/>
        <result column="check_status" jdbcType="INTEGER" property="checkStatus"/>
        <result column="finance_lock_status" jdbcType="INTEGER" property="financeLockStatus"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
    </resultMap>

    <select id="querySupplyOrderAmt" parameterType="com.tiangong.order.remote.request.SupplyOrderIdListDTO"
            resultType="com.tiangong.order.dto.SupplyOrderAmtDTO">
        SELECT
        o.id orderId,
        o.order_code orderCode,
        o.company_code companyCode,
        so.id supplyOrderId,
        so.supply_order_code supplyOrderCode,
        so.supplier_code supplierCode,
        so.supplier_name supplierName,
        sf.id supplyOrderFinanceId,
        so.base_currency currency,
        so.supply_order_amt supplyOrderAmt,
        sf.paid_amt paidAmt,
        sf.unpaid_amt unpaidAmt,
        sf.unconfirmed_received_amt unconfirmedReceivedAmt,
        sf.unconfirmed_paid_amt unconfirmedPaidAmt
        FROM o_order o,o_supply_order so,o_supply_order_finance sf
        WHERE o.id=so.order_id AND so.id=sf.supply_order_id
        AND sf.finance_type = 0
        <if test="supplyOrderIdList != null and supplyOrderIdList.size > 0">
            AND so.id IN
            <foreach collection="supplyOrderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
    </select>

    <!-- 查询供货单财务相关信息【提供自助结算模块查询】 -->
    <select id="querySupplyOrderFinanceInfo" parameterType="com.tiangong.order.remote.request.SupplyOrderInfoListDTO"
        resultType="com.tiangong.order.remote.response.SupplyOrderFinanceDTO">
        SELECT o.order_code as orderCode,
               so.supply_order_code as supplyOrderCode,
               so.settlement_type as settlementType,
               o.id as orderId,
               o.channel_order_code as channelOrderCode,
               so.supplier_name as supplierName,
               o.hotel_name as hotelName,
               o.room_name as roomName,
               o.product_name as productName,
               so.start_date as startDate,
               so.end_date as endDate,
               so.room_qty as roomQty,
               so.supply_order_amt as supplyOrderAmt,
               sf.paid_amt as paidAmt,
               sf.unpaid_amt as unpaidAmt,
               so.id as supplyOrderId,
               o.room_id as roomId,
               o.pay_method as payMethod,
               o.product_id as productId,
               so.bed_type as bedType,
               so.breakfast_qty as breakfastQty,
               so.confirmation_remark as confirmationRemark,
               o.created_dt as orderDt,
               so.created_dt as supplyOrderDt,
               so.base_currency as baseCurrency,
               sf.check_status as checkStatus,
               so.room_numbers as roomNumbers,
               sp.cancellation_term as cancellationTerm
          FROM o_order o,
               o_supply_order so,
               o_supply_order_finance sf,
               o_supply_product sp
         WHERE o.id = so.order_id
           AND so.id = sf.supply_order_id
           AND so.id = sp.supply_order_id
        AND sf.finance_type = 0
        <if test="supplyOrderIdList != null and supplyOrderIdList.size > 0">
            AND so.id IN
            <foreach collection="supplyOrderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="supplyOrderCodeList != null and supplyOrderCodeList.size > 0">
            AND so.supply_order_code IN
            <foreach collection="supplyOrderCodeList" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
    </select>
</mapper>