<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.OrderRemarkMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderRemarkPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="remark_type" jdbcType="INTEGER" property="remarkType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
    </resultMap>
</mapper>