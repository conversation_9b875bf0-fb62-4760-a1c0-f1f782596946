package com.tiangong.cloud.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/3/24 19:37
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum RoutingEnums implements Serializable {

    HOME("/home", "工作台"),
    SIGNED_PRODUCT_BS("/signedProductBS", "自签产品底价/房态"),
    SIGNED_PRODUCT_INCREMENT("/signedProductIncrement", "自签产品加幅"),
    CACHED_PRODUCT_SALE_STATUS("/cachedProductSaleStatus", "产品上下架"),
    SELLING_PRICE("/sellingPrice", "差异化售价"),
    ORDER("/order", "所有订单"),
    ORDER_SETTING("/orderSetting", "直连订单设置"),
    AGENT_STATEMENT_ON_ACCOUNT("/agentStatementOnAccount", "客户周期结"),
    AGENT_STATEMENT_ON_TIME("/agentStatementOnTime", "客户单结"),
    SUPPLIER_STATEMENT_ON_ACCOUNT("/supplierStatementOnAccount", "供应商周期结"),
    SUPPLIER_STATEMENT_ON_TIME("/supplierStatementOnTime", "供应商单结"),
    ORDER_LOCKS("/orderLocks", "财务订单锁"),
    SUPPLY_ORDER_LOCKS("/supplyOrderLocks", "财务供货单锁"),
    SETTLEMENTS("/settlements", "财务结算"),
    SALE_STATISTIC("/saleStatistic", "销售明细表"),
    PROFIT_STATISTIC("/profitStatistic", "订单利润表"),
    OPERATION_STATISTIC("/operationStatistic", "经营概况表"),
    AGENT("/agent", "客户管理"),
    AGENT_DETAIL("/agentDetail", "客户详情"),
    SUPPLIER("/supplier", "供应商管理"),
    CITY_MAP("/cityMap", "城市映射"),
    HOTEL_MAP("/hotelMap", "供应商酒店映射"),
    ROOM_MAP("/roomMap", "供应商房型映射"),
    SUPPLIER_MANAGE("/supplierManage", "供应商对接管理"),
    HOTEL_MONITOR("/hotelMonitor", "已映射酒店监控"),
    ROOM_MONITOR("/roomMonitor", "已映射房型监控"),
    CHANNEL_V_DOING("/channelVdoing", "客户维度"),
    SUPPLIER_V_DOING("/supplierVdoing", "供应商维度"),
    HOTEL_INFO("/hotelInfo", "酒店基本信息"),
    HOTEL_SORT("/hotelSort", "酒店排序设置"),
    COMPANY_INFO("/companyInfo", "企业信息"),
    EMPLOYEE("/employee", "员工管理"),
    EMPLOYEE_INFO("/employeeInfo", "员工详情"),
    ROLE_MANAGE("/roleManage", "角色管理"),
    EXCHANGE_RATE("/exchangeRate", "汇率管理"),
    BANK_CARD("/bankCard", "银行卡管理"),
    USER_INFO("/userInfo", "个人信息"),
    AUDIT_LOG("/auditLog", "审计日志"),
    STATISTICS("/statistics", "统计日志"),
    SYSTEM("/system", "系统设置");

    public final String routingAddress;
    public final String routingName;

    public static String getRoutingNameByRoutingAddress(String routingAddress) {
        String routingName = null;
        for (RoutingEnums routingEnum : RoutingEnums.values()) {
            if (Objects.equals(routingAddress, routingEnum.getRoutingAddress())) {
                routingName = routingEnum.getRoutingName();
                break;
            }
        }
        return routingName;
    }

}
