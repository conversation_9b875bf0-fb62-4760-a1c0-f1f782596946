package com.tiangong.cloud.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

/**
 * @ author: wang
 * @ date: 2021/4/21 9:34
 * @ Description:  json 类型转换
 */
public class JsonUtils {

    //利用fastjson进行反序列化，转成复杂的java对象，如：List<User>
    public static <T> T typeCast(TypeReference<T> typeReference, Object data) {
        if (data == null) {
            return null;
        }
        String jsonString = JSON.toJSONString(data);
        T t = JSON.parseObject(jsonString, typeReference);
        return t;
    }

    //利用fastjson进行反序列化，转成简单的java对象,如：User
    public static <T> T jsonToObject(Object obj, Class<T> clazz) {
        if (obj == null) {
            return null;
        }
        T t = JSON.parseObject(JSON.toJSONString(obj), clazz);
        return t;
    }
}
