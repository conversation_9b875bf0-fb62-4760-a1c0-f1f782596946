package com.tiangong.cloud.common.constant;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019年11月28日
 * <p>
 * 全局security参数常量
 */
@Data
public class HttpConstant {

    public final static String HEADER = "X-token"; // token参数名
    public final static String RequestId = "X-Request-Id"; // token参数名

    public final static String Language = "Language"; // 语言

    public final static String X_MSE_TAG = "x-mse-tag"; // 灰度环境

    /**
     * saas的请求头参数
     */
    public final static String SAAS_PARTNER_CODE = "partnerCode"; // 分销商编码
    public final static String SAAS_REQUEST_TYPE = "requestType"; // 请求类型
    public final static String SAAS_SIGNATURE = "signature"; // 签名
    public final static String SAAS_TIME_STAMP = "timeStamp"; // 当前时间戳
    public final static String SAAS_VERSION = "version"; // 接口版本号


}
