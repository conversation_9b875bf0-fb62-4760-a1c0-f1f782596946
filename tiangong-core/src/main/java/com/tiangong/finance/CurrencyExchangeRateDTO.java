package com.tiangong.finance;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CurrencyExchangeRateDTO {

    /**
     * 原币种
     */
    private String originalCurrency;

    /**
     * 目标币种
     */
    private String targetCurrency;

    /**
     * 最终汇率
     */
    private BigDecimal rate;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    private BigDecimal modifiedAmt;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;
}
