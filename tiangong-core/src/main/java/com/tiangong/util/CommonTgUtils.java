package com.tiangong.util;

import com.alibaba.fastjson.JSON;
import com.tiangong.enums.SaleAdjustmentTypeEnum;
import com.tiangong.redis.core.RedisTemplateX;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用方法 统一封装
 */
@Slf4j
public class CommonTgUtils {

    /**
     * 加幅金额
     *
     * @param basePrice      底价
     * @param adjustmentType 加幅类型 SaleAdjustmentTypeEnum （0加数值 1减数值 2加百分比 3减百分比 4等于）
     * @param modifiedAmt    加幅值
     * @return 返回加幅金额
     */
    public static BigDecimal adjustmentAmt(BigDecimal basePrice, Integer adjustmentType, BigDecimal modifiedAmt) {
        if (Objects.isNull(basePrice)) {//非空情况
            return BigDecimal.ZERO;
        }
        if (adjustmentType == null || modifiedAmt == null) {//防止出现没有设置加幅的情况
            return BigDecimal.ZERO;
        }
        if (Objects.equals(SaleAdjustmentTypeEnum.PLUS_NUMBER.no, adjustmentType)) {
            return modifiedAmt;
        } else if (Objects.equals(SaleAdjustmentTypeEnum.DIVIDE_NUMBER.no, adjustmentType)) {
            return modifiedAmt.negate();
        } else if (Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, adjustmentType)) {
            return basePrice.multiply(modifiedAmt.divide(new BigDecimal("100")));
        } else if (Objects.equals(SaleAdjustmentTypeEnum.EQUALS.no, adjustmentType)) {
            return modifiedAmt;
        } else if (Objects.equals(SaleAdjustmentTypeEnum.SUBTRACT_PERCENTAGE.no, adjustmentType)) {
            return basePrice.multiply(modifiedAmt.divide(new BigDecimal("100"))).negate();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 设置汇率
     *
     * @param basePrice 底价
     * @param rate      汇率
     */
    public static BigDecimal setRate(BigDecimal basePrice, BigDecimal rate) {
        if (Objects.isNull(basePrice)) {//非空情况
            return BigDecimal.ZERO;
        }
        // 计算汇率
        basePrice = basePrice.multiply(rate);
        return basePrice;
    }

    /**
     * 计算汇率价格
     */
    public static BigDecimal setRateAmt(BigDecimal basePrice, BigDecimal rate) {
        if (Objects.isNull(basePrice)) {//非空情况
            return BigDecimal.ZERO;
        }
        return basePrice.multiply(rate);
    }

    /**
     * 设置n位小数点
     *
     * @param bigDecimal   原值
     * @param newScale     进位数
     * @param roundingType 进位类型 1向上取整 2向下取整 3四舍五入
     * @return 进位后的值
     */
    public static BigDecimal setScale(BigDecimal bigDecimal, int newScale, int roundingType) {
        //进位方式转换：取整方式 1向上取整 2向下取整 3四舍五入
        switch (roundingType) {
            case 1:
                roundingType = BigDecimal.ROUND_UP;
                break;
            case 2:
                roundingType = BigDecimal.ROUND_DOWN;
                break;
            case 3:
                roundingType = BigDecimal.ROUND_HALF_UP;
                break;
            default:
                roundingType = BigDecimal.ROUND_UP;
        }
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        }
        return bigDecimal.setScale(newScale, roundingType);
    }

    /**
     * 设置2位小数点 向上取整 为空默认为0
     */
    public static BigDecimal set2UpScale(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        } else {
            return bigDecimal.setScale(2, BigDecimal.ROUND_UP);
        }
    }

    /**
     * 格式化价格，null返回BigDecimal.ZERO,其他价格正常返回
     */
    public static BigDecimal formatBigDecimal(BigDecimal price) {
        if (null == price) {
            return BigDecimal.ZERO;
        }
        return price;
    }

    /**
     * 计算除法并设置n位小数点
     *
     * @param number       原值
     * @param number2      除数
     * @param newScale     进位数
     * @param roundingType 进位类型 1向上取整 2向下取整 3四舍五入
     * @return 进位后的值
     */
    public static BigDecimal divideSetScale(BigDecimal number, BigDecimal number2, int newScale, int roundingType) {
        //进位方式转换：取整方式 1向上取整 2向下取整 3四舍五入
        switch (roundingType) {
            case 1:
                roundingType = BigDecimal.ROUND_UP;
                break;
            case 2:
                roundingType = BigDecimal.ROUND_DOWN;
                break;
            case 3:
                roundingType = BigDecimal.ROUND_HALF_UP;
                break;
            default:
                roundingType = BigDecimal.ROUND_UP;
        }
        if (Objects.isNull(number)) {
            return BigDecimal.ZERO;
        }
        if (Objects.isNull(number2)) {
            return number;
        }
        return number.divide(number2, newScale, roundingType);
    }

    /**
     * 动态调整休眠时间
     */
    public static void adjustSleepTime(long baseSleep, int batchSize, int pageSize) {
        try {
            // 动态调整公式（带最小处理量保护）
            int thresholdLow = (int) Math.max(pageSize * 0.8, 100);
            int thresholdHigh = (int) (pageSize * 0.95);

            if (batchSize < thresholdLow) {
                Thread.sleep(baseSleep * 2);
            } else if (batchSize >= thresholdHigh) {
                Thread.sleep(baseSleep / 2);
            } else {
                Thread.sleep(baseSleep);
            }
        } catch (InterruptedException e) {
            log.error("休眠调整被中断", e);
        }
    }

    public static int compareInteger(Integer val1, Integer val2, boolean nullIsLess) {
        if (val1 == val2) {
            return 0;
        } else if (val1 == null) {
            return nullIsLess ? -1 : 1;
        } else if (val2 == null) {
            return nullIsLess ? 1 : -1;
        } else {
            return val1.equals(val2) ? 0 : 1;
        }
    }

    public static int compareBigDecimal(BigDecimal val1, BigDecimal val2, boolean nullIsLess) {
        if (val1 == val2) {
            return 0;
        } else if (val1 == null) {
            return nullIsLess ? -1 : 1;
        } else if (val2 == null) {
            return nullIsLess ? 1 : -1;
        } else {
            return val1.compareTo(val2) == 0 ? 0 : 1;
        }
    }
}
