package com.tiangong.util;


import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @date 2022/3/29 09:07
 */
@Slf4j
public class FileUtil {


    /**
     * 定义GB的计算常量，1024 * 1024 * 1024
     */
    private static final int GB = 1073741824;
    /**
     * 定义MB的计算常量，1024 * 1024
     */
    private static final int MB = 1048576;
    /**
     * 定义KB的计算常量
     */
    private static final int KB = 1024;

    /**
     * 格式化小数
     */
    private static final DecimalFormat DF = new DecimalFormat("0.00");

    /**
     * 获取文件后缀名，不带 .
     */
    public static String getFileSuffix(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }

    /**
     * 计算文件大小
     */
    public static String formetFileSize(long fileS) {//转换文件大小
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";
        if (fileS < 1024) {
            fileSizeString = df.format((double) fileS) + "B";
        } else if (fileS < 1048576) {
            fileSizeString = df.format((double) fileS / KB) + "K";
        } else if (fileS < 1073741824) {
            fileSizeString = df.format((double) fileS / MB) + "M";
        } else {
            fileSizeString = df.format((double) fileS / GB) + "G";
        }
        return fileSizeString;
    }

    /**
     * 根据后缀判断文件类型，1：图片，2：文档，3：音乐，4：视频，5：其他,0:下载包
     */
    public static String getFileType(String fileSuffix) {
        String image = "bmp dib pcp dif wmf gif jpg tif eps psd cdr iff tga pcd mpt png jpeg";
        String documents = "txt doc pdf ppt pps xlsx xls docx";
        String music = "mp3 wav wma mpa ram ra aac aif m4a";
        String video = "avi mpg mpe mpeg asf wmv mov qt rm mp4 flv m4v webm ogv ogg";
//        String sufname = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (image.contains(fileSuffix)) {
            return "image";
        } else if (documents.contains(fileSuffix)) {
            return "documents";
        } else if (music.contains(fileSuffix)) {
            return "music";
        } else if (video.contains(fileSuffix)) {
            return "video";
        } else {
            return "other";
        }
    }

    /**
     * 删除文件
     */
    public static boolean del(String fileName) {
        File file = new File(fileName);
        return FileUtil.del(file);
    }

    /**
     * 删除文件
     */
    public static boolean del(File file) {
        boolean flag = false;
        if (file.exists() && file.isFile()) {
            flag = file.delete();
            return flag;
        }
        return flag;
    }


    /**
     * 关闭流
     */
    public static void close(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException var2) {
                log.error("关闭流异常", var2);
            }
        }
    }

    /**
     * 输入流写进输出流
     */
    private static int doCopy(InputStream in, OutputStream out) throws IOException {
        int byteCount = 0;
        int bytesRead;
        for (byte[] buffer = new byte[4096]; (bytesRead = in.read(buffer)) != -1; byteCount += bytesRead) {
            out.write(buffer, 0, bytesRead);
        }
        out.flush();
        return byteCount;
    }


}
