package com.tiangong.util;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;

//获取sm2公钥与私钥
public class GetPKs {

    public static void main(String[] args) {
        SM2 sm2 = SmUtil.sm2();
        // 私钥
        byte[] privateKey = BCUtil.encodeECPrivateKey(sm2.getPrivateKey());
        // 公钥：这个是前后端加密用的，不压缩选择带04的，不带04到时候前端会报错
        byte[] publicKey = ((BCECPublicKey) sm2.getPublicKey()).getQ().getEncoded(false);
        Console.log("公钥：\n{}", HexUtil.encodeHexStr(publicKey));
        Console.log("私钥：\n{}", HexUtil.encodeHexStr(privateKey));
    }

}