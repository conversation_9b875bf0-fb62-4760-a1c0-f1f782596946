package com.tiangong.util;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tiangong.enums.BedTypeLanguageEnum;
import com.tiangong.enums.LanguageTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 床型语种工具类
 */
public class BedInfoUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();


    public static String generateBedDescription(String jsonData, String languageCode) {
        try {

            //判断为空直接返回
            if(StringUtils.isEmpty(jsonData)){
                return null;
            }

            // 解析 JSON 数据
            JsonNode rootNode = objectMapper.readTree(jsonData);
            JsonNode bedInfos = rootNode.path("bedInfos");

            List<String> bedDescriptions = new ArrayList<>();

            // 遍历床信息并生成描述
            for (JsonNode bedInfo : bedInfos) {
                String bedTypeCode = bedInfo.path("bedTypeCode").asText();
                int bedNum = bedInfo.path("bedNum").asInt();

                // 根据语言代码获取值
                String bedDescription = getBedDescription(bedTypeCode, bedNum, languageCode);
                bedDescriptions.add(bedDescription);
            }
            if(languageCode.equals(LanguageTypeEnum.zh_CN.getValue())){
                // 输出最终结果
                return String.join(" 和 ", bedDescriptions);
            }else{
                return String.join(" and ", bedDescriptions);
            }


        } catch (Exception e) {
            throw new RuntimeException("解析床信息失败", e);
        }
    }

    public static void main(String[] args) {
        String jsonData = "{\"bedInfos\":[{\"bedTypeName\":\"双人日式床\",\"bedTypeCode\":\"S000000\",\"bedNum\":1},{\"bedTypeName\":\"沙发床\",\"bedTypeCode\":\"I000000\",\"bedNum\":2},{\"bedTypeName\":\"水床\",\"bedTypeCode\":\"E000000\",\"bedNum\":1}]}";
        String result = generateBedDescription(jsonData, "en-US");
        System.out.println(result);
    }

    private static String getBedDescription(String bedTypeCode, int bedNum, String languageCode) {
        BedTypeLanguageEnum bedType = BedTypeLanguageEnum.fromCode(bedTypeCode);
        String bedName = bedType.getValueByLanguage(languageCode);

        if(languageCode.equals(LanguageTypeEnum.zh_CN.getValue())){
            return bedNum + "张" + bedName;
        }else{
            return bedNum + " " + bedName;
        }
    }
}

