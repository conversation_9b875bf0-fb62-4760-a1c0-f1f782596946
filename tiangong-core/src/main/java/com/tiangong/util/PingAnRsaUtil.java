package com.tiangong.util;

import org.apache.commons.codec.binary.Base64;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;


/**
 * @Auther: ziyi
 * @Date: 2024-03-18
 * @Description: 平安Rsa加密
 */
public class PingAnRsaUtil {

    /**
     * 非对称加密算法
     */
    private static final String KEY_ALGORITHM = "RSA";

    /**
     * 秘钥长度
     */
    private static final int KEY_SIZE = 2048;

    /**
     * 数字签名算法
     */
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";


    private PingAnRsaUtil() {
    }


    /**
     * RSA私钥加签
     *
     * @param privateKey base64处理后的私钥
     * @param plainText  明文内容
     * @return 十六进制的签名字符串
     */
    public static String sign(byte[] privateKey, String plainText) {
        try {
            PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            PrivateKey priKey = keyFactory.generatePrivate(priPKCS8);

            Signature sign = Signature.getInstance(SIGNATURE_ALGORITHM);
            sign.initSign(priKey);
            sign.update(plainText.getBytes(StandardCharsets.UTF_8));
            return HexUtil.byte2hex(sign.sign());
        } catch (Exception e) {
            // todo
            throw new RuntimeException(e);
        }
    }


    /**
     * RSA私钥加签
     *
     * @param privateKey base64处理后的私钥
     * @param plainText  明文内容
     * @return 十六进制的签名字符串
     */
    public static String sign(String privateKey, String plainText) {
        return sign(privateKey.getBytes(StandardCharsets.UTF_8), plainText);
    }


    /**
     * RSA公钥验签
     *
     * @param publicKey base64处理后的公钥
     * @param plainText 明文内容
     * @param signText  十六进制的签名字符串
     * @return 验签结果 true验证一致 false验证不一致
     */
    public static boolean verify(byte[] publicKey, String plainText, String signText) {
        try {
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            PublicKey pubKey = keyFactory.generatePublic(x509EncodedKeySpec);

            byte[] signed = HexUtil.hex2byte(signText);
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(pubKey);
            signature.update(plainText.getBytes(StandardCharsets.UTF_8));
            return signature.verify(signed);
        } catch (Throwable e) {
            // todo
            throw new RuntimeException(e);
        }
    }


    /**
     * RSA公钥验签
     *
     * @param publicKey base64处理后的公钥
     * @param plainText 明文内容
     * @param signText  十六进制的签名字符串
     * @return 验签结果 true验证一致 false验证不一致
     */
    public static boolean verify(String publicKey, String plainText, String signText) {
        return verify(publicKey.getBytes(StandardCharsets.UTF_8), plainText, signText);
    }


    public static KeyPair getKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGenerator.initialize(KEY_SIZE);
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * 返回base64编码秘钥对
     *
     * @return
     * @throws Exception
     */
    public static Base64KeyPair getBase64KeyPair() throws NoSuchAlgorithmException {
        KeyPair keyPair = getKeyPair();
        Base64KeyPair base64KeyPair = new Base64KeyPair();
        base64KeyPair.setPublicKey(Base64.encodeBase64String(keyPair.getPublic().getEncoded()));
        base64KeyPair.setPrivateKey(Base64.encodeBase64String(keyPair.getPrivate().getEncoded()));
        return base64KeyPair;
    }


    public static class Base64KeyPair {
        private String publicKey;
        private String privateKey;

        public String getPublicKey() {
            return publicKey;
        }

        public void setPublicKey(String publicKey) {
            this.publicKey = publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }

        public void setPrivateKey(String privateKey) {
            this.privateKey = privateKey;
        }
    }


    /**
     * 测试
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        Base64KeyPair base64KeyPair = getBase64KeyPair();
        String publicKey = base64KeyPair.getPublicKey();
        String privateKey = base64KeyPair.getPrivateKey();
        System.out.println("publicKey=" + publicKey);
        System.out.println("privateKey=" + privateKey);

        System.out.println("begin testing...");
        String data = "hello, 世界！";

        String signed = sign(privateKey, data);
        System.out.println("signed=" + signed);
        boolean verified = verify(publicKey, data, signed);
        System.out.println("verified=" + verified);
    }

}
