package com.tiangong.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class AESUtils {

    private static final String KEY_ALGORITHM = "AES";


    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";//默认的加密算法

    //秘钥
    private static final String ASSETS_DEV_PWD_FIELD = "827ccb0eea8a706c4c34a16891f84e7b";


    private static final String UTF_8 = "utf-8";

    // 加密
    public static String encrypt(String sSrc) throws Exception {
        byte[] raw = ASSETS_DEV_PWD_FIELD.getBytes(UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes(UTF_8));
        return new Base64().encodeToString(encrypted);
    }

    // 解密
    public static String decrypt(String sSrc) {
        try {
            byte[] raw = ASSETS_DEV_PWD_FIELD.getBytes(UTF_8);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_ALGORITHM);
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            byte[] encrypted1 = new Base64().decode(sSrc);//先用base64解密
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original, UTF_8);
                return originalString;
            } catch (Exception e) {
                return null;
            }
        } catch (Exception ex) {
            return null;
        }
    }

    public static void main(String[] args) throws Exception {
        String content = "123456789";
        String encryptString = encrypt(content);
        System.out.println("加密：" + encryptString);
        System.out.println("解密：" + decrypt("OlkvKEvOHjlL8UfjdsMuJQ=="));
    }


}
