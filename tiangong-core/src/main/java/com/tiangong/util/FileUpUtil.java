package com.tiangong.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.tiangong.config.AlyOssConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/11/28 19:52
 */
@Slf4j
@Component
public class FileUpUtil {

    @Autowired
    private AlyOssConfigProperties alyOssConfigProperties;

    /**
     * 上传单个文件
     */
    public String uploadFile(MultipartFile file){
        OSS ossClient = new OSSClientBuilder().build(alyOssConfigProperties.getEndpoint(), alyOssConfigProperties.getAccessKey(), alyOssConfigProperties.getAccessSecret());

        //文件名
        String objectName = "jiali_tiangong/" + alyOssConfigProperties.getFileUrl() +"/" + System.currentTimeMillis() + NumberUtil.generateNumber(8) + file.getOriginalFilename();

        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(alyOssConfigProperties.getBucketName(), objectName, file.getInputStream());

            //上传
            PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest);
            //System.out.println("https://"+alyOssConfigProperties.getBucketName()+"."+alyOssConfigProperties.getEndpoint()+"/"+objectName);
            //返回访问连接
            return "https://"+alyOssConfigProperties.getBucketName()+"."+alyOssConfigProperties.getEndpoint()+"/" + alyOssConfigProperties.getFileUrl() +"/" +objectName;
        } catch (IOException e) {
            log.error("上传单个文件异常", e);
        }

        //关闭客户端
        ossClient.shutdown();
        return null;
    }

    /**
     * 上传单个文件
     */
    public String uploadFile2(MultipartFile file, String objectName){
        OSS ossClient = new OSSClientBuilder().build(alyOssConfigProperties.getEndpoint(), alyOssConfigProperties.getAccessKey(), alyOssConfigProperties.getAccessSecret());

        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(alyOssConfigProperties.getBucketName(), alyOssConfigProperties.getFileUrl() +"/" +objectName, file.getInputStream());

            //上传
            PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest);
            //System.out.println("https://"+alyOssConfigProperties.getBucketName()+"."+alyOssConfigProperties.getEndpoint()+"/"+objectName);
            //返回访问连接
            return "https://"+alyOssConfigProperties.getBucketName()+"."+alyOssConfigProperties.getEndpoint()+"/" + alyOssConfigProperties.getFileUrl() +"/"+objectName;
        } catch (IOException e) {
            log.error("上传单个文件异常", e);
        }

        //关闭客户端
        ossClient.shutdown();
        return null;
    }

    /**
     * 删除文件
     * @param objectKey
     */
    public void deleteFile(String objectKey) {
        OSS ossClient = new OSSClientBuilder().build(alyOssConfigProperties.getEndpoint(), alyOssConfigProperties.getAccessKey(), alyOssConfigProperties.getAccessSecret());
        try {
            // 创建删除请求
            //DeleteObjectsRequest deleteObjectRequest = new DeleteObjectsRequest(alyOssConfigProperties.getBucketName());

            // 删除对象
            ossClient.deleteObject(alyOssConfigProperties.getBucketName(), objectKey);
            System.out.println("文件 " + objectKey + " 已成功删除。");
        } catch (Exception e) {
            System.err.println("删除文件时发生错误: " + e.getMessage());
        } finally {
            // 关闭 OSS 客户端
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 上传单个文件
     */
    public String uploadFileToInputStream(String url, InputStream inputStream){
        OSS ossClient = new OSSClientBuilder().build(alyOssConfigProperties.getEndpoint(), alyOssConfigProperties.getAccessKey(), alyOssConfigProperties.getAccessSecret());

        //文件名
        String objectName = "jiali_tiangong/" + alyOssConfigProperties.getFileUrl() +"/" + url;

        try {
            // 创建上传文件的元数据。
            ObjectMetadata meta = new ObjectMetadata();

           // String md5 = BinaryUtil.toBase64String(BinaryUtil.calculateMd5(content.getBytes()));
            // 开启文件内容MD5校验。开启后OSS会把您提供的MD5与文件的MD5比较，不一致则抛出异常。
            //meta.setContentMD5(md5);
            // 指定上传的内容类型。内容类型决定浏览器将以什么形式、什么编码读取文件。如果没有指定则根据文件的扩展名生成，如果没有扩展名则为默认值application/octet-stream。
            meta.setContentType("text/plain");
            // 设置内容被下载时的名称。
            meta.setContentDisposition("attachment; filename=\""+url+"\"");
            // 设置上传文件的长度。如超过此长度，则上传文件会被截断，上传的文件长度为设置的长度。如小于此长度，则为上传文件的实际长度。
            //meta.setContentLength(content.length());
            // 设置内容被下载时网页的缓存行为。
//            meta.setCacheControl("Download Action");
//            // 设置缓存过期时间，格式是格林威治时间（GMT）。
//            //meta.setExpirationTime(DateUtil.parseIso8601Date("2022-10-12T00:00:00.000Z"));
//            // 设置内容被下载时的编码格式。
//            meta.setContentEncoding("gzip");
//            // 设置Header。
//            meta.setHeader("yourHeader", "yourHeaderValue");
            PutObjectResult putObjectResult = ossClient.putObject(alyOssConfigProperties.getBucketName(), objectName, inputStream, meta);
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(alyOssConfigProperties.getBucketName(), objectName);
            //设置链接有效时间为1年
            Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 365);
            request.setExpiration(expiration);
            URL httpUrl = ossClient.generatePresignedUrl(request);
            //返回访问连接
            System.out.println(httpUrl);
            return httpUrl.toString().replace("http:", "https:").replace("jiali-gdp.oss-cn-shenzhen.aliyuncs.com", "tgfiles.redaug.cn");
        } catch (Exception e) {
            log.error("上传单个文件异常", e);
        }

        //关闭客户端
        ossClient.shutdown();
        return null;
    }

    /**
     * 上传单个文件
     */
    public String uploadFileToString(String url, String fileUrl){
        OSS ossClient = new OSSClientBuilder().build(alyOssConfigProperties.getEndpoint(), alyOssConfigProperties.getAccessKey(), alyOssConfigProperties.getAccessSecret());

        //文件名
        String objectName = "jiali_tiangong/" + alyOssConfigProperties.getFileUrl() +"/" + url;

        try {
            PutObjectResult putObjectResult = ossClient.putObject(alyOssConfigProperties.getBucketName(), objectName, new File(fileUrl));
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(alyOssConfigProperties.getBucketName(), objectName);
            //设置链接有效时间为1年
            Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 365);
            request.setExpiration(expiration);
            URL httpUrl = ossClient.generatePresignedUrl(request);
            //返回访问连接
            System.out.println(httpUrl);
            return httpUrl.toString();
        } catch (Exception e) {
            log.error("上传单个文件异常", e);
        }

        //关闭客户端
        ossClient.shutdown();
        return null;
    }


}
