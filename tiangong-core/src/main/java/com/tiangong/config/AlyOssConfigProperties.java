package com.tiangong.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/11/28 20:11
 */
@Data
@Component
@ConfigurationProperties(prefix = "alyoss")
public class AlyOssConfigProperties {

    /**
     * 节点
     */
    private String endpoint;

    /**
     * assessKey
     */
    private String accessKey;

    /**
     * secret
     */
    private String accessSecret;

    /**
     * 存储空间名
     */
    private String bucketName;

    /**
     * 文件存储文件夹
     */
    private String fileUrl;
}
