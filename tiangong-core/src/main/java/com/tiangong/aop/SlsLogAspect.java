package com.tiangong.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tiangong.annotations.SlsLog;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.SlsLoggerUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023/11/16 15:55
 */
@Slf4j
@Aspect
@Component
public class SlsLogAspect {

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    private HttpServletRequest request;

    @Autowired
    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    /**
     * 配置切入点
     */
    @Pointcut("@annotation(com.tiangong.annotations.SlsLog)")
    public void slsLogPointcut() {

    }

    @Around("slsLogPointcut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        SlsLog annotation = method.getAnnotation(SlsLog.class);
        Date startDate = new Date();
        String requestId = request.getHeader("X-Request-Id");
        String token = request.getHeader("X-Token");
        String tokenRandomId = "";
        if (StrUtilX.isNotEmpty(token)) {
            tokenRandomId = token.substring(token.length() - 32);
        }
        Object proceed = pjp.proceed();


        // 获取响应参数
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonResponse = objectMapper.writeValueAsString(proceed);
        //JSONObject response = JSONObject.parseObject(jsonResponse);

        //获取方法参数
        String name = annotation.name();
        String message = annotation.message();
        String level = annotation.level();
        String topic = annotation.topic();
        String source = annotation.source();

        //获取酒店信息的时候common服务不打印响应日志
        if (StringUtils.isNotEmpty(token) && "/common/hotel/queryHotelInfoList".equals(topic)) {
            if (StringUtils.isNotEmpty(jsonResponse) && jsonResponse.length() > 100) {
                JSONObject object = new JSONObject();
                object.put("result", "基础信息返回有数据，且正常输出");
                jsonResponse = object.toJSONString();
            }
        }

        //请求的参数
        String param = "";
        Object[] args = pjp.getArgs();
        if (args != null && args.length > 0) {
            try {
                param = JSON.toJSONString(args[0]); //不是json参数会报错
            } catch (Exception e) {
                log.error("转换参数异常", e);
            }
        }


        Map<String, String> map = new HashMap<>();
        map.put("name", name);
        map.put("level", level);
        //HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        //String ip = StrUtils.getIp(request);
        map.put("message", message + " ip是：");
        map.put("request", param);
        map.put("response", jsonResponse);
        map.put("startDate", DateUtilX.dateToString(startDate, DateUtilX.hour_ms_format));
        Date endDate = new Date();
        map.put("requestId", requestId);
        map.put("tokenRandomId", tokenRandomId);
        map.put("endDate", DateUtilX.dateToString(endDate, DateUtilX.hour_ms_format));
        map.put("costTime", String.valueOf(endDate.getTime() - startDate.getTime()));
        try {
            //记录dhub请求状态
            String status = "1";//0成功，1失败
            if ("dhub".equals(source) && !topic.contains("-")) {
                if (Objects.nonNull(jsonResponse)) {
                    JSONObject object = JSONObject.parseObject(jsonResponse);
                    if (Objects.nonNull(object)) {
                        Object object1 = object.get("returnCode");
                        if ("000".equals(object1)) {
                            status = "0";
                        }
                    }
                }
                map.put("status", status);
            }

        } catch (Exception e) {
            map.put("status", "1");
        }
        slsLoggerUtil.saveLog(map, topic, source);
        return proceed;
    }
}
