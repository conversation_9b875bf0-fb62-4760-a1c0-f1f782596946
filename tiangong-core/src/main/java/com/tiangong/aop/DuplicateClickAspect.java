package com.tiangong.aop;

import com.alibaba.fastjson.JSON;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.redis.core.RedisTemplateX;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @create 2024/1/15 9:28
 */
@Slf4j
@Aspect
@Component
public class DuplicateClickAspect {

    private HttpServletRequest request;

    @Autowired
    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    /**
     * 配置切入点
     */
    @Pointcut("@annotation(com.tiangong.annotations.Duplicate)")
    public void duplicatePointcut() {
    }

    @Before("duplicatePointcut()")
    public Object before(JoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Object proceed = joinPoint.getArgs();
        System.out.println("-------------:" + joinPoint);
        //请求的参数
        String param = "";
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            try {
                param = JSON.toJSONString(args[0]); //不是json参数会报错
            } catch (Exception e) {
                log.error("转换参数异常", e);
            }
        }
        String token = request.getHeader("X-Token");
        String result = RedisTemplateX.get(joinPoint + ":" + param);
        if (result == null) {
            //设置2s内不能重复提交
            RedisTemplateX.setAndExpire(joinPoint + ":" + param, token, 2);
        }
        if (result != null && result.equals(token)) {
            throw new SysException(ErrorCodeEnum.DUPLICATE_CLICK_ERROR.errorCode, ErrorCodeEnum.DUPLICATE_CLICK_ERROR.errorDesc);
        }

        return proceed;
    }

}

