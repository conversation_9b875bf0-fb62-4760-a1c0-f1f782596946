package com.tiangong.listener;

import com.tiangong.initializer.CommonInitializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 *【消费类】商家信息
 */
@Component
@Slf4j
public class ConsumeOrgMessage implements MessageListener {

    @Override
    public void onMessage(Message message, byte[] bytes) {
        CommonInitializer.initializeOrgData();
    }
}
