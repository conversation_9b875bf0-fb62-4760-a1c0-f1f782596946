package com.tiangong.initializer;

import com.alibaba.fastjson.JSON;
import com.tiangong.finance.OrgDTO;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicReference;

/**
 * 公共初始化
 */
@Slf4j
@Component
public class CommonInitializer implements ApplicationRunner {

    // 使用原子引用保证线程安全
    private static final AtomicReference<OrgDTO> orgHolder = new AtomicReference<>();

    /**
     * 项目启动自动执行初始化
     */
    @Override
    public void run(ApplicationArguments args) {
        OrgDTO org = orgHolder.get();
        if (org == null) {
            initializeOrgData();
        }
    }

    /**
     * 手动初始化方法（对外暴露）
     * @return 是否初始化成功
     */
    public static synchronized boolean initializeOrgData() {
        try {
            String orgJson = RedisTemplateX.get(RedisKey.ORG_INFO);
            if (StrUtilX.isEmpty(orgJson)) {
                log.warn("机构信息未配置，请检查Redis键: {}", RedisKey.ORG_INFO);
                return false;
            }

            OrgDTO newOrg = JSON.parseObject(orgJson, OrgDTO.class);
            orgHolder.set(newOrg);
            log.info("机构信息初始化成功");
            return true;
        } catch (Exception e) {
            log.error("机构信息初始化失败", e);
            return false;
        }
    }

    /**
     * 获取机构信息（空安全处理）
     */
    public static OrgDTO getOrgInfo() {
        OrgDTO org = orgHolder.get();
        if (org == null) {
            throw new IllegalStateException("机构信息未初始化");
        }
        return org;
    }
}
