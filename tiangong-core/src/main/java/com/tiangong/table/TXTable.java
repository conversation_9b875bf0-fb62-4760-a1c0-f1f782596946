package com.tiangong.table;

import com.tiangong.enums.LanguageTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2023/11/16 12:00
 */
public class TXTable {

    /**
     * 获取具体区域表名
     * @param language
     * @return
     */
    public static String getAreaTXTable(String language){
        if (StringUtils.isEmpty(language)){
            return "t_baseinfo_areadata_zh_CN";
        }
        return "t_baseinfo_areadata_" + LanguageTypeEnum.getCodeByValue(language);
    }
}
