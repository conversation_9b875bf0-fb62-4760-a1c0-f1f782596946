package com.tiangong.common;

import java.math.BigDecimal;

/**
 * 数字常量
 * <AUTHOR>
 * @date 2025/6/9
 * @Description 数字常量
 */
public interface NumberConstant {
    /**
     * 100
     */
    BigDecimal ONE_HUNDRED = new BigDecimal("100");

    /**
     * 10
     */
    int TEN = 10;

    /**
     * 20
     */
    int TWENTY = 20;

    /**
     * 30
     */
    int THIRTY = 30;

    /**
     * 40
     */
    int FORTY = 40;

    /**
     * 50
     */
    int FIFTY = 50;
}
