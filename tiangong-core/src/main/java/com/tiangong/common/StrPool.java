package com.tiangong.common;

/**
 * 字符常量池
 *
 * <AUTHOR>
 * @date 2025/6/9
 * @Description 字符常量池
 */
public interface StrPool {
    char C_SPACE = ' ';
    char C_TAB = '\t';
    char C_DOT = '.';
    char C_SLASH = '/';
    char C_BACKSLASH = '\\';
    char C_CR = '\r';
    char C_LF = '\n';
    char C_UNDERLINE = '_';
    char C_COMMA = ',';
    char C_DELIM_START = '{';
    char C_DELIM_END = '}';
    char C_BRACKET_START = '[';
    char C_BRACKET_END = ']';
    char C_COLON = ':';
    char C_AT = '@';
    String TAB = "\t";
    String DOT = ".";
    String DOUBLE_DOT = "..";
    String SLASH = "/";
    String BACKSLASH = "\\";
    String CR = "\r";
    String LF = "\n";
    String CRLF = "\r\n";
    String UNDERLINE = "_";
    String DASHED = "-";
    String COMMA = ",";
    String CHINESE_COMMA = "，";
    String DELIM_START = "{";
    String DELIM_END = "}";
    String BRACKET_START = "[";
    String BRACKET_END = "]";
    String COLON = ":";
    String AT = "@";
    String HTML_NBSP = "&nbsp;";
    String HTML_AMP = "&amp;";
    String HTML_QUOTE = "&quot;";
    String HTML_APOS = "&apos;";
    String HTML_LT = "&lt;";
    String HTML_GT = "&gt;";
    String EMPTY_JSON = "{}";
    String ASTERISK = "*";
    String SPACE = " ";

    /**
     * Excel文件扩展名
     */
    String EXCEL_EXTENSION = "xlsx";
    String EXCEL_XLS_EXTENSION = "xls";
}
