package com.tiangong.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.exception.ErrorCode;
import com.tiangong.exception.GlobalErrorCodeConstants;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

/**
 * @Auther: Owen
 * @Date: 2019/4/23 23:06
 * @Description:
 */
@Data
@AllArgsConstructor
public class Response<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回結果(1:成功 0:失敗)
     */
    protected Integer result;

    /**
     * 错误编码
     */
    protected String failCode;

    /**
     * 错误原因
     */
    protected String failReason;

    /**
     * 返回对象
     */
    protected T model;

    /**
     * 每个返回对象带log唯一标识，排除问题用
     */
    private String logId = UUID.randomUUID().toString();

    public Response() {
    }

    ;

    public Response(Integer result) {
        this.result = result;
    }

    public Response(Integer result, String failCode, String failReason) {
        this.result = result;
        this.failCode = failCode;
        this.failReason = failReason;
    }

    public static <T> Response<T> error(Response<?> result) {
        return error(result.getFailCode(), result.getFailReason());
    }

    public static <T> Response<T> error(String failCode, String message) {
        Response<T> result = new Response<T>();
        result.result = 0;
        result.failCode = failCode;
        result.failReason = message;
        return result;
    }

    public static <T> Response<T> error(String failCode, String message, T model) {
        Response<T> result = new Response<T>();
        result.result = 0;
        result.failCode = failCode;
        result.failReason = message;
        result.model = model;
        return result;
    }

    public static <T> Response<T> error() {
        Response<T> result = new Response<>();
        result.result = 0;
        return result;
    }

    public static <T> Response<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> Response<T> error(ErrorCodeEnum errorCodeEnum) {
        return error(errorCodeEnum.errorCode, errorCodeEnum.errorDesc);
    }

    @SuppressWarnings("unchecked")
    public static <T> Response<T> failure(String failCode, String message, T data) {
        Response<T> result = new Response<>();
        result.result = 0;
        result.failCode = failCode;
        result.failReason = message;
        result.model = data;
        return result;
    }
    @SuppressWarnings("unchecked")
    public static <T> Response<T> failure(ErrorCodeEnum errorCodeEnum) {
        Response<T> result = new Response<>();
        result.result = 0;
        result.failCode = errorCodeEnum.getErrorCode();
        result.failReason = errorCodeEnum.getErrorDesc();
        result.model = null;
        return result;
    }
    @SuppressWarnings("unchecked")
    public static <T> Response<T> success(T data) {
        Response<T> result = new Response<T>();
        result.result = 1;
        result.failCode = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.failReason = GlobalErrorCodeConstants.SUCCESS.getMsg();
        result.model = data;

        return result;
    }

    public static <T> Response<T> successList(T data) {
        Response<T> result = new Response<>();
        result.result = 1;
        result.failCode = String.valueOf(ResultCodeEnum.SUCCESS.code);
        result.failReason = GlobalErrorCodeConstants.SUCCESS.getMsg();
        result.model = data;
        return result;
    }

    public static Response<Object> success() {
        return success(1);
    }


    @JsonIgnore // 避免 jackson 序列化
    public boolean isSuccess() {
        return isSuccess(result);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    public static boolean isSuccess(Integer result) {
        return result != null && ResultCodeEnum.SUCCESS.code == result;
    }
}
