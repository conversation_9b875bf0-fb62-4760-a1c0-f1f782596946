package com.tiangong.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/1/26 14:27
 */
@Getter
@AllArgsConstructor
public enum LabelTypeEnum {

    PLATFORM(1, "协议"),
    GROUP_ROOM(2, "平台自签"),
    AGREEMENT(3, "团房"),
    FIXED_PRICE_AGREEMENT(4, "固定协议价"),
    DISCOUNT_PRICE_AGREEMENT(5, "折扣协议价"),
    BALE_PRICE(6, "打包价"),
    ;

    private final Integer no;
    private final String desc;

    public static String getDescByNo(Integer key) {
        String desc = null;
        for (LabelTypeEnum labelTypeEnum : LabelTypeEnum.values()) {
            if (Objects.equals(labelTypeEnum.no, key)) {
                desc = labelTypeEnum.desc;
                break;
            }
        }
        return desc;
    }

    public static Integer getNoByDesc(String desc) {
        Integer no = null;
        for (LabelTypeEnum labelTypeEnum : LabelTypeEnum.values()) {
            if (Objects.equals(labelTypeEnum.desc, desc)) {
                no = labelTypeEnum.no;
                break;
            }
        }
        return no;
    }

    /**
     * 是否为协议标签
     *
     * @param no
     * @return
     */
    public static boolean isContractLabel(Integer no) {
        return ObjectUtil.isNotEmpty(no) && (no.equals(LabelTypeEnum.PLATFORM.getNo()) ||
                no.equals(LabelTypeEnum.GROUP_ROOM.getNo()) ||
                no.equals(LabelTypeEnum.FIXED_PRICE_AGREEMENT.getNo()) ||
                no.equals(LabelTypeEnum.DISCOUNT_PRICE_AGREEMENT.getNo()));
    }
}
