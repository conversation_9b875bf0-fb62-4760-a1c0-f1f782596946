package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 税费适用范围
 */
@Getter
@AllArgsConstructor
public enum TaxScopeOfApplicationEnum {

    ALL(0, "无限制"),
    COUNTRY(1, "国家"),
    CITY(2, "城市"),
    HOTEL(3, "酒店"),
    ;

    private final Integer code;
    private final String desc;

    public static TaxScopeOfApplicationEnum taxScopeOfApplication(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaxScopeOfApplicationEnum taxScopeOfApplication : TaxScopeOfApplicationEnum.values()) {
            if (taxScopeOfApplication.code.equals(code)) {
                return taxScopeOfApplication;
            }
        }
        return null;
    }

}
