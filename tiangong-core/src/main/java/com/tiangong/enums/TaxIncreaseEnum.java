package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: tiangong
 * @ClassName TaxAndFeesIncreaseTypeEnum
 * @description: 税费加辐类型
 * @author: 湫
 * @create: 2024/08/30/ 17:05
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum TaxIncreaseEnum {

    FIXED(0, "固定加辐"),
    PERCENTAGE(1, "百分比加辐"),
    ;

    private final Integer code;
    private final String desc;

    public static TaxIncreaseEnum taxIncrease(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaxIncreaseEnum taxIncrease : TaxIncreaseEnum.values()) {
            if (taxIncrease.code.equals(code)) {
                return taxIncrease;
            }
        }
        return null;
    }

}
