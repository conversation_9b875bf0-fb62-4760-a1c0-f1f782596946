package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: tiangong
 * @ClassName TaxAndFeeStandards
 * @description: 税费收费标准
 * @author: 湫
 * @create: 2024/08/30/ 17:03
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum TaxStandardsEnum {

    MIDNIGHT(0, "/次"),
    NIGHT(1, "/晚");

    private final Integer code;
    private final String desc;

    public static TaxStandardsEnum taxStandards(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaxStandardsEnum taxStandards : TaxStandardsEnum.values()) {
            if (taxStandards.code.equals(code)) {
                return taxStandards;
            }
        }
        return null;
    }
}
