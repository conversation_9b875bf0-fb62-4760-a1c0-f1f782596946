package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvoiceTitleTypeEnum {

    COMPANY(0, "企业单位"),
    PERSON(1, "个人/非企业单位");

    public final int no;
    public final String desc;

    public static String getDesc(int no) {
        for (InvoiceTitleTypeEnum invoiceTitleTypeEnum : InvoiceTitleTypeEnum.values()) {
            if (invoiceTitleTypeEnum.no == no) {
                return invoiceTitleTypeEnum.desc;
            }
        }
        return null;
    }
}
