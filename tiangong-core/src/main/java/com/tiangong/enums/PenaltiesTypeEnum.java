package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 罚金类型枚举
 * @author: qiu
 * @create: 2024-06-13 10:39
 */
@Getter
@AllArgsConstructor
public enum PenaltiesTypeEnum {
    NIGHTS(1, "晚"),
    FIXEDAMOUNT(2, "固定金额"),
    PERCENTAGE(3, "百分比"),
    // 第一晚金额的百分比扣款
    FIRSTNIGHTPERCENTAGE(4, "首晚百分比");

    private final Integer key;
    private final String desc;
}