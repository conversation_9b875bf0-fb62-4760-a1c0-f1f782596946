package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SendStatusEnum {

    NO_SEND(0, "未发送"),
    SEND(1, "已发送"),
    SEND_FAIL(2, "发送失败");

    public final int no;
    public final String desc;

    public static String getDesc(int no) {
        for (SendStatusEnum sendStatusEnum : SendStatusEnum.values()) {
            if (sendStatusEnum.no == no) {
                return sendStatusEnum.desc;
            }
        }
        return null;
    }
}
