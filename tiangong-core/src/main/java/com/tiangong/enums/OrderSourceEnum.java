package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderSourceEnum {

    MANUAL(1, "手工单"),
    GDP(2, "GDP"),
    API(3, "API");

    public final int key;
    public final String desc;

    public static String getDescByKey(Integer key) {
        String desc = null;
        if (key == null) {
            return desc;
        }
        for (OrderSourceEnum orderSourceEnum : OrderSourceEnum.values()) {
            if (orderSourceEnum.key == key) {
                desc = orderSourceEnum.desc;
                break;
            }
        }
        return desc;
    }
}
