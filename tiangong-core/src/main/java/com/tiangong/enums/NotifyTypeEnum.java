package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知类型
 */
@Getter
@AllArgsConstructor
public enum NotifyTypeEnum {

    IMPORT_NOTIFY(0, "重要通知"),
    CHECKIN_LIMIT(1, "入住人群限制"),
    CHECKIN_TYPE(2, "入住方式"),
    HOTEL_IMPORT_NOTIFY(3, "酒店重要通知"),
    CITY_NOTIFY(4, "城市通知"),
    HOTEL_TITLE(5, "酒店提示");

    public final Integer no;
    public final String desc;
}
