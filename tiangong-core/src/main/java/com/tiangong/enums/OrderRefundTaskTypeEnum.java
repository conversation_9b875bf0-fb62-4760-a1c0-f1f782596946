package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderRefundTaskTypeEnum {

    AUTO(1, "自动"),
    MANUAL(0, "人工");

    public final int key;
    public final String value;

    public static String getValueByKey(int key) {
        String value = "";
        for (OrderRefundTaskTypeEnum orderRefundTaskTypeEnum : OrderRefundTaskTypeEnum.values()) {
            if (orderRefundTaskTypeEnum.key == key) {
                value = orderRefundTaskTypeEnum.value;
                break;
            }
        }
        return value;
    }
}