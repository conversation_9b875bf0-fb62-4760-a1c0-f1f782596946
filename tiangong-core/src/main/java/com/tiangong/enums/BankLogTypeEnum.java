package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 银行流水类型枚举
 * 
 * <AUTHOR>
 * @date 2025/1/9
 */
@Getter
@AllArgsConstructor
public enum BankLogTypeEnum {

    /**
     * 收入
     */
    INCOME(1, "收入"),

    /**
     * 支出
     */
    EXPENDITURE(2, "支出");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BankLogTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BankLogTypeEnum logType : values()) {
            if (logType.getCode().equals(code)) {
                return logType;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescriptionByCode(Integer code) {
        BankLogTypeEnum logType = getByCode(code);
        return logType != null ? logType.getDescription() : null;
    }
}
