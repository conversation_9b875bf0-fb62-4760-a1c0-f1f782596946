package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AddressEnum {
    HOTEL(1, 10, "H", "酒店"),
    CITY(2, 3, "C", "城市"),
    DISTRICT(3, 4, "D", "行政区"),
    BUSINESS(4, 5, "S", "商业区"),
    TRIP(5, 6, "T", "旅游区"),
    CUA(6, 7, "Z", "城市及周边"),
    SMALL_CITY(7, 8, "LC", "比城市小的区"),
    ADDRESS(8, 9, "A", "地点"),
    PROVINCE(9, 2, "P", "省份"),
    COUNTRY(10, 1, "K", "国家"),
    GROUP(11, 11, "G", "集团"),
    BRAND(12, 12, "B", "品牌"),

    ;

    private final Integer key;
    private final Integer type;
    private final String abbreviation;
    private final String value;

}
