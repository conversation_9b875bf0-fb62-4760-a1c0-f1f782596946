package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HotelSettingEnum {
    GEOINFO("geoInfo", "geoInfo", "经纬度"),
    LOCATION("location", "location", "位置"),
    HOTELSTAR("hotelStar", "hotelStar", "酒店星级"),
    INOUTPOLICY("inOutPolicy", "inOutPolicy", "入离政策"),
    ADDBEDCHARGE("addBedCharge", "addBedCharge", "收费标准"),
    TEL("tel", "tel", "电话"),
    INFORM("inform", "inform", "通知"),
    FACILITIES("facilities", "facilities", "设施集合"),
    IMAGES("images", "images", "图片集合"),
    PARKINGS("parkings", "parkings", "停车场集合"),
    ROOMLIST("roomList", "roomList", "房型集合"),
    MEETINGS("meetings", "meetings", "会议室集合"),
    RATING("rating", "rating", "酒店评分"),
    CERTIFICATES("certificates", "certificates", "资质"),
    VIDEOINFOS("videoInfos", "videoInfos", "视频"),
    BREAKFAST("breakfast", "breakfast", "早餐"),
    ADDBEDPOLICY("addBedPolicy", "addBedPolicy", "加床政策"),
    CHILDPOLICY("childPolicy", "addBedPolicy", "儿童政策"),
    PETPOLICY("petPolicy", "petPolicy", "宠物政策"),
    HOTELTEXTPOLICY("hotelTextPolicy", "hotelTextPolicyList", "酒店文本政策");

    public final String key;
    public final String value;
    private final String description;

    public static String getValue(String key) {
        for (HotelSettingEnum hotelSettingEnum : HotelSettingEnum.values()) {
            if (key.equals(hotelSettingEnum.key)) {
                return hotelSettingEnum.value;
            }
        }
        return null;
    }
}
