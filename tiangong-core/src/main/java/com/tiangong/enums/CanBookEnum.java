package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CanBookEnum {
    CAN_NOT_BOOK(0, "不可定"),
    CAN_BOOK(1, "可定"),
    CAN_PART_BOOK(2, "部分可订(即部分日期有房部分日期满房或无价格等)"),
    CAN_NOT_BOOK_RESTRICT(3, "查看(一般是不满足预订或入住条款)"),
    CAN_NOT_MEET_WITH_PRE_RESTRICT(4, "不满足预定条款"),
    ;

    public final Integer value;
    public final String description;
}
