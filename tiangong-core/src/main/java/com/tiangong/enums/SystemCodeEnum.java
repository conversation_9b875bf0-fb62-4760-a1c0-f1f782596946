package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 早餐
 */
@Getter
@AllArgsConstructor
public enum SystemCodeEnum {

    ORDERCODE("MH", "seq_order", "订单编码"),
    SUPPLYORDERCODE("S", "seq_supply_order", "供货单编码"),
    AGENTSTATEMENTCODE("BA", "seq_agent_statement", "分销账单编码"),
    SUPPLIERSTATEMENTCODE("BS", "seq_supplier_statement", "供应账单编码"),
    WORKORDERCODE("W", "seq_work_order", "工单编码"),
    ORGCODE("E", "seq_org_code", "机构编码"),
    SUPPLIERCODE("TS", "seq_supplier_code", "供应商编码"),
    CUSTOMERCODE("TF", "seq_customer_code", "分销商编码"),
    PARTNERCODE("TP", "seq_partner_code", "合作商编码"),
    SUPPLIERREWARDSTATEMENTCODE("BS", "seq_supplier_reward_statement", "供应奖励账单编码"),
    SUPPLIERREBATESTATEMENTCODE("BS", "seq_supplier_rebate_statement", "供应返佣账单编码"),
    SUPPLIERSETTLEWORKORDERCODE("TT", "seq_settle_work_order", "记账工单编码"),
    SEQ_SETTLE_JOB_CODE("ST", "seq_settle_job_code", "自助结算任务编码"),
    SEQ_SETTLE_PAY_CODE("PAY", "seq_settle_pay_code", "自助结算支付流水号"),
    SEQ_ORDER_REFUND_TASK_CODE("RT", "seq_order_refund_task_code", "订单退款任务编码"),
    SEQ_ORDER_DEDUCTION_TASK_CODE("DT", "seq_order_deduction_task_code", "订单担保扣款任务编码"),
    SEQ_ORDER_STATEMENT_TASK_CODE("TH", "seq_order_statement_task_code", "订单记账任务编码"),
    CHANGE_WORD_ORDER_TASK_CODE("TM", "seq_change_word_order_task_code", "退改工单任务编码"),
    FINANCING_CODE("RZDD", "seq_financing_code", "融资任务编码"),
    ;

    public final String letter;
    public final String code;
    public final String desc;

    public static String getLetterByCode(String code) {
        String letter = null;
        for (SystemCodeEnum systemCodeEnum : SystemCodeEnum.values()) {
            if (systemCodeEnum.code.equals(code)) {
                letter = systemCodeEnum.letter;
                break;
            }
        }
        return letter;
    }
}
