package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StatementTypeEnum {

    SUPPLY_ORDER_AMT(0, "供货单金额"),
    REWARD_AMT(1, "供货单奖励"),
    REBATE_AMT(2, "供货单返佣");

    public final Integer key;
    public final String value;

    public static Integer getKeyByValue(String value) {
        Integer key = null;
        for (StatementTypeEnum checkStatusEnum : StatementTypeEnum.values()) {
            if (checkStatusEnum.value.equals(value)) {
                key = checkStatusEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (StatementTypeEnum checkStatusEnum : StatementTypeEnum.values()) {
            if (checkStatusEnum.key.equals(key)) {
                value = checkStatusEnum.value;
                break;
            }
        }
        return value;
    }

    public static StatementTypeEnum getEnumByKey(Integer key) {
        StatementTypeEnum checkStatusEnum = null;
        for (StatementTypeEnum hotelFeature : StatementTypeEnum.values()) {
            if (hotelFeature.key.equals(key)) {
                checkStatusEnum = hotelFeature;
                break;
            }
        }
        return checkStatusEnum;
    }

    public static StatementTypeEnum getEnumBySupplyOrderType(Integer supplyOrderType) {
        StatementTypeEnum checkStatusEnum = null;
        for (StatementTypeEnum hotelFeature : StatementTypeEnum.values()) {
            if (hotelFeature.key.equals(supplyOrderType + 1)) {
                checkStatusEnum = hotelFeature;
                break;
            }
        }
        return checkStatusEnum;
    }
}
