package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批状态枚举
 * 
 * <AUTHOR>
 * @date 2025/1/9
 */
@Getter
@AllArgsConstructor
public enum ApprovalStatusEnum {

    /**
     * 待审批
     */
    PENDING(1, "待审批"),

    /**
     * 审批中
     */
    IN_PROGRESS(2, "审批中"),

    /**
     * 审批通过
     */
    APPROVED(3, "审批通过"),

    /**
     * 审批拒绝
     */
    REJECTED(4, "审批拒绝");

    /**
     * 状态编码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static ApprovalStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApprovalStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescriptionByCode(Integer code) {
        ApprovalStatusEnum status = getByCode(code);
        return status != null ? status.getDescription() : null;
    }
}
