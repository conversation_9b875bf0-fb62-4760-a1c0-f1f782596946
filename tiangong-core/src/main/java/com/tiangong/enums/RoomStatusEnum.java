package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 关房设置
 */
@Getter
@AllArgsConstructor
public enum RoomStatusEnum {

    CLOSE(0, "CLOS<PERSON>", "关房"),
    OPEN(1, "<PERSON><PERSON><PERSON>", "开房");

    public final int no;
    public final String code;
    public final String desc;

    public static String getDesc(int no) {
        for (RoomStatusEnum roomStatusEnum : RoomStatusEnum.values()) {
            if (no == roomStatusEnum.no) {
                return roomStatusEnum.desc;
            }
        }
        return null;
    }
}
