package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum RequestUrlEnums implements Serializable {

    //login
    COMMON_AUTH_SENDVERIFICATIONCODE("/common/auth/sendVerificationCode", "获取验证码", 0, 7),
    COMMON_AUTH_LOGIN("/common/auth/login", "登录", 0, 8),
    COMMON_FUZZY_QUERYAGENTCHANNELCODE("/common/fuzzy/queryAgentChannelCode", "查询客户渠道编码", 1, 0),

    //notifyLogPop
    //FINANCE_NOTIFY_FINANCENOTIFICATIONLOGLIST("/finance/notify/financeNotificationLogList", "查询通知记录", 1, 0),

    //agentStatementDetail
    FINANCE_AGENT_QUERYSTATEMENTDETAIL("/finance/agent/queryStatementDetail", "查询账单详情", 1, 0),
    FINANCE_AGENT_MODIFYSTATEMENTNAME("/finance/agent/modifyStatementName", "修改账单名称", 1, 3),
    FINANCE_AGENT_MODIFYSETTLEMENTDATE("/finance/agent/modifySettlementDate", "修改结算日期", 1, 3),
    FINANCE_AGENT_MODIFYSTATEMENTSTATUS("/finance/agent/modifyStatementStatus", "修改账单状态", 1, 3),
    FINANCE_AGENT_QUERYSTATEMENTORDERLIST("/finance/agent/queryStatementOrderList", "查看账单明细", 1, 0),
    FINANCE_AGENT_QUERYUNCHECKOUTORDER("/finance/agent/queryUnCheckOutOrder", "查询未出账订单", 1, 0),
    FINANCE_AGENT_ADDSTATEMENTORDERLIST("/finance/agent/addStatementOrderList", "添加账单明细", 1, 1),
    FINANCE_AGENT_DELETESTATEMENTORDERLIST("/finance/agent/deleteStatementOrderList", "删除账单明细", 1, 2),
    FINANCE_AGENT_QUERYUPDATEDSTATEMENTORDERLIST("/finance/agent/queryUpdatedStatementOrderList", "查询账单明细变更", 1, 0),
    FINANCE_AGENT_UPDATESTATEMENTORDERLIST("/finance/agent/updateStatementOrderList", "更新账单明细", 1, 3),
    FINANCE_AGENT_EXPORTSTATEMENT("/finance/agent/exportStatement", "导出账单", 1, 4),
    //    FINANCE_NOTIFY_FINANCENOTIFICATIONLOGLIST("/finance/notify/financeNotificationLogList", "查询通知记录", 1, 0),
    FINANCE_AGENT_NOTIFYCOLLECTIONOFSTATEMENT("/finance/agent/notifyCollectionOfStatement", "通知收款", 1, 24),

    //agentStatementOnAccount
    FINANCE_AGENT_CREATESTATEMENT("/finance/agent/createStatement", "创建账单", 1, 1),
    FINANCE_AGENT_QUERYSTATEMENTLIST("/finance/agent/queryStatementList", "查询已出账单", 1, 0),
    FINANCE_AGENT_QUERYUNCHECKOUTAGENTLIST("/finance/agent/queryUncheckOutAgentList", "查询未出账", 1, 0),
//    COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),

    //agentStatementOnTime
    ORDER_FINANCE_QUERYONTIMEORDERLIST("/order/finance/queryOnTimeOrderList", "查询单结订单", 1, 0),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    //FINANCE_NOTIFY_FINANCENOTIFICATIONLOGLIST("/finance/notify/financeNotificationLogList", "查询通知记录", 1, 0),

    //orderLocks
    FINANCE_LOCK_QUERYORDERLIST("/finance/lock/queryOrderList", "查询财务订单锁", 1, 0),
    FINANCE_LOCK_LOCKORDER("/finance/lock/lockOrder", "财务订单加锁", 1, 5),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),

    //supplyOrderLocks
    FINANCE_LOCK_QUERYSUPPLYORDERLIST("/finance/lock/querySupplyOrderList", "查询财务供货单锁", 1, 0),
    FINANCE_LOCK_LOCKSUPPLYORDER("/finance/lock/lockSupplyOrder", "财务供货单加锁", 1, 5),
//    COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),

    //Settlements
    FINANCE_WORKORDER_QUERYWORKORDERLIST("/finance/workorder/queryWorkOrderList", "查询财务工单", 1, 0),
    FINANCE_WORKORDER_CONFIRMWORKORDER("/finance/workorder/confirmWorkOrder", "确认工单", 1, 6),
    FINANCE_WORKORDER_QUERYWORKORDERDETAIL("/finance/workorder/queryWorkOrderDetail", "查询工单详情", 1, 0),
    //FINANCE_DELETEWORKORDER("/finance/deleteWorkOrder","",1,-1),
    FINANCE_WORKORDER_DELETEWORKORDER("/finance/workorder/deleteWorkOrder", "删除工单", 1, 2),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),

    //supplierStatementDetail
    FINANCE_SUPPLIER_QUERYSTATEMENTDETAIL("/finance/supplier/queryStatementDetail", "查询账单详情", 1, 0),
    FINANCE_SUPPLIER_MODIFYSTATEMENTNAME("/finance/supplier/modifyStatementName", "修改账单名称", 1, 3),
    FINANCE_SUPPLIER_MODIFYSETTLEMENTDATE("/finance/supplier/modifySettlementDate", "修改结算日期", 1, 3),
    FINANCE_SUPPLIER_MODIFYSTATEMENTSTATUS("/finance/supplier/modifyStatementStatus", "修改账单状态", 1, 3),
    FINANCE_SUPPLIER_QUERYSTATEMENTORDERLIST("/finance/supplier/queryStatementOrderList", "查询账单明细", 1, 0),
    FINANCE_SUPPLIER_QUERYUNCHECKOUTSUPPLYORDER("/finance/supplier/queryUnCheckOutSupplyOrder", "查询未出账订单", 1, 0),
    FINANCE_SUPPLIER_ADDSTATEMENTORDERLIST("/finance/supplier/addStatementOrderList", "添加账单明细", 1, 1),
    FINANCE_SUPPLIER_DELETESTATEMENTORDERLIST("/finance/supplier/deleteStatementOrderList", "删除账单明细", 1, 2),
    FINANCE_SUPPLIER_QUERYUPDATEDSTATEMENTORDERLIST("/finance/supplier/queryUpdatedStatementOrderList", "查询账单明细变更", 1, 0),
    FINANCE_SUPPLIER_UPDATESTATEMENTORDERLIST("/finance/supplier/updateStatementOrderList", "更新账单明细", 1, 3),
    FINANCE_SUPPLIER_EXPORTSTATEMENT("/finance/supplier/exportStatement", "导出账单", 1, 4),
    FINANCE_NOTIFY_FINANCENOTIFICATIONLOGLIST("/finance/notify/financeNotificationLogList", "查询通知记录", 1, 0),
    FINANCE_SUPPLYAUTORECONCILIATION_COMPARISONBILLADD("/finance/supplyAutoReconciliation/comparisonBillAdd", "添加/减少/重新比对账单", 1, 3),
    FINANCE_SUPPLIERIMPORTSTATEMENT_SUPPLIERSTATEMENTINCOMPARISON("/finance/supplierImportStatement/supplierStatementInComparison", "查询比对中的供应商账单", 1, 0),
    FINANCE_SUPPLYAUTORECONCILIATION_SUPPLYAUTORECONCILIATIONPAGE("/finance/supplyAutoReconciliation/supplyAutoReconciliationPage", "分页查询自动对账列表", 1, 0),
    FINANCE_SUPPLYAUTORECONCILIATION_RECONCILIATIONRESULTTYPECOUNT("/finance/supplyAutoReconciliation/reconciliationResultTypeCount", "统计自动对账结果各种类型数量", 1, 0),
    FINANCE_SUPPLYAUTORECONCILIATION_RECONCILIATIONRESULTEXPORT("/finance/supplyAutoReconciliation/reconciliationResultExport", "导出比对结果", 1, 4),
    FINANCE_SUPPLIERIMPORTSTATEMENT_CONTRASTSTATEMENTPAGE("/finance/supplierImportStatement/contrastStatementPage", "分页查询比对账单列表", 1, 0),
    //FINANCE_SUPPLIER_NOTIFYPAYMENTOFSTATEMENT("/finance/supplier/notifyPaymentOfStatement", "通知付款", 1, 24),
    FINANCE_SUPPLIER_QUERYSTATEMENTORDERWITHORDERCODELIST("/finance/supplier/queryStatementOrderWithOrderCodeList", "查询账单明细带订单号", 1, 0),
    FINANCE_SUPPLYAUTORECONCILIATION_QUERYSTATEMENTSTATUS("/finance/supplyAutoReconciliation/queryStatementStatus", "查询账单状态", 1, 0),

    //supplierStatementOnAccount
    FINANCE_SUPPLIER_CREATESTATEMENT("/finance/supplier/createStatement", "创建账单", 1, 1),
    FINANCE_SUPPLIER_QUERYSTATEMENTLIST("/finance/supplier/queryStatementList", "查询已出账单", 1, 0),
    FINANCE_SUPPLIER_QUERYUNCHECKOUTSUPPLIERLIST("/finance/supplier/queryUncheckOutSupplierList", "查询未出账", 1, 0),
    //    COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),
    FINANCE_SUPPLIERIMPORTSTATEMENT_SUPPLIERSTATEMENTIMPORT("/finance/supplierImportStatement/supplierStatementImport", "导入供应商的账单", 1, 26),
    FINANCE_SUPPLIERIMPORTSTATEMENT_SUPPLIERIMPORTANDRECONCILIATION("/finance/supplierImportStatement/supplierImportAndReconciliation", "导入供应商的账单和对比账单", 1, 26),
    FINANCE_SUPPLIERIMPORTSTATEMENT_SUPPLIERIMPORTSTATEMENTPAGE("/finance/supplierImportStatement/supplierImportStatementPage", "分页查询供应商导入账单列表", 1, 0),
    FINANCE_SUPPLIERIMPORTSTATEMENTANNEX_SUPPLIERIMPORTSTATEMENTANNEXPAGE("/finance/supplierImportStatementAnnex/supplierImportStatementAnnexPage", "分页查询供应商导入的账单的附件列表", 1, 0),
    FINANCE_SUPPLIERIMPORTSTATEMENT_SUPPLIERIMPORTSTATEMENTDEL("/finance/supplierImportStatement/supplierImportStatementDel", "删除供应商导入账单", 1, 2),
    FINANCE_SUPPLIERIMPORTSTATEMENT_SUPPLIERSTATEMENTTEMPLATEGENERATE("/finance/supplierImportStatement/supplierStatementTemplateGenerate", "生成供应商导入的账单模板", 1, 21),

    //supplierStatementOnTime
    ORDER_FINANCE_QUERYONTIMESUPPLYORDERLIST("/order/finance/queryOnTimeSupplyOrderList", "查询单结订单", 1, 0),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),

    //Home
//    ORDER_QUERYORDERSTATISTICS("/order/queryOrderStatistics","统计订单列表",1,0),
//    COMMON_STATISTICS_QUERYAGENTSTATISTICS("/common/statistics/queryAgentStatistics", "统计客户对账", 1, 0),
//    COMMON_STATISTICS_QUERYFINANCESTATISTICS("/common/statistics/queryFinanceStatistics", "统计财务概况", 1, 0),
//    COMMON_STATISTICS_QUERYCASHSTATUSSTATISTICS("/common/statistics/queryCashStatusStatistics", "统计现金状态", 1, 0),
//    COMMON_STATISTICS_QUERYOPERATESTATISTICS("/common/statistics/queryOperateStatistics", "经营概况 1.0", 1, 0),
//    COMMON_STATISTICS_QUERYCASHIERSTATISTICS("/common/statistics/queryCashierStatistics", "统计出纳记账", 1, 0),

    //hotelInfo
    COMMON_HOTEL_QUERYHOTELLISTBYSORT("/common/hotel/queryHotelListBySort", "查询酒店列表", 1, 0),
    COMMON_HOTEL_MODIFYHOTELSORT("/common/hotel/modifyHotelSort", "修改酒店的排序", 1, 3),
    //COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData","查询城市列表",1,0),
    //COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),

    //hotelSort
    COMMON_HOTELRECOMMEND_HOTELRECOMMENDLIST("/common/hotelRecommend/hotelRecommendList", "查询酒店排序列表", 1, 0),
    COMMON_HOTELRECOMMEND_HOTELRECOMMENDDEL("/common/hotelRecommend/hotelRecommendDel", "删除酒店排序", 1, 2),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    //COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),


    //addEditSort
    COMMON_HOTELRECOMMEND_HOTELRECOMMENDEDIT("/common/hotelRecommend/hotelRecommendEdit", "编辑酒店排序", 1, 3),
    COMMON_HOTELRECOMMEND_HOTELRECOMMENDADD("/common/hotelRecommend/hotelRecommendAdd", "新增酒店排序", 1, 1),

    //facilityDivision
    COMMON_HOTELFACILITY_QUERYFACILITYLIST("/common/hotelFacility/queryFacilityList", "查询酒店设施", 1, 0),

    //hotelDivision
    //COMMON_BASEINFOHOTEL_QUERYHOTELINFO("/common/baseinfoHotel/queryHotelInfo","查询酒店基本信息",1,0),

    //hotelPolicy
    COMMON_BASEINFOHOTEL_QUERYHOTELPOLICYDETAIL("/common/baseinfoHotel/queryHotelPolicyDetail", "查询酒店政策", 1, 0),

    //imgDivision
    COMMON_HOTEL_QUERYHOTELPHOTOLIST("/common/hotel/queryHotelPhotoList", "查询酒店图片列表", 1, 0),

    //roomDivision
    COMMON_BASEINFOROOM_BASEINFOROOMLIST("/common/baseinfoRoom/baseinfoRoomList", "查询房型信息列表", 1, 0),

    //roomPolicy
    COMMON_HOTELFACILITY_QUERYROOMFACILITYLIST("/common/hotelFacility/queryRoomFacilityList", "查询房型设备", 1, 0),

    //channelVdoing
    PRODUCT_PROORGAVAILABLE_PROAGENTAVAILABLEPAGE("/product/proOrgAvailable/proAgentAvailablePage", "查询客户维度可见性列表", 1, 0),
    PRODUCT_PROORGAVAILABLE_PROAGENTAVAILABLEADD("/product/proOrgAvailable/proAgentAvailableAdd", "新增客户维度", 1, 1),
    PRODUCT_PROORGAVAILABLE_PROORGAVAILABLEDEL("/product/proOrgAvailable/proOrgAvailableDel", "删除供应商维度可见性", 1, 2),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),

    //supplierVdoing
    //PRODUCT_PROORGAVAILABLE_PROORGAVAILABLEDEL("/product/proOrgAvailable/proOrgAvailableDel","删除供应商维度可见性",1,2),
    //PRODUCT_PROORGAVAILABLE_PROSUPPIERAVAILABLEPAGE("/product/proOrgAvailable/proSuppierAvailablePage","查询供应商维度可见性列表",1,0),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),

    //addSuppler
    PRODUCT_PROORGAVAILABLE_PROSUPPLIERAVAILABLEADD("/product/proOrgAvailable/proSupplierAvailableAdd", "新增供应商维度可见性", 1, 1),

    //cityMap
    COMMON_SUPPLYAREAMAPPING_SUPPLYCITYMAPPINGLIST("/common/supplyAreaMapping/supplyCityMappingList", "查询供应商城市映射", 1, 0),
    //COMMON_FUZZY_QUERYDIRECTSUPPLYLIST("/common/fuzzy/queryDirectSupplyList","查询供应商列表",1,0),
    //COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData","查询城市列表",1,0),
    COMMON_BASEINFOAREADATA_QUERYCITYLIST("/common/baseinfoAreadata/queryCityList", "查询我方城市名称", 1, 0),

    //hotelMap
    COMMON_SUPPLYHOTELMAPPING_QUERYHOTELMAPPINGLIST("/common/supplyHotelMapping/queryHotelMappingList", "查询供应商酒店映射列表", 1, 0),
    //COMMON_FUZZY_QUERYDIRECTSUPPLYLIST("/common/fuzzy/queryDirectSupplyList","查询供应商列表",1,0),
    //COMMON_BASEINFOAREADATA_COUNTRYLIST("/common/baseinfoAreadata/countryList","查询国家列表",1,0),
    COMMON_SUPPLY_HOTEL_NOTMAPPING("/common/supply/hotel/notMapping", "酒店监控列表-不再映射", 1, 3),
    COMMON_SUPPLYHOTELMAPPING_DISCONNECT_HOTELMAPPING("/common/supplyHotelMapping/disConnect/hotelMapping", "酒店映射断开映射", 1, 3),
    COMMON_SUPPLY_HOTEL_RECOVERMAPPING("/common/supply/hotel/recoverMapping", "酒店映射恢复映射", 1, 3),
    //COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData","查询城市列表",1,0),
    //COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
    COMMON_SUPPLY_HOTEL_SUPPLYHOTELPAGE("/common/supply/hotel/supplyHotelPage", "查询供应商酒店名", 1, 0),

    //hotelMonitor
    COMMON_MONITORMAPPINGHOTEL_QUERYMONITORMAPPINGHOTELLIST("/common/monitorMappingHotel/queryMonitorMappingHotelList", "查询已映射酒店监控列表", 1, 0),
    //COMMON_FUZZY_QUERYDIRECTSUPPLYLIST("/common/fuzzy/queryDirectSupplyList","查询供应商列表",1,0),
    COMMON_MONITORMAPPINGHOTEL_SAVEORUPDATEHOTELMAPPINGLIST("/common/monitorMappingHotel/saveOrUpdateHotelMappingList", "酒店监控列表-更新映射", 1, 3),
    COMMON_MONITORMAPPINGHOTEL_DISCONNECT_HOTELMAPPING("/common/monitorMappingHotel/disConnect/hotelMapping", "酒店监控列表-断开映射", 1, 3),

    //roomMap
    COMMON_HOTEL_SUPPLYROOMMAPPING_QUERYSUPPLYROOMMAPPINGLIST("/common/hotel/supplyRoomMapping/querySupplyRoomMappingList", "查询房型映射列表", 1, 0),
    //COMMON_FUZZY_QUERYDIRECTSUPPLYLIST("/common/fuzzy/queryDirectSupplyList","查询供应商列表",1,0),
    //COMMON_BASEINFOAREADATA_COUNTRYLIST("/common/baseinfoAreadata/countryList","查询国家列表",1,0),
    COMMON_HOTEL_SUPPLYROOM_NOTMAPPING("/common/hotel/supplyRoom/notMapping", "房型映射-不再映射", 1, 3),
    COMMON_HOTEL_SUPPLYROOMMAPPING_DISCONNECTMAPPING("/common/hotel/supplyRoomMapping/disConnectMapping", "房型映射断开映射", 1, 3),
    COMMON_HOTEL_SUPPLYROOM_RECOVERMAPPING("/common/hotel/supplyRoom/recoverMapping", "房型映射恢复映射", 1, 3),
    //COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData","查询城市列表",1,0),
    //COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
    //COMMON_SUPPLY_HOTEL_SUPPLYHOTELPAGE("/common/supply/hotel/supplyHotelPage","查询供应商酒店名",1,0),

    //roomMonitor
    COMMON_MONITORMAPPINGROOM_QUERYMONITORMAPPINGROOMLIST("/common/monitorMappingRoom/queryMonitorMappingRoomList", "查询已映射房型监控列表", 1, 0),
    COMMON_FUZZY_QUERYDIRECTSUPPLYLIST("/common/fuzzy/queryDirectSupplyList", "查询供应商列表", 1, 0),
    COMMON_MONITORMAPPINGROOM_SAVEORUPDATEROOMMAPPINGLIST("/common/monitorMappingRoom/saveOrUpdateRoomMappingList", "房型监控列表-更新映射", 1, 3),//saveOrUpdate
    COMMON_MONITORMAPPINGROOM_DISCONNECTMAPPING("/common/monitorMappingRoom/disConnectMapping", "房型监控列表-断开映射", 1, 3),

    //supplierManage
    COMMON_SUPPLYCONFIG_SUPPLYCONFIGLIST("/common/supplyConfig/supplyConfigList", "查询供应商对接配置列表", 1, 0),
    COMMON_SUPPLYCONFIGTEMPLATE_SUPPLYCONFIGTEMPLATELIST("/common/supplyConfigTemplate/supplyConfigTemplateList", "查询供应商配置模板列表", 1, 0),
    COMMON_SUPPLYINFO_SUPPLYINFOLIST("/common/supplyInfo/supplyInfoList", "查询供应商类型列表", 1, 0),
    COMMON_SUPPLYCONFIG_SUPPLYCONFIGDEL("/common/supplyConfig/supplyConfigDel", "删除供应商对接配置", 1, 2),
    COMMON_SUPPLYCONFIGTEMPLATE_SUPPLYCONFIGTEMPLATEDEL("/common/supplyConfigTemplate/supplyConfigTemplateDel", "删除供应商配置模板", 1, 2),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),

    //addSupplierConfig
    COMMON_SUPPLYCONFIG_SUPPLYCONFIGADD("/common/supplyConfig/supplyConfigAdd", "新增供应商对接配置", 1, 1),
    COMMON_SUPPLYCONFIG_SUPPLYCONFIGEDIT("/common/supplyConfig/supplyConfigEdit", "编辑供应商对接配置", 1, 3),
    COMMON_SUPPLYCONFIG_SUPPLYCONFIGLISTWITHTEMPLATE("/common/supplyConfig/supplyConfigListWithTemplate", "供应商对接配置新增-查询待新增模板", 1, 0),

    //addSupplierTem
    COMMON_SUPPLYCONFIGTEMPLATE_SUPPLYCONFIGTEMPLATEADD("/common/supplyConfigTemplate/supplyConfigTemplateAdd", "新增供应商配置模板", 1, 1),

    //hotelMapLog
    //COMMON_HOTEL_SUPPLYMAPPINGLOG_QUERYSUPPLYMAPPINGLOGPAGE("/common/hotel/supplyMappingLog/querySupplyMappingLogPage","查询酒店映射日志",1,0),

    //hotelPage
    COMMON_BASEINFOHOTEL_QUERYNOTMAPPINGLIST("/common/baseinfoHotel/queryNotMappingList", "未映射酒店列表-点击酒店映射-查询酒店列表", 1, 0),
    COMMON_SUPPLYHOTELMAPPING_SAVEORUPDATEHOTELMAPPINGLIST("/common/supplyHotelMapping/saveOrUpdateHotelMappingList", "新增/更新酒店映射操作", 1, 1),//saveOrUpdate

    //roomMapLog
    COMMON_HOTEL_SUPPLYMAPPINGLOG_QUERYSUPPLYMAPPINGLOGPAGE("/common/hotel/supplyMappingLog/querySupplyMappingLogPage", "查询酒店映射日志", 1, 0),

    //roomPage
    COMMON_BASEINFOROOM_QUERYNOTMAPPINGLIST("/common/baseinfoRoom/queryNotMappingList", "新增映射-查询房型列表", 1, 0),
    COMMON_HOTEL_SUPPLYROOMMAPPING_SAVEORUPDATEROOMMAPPINGLIST("/common/hotel/supplyRoomMapping/saveOrUpdateRoomMappingList", "新增/更新房型映射", 1, 1),

    //stateSelect<---
    COMMON_BASEINFOAREADATA_COUNTRYLIST("/common/baseinfoAreadata/countryList", "查询国家列表", 1, 0),

    //addHotel
    common_supplyHotelMapping_saveOrUpdateHotelMappingList("/common/supplyHotelMapping/saveOrUpdateHotelMappingList", "新增/更新酒店映射操作", 1, 1),

    //order
    ORDER_QUERYORDERLIST("/order/queryOrderList", "查询订单列表", 1, 0),
    ORDER_QUERYORDERSTATISTICS("/order/queryOrderStatistics", "统计订单列表", 1, 0),

    //addOrderPop
    //COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
    //COMMON_FUZZY_QUERYROOM("/common/fuzzy/queryRoom","查询房型",1,0),
    //common_fuzzy_querySupplier("/common/fuzzy/querySupplier","查询供应商名称",1,0),
    ORDER_ADDMANUALORDER("/order/addManualOrder", "新增手工单", 1, 1),

    //orderDetail
    ORDER_QUERYORDERDETAIL("/order/queryOrderDetail", "查询订单详情", 1, 0),
    ORDER_QUERYCONFIRMORDERINFO("/order/queryConfirmOrderInfo", "查询确认订单信息", 1, 0),
    ORDER_MODIFYSALEPRICE("/order/modifySalePrice", "修改售价", 1, 3),
    ORDER_QUERYSUPPLYPRODUCT("/order/querySupplyProduct", "查询供货单产品详情", 1, 0),
    ORDER_QUERYSUPPLYORDERRESULT("/order/querySupplyOrderResult", "查询供货单结果", 1, 0),
    ORDER_QUERYORDERREMARK("/order/queryOrderRemark", "查询订单备注", 1, 0),
    ORDER_QUERYORDERLOG("/order/queryOrderLog", "查询订单日志", 1, 0),
    ORDER_PREVIEWSUPPLYORDER("/order/previewSupplyOrder", "预览供货单", 1, 0),
    COMMON_BASEINFOHOTEL_QUERYHOTELINFO("/common/baseinfoHotel/queryHotelInfo", "查询酒店基本信息", 1, 0),
    ORDER_QUERYMAPPINGCOMPARE("/order/queryMappingCompare", "查询映射对比", 1, 0),
    COMMON_BASEINFOROOM_BASEINFOROOMDETAIL("/common/baseinfoRoom/baseinfoRoomDetail", "查询房型详情", 1, 0),
    ORDER_MODIFYSUPPLYORDERSETTLEMENTTYPE("/order/modifySupplyOrderSettlementType", "修改供货单结算方式", 1, 3),
    ORDER_MODIFYORDERSETTLEMENTTYPE("/order/modifyOrderSettlementType", "修改订单结算方式", 1, 3),
    ORDER_MODIFYORDERROOM("/order/modifyOrderRoom", "修改订单房型", 1, 3),
    ORDER_MODIFYROOM("/order/modifyRoom", "修改订单房型", 1, 3),
    ORDER_MODIFYGUEST("/order/modifyGuest", "修改入住人", 1, 3),
    ORDER_MODIFYSPECIALREQUIREMENT("/order/modifySpecialRequirement", "修改特殊要求", 1, 3),
    ORDER_MODIFYSUPPLIERORDERCODE("/order/modifySupplierOrderCode", "修改供应商订单号", 1, 3),
    ORDER_LOCKORDER("/order/lockOrder", "解锁订单", 1, 15),
    //ORDER_MODIFYCHANNELORDERCODE("/order/modifyChannelOrderCode","修改渠道订单号",1,3),
    ORDER_MODIFYREFUNDFEE("/order/modifyRefundFee", "修改退订费", 1, 3),
    ORDER_MODIFYSUPPLYCOMMISSION("/order/modifySupplyCommission", "修改供货单佣金", 1, 3),
    //    ORDER_QUERYSUPPLIER("/order/querySupplier","查询供应商",1,0),
    COMMON_FUZZY_QUERYROOM("/common/fuzzy/queryRoom", "查询房型", 1, 0),
    ORDER_ADDITIONALCHARGESQUERY("/order/additionalChargesQuery", "查询附加项明细", 1, 0),
    ORDER_ADDITIONALCHARGESEDIT("/order/additionalChargesEdit", "修改订单附加项费用", 1, 3),

    //orderSetting
    ORDER_ORDERAUTOMATICLIST("/order/orderAutomaticList", "查询订单设置-自动发单列表", 1, 0),
    ORDER_QUERYAUTOCONFIRMCHANNEL("/order/queryAutoConfirmChannel", "查询自动确认", 1, 0),
    ORDER_MODIFYAUTOCONFIRMCHANNEL("/order/modifyAutoConfirmChannel", "修改自动确认", 1, 3),
    ORDER_QUERYAUTOCANCEL("/order/queryAutoCancel", "查询自动取消", 1, 0),
    ORDER_MODIFYAUTOCANCEL("/order/modifyAutoCancel", "修改自动取消", 1, 3),
    ORDER_ADDSUPPLIERAUTOCHANNEL("/order/addSupplierAutoChannel", "添加自动发单设置", 1, 1),
//    COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),

    //addCheck
    ORDER_SAVEORUPDATEORDERCHECKINFO("/order/saveOrUpdateOrderCheckInfo", "订单入住明细新增+编辑", 1, 1),//saveOrUpdate

    //addSupplyOrderPop
    PRODUCT_QUERYORDERPRODUCTLIST("/product/queryOrderProductList", "查询订单产品列表", 1, 0),
    //ORDER_ADDPRODUCT("/order/addProduct","添加产品",1,1),
    PRODUCT_SALE_QUERYORDERPRODUCTPRICE("/product/sale/queryOrderProductPrice", "查询订单产品价格", 1, 0),
    ORDER_ADDPRODUCT("/order/addProduct", "添加产品", 1, 1),

    //handleOrderView
    ORDER_HANDLEORDERREQUEST("/order/handleOrderRequest", "处理订单申请", 1, 9),
    ORDER_CANCELORDER("/order/cancelOrder", "取消订单", 1, 10),
    ORDER_CONFIRMORDER("/order/confirmOrder", "确认订单", 1, 6),
    //ORDER_DELETEORDERREQUEST("/order/deleteOrderRequest","",1,-1),
    ORDER_REMOVEORDERREQUEST("/order/removeOrderRequest", "移除订单请求", 1, 2),

    //handleSupplyOrderView
    ORDER_SAVESUPPLYRESULT("/order/saveSupplyResult", "录供货单结果", 1, 1),//save

    //invoiceDivision
    ORDER_QUERYORDERINVOICE("/order/queryOrderInvoice", "查询订单发票", 1, 0),
    ORDER_MODIFYORDERINVOICE("/order/modifyOrderInvoice", "修改订单发票", 1, 3),

    //modifySupplyProductPop
    ORDER_MODIFYSUPPLYPRODUCT("/order/modifySupplyProduct", "修改供货单产品", 1, 3),

    //sendOrderPop
    ORDER_SENDTOSUPPLIER("/order/sendToSupplier", "发单给供货单", 1, 11),

    //agent
    COMMON_AGENT_QUERYAGENTLIST("/common/agent/queryAgentList", "按条件查询客户信息", 1, 0),
    COMMON_AGENT_MODIFYAGENTSTATUS("/common/agent/modifyAgentStatus", "修改客户启用状态", 1, 3),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    //COMMON_FUZZY_QUERYSALEMANAGERLIST("/common/fuzzy/querySaleManagerList","查询销售经理",1,0),

    //agentDetail
    COMMON_AGENT_QUERYAGENTDETAIL("/common/agent/queryAgentDetail", "查询客户详情", 1, 0),
    //SETTING_RESETADMINPWD("/setting/resetAdminPwd","",1,-1),
    USER_RESETADMINPWD("/user/resetAdminPwd", "重置总管理员密码", 0, 12),//?

    //supplier
    COMMON_SUPPLIER_QUERYSUPPLIERLIST("/common/supplier/querySupplierList", "按条件查询供应商信息", 1, 0),
    COMMON_SUPPLIER_MODIFYSUPPLIERSTATUS("/common/supplier/modifySupplierStatus", "修改供应商启用状态", 1, 3),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),
    //COMMON_FUZZY_QUERYPURCHASEMANAGERLIST("/common/fuzzy/queryPurchaseManagerList","查询采购经理",1,0),

    //supplierDetail
    COMMON_SUPPLIER_QUERYSUPPLIERDETAIL("/common/supplier/querySupplierDetail", "查询供应商详情", 1, 0),
    COMMON_USER_RESETADMINPWD("/common/user/resetAdminPwd", "重置总管理员密码", 0, 12),

    //agentPop
    //FINANCE_QUERYEXCHANGERATE("/finance/queryExchangeRate","获取汇率列表",1,0),
    COMMON_AGENT_MODIFYAGENT("/common/agent/modifyAgent", "修改客户api配置", 0, 3),
    COMMON_AGENT_ADDAGENT("/common/agent/addAgent", "新增客户信息", 1, 1),


    //supplierPop
    //FINANCE_QUERYEXCHANGERATE("/finance/queryExchangeRate","获取汇率列表",1,0),
    COMMON_SUPPLIER_MODIFYSUPPLIER("/common/supplier/modifySupplier", "修改供应商信息", 1, 3),
    COMMON_SUPPLIER_ADDSUPPLIER("/common/supplier/addSupplier", "新增供应商", 1, 1),

    //sellingPrice
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    PRODUCT_PROORGINCREASE_PROORGINCREASEPAGE("/product/proOrgIncrease/proOrgIncreasePage", "查询加幅规则", 1, 0),
    PRODUCT_PROORGINCREASE_PROORGINCREASEEDIT("/product/proOrgIncrease/proOrgIncreaseEdit", "修改加幅规则", 1, 3),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),


    //signedProductBS
    PRODUCT_QUERYHOTELLIST("/product/queryHotelList", "查询酒店列表", 1, 0),
    PRODUCT_QUERYHOTELPRODUCTS("/product/queryHotelProducts", "查询酒店产品", 1, 0),
    PRODUCT_QUERYPRODUCT("/product/queryProduct", "查询产品", 1, 0),
    PRODUCT_MODIFYOFFSHELVESTATUS("/product/modifyOffShelveStatus", "停售产品", 1, 3),
    PRODUCT_DELETEPRODUCT("/product/deleteProduct", "删除产品", 1, 2),
    PRODUCT_QUERYPRODUCTRESTRICTINFO("/product/queryProductRestrictInfo", "查询产品条款信息", 1, 0),
    //PRODUCT_BATCHMODIFYROOMSTATUS("/product/batchModifyRoomStatus", "批量调整房态", 1, 3),
    //PRODUCT_BATCHMODIFYBASEPRICE("/product/batchModifyBasePrice", "批量修改底价", 1, 3),
//    COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData","查询城市列表",1,0),
//    COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
//    COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),
    PRODUCT_QUERYPRODUCTLOGLIST("/product/queryProductLogList", "查询产品日志列表", 1, 0),

    //productItemtree
    PRODUCT_SALE_QUERYHOTELLIST("/product/sale/queryHotelList", "查询酒店列表", 1, 0),
    PRODUCT_SALE_BATCHMODIFYSALESTATUS("/product/sale/batchModifySaleStatus", "批量修改销售状态", 1, 3),
    DIS_PUSHSALEROOMLIST("/dis/pushSaleRoomList", "推送售卖房型列表", 1, 13),
    COMMON_FUZZY_QUERYCHANNELCONFIG("/common/fuzzy/queryChannelConfig", "查询渠道配置", 1, 0),
    PRODUCT_SALE_SINGLEPRODUCTMODIFYSALESTATUS("/product/sale/singleProductModifySaleStatus", "修改销售状态", 1, 3),
    //SALECHANNEL_SYNCPRODUCTS("/saleChannel/syncProducts","",1,-1),
    //DIS_PUSHSALEROOM("/dis/pushSaleRoom","",1,-1),
    //SALECHANNEL_RESETHOTELLIST("/saleChannel/resetHotelList","",1,-1),
    PRODUCT_SALE_RESETHOTELLIST("/product/sale/resetHotelList", "刷新酒店列表", 1, 14),
    //COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData","查询城市列表",1,0),
    //COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),
    //PRODUCT_SALE_BATCHMODIFYSALEPRICE("/product/sale/batchModifySalePrice", "批量修改售价", 1, 3),

    //salePriceCalendar
    PRODUCT_SALE_QUERYSALEPRICELIST("/product/sale/querySalePriceList", "查询售价列表", 1, 0),
    SALE_DAILYMODIFYSALEPRICE("/sale/dailyModifySalePrice", "修改单日售价", 1, 3),//?
    PRODUCT_SALE_QUERYPRODUCTSALELOGLIST("/product/sale/queryProductSaleLogList", "查询售价日志列表", 1, 0),
    PRODUCT_SALE_UPDATESALEPRICE("/product/sale/updateSalePrice", "更新售价", 1, 3),

    //auditLog
    COMMON_AUDITLOG_FINDAUDITLOGSEARCHPAGE("/common/auditLog/findAuditLogSearchPage", "查询审计日志", 0, 0),

    //bankCard
    COMMON_BANK_QUERYBANKLIST("/common/bank/queryBankList", "查询银行账号列表", 1, 0),//?
    //COMMON_BANK_MODIFYBANK("/common/bank/modifyBank","编辑银行账号",1,3),
    //COMMON_BANK_ADDBANK("/common/bank/addBank","新增银行账号",1,1),
    //COMMON_BANK_DELETEBANK("/common/bank/deleteBank","删除银行卡信息",1,2),

    //companyInfo
    COMMON_COMPANY_QUERYCOMPANYDETAIL("/common/company/queryCompanyDetail", "查询企业详细信息", 1, 0),

    //employee
    COMMON_USER_QUERYEMPLOYEELIST("/common/user/queryEmployeeList", "按企业成员姓名查询企业成员信息", 0, 0),
    //COMMON_USER_MODIFYEMPLOYEE("/common/user/modifyEmployee","修改密码",0,3),
    //COMMON_USER_ADDEMPLOYEE("/common/user/addEmployee","新增企业员工",0,1),
    //COMMON_USER_QUERYROLEANDDOMAIN("/common/user/queryRoleAndDomain","获取角色列表以及域名",1,0),
    //COMMON_USER_DELETEEMPLOYEE("/common/user/deleteEmployee", "删除企业员工", 0, 2),
    COMMON_USER_UPDATEACCOUNTSTATUS("/common/user/updateAccountStatus", "解锁员工", 0, 15),

    //employeeInfo
    //common_user_queryEmployeeDetail("/common/user/queryEmployeeDetail","查询企业员工详情",1,0),
    //COMMON_USER_MODIFYEMPLOYEEPWD("/common/user/modifyEmployeePwd","修改企业员工密码",0,3),
    //COMMON_AUTH_LOGOUT("/common/auth/logout","退出登录",0,16),
    COMMON_USER_QUERYROLEANDDOMAIN("/common/user/queryRoleAndDomain", "获取角色列表以及域名", 0, 0),
    //COMMON_APPLY_QUERYUSERAPPLYLIST("/common/apply/queryUserApplyList","查看用户信息",1,0),

    //exchangeRate
    FINANCE_QUERYEXCHANGERATE("/finance/queryExchangeRate", "获取汇率列表", 1, 0),
    FINANCE_QUERYEXCHANGERATELOG("/finance/queryExchangeRateLog", "查询操作日志", 1, 0),
    FINANCE_MODIFYEXCHANGERATE("/finance/modifyExchangeRate", "编辑汇率", 1, 3),
    FINANCE_ADDEXCHANGERATE("/finance/addExchangeRate", "新增汇率", 1, 1),

    //roleManage
    COMMON_USER_QUERYROLELIST("/common/user/queryRoleList", "查询角色列表", 0, 0),
    COMMON_USER_ROLEDEL("/common/user/roleDel", "删除角色", 0, 2),

    //statistics
    COMMON_AUDITLOG_FINDAUDITLOGSTATISTICSPAGE("/common/auditLog/findAuditLogStatisticsPage", "查看审计日志统计", 0, 0),

    //system
    COMMON_USER_QUERYSYSTEMSETTING("/common/user/querySystemSetting", "查询系统设置", 0, 0),
    COMMON_USER_CONFIGSYSTEMSETTING("/common/user/configSystemSetting", "保存系统设置", 0, 3),//save

    //userInfo
    COMMON_USER_QUERYSENDVERIFICATIONCODETYPE("/common/user/querySendVerificationCodeType", "获取发送验证码类型", 0, 0),
    COMMON_USER_UPDATESENDVERIFICATIONCODETYPE("/common/user/updateSendVerificationCodeType", "修改发送验证码类型", 0, 3),
    COMMON_USER_QUERYEMPLOYEEDETAIL("/common/user/queryEmployeeDetail", "查询企业员工详情", 0, 0),
    //COMMON_USER_MODIFYEMPLOYEEPWD("/common/user/modifyEmployeePwd", "修改企业员工密码", 0, 3),
    COMMON_AUTH_LOGOUT("/common/auth/logout", "退出登录", 0, 16),

    //userApplyInfo
    COMMON_APPLY_QUERYUSERAPPLYLIST("/common/apply/queryUserApplyList", "查询员工增改申请列表", 0, 0),
    COMMON_APPLY_USERAPPLY("/common/apply/userApply", "员工增改审核", 0, 3),
    COMMON_APPLY_QUERYUSERAPPLYDETAIL("/common/apply/queryUserApplyDetail", "查询员工增改申请详情", 0, 0),

    //addEditRole
    COMMON_USER_QUERYMENULIST("/common/user/queryMenuList", "查询所有菜单信息", 1, 0),
    COMMON_USER_QUERYROLEMENULIST("/common/user/queryRoleMenuList", "查询角色菜单信息", 1, 0),
    COMMON_USER_ROLEEDIT("/common/user/roleEdit", "角色编辑-新增加修改", 0, 1),//saveOrUpdate

    //employeeInfoModel
    COMMON_USER_MODIFYEMPLOYEE("/common/user/modifyEmployee", "修改用户信息", 0, 3),
    COMMON_USER_ADDEMPLOYEE("/common/user/addEmployee", "新增企业员工", 0, 1),
    //    COMMON_USER_DELETEEMPLOYEE("/common/user/deleteEmployee", "删除企业员工", 0, 2),
    COMMON_USER_EXAMINEPWD("/common/user/examinePwd", "校验密码", 0, 25),
    COMMON_USER_EXAMINEPHONEORIDCARD("/common/user/examinePhoneOrIdCard", "校验手机号或身份证", 0, 25),
    COMMON_USER_EXAMINEUSERACCOUNT("/common/user/examineUserAccount", "校验账号", 0, 25),

    //operationStatistic
    COMMON_STATISTICS_QUERYBUSINESSSTATISTICS("/common/statistics/queryBusinessStatistics", "查询经营概况", 1, 0),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    //COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData","查询区域数据",1,0),

    //profitStatistic
    COMMON_STATISTICS_QUERYORDERPROFITSTATISTICS("/common/statistics/queryOrderProfitStatistics", "查询订单利润表", 1, 0),
    COMMON_STATISTICS_QUERYORDERSTATISTICS("/common/statistics/queryOrderStatistics", "查询订单利润表统计", 1, 0),
    COMMON_STATISTICS_EXPORTORDERPROFITSTATISTICS("/common/statistics/exportOrderProfitStatistics", "导出订单利润表", 1, 4),
    //COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
    //COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),

    //saleStatistic
    COMMON_STATISTICS_QUERYSALESDETAILSTATISTICS("/common/statistics/querySalesDetailStatistics", "查询销售明细表", 1, 0),
    COMMON_STATISTICS_QUERYSALESSTATISTICS("/common/statistics/querySalesStatistics", "查询销售明细统计", 1, 0),
    COMMON_STATISTICS_("/common/statistics/exportSalesStatistics", "导出销售明细", 1, 4),
    //    COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
    COMMON_FUZZY_QUERYAGENT("/common/fuzzy/queryAgent", "模糊查询分销商", 1, 0),
    COMMON_FUZZY_QUERYSALEMANAGERLIST("/common/fuzzy/querySaleManagerList", "查询销售经理", 1, 0),
    //COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier","查询供应商名称",1,0),
    COMMON_FUZZY_QUERYPURCHASEMANAGERLIST("/common/fuzzy/queryPurchaseManagerList", "查询采购经理", 1, 0),


    //loginPanel<---
    PRODUCT_BATCHMODIFYROOMSTATUS("/product/batchModifyRoomStatus", "批量调整房态", 1, 3),
    PRODUCT_BATCHMODIFYBASEPRICE("/product/batchModifyBasePrice", "批量修改底价", 1, 3),
    //SALECHANNEL_BATCHMODIFYSALEPRICE("/saleChannel/batchModifySalePrice","",1,-1),
    PRODUCT_SALE_BATCHMODIFYSALEPRICE("/product/sale/batchModifySalePrice", "批量修改售价", 1, 3),

    //taskDetail<---
    //ADVANCEDSETTING_CANCELTASK("/advancedSetting/cancelTask","",1,-1),
    //HOTEL_CANCELTASK("/hotel/cancelTask","",1,-1),

    //taskList<---
    //HOTEL_ADDTASK("/hotel/addTask","",1,-1),
    //HOTEL_CREATEOPTHOTELTASK("/hotel/createOptHotelTask","",1,-1),

    //menuPanel<---
    HOME_MENUADDANDEDIT("/home/<USER>", "添加和编辑菜单", 1, 1),

    //editHotel
    COMMON_BASEINFOHOTEL_ADDHOTELINFO("/common/baseinfoHotel/addHotelInfo", "新增酒店", 1, 1),

    //addAgent
    COMMON_PRODUCT_PROORGAVAILABLE_PROORGAVAILABLE("/common/product/proOrgAvailable/proOrgAvailable", "新增客户维度可见性", 1, 1),

    //addSupplierType
    COMMON_SUPPLYINFO_SUPPLYINFOADD("/common/supplyInfo/supplyInfoAdd", "新增供应商类型", 1, 1),

    //editorCity
    COMMON_SUPPLYAREAMAPPING_SAVEORUPDATESUPPLYAREAMAPPING("/common/supplyAreaMapping/saveOrUpdateSupplyAreaMapping", "新增或者修改供应商城市映射", 1, 1),

    //addRemarkPop
    ORDER_ADDREMARK("/order/addRemark", "新增备注", 1, 1),

    //channel<--
    ORDER_MODIFYCHANNELORDERCODE("/order/modifyChannelOrderCode", "修改渠道订单号", 1, 3),

    //bankTable
    COMMON_BANK_MODIFYBANK("/common/bank/modifyBank", "编辑银行账号", 1, 3),
    COMMON_BANK_ADDBANK("/common/bank/addBank", "新增银行账号", 1, 1),
    COMMON_BANK_DELETEBANK("/common/bank/deleteBank", "删除银行卡信息", 1, 2),

    //callTable
    COMMON_AGENT_MODIFYAGENTAPIINVOKECONFIG("/common/agent/modifyAgentApiInvokeConfig", "修改客户api接口频次信息", 0, 3),

    //contactTable
    COMMON_CONTACT_MODIFYCONTACT("/common/contact/modifyContact", "修改联系人信息", 1, 3),
    COMMON_CONTACT_ADDCONTACT("/common/contact/addContact", "新增联系人", 1, 1),
    COMMON_CONTACT_DELETECONTACT("/common/contact/deleteContact", "删除联系人信息", 1, 2),

    //copDivision
    COMMON_AGENT_MODIFYAGENTAPICONFIG("/common/agent/modifyAgentApiConfig", "修改客户api配置", 1, 3),

    //idleHotel<---
    //product/batchSetIdleStatus("/product/batchSetIdleStatus","",1,-1),
    PRODUCT_BATCHMOVEHOTEL("/product/batchMoveHotel", "批量移入仓库", 1, 17),
    //PRODUCT_SETIDLESTATUS("/product/setIdleStatus","",1,-1),
    PRODUCT_MOVEHOTEL("/product/moveHotel", "移入仓库", 1, 17),

    //adjustPop
    //PRODUCT_ADJUSTROOMSTATUS("/product/adjustRoomStatus","",1,-1),
    PRODUCT_DAILYMODIFYPRODUCTINFO("/product/dailyModifyProductInfo", "单日调整价格，房态，配额", 1, 3),

    //batchIncrementPanel<---
    //PRODUCT_BATCHMODIFYSUPPLIERINCREMENT("/product/batchModifySupplierIncrement","",1,-1),
    PRODUCT_NOCACHED_BATCHMODIFYINCREASE("/product/noCached/batchModifyIncrease", "批量加幅设置", 1, 3),

    //incrementCalendar<---
    //PRODUCT_SINGLEMODIFYINCREASE("/product/singleModifyIncrease","",1,-1),
    PRODUCT_NOCACHED_SINGLEMODIFYINCREASE("/product/noCached/singleModifyIncrease", "单个酒店加幅", 1, 3),

    //productPop
    PRODUCT_MODIFYPRODUCT("/product/modifyProduct", "修改产品", 1, 3),
    PRODUCT_ADDPRODUCT("/product/addProduct", "新增产品", 1, 1),

    //supplierSettingPanel<---
//    PRODUCT_MODIFYSUPPLIERCONFIG("/product/modifySupplierConfig","",1,-1),
    SUPPLIER_NOCACHED_MODIFYSUPPLIERCONFIG("/supplier/noCached/modifySupplierConfig", "修改供应商设置", 1, 3),//?

    //auditLog
    COMMON_AUDITLOG_FINDAUDITLOGSEARCH("/common/auditLog/findAuditLogSearch", "查看审计日志", 0, 0),
    COMMON_AUDITLOG_EXPORTAUDITLOG("/common/auditLog/exportAuditLog", "导出审计日志", 0, 4),

    //statistics
    COMMON_AUDITLOG_FINDAUDITLOGSTATISTICS("/common/auditLog/findAuditLogStatistics", "查看审计统计", 0, 0),

    //auditSettings
    COMMON_AUDITLOG_SAVEAUDITSETTING("/common/auditLog/saveAuditSetting", "保存审计设置", 0, 3),
    COMMON_AUDITLOG_QUERYAUDITSETTING("/common/auditLog/queryAuditSetting", "查询审计设置", 0, 0),
    //COMMON_USER_QUERYROLEANDDOMAIN("/common/user/queryRoleAndDomain","获取角色列表以及域名",1,0),

    //productScreening
    PRODUCT_PROPRODUCTFILTERSTRATEGY_PROPRODUCTFILTERSTRATEGYADD("/product/proProductFilterStrategy/proProductFilterStrategyAdd", "新增产品筛选策略", 1, 1),
    PRODUCT_PROPRODUCTFILTERSTRATEGY_PROPRODUCTFILTERSTRATEGYEDIT("/product/proProductFilterStrategy/proProductFilterStrategyEdit", "编辑产品筛选策略", 1, 3),
    PRODUCT_PROPRODUCTFILTERSTRATEGY_PROPRODUCTFILTERSTRATEGYDEL("/product/proProductFilterStrategy/proProductFilterStrategyDel", "删除产品筛选策略", 1, 2),
    PRODUCT_PROPRODUCTFILTERSTRATEGY_PROPRODUCTFILTERSTRATEGYPAGE("/product/proProductFilterStrategy/proProductFilterStrategyPage", "分页查询产品策略", 1, 0),
    COMMON_HOTEL_BASEINFOHOTELBRAND_GROUPLIST("/common/hotel/baseinfoHotelBrand/groupList", "模糊查询酒店集团列表", 1, 0),
    //    COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel","查询酒店",1,0),
    PRODUCT_PROPRODUCTFILTERSTRATEGY_DRAINAGESETTING("/product/proProductFilterStrategy/drainageSetting", "设置引流", 1, 3),
    PRODUCT_PROPRODUCTFILTERSTRATEGY_DRAINAGESETTINGQUERY("/product/proProductFilterStrategy/drainageSettingQuery", "查询引流设置", 1, 0),

    //orderBook
    ORDER_QUERYHOTELPRODUCTS("/order/queryHotelProducts", "查询酒店产品", 1, 0),

    //mappingSituation
    COMMON_CONTRACTHOTEL_CONTRACTHOTELIMPORTPAGE("/common/contractHotel/contractHotelImportPage", "查询协议导入分页", 1, 0),
    COMMON_CONTRACTHOTEL_CONTRACTHOTELRESULTPAGE("/common/contractHotel/contractHotelResultPage", "查询协议产品分页", 1, 0),
    COMMON_CONTRACTHOTEL_CONTRACTHOTEINFOPAGE("/common/contractHotel/contractHotelInfoPage", "查询协议酒店分页", 1, 0),

    //fillOrderPage

    //exportStatistic
    REPORT_AUDITLOG_EXPORTAUDITLOG("/report/auditLog/exportAuditLog", "导出审计日志", 0, 4),
    REPORT_CONTRACTHOTEL_CONTRACTHOTELIMPORTTEMPLATE("/report/contractHotel/contractHotelImportTemplate", "导出导入协议酒店模板", 1, 4),
    REPORT_CONTRACTHOTEL_CONTRACTHOTELEXPORT("/report/contractHotel/contractHotelExport", "导出协议酒店详情", 1, 4),
    REPORT_CONTRACTHOTEL_CONTRACTHOTELIMPORT("/report/contractHotel/contractHotelImport", "导入协议酒店", 1, 26),
    REPORT_STATISTICS_EXPORTORDERPROFITSTATISTICS("/report/statistics/exportOrderProfitStatistics", "导出订单利润表", 1, 4),
    REPORT_STATISTICS_EXPORTSALESSTATISTICS("/report/statistics/exportSalesStatistics", "导出销售明细", 1, 4),
    REPORT_STATISTICS_QUERYORDERPROFITSTATISTICS("/report/statistics/queryOrderProfitStatistics", "查询订单利润表", 1, 0),
    REPORT_STATISTICS_QUERYSALESDETAILSTATISTICS("/report/statistics/querySalesDetailStatistics", "查询销售明细表", 1, 0),
    REPORT_STATISTICS_QUERYSALESSTATISTICS("/report/statistics/querySalesStatistics", "查询销售明细统计", 1, 0),
    REPORT_STATISTICS_QUERYORDERSTATISTICS("/report/statistics/queryOrderStatistics", "查询订单利润表统计", 1, 0),
    REPORT_SUPPLIERIMPORTSTATEMENT_SUPPLIERSTATEMENTIMPORT("/report/supplierImportStatement/supplierStatementImport", "导入供应商的账单", 1, 26),
    REPORT_SUPPLIERIMPORTSTATEMENT_SUPPLIERIMPORTANDRECONCILIATION("/report/supplierImportStatement/supplierImportAndReconciliation", "导入供应商的账单和对比账单", 1, 26),
    REPORT_SUPPLYAUTORECONCILIATION_RECONCILIATIONRESULTEXPORT("/report/supplyAutoReconciliation/reconciliationResultExport", "导出比对结果", 1, 4),
    REPORT_EXPORTREPORT_EXPORTREPORTPAGE("/report/exportReport/exportReportPage", "分页查询导出报表列表", 1, 0),
    REPORT_EXPORTREPORT_EXPORTREPORTDEL("/report/exportReport/exportReportDel", "删除导出报表", 1, 2),
    REPORT_EXPORTREPORT_DOWNLOADREPORT("/report/exportReport/downloadReport", "下载报表", 1, 27),

    //other
    COMMON_OTHERAPI_ADDROLEANDMENU("/common/otherApi/addRoleAndMenu", "添加角色以及对应的菜单", 1, 1),
    COMMON_OTHERAPI_ADDMENUPERMISSIONS("/common/otherApi/addMenuPermissions", "添加菜单权限", 1, 1),
    COMMON_OTHERAPI_QUERYLOGSETTING("/common/otherApi/queryLogSetting", "查询日志设置", 1, 0),
    COMMON_OTHERAPI_EDITLOGSETTING("/common/otherApi/editLogSetting", "编辑日志设置", 1, 3),
    COMMON_OTHERAPI_FORCEUPDATEPRODUCTFILTERSTRATEGYCACHE("/common/otherApi/forceUpdateProductFilterStrategyCache", "强制更新产品筛选策略缓存", 1, 14),


    //公共部分
    DISCOMMON_INIT_INITEXCHANGERATE("/discommon/init/initExchangeRate", "初始化汇率", 1, 23),
    DISCOMMON_INIT_INITSALESTATUS("/discommon/init/initSaleStatus", "初始化销售状态", 1, 23),
    COMMON_STATESECRET_GETSECRETKEY("/common/stateSecret/getSecretKey", "获取密钥", 0, 21),
    DISCOMMON_INIT_INITRESTRICT("/discommon/init/initRestrict", "初始化条款", 1, 23),
    DISCOMMON_INIT_INITPRODUCTINFO("/discommon/init/initProductInfo", "初始化产品信息", 1, 23),
    DISCOMMON_INIT_INITBASEPRICEANDROOMSTATUS("/discommon/init/initBasePriceAndRoomStatus", "初始化基础价格和房型状态", 1, 23),
    ERROR("/error", "请求错误", 0, 22),
    ORDER_QUERYSUPPLIER("/order/querySupplier", "查询供应商", 1, 0),
    FINANCE_SUPPLIER_NOTIFYPAYMENTOFSTATEMENT("/finance/supplier/notifyPaymentOfStatement", "通知付款", 1, 24),
    COMMON_SEQUENCE_CREATECODE("/common/sequence/createCode", "创建序列号", 1, 1),
    COMMON_FUZZY_QUERYSUPPLIER("/common/fuzzy/querySupplier", "供应商模糊查询", 1, 0),
    COMMON_AREADATA_QUERYAREADATA("/common/areaData/queryAreaData", "查询区域数据", 1, 0),
    DISCOMMON_MAPPING_QUERYPRODUCTMAPPING("/discommon/mapping/queryProductMapping", "查询产品映射", 1, 0),
    PRODUCT_PROORGAVAILABLE_PROSUPPIERAVAILABLEPAGE("/product/proOrgAvailable/proSuppierAvailablePage", "查询供应商维度可见性列表", 1, 0),
    COMMON_STATESECRET_REMOVESECRETKEY("/common/stateSecret/removeSecretKey", "移除密钥", 0, 2),
    COMMON_AUDITLOG_AUDITLOGADD("/common/auditLog/auditLogAdd", "添加审计日志", 0, 1),//?
    COMMON_STATESECRET_VERIFYSECRETKEY("/common/stateSecret/verifySecretKey", "验证密钥", 0, 0),
    COMMON_FUZZY_QUERYHOTEL("/common/fuzzy/queryHotel", "联想查询酒店名称", 1, 0),


    //unauthorized operations
    UNAUTHORIZED_OPERATIONS("unauthorizedOperations", "越权操作", 0, 18);


    //请求Url
    public final String requestUrl;
    //日志名称(存入数据库时，需要routingName+logName，如：工作台查看财务)
    public final String logName;
    //日志级别 0：系统级事件 1：业务级事件
    public final Integer logLevel;

    /**
     * 操作类型
     * 0:查询
     * 1:增加
     * 2:删除
     * 3:修改
     * 4:导出
     * 5:加锁
     * 6:确认
     * 7:发短信
     * 8:登录
     * 9:处理
     * 10:取消
     * 11:发单
     * 12:重置
     * 13:推送
     * 14:刷新
     * 15:解锁
     * 16:退出登录
     * 17:移动
     * 18:越权操作
     * 19:ip异常操作
     * 20:连续登录失败
     * 21:获取
     * 22:请求错误
     * 23:初始化
     * 24:通知
     * 25:校验
     * 26:导入
     * 27:下载
     */
    public final Integer operationType;

    public static RequestUrlEnums getRequestUrlEnumByRequestUrl(String requestUrl) {
        RequestUrlEnums requestUrlEnums = null;
        for (RequestUrlEnums requestUrlEnum : RequestUrlEnums.values()) {
            if (requestUrl.endsWith(requestUrlEnum.getRequestUrl())) {
                requestUrlEnums = requestUrlEnum;
                break;
            }
        }
        return requestUrlEnums;
    }

    public static RequestUrlEnums getRequestUrlEnumByOperationType(Integer operationType) {
        RequestUrlEnums requestUrlEnums = null;
        for (RequestUrlEnums requestUrlEnum : RequestUrlEnums.values()) {
            if (Objects.equals(operationType, requestUrlEnum.operationType)) {
                requestUrlEnums = requestUrlEnum;
                break;
            }
        }
        return requestUrlEnums;
    }


}

