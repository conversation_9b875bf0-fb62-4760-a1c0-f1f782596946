package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum LanguageTypeEnum {

    zh_CN("zh_CN", "zh-C<PERSON>", "简体中文"),
    en_US("en_US", "en-US", "英语");

    private final String code;
    private final String value;
    private final String msg;

    public static String getValueByCode(String code) {
        String value = null;
        for (LanguageTypeEnum languageEnum : LanguageTypeEnum.values()) {
            if (Objects.equals(languageEnum.code, code)) {
                value = languageEnum.value;
                break;
            }
        }
        return value;
    }

    public static String getCodeByValue(String value) {
        String code = null;
        for (LanguageTypeEnum languageEnum : LanguageTypeEnum.values()) {
            if (languageEnum.value.equals(value)) {
                code = languageEnum.code;
                break;
            }
        }
        return code;
    }

    public static LanguageTypeEnum getEnumByCode(String code) {
        LanguageTypeEnum languageEnum = null;
        for (LanguageTypeEnum language : LanguageTypeEnum.values()) {
            if (Objects.equals(language.code, code)) {
                languageEnum = language;
                break;
            }
        }
        return languageEnum;
    }

}
