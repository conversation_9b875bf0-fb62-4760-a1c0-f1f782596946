package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发票开票状态
 */
@Getter
@AllArgsConstructor
public enum InvoiceStatusEnum {

    WILL_INVOICE(0, "待开票"),
    INVOICED(1, "已开票"),
    INVOICING(2, "开票中"),
    INVOICE_SUCCESS_WAIT_DOWNLOAD(3, "开票成功待下载"),
    INVOICE_FAILED(4, "开票失败"),
    DOWNLOAD_FAILED(5, "下载附件失败"),
    DOWNLOAD_SUCCESS_WAIT_CREATE_BILL(6, "下载成功待创建票单"),
    CREATE_BILL_FAILED(7, "创建票单失败"),
    INVOICE_BINDING_FAILED(8, "发票绑定失败");

    public final int no;
    public final String desc;

    public static String getDesc(int no) {
        for (InvoiceStatusEnum invoiceStatusEnum : InvoiceStatusEnum.values()) {
            if (invoiceStatusEnum.no == no) {
                return invoiceStatusEnum.desc;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param no 状态码
     * @return 枚举实例
     */
    public static InvoiceStatusEnum getByNo(int no) {
        for (InvoiceStatusEnum invoiceStatusEnum : InvoiceStatusEnum.values()) {
            if (invoiceStatusEnum.no == no) {
                return invoiceStatusEnum;
            }
        }
        return null;
    }
}
