package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RoomStateEnum {

    HAVA_ROOM(1, "有房"),
    FULL_ROOM(0, "满房"),
    CONSULT(2, "待查"),
    FREE_SALE(4, "Free Sale");

    public final int key;
    public final String value;

    public static int getKeyByValue(String value) {
        int key = 0;
        for (RoomStateEnum roomStateEnum : RoomStateEnum.values()) {
            if (roomStateEnum.value.equals(value)) {
                key = roomStateEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (RoomStateEnum roomStateEnum : RoomStateEnum.values()) {
            if (roomStateEnum.key == key) {
                value = roomStateEnum.value;
                break;
            }
        }
        return value;
    }

    public static RoomStateEnum getEnumByKey(int key) {
        RoomStateEnum roomStateEnum = null;
        for (RoomStateEnum roomState : RoomStateEnum.values()) {
            if (roomState.key == key) {
                roomStateEnum = roomState;
                break;
            }
        }
        return roomStateEnum;
    }

}
