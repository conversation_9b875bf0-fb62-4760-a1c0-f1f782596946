package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 条款类型
 */
@Getter
@AllArgsConstructor
public enum CancelRestrictionTypeEnum {

    NOT_RESTRICTION(0, "未设置取消条款"),
    NO_CANCEL(1, "表示不可取消，不可更改"),
    PARTIAL_CANCEL(2, "表示入住前day天time点之后取消，取消需支付违约金"),
    FULL_CANCEL_LATE(3, "表示入住前day天time点之后不可取消，不可更改"),
    FREE_CANCELLATION(4, "可免费取消"),
    ;

    private final int code;
    private final String desc;

    public static CancelRestrictionTypeEnum getEnumByKey(int key) {
        CancelRestrictionTypeEnum cancelRestrictionTypeEnum = null;
        for (CancelRestrictionTypeEnum restrictionTypeEnum : CancelRestrictionTypeEnum.values()) {
            if (restrictionTypeEnum.code == key) {
                cancelRestrictionTypeEnum = restrictionTypeEnum;
                break;
            }
        }
        return cancelRestrictionTypeEnum;
    }
}
