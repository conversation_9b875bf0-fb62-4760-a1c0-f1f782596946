package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum CheckInstateEnum {

    LEFT("0", "Left", "正常离店"),
    NOSHOW("1", "NoShow", "NoShow"),
    UNASCERTAINED("2", "Unascertained", "未确定"),
    STAYING("3", "Staying", "在住"),
    EARLY("4", "Early", "提前离店");

    public final String name;
    public final String value;
    public final String desc;

    public static String getValueByName(String name) {
        return Arrays.stream(CheckInstateEnum.values())
                .filter(e -> Objects.equals(e.getName(), name))
                .map(CheckInstateEnum::getValue)
                .findFirst()
                .orElse(null);
    }

    public static String getDescByName(String name) {
        return Arrays.stream(CheckInstateEnum.values())
                .filter(e -> Objects.equals(e.getName(), name))
                .map(CheckInstateEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

}
