package com.tiangong.enums;

public interface ReportConstants {

    int BATCH_SIZE = 100000;// 每次存入csv的数据

    /**
     * 酒店基础信息
     */
    String[] BASE_HOTEL_TABLE_HEADER = {"酒店ID", "酒店名称", "城市编码", "城市名称", "来源供应商类型", "来源供应商酒店ID", "创建时间", "创建人", "更新时间", "更新人"};
    String BASE_HOTEL_FILE_NAME = "酒店基础信息";

    /**
     * 酒店映射信息
     */
    String[] HOTEL_MAPPING_TABLE_HEADER = {"我方酒店ID", "我方酒店名称", "供应商类型编码", "供应商酒店ID", "供应商酒店名称", "映射状态", "创建时间", "创建人", "更新时间", "更新人"};
    String HOTEL_MAPPING_FILE_NAME = "酒店映射信息";

    /**
     * 房型映射映射信息
     */
    String[] ROOM_MAPPING_TABLE_HEADER = {"我方酒店ID", "我方房型ID", "我方房型名称", "供应商类型编码", "供应商酒店ID", "供应商房型ID", "供应商房型名称", "映射状态", "创建时间", "创建人", "更新时间", "更新人"};
    String ROOM_MAPPING_FILE_NAME = "房型映射信息";

    /**
     * 导出销售利润表信息
     */
    String[] SALES_STATISTICS_TABLE_HEADER = {"订单号", "订单来源", "支付类型", "酒店ID", "酒店名称", "国家/省份/城市", "房型", "产品", "是否协议订单", "下单日期", "入住日期", "入住时间", "离店日期", "离店时间",
            "间数", "间夜数", "入住人", "订单确认状态", "订单结算方式", "创建人", "归属人", "客户渠道", "客户名称", "客户编码", "客户单号", "销售币种", "订单应收", "订单已收金额",
            "订单未收金额", "订单应收(CNY)", "供货单应付总额(CNY)", "利润(CNY)", "供货单号", "房型名称(供)", "产品(供)", "入住日期(供)", "离店日期(供)", "入住时间(供)", "离店时间(供)",
            "间数(供)", "间夜数(供)", "入住人(供)", "供货单确认状态", "供货单结算方式", "供应商名称", "供应商编码", "供应商通道一级分类", "供应商通道二级分类", "供应商通道三级分类", "供应商单号", "酒店确认号", "采购币种", "供货单应付", "供货单已付金额",
            "供货单未付金额", "供货单应付(CNY)", "供应商奖励", "供应商奖励(CNY)", "供应商返佣", "供应商返佣(CNY)"};

    /**
     * 导出销售利润表信息(英文)
     */
    String[] SALES_STATISTICS_TABLE_HEADER_EN_US = {"Order Code", "Order Source", "Payment Type", "Hotel ID", "Hotel Name","Country/Province/City", "Room Type", "Product", "Agreed Order", "Order Date", "check-in date", "check-in time", "check-out date", "check-out time",
            "Number of rooms", "Number of nights", "Occupant", "Order confirmation status", "Order settlement method", "Creator", "Owner", "Customer channel", "Customer name", "Customer code", "Customer tracking number", "Sales currency", "Order receivable", "Order received amount",
            "Order outstanding amount", "Order receivable (CNY)", "Supplier payable total amount (CNY)", "Profit (CNY)", "Supply order code", "Room type name (supply)", "Product (supply)", "Occupation date (supply)", "Departure date (supply)", "Occupation time (supply)", "Departure time (supply)",
            "Number of rooms (supply)", "Number of nights (supply)", "Occupant (supply)", "Confirmation status of supply order", "Settlement method of supply order", "Supplier name", "Supplier code", "￼First-level classification of supplier channels", "Secondary classification of supplier channels",
            "Three-level classification of supplier channels", "Supplier order number", "Hotel confirmation number", "Purchase currency", "Payable amount of supply order", "Paid amount of supply order", "Unpaid Amount on Supply Order", "Payable on Supply Order (CNY)", "Supplier Rewards", "Supplier Rewards (CNY)", "Supplier Rebates", "Supplier Rebates (CNY)"};
    String SALES_STATISTICS_FILE_NAME = "销售明细表";
    String SALES_STATISTICS_FILE_NAME_EN_US = "Sales Income Statement";

    /**
     * 酒店房型基础信息
     */
    String[] BASE_ROOM_TABLE_HEADER = {"酒店ID", "酒店名称", "房型ID", "房型名称", "最大入住人数", "是否有窗", "面积"};

    String BASE_ROOM_FILE_NAME = "酒店房型基础信息";

    /**
     * 导出利润统计表信息
     */
    String[] EXPORT_PROFIT_STATISTICS_TABLE_HEADER = {"订单号", "酒店名称", "房型名称", "是否协议订单", "下单时间",
            "入住日期", "离店日期", "入住时间", "离店时间", "间数", "间夜数", "提前预订天数", "订单确认状态", "订单应收",
            "订单应收(CNY)", "供货单应付总额(CNY)", "供应商奖励总额(CNY)", "供应商返佣总额(CNY)", "供应商应付总额(CNY)", "利润(CNY)",
            "归属人", "客户名称", "省份名称", "城市名称", "酒店Id", "酒店星级", "集团名称", "品牌名称"};

    /**
     * 导出利润统计表信息(英文)
     */
    String[] EXPORT_PROFIT_STATISTICS_TABLE_HEADER_EN_US = {"Order Code", "Hotel Name", "Room Type Name", "Agreed Order", "Order Time",
            "Check-in date", "check-out date", "check-in time", "check-out time", "number of rooms", "number of room nights", "advance booking days", "order confirmation status", "order receivable",
            "Order Receivable (CNY)", "Supply Order Payable Total (CNY)", "Supplier Reward Total (CNY)", "Supplier Rebate Total (CNY)", "Supplier Payable Total (CNY)", "Profit (CNY)",
            "Owner", "Customer Name", "Province Name", "City Name", "Hotel ID", "Hotel Star Rating", "Group Name", "Brand Name"};

    String EXPORT_PROFIT_STATISTICS_FILE_NAME = "利润统计表";
    String EXPORT_PROFIT_STATISTICS_FILE_NAME_EN_US = "Profit Statistics Table";

    /**
     * 进项发票信息
     */
    String[] INPUT_INVOICE_TABLE_HEADER = {"发票类型", "发票号码", "开票日期", "购买方名称", "销售方名称", "发票状态", "发票金额", "可使用金额", "税率", "备注", "创建人", "创建时间"};
    /**
     * 销项发票信息
     */
    String[] SALES_INVOICE_TABLE_HEADER = {"发票来源","发票类型", "发票号码", "开票日期", "购买方名称", "销售方名称", "发票状态", "发票金额", "可使用金额", "税率", "备注", "创建人", "创建时间"};

    /**
     * 进项发票信息(英文)
     */
    String[] INPUT_INVOICE_TABLE_HEADER_EN_US = {"Invoice Type", "Invoice Number", "Invoice Date", "Buyer Name", "Seller Name", "Invoice Status", "Invoice Amount", "Available Amount", "Tax Rate", "Remarks", "Creator", "Creation Time"};
    /**
     * 销项发票信息(英文)
     */
    String[] SALES_INVOICE_TABLE_HEADER_EN_US = {"Source Type","Invoice Type", "Invoice Number", "Invoice Date", "Buyer Name", "Seller Name", "Invoice Status", "Invoice Amount", "Available Amount", "Tax Rate", "Remarks", "Creator", "Creation Time"};

    /**
     * 进项发票票单明细信息
     */
    String[] INPUT_INVOICE_DETAIL_TABLE_HEADER = {"供货单号", "下单日期", "酒店", "入住日期", "离店日期", "入住人", "间夜数", "本单要开票金额"};

    /**
     * 进项发票票单明细信息(英文)
     */
    String[] INPUT_INVOICE_DETAIL_TABLE_HEADER_EN_US = {"Supply Order Number", "Order Date", "Hotel", "Check In Date", "Departure Date", "Check In Person", "Number of Nights", "Amount to be invoiced for this order"};

    /**
     * 销项发票票单明细信息
     */
    String[] OUTPUT_INVOICE_DETAIL_TABLE_HEADER = {"订单号", "下单日期", "酒店", "入住日期", "离店日期", "入住人", "间夜数","应开票金额","已开票金额","未开票金额", "本单要开票金额"};

    /**
     * 销项发票票单明细信息(英文)
     */
    String[] OUTPUT_INVOICE_DETAIL_TABLE_HEADER_EN_US = {"Order number", "Order date", "Hotel", "Check in date", "Departure date", "Check in person", "Room nights","Amount need to be invoiced","Invoiced amount","Amount not invoiced", "Amount to be invoiced for this order"};

    /**
     * 导出在线退款任务信息
     */
    String[] EXPORT_REFUND_TASK_TABLE_HEADER = {"退款任务单号", "任务类型", "退款任务状态", "通知退款金额", "退款类型", "订单号", "客户单号", "创建任务时间", "创建人"};

    /**
     * 导出在线退款任务信息(英文)
     */
    String[] EXPORT_REFUND_TASK_TABLE_HEADER_EN_US = {"Refund Task Number", "Task Type", "Refund Task Status", "Notification Refund Amount", "Refund Type", "Order Number", "Customer Order Number", "Task Creation Time", "Creator"};

    String EXPORT_REFUND_TASK_FILE_NAME = "在线退款通知";
    String EXPORT_REFUND_TASK_FILE_NAME_EN_US = "Online refund notice";


    /**
     * 客户账单表头
     */
    String[] INPUT_CUSTOMER_BILL_TABLE_HEADER = {"订单号", "是否协议订单","下单日期", "客户单号", "酒店", "城市", "房型", "入住日期", "离店日期", "入住人", "间数", "夜数", "客户币种","客户币种应收", "客户币种已收", "客户币种未收", "客户币种本次账单要收","商家币种","商家币种应收", "商家币种已收", "商家币种未收", "商家币种本次账单要收","汇率", "刷卡币种", "刷卡金额", "刷卡汇率", "刷卡时间", "结算币种","结算金额", "收款汇率", "成本收款币种", "成本收款金额", "汇差", "确认状态"};

    /**
     * 客户账单表头(英文)
     */
    String[] INPUT_CUSTOMER_BILL_TABLE_HEADER_EN_US = {"Order Number","Agreed Order" ,"Order Created", "Client Order Number", "Hotel", "City", "Room Type", "Checkin Date", "Checkout Date", "Guest", "Rooms", "Nights", "Client Currency", "Customer Currency Amount need to be charged", "Customer Currency Bill charged", " Customer Currency Bill uncharged", "Customer Currency Amount need to be charged in this bill", "Merchant currency", "Merchant currency  Amount need to be charged", "Merchant currency  Bill charged", "Merchant currency  Bill uncharged", "Merchant currency  Amount need to be charged in this bill", "Exchange Rate","Currency used for card swiping","Swipe amount","Card exchange rate","Swiping time","Settlement currency","Settlement amount","Receipt exchange rate","Cost collection currency","Cost receiving amount","Foreign exchange differential","Order Status"};

    /**
     * 客户账单表头 结算成本
     */
    String[] INPUT_CUSTOMER_BILL_SETTLEMENT_COST_TABLE_HEADER = {"订单号", "是否协议订单","下单日期", "客户单号", "酒店", "城市", "房型", "入住日期", "离店日期", "入住人", "间数", "夜数", "客户币种","客户币种应收", "客户币种已收", "客户币种未收", "客户币种本次账单要收","商家币种","商家币种应收", "商家币种已收", "商家币种未收", "商家币种本次账单要收","汇率", "刷卡币种", "刷卡金额", "刷卡汇率", "刷卡时间", "结算币种","结算金额", "收款汇率", "成本收款币种", "成本收款金额", "汇差", "确认状态"};

    /**
     * 客户账单表头(英文) 结算成本
     */
    String[] INPUT_CUSTOMER_BILL_SETTLEMENT_COST_TABLE_HEADER_EN_US = {"Order Number","Agreed Order", "Order Created", "Client Order Number", "Hotel", "City", "Room Type", "Checkin Date", "Checkout Date", "Guest", "Rooms", "Nights", "Client Currency", "Customer Currency Amount need to be charged", "Customer Currency Bill charged", " Customer Currency Bill uncharged", "Customer Currency Amount need to be charged in this bill", "Merchant currency", "Merchant currency  Amount need to be charged", "Merchant currency  Bill charged", "Merchant currency  Bill uncharged", "Merchant currency  Amount need to be charged in this bill", "Exchange Rate","Currency used for card swiping","Swipe amount","Card exchange rate","Swiping time","Settlement currency","Settlement amount","Receipt exchange rate","Cost collection currency","Cost receiving amount","Foreign exchange differential","Order Status"};

    /**
     * 供应商账单
     */
    String[] INPUT_SUPPLIER_BILL_TABLE_HEADER = {"供货单号", "供应商单号", "下单日期", "酒店", "房型", "入住日期", "离店日期", "入住人", "间数", "供应商币种", "供应商币种应付", "供应商币种已付", "供应商币种未付", "供应商币种本次账单要付","商家币种","商家币种应付","商家币种已付","商家币种未付","商家币种本次账单要付", "汇率", "确认状态"};

    /**
     * 供应商账单(英文)
     */
    String[] INPUT_SUPPLIER_BILL_TABLE_HEADER_EN_US = {"Batch Code", "Supplier Code", "Order Created", "Hotel", "Room Type", "Checkin Date", "Checkout Date", "Guest", "Rooms", "Supplier Currency", "Supplier Currency Amount due", "Supplier Currency Paid", "Supplier Currency Unpaid", "Supplier Currency This Bill to be Paid", "Merchant currency","Merchant currency  Amount due","Merchant currency  Paid","Merchant currency  Unpaid","Merchant currency  This Bill to be Paid","Exchange Rate","Order Status"};

    /**
     * 供应商奖励
     */
    String[] INPUT_SUPPLIER_REWARD_BILL_TABLE_HEADER = {"供货单号", "供应商单号", "下单日期", "酒店", "房型", "入住日期", "离店日期", "入住人", "间数","供应商币种", "供应商币种应收", "供应商币种已收", "供应商币种未收", "供应商币种本次账单要收","商家币种","商家币种应收", "商家币种已收", "商家币种未收", "商家币种本次账单要收", "汇率", "确认状态"};

    /**
     * 供应商奖励(英文)
     */
    String[] INPUT_SUPPLIER_REWARD_BILL_TABLE_HEADER_EN_US = {"Batch Code", "Supplier Code", "Order Created", "Hotel", "Room Type", "Check In Date", "Departure Date", "Occupant", "Quantity", "Accounts Receivable", "Received", "Not Received", "This Bill to be Received", "Exchange Rate", "Confirmed Status"};

    /**
     * 供应商返佣
     */
    String[] INPUT_SUPPLIER_REBATE_BILL_TABLE_HEADER = {"供货单号", "供应商单号", "下单日期", "酒店", "房型", "入住日期", "离店日期", "入住人", "间数", "供应商币种","供应商币种应付", "供应商币种已付", "供应商币种未付", "供应商币种本次账单要付","商家币种","商家币种应付", "商家币种已付", "商家币种未付", "商家币种本次账单要付", "汇率", "确认状态"};

    /**
     * 供应商返佣(英文)
     */
    String[] INPUT_SUPPLIER_REBATE_BILL_TABLE_HEADER_EN_US = {"Batch Code", "Supplier Code", "Order Created", "Hotel", "Room Type", "Checkin Date", "Checkout Date", "Guest", "Rooms", "Supplier Currency", "Supplier Currency Amount due", "Supplier Currency Paid", "Supplier Currency Unpaid", "Supplier Currency This Bill to be Paid", "Merchant currency","Merchant currency  Amount due","Merchant currency  Paid","Merchant currency  Unpaid","Merchant currency  This Bill to be Paid","Exchange Rate","Order Status"};

    /**
     * 结算单表列表导出
     */
    String[] INPUT_SETTLE_ORDER_TABLE_HEADER = {"订单号", "供货单", "下单日期", "客户单号", "供应商名称", "订单通订单号", "酒店/房型/产品", "入离日期", "间夜数", "供货单金额", "供货单已付金额", "供货单未付金额"};

    /**
     * 结算单表列表导出(英文)
     */
    String[] INPUT_SETTLE_ORDER_TABLE_HEADER_EN_US = {"Order Number", "Supply Order", "Order Date", "Customer Order Number", "Supplier Name", "Order Order Number", "Hotel/Room Type/Product", "Date of Entry and Exit", "Number of Nights", "Supply Order Amount", "Supply Order Paid Amount", "Supply Order Unpaid Amount"};

    /**
     * 付款任务列表导出
     */
    String[] INPUT_SETTLE_SERVICE_TABLE_HEADER = {"任务编码", "订单类型", "订单通账单编码或订单编码", "酒店名称", "银行付款状态", "订单通工单状态", "天宫工单状态", "任务状态", "付款时间", "操作人", "更新时间"};

    /**
     * 付款任务列表导出(英文)
     */
    String[] INPUT_SETTLE_SERVICE_TABLE_HEADER_EN_US = {"Task Code", "Order Type", "Order Bill Code or Order Code", "Hotel Name", "Bank Payment Status", "Order Work Order Status", "Tiangong Work Order Status", "Task Status", "Payment Time", "Operator", "Update Time"};

    /**
     * 应收报表
     */
    String[] RECEIVABLE_STATISTICS_TABLE_HEADER = {"客户名称", "客户编码", "客户经理", "结算方式", "间夜", "销售币种", "订单应收", "订单实收", "订单未收", "本币应收(CNY)", "成本(CNY)", "利润(CNY)"};

    /**
     * 应收报表(英文)
     */
    String[] RECEIVABLE_STATISTICS_TABLE_HEADER_EN_US = {"Customer Name", "Customer Code", "Customer Manager", "Settlement Method", "Inter Night", "Sales Currency", "Order Receivable", "Order Received", "Order Unreceived", "Local Currency Receivable (CNY)", "Cost (CNY)", "Profit (CNY)"};

    String RECEIVABLE_STATISTICS_FILE_NAME = "应收报表";
    String RECEIVABLE_STATISTICS_FILE_NAME_EN_US = "Receivables statement";

    /**
     * 应付报表
     */
    String[] PAYABLE_STATISTICS_TABLE_HEADER = {"供应商名称", "供应商编码", "间夜", "应付金额(CNY)", "已付金额(CNY)", "未付金额(CNY)"};

    /**
     * 应付报表(英文)
     */
    String[] PAYABLE_STATISTICS_TABLE_HEADER_EN_US = {"供应商名称", "供应商编码", "间夜", "应付金额(CNY)", "已付金额(CNY)", "未付金额(CNY)"};

    String PAYABLE_STATISTICS_FILE_NAME = "应付报表";
    String PAYABLE_STATISTICS_FILE_NAME_EN_US = "Statement payable";

    /**
     * 订单备注表
     */
    String EXPORT_ORDER_REMARK_FILE_NAME = "订单备注表";
    String EXPORT_ORDER_REMARK_FILE_NAME_EN_US = "order remark Table";

    String[] EXPORT_ORDER_REMARK_TABLE_HEADER = {"订单号", "订单状态", "供货单号", "分销商编码", "分销商名称", "供应商编码", "供应商名称", "商务经理", "下单时间", "入住时间", "离店时间", "确认时间", "备注类型", "备注内容", "备注创建人", "备注创建时间"};
    String[] EXPORT_ORDER_REMARK_TABLE_HEADER_EN_US = {"Order Code", "Order Status", "Supply Order Code", "Distributor Code", "Distributor Name", "Supplier Code", "Supplier Name", "Business Manager", "Order Time", "Check-in Time", "Check-out Time", "Confirmation Time", "Remark Type", "Remark Content", "Remark Creator", "Remark Creation Time"};

    /**
     * 订单备注表
     */
    String EXPORT_ORDER_CANCEL_REASON_FILE_NAME = "订单取消原因表";
    String EXPORT_ORDER_CANCEL_REASON_FILE_NAME_EN_US = "Order cancellation reason table";

    String[] EXPORT_ORDER_CANCEL_REASON_TABLE_HEADER = {"订单号", "订单确认时长", "取消原因", "取消内容", "订单状态", "分销商名称", "下单日期", "入住日期", "离店日期", "订单间夜", "酒店名称", "城市名称", "省份名称", "国家名称", "是否中国订单"};
    String[] EXPORT_ORDER_CANCEL_REASON_TABLE_HEADER_EN_US = {"Order Number", "Confirmation Time", "Cancel", "Reason for Cancellation", "Order Status", "Client Name", "Order Date", "Arrive Date", "Depart Date", "Room night", "Hotel Name", "City Name", "Province Name", "Country Name", "Whether to order in China"};
}
