package com.tiangong.discommon;

import java.util.List;

public interface HandlerProduct {

    /**
     * 处理增量
     *
     * @param incrementList
     */
    void handlerIncrement(List<Object> incrementList);

    /**
     * 推送全量
     *
     * @param disMappingDTO
     */
    void pushAllProduct(List<Object> disMappingDTO);

    /**
     * 推送酒店信息
     *
     * @param params
     */
    void pushHotelInfo(List<Object> params);

    /**
     * 推送房型信息
     *
     * @param params
     */
    void pushRoomInfo(List<Object> params);

    /**
     * 查询酒店信息
     *
     * @param params
     */
    void queryHotel(List<Object> params);

    /**
     * 查询房型信息
     *
     * @param params
     */
    void queryRoom(List<Object> params);

    /**
     * 推送产品
     *
     * @param params
     */
    void pushProduct(List<Object> params);

    /**
     * 查询产品
     *
     * @param params
     */
    void queryProduct(List<Object> params);
}
