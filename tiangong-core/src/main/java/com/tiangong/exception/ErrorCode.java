package com.tiangong.exception;

import lombok.Data;

/**
 * 错误码对象
 *
 */
@Data
public class ErrorCode {

    /**
     * 错误编号
     */
    private String codeNo;
    /**
     * 错误码
     */
    private String code;
    /**
     * 错误提示
     */
    private String msg;

    public ErrorCode(String code, String message) {
        this.code = code;
        this.msg = message;
    }

    public ErrorCode(String codeNo, String code, String message) {
        this.codeNo = codeNo;
        this.code = code;
        this.msg = message;
    }
}
