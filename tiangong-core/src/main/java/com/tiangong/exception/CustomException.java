package com.tiangong.exception;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 自定义异常处理
 */

@Data
@AllArgsConstructor
public class CustomException extends RuntimeException {

    protected String code;

    protected String failCode;

    protected String msg;

//    public CustomException(String failCode, String msg) {
//        super(msg);
//        this.failCode = failCode;
//        this.msg = msg;
//    }
}
