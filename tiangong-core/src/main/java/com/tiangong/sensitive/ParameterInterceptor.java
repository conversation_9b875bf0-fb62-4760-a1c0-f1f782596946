package com.tiangong.sensitive;

import com.tiangong.util.CollUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.scripting.defaults.DefaultParameterHandler;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

@Slf4j
@Intercepts({
        @Signature(type = ParameterHandler.class, method = "setParameters", args = PreparedStatement.class),
})
@Component

public class ParameterInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //@Signature 指定了 type= parameterHandler 后，这里的 invocation.getTarget() 便是parameterHandler
        // 若指定ResultSetHandler，这里则能强转为ResultSetHandler
        DefaultParameterHandler parameterHandler = (DefaultParameterHandler) invocation.getTarget();
        // 获取参数对像，即 mapper 中 paramsType 的实例
        Field parameterField = parameterHandler.getClass().getDeclaredField("parameterObject");
        parameterField.setAccessible(true);
        // 取出实例
        Object parameterObject = parameterField.get(parameterHandler);
        try {
            // 搜索该方法中是否有需要加密的字段
            List<String> paramNames = searchParamAnnotation(parameterHandler);
            if (parameterObject != null) {
                if (CollUtilX.isNotEmpty(paramNames)) {
                    PreparedStatement ps = (PreparedStatement) invocation.getArgs()[0];
                    //改写参数
                    processParam(parameterObject, paramNames);
                    //改写的参数设置到原parameterHandler对象
                    parameterField.set(parameterHandler, parameterObject);
                    parameterHandler.setParameters(ps);
                }
            }
        } catch (Exception e) {
            log.error("加解密失败！", e);
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    private List<String> searchParamAnnotation(ParameterHandler parameterHandler) throws Exception {
        Class<DefaultParameterHandler> handlerClass = DefaultParameterHandler.class;
        Field mappedStatementFiled = handlerClass.getDeclaredField("mappedStatement");
        mappedStatementFiled.setAccessible(true);
        MappedStatement mappedStatement = (MappedStatement) mappedStatementFiled.get(parameterHandler);
        String methodName = mappedStatement.getId();
        // 获取Mapper类对象
        Class<?> mapperClass = Class.forName(methodName.substring(0, methodName.lastIndexOf('.')));
        methodName = methodName.substring(methodName.lastIndexOf('.') + 1);
        Method[] methods = mapperClass.getMethods();
        Method method = null;
        for (Method m : methods) {
            if (m.getName().equals(methodName)) {
                method = m;
                break;
            }
        }
        List<String> paramList = new ArrayList<>();
        try {
            if (method != null) {
                Annotation[][] pa = method.getParameterAnnotations();
                Parameter[] parameters = method.getParameters();
                for (int i = 0; i < pa.length; i++) {
                    Parameter parameter = parameters[i];
                    String typeName = parameter.getParameterizedType().getTypeName();
                    // 去除泛型导致的ClassNotFoundException
                    Object parameterObject = parameterHandler.getParameterObject();
                    if (parameterObject == null) {//如果不判断非空 会报空指针异常 属于扩展缺少判断的问题
                        //log.info("Mapper接口" + methodName + "写法不规范 单个参数务必使用@Param 或者使用对象 Example方式");
                        return paramList;
                    }
                    Class<?> parameterClass = parameterObject.getClass();
                    SensitiveClass sensitiveClass = AnnotationUtils.findAnnotation(parameterClass, SensitiveClass.class);
                    if (Objects.nonNull(sensitiveClass)) { // 该类有字段需要被加密
                        // 多个参数时 parameterObject 为 MapperMethod.ParamMap，其中的参数key为param1、param2...
                        paramList.add("param" + (i + 1));
                    } else {
                        // 如果类上没有注解，那么就判断参数是否有注解
                        for (Annotation annotation : pa[i]) {
                            if (annotation instanceof EncryptField) {
                                if (parameterClass == String.class) {
                                    // 目前只针对String加密
                                    paramList.add("param" + (i + 1));
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("加解密失败，", e);
        }

        return paramList;
    }

    private void processParam(Object parameterObject, List<String> params) throws Exception {
        if (parameterObject instanceof MapperMethod.ParamMap) { //多个参数
            MapperMethod.ParamMap paramMap = (MapperMethod.ParamMap) parameterObject;
            for (String paramName : params) {
                Object param = paramMap.get(paramName);
                paramMap.put(paramName, EncryptUtil.encrypt(param));
            }
        } else { //单个参数
            parameterObject = EncryptUtil.encrypt(parameterObject);
        }
    }

}
