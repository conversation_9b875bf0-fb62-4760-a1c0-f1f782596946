package com.tiangong.sensitive;

import com.tiangong.util.SM4Utils;
import com.tiangong.util.Sm4O;
import com.tiangong.util.StrUtilX;

import java.lang.reflect.Field;
import java.util.Objects;

public class EncryptUtil {


    public static Object encrypt(Object param) throws Exception {
        Class<?> paramClass = param.getClass();
        if (String.class == paramClass) {
            return SM4Utils.encrypt((String) param, Sm4O.defaultKey);
        } else {
            Field[] fields = paramClass.getDeclaredFields();
            for (Field field : fields) {
                EncryptField encryptField = field.getAnnotation(EncryptField.class);
                if (Objects.nonNull(encryptField)) {
                    field.setAccessible(true);
                    Object fieldInstance = field.get(param);
                    if (fieldInstance instanceof String) {
                        field.set(param, SM4Utils.encrypt((String) fieldInstance, Sm4O.defaultKey));
                    }
                }
            }
        }
        return param;
    }

    public static Object decrypt(Object result) throws Exception {
        Class<?> resultClass = result.getClass();
        Field[] fields = resultClass.getDeclaredFields();
        for (Field field : fields) {
            EncryptField annotation = field.getAnnotation(EncryptField.class);
            if (Objects.nonNull(annotation)) {
                field.setAccessible(true);
                Object fieldInstance = field.get(result);
                if (fieldInstance instanceof String) {
                    field.set(result, StrUtilX.isNotEmpty(SM4Utils.decrypt(((String) fieldInstance), Sm4O.defaultKey)) ? (SM4Utils.decrypt(((String) fieldInstance), Sm4O.defaultKey)) : (String) fieldInstance);
                }
            }
        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        System.out.println(encrypt("86-17727554507"));
    }
}
