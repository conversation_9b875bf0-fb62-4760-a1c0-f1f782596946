package com.tiangong.constanct;

/**
 * @program: glink_shub
 * @ClassName MqConstants
 * @description:
 * @author: 湫
 * @create: 2025/04/02/ 14:02
 * @Version 1.0
 **/

public class MqConstants {

    /**
     * 天宫基础topic
     */
    private static final String TIANGONG_BASE_TOPIC = "tiangong_";

    /**
     * shub基础topic
     */
    private static final String SHUB_BASE_TOPIC = "shub_";


    public static final String ORDER_STATUS_PUSH = TIANGONG_BASE_TOPIC + "order_status_push";

    /**
     * 订单发票主题
     */
    public static final String ORDER_INVOICE_MQ = TIANGONG_BASE_TOPIC + "order_invoice_mq";

    /**
     * 订单支付超时自动取消主题
     */
    public static final String ORDER_PAYMENT_OVERTIME_CANCEL_TOPIC = TIANGONG_BASE_TOPIC + "order_payment_overtime_cancel";

    /**
     * 酒店起价路由
     */
    public static final String HOTEL_LOWEST_PRICE_TOPIC = SHUB_BASE_TOPIC + "hotel_lowest_price";

    /**
     * 新增酒店起价tag
     */
    public static final String HOTEL_LOWEST_PRICE_ADD_TAG = "add";

    /**
     * 删除酒店起价tag
     */
    public static final String HOTEL_LOWEST_PRICE_DELETE_TAG = "delete";

    /**
     * 客户信用额度变更主题
     */
    public static final String CREDIT_CHANGE_TOPIC = TIANGONG_BASE_TOPIC + "agent_credit_change";
}
