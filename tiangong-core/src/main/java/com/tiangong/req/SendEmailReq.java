package com.tiangong.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.File;

/**
 * <AUTHOR>
 * @create 2023/12/13 15:06
 */
@Data
public class SendEmailReq {

    /**
     * 邮件主题
     */
    @NotEmpty(message = "EMPTY_PARAM_TOPIC")
    private String topic;

    /**
     * 收信账号
     */
    @NotEmpty(message = "EMPTY_PARAM_EMAIL")
    private String email;

    /**
     * 邮件类型 1纯文本 2文本加图片 3文本加附件 4文本，图片，附件
     */
    @NotNull(message = "EMPTY_PARAM_EMAILTYPE")
    private Integer type;

    /**
     * 文本内容
     */
    @NotEmpty(message = "EMPTY_PARAM_CONTENT")
    private String content;

    /**
     * 抄送邮箱
     */
    private String ccEmail;
    /**
     * 密送邮箱
     */
    private String bccEmail;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 附件地址
     */
    private String fileUrl;

    /**
     * 附件名称
     */
    private String fileName;

    private File file;
}
