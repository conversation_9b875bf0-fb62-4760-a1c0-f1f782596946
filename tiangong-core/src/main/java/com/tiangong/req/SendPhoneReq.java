package com.tiangong.req;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/13 15:27
 */
@Data
public class SendPhoneReq {

    /**
     * 手机号
     */
    @NotEmpty(message = "EMPTY_PARAM_TEL")
    private String phone;

    /**
     * 类型：1国内 2海外（海外手机号需要带上区号，国内手机号不需要）
     */
    @NotNull(message = "EMPTY_PARAM_TYPE")
    private Integer type;

    /**
     * 发送内容如：{"code":"123456}
     */
    private JSONObject json;

    /**
     * 模板编码类型枚举
     */
    @NotNull(message = "EMPTY_PARAM_TEMPLATECODE")
    private String templateCode;
}
