package com.tiangong.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/13 上午11:49
 * @Description:
 */

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderNotifyMessageReq {

    /**
     * 0:新单
     * 1:申请取消订单
     * 2:供货单预定失败或发单异常消息
     */
    private Integer orderNotifyType;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 入住月份
     */
    //private String checkInMonth;

    /**
     * 入住天期
     */
    //private String checkInDay;

    /**
     * 入住日期
     */
    private String checkInDate;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 夜数
     */
    private Double nightQty;

    /**
     * 入住人信息
     */
    private String guestInfos;

}
