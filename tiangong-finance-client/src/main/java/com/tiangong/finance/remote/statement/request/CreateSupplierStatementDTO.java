package com.tiangong.finance.remote.statement.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CreateSupplierStatementDTO implements Serializable {

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 账单类型 0-供货单金额 1-奖励 2-返佣
     */
    private Integer statementType;

    /**
     * 账单名称
     */
    private String statementName;

    /**
     * 结算日期
     */
    private String settlementDate;

    /**
     * 日期查询类型：0下单日期，1入住日期，2离店日期
     */
    private Integer dateQueryType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 订单数量
     */
    private Integer orderNumber;

    /**
     * 自助结算任务编码
     */
    private String settleTaskCode;

    /**
     * 供货单列表
     */
    private List<String> supplyOrderCodeList;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 是否同步创建账单
     * 0-异步创建账单
     * 1-同步创建账单
     */
    private Integer isSyncCreateStatement;

    /**
     * 账单标签
     * 0-URL付款 1-银行转账 2-WorldFirst账户收款
     */
    private Integer statementLabel;
}
