package com.tiangong.finance.remote.lock.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FinanceLockSupplyOrderDTO implements Serializable {

    /**
     * 供货单id
     */
    private Integer supplyOrderId;

    /**
     * 锁类型：0解锁，1加锁 2强制锁
     */
    private Integer lockStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 账单类型 0-供货单金额 1-奖励 2-返佣
     */
    private Integer financeType;

    /**
     * 供货单列表
     */
    private List<String> supplyOrderList;
}
