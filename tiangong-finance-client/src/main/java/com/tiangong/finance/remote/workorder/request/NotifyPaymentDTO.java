package com.tiangong.finance.remote.workorder.request;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class NotifyPaymentDTO extends BaseDTO implements Serializable {

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 业务类型：0订单，1供货单，2分销商账单，3供应商账单，4供应商奖励账单，5供应商返佣账单
     */
    private Integer businessType;

    /**
     * 通知明细
     */
    private List<NotifyItemDTO> notifyItemDTOList;

    /**
     * 内容
     */
    private String content;

    /**
     * 支付方式
     */
    private Integer paymentType;

    /**
     * 付款方
     */
    private String payer;

    /**
     * 收款方
     */
    private String receiver;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmt;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工单附件
     */
    private List<WorkOrderAttchDTO> photoList;

    /**
     * 交易时间
     */
    private String changeTime;

    /**
     * 付款方名称
     */
    private String payerName;

    /**
     * 收款方名称
     */
    private String receiverName;

    /**
     * 付款方ID
     */
    private Integer payerId;

    /**
     * 收款方ID
     */
    private Integer receiverId;

    /**
     * 支付流水号
     */
    private String paySerialNo;
}
