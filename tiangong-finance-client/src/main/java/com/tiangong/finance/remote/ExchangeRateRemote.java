package com.tiangong.finance.remote;


import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.exchange.ExchangeRateDTO;
import com.tiangong.dto.exchange.QueryExchangeReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

@FeignClient(value = "tiangong-finance-server")
public interface ExchangeRateRemote {

    /**
     * 查询汇率
     *
     * @param request 查询汇率请求，包含查询条件
     * @return 返回分页的汇率DTO数据响应结果
     */
    @PostMapping("/finance/queryExchangeRate")
    Response<PaginationSupportDTO<ExchangeRateDTO>> queryExchangeRate(@RequestBody QueryExchangeReq request);

    /**
     * 获取币种兑换商家币种汇率
     *
     * @param originalCurrency 原始币种，要转换的币种类型
     * @param companyCode 公司编码，用于获取商家币种
     * @return 返回兑换汇率值
     */
    @GetMapping("/finance/getRateToOrgCurrency")
    BigDecimal getRateToOrgCurrency(@RequestParam("originalCurrency") Integer originalCurrency ,@RequestParam("companyCode") String companyCode);

    /**
     * 获取币种兑换指定币种的汇率
     *
     * @param originalCurrency 原始币种，要转换的币种类型
     * @param companyCode 公司编码，用于获取汇率配置
     * @param targetCurrency 目标币种，要转换到的币种类型
     * @return 返回兑换汇率值
     */
    @GetMapping("/finance/getRateToTargetCurrency")
    BigDecimal getRateToTargetCurrency(@RequestParam("originalCurrency") Integer originalCurrency ,
                                       @RequestParam("companyCode") String companyCode,
                                       @RequestParam("targetCurrency") Integer targetCurrency);
}
