package com.tiangong.finance.remote.workorder.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ConfirmWorkOrderDTO implements Serializable {

    /**
     * 工单id
     */
    private Integer workOrderId;

    /**
     * 支付方式
     */
    private Integer paymentType;

    /**
     * 付款方
     */
    private String payer;

    /**
     * 收款方
     */
    private String receiver;

    /**
     * 付款方名称
     */
    private String payerName;

    /**
     * 收款方名称
     */
    private String receiverName;

    /**
     * 收款金额
     */
    private BigDecimal amt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 凭证照片list
     */
    private List<WorkOrderAttchDTO> photoList;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 记账还是收账 （true 为收款， false 为付款）
     */
    private Boolean isCollection;

    /**
     * 交易时间
     */
    private String changeTime;

    /**
     * 付款方ID
     */
    private Integer payerId;

    /**
     * 收款方ID
     */
    private Integer receiverId;

    /**
     * 支付流水号
     */
    private String paySerialNo;
}
