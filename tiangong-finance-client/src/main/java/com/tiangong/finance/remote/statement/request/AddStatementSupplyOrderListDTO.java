package com.tiangong.finance.remote.statement.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddStatementSupplyOrderListDTO implements Serializable {

    /**
     * 账单id
     */
    private Integer statementId;

    /**
     * 供货单idList
     */
    private List<Integer> supplyOrderIdList;

    /**
     * 账单类型 0-供货单金额 1-奖励 2-返佣
     */
    private Integer statementType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 日期查询类型：0下单日期，1入住日期，2离店日期
     */
    private Integer dateQueryType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 币种
     */
    private Integer currency;
}
