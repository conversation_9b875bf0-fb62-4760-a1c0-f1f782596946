package com.tiangong.finance.remote.statement.request;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

@Data
public class QueryStatementSupplyOrderListPageDTO extends BaseRequest {

    /**
     * 账单id
     */
    private Integer statementId;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 日期查询类型：0下单日期，1入住日期，2离店日期
     */
    private Integer dateQueryType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;
}

