package com.tiangong.finance.remote.statement.request;

import com.tiangong.finance.remote.workorder.request.WorkOrderAttchDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class NotifyPaymentOfStatementDTO implements Serializable {

    /**
     * 账单id
     */
    private Integer statementId;

    /**
     * 支付方式 0-线下转账 1-在线转账 2-第三方支付
     */
    private Integer paymentType;

    /**
     * 付款方
     */
    private String payer;

    /**
     * 收款方
     */
    private String receiver;

    /**
     * 付款金额
     */
    private BigDecimal amt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 工单附件
     */
    private List<WorkOrderAttchDTO> photoList;

    /**
     * 交易时间
     */
    private String changeTime;

    /**
     * 账单类型 0-供货单金额 1-奖励 2-返佣
     */
    private Integer statementType;

    /**
     * 付款方名称
     */
    private String payerName;

    /**
     * 收款方名称
     */
    private String receiverName;

    /**
     * 付款方ID
     */
    private Integer payerId;

    /**
     * 收款方ID
     */
    private Integer receiverId;

    /**
     * 支付流水号
     */
    private String paySerialNo;
}
