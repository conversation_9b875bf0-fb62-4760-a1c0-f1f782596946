package com.tiangong.finance.remote.workorder.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: qiu
 * @create: 2024-07-25 09:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkOrderExportResponseDTO implements Serializable {


    /**
     * 财务工单编号
     */
    private String workOrderCode;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 内容
     */
    private String content;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 支付方式
     */
    private Integer paymentType;

    /**
     * 业务单号
     */
    private String businessCodes;

    /**
     * 收款方名称 new
     */
    private String receiverName;

    /**
     * 收款金额
     */
    private BigDecimal collectionAmt;

    /**
     * 付款方名称 new
     */
    private String payerName;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmt;

    /**
     * 应处理完日期
     */
    private Date dueDate;



    /**
     * 业务类型：0订单，1供货单，2分销商账单，3供应商账单，4供应商奖励账单，5供应商返佣账单
     */
    private Integer businessType;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 交易时间 new
     */
    private String changeTime;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 凭证照片 多个使用,拼接 new
     */
    private String photos;

    /**
     * 备注 new
     */
    private String remark;

    /**
     * 逾期天数
     */
    private Integer overdueDays;

    /**
     * 工单状态：0未结算，1已结算，2已删除
     */
    private Integer workOrderStatus;

    /**
     * 结算人 new
     */
    private String settledBy;

    /**
     * 结算时间 new
     */
    private Date settledDt;




}