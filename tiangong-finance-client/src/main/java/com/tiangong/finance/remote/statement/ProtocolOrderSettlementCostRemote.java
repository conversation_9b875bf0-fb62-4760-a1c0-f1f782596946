package com.tiangong.finance.remote.statement;

import com.tiangong.common.Response;
import com.tiangong.finance.remote.statement.request.ProtocolOrderSettlementCostVO;
import com.tiangong.finance.remote.statement.response.ProtocolOrderSettlementCostResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "tiangong-finance-server")
public interface ProtocolOrderSettlementCostRemote {

    /**
     * 查询协议订单结算信息列表
     */
    @PostMapping("/finance/protocolOrderSettlementCost/queryProtocolOrderSettlementCostList")
    Response<List<ProtocolOrderSettlementCostResponseDTO>> queryProtocolOrderSettlementCostList(@RequestBody ProtocolOrderSettlementCostVO request);
}
