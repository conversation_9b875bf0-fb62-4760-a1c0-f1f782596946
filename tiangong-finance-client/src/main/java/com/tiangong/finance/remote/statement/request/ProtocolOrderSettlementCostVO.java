package com.tiangong.finance.remote.statement.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProtocolOrderSettlementCostVO {
    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 刷卡金额币种
     */
    private Integer swipingCardAmountCurrency;

    /**
     * 刷卡金额
     */
    private BigDecimal swipingCardAmount;

    /**
     * 刷卡汇率
     */
    private BigDecimal swipingCardRate;

    /**
     * 刷卡时间
     */
    private String swipingCardDt;

    /**
     * 结算金额币种
     */
    private Integer settleAmountCurrency;

    /**
     * 结算金额
     */
    private BigDecimal settleAmount;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 订单编码
     */
    private String orderCode;
}
