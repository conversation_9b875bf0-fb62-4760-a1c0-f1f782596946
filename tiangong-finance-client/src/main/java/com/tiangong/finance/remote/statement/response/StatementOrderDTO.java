package com.tiangong.finance.remote.statement.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatementOrderDTO implements Serializable {

    /**
     * 账单明细id
     */
    private Integer statementOrderId;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 客户单号
     */
    private String channelOrderCode;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 入住日期
     */
    private Date startDate;

    /**
     * 离店日期
     */
    private Date endDate;

    /**
     * 入住人
     */
    private String guest;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmt;

    /**
     * 已收金额
     */
    private BigDecimal receivedAmt;

    /**
     * 未收金额
     */
    private BigDecimal unreceivedAmt;

    /**
     * 要收金额
     */
    private BigDecimal receiveAmt;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 售价汇率
     */
    private BigDecimal saleRate;

    /**
     * 商家币种转客户币种汇率
     */
    private BigDecimal orgToAgentRate;

    /**
     * 确认状态：0未确认，1确认，2已取消，3已完成
     *
     * @see com.tiangong.order.enums.ConfirmationStatusEnum
     */
    private Integer confirmationStatus;
    /**
     * 确认信息
     */
    private String confirmationStatusStr;

    /**
     * 附加费
     */
    private String additionalCharges;

    /**
     * 退订费
     */
    private String refundFee;

    /**
     * 房费
     */
    private String sumSalePrice;

    /**
     * 应收金额（商家币种）
     */
    private BigDecimal receivableOrgCurrencyAmt;

    /**
     * 已收金额（商家币种）
     */
    private BigDecimal receivedOrgCurrencyAmt;

    /**
     * 未收金额（商家币种）
     */
    private BigDecimal unreceivedOrgCurrencyAmt;

    /**
     * 要收金额（商家币种）
     */
    private BigDecimal receiveOrgCurrencyAmt;

    private Integer orderId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 夜数
     */
    private String nightQty;

    /**
     * 刷卡金额
     */
    private BigDecimal swipingCardAmount;

    /**
     * 刷卡金额币种
     */
    private Integer swipingCardAmountCurrency;

    /**
     * 刷卡汇率
     */
    private BigDecimal swipingCardRate;

    /**
     * 刷卡时间
     */
    private String swipingCardDt;

    /**
     * 结算金额
     */
    private BigDecimal settleAmount;

    /**
     * 结算金额币种
     */
    private Integer settleAmountCurrency;

    /**
     * 收款汇率(结算金额与成本收款金额的汇率)
     */
    private BigDecimal collectionRate;

    /**
     * 成本收款金额
     */
    private BigDecimal collectionAmount;

    /**
     * 汇差
     */
    private BigDecimal rateDifferenceValue;

    /**
     * 结算成本错误描述
     */
    private String settlementCostErrorDesc;
    /**
     * 供应商标签
     */
    private Integer supplierLabel;
}
