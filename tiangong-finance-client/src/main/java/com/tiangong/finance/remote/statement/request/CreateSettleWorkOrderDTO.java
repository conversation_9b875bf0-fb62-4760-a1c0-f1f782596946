package com.tiangong.finance.remote.statement.request;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CreateSettleWorkOrderDTO extends BaseRequest {

    /**
     * 自助结算任务编码
     */
    private String settleTaskCode;

    /**
     * 供货单列表
     */
    private List<String> supplyOrderCodeList;

    /**
     * 供应商总金额
     */
    private BigDecimal supplyTotalAmt;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 交易时间
     */
    private Date changeTime;

    /**
     * 付款方ID
     */
    private Integer payerId;

    /**
     * 付款方
     */
    private String payer;

    /**
     * 收款方
     */
    private String receiver;

    /**
     * 备注（流水号）
     */
    private String remark;

}
