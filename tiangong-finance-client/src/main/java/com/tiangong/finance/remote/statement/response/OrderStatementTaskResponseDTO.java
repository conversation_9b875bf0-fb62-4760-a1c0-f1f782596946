package com.tiangong.finance.remote.statement.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatementTaskResponseDTO implements Serializable {

    /**
     * 订单记账任务ID
     */
    private Integer id;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 任务来源 0-第三方支付 1-第三方退款
     */
    private Integer taskSource;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 账单编码
     */
    private String statementCode;

    /**
     * 账单创建状态 0-未处理 1-已处理 2-处理失败
     */
    private Integer statementCreateStatus;

    /**
     * 账单确认状态 0-未处理 1-已处理 2-处理失败
     */
    private Integer statementConfirmStatus;

    /**
     * 工单处理状态 0-未处理 1-已处理 2-处理失败
     */
    private Integer workOrderStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 任务状态 0-未开始 1-账单创建 2-账单确认 3-工单处理 4-已完成
     */
    private Integer taskStatus;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

}
