package com.tiangong.finance.remote.statement;

import com.tiangong.common.Response;
import com.tiangong.finance.remote.statement.request.CreateOrderStatementTaskDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "tiangong-finance-server")
public interface AgentStatementRemote {

    /**
     * 创建订单记账任务
     * 为指定的订单创建财务记账任务，用于后续的账单生成和财务处理
     * @param request 创建订单记账任务请求对象，包含订单信息、客户编码等
     * @return 创建结果响应对象
     */
    @PostMapping("/finance/agent/createOrderStatementTask")
    Response<Object> createOrderStatementTask(@RequestBody CreateOrderStatementTaskDTO request);
}
