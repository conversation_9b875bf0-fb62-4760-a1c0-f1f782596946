package com.tiangong.finance.enums;

public enum PaymentTypeEnum {
    OFFLINETRANSFER(0, "Offline transaction","线下转账"),
    ONLINETRANSFER(1, "Online money-transfer","在线转账"),
    THIRD_PARTY_PAYMENT(2, "third-party payment","第三方支付（一网通、微信、银联、支付宝）"),
    ;
    private Integer key;
    private String envalue;
    private String cnvalue;

    private PaymentTypeEnum(int key, String envalue, String cnvalue) {
        this.key = key;
        this.envalue = envalue;
        this.cnvalue = cnvalue;
    }

    public Integer getKey() {
        return key;
    }

    public String getCnvalue() {
        return cnvalue;
    }

    public String getEnvalue() {
        return envalue;
    }

    public static PaymentTypeEnum getEnumByKey(Integer key) {
        if(key==null){
            return null;
        }
        for (PaymentTypeEnum value : PaymentTypeEnum.values()) {
            if(value.key.equals(key)){
                return value;
            }
        }
        return null;
    }
}
