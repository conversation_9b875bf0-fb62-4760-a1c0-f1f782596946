package com.tiangong.finance.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/6 19:54
 * @Description:
 */
@Getter
public enum BillingRuleEnum {
    /**
     * 按订单
     */
    BY_ORDER(0, "按订单"),
    DAILY(1, "按日"),
    WEEKLY(2, "按周"),
    SEMI_MONTHLY(3, "按半月"),
    MONTHLY(4, "按月");

    private Integer code;
    private String desc;

    BillingRuleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
