package com.tiangong.finance.enums;

public enum CheckStatusEnum {
    /**
     * 不可出账
     */
    CANNOT_CHECK(0, "不可出账"),
    /**
     * 可出账
     */
    CAN_CHECK(1, "可出账"),
    /**
     * 出账中
     */
    CHECKING(2, "出账中");

    public Integer key;
    public String value;

    private CheckStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = null;
        for (CheckStatusEnum checkStatusEnum : CheckStatusEnum.values()) {
            if (checkStatusEnum.value.equals(value)) {
                key = checkStatusEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (CheckStatusEnum checkStatusEnum : CheckStatusEnum.values()) {
            if (checkStatusEnum.key.equals(key)) {
                value = checkStatusEnum.value;
                break;
            }
        }
        return value;
    }
}
