package com.tiangong.finance.enums;

/**
 * <AUTHOR>
 * @Date 2023/6/1 16:04
 * @Description:
 */
public enum ReconciliationResultTypeEnum {
    CONSISTENT(0, "一致"),
    AMOUNTINCORRECT(1, "金额不对"),
    SUPPLIERLESSORDER(2, "供应商少单"),
    LESSORDERCOUNT(3, "我方少单");


    public Integer key;
    public String value;

    private ReconciliationResultTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

}
