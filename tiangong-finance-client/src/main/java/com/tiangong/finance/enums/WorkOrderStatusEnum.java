package com.tiangong.finance.enums;

public enum WorkOrderStatusEnum {

    UN_SETTLE(0, "未结算","Wait for settlement"),
    SETTLED(1, "已结算","Settled"),
    CANCELED(2, "已取消","deleted");

    public Integer key;
    public String value;
    public String enValue;

    private WorkOrderStatusEnum(Integer key, String value,String enValue) {
        this.key = key;
        this.value = value;
        this.enValue=enValue;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = null;
        for (WorkOrderStatusEnum workOrderStatusEnum : WorkOrderStatusEnum.values()) {
            if (workOrderStatusEnum.value.equals(value)) {
                key = workOrderStatusEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (WorkOrderStatusEnum workOrderStatusEnum : WorkOrderStatusEnum.values()) {
            if (workOrderStatusEnum.key.equals(key)) {
                value = workOrderStatusEnum.value;
                break;
            }
        }
        return value;
    }

    public static WorkOrderStatusEnum getEnumByKey(Integer key) {
        WorkOrderStatusEnum workOrderStatusEnum = null;
        for (WorkOrderStatusEnum hotelFeature : WorkOrderStatusEnum.values()) {
            if (hotelFeature.key.equals(key)) {
                workOrderStatusEnum = hotelFeature;
                break;
            }
        }
        return workOrderStatusEnum;
    }

}
