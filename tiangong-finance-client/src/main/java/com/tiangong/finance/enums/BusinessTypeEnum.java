package com.tiangong.finance.enums;

public enum BusinessTypeEnum {
    /**
     * 业务类型枚举
     */
    ORDER(0, "订单"),
    /**
     * 供货单
     */
    SUPPLYORDER(1, "供货单"),
    /**
     * 分销商账单结算
     */
    AGENTSTATEMENT(2, "分销商账单结算"),
    /**
     * 供应商账单结算
     */
    SUPPLIERSTATEMENT(3, "供应商账单结算"),
    /**
     * 供应商奖励账单结算
     */
    SUPPLIER_REWARD_STATEMENT(4, "供应商账单奖励结算"),
    /**
     * 供应商返佣账单结算
     */
    SUPPLIER_REBATE_STATEMENT(5, "供应商账单返佣结算");

    public Integer key;
    public String value;

    private BusinessTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getKeyByValue(String value) {
        Integer key = null;
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                key = businessTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(Integer key) {
        String value = null;
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.key.equals(key)) {
                value = businessTypeEnum.value;
                break;
            }
        }
        return value;
    }
}
