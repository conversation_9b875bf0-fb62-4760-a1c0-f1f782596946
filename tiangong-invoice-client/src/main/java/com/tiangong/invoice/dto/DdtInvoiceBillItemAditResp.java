package com.tiangong.invoice.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2024/1/6
 */
@Data
public class DdtInvoiceBillItemAditResp implements Serializable {

    /**
     * 币种
     */
    private String currency;

    /**
     * 本次开票金额
     */
    private BigDecimal currentInvoiceAmt;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 产品类型：0-预付无佣金、1-预付有佣金、2-到店付无佣金、3-到店付有佣金
     */
    private Integer productType;

    /**
     * 订单通票单号
     */
    private String invoiceStatementCode;


}
