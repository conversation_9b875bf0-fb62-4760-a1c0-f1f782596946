package com.tiangong.invoice.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 自动创建分销商发票DTO
 *
 * <AUTHOR>
 * @date 2024/05/13
 */
@Data
public class AutoCreateAgentInvoiceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单发票ID
     */
    private Integer orderInvoiceId;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 分销商名称
     */
    private String agentName;
    /**
     * 分销商编码
     */
    private String agentCode;
    /**
     * 开票金额
     */
    private BigDecimal invoiceAmt;
    private Integer orderId;
}
