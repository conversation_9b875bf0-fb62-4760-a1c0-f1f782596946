package com.tiangong.invoice.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/27
 */
@Data
public class SupplyOrderInfoDTO implements Serializable {

    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 供应商订单号
     */
    private String supplierOrderCode;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供货单金额
     */
    private BigDecimal supplyOrderAmt;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 支付方式
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 供货单已开票金额
     */
    private BigDecimal invoicedAmt;

    /**
     * 供货单未开票金额
     */
    private BigDecimal uninvoicedAmt;


}
