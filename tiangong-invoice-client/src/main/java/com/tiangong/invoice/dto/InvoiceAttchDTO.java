package com.tiangong.invoice.dto;

import lombok.Data;

@Data
public class InvoiceAttchDTO {

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件别名
     */
    private String aliasFileName;

    /**
     * 文件url
     */
    private String fileUrl;

    /**
     * 缩略图文件url
     */
    private String thumbnailUrl;

    /**
     * 文件类型
     */
    private String fileFormat;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 宽
     */
    private Integer fileWidth;

    /**
     * 高
     */
    private Integer fileHeight;

    /**
     * 对象id
     */
    private String objId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 域名
     */
    private String domain;

    /**
     * 是否为旧的文件，复制授信文件的是时候使用
     */
    private Integer isOldFile = 0;//是否为旧文件
}