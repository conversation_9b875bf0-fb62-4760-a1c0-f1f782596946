package com.tiangong.invoice.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/26
 */
@Data
public class InvoiceableSupplierInfoResp implements Serializable {

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 待开票单数
     */
    private Integer waitingInvoiceNum;

    /**
     * 待开票金额
     */
    private BigDecimal waitingInvoiceAmt;

}
