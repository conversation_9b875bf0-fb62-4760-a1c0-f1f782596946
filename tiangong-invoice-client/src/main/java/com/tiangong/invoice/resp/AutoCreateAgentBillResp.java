package com.tiangong.invoice.resp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AutoCreateAgentBillResp {
    /**
     * 是否成功 0 非 1 是
     */
    private Integer result;
    private String errorCode;
    private String errorMsg;
    /**
     * 订单号
     */
    private Integer orderId;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 发票状态
     */
    private Integer invoiceStatus;
    /**
     * 操作人
     */
    private String operator;
}
