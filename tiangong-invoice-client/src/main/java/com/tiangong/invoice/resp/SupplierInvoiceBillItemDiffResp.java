package com.tiangong.invoice.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: zhiling
 * @date: 2024/3/2 10:24
 * @description: 订单通未添加到天宫供货单列表
 */
@Data
public class SupplierInvoiceBillItemDiffResp {

    /**
     * 票单id
     */
    private String invoiceBillId;
    /**
     * 当前供货单绑定票单id
     */
    private Long currentInvoiceBillId;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;


    /**
     * 供货单应付金额
     */
    private BigDecimal supplyOrderAmt;

    /**
     * 应开票金额
     */
    private BigDecimal invoicePayableAmt;

    /**
     * 供货单本次开票金额
     */
    private BigDecimal invoicedAmt;

    /**
     * 未开票金额
     */
    private BigDecimal uninvoicedAmt;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 确认状态：0待确认，1已确认，2已取消
     */
    private Integer confirmationStatus;

    /**
     * 订单通订单开票金额
     */
    private BigDecimal ddtInvoiceAmount;

    /**
     * 票单状态 票单状态：0未绑定，9已坏账，10已平账,11可平账
     */
    private Integer invoiceBillStatus;

    /**
     * 原因
     */
    private String reason;

    /**
     * 建议操作
     */
    private String operation;

    /**
     * 订单通订单号
     */
    private String ireveOrderCode;

}
