package com.tiangong.invoice.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tiangong.invoice.dto.InvoiceAttchDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 分销商发票表
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class AgentInvoiceResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发票id
     */
    private Long invoiceId;
    /**
     * 第三方id
     */
    private String thirdId;
    /**
     * 发票状态 发票状态：0-未使用，1-待检查，2-部分使用,3已使用
     */
    private Integer invoiceStatus;
    /**
     * 发票类型 发票类型：1-普通发票、2-专用发票、3-电子普通发票、4-电子专用发票、5-通行费电子普票、6-区块链发票、7-通用机打电子发票、8-电子发票(普通发票)、9-电子发票（专用发票）
     */
    private Integer invoiceType;
    /**
     * 购买方名称
     */
    private String purchaserName;
    /**
     * 购买方纳税人识别号
     */
    private String purchaserRegisterNum;
    /**
     * 发票号码
     */
    private String invoiceNum;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 开票时间
     */
    private String invoiceDate;
    /**
     * 发票内容
     */
    private String invoiceContent;
    /**
     * 销售方名称
     */
    private String sellerName;
    /**
     * 销售方纳税人识别号
     */
    private String sellerRegisterNum;
    /**
     * 发票金额
     */
    private BigDecimal invoiceAmt;

    /**
     * 发票金额CNY
     */
    private String invoiceAmtCur;

    /**
     * 合计税额
     */
    private BigDecimal totalTaxAmt;
    /**
     * 发票税率
     */
    private BigDecimal invoiceTaxRate;

    /**
     * 发票税率%
     */
    private String invoiceRate;

    /**
     * 备注
     */
    private String remark;
    /**
     * 发票附件
     */
    private List<InvoiceAttchDTO> url;
    /**
     * 发票来源类型 发票来源类型：0-手动录入，1-ocr识别，2系统同步，3-ireve指定客户上传，10-其它
     */
    private Integer invoiceSourceType;
    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 余额CNY
     */
    private String balanceCur;

    /**
     * 乐观锁版本号
     */
    private Integer revision;
    /**
     * 票单领用金额
     */
    private BigDecimal billUsedAmt;
    /**
     * 校验码
     */
    private String checkCode;
    /**
     * 合计金额
     */
    private BigDecimal totalAmount;

    /**
     * 本次使用金额
     */
    private BigDecimal currentAmt;

    /**
     * 本次使用金额CNY
     */
    private String currentAmtCur;

    /**
     * 票单确认时间
     */
    private Date confirmDt;

    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private String createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private String updatedDt;

    /**
     * 票单数据
     */
    private  List<AgentInvoiceBillResp> invoiceBillList;

    /**
     * 未使用金额
     */
    private BigDecimal unusedAmt;

    /**
     * 发票明细id
     */
    private String id;

    /**
     * 绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String bindDt;

}