package com.tiangong.invoice.enums;

public enum InvoiceStatusEnum {
    UNUSED(0, "未使用"),
    PENDING(1, "待检查"),
    PARTIAL_USE(2, "部分使用"),
    USED(3, "已使用"),
    REPETITION(4,"重复数据");

    public int key;
    public String value;

    private InvoiceStatusEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        Integer key = null;
        for (InvoiceStatusEnum invoiceStatusEnum : InvoiceStatusEnum.values()) {
            if (invoiceStatusEnum.value.equals(value)) {
                key = invoiceStatusEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (InvoiceStatusEnum invoiceStatusEnum : InvoiceStatusEnum.values()) {
            if (invoiceStatusEnum.key == key) {
                value = invoiceStatusEnum.value;
                break;
            }
        }
        return value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
