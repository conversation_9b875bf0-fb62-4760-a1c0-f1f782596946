package com.tiangong.invoice.enums;

import com.tiangong.enums.ErrorCodeEnum;

/**
 * 发票错误码枚举
 */
public enum InvoiceErrorCodeEnum {
    /**** 分销商调用 以8开头****/
    LOSE_APPID_OR_APPSECRET("800", "LOSE_APPID_OR_APPSECRET", "缺少appId或者appSecret"),
    AUTH_ERROR("801", "AUTH_ERROR", "没有权限"),
    PARTNER_CODER_IS_ALREADY("802", "PARTNER_CODER_IS_ALREADY", "该分销商编码已存在");

    public String errorNo;
    public String errorCode;//业务场景编号
    public String errorDesc;//业务场景描述

    private InvoiceErrorCodeEnum(String errorNo, String errorCode, String errorDesc) {
        this.errorNo = errorNo;
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getKeyByValue(String errorCode) {
        String key = "000";
        for (InvoiceErrorCodeEnum invoiceErrorCodeEnum : InvoiceErrorCodeEnum.values()) {
            if (invoiceErrorCodeEnum.errorCode.equals(errorCode)) {
                key = invoiceErrorCodeEnum.errorNo;
                break;
            }
        }
        return key;
    }

    public static String getDescByValue(String errorCode) {
        String desc = "";
        for (InvoiceErrorCodeEnum invoiceErrorCodeEnum : InvoiceErrorCodeEnum.values()) {
            if (invoiceErrorCodeEnum.errorCode.equals(errorCode)) {
                desc = invoiceErrorCodeEnum.errorDesc;
                break;
            }
        }
        return desc;
    }

    public static String getErrorCodeByKey(String key) {
        String errorCode = "";
        for (InvoiceErrorCodeEnum invoiceErrorCodeEnum : InvoiceErrorCodeEnum.values()) {
            if (invoiceErrorCodeEnum.errorNo.equals(key)) {
                errorCode = invoiceErrorCodeEnum.errorCode;
                break;
            }
        }
        return errorCode;
    }
}
