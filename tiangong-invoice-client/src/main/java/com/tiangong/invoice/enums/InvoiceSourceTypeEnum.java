package com.tiangong.invoice.enums;

public enum InvoiceSourceTypeEnum {
    MANUAL(0, "手动录入"),
    <PERSON><PERSON>(1, "OCR识别"),
    SYSTEM_SYNC(2, "第三方自动开票"),
    IREVE_CUSTOMER_UPLOAD(3, "iReve指定客户上传"),
    OTH<PERSON>(10, "其他");

    public int key;
    public String value;

    private InvoiceSourceTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        Integer key = null;
        for (InvoiceSourceTypeEnum invoiceSourceTypeEnum : InvoiceSourceTypeEnum.values()) {
            if (invoiceSourceTypeEnum.value.equals(value)) {
                key = invoiceSourceTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (InvoiceSourceTypeEnum invoiceSourceTypeEnum : InvoiceSourceTypeEnum.values()) {
            if (invoiceSourceTypeEnum.key == key) {
                value = invoiceSourceTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
