package com.tiangong.invoice.enums;

public enum DdtPayMethodTypeEnum {
    PREPAY_NO_COMMISSION(0, "预付（无佣金）"),
    PREPAY_COMMISSION(1, "预付（有佣金）"),
    SPOT_PAY_NO_COMMISSION(2, "到店付（无佣金）"),
    SPOT_PAY_COMMISSION(3, "到店付（有佣金）");

    public int key;
    public String value;

    private DdtPayMethodTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        Integer key = null;
        for (DdtPayMethodTypeEnum ddtPayMethodTypeEnum : DdtPayMethodTypeEnum.values()) {
            if (ddtPayMethodTypeEnum.value.equals(value)) {
                key = ddtPayMethodTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (DdtPayMethodTypeEnum ddtPayMethodTypeEnum : DdtPayMethodTypeEnum.values()) {
            if (ddtPayMethodTypeEnum.key == key) {
                value = ddtPayMethodTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }


}
