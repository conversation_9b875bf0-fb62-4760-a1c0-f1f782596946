package com.tiangong.invoice.enums;

public enum FinanceLogTypeEnum {
    SUPPLIER_INVOICE_TYPE(0, "进项票"),
    AGENT_INVOICE_TYPE(1, "销项票");

    private int key;
    private String value;

    FinanceLogTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
