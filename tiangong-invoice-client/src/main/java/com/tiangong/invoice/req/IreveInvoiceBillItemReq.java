package com.tiangong.invoice.req;

import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单通票单订单列表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class IreveInvoiceBillItemReq extends PageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 订单通订单编码
     */
    private String ddtOrderCode;
    /**
     * 订单通订单开票金额
     */
    private BigDecimal ddtInvoiceAmount;
    /**
     * 订单通币种
     */
    private String ddtCurrency;
    /**
     * 订单通支付类型 订单通支付类型：0:预付无佣金，1:预付有佣金，2:到店付无佣金，3:到店付有佣金
     */
    private Integer ddtPayMethod;
    /**
     * 订单通票单id
     */
    private String ddtInvoiceBillId;
    /**
     * 天宫供应商票单id
     */
    private Long tgInvoiceBillId;
    /**
     * 天宫供货单票单id
     */
    private Long tgSupplierInvoiceBillId;
    /**
     * 天宫供货单编码
     */
    private String supplyOrderCode;
    /**
     * 天宫支付方式 支付方式：1-预付 2面付无佣金
     */
    private Integer tgPayMethod;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}