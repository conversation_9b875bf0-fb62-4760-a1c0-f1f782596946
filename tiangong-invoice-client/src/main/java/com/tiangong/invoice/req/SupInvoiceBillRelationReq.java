package com.tiangong.invoice.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 票单关联发票表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class SupInvoiceBillRelationReq implements Serializable {

    private static final long serialVersionUID = -8108719593745275817L;
    /**
     * 登陆人
     */
    private String loginName;

    /**
     * 票单id
     */
    private Long invoiceBillId;


    /**
     * 票单关联发票表
     *
     */
    private List<SupplierInvoiceBillRelationReq> invoicList;
}