package com.tiangong.invoice.req;

import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 供应商票单表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL> 已使用
 * @date 2023-12-22 10:49:56
 */
@Data
public class SupplierInvoiceBillQueryReq extends PageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 票单id
     */
    private String invoiceBillId;

    /**
     * 发票id
     */
    private String invoiceId;

    /**
     * 创建开始时间
     */
    private String createdStartDt;

    /**
     * 创建结束时间
     */
    private String createdEndDt;

    /**
     * 确认开始时间
     */
    private String confirmStartDt;

    /**
     * 确认结束时间
     */
    private String confirmEndDt;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 票单状态：0未绑定，9已坏账，10已平账
     */
    private String invoiceBillStatus;

    /**
     * 票单编码
     */
    private String invoiceBillCode;

    /**
     * 发票号码
     */
    private String invoiceNum;

    /**
     * 发票号码
     */
    private List<Long> invoiceNums;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 是否显示空单：0-不显示、1显示
     */
    private Integer isEmpty;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 订单通订单号
     */
    private String ireveOrderCode;

    /**
     * 票单id集合
     */
    private Set<Long> invoiceBillIdList;

}