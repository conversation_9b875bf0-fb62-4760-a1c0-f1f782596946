package com.tiangong.invoice.req;

import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2024/1/3
 */
@Data
public class SupplierInvoiceBillItemAditReq implements Serializable {

    /**
     * 票单id
     */
    private String invoiceBillId;

    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 票单明细id
     */
    private String invoiceBillItemId;

    /**
     * 应开票金额
     */
    private BigDecimal invoicePayableAmt;

    /**
     * 本次使用金额
     */
    private BigDecimal currentAmt;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 支付方式
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 数据来源:
     * 0-非差异来源
     * 1-差异来源
     */
    private Integer dataSource;

    /**
     * 订单通订单号
     */
    private String ireveOrderCode;
}
