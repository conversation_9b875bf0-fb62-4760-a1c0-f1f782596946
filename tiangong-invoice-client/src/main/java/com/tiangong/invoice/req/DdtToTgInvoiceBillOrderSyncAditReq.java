package com.tiangong.invoice.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/27
 */
@Data
public class DdtToTgInvoiceBillOrderSyncAditReq implements Serializable {

    /**
     * ireve订单号
     */
    private String ireveOrderCode;

    /**
     * ireve订单开票金额
     */
    private BigDecimal ireveInvoiceAmount;

    /**
     * ireve订单币种
     */
    private Integer ireveCurrency;

    /**
     * ireve订单类型，
     * 旧版6面付，1预付；
     * 新版0预(无佣金),1预(有佣金) ,2面(无佣金),3面(有佣金)
     */
    private Integer ireveOrderType;

    /**
     * saas供货单号
     */
    private String saasSupplierOrderCode;

}
