package com.tiangong.invoice.req;

import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/25
 */
@Data
public class AgentInvoiceQueryPageReq extends PageDto implements Serializable {

    private static final long serialVersionUID = -8400433882562240323L;

    /**
     * 票单id
     */
    private String invoiceBillId;

    /**
     * 票单状态
     */
    private Integer invoiceBillStatus;

    /**
     * 开票开始日期
     */
    private String invoiceStartDt;

    /**
     * 开票结束日期
     */
    private String invoiceEndDt;

    /**
     * 发票号码
     */
    private String invoiceNum;

    /**
     * 购买方名称
     */
    private String purchaserName;

    /**
     * 销售方名称
     */
    private String sellerName;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建开始日期
     */
    private String createdStartDt;

    /**
     * 创建结束日期
     */
    private String createdEndDt;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 发票号码
     */
    private List<String> invoiceNumList;

}
