package com.tiangong.invoice.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/27
 */
@Data
public class SupplierInvoiceBillAditReq {

    /**
     * 票单id
     */
    private Long invoiceBillId;

    /**
     * ireve票单ID
     */
    private String ireveInvoiceBillId;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 票单名称
     */
    private String invoiceBillName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 查询口径
     */
    private Integer dateQueryType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 自动票单类型：0-手动票单、1-自动票单
     */
    private Integer isAuto;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 是否创建空票单
     */
    private boolean isAllowEmptyInvoiceBill;

    /**
     * 酒店id
     */
    private Long hotelId;
    /**
     * 结算方式：null-全部、0-月结、1-半月结、2-周结、3-单结、4-日结
     */
    private Integer settlementType;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 供货单订单列表
     */
    private List<String> supplyOrderCodeList;

    /**
     * 应开票金额
     */
    private BigDecimal invoicePayableAmt;

    /**
     * 已绑定金额
     */
    private BigDecimal invoicedAmt;
}
