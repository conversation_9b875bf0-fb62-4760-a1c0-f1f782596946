package com.tiangong.invoice.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 订单通同步天宫供货单数据
 * @date 2024/1/3
 */
@Data
public class TgToDdtInvoiceBillAditReq implements Serializable {
    /**
     * 订单通票单编号
     */
    private String invoiceStatementCode;

    /**
     * 供货单列表
     */
    private List<String> supplyOrderCodeList;

    /**
     * 订单信息列表
     */
    List<TgToDdtInvoiceBillItemAditReq> orderInfoList;

    /**
     * 发票明细
     */
    List<TgToDdtInvoiceBillInvoiceItemAditReq> invoiceList;

}
