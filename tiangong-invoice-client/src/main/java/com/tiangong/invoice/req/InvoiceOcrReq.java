package com.tiangong.invoice.req;

import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * OCR解析记录表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class InvoiceOcrReq extends PageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * ocr识别唯一标识
     */
    private String ocrIdCard;
    /**
     * 应用key
     */
    private String appKey;
    /**
     * 应用业务分类
     */
    private Integer appBussType;
    /**
     * 原始文件名称
     */
    private String oldFileName;
    /**
     * 发票号码
     */
    private String invoiceNum;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 开票时间
     */
    private Date invoiceDate;
    /**
     * 购买方名称
     */
    private String purchaserName;
    /**
     * 销售方名称
     */
    private String sellerName;
    /**
     * 文件访问地址
     */
    private String url;
    /**
     * ocr解析状态
     */
    private Integer ocrStatus;
    /**
     * ocr解析出发票原始文本
     */
    private String invoiceOcrResult;
    /**
     * ocr解析出发票格式化文本
     */
    private String invoiceOcrFmt;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}