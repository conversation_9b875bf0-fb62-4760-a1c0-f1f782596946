package com.tiangong.invoice.supplierInvoice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.invoice.supplierInvoice.domain.entity.SupplierInvoiceBillOrderStateEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;


/**
 * 供应商发票表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Mapper
public interface SupplierInvoiceBillOrderStateMapper extends BaseMapper<SupplierInvoiceBillOrderStateEntity> {

    /**
     * 批量插入，适合大量数据插入
     *
     * @param entities 实体们
     */
    default void insertBatch(Collection<SupplierInvoiceBillOrderStateEntity> entities) {
        entities.forEach(this::insert);
    }

    /**
     * 更新票单供货单信息
     */
    int updateSupplierInvoiceBillOrderState(SupplierInvoiceBillOrderStateEntity updateInvoiceBillOrderStateParam);

    /**
     * 更新票单明细已使用金额
     */
    int updateOrderReturnAmtByOrderCode(SupplierInvoiceBillOrderStateEntity updateInvoiceBillOrderStateParam);
}
