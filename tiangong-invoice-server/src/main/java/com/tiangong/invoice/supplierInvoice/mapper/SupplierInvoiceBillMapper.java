package com.tiangong.invoice.supplierInvoice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.invoice.dto.SupplyOrderInfoDTO;
import com.tiangong.invoice.dto.SupplyOrderInfoQueryDTO;
import com.tiangong.invoice.req.SupplierInvoiceBillAditReq;
import com.tiangong.invoice.req.SupplierInvoiceBillQueryReq;
import com.tiangong.invoice.resp.SupplierInvoiceBillResp;
import com.tiangong.invoice.supplierInvoice.domain.entity.SupplierInvoiceBillEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 供应商票单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Mapper
public interface SupplierInvoiceBillMapper extends BaseMapper<SupplierInvoiceBillEntity> {

    /**
     * 查询可开票供应商信息列表
     */
    List<SupplyOrderInfoDTO> queryInvoiceableSupplyOrderList(SupplierInvoiceBillAditReq req);


    /**
     * 查询票单列表
     */
    List<SupplierInvoiceBillResp> queryInvoiceBillList(SupplierInvoiceBillQueryReq req);

    /**
     * 查询票单列表(优化)
     */
    IPage<SupplierInvoiceBillResp> queryInvoiceBillPage(IPage<?> page, SupplierInvoiceBillQueryReq req);

    /**
     * 查询票单
     */
    SupplierInvoiceBillResp queryInvoiceBill(SupplierInvoiceBillQueryReq req);

    /**
     * 根据ireve订单号查询订单号(查自助结算单)
     */
    List<SupplyOrderInfoDTO> querySupplyOrderInfo(SupplyOrderInfoQueryDTO req);


    /**
     * 根据ireve订单号查询订单号(查供货单)
     */
    List<SupplyOrderInfoDTO> querySupplyOrderInfo2(SupplyOrderInfoQueryDTO req);

    /**
     * 更新票单
     */
    int updateSupplierInvoiceBill(SupplierInvoiceBillEntity updateInvoiceBillParam);
}
