package com.tiangong.invoice.supplierInvoice.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商票单供货单列表
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
@TableName("f_supplier_invoice_bill_item")
public class SupplierInvoiceBillItemEntity extends Model<SupplierInvoiceBillItemEntity> {

    private static final long serialVersionUID = 1L;
    /**
     * 自增长主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 票单id
     */
    private Long invoiceBillId;
    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 订单通订单号
     */
    private String ireveOrderCode;

    /**
     * 支付方式
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 应开票金额
     */
    private BigDecimal invoicePayableAmt;

    /**
     * 供货单本次开票金额
     */
    private BigDecimal invoiceAmt;
    /**
     * 币种
     */
    private Integer currency;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

}
