package com.tiangong.invoice.supplierInvoice.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 财务日志表
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
@TableName("f_finance_log")
public class FinanceLogEntity extends Model<FinanceLogEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 日志类型 日志类型：0-进项票、1-销项票
     */
    private Integer logType;
    /**
     * 对象id
     */
    private String objId;
    /**
     * 对象状态
     */
    private Integer objStatus;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

}
