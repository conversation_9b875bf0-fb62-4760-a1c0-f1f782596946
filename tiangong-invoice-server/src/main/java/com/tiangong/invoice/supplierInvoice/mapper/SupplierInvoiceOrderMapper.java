package com.tiangong.invoice.supplierInvoice.mapper;

import com.tiangong.invoice.req.InvoiceableSupplierInfoQueryReq;
import com.tiangong.invoice.resp.InvoiceableSupplierInfoResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SupplierInvoiceOrderMapper {

    /**
     * 查询供应商待开票信息列表
     */
    List<InvoiceableSupplierInfoResp> queryInvoiceableSupplierInfoList(InvoiceableSupplierInfoQueryReq req);


}
