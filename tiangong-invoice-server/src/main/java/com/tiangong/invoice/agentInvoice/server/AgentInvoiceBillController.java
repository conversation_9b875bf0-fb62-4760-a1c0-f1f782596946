package com.tiangong.invoice.agentInvoice.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.invoice.agentInvoice.service.AgentInvoiceBillService;
import com.tiangong.invoice.dto.AutoCreateAgentInvoiceDTO;
import com.tiangong.invoice.req.*;
import com.tiangong.invoice.resp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销商票单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Slf4j
@RestController
@RequestMapping("/invoice/agentInvoiceBill")
public class AgentInvoiceBillController extends BaseController {

    @Autowired
    private AgentInvoiceBillService agentInvoiceBillService;

    /**
     * 分销商票单表新增
     */
    @PostMapping("/agentInvoiceBillAdd")
    public Response<Long> agentInvoiceBillAdd(@RequestBody AgentInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setCreatedBy(getUserName());
        return Response.success(agentInvoiceBillService.agentInvoiceBillAdd(req));
    }

    /**
     * 分销商票单表编辑
     */
    @PostMapping("/agentInvoiceBillEdit")
    public Response<Object> agentInvoiceBillEdit(@RequestBody AgentInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.agentInvoiceBillEdit(req);
        return Response.success();
    }

    /**
     * 票单分页
     */
    @PostMapping("/agentInvoiceBillPage")
    public Response<PaginationSupportDTO<AgentInvoiceBillResp>> agentInvoiceBillPage(@RequestBody AgentInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(agentInvoiceBillService.agentInvoiceBillPage2(req));
    }

    /**
     * 进项票单详情
     */
    @PostMapping("/agentInvoiceBillDetail")
    public Response<AgentInvoiceBillDetailResp> agentInvoiceBillDetail(@RequestBody AgentInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(agentInvoiceBillService.agentInvoiceBillDetail(req));
    }

    /**
     * 查询票单详情供货单分页数据
     */
    @PostMapping("/queryInvoiceBillItemPage")
    public Response<PaginationSupportDTO<AgentInvoiceBillItemResp>> queryInvoiceBillItemPage(@RequestBody AgentInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(agentInvoiceBillService.queryInvoiceBillItemPage(req));
    }


    /**
     * 查询票单详情发票分页数据
     */
    @PostMapping("/queryInvoiceBillInvoicePage")
    public Response<PaginationSupportDTO<AgentInvoiceResp>> queryInvoiceBillInvoicePage(@RequestBody AgentInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(agentInvoiceBillService.queryInvoiceBillInvoicePage(req));
    }


    /**
     * 分销商票单表删除
     */
    @PostMapping("/agentInvoiceBillDel")
    public Response<Object> agentInvoiceBillDel(@RequestBody AgentInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.agentInvoiceBillDel(req);
        return Response.success();
    }

    /**
     * 确认平账
     */
    @PostMapping("/agentBillConfirmBalan")
    public Response<Object> suppInvoiceBillConfirmBalan(@RequestBody AgentInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.suppInvoiceBillConfirmBalan(req);
        return Response.success();
    }

    /**
     * 确认坏账
     */
    @PostMapping("/agentInvoiceBillConfirmBad")
    public Response<Object> agentInvoiceBillConfirmBad(@RequestBody AgentInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.agentInvoiceBillConfirmBad(req);
        return Response.success();
    }

    /**
     * 查询可添加的供货单明细
     */
    @PostMapping("/querySuppInvoiceBillList")
    public Response<PaginationSupportDTO<AgentInvoiceBillItemResp>> queryagentInvoiceableInvoiceBillItemList(@RequestBody AgentInvoiceBillItemQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(agentInvoiceBillService.queryagentInvoiceableInvoiceBillItemList(req));
    }

    /**
     * 票单中添加供货单
     */
    @PostMapping("/agentInvoiceBillItemAdd")
    public Response<Object> agentInvoiceBillItemAdd(@RequestBody AgentInvoiceBillItemAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setCreatedBy(getUserName());
        agentInvoiceBillService.agentInvoiceBillItemAdd(req);
        return Response.success();
    }

    /**
     * 票单中删除供货单
     */
    @PostMapping("/agentInvoiceBillItemDel")
    public Response<Object> agentInvoiceBillItemDel(@RequestBody AgentInvoiceBillItemAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setCreatedBy(getUserName());
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.agentInvoiceBillItemDel(req);
        return Response.success();
    }
    /**
     * 票单发票明细修改票单领用金额
     */
    @PostMapping("/agentInvoiceBillItemAmountEdit")
    public Response<Object> agentInvoiceBillItemAmountEdit(@RequestBody AgentInvoiceBillRelationReq req) {
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.agentInvoiceBillItemAmountEdit(req);
        return Response.success();
    }
    /**
     * 明细单有更新查询（分页）
     */
    @PostMapping("/agentInvoiceBillItemDiffPage")
    public Response<PaginationSupportDTO<AgentInvoiceBillItemResp>> agentInvoiceBillItemDiffPage(@RequestBody AgentInvoiceBillItemQueryReq req) {
        return Response.success(agentInvoiceBillService.agentInvoiceBillItemDiffPage(req));
    }

    /**
     * 明细单有更新-立即更新
     */
    @PostMapping("/agentInvoiceBillItemDiffEdit")
    public Response<Object> agentInvoiceBillItemDiffEdit(@RequestBody List<AgentInvoiceBillItemResp> reqs) {
        agentInvoiceBillService.agentInvoiceBillItemDiffEdit(reqs, getUserName());
        return Response.success();
    }

    /**
     * 查询供货单开票记录（分页）
     */
    @PostMapping("/orderInvoiceRecordPage")
    public Response<PaginationSupportDTO<AgentInvoiceBillItemResp>> supplyOrderInvoiceRecordPage(@RequestBody AgentInvoiceBillItemQueryReq req) {
        return Response.success(agentInvoiceBillService.orderInvoiceRecordPage(req));
    }


    /**
     * 查询可添加发票的列表（分页）
     */
    @PostMapping("/queryagentInvoicePage")
    public Response<PaginationSupportDTO<AgentInvoiceResp>> queryAgentInvoicePage(@RequestBody AgentInvoiceQueryPageReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(agentInvoiceBillService.queryAgentInvoicePage(req));
    }

    /**
     * 添加票单发票明细
     */
    @PostMapping("/invoiceBillInvoiceItemAdd")
    public Response<Object> invoiceBillInvoiceItemAdd(@RequestBody AgeInvoiceBillRelationReq req) {
        req.setLoginName(getUserName());
        agentInvoiceBillService.invoiceBillInvoiceItemAdd(req);
        return Response.success();
    }

    /**
     * 移除票单发票明细
     */
    @PostMapping("/invoiceBillInvoiceItemDel")
    public Response<Object> invoiceBillInvoiceItemDel(@RequestBody AgentInvoiceBillRelationReq req) {
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.invoiceBillInvoiceItemDel(req);
        return Response.success();
    }

    /**
     * 票单发票明细修改票单领用金额
     */
    @PostMapping("/invoiceBillInvoiceItemAmountEdit")
    public Response<Object> invoiceBillInvoiceItemAmountEdit(@RequestBody AgentInvoiceBillRelationReq req) {
        req.setUpdatedBy(getUserName());
        agentInvoiceBillService.invoiceBillInvoiceItemAmountEdit(req);
        return Response.success();
    }

    @PostMapping("/autoCreateAgentBill")
    @AnonymousAccess
    public Response<AutoCreateAgentBillResp> autoCreateAgentBill(@RequestBody AutoCreateAgentBillReq autoCreateAgentBillReq) {
        String orderCode = autoCreateAgentBillReq.getOrderCode();
        String invoiceNo = autoCreateAgentBillReq.getInvoiceNo();
        String companyCode = autoCreateAgentBillReq.getCompanyCode();
        String requestId = autoCreateAgentBillReq.getRequestId();
        String agentCode = autoCreateAgentBillReq.getAgentCode();
        String agentName = autoCreateAgentBillReq.getAgentName();
        BigDecimal invoiceAmt = autoCreateAgentBillReq.getInvoiceAmt();
        String operator = autoCreateAgentBillReq.getOperate();
        return Response.success( agentInvoiceBillService.autoCreateAgentInvoice(orderCode, invoiceNo, companyCode, requestId, agentCode, agentName, invoiceAmt, operator,autoCreateAgentBillReq.getOrderId()));
    }
}
