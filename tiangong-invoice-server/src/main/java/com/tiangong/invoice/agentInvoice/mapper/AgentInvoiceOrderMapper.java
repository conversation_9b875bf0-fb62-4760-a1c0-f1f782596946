package com.tiangong.invoice.agentInvoice.mapper;

import com.tiangong.invoice.req.InvoiceableAgentInfoQueryReq;
import com.tiangong.invoice.resp.InvoiceableAgentInfoResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/3/16 20:52
 * @description:
 */
@Mapper
public interface AgentInvoiceOrderMapper {
    List<InvoiceableAgentInfoResp> queryInvoiceableAgentInfoList(InvoiceableAgentInfoQueryReq req);
}
