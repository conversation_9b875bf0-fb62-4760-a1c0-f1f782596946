package com.tiangong.invoice.agentInvoice.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceBillItemEntity;
import com.tiangong.invoice.req.AgentInvoiceBillItemQueryReq;
import com.tiangong.invoice.resp.AgentInvoiceBillItemResp;

import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/3/15 21:07
 * @description:
 */
public interface AgentInvoiceBillItemService {

    /**
     * 新增票单明细
     */
    List<AgentInvoiceBillItemEntity> addSupplierInvoiceBillItem(List<AgentInvoiceBillItemEntity> invoiceBillItems);

    /**
     * 查询票单明细列表
     */
    List<AgentInvoiceBillItemResp> queryAgentInvoiceBillItemList(AgentInvoiceBillItemQueryReq queryInvoiceBillItemParam);

    /**
     * 查询可添加的供货单明细列表
     */
    PaginationSupportDTO<AgentInvoiceBillItemResp> queryAgentInvoiceableInvoiceBillItemList(AgentInvoiceBillItemQueryReq req);

    /**
     * 查询供货单开票记录（分页）
     */
    List<AgentInvoiceBillItemResp> queryOrderInvoiceRecordPage(AgentInvoiceBillItemQueryReq req);

    /**
     * 票单明细表数据新增
     */
    void addAgentInvoiceBillItem(List<AgentInvoiceBillItemEntity> invoiceBillItems);
}
