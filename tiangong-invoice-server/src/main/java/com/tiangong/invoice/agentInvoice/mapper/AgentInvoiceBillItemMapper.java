package com.tiangong.invoice.agentInvoice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceBillItemEntity;
import com.tiangong.invoice.req.AgentInvoiceBillItemQueryReq;
import com.tiangong.invoice.resp.AgentInvoiceBillItemResp;
import com.tiangong.invoice.resp.AgentInvoiceBillResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;


/**
 * 分销商票单分销商列表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Mapper
public interface AgentInvoiceBillItemMapper extends BaseMapper<AgentInvoiceBillItemEntity> {

    /**
     * 批量插入，适合大量数据插入
     *
     * @param entities 实体们
     */
    default void insertBatch(Collection<AgentInvoiceBillItemEntity> entities) {
        entities.forEach(this::insert);
    }

    List<AgentInvoiceBillItemResp> querySupplierInvoiceBillItemList(AgentInvoiceBillItemQueryReq req);

    List<AgentInvoiceBillItemResp> queryAgentOrderCodeList(AgentInvoiceBillItemQueryReq req);

    List<AgentInvoiceBillResp> queryAgentInvoiceBillList(AgentInvoiceBillItemQueryReq queryReq);

    List<AgentInvoiceBillItemResp> queryOrderInvoiceRecordPage(AgentInvoiceBillItemQueryReq req);
}
