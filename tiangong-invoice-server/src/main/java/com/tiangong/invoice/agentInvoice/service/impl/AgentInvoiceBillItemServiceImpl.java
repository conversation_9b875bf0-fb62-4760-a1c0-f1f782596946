package com.tiangong.invoice.agentInvoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceBillEntity;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceBillItemEntity;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceBillOrderStateEntity;
import com.tiangong.invoice.agentInvoice.mapper.AgentInvoiceBillItemMapper;
import com.tiangong.invoice.agentInvoice.mapper.AgentInvoiceBillMapper;
import com.tiangong.invoice.agentInvoice.mapper.AgentInvoiceBillOrderStateMapper;
import com.tiangong.invoice.agentInvoice.service.AgentInvoiceBillItemService;
import com.tiangong.invoice.req.AgentInvoiceBillItemQueryReq;
import com.tiangong.invoice.resp.AgentInvoiceBillItemResp;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/3/15 21:07
 * @description:
 */
@Slf4j
@Service
public class AgentInvoiceBillItemServiceImpl implements AgentInvoiceBillItemService {

    @Autowired
    private AgentInvoiceBillMapper agentInvoiceBillMapper;

    @Autowired
    private AgentInvoiceBillItemMapper agentInvoiceBillItemMapper;

    @Autowired
    private AgentInvoiceBillOrderStateMapper agentInvoiceBillOrderStateMapper;


    @Override
    public List<AgentInvoiceBillItemEntity> addSupplierInvoiceBillItem(List<AgentInvoiceBillItemEntity> invoiceBillItems) {
        if (CollUtilX.isEmpty(invoiceBillItems)) {
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "添加票单明细失败:票单明细数据为空！");
        }
        Long invoiceBillId = invoiceBillItems.get(0).getInvoiceBillId();
        if (invoiceBillId == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLID);
        }

        // 获取票单信息
        AgentInvoiceBillEntity invoiceBillInfo = agentInvoiceBillMapper.selectById(invoiceBillId);
        for (AgentInvoiceBillItemEntity invoiceBillItem : invoiceBillItems) {
            // 校验参数
            validateInvoiceBillItemAddParam(invoiceBillItem);

            // 查询分销商发票绑定表,不存在新增,存在修改当前供货单绑定票单id
            LambdaQueryWrapper<AgentInvoiceBillOrderStateEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AgentInvoiceBillOrderStateEntity::getOrderCode, invoiceBillItem.getOrderCode());
            AgentInvoiceBillOrderStateEntity existInvoiceBillOrderState = agentInvoiceBillOrderStateMapper.selectOne(queryWrapper);
            if (existInvoiceBillOrderState == null) {
                // 新增分销商发票绑定表
                AgentInvoiceBillOrderStateEntity insert = new AgentInvoiceBillOrderStateEntity();
                insert.setOrderCode(invoiceBillItem.getOrderCode());
                insert.setPayMethod(invoiceBillItem.getPayMethod());
                insert.setInvoiceBillStatus(invoiceBillInfo.getInvoiceBillStatus());
                insert.setAlreadyInvoiceAmt(BigDecimal.ZERO);
                insert.setCurrentInvoiceBillId(invoiceBillId);
                insert.setRevision(0);
                insert.setCreatedBy(invoiceBillItem.getCreatedBy());
                insert.setCreatedDt(invoiceBillItem.getCreatedDt());
                int i = agentInvoiceBillOrderStateMapper.insert(insert);
                if (i == 0) {
                    log.error("新增分销商发票绑定表失败,insert=" + JSON.toJSONString(insert));
                    throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
                }
            } else {
                // 修改分销商发票绑定表
                AgentInvoiceBillOrderStateEntity update = new AgentInvoiceBillOrderStateEntity();
                update.setId(existInvoiceBillOrderState.getId());
                update.setCurrentInvoiceBillId(invoiceBillId);
                update.setInvoiceBillStatus(invoiceBillInfo.getInvoiceBillStatus());
                update.setUpdatedBy(invoiceBillItem.getCreatedBy());
                update.setRevision(existInvoiceBillOrderState.getRevision());
                int i = agentInvoiceBillOrderStateMapper.updateSupplierInvoiceBillOrderState(update);
                if (i == 0) {
                    log.error("修改分销商发票绑定表失败,update=" + JSON.toJSONString(update));
                    throw new SysException(ErrorCodeEnum.SYSTEM_EDIT_FAILED);
                }
            }
            // 新增票单明细
            int i = agentInvoiceBillItemMapper.insert(invoiceBillItem);
            if (i == 0) {
                log.error("新增票单明细失败,invoiceBillItem=" + JSON.toJSONString(invoiceBillItem));
                throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
            }
        }
        return invoiceBillItems;
    }

    @Override
    public List<AgentInvoiceBillItemResp> queryAgentInvoiceBillItemList(AgentInvoiceBillItemQueryReq req) {
        if (null == req) {
            log.error("销项票明细查询失败：请求参数为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceBillId()) {
            log.error("销项票明细查询失败：请求参数为空,票单id");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLID);
        }
        if (null != req.getCurrentPage() && null != req.getPageSize()) {
            PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        }
        return agentInvoiceBillItemMapper.querySupplierInvoiceBillItemList(req);
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceBillItemResp> queryAgentInvoiceableInvoiceBillItemList(AgentInvoiceBillItemQueryReq req) {
        PaginationSupportDTO<AgentInvoiceBillItemResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        List<AgentInvoiceBillItemResp> invoiceableInvoiceBillItemList = agentInvoiceBillItemMapper.queryAgentOrderCodeList(req);
        invoiceableInvoiceBillItemList.forEach(itemResp -> {
            if ("null".equals(itemResp.getGuest())) {
                itemResp.setGuest("");
            }
        });

        PageInfo<AgentInvoiceBillItemResp> page = new PageInfo<>(invoiceableInvoiceBillItemList);
        paginationSupportDTO.setItemList(invoiceableInvoiceBillItemList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());
        return paginationSupportDTO;
    }

    @Override
    public List<AgentInvoiceBillItemResp> queryOrderInvoiceRecordPage(AgentInvoiceBillItemQueryReq req) {
        if (null == req) {
            log.error("进项票明细查询失败：请求参数为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (null != req.getCurrentPage() && null != req.getPageSize()) {
            PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        }
        return agentInvoiceBillItemMapper.queryOrderInvoiceRecordPage(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAgentInvoiceBillItem(List<AgentInvoiceBillItemEntity> invoiceBillItems) {
        if (CollUtilX.isEmpty(invoiceBillItems)) {
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "添加票单明细失败:票单明细数据为空！");
        }
        Long invoiceBillId = invoiceBillItems.get(0).getInvoiceBillId();
        if (invoiceBillId == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLID);
        }

        // 临时对象
        List<AgentInvoiceBillOrderStateEntity> orderStateEntities = new ArrayList<>();
        List<AgentInvoiceBillItemEntity> invoiceBillItemEntities = new ArrayList<>();
        // 获取票单信息
        AgentInvoiceBillEntity invoiceBillInfo = agentInvoiceBillMapper.selectById(invoiceBillId);
        for (AgentInvoiceBillItemEntity invoiceBillItem : invoiceBillItems) {
            // 校验参数
            validateInvoiceBillItemAddParam(invoiceBillItem);

            // 查询供应商发票绑定表,不存在新增,存在修改当前供货单绑定票单id
            LambdaQueryWrapper<AgentInvoiceBillOrderStateEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AgentInvoiceBillOrderStateEntity::getOrderCode, invoiceBillItem.getOrderCode());
            AgentInvoiceBillOrderStateEntity existInvoiceBillOrderState = agentInvoiceBillOrderStateMapper.selectOne(queryWrapper);
            if (existInvoiceBillOrderState == null) {
                // 新增供应商发票绑定表
                AgentInvoiceBillOrderStateEntity insert = new AgentInvoiceBillOrderStateEntity();
                insert.setOrderCode(invoiceBillItem.getOrderCode());
                insert.setPayMethod(invoiceBillItem.getPayMethod());
                insert.setInvoiceBillStatus(invoiceBillInfo.getInvoiceBillStatus());
                insert.setAlreadyInvoiceAmt(BigDecimal.ZERO);
                insert.setCurrentInvoiceBillId(invoiceBillId);
                insert.setRevision(0);
                insert.setCreatedBy(invoiceBillItem.getCreatedBy());
                insert.setCreatedDt(invoiceBillItem.getCreatedDt());
                orderStateEntities.add(insert);
            } else {
                // 修改供应商发票绑定表
                AgentInvoiceBillOrderStateEntity update = new AgentInvoiceBillOrderStateEntity();
                update.setId(existInvoiceBillOrderState.getId());
                update.setCurrentInvoiceBillId(invoiceBillId);
                update.setInvoiceBillStatus(invoiceBillInfo.getInvoiceBillStatus());
                update.setUpdatedBy(invoiceBillItem.getCreatedBy());
                update.setRevision(existInvoiceBillOrderState.getRevision());
                int i = agentInvoiceBillOrderStateMapper.updateAgentInvoiceBillOrderState(update);
                if (i == 0) {
                    log.error("修改供应商发票绑定表失败,update=" + JSON.toJSONString(update));
                    throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
                }
            }
            invoiceBillItemEntities.add(invoiceBillItem);
        }
        // 新增供应商发票绑定
        if (CollUtilX.isNotEmpty(orderStateEntities)) {
            agentInvoiceBillOrderStateMapper.insertBatch(orderStateEntities);
        }
        // 新增票单明细
        if (CollUtilX.isNotEmpty(invoiceBillItemEntities)) {
            agentInvoiceBillItemMapper.insertBatch(invoiceBillItemEntities);
        }
    }

    /**
     * 校验参数
     */
    private void validateInvoiceBillItemAddParam(AgentInvoiceBillItemEntity invoiceBillItem) {
        if (null == invoiceBillItem) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == invoiceBillItem.getInvoiceBillId()) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(invoiceBillItem.getOrderCode())) {
            log.error("销项票单添加发票明细失败，请求参数为空：订单号");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
        } else if (null == invoiceBillItem.getCurrency()) {
            log.error("销项票单添加发票明细失败，请求参数为空：币种");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CURRENCY);
        } else if (null == invoiceBillItem.getInvoicePayableAmt()) {
            log.error("销项票单添加发票明细失败，订单应收金额为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
    }
}
