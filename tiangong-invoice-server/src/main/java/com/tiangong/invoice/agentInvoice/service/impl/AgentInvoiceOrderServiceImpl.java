package com.tiangong.invoice.agentInvoice.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.invoice.agentInvoice.mapper.AgentInvoiceOrderMapper;
import com.tiangong.invoice.agentInvoice.service.AgentInvoiceOrderService;
import com.tiangong.invoice.req.InvoiceableAgentInfoQueryReq;
import com.tiangong.invoice.resp.InvoiceableAgentInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/3/16 20:50
 * @description:
 */
@Service
@Slf4j
public class AgentInvoiceOrderServiceImpl implements AgentInvoiceOrderService {

    @Autowired
    private AgentInvoiceOrderMapper agentInvoiceOrderMapper;

    @Override
    public PaginationSupportDTO<InvoiceableAgentInfoResp> invoiceableSupplierInfoPage(InvoiceableAgentInfoQueryReq req) {
        PaginationSupportDTO<InvoiceableAgentInfoResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());

        List<InvoiceableAgentInfoResp> invoiceableAgentInfoList = agentInvoiceOrderMapper.queryInvoiceableAgentInfoList(req);
        PageInfo<InvoiceableAgentInfoResp> page = new PageInfo<>(invoiceableAgentInfoList);
        paginationSupportDTO.setItemList(invoiceableAgentInfoList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());
        return paginationSupportDTO;
    }
}
