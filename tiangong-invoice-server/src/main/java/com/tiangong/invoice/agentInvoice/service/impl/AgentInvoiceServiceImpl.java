package com.tiangong.invoice.agentInvoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.file.remote.FileRemote;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceEntity;
import com.tiangong.invoice.agentInvoice.mapper.AgentInvoiceBillMapper;
import com.tiangong.invoice.agentInvoice.mapper.AgentInvoiceMapper;
import com.tiangong.invoice.agentInvoice.service.AgentInvoiceService;
import com.tiangong.invoice.config.SettingsConstant;
import com.tiangong.invoice.convert.InvoiceConvert;
import com.tiangong.invoice.dto.AgentInvoiceBaseRespDTO;
import com.tiangong.invoice.dto.InvoiceAttchDTO;
import com.tiangong.invoice.enums.BaiDuOcrErrorEnum;
import com.tiangong.invoice.enums.InvoiceSourceTypeEnum;
import com.tiangong.invoice.enums.InvoiceStatusEnum;
import com.tiangong.invoice.enums.InvoiceTypeEnums;
import com.tiangong.invoice.req.*;
import com.tiangong.invoice.resp.AgentInvoiceBillResp;
import com.tiangong.invoice.resp.AgentInvoiceResp;
import com.tiangong.invoice.resp.BaiduOcrResp;
import com.tiangong.invoice.supplierInvoice.domain.entity.InvoiceOcrEntity;
import com.tiangong.invoice.supplierInvoice.mapper.InvoiceOcrMapper;
import com.tiangong.invoice.utils.BaiduUtils;
import com.tiangong.invoice.utils.InvoiceUtil;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.HttpUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author: zhiling
 * @date: 2024/3/5 11:47
 * @description:
 */
@Slf4j
@Service
public class AgentInvoiceServiceImpl implements AgentInvoiceService {

    @Autowired
    private AgentInvoiceMapper agentInvoiceMapper;

    @Autowired
    private FileRemote fileRemote;

    @Autowired
    private AgentInvoiceBillMapper agentInvoiceBillMapper;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private InvoiceOcrMapper invoiceOcrMapper;

    // 发票可识别文件类型
    private final String[] invoiceFileAllowType = {"PNG", "JPG", "JPEG", "BMP", "PDF"};

    // 百度ORC识别id(生产环境)，用于判断，测试的只能用base64将图片给到百度
    private static String PRODUCTION_BAIDU_CLIENT_ID = "KPdcdshFCEXI7WBPx8nIUoSu";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceAdd(AgentInvoiceAditReq req) {
        Response<Long> response = new Response<>();
        // 校验必填参数
        validateInvoiceAddParam(req, response);

        // 做唯一校验
        LambdaQueryWrapper<AgentInvoiceEntity> queryInvoiceWrapper = new LambdaQueryWrapper<>();
        queryInvoiceWrapper.eq(AgentInvoiceEntity::getInvoiceNum, req.getInvoiceNum());
        queryInvoiceWrapper.eq(StrUtilX.isNotEmpty(req.getInvoiceCode()), AgentInvoiceEntity::getInvoiceCode, req.getInvoiceCode());
        List<AgentInvoiceEntity> existInvoiceList = agentInvoiceMapper.selectList(queryInvoiceWrapper);
        if (CollUtilX.isNotEmpty(existInvoiceList)) {
            log.error(req.getInvoiceNum() + ",该发票已添加，不可再添加");
            throw new SysException(ParamErrorEnum.INVOICENUM_CORRES_INVOICE_EXIST);
        }
        // 开票日期
        Date invoiceDate = DateUtilX.stringToDate(req.getInvoiceDate());
        // 插入数据
        AgentInvoiceEntity insertInvoiceParam = new AgentInvoiceEntity();
        // 税额
        if (null != req.getTotalTaxAmt()) {
            insertInvoiceParam.setTotalTaxAmt(req.getTotalTaxAmt());
            // 未税金额 = 价税合计 - 税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(req.getTotalTaxAmt());
            insertInvoiceParam.setTotalAmt(totalAmt);
        } else {
            BigDecimal totalTaxAmt = totalTaxAmtCalculate(req.getInvoiceAmt(), req.getInvoiceTaxRate());
            insertInvoiceParam.setTotalTaxAmt(totalTaxAmt);
            // 未税金额 = 价税合计 - 税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(totalTaxAmt);
            insertInvoiceParam.setTotalAmt(totalAmt);
        }
        insertInvoiceParam.setMerchantCode(req.getMerchantCode());
        insertInvoiceParam.setThirdId(req.getThirdId());
        insertInvoiceParam.setInvoiceStatus(InvoiceStatusEnum.UNUSED.getKey());
        insertInvoiceParam.setInvoiceType(req.getInvoiceType());
        insertInvoiceParam.setPurchaserName(req.getPurchaserName());
        insertInvoiceParam.setPurchaserRegisterNum(req.getPurchaserRegisterNum());
        insertInvoiceParam.setSellerName(req.getSellerName());
        insertInvoiceParam.setSellerRegisterNum(req.getSellerRegisterNum());
        insertInvoiceParam.setInvoiceNum(req.getInvoiceNum());
        insertInvoiceParam.setInvoiceCode(req.getInvoiceCode());
        insertInvoiceParam.setInvoiceAmt(req.getInvoiceAmt());
        insertInvoiceParam.setInvoiceTaxRate(req.getInvoiceTaxRate());
        insertInvoiceParam.setRemark(req.getRemark());
        insertInvoiceParam.setInvoiceSourceType(req.getInvoiceSourceType());
        insertInvoiceParam.setBalance(req.getInvoiceAmt());
        insertInvoiceParam.setRevision(0);
        insertInvoiceParam.setBillUsedAmt(BigDecimal.ZERO);
        insertInvoiceParam.setInvoiceDate(invoiceDate);
        insertInvoiceParam.setInvoiceContent(req.getInvoiceContent());
        insertInvoiceParam.setCreatedBy(req.getCreatedBy());
        insertInvoiceParam.setCreatedDt(DateUtilX.getCurrentDate());
        agentInvoiceMapper.insert(insertInvoiceParam);

        // 保存发票附件
        updateInvoiceAttch(req, insertInvoiceParam);
    }

    /**
     * 保存发票附件
     */
    private void updateInvoiceAttch(AgentInvoiceAditReq req, AgentInvoiceEntity entity) {
        if (req.getUrl() != null && req.getUrl().size() > 0) {
            for (InvoiceAttchDTO photo : req.getUrl()) {
                FileReq fileReq = new FileReq();
                fileReq.setFileId(photo.getFileId());
                fileReq.setObjId(entity.getInvoiceId().toString());
                fileReq.setTableName("f_agent_invoice");
                fileRemote.update(fileReq);
            }
        }
    }

    /**
     * 计算合计税额
     *
     * @param invoiceAmt     发票金额
     * @param invoiceTaxRate 发票税率
     *                       公式 合计税额 = 价税 - (价税/(1+合计税率))
     */
    private BigDecimal totalTaxAmtCalculate(BigDecimal invoiceAmt, BigDecimal invoiceTaxRate) {
        // 税率转成小数
        BigDecimal invoiceTaxRateNum = invoiceTaxRate.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        // 合计税额 = 价税 - (价税/(1+合计税率))

        return invoiceAmt.subtract(invoiceAmt.divide(BigDecimal.ONE.add(invoiceTaxRateNum), 2, RoundingMode.HALF_UP));
    }


    @Override
    public void invoiceEdit(AgentInvoiceAditReq req) {
        // 校验入参
        validateInvoiceEditParam(req);

        // 校验是否存在该id的记录
        AgentInvoiceEntity existInvoiceById = agentInvoiceMapper.selectById(req.getInvoiceId());
        if (null == existInvoiceById) {
            log.error("销项票发票编辑失败：该发票id的记录不存在," + req.getInvoiceId());
            throw new SysException(ParamErrorEnum.INVOICENUM_CORRES_INVOICE_EXIST);
        }

        // 发票状态部分使用和已使用不可编辑
        if (existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.PARTIAL_USE.getKey() || existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.USED.getKey()) {
            log.error("销项票发票编辑失败：发票状态是部分使用或已使用，不可编辑发票");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "销项票发票编辑失败：发票状态是部分使用或已使用，不可编辑发票");
        }

        // 校验是是否存在该发票号码的记录
        LambdaQueryWrapper<AgentInvoiceEntity> queryInvoiceWrapper = new LambdaQueryWrapper<>();
        queryInvoiceWrapper.eq(AgentInvoiceEntity::getInvoiceNum, req.getInvoiceNum());
        queryInvoiceWrapper.eq(StrUtilX.isNotEmpty(req.getInvoiceCode()), AgentInvoiceEntity::getInvoiceCode, req.getInvoiceCode());
        queryInvoiceWrapper.ne(AgentInvoiceEntity::getInvoiceId, req.getInvoiceId());
        AgentInvoiceEntity existInvoiceByInvoice = agentInvoiceMapper.selectOne(queryInvoiceWrapper);
        if (existInvoiceByInvoice != null) {
            log.error("销项票发票编辑失败：" + req.getInvoiceNum() + "，该发票号码的发票已存在！");
            throw new SysException(ParamErrorEnum.INVOICENUM_CORRES_INVOICE_EXIST);
        }

        //开票日期
        Date invoiceDate = DateUtilX.stringToDate(req.getInvoiceDate());
        //更新发票
        AgentInvoiceEntity updateInvoiceParam = new AgentInvoiceEntity();
        //算出合计税额 和 合计金额
        if (null != req.getTotalTaxAmt()) {
            updateInvoiceParam.setTotalTaxAmt(req.getTotalTaxAmt());
            //未税金额=价税合计-税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(req.getTotalTaxAmt());
            updateInvoiceParam.setTotalAmt(totalAmt);
        } else {
            BigDecimal totalTaxAmt = totalTaxAmtCalculate(req.getInvoiceAmt(), req.getInvoiceTaxRate());
            updateInvoiceParam.setTotalTaxAmt(totalTaxAmt);
            //未税金额=价税合计-税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(totalTaxAmt);
            updateInvoiceParam.setTotalAmt(totalAmt);
        }
        updateInvoiceParam.setInvoiceId(req.getInvoiceId());
        updateInvoiceParam.setInvoiceType(req.getInvoiceType());
        updateInvoiceParam.setPurchaserName(req.getPurchaserName());
        updateInvoiceParam.setPurchaserRegisterNum(req.getPurchaserRegisterNum());
        updateInvoiceParam.setSellerName(req.getSellerName());
        updateInvoiceParam.setSellerRegisterNum(req.getSellerRegisterNum());
        updateInvoiceParam.setInvoiceNum(req.getInvoiceNum());
        updateInvoiceParam.setInvoiceCode(req.getInvoiceCode());
        updateInvoiceParam.setInvoiceAmt(req.getInvoiceAmt());
        updateInvoiceParam.setInvoiceTaxRate(req.getInvoiceTaxRate());
        updateInvoiceParam.setRemark(req.getRemark());
        updateInvoiceParam.setBalance(req.getInvoiceAmt());
        updateInvoiceParam.setInvoiceDate(invoiceDate);
        updateInvoiceParam.setInvoiceContent(req.getInvoiceContent());
        updateInvoiceParam.setUpdatedBy(req.getUpdatedBy());
        updateInvoiceParam.setRevision(existInvoiceById.getRevision());
        int i = agentInvoiceMapper.updateInvoice(updateInvoiceParam);
        if (i == 0) {
            // 记录无更新
            log.error("销项票发票编辑失败：发票记录无更新");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "发票NO:" + req.getInvoiceNum() + " 数据已变化请刷新后重试");
        }
        // 保存发票附件
        updateInvoiceAttch(req, updateInvoiceParam);
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceResp> invoicePage(AgentInvoiceQueryPageReq req) {
        // 查询发票分页
        PaginationSupportDTO<AgentInvoiceResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize(), true, false, null);
        List<AgentInvoiceResp> invoiceList = agentInvoiceMapper.queryInvoiceList(req);
        for (AgentInvoiceResp invoice : invoiceList) {
            if (null != invoice.getInvoiceTaxRate()) {
                invoice.setInvoiceRate(invoice.getInvoiceTaxRate().stripTrailingZeros().toPlainString() + "%");
            }
            invoice.setInvoiceAmtCur(invoice.getInvoiceAmt() + " CNY");
            invoice.setBalanceCur(invoice.getBalance() + " CNY");
            //查询发票附件
            FileReq fileReq = new FileReq();
            fileReq.setObjId(invoice.getInvoiceId().toString());
            fileReq.setTableName("f_agent_invoice");
            Response<List<FileResp>> fileResp = fileRemote.list(fileReq);
            if (fileResp.getModel() != null && fileResp.getModel().size() > 0) {
                List<InvoiceAttchDTO> photoList = InvoiceConvert.INSTANCE.invoiceAttchDTO(fileResp.getModel());
                // 使用 Comparator 接口实现自定义的比较逻辑
                Comparator<InvoiceAttchDTO> comparator = Comparator.comparing(InvoiceAttchDTO::getFileId).reversed();
                // 使用 Collections.sort() 方法进行排序
                photoList.sort(comparator);
                invoice.setUrl(photoList);
            }
        }
        PageInfo<AgentInvoiceResp> page = new PageInfo<>(invoiceList);
        paginationSupportDTO.setItemList(page.getList());
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());
        return paginationSupportDTO;
    }

    @Override
    public AgentInvoiceResp invoiceDetail(String merchantCode, AgentInvoiceAditReq agentInvoiceAditReq) {
        if (null == agentInvoiceAditReq.getInvoiceId() || null == merchantCode) {
            log.error("销项票查询发票详情失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 查询发票详情
        AgentInvoiceResp invoiceDetail = agentInvoiceMapper.queryInvoice(merchantCode, agentInvoiceAditReq.getInvoiceId());
        if (invoiceDetail.getInvoiceTaxRate() != null) {
            invoiceDetail.setInvoiceRate(invoiceDetail.getInvoiceTaxRate().stripTrailingZeros().toPlainString() + "%");
        }
        // 查询发票附件
        FileReq fileReq = new FileReq();
        fileReq.setObjId(agentInvoiceAditReq.getInvoiceId().toString());
        fileReq.setTableName("f_agent_invoice");
        Response<List<FileResp>> fileResp = fileRemote.list(fileReq);
        if (fileResp.getModel() != null && fileResp.getModel().size() > 0) {
            List<InvoiceAttchDTO> photoList = InvoiceConvert.INSTANCE.invoiceAttchDTO(fileResp.getModel());
            // 使用 Comparator 接口实现自定义的比较逻辑
            Comparator<InvoiceAttchDTO> comparator = Comparator.comparing(InvoiceAttchDTO::getFileId).reversed();
            // 使用 Collections.sort() 方法进行排序
            photoList.sort(comparator);
            List<InvoiceAttchDTO> photoList0 = new ArrayList<>();
            photoList0.add(photoList.get(0));
            invoiceDetail.setUrl(photoList0);
        }
        // 查询详情(票单维度)
        AgentInvoiceBillQueryReq queryInvoiceBillParam = new AgentInvoiceBillQueryReq();
        queryInvoiceBillParam.setInvoiceId(agentInvoiceAditReq.getInvoiceId().toString());
        queryInvoiceBillParam.setMerchantCode(merchantCode);
        // 票单
        List<AgentInvoiceBillResp> agentInvoiceBillList = agentInvoiceBillMapper.queryInvoiceBillList(queryInvoiceBillParam);
        for (AgentInvoiceBillResp resp : agentInvoiceBillList) {
            BigDecimal uninvoicedAmt = resp.getInvoicePayableAmt().subtract(resp.getInvoicedAmt());
            resp.setUninvoicedAmt(uninvoicedAmt);
            resp.setInvoicePayableAmtCur("CNY " + resp.getInvoicePayableAmt());
            resp.setInvoicedAmtCur("CNY " + resp.getInvoicedAmt());
            resp.setUninvoicedAmtCur("CNY " + resp.getUninvoicedAmt());
        }
        invoiceDetail.setInvoiceBillList(agentInvoiceBillList);
        return invoiceDetail;
    }

    @Override
    public void invoiceDel(Long invoiceId) {
        // 验证参数
        if (null == invoiceId) {
            log.error("销项票查询发票删除失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 校验是否存在该id的记录
        AgentInvoiceEntity existInvoiceById = agentInvoiceMapper.selectById(invoiceId);
        if (null == existInvoiceById) {
            log.error("销项票查询发票删除失败：该发票id的记录不存在," + invoiceId);
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "销项票查询发票删除失败，该发票id的记录不存在," + invoiceId);
        }

        // 发票状态部分使用和已使用不可删除
        if (existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.PARTIAL_USE.getKey() || existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.USED.getKey()) {
            log.error("销项票发票删除失败：发票状态是部分使用或已使用，不可删除发票");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "销项票发票删除失败：发票状态是部分使用或已使用，不可删除发票");
        }

        // 删除发票
        int i = agentInvoiceMapper.deleteById(invoiceId);
        if (i == 0) {
            // 记录无更新
            log.error("销项票发票删除失败");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
    }

    @Override
    public BigDecimal taxAmtCalculate(AgentInvoiceAditReq req) {
        if (null == req) {
            log.error("销项票计算税额：请求参数为空！");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceAmt()) {
            log.error("销项票计算税额：请求参数为空！");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceTaxRate()) {
            log.error("销项票计算税额：请求参数为空！");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceAmt().compareTo(BigDecimal.ZERO) < 0) {
            log.error("销项票计算税额：发票金额小于0！");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(BigDecimal.ZERO) < 0) {
            log.error("销项票计算税额：税率小于0！");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }

        // 计算税额
        return totalTaxAmtCalculate(req.getInvoiceAmt(), req.getInvoiceTaxRate());
    }

    @Override
    @Transactional
    public void invoiceSyncToTg(DdtToTgInvoiceSyncAditReq req) {
        // 校验入参
        validateInvoiceSyncToTgParam(req);

        // 转换参数新增发票
        AgentInvoiceAditReq supplierInvoiceAddParam = new AgentInvoiceAditReq();
        supplierInvoiceAddParam.setMerchantCode(req.getSaasMerchantCode());
        supplierInvoiceAddParam.setInvoiceSourceType(InvoiceSourceTypeEnum.SYSTEM_SYNC.getKey());
        supplierInvoiceAddParam.setInvoiceType(req.getInvoiceType());
        supplierInvoiceAddParam.setInvoiceNum(req.getInvoiceNum());
        supplierInvoiceAddParam.setInvoiceCode(req.getInvoiceCode());
        supplierInvoiceAddParam.setPurchaserName(req.getPurchaserName());
        supplierInvoiceAddParam.setPurchaserRegisterNum(req.getPurchaserRegisterNum());
        supplierInvoiceAddParam.setSellerName(req.getSellerName());
        supplierInvoiceAddParam.setSellerRegisterNum(req.getSellerRegisterNum());
        supplierInvoiceAddParam.setInvoiceContent(req.getCommodityName());
        supplierInvoiceAddParam.setInvoiceAmt(req.getAmountInFiguers());
        supplierInvoiceAddParam.setInvoiceTaxRate(req.getInvoicetaxrate());
        supplierInvoiceAddParam.setTotalTaxAmt(req.getTotaltax());
        supplierInvoiceAddParam.setTotalAmt(req.getTotalAmount());
        supplierInvoiceAddParam.setRemark(req.getRemarks());
        supplierInvoiceAddParam.setCreatedBy(Constant.SYSTEM);
        supplierInvoiceAddParam.setCheckCode(req.getCheckCode());

        // 新增发票
        invoiceAdd(supplierInvoiceAddParam);
    }


    @Override
    public List<AgentInvoiceResp> queryAgentBillInvoiceItemList(AgentInvoiceQueryPageReq req) {
        if (null == req) {
            log.error("查询票单的发票明细失败：请求参数为空！");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
        if (null != req.getCurrentPage() && null != req.getPageSize()) {
            PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        }
        // 票单
        return agentInvoiceMapper.queryAgentInvoiceBillInvoiceItemList(req);
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceResp> queryAgentInvoiceList(AgentInvoiceQueryPageReq req) {
        // 校验公共参数
//        validateInvoiceableInvoiceListParam(req);

        if (StrUtilX.isNotEmpty(req.getInvoiceStartDt()) && StrUtilX.isNotEmpty(req.getInvoiceEndDt())) {
            req.setInvoiceStartDt(req.getInvoiceStartDt() + " 00:00:00");
            req.setInvoiceEndDt(req.getInvoiceEndDt() + " 23:59:59");
        }
        //查询待开票发票列表
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        List<AgentInvoiceResp> invoiceableList = agentInvoiceMapper.queryAgentInvoiceableInvoiceList(req);
        PaginationSupportDTO<AgentInvoiceResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageInfo<AgentInvoiceResp> page = new PageInfo<>(invoiceableList);
        paginationSupportDTO.setItemList(invoiceableList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());

        return paginationSupportDTO;
    }

    @Override
    public List<AgentInvoiceEntity> invoiceQuery(AgentInvoiceAditReq req) {
        LambdaQueryWrapper<AgentInvoiceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(req.getInvoiceNum()), AgentInvoiceEntity::getInvoiceNum, req.getInvoiceNum());
        queryWrapper.eq(null != req.getInvoiceId(), AgentInvoiceEntity::getInvoiceId, req.getInvoiceId());
        return agentInvoiceMapper.selectList(queryWrapper);
    }

    @Override
    public void updateById(AgentInvoiceEntity entity) {
        agentInvoiceMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean orcFileCreateInvoice(MultipartFile[] files, LoginUser loginUser) {
        if (files == null) {
            log.error("请求参数不能为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (files.length > 20) {
            log.error("一次最多识别20个文件");
            throw new SysException(ErrorCodeEnum.MAX_TWENTY_FILE_FAILED.errorCode, ErrorCodeEnum.MAX_TWENTY_FILE_FAILED.errorDesc);
        }
        // 次数校验 todo 限制商家ocr使用功能先不处理
//        MerchantOcrRechargeReq merchantOcrRechargeVo = new MerchantOcrRechargeReq();
//        merchantOcrRechargeVo.setMerchantCode(merchantCode);
//        ResultVo resultVo = adminFeign.queryMerchantOcrResidueCount(merchantOcrRechargeVo);
//        if (resultVo == null || resultVo.getResult() != 1) {
//            log.error("调用admin服务查询充值记录失败request=" + JSON.toJSONString(merchantOcrRechargeVo) + ",response=" + JSON.toJSONString(resultVo));
//            throw new SysException(ResultEnum.SYSTEM_ERROR.getCode(), ResultEnum.SYSTEM_ERROR.getMessage());
//        }
//        Integer count = MyJSONUtils.jsonToObject(resultVo.getData(), Integer.class);
//        if (count == null) {
//            throw new SysException("您未充值暂无法使用");
//        } else if (count < 1) {
//            throw new SysException("充值余额不足无法使用");
//        } else if (count < files.length) {
//            throw new SysException("剩余次数不足无法使用");
//        }

        List<AgentInvoiceEntity> entityList = new ArrayList<>();// 需要新增的发票信息
        List<FileReq> delFileIds = new ArrayList<>();// 识别失败需要删除的文件id
        List<String> delFileUrls = new ArrayList<>();// 识别失败需要删除的文件url
//        List<FileReq> fileVoList = new ArrayList<>();// 绑定图片关系
        // todo 限制商家ocr使用功能先不处理
        List<InvoiceOcrEntity> merchantOcrItemVoList = new ArrayList<>();// 记录识别信息并扣减费用
        Map<String, AgentInvoiceEntity> invoiceMap = new HashMap<>();// 存放本次新增发票信息，用与判断重复发票，key = 发票号码+发票代码
        String baiduToken = queryBaiDuOrcToken();// 获取百度token
        Date currentDate = DateUtilX.getCurrentDate();

        // 上传文件到服务器
        List<CompletableFuture<FileResp>> uploadFutures = new ArrayList<>();
        // 获取当前线程请求头信息(解决丢失请求头问题)
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        for (MultipartFile file : files) {
            uploadFutures.add(CompletableFuture.supplyAsync(() -> {
                RequestContextHolder.setRequestAttributes(attributes);
                Response<FileResp> upload = fileRemote.upload(file);
                return upload.getModel();
            }));
        }

        // 等待所有上传任务完成并获取结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(uploadFutures.toArray(new CompletableFuture[0]));
        try {
            allFutures.join();// 阻塞等待所有上传任务完成
        } catch (Exception e) {
            log.error("异步上传文件失败", e);
            throw new SysException(ErrorCodeEnum.ASYN_FILE_UPLOAD_FAILED.errorCode, ErrorCodeEnum.ASYN_FILE_UPLOAD_FAILED.errorDesc);
        }

        // 获取所有上传结果
        List<FileResp> uploadFileResp = uploadFutures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        // 上传文件到服务器
        // 异步执行
        List<CompletableFuture<Response<String>>> futures = new ArrayList<>();
        for (FileResp fileDto : uploadFileResp) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    // 1.调用百度ocr接口识别
                    BaiduOcrResp resp = this.baiduOcr(fileDto, baiduToken);
                    JSONObject resultObj = resp.getResultObj();

                    if (!resultObj.containsKey("words_result_num") && resultObj.getIntValue("words_result_num") < 1) {
                        log.error("百度API识别图片失败，result=" + resp.getResult());
                        if (resultObj.containsKey("error_code")) {
                            log.error(BaiDuOcrErrorEnum.getValueByKey(resultObj.getIntValue("error_code")));
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
                        } else {
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
                        }
                    } else {
                        // 组装发票信息
                        AgentInvoiceEntity entity = this.assembleInvoice(resultObj, loginUser.getCompanyCode(),
                                currentDate, loginUser.getUserAccount(), resp.getIsRollInvoice(), invoiceMap, InvoiceSourceTypeEnum.OCR.getKey());
                        entity.setUpdatedBy(fileDto.getFileId());
                        entityList.add(entity);
                        String invoiceKey = entity.getInvoiceNum() + entity.getInvoiceCode();
                        invoiceMap.put(invoiceKey, entity);
                        // 2.发票识别成功，发票验真 todo 测试先不验真
//                        this.baiduInvoiceVerification(entity,baiduToken);

                        // 组装记录发票识别
                        InvoiceOcrEntity merchantOcrItemVo = new InvoiceOcrEntity();
                        merchantOcrItemVo.setInvoiceCode(entity.getInvoiceCode());
                        merchantOcrItemVo.setInvoiceNum(entity.getInvoiceNum());
                        merchantOcrItemVo.setPurchaserName(entity.getPurchaserName());
                        merchantOcrItemVo.setSellerName(entity.getSellerName());
                        merchantOcrItemVo.setUrl(fileDto.getFileUrl());
                        merchantOcrItemVo.setOldFileName(fileDto.getFileName());
                        merchantOcrItemVo.setCreatedBy(loginUser.getUserAccount());
                        merchantOcrItemVo.setCreatedDt(currentDate);
                        merchantOcrItemVo.setInvoiceDate(entity.getInvoiceDate());
                        // todo 限制商家ocr使用功能先不处理
                        merchantOcrItemVoList.add(merchantOcrItemVo);

//                        //更新附件信息
//                        FileReq fileReq = new FileReq();
//                        fileReq.setFileId(fileDto.getFileId());
//                        fileReq.setTableName("f_supplier_invoice");
//                        fileVoList.add(fileReq);
                    }
                    return Response.success(fileDto.getFileName());
                } catch (SysException e) {
                    log.error("识别发票失败", e);
                    // 需要删除服务器文件并删除数据记录
                    FileReq req = new FileReq();
                    req.setFileId(fileDto.getFileId());
                    delFileIds.add(req);

                    return Response.error(e.getCode(), e.getMessage(), fileDto.getFileName());
                } catch (Exception e) {
                    log.error("识别发票异常", e);
                    // 需要删除服务器文件并删除数据记录
                    FileReq req = new FileReq();
                    req.setFileId(fileDto.getFileId());
                    delFileIds.add(req);

                    return Response.error(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode,
                            ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc, fileDto.getFileName());
                }
            }));
        }

        // 等待全部完成
        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futures.stream().toArray(CompletableFuture[]::new));
        List<Response<String>> resultList = voidCompletableFuture.thenApply(v ->
                futures.stream().map(CompletableFuture::join).collect(Collectors.toList())).join();
        try {
            CompletableFuture.allOf(voidCompletableFuture).get();
        } catch (Exception e) {
            log.error("异步识别发票识别异常", e);
            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
        }

        // 判断OCR是否出现异常
        if (CollUtilX.isNotEmpty(resultList)) {
            List<Response<String>> collect = resultList.stream().filter(item -> item.getResult() == 0).collect(Collectors.toList());
            if (CollUtilX.isNotEmpty(collect)) {
                log.error("ocr处理失败:失败原因" + JSON.toJSONString(collect));
                throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
            }
        }

        // 新增发票信息
        if (CollUtilX.isNotEmpty(entityList)) {
            for (AgentInvoiceEntity entity : entityList) {
                try {
                    String fileId = entity.getUpdatedBy();
                    entity.setUpdatedBy(null);
                    int insert = agentInvoiceMapper.insert(entity);
                    if (insert == 0) {
                        throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
                    }
                    FileReq fileReq = new FileReq();
                    fileReq.setFileId(fileId);
                    fileReq.setObjId(entity.getInvoiceId().toString());
                    fileReq.setTableName("f_agent_invoice");
                    fileRemote.update(fileReq);
                } catch (DuplicateKeyException exception) {
                    log.error("新增发票信息异常====================>>>", exception);
                    throw new SysException(ParamErrorEnum.INVOICENUM_CORRES_INVOICE_EXIST);
                } catch (Exception e) {
                    log.error("新增发票信息异常====================>>>", e);
                    throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
                }
            }
        }

        // todo 限制商家ocr使用功能先不处理
        // 记录识别信息并扣减费用
        if (CollUtilX.isNotEmpty(merchantOcrItemVoList)) {
            for (InvoiceOcrEntity ocrEntity : merchantOcrItemVoList) {
                int insert = invoiceOcrMapper.insert(ocrEntity);
                if (insert == 0) {
                    log.error("OCR解析记录表新增失败" + JSON.toJSONString(merchantOcrItemVoList) + ",response=" + JSON.toJSONString(ocrEntity));
                    throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
                }
            }
        }

        // 需要删除的文件数据
        if (CollUtilX.isNotEmpty(delFileIds)) {
            for (FileReq fileReq : delFileIds) {
                fileRemote.del(fileReq);
            }
        }

        return true;
    }

    @Override
    public AgentInvoiceBaseRespDTO invoiceDetailBase(AgentInvoiceBaseReqDTO agentInvoiceBaseReqDTO) {
        LambdaQueryWrapper<AgentInvoiceEntity> queryInvoiceWrapper = new LambdaQueryWrapper<>();
        queryInvoiceWrapper.eq(AgentInvoiceEntity::getInvoiceNum, agentInvoiceBaseReqDTO.getInvoiceNum());
        queryInvoiceWrapper.eq(StrUtilX.isNotEmpty(agentInvoiceBaseReqDTO.getInvoiceCode()), AgentInvoiceEntity::getInvoiceCode, agentInvoiceBaseReqDTO.getInvoiceCode());
        return InvoiceConvert.INSTANCE.agentInvoiceEntityToBaseRespDTO(agentInvoiceMapper.selectOne(queryInvoiceWrapper));
    }

    /**
     * 组装发票信息
     *
     * @param resultObj     发票信息
     * @param merchantCode  商家编码
     * @param currentDate   当前时间
     * @param fullUserName  操作人
     * @param isRollInvoice 是否为卷票
     * @param invoiceMap    本次已识别的发票，判断是否是重复发票用
     * @return 客户发票对象
     */
    public AgentInvoiceEntity assembleInvoice(JSONObject resultObj,
                                              String merchantCode,
                                              Date currentDate,
                                              String fullUserName,
                                              Boolean isRollInvoice,
                                              Map<String, AgentInvoiceEntity> invoiceMap,
                                              Integer sourceType) {
        JSONObject baiduInvoice = resultObj.getJSONObject("words_result");
        AgentInvoiceEntity entity = new AgentInvoiceEntity();
        entity.setInvoiceNum(baiduInvoice.getString("InvoiceNum"));
        entity.setInvoiceCode(baiduInvoice.getString("InvoiceCode"));
        entity.setPurchaserName(baiduInvoice.getString("PurchaserName"));
        entity.setPurchaserRegisterNum(baiduInvoice.getString("PurchaserRegisterNum"));
        entity.setSellerName(baiduInvoice.getString("SellerName"));
        entity.setSellerRegisterNum(baiduInvoice.getString("SellerRegisterNum"));
        String invoiceDate = baiduInvoice.getString("InvoiceDate");
        entity.setInvoiceDate(DateUtilX.stringToDate(invoiceDate, invoiceDate.indexOf("年") > 0 ? "yyyy年MM月dd" : "yyyy-MM-dd"));
        entity.setRemark(baiduInvoice.getString("Remarks"));
        entity.setBillUsedAmt(BigDecimal.ZERO);
        entity.setMerchantCode(merchantCode);
        entity.setInvoiceSourceType(sourceType);
        entity.setCheckCode(baiduInvoice.getString("CheckCode"));
        entity.setCreatedDt(currentDate);
        entity.setCreatedBy(fullUserName);

        //发票内容
        String invoiceContent = (String) BaiduUtils.baiduJsonGetWord("CommodityName", baiduInvoice, false, true);
        entity.setInvoiceContent(invoiceContent);

        //税额合计
        String totalTaxString = baiduInvoice.getString("TotalTax");

        //货物列表税率拼接
        String commodityTaxRate = (String) BaiduUtils.baiduJsonGetWord("CommodityTaxRate", baiduInvoice, false, true);

        if ("免税".equals(commodityTaxRate)) {
            //免税发票  的 税额合计设为0
            entity.setTotalTaxAmt(BigDecimal.ZERO);
        } else if (commodityTaxRate.contains("免税")
                && StringUtils.isBlank(commodityTaxRate.replaceAll("免税", "").replaceAll(",", ""))) {
            //有免税字样，替换掉 免税 和逗号，还不为空说明免税和非免税的开到了一张票上，发票设为待检查
            entity.setInvoiceStatus(InvoiceStatusEnum.PENDING.key);
        } else {
            //不为空并不包含星号才设置税额
            if (totalTaxString != null && !totalTaxString.contains("*")) {
                entity.setTotalTaxAmt(new BigDecimal(totalTaxString));
            }
        }
        //合计金额(未税金额)
        BigDecimal totalAmount = baiduInvoice.getBigDecimal("TotalAmount");
        entity.setTotalAmt(totalAmount);
        //合计税额
        BigDecimal totalTax = entity.getTotalTaxAmt();
        //价税合计
        BigDecimal invoiceAmt = baiduInvoice.getBigDecimal("AmountInFiguers");
        entity.setInvoiceAmt(invoiceAmt);
        entity.setBalance(invoiceAmt);
        if (totalTax == null) {
            entity.setTotalTaxAmt(BigDecimal.ZERO);
            entity.setInvoiceTaxRate(BigDecimal.ZERO);
            totalTax = BigDecimal.ZERO;
        }

        if (entity.getInvoiceStatus() == null) {
            if (invoiceAmt.compareTo(totalTax.add(totalAmount)) != 0) {
                entity.setInvoiceStatus(InvoiceStatusEnum.PENDING.key);
            } else {
                entity.setInvoiceStatus(InvoiceStatusEnum.UNUSED.key);
                // 计算发票总的税率，公式 : 合计税额 /（价税合计-合计税额 ）;  分母为0不计算税率（卷票会是这种情况）
                if (BigDecimal.ZERO.compareTo(entity.getInvoiceAmt().subtract(entity.getTotalTaxAmt())) != 0) {
                    entity.setInvoiceTaxRate(new BigDecimal(100).multiply(entity.getTotalTaxAmt().
                            divide(entity.getInvoiceAmt().subtract(entity.getTotalTaxAmt()), 2, RoundingMode.HALF_EVEN)
                    ));
                }
            }
        }

        //卷票
        if (isRollInvoice) {
            entity.setInvoiceType(InvoiceTypeEnums.ROLL_TYPE.getKey());
            entity.setInvoiceTaxRate(BigDecimal.ZERO);
            entity.setTotalTaxAmt(BigDecimal.ZERO);
            if (entity.getInvoiceAmt() != null) {
                entity.setInvoiceStatus(InvoiceStatusEnum.UNUSED.key);
            }
        } else {
            entity.setInvoiceType(InvoiceTypeEnums.getKeyByValue(baiduInvoice.getString("InvoiceType")));
        }

        //如果为空或未使用或待检查，校验是不是重复发票
        if (entity.getInvoiceStatus() == null || entity.getInvoiceStatus() == InvoiceStatusEnum.UNUSED.getKey() ||
                entity.getInvoiceStatus() == InvoiceStatusEnum.PENDING.getKey()) {
            //验重 如果是多个发票识别，先校验本次识别发票再校验数据库数据
            if (invoiceMap != null && invoiceMap.size() != 0) {
                String invoiceKey = entity.getInvoiceNum() + entity.getInvoiceCode();
                if (invoiceMap.containsKey(invoiceKey)) {
                    entity.setInvoiceStatus(InvoiceStatusEnum.REPETITION.getKey());
                }
            }
            if (entity.getInvoiceStatus() == null || entity.getInvoiceStatus() != InvoiceStatusEnum.REPETITION.getKey()) {
                List<AgentInvoiceEntity> list = agentInvoiceMapper.selectList(new QueryWrapper<AgentInvoiceEntity>()
                        .eq("merchant_code", entity.getMerchantCode())
                        .eq("invoice_num", entity.getInvoiceNum())
                        .eq("invoice_code", entity.getInvoiceCode()));
                if (CollUtilX.isNotEmpty(list)) {
                    entity.setInvoiceStatus(InvoiceStatusEnum.REPETITION.getKey());
                }
            }
        }

        return entity;
    }

    private void baiduInvoiceVerification(AgentInvoiceEntity entity, String baiduToken) {
        if (StringUtils.isEmpty(entity.getInvoiceNum())
                && entity.getInvoiceDate() == null
                && entity.getInvoiceType() == null
                && entity.getTotalTaxAmt() == null) {
            log.error("发票验真不成功entity=" + JSON.toJSONString(entity));
            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
        } else {
            StringBuilder sbStr = new StringBuilder();
            sbStr.append("invoice_code=").append(entity.getInvoiceCode())
                    .append("&invoice_num=").append(entity.getInvoiceNum())
                    .append("&invoice_date=").append(DateUtilX.dateToString(entity.getInvoiceDate(), "yyyyMMdd"));
            if (entity.getInvoiceType() != null) {
                InvoiceTypeEnums invoiceTypeEnum = InvoiceTypeEnums.getEnumByKey(entity.getInvoiceType());
                if (invoiceTypeEnum != null) {
                    sbStr.append("&invoice_type=").append(invoiceTypeEnum.baiDuCode);
                }
            }

            /*
             * 发票金额。增值税专票、电子专票、货运专票、机动车销售发票填写不含税金额；
             * 二手车销售发票填写车价合计；
             * 全电发票（专用发票）、全电发票（普通发票）填写价税合计金额，其他类型发票可为空
             */
            if (null != entity.getInvoiceType() && (InvoiceTypeEnums.A_E_NORMAL.key == entity.getInvoiceType()
                    || InvoiceTypeEnums.A_E_SPECIAL.key == entity.getInvoiceType())) {
                //价税合计金额
                sbStr.append("&total_amount=").append(entity.getInvoiceAmt());
            } else {
                //不含税金额
                sbStr.append("&total_amount=").append(entity.getTotalAmt());
            }

            if (StringUtils.isNotEmpty(entity.getCheckCode()) && entity.getCheckCode().length() >= 6) {
                sbStr.append("&check_code=").append(entity.getCheckCode().substring(entity.getCheckCode().length() - 6));
            } else {
                sbStr.append("&check_code=");
            }

            String validResultJson = null;
            //当前重试次数
            int retry_count = 0;
            //确认拿到百度
            boolean resultFlag = true;

            while (resultFlag) {
                // 去验真
                try {
                    validResultJson = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceVerificationUrl(), baiduToken, sbStr.toString());
                } catch (Exception e) {
                    log.error("调用百度发票验真接口失败", e);
                }
                if (StringUtils.isEmpty(validResultJson)) {
                    continue;
                }
                //返回 QPS 超限，
                if (validResultJson.contains("Open api qps request limit reached")) {
                    retry_count++;
                    if (retry_count > settingsConstant.getBaiduMaxRetryCount()) {
                        log.error("重试" + settingsConstant.getBaiduMaxRetryCount() + "发票验真次失败，param=>" + sbStr.toString() + ",返回结果=>" + validResultJson);
                        break;
                    }
                    if (retry_count > 1) {
                        try {
                            Thread.sleep(1100);
                        } catch (Exception e) {
                            log.error("发票验真异常", e);
                        }
                        log.info("第" + retry_count + "次请求验真,param=> " + sbStr.toString() + ",返回结果=>" + validResultJson);
                    }
                } else {
                    JSONObject baiduResult = JSON.parseObject(validResultJson);

                    if (baiduResult.containsKey("error_code")) {
                        log.error("百度OCR验真失败,失败原因：error_code:" + baiduResult.getString("error_code") + ",error_msg:" + baiduResult.getString("error_msg"));
                        throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                    } else if (!baiduResult.getString("VerifyResult").equals("0001")) {
                        log.error("百度OCR验真失败,失败原因：VerifyResult:" + baiduResult.getString("VerifyResult") + ",VerifyMessage:" + baiduResult.getString("VerifyMessage"));
                        throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                    } else if (baiduResult.getString("VerifyResult").equals("0001")) {
                        //是否作废（冲红）。Y：已作废；H：已冲红；N：未作废
                        String InvalidSign = baiduResult.getString("InvalidSign");
                        if ("N".equals(InvalidSign)) {
                            //发票验真成功
                        } else if ("Y".equals(InvalidSign)) {
                            log.error("验真结果：已作废");
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                        } else if ("H".equals(InvalidSign)) {
                            log.error("验真结果：已冲红");
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                        } else {
                            log.error("验真结果：百度验真未被解析的响应");
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                        }
                    } else {
                        log.error("验真结果：百度验真未被解析的响应");
                        throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                    }
                    resultFlag = false;
                }
            }
        }
    }

    /**
     * author: weihua
     * description: 校验发票添加参数
     */
    private void validateInvoiceAddParam(AgentInvoiceAditReq req, Response<Long> response) {
        if (null == req) {
            log.error("销票添加发票失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceType()) {
            log.error("销票添加发票失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceSourceType()) {
            log.error("销票添加发票失败：请求参数为空,发票来源类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceNum())) {
            log.error("销票添加发票失败：请求参数为空,发票号码");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceDate())) {
            log.error("销票添加发票失败：请求参数为空,发票日期");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserName())) {
            log.error("销票添加发票失败：请求参数为空,购买方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserRegisterNum())) {
            log.error("销票添加发票失败：请求参数为空,购买方纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerName())) {
            log.error("销票添加发票失败：请求参数为空,销售方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerRegisterNum())) {
            log.error("销票添加发票失败：请求参数为空,销售费纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceContent())) {
            log.error("销票添加发票失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceAmt()) {
            log.error("销票添加发票失败：请求参数为空,发票金额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceTaxRate()) {
            log.error("销票添加发票失败：请求参数为空,发票税率");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getTotalTaxAmt()) {
            log.error("销票添加发票失败：请求参数为空,合计税额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceAmt().compareTo(BigDecimal.ZERO) < 0) {
            log.error("销票添加发票失败：发票金额小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(BigDecimal.ZERO) < 0) {
            log.error("销票添加发票失败：税率小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(new BigDecimal(100)) > 0) {
            log.error("销票添加发票失败：税率大于100%！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
    }

    /**
     * author: weihua
     * description: 校验发票编辑参数
     */
    private void validateInvoiceEditParam(AgentInvoiceAditReq req) {
        if (null == req) {
            log.error("销项票发票编辑失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceId()) {
            log.error("销项票发票编辑失败：请求参数发票id为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceType()) {
            log.error("销项票发票编辑失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceNum())) {
            log.error("销项票发票编辑失败：请求参数为空,发票号码");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceDate())) {
            log.error("销项票发票编辑失败：请求参数为空,发票日期");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserName())) {
            log.error("销项票发票编辑失败：请求参数为空,购买方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserRegisterNum())) {
            log.error("销项票发票编辑失败：请求参数为空,购买方纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerName())) {
            log.error("销项票发票编辑失败：请求参数为空,销售方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerRegisterNum())) {
            log.error("销项票发票编辑失败：请求参数为空,销售费纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceContent())) {
            log.error("销项票发票编辑失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceAmt()) {
            log.error("销项票发票编辑失败：请求参数为空,发票金额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceTaxRate()) {
            log.error("销项票发票编辑失败：请求参数为空,发票税率");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getTotalTaxAmt()) {
            log.error("销项票发票编辑失败：请求参数为空,合计税额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceAmt().compareTo(BigDecimal.ZERO) < 0) {
            log.error("销项票发票编辑失败：发票金额小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(BigDecimal.ZERO) < 0) {
            log.error("销项票发票编辑失败：税率小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(new BigDecimal(100)) > 0) {
            log.error("销项票发票编辑失败：税率大于100%！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
    }

    /**
     * 校验同步发票参数
     */
    private void validateInvoiceSyncToTgParam(DdtToTgInvoiceSyncAditReq req) {
        if (null == req) {
            log.error("销项票发票同步发票失败：请求参数为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceType()) {
            log.error("销项票发票同步发票失败：请求参数发票类型为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceNum())) {
            log.error("销项票发票同步发票失败：请求参数发票号码为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserRegisterNum())) {
            log.error("销项票发票同步发票失败：请求参数购买方纳税人识别号为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserName())) {
            log.error("销项票发票同步发票失败：请求参数购买方名称为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerRegisterNum())) {
            log.error("销项票发票同步发票失败：请求参数销售方纳税人识别号为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerName())) {
            log.error("销项票发票同步发票失败：请求参数销售方名称为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceDate())) {
            log.error("销项票发票同步发票失败：请求参数发票日期为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getCommodityName())) {
            log.error("销项票发票同步发票失败：请求参数发票内容为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getAmountInFiguers()) {
            log.error("销项票发票同步发票失败：请求参数发票金额为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getTotaltax()) {
            log.error("销项票发票同步发票失败：请求参数合计税额为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoicetaxrate()) {
            log.error("销项票发票同步发票失败：请求参数发票税率为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getIreveAgentCode())) {
            log.error("销项票发票同步发票失败：请求参数ireve客户编码为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSaasMerchantCode())) {
            log.error("销项票发票同步发票失败：请求参数天宫商家编码为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
    }

    /**
     * 校验可开票发票列表参数
     */
    private void validateInvoiceableInvoiceListParam(AgentInvoiceQueryPageReq req) {
        if (null == req) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceStartDt())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_STARTDATE);
        } else if (StrUtilX.isEmpty(req.getInvoiceEndDt())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ENDDATE);
        }
        Date invoiceStartDate = DateUtilX.stringToDate(req.getInvoiceStartDt());
        Date invoiceEndDate = DateUtilX.stringToDate(req.getInvoiceEndDt());

        if (invoiceStartDate.after(invoiceEndDate)) {
            throw new SysException(ParamErrorEnum.STARTDT_GT_ENDDT_ERROR);
        } else if (DateUtilX.getDay(invoiceStartDate, invoiceEndDate) > 92) {
            log.error("绑定发票查询列表：开票日期跨度大于三个月");
            throw new SysException(ParamErrorEnum.DATE_GTT_THREE_MONTH);
        }
    }

    /**
     * 获取百度token
     * Access token默认有效期为30天
     */
    public String queryBaiDuOrcToken() {
        Map<String, String> map = new HashMap<>();
        //grant_type固定为client_credentials
        map.put("grant_type", "client_credentials");
        map.put("client_id", settingsConstant.getBaiduClientId());
        map.put("client_secret", settingsConstant.getBaiduClientSecret());
        String result = HttpUtilX.get(settingsConstant.getBaiduOrcTokenUrl(), map, null);
        if (StringUtils.isBlank(result)) {
            log.error("获取百度token失败，返回结果为空,请求参数:" + JSON.toJSONString(map));
            throw new SysException(ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorCode, ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorDesc);
        }
        String baiduToken = JSON.parseObject(result).getString("access_token");
        if (StringUtils.isBlank(baiduToken)) {
            log.error("获取百度token失败，返回结果不正确{}", result);
            throw new SysException(ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorCode, ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorDesc);
        }
        return baiduToken;
    }

    /**
     * 百度orc识别
     *
     * @param fileDto    识别文件参数
     * @param baiduToken 百度token
     * @return
     */
    public BaiduOcrResp baiduOcr(FileResp fileDto, String baiduToken) {
        if (StringUtils.isBlank(fileDto.getFileUrl())) {
            log.error("百度orc识别：文件路径为空");
            throw new SysException(ErrorCodeEnum.FILE_PATH_ISEMPTY.errorCode, ErrorCodeEnum.FILE_PATH_ISEMPTY.errorDesc);
        } else if (StringUtils.isBlank(fileDto.getFileFormat())) {
            log.error("百度orc识别：文件类型为空");
            throw new SysException(ErrorCodeEnum.FILE_TYPE_ISEMPTY.errorCode, ErrorCodeEnum.FILE_TYPE_ISEMPTY.errorDesc);
        } else if (StringUtils.isBlank(fileDto.getFileId())) {
            log.error("百度orc识别：文件id为空");
            throw new SysException(ErrorCodeEnum.FILE_ID_ISEMPTY.errorDesc, ErrorCodeEnum.FILE_ID_ISEMPTY.errorDesc);
        }
        //格式校验是否通过
        boolean flag = false;
        for (String type : invoiceFileAllowType) {
            if (fileDto.getFileFormat().equalsIgnoreCase(type)) {
                flag = true;
                break;
            }
        }
        if (!flag) {
            log.error("上传失败,文件的类型是:" + fileDto.getFileFormat() + ",不能上传该类型文件!");
            throw new SysException(ErrorCodeEnum.FILE_TYPE_MISMATCH.errorCode, ErrorCodeEnum.FILE_TYPE_MISMATCH.errorDesc);
        }
        // 参数组装
        StringBuilder paramStr = new StringBuilder();
        try {
            byte[] imgData = InvoiceUtil.readFileByBytes(fileDto.getFileUrl());
            String imageStr = InvoiceUtil.encode(imgData);
            String imgParam = URLEncoder.encode(imageStr, "UTF-8");
            if ("pdf".equals(fileDto.getFileFormat())) {
                paramStr.append("pdf_file=").append(imgParam);
            } else if (!PRODUCTION_BAIDU_CLIENT_ID.equals(settingsConstant.getBaiduClientId())) {
                paramStr.append("image=").append(imgParam);
            } else {
                //生产环境并是图片类型使用url
                paramStr.append("url=").append(fileDto.getFileUrl());
            }
        } catch (Exception e) {
            log.error("文件解析失败", e);
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }

        //进行识别的增值税发票类 normal：可识别增值税普票、专票、电子发票 roll：可识别增值税卷票 默认为 normal
        String result = null;
        //重试
        for (int i = 0; i < settingsConstant.getBaiduMaxRetryCount(); i++) {
            long startTime1 = System.currentTimeMillis();
            try {
                result = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceUrl(), baiduToken, paramStr + "&type=normal");
            } catch (Exception e) {
                log.error("调用百度API识别图片异常", e);
            }
            log.info("OCR非卷票第" + i + 1 + "次识别用时" + (System.currentTimeMillis() - startTime1) + " ms, result=" + result);
            if (StringUtils.isBlank(result) || result.contains("Open api qps request limit reached")) {
                log.info("OCR非卷票第" + i + 1 + "次返回结果不正确，需要重试！result=" + result);
                try {
                    //休眠一秒,再重试
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error("OCR非卷票识别异常", e);
                }
            } else {
                break;
            }
        }
        if (StringUtils.isBlank(result)) {
            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
        }
        // 是否为卷票 false 使用普通发票识别，true 使用券票识别
        boolean isRollInvoice = false;
        JSONObject resultObj = JSON.parseObject(result);
        if (result.contains("failed to match the template")) {
            isRollInvoice = true;
        } else if (resultObj.containsKey("words_result_num") && resultObj.getIntValue("words_result_num") > 0
                && resultObj.getJSONObject("words_result").getBigDecimal("AmountInFiguers") == null) {
            isRollInvoice = true;
        }

        // 为卷票再次调用接口
        if (isRollInvoice) {
            // 重试
            for (int i = 0; i < settingsConstant.getBaiduMaxRetryCount(); i++) {
                long startTime2 = System.currentTimeMillis();
                try {
                    result = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceUrl(), baiduToken, paramStr + "&type=roll");
                } catch (Exception e) {
                    log.error("调用百度API识别图片异常", e);
                }
                log.info("OCR卷票第" + i + 1 + "次识别用时" + (System.currentTimeMillis() - startTime2) + " ms, result=" + result);
                if (StringUtils.isBlank(result) || result.contains("Open api qps request limit reached")) {
                    log.info("OCR卷票第" + i + 1 + "次返回结果不正确，需要重试！result=" + result);
                    try {
                        //休眠一秒,再重试
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("OCR卷票识别异常", e);
                    }
                } else {
                    break;
                }
            }
            if (StringUtils.isBlank(result)) {
                throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
            }
            resultObj = JSON.parseObject(result);
        }
        BaiduOcrResp resp = new BaiduOcrResp();
        resp.setResult(result);
        resp.setResultObj(resultObj);
        resp.setIsRollInvoice(isRollInvoice);

        return resp;
    }

}
