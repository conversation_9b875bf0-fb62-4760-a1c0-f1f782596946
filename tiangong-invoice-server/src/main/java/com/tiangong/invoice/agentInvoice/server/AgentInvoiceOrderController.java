package com.tiangong.invoice.agentInvoice.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.invoice.agentInvoice.service.AgentInvoiceOrderService;
import com.tiangong.invoice.req.InvoiceableAgentInfoQueryReq;
import com.tiangong.invoice.resp.InvoiceableAgentInfoResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:分销商发票订单
 * @date 2023/12/22
 */
@RestController
@RequestMapping("/invoice/agentInvoiceOrder")
public class AgentInvoiceOrderController extends BaseController {

    @Autowired
    private AgentInvoiceOrderService agentInvoiceOrderService;

    /**
     * 分页查询未开票供应商
     */
    @PostMapping("/uninvoicSupplierList")
    public Response<PaginationSupportDTO<InvoiceableAgentInfoResp>> invoiceAbleSupplierInfoPage(@RequestBody InvoiceableAgentInfoQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        PaginationSupportDTO<InvoiceableAgentInfoResp> paginationSupportDTO = agentInvoiceOrderService.invoiceableSupplierInfoPage(req);
        return Response.success(paginationSupportDTO);
    }

}
