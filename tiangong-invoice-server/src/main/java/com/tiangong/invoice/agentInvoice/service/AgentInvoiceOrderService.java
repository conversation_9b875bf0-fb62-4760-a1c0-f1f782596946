package com.tiangong.invoice.agentInvoice.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.invoice.req.InvoiceableAgentInfoQueryReq;
import com.tiangong.invoice.resp.InvoiceableAgentInfoResp;

public interface AgentInvoiceOrderService {

    /**
     * 分页查询未开票供应商
     */
    PaginationSupportDTO<InvoiceableAgentInfoResp> invoiceableSupplierInfoPage(InvoiceableAgentInfoQueryReq req);
}
