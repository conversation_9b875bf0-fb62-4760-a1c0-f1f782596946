package com.tiangong.invoice.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.file.resp.FileResp;
import com.tiangong.invoice.config.SettingsConstant;
import com.tiangong.invoice.resp.BaiduOcrResp;
import com.tiangong.util.HttpUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zhiling
 * @date: 2024/3/6 20:42
 * @description: 百度OCR识别工具类
 */
@Slf4j
public class BaiduUtils {

    //发票可识别文件类型
    private static final String[] invoiceFileAllowType = { "PNG", "JPG", "JPEG", "BMP", "PDF" };

    //百度ORC识别id(生产环境)，用于判断，测试的只能用base64将图片给到百度
    private static String PRODUCTION_BAIDU_CLIENT_ID = "KPdcdshFCEXI7WBPx8nIUoSu";

    public static String queryBaiDuOrcToken(SettingsConstant settingsConstant) {
        Map<String, String> map = new HashMap<>();
        //grant_type固定为client_credentials
        map.put("grant_type", "client_credentials");
        map.put("client_id", settingsConstant.getBaiduClientId());
        map.put("client_secret", settingsConstant.getBaiduClientSecret());
        String result = HttpUtilX.get(settingsConstant.getBaiduOrcTokenUrl(), map,null);
        if (StringUtils.isBlank(result)) {
            log.error("获取百度token失败，返回结果为空,请求参数:"+ JSON.toJSONString(map));
            throw new SysException(ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorCode,ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorDesc);
        }
        log.info("获取百度token返回结果：" + result);
        String baiduToken = JSON.parseObject(result).getString("access_token");
        if (StringUtils.isBlank(baiduToken)) {
            throw new SysException(ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorCode,ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorDesc);
        }
        return baiduToken;
    }

    public static BaiduOcrResp baiduOcr(SettingsConstant settingsConstant,FileResp fileDto, String baiduToken) {
        if (StringUtils.isBlank(fileDto.getFileUrl())) {
            log.error("百度orc识别：文件路径为空");
            throw new SysException(ErrorCodeEnum.FILE_PATH_ISEMPTY.errorCode, ErrorCodeEnum.FILE_PATH_ISEMPTY.errorDesc);
        } else if (StringUtils.isBlank(fileDto.getFileFormat())) {
            log.error("百度orc识别：文件类型为空");
            throw new SysException(ErrorCodeEnum.FILE_TYPE_ISEMPTY.errorCode, ErrorCodeEnum.FILE_TYPE_ISEMPTY.errorDesc);
        } else if (StringUtils.isBlank(fileDto.getFileId())) {
            log.error("百度orc识别：文件id为空");
            throw new SysException(ErrorCodeEnum.FILE_ID_ISEMPTY.errorDesc, ErrorCodeEnum.FILE_ID_ISEMPTY.errorDesc);
        }
        //格式校验是否通过
        boolean flag = false;
        for (String type : invoiceFileAllowType) {
            if (fileDto.getFileFormat().equalsIgnoreCase(type)) {
                flag = true;
                break;
            }
        }
        if(!flag){
            log.error("上传失败,文件的类型是:" + fileDto.getFileFormat() + ",不能上传该类型文件!");
            throw new SysException(ErrorCodeEnum.FILE_TYPE_MISMATCH.errorCode,ErrorCodeEnum.FILE_TYPE_MISMATCH.errorDesc);
        }
        //参数组装
        StringBuilder paramStr = new StringBuilder();
        try {
            byte[] imgData = InvoiceUtil.readFileByBytes(fileDto.getFileUrl());
            String imageStr = InvoiceUtil.encode(imgData);
            String imgParam = URLEncoder.encode(imageStr, "UTF-8");
            if ("pdf".equals(fileDto.getFileFormat())) {
                paramStr.append("pdf_file=").append(imgParam);
            } else if (!PRODUCTION_BAIDU_CLIENT_ID.equals(settingsConstant.getBaiduClientId())) {
                paramStr.append("image=").append(imgParam);
            } else {
                //生产环境并是图片类型使用url
                paramStr.append("url=").append(fileDto.getFileUrl());
            }
        } catch (Exception e) {
            log.error("文件解析失败", e);
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }

        //进行识别的增值税发票类 normal：可识别增值税普票、专票、电子发票 roll：可识别增值税卷票 默认为 normal
        String result = null;
        //重试
        for (int i = 0; i < settingsConstant.getBaiduMaxRetryCount(); i++) {
            long startTime1 = System.currentTimeMillis();
            try {
                result = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceUrl(), baiduToken, paramStr + "&type=normal");
            } catch (Exception e) {
                log.error("调用百度API识别图片异常", e);
            }
            log.info("OCR非卷票第" + i + 1 + "次识别用时" + (System.currentTimeMillis() - startTime1) + " ms, result=" + result);
            if (StringUtils.isBlank(result) || result.contains("Open api qps request limit reached")) {
                log.info("OCR非卷票第" + i + 1 + "次返回结果不正确，需要重试！result=" + result);
                try {
                    //休眠一秒,再重试
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error("调用百度API识别图片异常", e);
                }
            } else {
                break;
            }
        }
        if (StringUtils.isBlank(result)) {
            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode,ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
        }
        // 是否为卷票 false 使用普通发票识别，true 使用券票识别
        boolean isRollInvoice = false;
        JSONObject resultObj = JSON.parseObject(result);
        if (result.contains("failed to match the template")) {
            isRollInvoice = true;
        } else if (resultObj.containsKey("words_result_num") && resultObj.getIntValue("words_result_num") > 0
                && resultObj.getJSONObject("words_result").getBigDecimal("AmountInFiguers") == null) {
            isRollInvoice = true;
        }

        //为卷票再次调用接口
        if (isRollInvoice) {
            //重试
            for (int i = 0; i < settingsConstant.getBaiduMaxRetryCount(); i++) {
                long startTime2 = System.currentTimeMillis();
                try {
                    StringBuffer str = new StringBuffer();
                    str.append(paramStr).append("&type=roll");
                    result = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceUrl(), baiduToken, str.toString());
                } catch (Exception e) {
                    log.error("调用百度API识别图片异常", e);
                }
                log.info("OCR卷票第" + i + 1 + "次识别用时" + (System.currentTimeMillis() - startTime2) + " ms, result=" + result);
                if (StringUtils.isBlank(result) || result.contains("Open api qps request limit reached")) {
                    log.info("OCR卷票第" + i + 1 + "次返回结果不正确，需要重试！result=" + result);
                    try {
                        //休眠一秒,再重试
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("调用百度API识别图片异常", e);
                    }
                } else {
                    break;
                }
            }
            if (StringUtils.isBlank(result)) {
                throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode,ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
            }
            resultObj = JSON.parseObject(result);
        }
        BaiduOcrResp resp = new BaiduOcrResp();
        resp.setResult(result);
        resp.setResultObj(resultObj);
        resp.setIsRollInvoice(isRollInvoice);

        return resp;
    }

    /**
     * 解析百度数据 数据，提取出结果
     *
     * @param  jsonPro
     * @param  baiduResultObj
     * @param  returnIsNum 是否返回数字
     * @param  strRemoveDuplication 返回String 是否去重
     * @return Object 返回类型
     */
    public static Object baiduJsonGetWord(String jsonPro, JSONObject baiduResultObj, boolean returnIsNum, boolean strRemoveDuplication) {
        StringBuilder sb = new StringBuilder();
        List<String> strList = new ArrayList<String>();
        BigDecimal sum = BigDecimal.ZERO;
        if (baiduResultObj.containsKey(jsonPro)) {
            JSONArray array = baiduResultObj.getJSONArray(jsonPro);
            for (int i = 0; i < array.size(); i++) {
                if (returnIsNum) {
                    sum = sum.add(array.getJSONObject(i).getBigDecimal("word"));
                } else {
                    String str = array.getJSONObject(i).getString("word");
                    if (!(strRemoveDuplication && strList.contains(str))) {
                        strList.add(str);
                    }
                }
            }
        }

        for (String s : strList) {
            sb.append(",").append(s);
        }

        if (sb.length() == 0) {
            sb.append(",");
        }
        return returnIsNum ? sum : sb.substring(1);
    }
}
