<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.invoice.agentInvoice.mapper.AgentInvoiceMapper">

    <!-- 可根据自己的需求，是否要使用 -->
<!--    <resultMap id="BaseResultMap" type="com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceEntity">-->
<!--                    <result property="invoiceId" column="invoice_id"/>-->
<!--                    <result property="thirdId" column="third_id"/>-->
<!--                    <result property="invoiceStatus" column="invoice_status"/>-->
<!--                    <result property="invoiceType" column="invoice_type"/>-->
<!--                    <result property="purchaserName" column="purchaser_name"/>-->
<!--                    <result property="purchaserRegisterNum" column="purchaser_register_num"/>-->
<!--                    <result property="invoiceNum" column="invoice_num"/>-->
<!--                    <result property="invoiceCode" column="invoice_code"/>-->
<!--                    <result property="invoiceDate" column="invoice_date"/>-->
<!--                    <result property="invoiceContent" column="invoice_content"/>-->
<!--                    <result property="sellerName" column="seller_name"/>-->
<!--                    <result property="sellerRegisterNum" column="seller_register_num"/>-->
<!--                    <result property="invoiceAmt" column="invoice_amt"/>-->
<!--                    <result property="totalTaxAmt" column="total_tax_amt"/>-->
<!--                    <result property="invoiceTaxRate" column="invoice_tax_rate"/>-->
<!--                    <result property="remark" column="remark"/>-->
<!--                    <result property="url" column="url"/>-->
<!--                    <result property="invoiceSourceType" column="invoice_source_type"/>-->
<!--                    <result property="balance" column="balance"/>-->
<!--                    <result property="revision" column="revision"/>-->
<!--                    <result property="billUsedAmt" column="bill_used_amt"/>-->
<!--                    <result property="checkCode" column="check_code"/>-->
<!--                    <result property="totalAmt" column="total_amt"/>-->
<!--                    <result property="deleted" column="deleted"/>-->
<!--                    <result property="createdBy" column="created_by"/>-->
<!--                    <result property="createdDt" column="created_dt"/>-->
<!--                    <result property="updatedBy" column="updated_by"/>-->
<!--                    <result property="updatedDt" column="updated_dt"/>-->
<!--            </resultMap>-->


    <update id="updateInvoice" parameterType="com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceEntity">
        UPDATE f_agent_invoice
        SET
        <if test="thirdId!=null">
            third_id=#{thirdId},
        </if>
        <if test="invoiceStatus!=null ">
            invoice_status=#{invoiceStatus},
        </if>
        <if test="invoiceType!=null ">
            invoice_type=#{invoiceType},
        </if>
        <if test="purchaserName!=null and purchaserName!=''">
            purchaser_name=#{purchaserName},
        </if>
        <if test="purchaserRegisterNum!=null and purchaserRegisterNum!=''">
            purchaser_register_num=#{purchaserRegisterNum},
        </if>
        <if test="invoiceNum!=null and invoiceNum!=''">
            invoice_num=#{invoiceNum},
        </if>
        <if test="invoiceCode!=null and invoiceCode!=''">
            invoice_code=#{invoiceCode},
        </if>
        <if test="invoiceDate!=null">
            invoice_date=#{invoiceDate},
        </if>
        <if test="invoiceContent!=null and invoiceContent!=''">
            invoice_content=#{invoiceContent},
        </if>
        <if test="sellerName!=null and sellerName!=''">
            seller_name=#{sellerName},
        </if>
        <if test="sellerRegisterNum!=null and sellerRegisterNum!=''">
            seller_register_num=#{sellerRegisterNum},
        </if>
        <if test="invoiceAmt!=null ">
            invoice_amt=#{invoiceAmt},
        </if>
        <if test="totalTaxAmt!=null">
            total_tax_amt=#{totalTaxAmt},
        </if>
        <if test="invoiceTaxRate!=null">
            invoice_tax_rate=#{invoiceTaxRate},
        </if>
        <if test="totalAmt!=null">
            total_amt=#{totalAmt},
        </if>

        <if test="remark!=null">
            remark=#{remark},
        </if>
        <if test="balance!=null">
            balance=#{balance},
        </if>
        <if test="billUsedAmt!=null">
            bill_used_amt=#{billUsedAmt},
        </if>
        <if test="updatedBy!=null and updatedBy!=''">
            updated_by=#{updatedBy},
        </if>
        updated_dt=NOW(),
        revision = (revision + 1)
        WHERE invoice_id=#{invoiceId}
    </update>

    <update id="updateInvoiceInfo">
        UPDATE f_agent_invoice
        SET
        <if test="balance!=null">
            balance = balance + #{balance},
        </if>
        <if test="billUsedAmt!=null">
            bill_used_amt= bill_used_amt - #{billUsedAmt},
        </if>
        <if test="updatedBy!=null and updatedBy!=''">
            updated_by= #{updatedBy},
        </if>
        <if test="updatedDt!=null">
            updated_dt= #{updatedDt},
        </if>
        revision = (revision + 1)
        WHERE invoice_id= #{invoiceId}
    </update>

    <select id="queryInvoiceList" resultType="com.tiangong.invoice.resp.AgentInvoiceResp">
        SELECT
        fsi.invoice_id invoiceId,
        fsi.invoice_type invoiceType,
        fsi.invoice_num invoiceNum,
        fsi.invoice_date invoiceDate,
        fsi.purchaser_name purchaserName,
        fsi.seller_name sellerName,
        fsi.invoice_status invoiceStatus,
        fsi.invoice_amt invoiceAmt,
        fsi.balance Balance,
        fsi.invoice_tax_rate invoiceTaxRate,
        fsi.remark ,
        fsi.url,
        fsi.created_by createdBy,
        fsi.created_dt createdDt,
        fsi.invoice_source_type invoiceSourceType
        FROM f_agent_invoice fsi
        <where>
            fsi.merchant_code = #{merchantCode}
            <if test="invoiceStartDt != null and invoiceStartDt != '' and invoiceEndDt != null and invoiceEndDt != ''">
                AND date_format(fsi.invoice_date, '%Y-%m-%d') BETWEEN #{invoiceStartDt} AND #{invoiceEndDt}
            </if>
            <if test="createdStartDt != null and createdStartDt != '' and createdEndDt != null and createdEndDt != ''">
                AND date_format(fsi.created_dt, '%Y-%m-%d') BETWEEN #{createdStartDt} AND #{createdEndDt}
            </if>
            <if test="invoiceNum != null and invoiceNum != ''">
                AND fsi.invoice_num like concat('%', #{invoiceNum}, '%')
            </if>
            <if test="purchaserName != null and purchaserName != ''">
                AND fsi.purchaser_name like concat('%', #{purchaserName}, '%')
            </if>
            <if test="sellerName != null and sellerName != ''">
                AND fsi.seller_name like concat('%', #{sellerName}, '%')
            </if>
            <if test="invoiceStatus != null ">
                AND fsi.invoice_status = #{invoiceStatus}
            </if>
            <if test="createdBy != null and createdBy != ''">
                AND fsi.created_by like concat('%', #{createdBy}, '%')
            </if>
        </where>
        ORDER BY fsi.created_dt DESC
    </select>

    <select id="queryInvoice" resultType="com.tiangong.invoice.resp.AgentInvoiceResp">
        SELECT
            fsi.invoice_id invoiceId,
            fsi.third_id thirdId,
            fsi.invoice_type invoiceType,
            fsi.invoice_num invoiceNum,
            fsi.invoice_code invoiceCode,
            fsi.invoice_content invoiceContent,
            fsi.invoice_date invoiceDate,
            fsi.purchaser_name purchaserName,
            fsi.purchaser_register_num purchaserRegisterNum,
            fsi.seller_name sellerName,
            fsi.seller_register_num sellerRegisterNum,
            fsi.invoice_status invoiceStatus,
            fsi.invoice_amt invoiceAmt,
            fsi.balance Balance,
            fsi.invoice_tax_rate invoiceTaxRate,
            fsi.total_tax_amt totalTaxAmt,
            fsi.remark ,
            fsi.url,
            fsi.created_by createdBy,
            fsi.created_dt createdDt,
            fsi.revision,
            fsi.bill_used_amt billUsedAmt,
            fsi.check_code checkCode,
            fsi.total_amt totalAmount,
            fsi.merchant_code merchantCode,
            fsi.updated_by updatedBy,
            fsi.updated_dt updatedDt,
            fsi.invoice_source_type invoiceSourceType
        FROM f_agent_invoice as fsi
        WHERE
            fsi.merchant_code = #{merchantCode}
          AND fsi.invoice_id = #{invoiceId}
    </select>

    <select id="queryAgentInvoiceBillInvoiceItemList"
            resultType="com.tiangong.invoice.resp.AgentInvoiceResp">
        SELECT
        fsi.invoice_id invoiceId,
        fsi.invoice_type invoiceType,
        fsi.invoice_code invoiceCode,
        fsi.purchaser_name purchaserName,
        fsi.purchaser_register_num purchaserRegisterNum,
        fsi.seller_register_num sellerRegisterNum,
        fsi.total_amt totalAmount,
        fsi.invoice_tax_rate invoiceTaxRate,
        fsi.total_tax_amt totalTaxAmt,
        fsi.invoice_content invoiceContent,
        fsi.remark remark,
        fsi.invoice_num invoiceNum,
        fsi.invoice_date invoiceDate,
        fsi.purchaser_name purchaserName,
        fsi.seller_name sellerName,
        fsi.invoice_amt invoiceAmt,
        fsi.balance balance,
        fsibr.bill_used_amt currentAmt,
        fsibr.created_dt bindDt,
        fsibr.id,
        fsi.invoice_source_type invoiceSourceType
        FROM
        f_agent_invoice fsi
        LEFT JOIN f_agent_invoice_bill_relation fsibr ON fsibr.invoice_id = fsi.invoice_id
        <where>
            <if test="invoiceBillId != null">
                fsibr.invoice_bill_id = #{invoiceBillId}
            </if>
        </where>

    </select>

    <select id="queryAgentInvoiceableInvoiceList" resultType="com.tiangong.invoice.resp.AgentInvoiceResp">
        SELECT distinct
        fsi.invoice_id invoiceId,
        fsi.invoice_num invoiceNum,
        fsi.invoice_date invoiceDate,
        fsi.purchaser_name purchaserName,
        fsi.seller_name sellerName,
        fsi.invoice_status invoiceStatus,
        fsi.invoice_amt invoiceAmt,
        (fsi.invoice_amt - IFNULL(fsi.bill_used_amt,0)) unusedAmt,
        (fsi.invoice_amt - IFNULL(fsi.bill_used_amt,0)) currentAmt,
        fsi.invoice_source_type invoiceSourceType
        FROM f_agent_invoice fsi
        LEFT JOIN f_agent_invoice_bill_relation fsibr ON fsibr.invoice_id = fsi.invoice_id
        <where>
            fsi.merchant_code = #{merchantCode}
            <!-- 该发票不在当前票单 或者 未绑定过票单-->
            AND (fsibr.invoice_bill_id != #{invoiceBillId} OR fsibr.id IS NULL)
            AND fsi.invoice_amt > fsi.bill_used_amt
            <if test="invoiceStartDt != null and invoiceStartDt != ''">
                AND fsi.invoice_date >= #{invoiceStartDt}
            </if>
            <if test="invoiceEndDt != null and invoiceEndDt != ''">
                <![CDATA[ AND fsi.invoice_date <= #{invoiceEndDt} ]]>
            </if>
            <if test="invoiceNum != null and invoiceNum != ''">
                AND fsi.invoice_num like concat('%', #{invoiceNum}, '%')
            </if>
            <if test="purchaserName != null and purchaserName != ''">
                AND fsi.purchaser_name like concat('%', #{purchaserName}, '%')
            </if>
            <if test="sellerName != null and sellerName != ''">
                AND fsi.seller_name like concat('%', #{sellerName}, '%')
            </if>
            <choose>
                <when test="invoiceStatus != null">
                    AND fsi.invoice_status = #{invoiceStatus}
                </when>
                <otherwise>
                    AND fsi.invoice_status in (0,2)
                </otherwise>
            </choose>
            <if test="invoiceNumList != null and invoiceNumList.size > 0 ">
                AND fsi.invoice_num NOT IN
                <foreach collection="invoiceNumList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by fsi.invoice_date desc
    </select>
</mapper>