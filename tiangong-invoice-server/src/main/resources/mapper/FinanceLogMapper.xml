<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.invoice.supplierInvoice.mapper.FinanceLogMapper">

    <!-- 可根据自己的需求，是否要使用 -->
<!--    <resultMap id="BaseResultMap" type="com.tiangong.invoice.tiangong-invoice-server.domain.entity.FinanceLogEntity">-->
<!--                    <result property="id" column="id"/>-->
<!--                    <result property="logType" column="log_type"/>-->
<!--                    <result property="objId" column="obj_id"/>-->
<!--                    <result property="objStatus" column="obj_status"/>-->
<!--                    <result property="content" column="content"/>-->
<!--                    <result property="deleted" column="deleted"/>-->
<!--                    <result property="createdBy" column="created_by"/>-->
<!--                    <result property="createdDt" column="created_dt"/>-->
<!--                    <result property="updatedBy" column="updated_by"/>-->
<!--                    <result property="updatedDt" column="updated_dt"/>-->
<!--            </resultMap>-->


    <select id="queryLogList" parameterType="com.tiangong.invoice.supplierInvoice.domain.entity.FinanceLogEntity" resultType="com.tiangong.invoice.resp.FinanceLogResp">
        SELECT
        ffl.id,
        ffl.content,
        ffl.created_by,
        ffl.created_dt
        FROM f_finance_log ffl
        WHERE ffl.log_type = #{logType}
        AND ffl.obj_id = #{objId}
    </select>
</mapper>