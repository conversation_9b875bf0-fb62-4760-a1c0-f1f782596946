<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.invoice.supplierInvoice.mapper.InvoiceOcrMapper">

    <!-- 可根据自己的需求，是否要使用 -->
<!--    <resultMap id="BaseResultMap" type="com.tiangong.invoice.tiangong-invoice-server.domain.entity.InvoiceOcrEntity">-->
<!--                    <result property="id" column="id"/>-->
<!--                    <result property="ocrIdCard" column="ocr_id_card"/>-->
<!--                    <result property="appKey" column="app_key"/>-->
<!--                    <result property="appBussType" column="app_buss_type"/>-->
<!--                    <result property="oldFileName" column="old_file_name"/>-->
<!--                    <result property="invoiceNum" column="invoice_num"/>-->
<!--                    <result property="invoiceCode" column="invoice_code"/>-->
<!--                    <result property="invoiceDate" column="invoice_date"/>-->
<!--                    <result property="purchaserName" column="purchaser_name"/>-->
<!--                    <result property="sellerName" column="seller_name"/>-->
<!--                    <result property="url" column="url"/>-->
<!--                    <result property="ocrStatus" column="ocr_status"/>-->
<!--                    <result property="invoiceOcrResult" column="invoice_ocr_result"/>-->
<!--                    <result property="invoiceOcrFmt" column="invoice_ocr_fmt"/>-->
<!--                    <result property="deleted" column="deleted"/>-->
<!--                    <result property="createdBy" column="created_by"/>-->
<!--                    <result property="createdDt" column="created_dt"/>-->
<!--                    <result property="updatedBy" column="updated_by"/>-->
<!--                    <result property="updatedDt" column="updated_dt"/>-->
<!--            </resultMap>-->


</mapper>