package com.tiangong.settle.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * 自助结算酒店配置表
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:59
 */
@Data
public class SettleHotelConfigResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long hid;
    /**
     * 商家编码 ,天宫商家编码
     */
    private String merchantCode;
    /**
     * 供应商编码 天宫供应商编码
     */
    private String supplierCode;
    /**
     * 供应商编码 天宫供应商名称
     */
    private String supplierName;
    /**
     * 酒店id 通过酒店名称检索到酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 订单通商家编码 订单通商家编码  ddt_merchant_code
     */
    private String ddtMerchantCode;
    /**
     * 订单通客户编码 订单通客户编码
     */
    private String ddtCustomerCode;

    /**
     * 订单通客户名称
     */
    private String ddtCustomerName;

    /**
     * 结算时间类型 [1:下单后 2:入住当日之后 3:离店当日之后 4:离店周结 5:离店半月结 6离店月结]
     */
    private Integer settleTimeType;
    /**
     * 是否审核入住明细 [0:否 1:是]
     */
    private Integer isCheckDetail;
    /**
     * 已收款待开票额度(自助结算金额上限) 自助结算金额上限
     */
    private BigDecimal invoiceLimit;
    /**
     * 是否可用 [0:否 1:是]
     */
    private Integer available;
    /**
     * 酒店收款帐号 订单通收款账号，可为空,如果不为空,收款的时候必须匹配上，否则收款失败
     */
    private String accountNum;
    /**
     * 酒店收款账户 订单通收款账户，可为空,如果不为空,收款的时候必须匹配上，否则收款失败
     */
    private String registerName;
    /**
     * 公付订单提醒邮箱 公付邮箱提醒
     */
    private String pubPaymentOrderEmailUrl;
    /**
     * 是否同步订单联系人信息到ireve [0:不同步 1:同步]
     */
    private Integer isSync;
    /**
     * 是否立即到账 [0:不是 1:是]
     */
    private Integer isRealTime;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDt;


    /**
     * 付款主体id 银行账号id值
     */
    private Long payerAccountId;

    /**
     * 付款类型 8网商银行
     */
    private Integer passageType;


    /**
     * 开票主体类型
     * 0:银行绑定发票主体  1:自选发票主体
     * 备注，当类型为0的时候，下面的数据为空。
     *      当类型为1的时候，下面属性才有数据。
     */
    private Integer invoiceSubType;

    /**
     * 开票主体id
     */
    private Integer invoiceSubId;

    /**
     * 发票类型
     * 详见：InvoiceTypeEnums枚举 1:普通发票  2:电子发票
     */
    private Integer invoiceType;

    /**
     * 发票税点
     */
    private BigDecimal invoiceRatio;

}