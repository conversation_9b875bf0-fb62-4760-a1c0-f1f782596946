package com.tiangong.settle.remote;

import com.tiangong.common.Response;
import com.tiangong.settle.req.SettleSupplyResultReq;
import com.tiangong.settle.req.TgHotelConfigReq;
import com.tiangong.settle.req.TgWorkOrderReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 自助结算内部fegin接口调用入口
 * <AUTHOR>
 */
@FeignClient(value = "tiangong-settle-server")
public interface SettleServerRemote {

    /**
     * 天宫供货单确认/取消
     * @param req 请求参数
     * {
     * 	"supplierCode": "TS10000702",   //供应商编码
     * 	"hotelId": 517675,              //酒店id
     * 	"orderId": 91,                  //订单id
     * 	"supplyOrderId": 566084,        //供货单id
     * 	"confirmNo": "168",             //确认号
     * 	"supplyResult": 1               //确认结果： 1:确认供货单（是）  2:取消供货单（否）
     * }
     * @return 返回处理结果
     */
    @PostMapping("/settle/settleTask/writeSettleTaskToRedis")
    Response<Object> writeSettleTaskToRedis(@RequestBody SettleSupplyResultReq req);

    /**
     * 自助结算任务状态处理
     * {
     *  "taskCode": ST100001,   //任务编码
     * 	"flag": true,  //处理结果
     * 	"msg": 517675  //异常日志
     * }
     */
    @PostMapping("/settle/external/processTgJobOrderStatus")
    Response<Object> processTgJobOrderStatus(@RequestBody TgWorkOrderReq req);

    /**
     * 查询是否为自助结算配置
     * @param req 请求参数
     * @return 返回处理结果
     */
    @PostMapping("/settle/settleConfig/querySettleHotelConfigIsExists")
    Response<Boolean> querySettleHotelConfigIsExists(@RequestBody TgHotelConfigReq req);

}
