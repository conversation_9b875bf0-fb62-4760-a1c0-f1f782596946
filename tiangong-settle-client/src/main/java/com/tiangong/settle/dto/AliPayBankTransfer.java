package com.tiangong.settle.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 网商银行付款
 * <AUTHOR>
 */

@Data
public class AliPayBankTransfer {

    /**
     * 业务类型：1-单笔跨行转账，2-批量转账，不同渠道需要转换
     */
    private Integer bizType;

    /**
     * 业务单号
     */
    private String businessCode;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 付款账号
     */
    private String payAccount;

    /**
     * 付款开户名
     */
    private String payersName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 收款账户
     */
    private String receAccount;

    /**
     * 收款开户名
     */
    private String payeesName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收款开户行名称
     */
    private String receBankName;

    /**
     * 支行行号
     */
    private String receBankNo;

    /**
     * 支付回调地址
     */
    private String payResultNotif;
}
