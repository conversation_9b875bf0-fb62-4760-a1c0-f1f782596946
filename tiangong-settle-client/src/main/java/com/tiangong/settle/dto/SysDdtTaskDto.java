package com.tiangong.settle.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 同步结算单到新ireve（创建结算单、取消结算单）
 */
public class SysDdtTaskDto implements Serializable {

    private Long id;

    /**
     * 业务号(订单编码或账单编码)
     */
    private String businessCode;


    /**
     * 任务类型(1、创建结算单, 2、取消订单，3、工单处理，4、支付失败
     */
    private Integer taskType;

    /**
     * 完成类型(0 未开始,1 已完成, 2 失败)
     */
    private Integer resultType;

    /**
     * 请求参数
     */
    private String requestMsg;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date modifyDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getResultType() {
        return resultType;
    }

    public void setResultType(Integer resultType) {
        this.resultType = resultType;
    }

    public String getRequestMsg() {
        return requestMsg;
    }

    public void setRequestMsg(String requestMsg) {
        this.requestMsg = requestMsg;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }
}
