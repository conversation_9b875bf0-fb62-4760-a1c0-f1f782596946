package com.tiangong.settle.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 【订单通】统一返回实体类
 * <AUTHOR>
 */
@Data
public class DdtResultVo<T> implements Serializable {


    /**
     * 结果(1：成功 0：失败)
     */
    protected Integer result;

    /**
     * 错误编码
     */
    protected String errorNo;

    /**
     * 失败原因
     */
    protected String failReason;

    /**
     * 返回对象
     */
    protected T obj;

}