package com.tiangong.settle.req;


import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * 自助结算酒店配置附加项表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
@Data
public class SettleHotelConfigAttachReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 自助结算配置id 外健id
     */
    private Long hid;
    /**
     * 付款主体id 银行账号id值
     */
    private Long payerAccountId;
    /**
     * 付款类型 8网商银行
     */
    private Integer passageType;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}