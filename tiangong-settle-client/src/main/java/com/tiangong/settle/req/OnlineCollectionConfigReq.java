package com.tiangong.settle.req;


import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * 延时付款配置表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 15:38:33
 */
@Data
public class OnlineCollectionConfigReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 限时付款配置id
     */
    private Long configId;
    /**
     * 商家编码
     */
    private String merchantCode;
    /**
     * 类型 ['账单','下单日','入住日','离店日']
     */
    private Integer settleType;
    /**
     * 开始时间
     */
    private String startDt;
    /**
     * 结束时间
     */
    private String endDt;
    /**
     * 其他时间
     */
    private Integer otherTime;
    /**
     * 范围内时间
     */
    private Integer withInDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}