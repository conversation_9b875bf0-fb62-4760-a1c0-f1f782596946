package com.tiangong.settle.enums;

/**
 * 完成类型
 * <AUTHOR>
 * 公付邮件发送状态 [0:无需发送 1:需要发送 2:发送成功 3:发送失败 4:首次失败 5:二次发送失败]
 */
public enum SendStatusEnum {
    SEND_STATUS_ENUM_0(0, "无需发送"),
    SEND_STATUS_ENUM_1(1, "需要发送"),
    SEND_STATUS_ENUM_2(2, "发送成功"),
    SEND_STATUS_ENUM_3(3, "发送失败"),
    SEND_STATUS_ENUM_4(4, "首次失败"),
    SEND_STATUS_ENUM_5(5, "二次发送失败");


    public final int key;

    public final String value;

    private SendStatusEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        SendStatusEnum[] var2 = values();
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            SendStatusEnum supplyItemStateEnum = var2[var4];
            if (supplyItemStateEnum.value.equals(value)) {
                key = supplyItemStateEnum.key;
                break;
            }
        }

        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        SendStatusEnum[] var2 = values();
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            SendStatusEnum supplyItemStateEnum = var2[var4];
            if (supplyItemStateEnum.key == key) {
                value = supplyItemStateEnum.value;
                break;
            }
        }

        return value;
    }

    public static SendStatusEnum getEnumByKey(int key) {
        SendStatusEnum supplyItemStateEnum = null;
        SendStatusEnum[] var2 = values();
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            SendStatusEnum supplyItemState = var2[var4];
            if (supplyItemState.key == key) {
                supplyItemStateEnum = supplyItemState;
                break;
            }
        }

        return supplyItemStateEnum;
    }
}
