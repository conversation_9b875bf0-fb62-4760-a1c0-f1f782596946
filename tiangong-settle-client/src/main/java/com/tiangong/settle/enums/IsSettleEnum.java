package com.tiangong.settle.enums;

import java.io.Serializable;

/**
 * 标记自助结算枚举
 */
public enum IsSettleEnum implements Serializable {

    SETTLE_DEL(0,"删除自助结算"),
    SETTLE_ADD(1,"标记自助结算");

    public int key;
    public String value;
    private IsSettleEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for(IsSettleEnum isSettleEnum : IsSettleEnum.values()) {
            if(isSettleEnum.value == value) {
                key = isSettleEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for(IsSettleEnum isSettleEnum : IsSettleEnum.values()) {
            if(isSettleEnum.key == key) {
                value = isSettleEnum.value;
                break;
            }
        }
        return value;
    }

    public static IsSettleEnum getEnumByKey(int key){
        IsSettleEnum isSettleEnum1 = null;
        for(IsSettleEnum isSettleEnum : isSettleEnum1.values()) {
            if(isSettleEnum.key == key) {
                isSettleEnum1 = isSettleEnum;
                break;
            }
        }
        return isSettleEnum1;
    }
}
