package com.tiangong.settle.enums;

import java.io.Serializable;

/**
 * 是否填写入住明细枚举
 * 0:否 1:是
 */
public enum CheckDetailEnum implements Serializable {

    CHECKDETAIL_NO(0,"否"),
    CHECKDETAIL_YES(1,"是");

    public int key;
    public String value;

    private CheckDetailEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for(CheckDetailEnum settleDateTypeEnum : CheckDetailEnum.values()) {
            if(settleDateTypeEnum.value == value) {
                key = settleDateTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for(CheckDetailEnum checkDetailEnum : CheckDetailEnum.values()) {
            if(checkDetailEnum.key == key) {
                value = checkDetailEnum.value;
                break;
            }
        }
        return value;
    }

    public static CheckDetailEnum getEnumByKey(int key){
        CheckDetailEnum checkDetailEnum = null;
        for(CheckDetailEnum checkDetailEnum1 : checkDetailEnum.values()) {
            if(checkDetailEnum1.key == key) {
                checkDetailEnum = checkDetailEnum1;
                break;
            }
        }
        return checkDetailEnum;
    }
}
