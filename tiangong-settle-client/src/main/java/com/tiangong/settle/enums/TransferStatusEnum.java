package com.tiangong.settle.enums;

import java.io.Serializable;

/**
 * 转账任务状态枚举值
 *  [0:收(付)款中 1:收(付)款成功 2:收(付)款失败 3:等待收(付)款]
 */
public enum TransferStatusEnum implements Serializable {

    TRANSFER_STATUS_0(0,"收(付)款中"),

    TRANSFER_STATUS_1(1,"收(付)款成功"),

    TRANSFER_STATUS_2(2,"收(付)款失败"),

    TRANSFER_STATUS_3(3,"等待收(付)款");

    public int key;
    public String value;

    private TransferStatusEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        for(TransferStatusEnum settleDateTypeEnum : TransferStatusEnum.values()) {
            if(settleDateTypeEnum.value == value) {
                key = settleDateTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for(TransferStatusEnum checkDetailEnum : TransferStatusEnum.values()) {
            if(checkDetailEnum.key == key) {
                value = checkDetailEnum.value;
                break;
            }
        }
        return value;
    }

    public static TransferStatusEnum getEnumByKey(int key){
        TransferStatusEnum checkDetailEnum = null;
        for(TransferStatusEnum checkDetailEnum1 : checkDetailEnum.values()) {
            if(checkDetailEnum1.key == key) {
                checkDetailEnum = checkDetailEnum1;
                break;
            }
        }
        return checkDetailEnum;
    }
}
