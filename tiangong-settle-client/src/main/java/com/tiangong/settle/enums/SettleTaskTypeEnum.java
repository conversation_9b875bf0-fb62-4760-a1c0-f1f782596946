package com.tiangong.settle.enums;

/**
 * 结算单任务类型枚举
 * 0: 下单到订单通 并且 创建结算单
 * 1: 取消订单通订单
 * <AUTHOR>
 */
public enum SettleTaskTypeEnum {
    CONFIRMED(0, "加力天宫供货单结果【是】(下结算单到订单通) "),
    CANCELLED(1, "加力天宫供货单结果【否】(取消订单通订单)");

    public final int key;

    public final String value;

    private SettleTaskTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static int getKeyByValue(String value) {
        int key = 0;
        SettleTaskTypeEnum[] var2 = values();
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            SettleTaskTypeEnum supplyItemStateEnum = var2[var4];
            if (supplyItemStateEnum.value.equals(value)) {
                key = supplyItemStateEnum.key;
                break;
            }
        }

        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        SettleTaskTypeEnum[] var2 = values();
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            SettleTaskTypeEnum supplyItemStateEnum = var2[var4];
            if (supplyItemStateEnum.key == key) {
                value = supplyItemStateEnum.value;
                break;
            }
        }

        return value;
    }

    public static SettleTaskTypeEnum getEnumByKey(int key) {
        SettleTaskTypeEnum supplyItemStateEnum = null;
        SettleTaskTypeEnum[] var2 = values();
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            SettleTaskTypeEnum supplyItemState = var2[var4];
            if (supplyItemState.key == key) {
                supplyItemStateEnum = supplyItemState;
                break;
            }
        }

        return supplyItemStateEnum;
    }
}
