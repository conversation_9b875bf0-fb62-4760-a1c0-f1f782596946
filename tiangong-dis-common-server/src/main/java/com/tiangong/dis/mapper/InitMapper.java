package com.tiangong.dis.mapper;

import com.tiangong.dis.dto.*;
import com.tiangong.product.dto.ProductDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InitMapper {

    /**
     * 查询条款信息
     */
    List<ProductRestrictDTO> queryRestrictList();

    /**
     * 查询底价和房态
     */
    List<ProductBasePriceAndRoomStatusDTO> queryBasePriceAndRoomStatusList();

    /**
     * 查询销售状态
     */
    List<ProductSaleStatusDTO> querySaleStatusList();

    /**
     * 查询销售价格
     */
    List<ProductSaleIncreaseDTO> querySalePriceList();

    /**
     * 查询产品与商家关系
     */
    List<ProductCompanyRelationDTO> queryProductCompanyRelationList();

    /**
     * 查询汇率列表
     */
    List<CurrencyExchangeRateDTO> queryExchangeRateList();

    /**
     * 查询汇率列表
     */
    List<CurrencyCoinDTO> queryExchangeCoinList();

    /**
     * 查询商家渠道关系
     */
    List<CompanyChannelDTO> queryCompanyChannelRelation();

    /**
     * 初始化产品信息
     */
    List<ProductDTO> queryProductInfo();

    /**
     * 查询产品的总共信息
     */
    Integer queryProductInfoCount();
}
