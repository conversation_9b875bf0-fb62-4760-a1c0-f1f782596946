package com.tiangong.dis.convert;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.mapstruct.Named;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderStatusMappingEnum {
    /**
     * 新建
     */
    NEW(-1, 1, "新建"),
    IN_HAND(0, 2, "处理中"),
    CONFIRMED(1, 3, "已确认"),
    CANCELED(2, 4, "已取消"),
    DONE(3, 5, "已完成");

    private final Integer type;
    private final Integer targetType;
    private final String desc;

    @Named("getOrderStatus")
    public static Integer getOrderStatus(Integer type) {
        return Arrays.stream(OrderStatusMappingEnum.values())
                .filter(e -> Objects.equals(e.getType(), type))
                .map(OrderStatusMappingEnum::getTargetType)
                .findFirst()
                .orElse(null);
    }

}
