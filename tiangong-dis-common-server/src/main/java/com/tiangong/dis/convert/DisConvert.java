package com.tiangong.dis.convert;

import com.tiangong.dto.order.request.OrderCheckDetails;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;
import com.tiangong.product.dto.GuaranteeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DisConvert {
    DisConvert INSTANCE = Mappers.getMapper(DisConvert.class);

    /**
     * 将订单检查信息DTO列表转换为订单检查详情列表
     *
     * @param orderCheckInfoDTOList 订单检查信息DTO列表
     * @return 返回转换后的订单检查详情列表
     */
    List<OrderCheckDetails> orderCheckDetailsConvert(List<OrderCheckInfoDTO> orderCheckInfoDTOList);

    /**
     * 将担保DTO列表转换为分销担保DTO列表
     *
     * @param guarantees 担保DTO列表
     * @return 返回转换后的分销担保DTO列表
     */
    List<com.tiangong.dis.dto.GuaranteeDTO> guaranteeDtoListConvert(List<GuaranteeDTO> guarantees);
}
