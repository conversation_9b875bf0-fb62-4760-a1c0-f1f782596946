package com.tiangong.dis.service;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.QueryOrderInvoiceResponse;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.*;
import com.tiangong.dto.order.response.*;
import com.tiangong.order.remote.request.QueryOrderInvoiceDTO;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.request.SupplyOrderIdDTO;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;

/**
 * <AUTHOR>
 */
public interface HotelOrderService {

    /**
     * 试预订(可订检查)接口
     */
    ResponseResult<CheckBookingResponse> checkBooking(CheckBookingRequest request);

    /**
     * 创建订单
     */
    ResponseResult<CreateOrderResponse> createOrder(CreateOrderRequest request);

    /**
     * 取消订单
     */
    ResponseResult<CancelOrderResponse> cancelOrder(CancelOrderRequest request);

    /**
     * 订单详情查询-api
     */
    ResponseResult<OrderDetailResponse> queryAPIOrderDetail(OrderDetailRequest request);

    /**
     * 获取订单列表
     */
    Response<PaginationSupportDTO<OrderSimpleDTO>> queryOrderList(QueryOrderListDTO request);

    /**
     * 订单详情查询-b2b
     */
    OrderDTO queryB2BOrderDetail(OrderDetailRequest request);

    /**
     * 订单支付接口
     */
    ResponseResult<PayOrderResponse> payOrder(PayOrderRequest request);

    /**
     * 订单退款通知接口
     */
    ResponseResult<OrderRefundNoticeResponse> refundNoticeOrder(OrderRefundNoticeRequest request);

    /**
     * 信用额度查询接口
     */
    ResponseResult<CreditBalanceResponse> queryCreditBalance(QueryCreditBalanceRequest request);

    /**
     * 订单开票接口
     */
    ResponseResult<CreateInvoiceResponse> createInvoice(CreateInvoiceRequest request);

    /**
     * 根据订单号查询发票详情
     */
    ResponseResult<QueryOrderInvoiceResponse> queryOrderInvoice(QueryOrderInvoiceDTO request);

    /**
     * 订单入住详细情况查询接口
     */
    ResponseResult<OrderCheckDetailInfoResponse> queryOrderCheckDetailInfo(OrderCheckInfoRequest request);

    /**
     * 查询订单状态统计--B2B
     */
    Response<OrderStatisticsDTOB2BResponse> queryOrderStatisticsB2B(QueryOrderStatisticsDTOB2BRequest request);

    /**
     * 退房申请接口
     */
    ResponseResult<CheckoutApplyInfoResponse> checkoutApply(CheckoutApplyInfoRequest request);

    /**
     * 退房详情查询接口
     */
    ResponseResult<CheckoutDetailInfoResponse> queryCheckoutDetail(CheckoutDetailInfoRequest request);

    /**
     * 查询供货单详情
     */
    Response<SupplyOrderDTO> querySupplyOrderInfo(SupplyOrderIdDTO request);


}
