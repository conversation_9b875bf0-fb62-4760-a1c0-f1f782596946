package com.tiangong.dis.service;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.QueryOrderInvoiceResponse;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.*;
import com.tiangong.dto.order.response.*;
import com.tiangong.order.remote.request.QueryOrderInvoiceDTO;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.request.SupplyOrderIdDTO;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;

/**
 * <AUTHOR>
 */
public interface HotelOrderService {

    /**
     * 试预订(可订检查)接口
     *
     * @param request 检查预订请求，包含预订检查参数
     * @return 返回检查预订响应结果
     */
    ResponseResult<CheckBookingResponse> checkBooking(CheckBookingRequest request);

    /**
     * 创建订单
     *
     * @param request 创建订单请求，包含订单创建信息
     * @return 返回创建订单响应结果
     */
    ResponseResult<CreateOrderResponse> createOrder(CreateOrderRequest request);

    /**
     * 取消订单
     *
     * @param request 取消订单请求，包含订单取消信息
     * @return 返回取消订单响应结果
     */
    ResponseResult<CancelOrderResponse> cancelOrder(CancelOrderRequest request);

    /**
     * 订单详情查询-api
     *
     * @param request 订单详情请求，包含API订单查询参数
     * @return 返回订单详情响应结果
     */
    ResponseResult<OrderDetailResponse> queryAPIOrderDetail(OrderDetailRequest request);

    /**
     * 获取订单列表
     *
     * @param request 查询订单列表DTO，包含查询条件
     * @return 返回分页的订单简单DTO数据响应结果
     */
    Response<PaginationSupportDTO<OrderSimpleDTO>> queryOrderList(QueryOrderListDTO request);

    /**
     * 订单详情查询-b2b
     *
     * @param request 订单详情请求，包含B2B订单查询参数
     * @return 返回订单DTO数据
     */
    OrderDTO queryB2BOrderDetail(OrderDetailRequest request);

    /**
     * 订单支付接口
     *
     * @param request 支付订单请求，包含支付信息
     * @return 返回支付订单响应结果
     */
    ResponseResult<PayOrderResponse> payOrder(PayOrderRequest request);

    /**
     * 订单退款通知接口
     *
     * @param request 订单退款通知请求，包含退款通知信息
     * @return 返回订单退款通知响应结果
     */
    ResponseResult<OrderRefundNoticeResponse> refundNoticeOrder(OrderRefundNoticeRequest request);

    /**
     * 信用额度查询接口
     *
     * @param request 查询信用余额请求，包含查询参数
     * @return 返回信用余额响应结果
     */
    ResponseResult<CreditBalanceResponse> queryCreditBalance(QueryCreditBalanceRequest request);

    /**
     * 订单开票接口
     *
     * @param request 创建发票请求，包含开票信息
     * @return 返回创建发票响应结果
     */
    ResponseResult<CreateInvoiceResponse> createInvoice(CreateInvoiceRequest request);

    /**
     * 根据订单号查询发票详情
     *
     * @param request 查询订单发票DTO，包含订单号等查询条件
     * @return 返回查询订单发票响应结果
     */
    ResponseResult<QueryOrderInvoiceResponse> queryOrderInvoice(QueryOrderInvoiceDTO request);

    /**
     * 订单入住详细情况查询接口
     *
     * @param request 订单入住信息请求，包含入住详情查询参数
     * @return 返回订单入住详细信息响应结果
     */
    ResponseResult<OrderCheckDetailInfoResponse> queryOrderCheckDetailInfo(OrderCheckInfoRequest request);

    /**
     * 查询订单状态统计--B2B
     *
     * @param request 查询订单统计DTO B2B请求，包含统计查询条件
     * @return 返回订单统计DTO B2B响应数据
     */
    Response<OrderStatisticsDTOB2BResponse> queryOrderStatisticsB2B(QueryOrderStatisticsDTOB2BRequest request);

    /**
     * 退房申请接口
     *
     * @param request 退房申请信息请求，包含退房申请参数
     * @return 返回退房申请信息响应结果
     */
    ResponseResult<CheckoutApplyInfoResponse> checkoutApply(CheckoutApplyInfoRequest request);

    /**
     * 退房详情查询接口
     *
     * @param request 退房详情信息请求，包含退房详情查询参数
     * @return 返回退房详情信息响应结果
     */
    ResponseResult<CheckoutDetailInfoResponse> queryCheckoutDetail(CheckoutDetailInfoRequest request);

    /**
     * 查询供货单详情
     *
     * @param request 供货单ID DTO，包含供货单标识
     * @return 返回供货单DTO数据的响应结果
     */
    Response<SupplyOrderDTO> querySupplyOrderInfo(SupplyOrderIdDTO request);


}
