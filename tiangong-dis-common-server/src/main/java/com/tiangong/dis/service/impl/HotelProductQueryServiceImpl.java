package com.tiangong.dis.service.impl;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.ProductRemote;
import com.tiangong.dis.service.HotelProductQueryService;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.hotel.remote.HotelRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HotelProductQueryServiceImpl implements HotelProductQueryService {

    @Autowired
    private ProductRemote productRemote;

    @Autowired
    private HotelRemote hotelRemote;

    /**
     * 酒店每日起价查询
     */
    @Override
    public ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request) {
        try {
            return productRemote.queryHotelLowestPrice(request);
        } catch (Exception e) {
            log.error("product-queryHotelLowestPrice error!", e);
        }
        return null;
    }

    /**
     * 酒店列表查询接口
     */
    @Override
    public ResponseResult<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request) {
        try {
            return productRemote.queryDisHotelList(request);
        } catch (Exception e) {
            log.error("product-queryHotelList error!", e);
        }
        return null;
    }

    /**
     * 酒店每日起价查询-B2B
     */
    @Override
    public ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(HotelLowestPriceRequest request) {
        try {
            return productRemote.hotelLowestPrice(request);
        } catch (Exception e) {
            log.error("product-queryHotelLowestPrice error!", e);
        }
        return null;
    }

    /**
     * 酒店实时产品查询
     */
    @Override
    public ResponseResult<ProductDetailResponse> queryProductDetail(ProductDetailRequest request) {
        try {
            //判断该酒店是否加入黑名单
            AddHotelAvailableReq req = new AddHotelAvailableReq();
            req.setAgentCode(request.getAgentCode());
            req.setHotelId(request.getHotelId());
            req.setAvailableType(0);
            Response<HotelAvailableDTO> hotelAvailableResp = hotelRemote.queryHotelAvailable(req);
            if (hotelAvailableResp.getResult() == ResultCodeEnum.SUCCESS.code) {
                HotelAvailableDTO model = hotelAvailableResp.getModel();
                if (model != null) {
                    return new ResponseResult<>(DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.no, DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.description);
                }
            }
            return productRemote.queryHotelProductDetail(request);
        } catch (Exception e) {
            log.error("product-queryHotelProductDetail error!", e);
        }
        return null;
    }

}
