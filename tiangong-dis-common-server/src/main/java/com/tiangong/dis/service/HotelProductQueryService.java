package com.tiangong.dis.service;

import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;

/**
 * <AUTHOR>
 */
public interface HotelProductQueryService {

    /**
     * 酒店每日起价查询-dHub
     */
    ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店Id列表查询接口-dHub
     */
    ResponseResult<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request);

    /**
     * 酒店每日起价查询-B2B
     */
    ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店实时产品查询
     */
    ResponseResult<ProductDetailResponse> queryProductDetail(ProductDetailRequest request);
}
