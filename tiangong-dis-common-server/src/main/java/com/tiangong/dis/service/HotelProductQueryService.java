package com.tiangong.dis.service;

import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;

/**
 * <AUTHOR>
 */
public interface HotelProductQueryService {

    /**
     * 酒店每日起价查询-dHub
     *
     * @param request 酒店最低价格请求，包含酒店起价查询参数
     * @return 返回酒店最低价格响应结果
     */
    ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店Id列表查询接口-dHub
     *
     * @param request 酒店ID列表请求，包含酒店ID查询参数
     * @return 返回酒店ID列表响应结果
     */
    ResponseResult<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request);

    /**
     * 酒店每日起价查询-B2B
     *
     * @param request 酒店最低价格请求，包含B2B酒店起价查询参数
     * @return 返回酒店最低价格响应结果
     */
    ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(HotelLowestPriceRequest request);

    /**
     * 酒店实时产品查询
     *
     * @param request 产品详情请求，包含酒店产品查询参数
     * @return 返回产品详情响应结果
     */
    ResponseResult<ProductDetailResponse> queryProductDetail(ProductDetailRequest request);
}
