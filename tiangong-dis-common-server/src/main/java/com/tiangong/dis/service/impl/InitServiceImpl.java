package com.tiangong.dis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.tiangong.dis.convert.DisConvert;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.mapper.InitMapper;
import com.tiangong.dis.service.InitService;
import com.tiangong.finance.RateRedisDTO;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.dto.ProductDayQuotationDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class InitServiceImpl implements InitService {

    @Autowired
    private InitMapper initMapper;

    /**
     * 初始化条款
     */
    @Override
    public void initRestrict(List<ProductDTO> productDTOS, List<String> increment) {
        if (CollUtilX.isEmpty(productDTOS)) {
            return;
        }

        for (ProductDTO productDTO : productDTOS) {
            if (productDTO.getReservationTermStatus() != null && productDTO.getReservationTermStatus() == 0) {
                RedisTemplateX.hDelete(RedisKey.productRestrictKey, StrUtilX.concat(String.valueOf(productDTO.getProductId()), "_", productDTO.getSaleDate()));
            }

            String redisKey;
            if (StrUtilX.isEmpty(productDTO.getSaleDate())) {
                redisKey = String.valueOf(productDTO.getProductId());
            } else {
                redisKey = StrUtilX.concat(String.valueOf(productDTO.getProductId()), "_", productDTO.getSaleDate());
            }

            ProductRestrictDTO productRestrictDTO = null;
            if (StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, redisKey), ProductRestrictDTO.class) != null) {
                productRestrictDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, String.valueOf(productDTO.getProductId())), ProductRestrictDTO.class);
            }

            if (productRestrictDTO == null) {
                productRestrictDTO = new ProductRestrictDTO();
            }
            productRestrictDTO.setProductId(productDTO.getProductId());

            if (productDTO.getCancellationAdvanceDays() != null) {
                productRestrictDTO.setCancellationAdvanceDays(productDTO.getCancellationAdvanceDays());
            }

            if (StrUtilX.isNotEmpty(productDTO.getCancellationDeductionTerm())) {
                productRestrictDTO.setCancellationDeductionTerm(productDTO.getCancellationDeductionTerm());
            }

            if (StrUtilX.isNotEmpty(productDTO.getCancellationDueTime())) {
                productRestrictDTO.setCancellationDueTime(productDTO.getCancellationDueTime());
            }

            if (productDTO.getCancellationType() != null) {
                productRestrictDTO.setCancellationType(productDTO.getCancellationType());
            }

            if (productDTO.getReservationAdvanceDays() != null) {
                productRestrictDTO.setReservationAdvanceDays(productDTO.getReservationAdvanceDays());
            }

            if (StrUtilX.isNotEmpty(productDTO.getReservationDueTime())) {
                productRestrictDTO.setReservationDueTime(productDTO.getReservationDueTime());
            }

            if (productDTO.getReservationLimitNights() != null) {
                productRestrictDTO.setReservationLimitNights(productDTO.getReservationLimitNights());
            }

            if (productDTO.getReservationLimitRooms() != null) {
                productRestrictDTO.setReservationLimitRooms(productDTO.getReservationLimitRooms());
            }

            if (productDTO.getComparisonType() != null) {
                productRestrictDTO.setComparisonType(productDTO.getComparisonType());
            }

            productRestrictDTO.setCancelPenaltiesType(productDTO.getCancelPenaltiesType());
            productRestrictDTO.setCancelPenaltiesValue(productDTO.getCancelPenaltiesValue());
            if (CollUtil.isNotEmpty(productDTO.getGuarantees())){
                productRestrictDTO.setGuarantees(DisConvert.INSTANCE.guaranteeDtoListConvert(productDTO.getGuarantees()));
            }

            if (StrUtilX.isNotEmpty(productDTO.getSaleDate())) {
                productRestrictDTO.setSaleDate(productDTO.getSaleDate());
            }

            RedisTemplateX.hPut(RedisKey.productRestrictKey, redisKey, JSON.toJSONString(productRestrictDTO));
        }
    }

    /**
     * 初始化底价和房态
     */
    @Override
    public void initBasePriceAndRoomStatus(List<ProductDayQuotationDTO> productDayQuotationDTOList, List<String> incrementList) {
        if (CollUtilX.isEmpty(productDayQuotationDTOList)) {
            return;
        }

        Map<String, String> redisMap = new HashMap<>();
        for (ProductDayQuotationDTO productDayQuotationDTO : productDayQuotationDTOList) {
            String redisKey = StrUtilX.concat(String.valueOf(productDayQuotationDTO.getProductId()), "_", productDayQuotationDTO.getSaleDate());
            ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO = null;
            if (StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productBasePriceAndRoomStatusKey, redisKey), ProductBasePriceAndRoomStatusDTO.class) != null) {
                productBasePriceAndRoomStatusDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productBasePriceAndRoomStatusKey, redisKey), ProductBasePriceAndRoomStatusDTO.class);
            }

            if (productBasePriceAndRoomStatusDTO == null) {
                productBasePriceAndRoomStatusDTO = new ProductBasePriceAndRoomStatusDTO();
            }
            productBasePriceAndRoomStatusDTO.setProductId(productDayQuotationDTO.getProductId());
            productBasePriceAndRoomStatusDTO.setSaleDate(productDayQuotationDTO.getSaleDate());
            productBasePriceAndRoomStatusDTO.setRedisKey(StrUtilX.concat(String.valueOf(productDayQuotationDTO.getProductId()), "_", productDayQuotationDTO.getSaleDate()));

            //底价
            if (productDayQuotationDTO.getBasePriceAdjustmentType() != null && productDayQuotationDTO.getModifiedBasePrice() != null) {
                //设置房费、税费、底价到缓存 这里底价不能在重新计算了，因为之前已经计算好了
                productBasePriceAndRoomStatusDTO.setBasePrice(productDayQuotationDTO.getModifiedBasePrice());
                productBasePriceAndRoomStatusDTO.setRoomPrice(productDayQuotationDTO.getRoomPrice());
                productBasePriceAndRoomStatusDTO.setSalesTax(productDayQuotationDTO.getSalesTax());
                productBasePriceAndRoomStatusDTO.setTax(productDayQuotationDTO.getTax());
                productBasePriceAndRoomStatusDTO.setPayAtHotelFee(productDayQuotationDTO.getPayAtHotelFee());
                productBasePriceAndRoomStatusDTO.setOtherTaxFee(productDayQuotationDTO.getOtherTaxFee());
            }

            //售罄
            if (productDayQuotationDTO.getOverDraftStatus() != null) {
                productBasePriceAndRoomStatusDTO.setOverDraftStatus(productDayQuotationDTO.getOverDraftStatus());
            }

            //房态
            if (productDayQuotationDTO.getRoomStatus() != null) {
                productBasePriceAndRoomStatusDTO.setRoomStatus(productDayQuotationDTO.getRoomStatus());
            }

            if (productDayQuotationDTO.getQuotaAdjustmentType() != null && productDayQuotationDTO.getModifiedQuota() != null) {
                if (productDayQuotationDTO.getQuotaAdjustmentType() == 2) {
                    if (productDayQuotationDTO.getType() == null || productDayQuotationDTO.getType() != 1) {
                        productBasePriceAndRoomStatusDTO.setQuota(productDayQuotationDTO.getModifiedQuota());
                    }
                    productBasePriceAndRoomStatusDTO.setRemainingQuota(productDayQuotationDTO.getModifiedQuota());
                } else if (productDayQuotationDTO.getQuotaAdjustmentType() == 1) {
                    if (productDayQuotationDTO.getType() == null || productDayQuotationDTO.getType() != 1) {
                        productBasePriceAndRoomStatusDTO.setQuota(productBasePriceAndRoomStatusDTO.getQuota() == null ? 0 :
                                (Math.max(productBasePriceAndRoomStatusDTO.getQuota() + productDayQuotationDTO.getModifiedQuota(), 0)));
                    }
                    productBasePriceAndRoomStatusDTO.setRemainingQuota(productBasePriceAndRoomStatusDTO.getRemainingQuota() == null ? 0 :
                            (Math.max(productBasePriceAndRoomStatusDTO.getRemainingQuota() + productDayQuotationDTO.getModifiedQuota(), 0)));
                } else if (productDayQuotationDTO.getQuotaAdjustmentType() == 0) {
                    if (productDayQuotationDTO.getType() == null || productDayQuotationDTO.getType() != 1) {
                        productBasePriceAndRoomStatusDTO.setQuota(productBasePriceAndRoomStatusDTO.getQuota() == null ?
                                productDayQuotationDTO.getModifiedQuota() : (productBasePriceAndRoomStatusDTO.getQuota() + productDayQuotationDTO.getModifiedQuota()));
                    }
                    productBasePriceAndRoomStatusDTO.setRemainingQuota(productBasePriceAndRoomStatusDTO.getRemainingQuota() == null ?
                            productDayQuotationDTO.getModifiedQuota() : (productBasePriceAndRoomStatusDTO.getRemainingQuota() + productDayQuotationDTO.getModifiedQuota()));
                }
            }
            redisMap.put(redisKey, JSON.toJSONString(productBasePriceAndRoomStatusDTO));
        }
        RedisTemplateX.hPutAll(RedisKey.productBasePriceAndRoomStatusKey, redisMap);
    }


    /**
     * 初始化销售价格
     */
    @Override
    public void initSalePrice(List<ProductSaleIncreaseDTO> productSaleIncreaseList, List<String> incrementList) {
        if (CollUtilX.isEmpty(productSaleIncreaseList)) {
            return;
        }

        Map<String, String> redisMap = new HashMap<>();
        for (ProductSaleIncreaseDTO productSaleIncreaseDTO : productSaleIncreaseList) {
            String redisKey = StrUtilX.concat(productSaleIncreaseDTO.getCompanyCode(), "_", String.valueOf(productSaleIncreaseDTO.getProductId()), "_", productSaleIncreaseDTO.getSaleDate());
            ProductSaleIncreaseDTO productSaleIncrease = null;
            if (StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSalePriceKey, redisKey), ProductSaleIncreaseDTO.class) != null) {
                productSaleIncrease = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSalePriceKey, redisKey), ProductSaleIncreaseDTO.class);
            }

            if (productSaleIncrease == null) {
                productSaleIncrease = new ProductSaleIncreaseDTO();
            }
            productSaleIncrease.setProductId(productSaleIncreaseDTO.getProductId());
            productSaleIncrease.setSaleDate(productSaleIncreaseDTO.getSaleDate());
            productSaleIncrease.setCompanyCode(productSaleIncreaseDTO.getCompanyCode());
            productSaleIncrease.setRedisKey(redisKey);

            if (productSaleIncreaseDTO.getB2bAdjustmentType() != null) {
                productSaleIncrease.setB2bAdjustmentType(productSaleIncreaseDTO.getB2bAdjustmentType());
            }

            if (productSaleIncreaseDTO.getB2bModifiedAmt() != null) {
                productSaleIncrease.setB2bModifiedAmt(productSaleIncreaseDTO.getB2bModifiedAmt());
            }

            if(productSaleIncreaseDTO.getB2bMiniAddRadiation()!= null){
                productSaleIncrease.setB2bMiniAddRadiation(productSaleIncreaseDTO.getB2bMiniAddRadiation());
            }

            redisMap.put(redisKey, JSON.toJSONString(productSaleIncrease));
        }
        RedisTemplateX.hPutAll(RedisKey.productSalePriceKey, redisMap);
    }

    /**
     * 初始化上下架信息
     */
    @Override
    public void initSaleStatus(List<ProductSaleStatusDTO> productSaleStatusPoList, List<String> incrementList) {
        if (CollUtilX.isEmpty(productSaleStatusPoList)) {
            Integer count = initMapper.queryProductInfoCount();
            int loop = count / 10000 + (count % 10000 == 0 ? 0 : 1);
            log.info("saleStatus count:{},query count:{}", count, loop);
            for (int i = 1; i <= loop; i++) {
                PageHelper.startPage(i, 10000);
                List<ProductSaleStatusDTO> saleStatusList = initMapper.querySaleStatusList();
                Map<String, String> saleStatusMap = saleStatusList.stream().collect(Collectors.toMap(ProductSaleStatusDTO::getRedisKey, JSON::toJSONString, (s1, s2) -> s2));
                RedisTemplateX.hPutAll(RedisKey.productSaleStatusKey, saleStatusMap);
            }
            return;
        }

        Map<String, String> redisMap = new HashMap<>();
        for (ProductSaleStatusDTO productSaleStatusDTO : productSaleStatusPoList) {
            String redisKey = StrUtilX.concat(productSaleStatusDTO.getCompanyCode(), "_", String.valueOf(productSaleStatusDTO.getProductId()));
            ProductSaleStatusDTO productSaleStatus = null;
            if (StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSaleStatusKey, redisKey), ProductSaleStatusDTO.class) != null) {
                productSaleStatus = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSaleStatusKey, redisKey), ProductSaleStatusDTO.class);
            }

            if (productSaleStatus == null) {
                productSaleStatus = new ProductSaleStatusDTO();
            }
            productSaleStatus.setActive(1);
            productSaleStatus.setProductId(productSaleStatusDTO.getProductId());
            productSaleStatus.setCompanyCode(productSaleStatusDTO.getCompanyCode());
            productSaleStatus.setRedisKey(redisKey);

            if (productSaleStatusDTO.getB2bSaleStatus() != null) {
                productSaleStatus.setB2bSaleStatus(productSaleStatusDTO.getB2bSaleStatus());
            }

            redisMap.put(redisKey, JSON.toJSONString(productSaleStatus));
        }
        RedisTemplateX.hPutAll(RedisKey.productSaleStatusKey, redisMap);
    }

    /**
     * 初始化上下架
     * 重新初始化一份上下架信息，用于redis切换数据
     */
    @Override
    public void initSaleStatusForReplace(List<ProductSaleStatusDTO> productSaleStatusPoList, List<String> increment) {
        if (CollUtilX.isEmpty(productSaleStatusPoList)) {
            Integer count = initMapper.queryProductInfoCount();
            int loop = count / 10000 + (count % 10000 == 0 ? 0 : 1);
            log.info("saleStatusForReplace count:{},query count:{}", count, loop);
            for (int i = 1; i <= loop; i++) {
                PageHelper.startPage(i, 10000);
                List<ProductSaleStatusDTO> saleStatusList = initMapper.querySaleStatusList();
                Map<String, String> saleStatusMap = saleStatusList.stream().collect(Collectors.toMap(ProductSaleStatusDTO::getRedisKey, JSON::toJSONString, (s1, s2) -> s2));
                RedisTemplateX.hPutAll(RedisKey.productSaleStatusKey + "ForReplace", saleStatusMap);
            }
        }
    }

    /**
     * 初始化产品商家关系表
     */
    @Override
    public void initProductCompanyRelation() {
        List<ProductCompanyRelationDTO> productCompanyRelationList = initMapper.queryProductCompanyRelationList();
        Map<String, String> productCompanyMap = productCompanyRelationList.stream().collect(Collectors.toMap(ProductCompanyRelationDTO::getProductId, JSON::toJSONString, (s1, s2) -> s2));
        handlerModifyData(productCompanyMap, RedisKey.productCompanyRelationKey, ProductCompanyRelationDTO.class);
    }

    /**
     * 初始化汇率信息
     */
    @Override
    @Deprecated
    public void initExchangeRate(CurrencyExchangeRateDTO currencyExchangeRateDTO) {
        //汇率的key值组合：商户编码_原币种_目标币种
        if (currencyExchangeRateDTO == null || StringUtils.isEmpty(currencyExchangeRateDTO.getOriginalCurrency())) {
            List<CurrencyExchangeRateDTO> exchangeRateList = initMapper.queryExchangeRateList();
            Map<String, String> exchangeRateMap = exchangeRateList.stream().collect(Collectors.toMap(i -> i.getCompanyCode() + "_" + i.getOriginalCurrency() + "_" + i.getTargetCurrency(), JSON::toJSONString, (s1, s2) -> s2));
            handlerModifyData(exchangeRateMap, RedisKey.exchangeRateKey, CurrencyExchangeRateDTO.class);
            RedisTemplateX.convertAndSend(RedisKey.REFRESH_RATE_INFO, "");
            return;
        }

        String currency = currencyExchangeRateDTO.getCompanyCode() + "_" + currencyExchangeRateDTO.getOriginalCurrency() + "_" + currencyExchangeRateDTO.getTargetCurrency();
        CurrencyExchangeRateDTO currencyExchangeRate = null;
        if (StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.exchangeRateKey, currency), CurrencyExchangeRateDTO.class) != null) {
            currencyExchangeRate = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.exchangeRateKey, currency), CurrencyExchangeRateDTO.class);
        }

        if (currencyExchangeRate == null) {
            currencyExchangeRate = new CurrencyExchangeRateDTO();
        }
        currencyExchangeRate.setOriginalCurrency(currencyExchangeRateDTO.getOriginalCurrency());
        currencyExchangeRate.setTargetCurrency(currencyExchangeRateDTO.getTargetCurrency());
        currencyExchangeRate.setRate(currencyExchangeRateDTO.getRate());
        RedisTemplateX.hPut(RedisKey.exchangeRateKey, currency, JSON.toJSONString(currencyExchangeRate));

        RateRedisDTO dto = new RateRedisDTO();
        dto.setKey(currency);
        dto.setValue(currencyExchangeRate.getRate());
        RedisTemplateX.convertAndSend(RedisKey.REFRESH_RATE_INFO, JSONUtil.toJsonStr(dto));
    }

    @Override
    public void initProductInfo(List<ProductDTO> productDTOList, List<String> increment) {
        if (CollUtilX.isEmpty(productDTOList)) {
            Integer count = initMapper.queryProductInfoCount();
            int loop = count / 10000 + (count % 10000 == 0 ? 0 : 1);
            for (int i = 1; i <= loop; i++) {
                PageHelper.startPage(i, 10000);
                List<ProductDTO> productDTOS = initMapper.queryProductInfo();
                Map<String, String> productMap = productDTOS.stream().collect(Collectors.toMap(j -> String.valueOf(j.getProductId()), JSON::toJSONString));
                RedisTemplateX.hPutAll(RedisKey.productInfoKey, productMap);
            }
        } else {
            for (ProductDTO productDTO : productDTOList) {
                if (productDTO.getIsDel() != null && productDTO.getIsDel() == 1) {
                    RedisTemplateX.hDelete(RedisKey.productInfoKey, String.valueOf(productDTO.getProductId()));
                } else {
                    ProductDTO product = new ProductDTO();
                    if (StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productInfoKey, String.valueOf(productDTO.getProductId())), ProductDTO.class) != null) {
                        product = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productInfoKey, String.valueOf(productDTO.getProductId())), ProductDTO.class);
                    }

                    if (product == null) {
                        product = new ProductDTO();
                    }
                    product.setProductId(productDTO.getProductId());

                    if (StrUtilX.isNotEmpty(productDTO.getProductName())) {
                        product.setProductName(productDTO.getProductName());
                    }

                    if (productDTO.getHotelId() != null) {
                        product.setHotelId(productDTO.getHotelId());
                    }

                    if (productDTO.getRoomId() != null) {
                        product.setRoomId(productDTO.getRoomId());
                    }

                    if (StrUtilX.isNotEmpty(productDTO.getSupplierCode())) {
                        product.setSupplierCode(productDTO.getSupplierCode());
                    }

                    if (productDTO.getBreakfastQty() != null) {
                        product.setBreakfastQty(productDTO.getBreakfastQty());
                    }

                    if (productDTO.getPurchaseType() != null) {
                        product.setPurchaseType(productDTO.getPurchaseType());
                    }

                    if (productDTO.getSupplyType() != null) {
                        product.setSupplyType(productDTO.getSupplyType());
                    }

                    if (productDTO.getActive() != null) {
                        product.setActive(productDTO.getActive());
                    }

                    if (productDTO.getCurrency() != null) {
                        product.setCurrency(productDTO.getCurrency());
                    }

                    if (productDTO.getBedTypes() != null) {
                        product.setBedTypes(productDTO.getBedTypes());
                    }

                    if (productDTO.getMaxAdultQty() != null) {
                        product.setMaxAdultQty(productDTO.getMaxAdultQty());
                    }

                    if (productDTO.getPriceType() != null) {
                        product.setPriceType(productDTO.getPriceType());
                    }

                    if (productDTO.getTaxRuleConfigId() != null) {
                        product.setTaxRuleConfigId(productDTO.getTaxRuleConfigId());
                    }

                    if (productDTO.getPayMethod() != null) {
                        product.setPayMethod(productDTO.getPayMethod());
                    }

                    if (productDTO.getCommissionType() != null) {
                        product.setCommissionType(productDTO.getCommissionType());
                    }

                    if (productDTO.getCommissionValue() != null) {
                        product.setCommissionValue(productDTO.getCommissionValue());
                    }

                    RedisTemplateX.hPut(RedisKey.productInfoKey, String.valueOf(productDTO.getProductId()), JSON.toJSONString(product));
                }
            }
        }
    }

    /**
     * 处理修改日期数据
     */
    private <T> void handlerModifyData(Map<String, String> databaseMap, String redisKey, Class<T> clazz) {
        //redis里面的数据
        Map<Object, Object> redisMap = RedisTemplateX.hGetAll(redisKey);
        List<String> redisDel = new ArrayList<String>();
        if (redisMap.size() > 0) {
            for (Object key : redisMap.keySet()) {
                String mapKey = (String) key;
                if (databaseMap.containsKey(mapKey)) {
                    // 比较相同的就不进行本次redis操作
                    if (JSON.parseObject(redisMap.get(mapKey).toString(), clazz).equals(JSON.parseObject(databaseMap.get(mapKey), clazz))) {
                        databaseMap.remove(mapKey);
                    }
                } else {
                    redisDel.add(mapKey);
                }
            }
        }

        //需要删除的数据
        if (CollUtilX.isNotEmpty(redisDel)) {
            RedisTemplateX.hDelete(redisKey, redisDel.toArray());
        }

        //新增和修改的数据
        if (databaseMap != null && databaseMap.size() > 0) {
            RedisTemplateX.hPutAll(redisKey, databaseMap);
        }
    }
}
