package com.tiangong.dis.service;

import com.tiangong.dis.dto.*;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.dto.ProductDayQuotationDTO;

import java.util.List;

/**
 * redis初始化
 * <AUTHOR>
 */
public interface InitService {

    /**
     * 初始化条款
     */
    void initRestrict(List<ProductDTO> productDTO, List<String> increment);

    /**
     * 初始化底价和房态
     */
    void initBasePriceAndRoomStatus(List<ProductDayQuotationDTO> productDayQuotationDTOList, List<String> incrementList);

    /**
     * 初始化售价
     */
    void initSalePrice(List<ProductSaleIncreaseDTO> productSaleIncreaseList, List<String> increment);

    /**
     * 初始化上下架
     */
    void initSaleStatus(List<ProductSaleStatusDTO> productSaleStatusList, List<String> increment);

    /**
     * 初始化上下架
     * 重新初始化一份上下架信息，用于redis切换数据
     */
    void initSaleStatusForReplace(List<ProductSaleStatusDTO> productSaleStatusList, List<String> increment);

    /**
     * 初始化产品商家关系表
     */
    void initProductCompanyRelation();

    /**
     * 初始化汇率信息
     */
    void initExchangeRate(CurrencyExchangeRateDTO currencyExchangeRateDTO);

    /**
     * 清理产品信息
     */
    void initProductInfo(List<ProductDTO> productDTO, List<String> increment);
}
