package com.tiangong.dis.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.dto.common.BankBalanceChangeReq;
import com.tiangong.dto.common.BankListReq;
import com.tiangong.organization.remote.BankRemote;
import com.tiangong.organization.remote.dto.BankSupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/14 20:38
 */
@RestController
@RequestMapping("/discommon/bank")
public class BankServer {

    @Autowired
    private BankRemote bankRemote;

    /**
     * 银行卡余额变动
     */
    @PostMapping("bankBalanceChange")
    public Response<Object> bankBalanceChange(@RequestBody BankBalanceChangeReq req){
        return bankRemote.bankBalanceChange(req);
    }

    /**
     * 银行卡列表
     */
    @PostMapping("queryBankList")
    @AnonymousAccess
    public Response<List<BankSupplierDTO>> queryBankList(@RequestBody BankListReq req){
        return bankRemote.queryBankList(req);
    }
}
