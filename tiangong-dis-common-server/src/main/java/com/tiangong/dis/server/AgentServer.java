package com.tiangong.dis.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.fuzzyquery.dto.FuzzyAgentDTO;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.remote.FuzzyQueryRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/discommon/agent")
public class AgentServer {

    @Autowired
    private FuzzyQueryRemote fuzzyQueryRemote;

    /**
     * 模糊查询分销商
     */
    @PostMapping("queryAgent")
    @SlsLog(level = "info", name = "查询", message = "queryAgent", topic = "/discommon/agent/queryAgent", source = "tiangong-dis-common-server")
    public Response<PaginationSupportDTO<FuzzyAgentDTO>> queryAgent(@RequestBody FuzzyQueryDTO req){
        return fuzzyQueryRemote.queryAgent(req);
    }
}