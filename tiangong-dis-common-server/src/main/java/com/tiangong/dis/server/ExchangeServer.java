package com.tiangong.dis.server;

import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.exchange.ExchangeRateDTO;
import com.tiangong.dto.exchange.QueryExchangeReq;
import com.tiangong.finance.remote.ExchangeRateRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023/12/2 16:40
 */
@RestController
@RequestMapping("/discommon/exchange")
public class ExchangeServer {

    @Autowired
    private ExchangeRateRemote exchangeRateRemote;

    /**
     * 查询汇率
     */
    @PostMapping("/queryExchangeRate")
    public Response<PaginationSupportDTO<ExchangeRateDTO>> queryExchangeRate(@RequestBody QueryExchangeReq request) {
        return exchangeRateRemote.queryExchangeRate(request);
    }
}
