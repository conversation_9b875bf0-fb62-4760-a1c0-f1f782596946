package com.tiangong.dis.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dis.service.HotelProductQueryService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.product.request.HotelIdListRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.HotelIdListResponse;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.dto.product.response.ProductDetailResponse;
import com.tiangong.hotel.remote.HotelRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品输出接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/discommon/product")
public class HotelProductServer {

    @Autowired
    private HotelProductQueryService hotelProductQueryService;

    @Autowired
    private HotelRemote hotelRemote;

    /**
     * 酒店每日起价查询—Dhub
     */
    @PostMapping("/queryHotelLowestPrice")
    @AnonymousAccess
    public ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(@RequestBody HotelLowestPriceRequest request) {
        return hotelProductQueryService.queryHotelLowestPrice(request);
    }

    /**
     * 酒店Id列表查询接口
     */
    @PostMapping("/queryHotelIdList")
    @AnonymousAccess
    public ResponseResult<HotelIdListResponse> queryHotelList(@RequestBody HotelIdListRequest request) {
        return hotelProductQueryService.queryHotelIdList(request);
    }

    /**
     * 酒店实时产品查询
     */
    @PostMapping("/queryProductDetail")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryProductDetail", topic = "/discommon/product/queryProductDetail", source = "tiangong-dis-common-server")
    public ResponseResult<ProductDetailResponse> queryProductDetail(@RequestBody ProductDetailRequest request) {
        return hotelProductQueryService.queryProductDetail(request);
    }

    /**
     * 酒店实时起价查询接口_B2B
     */
    @RequestMapping("/hotelLowestPrice")
    ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(@RequestBody HotelLowestPriceRequest request) {
        return hotelProductQueryService.hotelLowestPrice(request);
    }

    /**
     * 查询酒店详情-按需输出
     */
    @PostMapping("/queryHotelInfo")
    @AnonymousAccess
    public Response<HotelInfoCollectionDTO> queryHotelInfo(@RequestBody HotelInfoCollectionReq req) {
        return hotelRemote.queryHotelInfo(req);
    }

    /**
     * 查询酒店详情-按需输出
     */
    @PostMapping("/queryHotelInfoList")
    @AnonymousAccess
    public Response<String> queryHotelInfoList(@RequestBody HotelInfoReq req) {
        return hotelRemote.queryHotelInfoStrList(req);
    }

    /**
     * 获取酒店标签信息
     */
    @PostMapping("/queryHotelLabelInfo")
    @AnonymousAccess
    public Response<Set<String>> queryHotelLabelInfo(@RequestBody HotelInfoReq req) {
        return hotelRemote.queryHotelLabelInfo(req);
    }

    /**
     * 获取目的地信息
     */
    @PostMapping("/getAddress")
    @AnonymousAccess
    public Response<List<AddressRespDTO>> getAddress(@RequestBody Map<String, String> paramMap) {
        return hotelRemote.getAddress(paramMap);
    }

    /**
     * 获取酒店过滤器
     */
    @PostMapping("getHotelSearch")
    @AnonymousAccess
    public Response<HotelSearchResp> getHotelSearch(@RequestBody HotelSearchReq hotelSearchReq){
        return hotelRemote.getHotelSearch(hotelSearchReq);
    }

    /**
     * 获取酒店列表
     */
    @PostMapping("findHotelList")
    @AnonymousAccess
    public Response<PaginationSupportDTO<HotelListResp>> findHotelList(@RequestBody HotelPageReq req) {
        return hotelRemote.findHotelList(req);
    }
}
