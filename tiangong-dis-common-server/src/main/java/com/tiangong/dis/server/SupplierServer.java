package com.tiangong.dis.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.common.Response;
import com.tiangong.dto.common.AddSupplierForeignReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.fuzzyquery.remote.FuzzyQueryRemote;
import com.tiangong.organization.remote.SupplierRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/21 16:30
 */
@RestController
@RequestMapping("/discommon/supplier")
public class SupplierServer {

    @Autowired
    private SupplierRemote supplierRemote;

    @Autowired
    private FuzzyQueryRemote fuzzyQueryRemote;

    /**
     * 批量新增供应商
     */
    @PostMapping("addSupplierForeign")
    @SlsLog(level = "info", name = "查询", message = "addSupplierForeign", topic = "/discommon/supplier/addSupplierForeign", source = "tiangong-dis-common-server")
    public Response<Map<String, Object>> addSupplierForeign(@RequestBody List<AddSupplierForeignReq> request){
        return supplierRemote.addSupplierForeign(request);
    }

    /**
     * 模糊查询供应商
     */
    @PostMapping("querySupplierAll")
    @SlsLog(level = "info", name = "查询", message = "querySupplierAll", topic = "/discommon/supplier/querySupplierAll", source = "tiangong-dis-common-server")
    public Response<PaginationSupportDTO<FuzzySupplierDTO>> querySupplierAll(@RequestBody FuzzyQueryDTO req){
        return fuzzyQueryRemote.querySupplierAll(req);
    }
}
