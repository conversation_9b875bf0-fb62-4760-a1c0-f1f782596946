package com.tiangong.dis.server;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.CityReq;
import com.tiangong.dis.dto.CountryReq;
import com.tiangong.dis.dto.DistrictOrBusinessReq;
import com.tiangong.dto.hotel.*;
import com.tiangong.hotel.dto.BaseinfoAreadataDTO;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;
import com.tiangong.hotel.remote.HotelRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/21 16:30
 */
@RestController
@RequestMapping("/discommon/area")
public class AreaServer {

    @Autowired
    private HotelRemote hotelRemote;

    /**
     * 查询国家列表
     */
    @PostMapping("queryCountryList")
    public Response<List<BaseinfoAreadataDTO>> queryCountryList(@RequestBody CountryReq req) {
        return hotelRemote.queryCountryList(req);
    }

    /**
     * 查询热门城市列表
     */
    @PostMapping("queryHotCityList")
    public Response<List<HotCityResp>> queryHotCityList(@RequestBody CityReq req) {
        return hotelRemote.queryHotCityList(req);
    }

    /**
     * 查询热门城市列表(新)
     */
    @PostMapping("queryHotelPopularCityList")
    public Response<List<HotelPopularCityDTO>> queryHotelPopularCityList(@RequestBody HotelPopularCityVO vo) {
        return hotelRemote.queryHotelPopularCityList(vo);
    }

    /**
     * 查询行政区/商业区
     */
    @PostMapping("queryBusinessAndDistrictList")
    public Response<DistrictOrBusinessResp> queryDistrictOrBusinessList(@Validated @RequestBody DistrictOrBusinessReq req) {
        return hotelRemote.queryBusinessAndDistrictList(req);
    }

    /**
     * 获取国际区号
     */
    @PostMapping("getRegion")
    public Response<List<BaseinfoRegionDTO>> getRegion() {
        return hotelRemote.getRegion();
    }

    /**
     * 根据关键字获取酒店城市信息
     */
    @PostMapping("queryCityAndHotelByKeyword")
    public Response<List<HotCityResp>> queryCityAndHotelByKeyword(@RequestBody SearchCityAndHotelReq req) {
        return hotelRemote.queryCityAndHotelByKeyword(req);
    }

    /**
     * 更新时区，转发给基础信息云化
     */
    @PostMapping("updateTimeZone")
    public Response<Object> updateTimeZone(@RequestBody UpdateTimeZoneDTO updateTimeZone) {
        return hotelRemote.updateTimeZone(updateTimeZone);
    }
}
