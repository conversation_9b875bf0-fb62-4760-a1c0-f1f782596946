package com.tiangong.dis.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.common.Response;
import com.tiangong.common.remote.MsgRemote;
import com.tiangong.dto.common.AddMsgDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.TemplateCodeTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.req.SendEmailReq;
import com.tiangong.req.SendPhoneReq;
import com.tiangong.util.SendCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023/12/15 11:16
 */
@RestController
@RequestMapping("/discommon/msg/")
@Slf4j
public class MsgServer {

    /**
     * 短信类型
     */
    private static final int SMS_TYPE = 1;

    /**
     * 海外短信类型
     */
    private static final int OSEA_TYPE = 2;

    @Autowired
    private SendCodeUtil sendCodeUtil;

    @Autowired
    private MsgRemote msgRemote;

    /**
     * 发送邮件
     */
    @PostMapping("sendEmail")
    @SlsLog(level = "info", name = "发送邮件", message = "sendEmail", topic = "/discommon/msg/sendEmail", source = "tiangong-dis-common-server")
    public Response<Boolean> sendEmail(@Validated @RequestBody SendEmailReq req) {
        return Response.success(sendCodeUtil.sendQQEmail(req));
    }

    /**
     * 发送短信
     */
    @PostMapping("sendPhone")
    @SlsLog(level = "info", name = "发送短信", message = "sendPhone", topic = "/discommon/msg/sendPhone", source = "tiangong-dis-common-server")
    public Response<Boolean> sendPhone(@Validated @RequestBody SendPhoneReq req) {
        boolean send = false;
        String templateCode = TemplateCodeTypeEnum.getTemplateCode(req.getTemplateCode());
        if (templateCode == null) {
            throw new SysException(ErrorCodeEnum.SMS_TEMPLATE_IS_EMPTY);
        }
        if (req.getType() == SMS_TYPE) {
            send = sendCodeUtil.sendSms(req);
        } else if (req.getType() == OSEA_TYPE) {
            send = sendCodeUtil.sendOSea(req);
        }
        return Response.success(send);
    }

    /**
     * 新增消息
     */
    @PostMapping("/addMsg")
    public Response<Object> addMsg(@Validated @RequestBody AddMsgDTO addMsgDTO) {
        return msgRemote.addMsg(addMsgDTO);
    }
}
