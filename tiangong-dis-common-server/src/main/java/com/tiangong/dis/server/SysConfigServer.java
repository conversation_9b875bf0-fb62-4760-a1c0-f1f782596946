package com.tiangong.dis.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.dto.common.SysConfigDTO;
import com.tiangong.dto.common.SysConfigReq;
import com.tiangong.organization.remote.SysConfigRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023/11/21 16:30
 */
@RestController
@RequestMapping("/discommon/sysConfig")
public class SysConfigServer {

    @Autowired
    private SysConfigRemote sysConfigRemote;


    /**
     * 查询系统配置
     */
    @PostMapping("querySysConfigByKey")
    @AnonymousAccess
    public Response<SysConfigDTO> querySysConfigByKey(@RequestBody SysConfigReq req){
        return sysConfigRemote.querySysConfigByKey(req);
    }
}
