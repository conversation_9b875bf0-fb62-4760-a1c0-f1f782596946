package com.tiangong.dis.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.service.InitService;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.dto.ProductDayQuotationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;

/**
 * 初始化redis数据
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/discommon/init")
public class InitServer {

    @Autowired
    private InitService initService;

    /**
     * 初始化价格
     */
    @RequestMapping("/initBasePriceAndRoomStatus")
    public Response<Object> initBasePriceAndRoomStatus(@RequestBody(required = false) IncrementObjectDTO incrementObjectDTO) {
        try {
            if (incrementObjectDTO != null) {
                initService.initBasePriceAndRoomStatus(JSON.parseObject(JSON.toJSONString(incrementObjectDTO.getObject()), new TypeReference<ArrayList<ProductDayQuotationDTO>>() {
                }), incrementObjectDTO.getIncrementList());
            } else {
                initService.initBasePriceAndRoomStatus(null, null);
            }
            return Response.success();
        } catch (Exception e) {
            log.error("初始化价格异常", e);
        }
        return Response.error(ErrorCodeEnum.INIT_PRICE_ERROR.errorCode, ErrorCodeEnum.INIT_PRICE_ERROR.errorDesc);
    }

    /**
     * 初始化售价信息
     */
    @RequestMapping("/initSalePrice")
    public Response<Object> initSalePrice(@RequestBody(required = false) IncrementObjectDTO incrementObjectDTO) {
        try {
            if (incrementObjectDTO != null) {
                initService.initSalePrice(JSON.parseObject(JSON.toJSONString(incrementObjectDTO.getObject()), new TypeReference<ArrayList<ProductSaleIncreaseDTO>>() {
                }), incrementObjectDTO.getIncrementList());
            } else {
                initService.initSalePrice(null, null);
            }
            return Response.success();
        } catch (Exception e) {
            log.error("初始化售价信息异常", e);
        }
        return Response.error(ErrorCodeEnum.INIT_SALE_PRICE_ERROR.errorCode, ErrorCodeEnum.INIT_SALE_PRICE_ERROR.errorDesc);
    }

    /**
     * 初始化上下架信息
     */
    @RequestMapping("/initSaleStatus")
    public Response<Object> initSaleStatus(@RequestBody(required = false) IncrementObjectDTO incrementObjectDTO) {
        try {
            if (incrementObjectDTO != null) {
                initService.initSaleStatus(JSON.parseObject(JSON.toJSONString(incrementObjectDTO.getObject()), new TypeReference<ArrayList<ProductSaleStatusDTO>>() {
                }), incrementObjectDTO.getIncrementList());
            } else {
                initService.initSaleStatus(null, null);
            }
            return Response.success();
        } catch (Exception e) {
            log.error("初始化上下架信息异常", e);
        }
        return Response.error(ErrorCodeEnum.INIT_SALE_PRICE_ERROR.errorCode, ErrorCodeEnum.INIT_SALE_PRICE_ERROR.errorDesc);
    }

    /**
     * 初始化上下架信息
     */
    @RequestMapping("/initSaleStatusForReplace")
    public Response<Object> initSaleStatusForReplace() {
        try {
            initService.initSaleStatusForReplace(null, null);
            return Response.success();
        } catch (Exception e) {
            log.error("初始化上下架信息异常", e);
        }
        return Response.error(ErrorCodeEnum.INIT_UP_DOWN_MSG_ERROR.errorCode, ErrorCodeEnum.INIT_UP_DOWN_MSG_ERROR.errorDesc);
    }

    /**
     * 初始化条款
     */
    @RequestMapping("/initRestrict")
    public Response<Object> initRestrict(@RequestBody(required = false) IncrementObjectDTO incrementObjectDTO) {
        try {
            if (incrementObjectDTO != null) {
                initService.initRestrict(JSON.parseObject(JSON.toJSONString(incrementObjectDTO.getObject()), new TypeReference<ArrayList<ProductDTO>>() {
                        }),
                        incrementObjectDTO.getIncrementList());
            } else {
                initService.initRestrict(null, null);
            }
            return Response.success();
        } catch (Exception e) {
            log.error("初始化条款异常", e);
        }
        return Response.error(ErrorCodeEnum.INIT_TERM_ERROR.errorCode, ErrorCodeEnum.INIT_TERM_ERROR.errorDesc);
    }

    /**
     * 初始化产品商家关系
     */
    @RequestMapping("/initProductCompanyRelation")
    public Response<Object> initProductCompanyRelation() {
        try {
            initService.initProductCompanyRelation();
            return Response.success();
        } catch (Exception e) {
            log.error("初始化产品商家关系异常", e);
        }
        return Response.error(ErrorCodeEnum.INIT_PRODUCT_SUPPER_ERROR.errorCode, ErrorCodeEnum.INIT_PRODUCT_SUPPER_ERROR.errorDesc);
    }

    /**
     * 初始化汇率
     */
    @RequestMapping("/initExchangeRate")
    public Response<Object> initExchangeRate(@RequestBody(required = false) CurrencyExchangeRateDTO currencyExchangeRateDTO) {
        try {
            initService.initExchangeRate(currencyExchangeRateDTO);
            return Response.success();
        } catch (Exception e) {
            log.error("初始化汇率异常", e);
        }
        return Response.error(ErrorCodeEnum.INIT_PRODUCT_RATE_ERROR.errorCode, ErrorCodeEnum.INIT_PRODUCT_RATE_ERROR.errorDesc);
    }

    /**
     * 初始产品信息
     */
    @RequestMapping("/initProductInfo")
    public Response<Object> initProductInfo(@RequestBody(required = false) IncrementObjectDTO incrementObjectDTO) {
        try {
            if (incrementObjectDTO != null) {
                initService.initProductInfo(JSON.parseObject(JSON.toJSONString(incrementObjectDTO.getObject()), new TypeReference<ArrayList<ProductDTO>>() {
                }), incrementObjectDTO.getIncrementList());
            } else {
                initService.initProductInfo(null, null);
            }
            return Response.success();
        } catch (Exception e) {
            log.error("初始产品信息系统异常", e);
        }
        return Response.error();
    }

}
