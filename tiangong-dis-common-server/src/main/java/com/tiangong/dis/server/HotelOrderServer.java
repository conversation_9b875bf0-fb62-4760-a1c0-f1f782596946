package com.tiangong.dis.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.dis.common.CommonUtils;
import com.tiangong.dis.dto.QueryOrderInvoiceResponse;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dis.service.HotelOrderService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.*;
import com.tiangong.dto.order.response.*;
import com.tiangong.order.remote.request.QueryOrderInvoiceDTO;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.request.SupplyOrderIdDTO;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 产品输出接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/discommon/order")
public class HotelOrderServer {

    @Autowired
    private HotelOrderService hotelOrderService;

    /**
     * 试预订(可订检查)接口
     */
    @PostMapping("/checkBooking")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "checkBooking", topic = "/discommon/order/checkBooking", source = "tiangong-dis-common-server")
    public ResponseResult<CheckBookingResponse> checkBooking(@RequestBody CheckBookingRequest request) {
        CommonUtils.checkDate30(request.getCheckInDate(), request.getCheckOutDate());
        return hotelOrderService.checkBooking(request);
    }

    /**
     * 创建订单
     */
    @PostMapping("/createOrder")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "createOrder", topic = "/discommon/order/createOrder", source = "tiangong-dis-common-server")
    public ResponseResult<CreateOrderResponse> createOrder(@RequestBody CreateOrderRequest request) {
        return hotelOrderService.createOrder(request);
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancelOrder")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "cancelOrder", topic = "/discommon/order/cancelOrder", source = "tiangong-dis-common-server")
    public ResponseResult<CancelOrderResponse> cancelOrder(@RequestBody CancelOrderRequest request) {
        return hotelOrderService.cancelOrder(request);
    }

    /**
     * 订单详情查询-api
     */
    @PostMapping("/queryAPIOrderDetail")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryAPIOrderDetail", topic = "/discommon/order/queryAPIOrderDetail", source = "tiangong-dis-common-server")
    public ResponseResult<OrderDetailResponse> queryAPIOrderDetail(@RequestBody OrderDetailRequest request) {
        return hotelOrderService.queryAPIOrderDetail(request);
    }

    /**
     * 订单详情查询-b2b
     */
    @PostMapping("/queryB2BOrderDetail")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryB2BOrderDetail", topic = "/discommon/order/queryB2BOrderDetail", source = "tiangong-dis-common-server")
    public OrderDTO queryB2BOrderDetail(@RequestBody OrderDetailRequest request) {
        return hotelOrderService.queryB2BOrderDetail(request);
    }

    /**
     * 查询订单列表
     */
    @PostMapping("/queryOrderList")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryOrderList", topic = "/discommon/order/queryOrderList", source = "tiangong-dis-common-server")
    Response<PaginationSupportDTO<OrderSimpleDTO>> queryOrderList(@RequestBody QueryOrderListDTO request) {
        return hotelOrderService.queryOrderList(request);
    }

    /**
     * 订单支付接口
     */
    @PostMapping("/payOrder")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "payOrder", topic = "/discommon/order/payOrder", source = "tiangong-dis-common-server")
    public ResponseResult<PayOrderResponse> payOrder(@RequestBody PayOrderRequest request) {
        return hotelOrderService.payOrder(request);
    }

    /**
     * 订单退款通知接口
     */
    @PostMapping("/refundNoticeOrder")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "refundNoticeOrder", topic = "/discommon/order/refundNoticeOrder", source = "tiangong-dis-common-server")
    public ResponseResult<OrderRefundNoticeResponse> refundNoticeOrder(@RequestBody OrderRefundNoticeRequest request) {
        return hotelOrderService.refundNoticeOrder(request);
    }

    /**
     * 信用额度查询接口
     */
    @PostMapping("/queryCreditBalance")
    @AnonymousAccess
    public ResponseResult<CreditBalanceResponse> queryCreditBalance(@RequestBody QueryCreditBalanceRequest request) {
        return hotelOrderService.queryCreditBalance(request);
    }

    /**
     * 订单开票接口
     */
    @PostMapping("/createInvoice")
    @AnonymousAccess
    public ResponseResult<CreateInvoiceResponse> createInvoice(@RequestBody CreateInvoiceRequest request) {
        return hotelOrderService.createInvoice(request);
    }

    /**
     * 根据订单号查询发票详情
     */
    @PostMapping("/discommon/orderInvoice/query")
    public ResponseResult<QueryOrderInvoiceResponse> queryOrderInvoice(@RequestBody QueryOrderInvoiceDTO request) {
        return hotelOrderService.queryOrderInvoice(request);
    }

    /**
     * 订单入住详细情况查询接口
     */
    @PostMapping("/queryOrderCheckDetailInfo")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryOrderCheckDetailInfo", topic = "/discommon/order/queryOrderCheckDetailInfo", source = "tiangong-dis-common-server")
    public ResponseResult<OrderCheckDetailInfoResponse> queryOrderCheckDetailInfo(@RequestBody OrderCheckInfoRequest request) {
        return hotelOrderService.queryOrderCheckDetailInfo(request);
    }

    /**
     * 查询订单列表统计-B2B
     */
    @PostMapping("/queryOrderStatisticsB2B")
    @AnonymousAccess
    public Response<OrderStatisticsDTOB2BResponse> queryOrderStatisticsB2B(@RequestBody QueryOrderStatisticsDTOB2BRequest request) {
        return hotelOrderService.queryOrderStatisticsB2B(request);
    }

    /**
     * 退房申请接口
     */
    @PostMapping("/checkoutApply")
    @AnonymousAccess
    @SlsLog(level = "info", name = "新增", message = "checkoutApply", topic = "/discommon/order/checkoutApply", source = "tiangong-dis-common-server")
    public ResponseResult<CheckoutApplyInfoResponse> checkoutApply(@RequestBody CheckoutApplyInfoRequest request) {
        return hotelOrderService.checkoutApply(request);
    }

    /**
     * 退房详情查询接口
     */
    @PostMapping("/queryCheckoutDetail")
    @AnonymousAccess
    @SlsLog(level = "info", name = "查询", message = "queryCheckoutDetail", topic = "/discommon/order/queryCheckoutDetail", source = "tiangong-dis-common-server")
    public ResponseResult<CheckoutDetailInfoResponse> queryCheckoutDetail(@RequestBody CheckoutDetailInfoRequest request) {
        return hotelOrderService.queryCheckoutDetail(request);
    }

    /**
     * 查询供货单详情
     */
    @PostMapping("/querySupplyOrderInfo")
    @AnonymousAccess
    public Response<SupplyOrderDTO> querySupplyOrderInfo(@RequestBody SupplyOrderIdDTO request) {
        return hotelOrderService.querySupplyOrderInfo(request);
    }
}
