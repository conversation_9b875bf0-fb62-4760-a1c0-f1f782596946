package com.tiangong.dis.server;

import com.tiangong.common.Response;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.hotel.remote.HotelRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 酒店信息输出接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/discommon/hotel")
public class HotelServer {

    @Autowired
    private HotelRemote hotelRemote;

    /**
     * 查询目的地酒店
     */
    @PostMapping("searchDestinationHotel")
    public Response<List<EsHotelDto>> searchDestinationHotel(@RequestBody DestinationReq req) {
        return hotelRemote.searchDestinationHotel2(req);
    }
}
