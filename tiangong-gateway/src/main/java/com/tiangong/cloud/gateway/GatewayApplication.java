package com.tiangong.cloud.gateway;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * @author: wang
 * @date: 2021/1/20 15:25
 * @Description:
 */
@Slf4j
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class GatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);

        log.info("\n----------------------------------------------------------\n\t" +
                "项目启动成功！ \n\t" +
                "Spring Boot Version: {}\n" +
                ".__   __.   ______      .______    __    __    _______\n" +
                "|  \\ |  |  /  __  \\     |   _  \\  |  |  |  |  /  _____|\n" +
                "|   \\|  | |  |  |  |    |  |_)  | |  |  |  | |  |  __\n" +
                "|  . `  | |  |  |  |    |   _  <  |  |  |  | |  | |_ |\n" +
                "|  |\\   | |  `--'  |    |  |_)  | |  `--'  | |  |__| |\n" +
                "|__| \\__|  \\______/     |______/   \\______/   \\______|\n" +
                "\n" +
                "███╗   ██╗ ██████╗     ██████╗ ██╗   ██╗ ██████╗\n" +
                "████╗  ██║██╔═══██╗    ██╔══██╗██║   ██║██╔════╝\n" +
                "██╔██╗ ██║██║   ██║    ██████╔╝██║   ██║██║  ███╗\n" +
                "██║╚██╗██║██║   ██║    ██╔══██╗██║   ██║██║   ██║\n" +
                "██║ ╚████║╚██████╔╝    ██████╔╝╚██████╔╝╚██████╔╝\n" +
                "╚═╝  ╚═══╝ ╚═════╝     ╚═════╝  ╚═════╝  ╚═════╝ \n" +
                "----------------------------------------------------------", SpringBootVersion.getVersion());
    }
}
