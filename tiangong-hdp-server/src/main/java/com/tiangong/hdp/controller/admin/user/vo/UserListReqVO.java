package com.tiangong.hdp.controller.admin.user.vo;

import com.tiangong.cloud.commonbean.utils.PageVo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class UserListReqVO extends PageVo {

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 是否启用 0否 1是 空全部
     */
    private Integer availableStatus;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 是否是管理员 1是
     */
    private Integer isSuperAdmin;
}