package com.tiangong.hdp.controller.admin.order;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hdp.controller.admin.order.vo.*;
import com.tiangong.hdp.service.order.OrderService;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/hdp/order")
public class OrderController extends BaseController {

    @Autowired
    private OrderService orderService;

    /**
     * 试预订(可订检查)接口
     */
    @PostMapping("/checkBooking")
    @SlsLog(level = "info", name = "创建订单", message = "checkBooking", topic = "/hdp/order/checkBooking", source = "tiangong-hdp-server")
    public Response<CheckBookingRespVO> checkBooking(@RequestBody @Valid CheckBookingReqVO reqVO) {
        return Response.success(orderService.checkBooking(reqVO));
    }

    /**
     * 创建订单
     */
    @PostMapping("/createOrder")
    @SlsLog(level = "info", name = "创建订单", message = "createOrder", topic = "/hdp/order/createOrder", source = "tiangong-hdp-server")
    public Response<CreateOrderRespVO> submitOrder(@RequestBody @Valid CreateOrderReqVO reqVO) {
        return Response.success(orderService.createOrder(reqVO));
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancelOrder")
    @SlsLog(level = "info", name = "取消订单", message = "cancelOrder", topic = "/hdp/order/cancelOrder", source = "tiangong-hdp-server")
    public Response<CancelOrderRespVO> cancelOrder(@RequestBody @Valid CancelOrderReqVO reqVO) {
        return Response.success(orderService.cancelOrder(reqVO));
    }

    /**
     * 查询订单详情
     */
    @PostMapping("/orderDetail")
    @SlsLog(level = "info", name = "查询订单详情", message = "orderDetail", topic = "/hdp/order/orderDetail", source = "tiangong-hdp-server")
    public Response<OrderDetailRespVO> orderDetail(@RequestBody @Valid OrderDetailReqVO reqVO) {
        return Response.success(orderService.orderDetail(reqVO));
    }

    /**
     * 订单支付接口
     */
    @PostMapping("/payOrder")
    @SlsLog(level = "info", name = "订单支付接口", message = "payOrder", topic = "/hdp/order/payOrder", source = "tiangong-hdp-server")
    public Response<PayOrderRespVO> payOrder(@RequestBody @Valid PayOrderReqVO reqVO) {
        return Response.success(orderService.payOrder(reqVO));
    }

    /**
     * 订单列表（分页）
     */
    @PostMapping("/orderPage")
    @SlsLog(level = "info", name = "订单列表", message = "orderPage", topic = "/hdp/order/orderPage", source = "tiangong-hdp-server")
    public Response<PaginationSupportDTO<OrderSimpleDTO>> orderList(@RequestBody @Valid QueryOrderListDTO dto) {
        return orderService.orderPage(dto);
    }
}