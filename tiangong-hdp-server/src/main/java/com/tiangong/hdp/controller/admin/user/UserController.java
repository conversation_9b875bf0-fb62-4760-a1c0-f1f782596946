package com.tiangong.hdp.controller.admin.user;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.exception.SysException;
import com.tiangong.hdp.controller.admin.user.vo.*;
import com.tiangong.hdp.domain.resp.UserListResp;
import com.tiangong.hdp.enums.ErrorEnum;
import com.tiangong.hdp.service.user.UserService;
import com.tiangong.util.DateUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/hdp/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    /**
     * 登录
     */
    @PostMapping("login")
    public Response<Map<String, Object>> login(@Validated @RequestBody LoginReqVO req, HttpServletRequest request) throws Exception {
        req.setIpAddress(getIpAddress());
        return userService.login(request, req);
    }

    /**
     * 用户列表（分页）
     */
    @PostMapping("/userPage")
    public Response<PaginationSupportDTO<UserListResp>> userPage(@Validated @RequestBody UserListReqVO reqVO) {
        return Response.success(userService.userPage(reqVO));
    }

    /**
     * 用户新增
     */
    @PostMapping("/userAdd")
    public Response<Object> userAdd(@Validated @RequestBody AddOrUpdateUserReqVO reqVO) throws Exception {
        reqVO.setCreatedBy(getUserName());
        reqVO.setCreatedDt(DateUtilX.getDateStr());
        reqVO.setUserType(getUser().getUserType());
        userService.userAdd(reqVO);
        return Response.success();
    }

    /**
     * 修改用户
     */
    @PostMapping("updateUser")
    public Response<Object> updateUser(@Validated @RequestBody AddOrUpdateUserReqVO req) throws Exception {
        req.setUpdatedBy(getUserName());
        req.setCreatedDt(DateUtilX.getDateStr());
        req.setUserType(getUser().getUserType());
        userService.updateUser(req);
        return Response.success();
    }

    /**
     * 删除用户
     */
    @PostMapping("delUser")
    public Response<Object> delUser(@Validated @RequestBody DelUserReqVO req) {
        req.setUserType(getUser().getUserType());
        userService.delUser(req);
        return Response.success();
    }

    /**
     * 重置密码
     */
    @PostMapping("resetPassword")
    public Response<Object> resetPassword(@Validated @RequestBody ResetPasswordReqVO req) {
        req.setUpdatedBy(getUserName());
        req.setUserType(getUser().getUserType());
        userService.resetPassword(req);
        return Response.success();
    }

    /**
     * 修改密码
     */
    @PostMapping("updatePassword")
    public Response<Object> updatePassword(@Validated @RequestBody UpdatePasswordReqVO req) {
        req.setUserId(getUserId());
        userService.updatePassword(req);
        return Response.success();
    }

    /**
     * 获取登录用户信息
     * 假如是商户登录，则返回客户管理员信息
     */
    @PostMapping("userInfo")
    public Response<UserInfoRespVO> userInfo(@RequestBody UserInfoReqVO req) {
        req.setUserId(getUserId());
        return Response.success(userService.userInfo(req));
    }

    /**
     * 校验账号是否存在
     */
    @PostMapping("userAccountExist")
    public Response<Object> userAccountExist(@Validated @RequestBody UserAccountExistReqVO req) throws Exception {
        Boolean exist = userService.userAccountExist(req.getUserAccount(), req.getUserId());
        if (!exist) {
            throw new SysException(ErrorEnum.USER_ACCOUNT_IS_ALREADY.type, ErrorEnum.USER_ACCOUNT_IS_ALREADY.content);
        }
        return Response.success();
    }

    /**
     * 退出登录
     */
    @PostMapping("loginBack")
    public Response<Object> loginBack() {
        userService.loginBack(getToken(), getLoginName(), request);
        return Response.success();
    }
}