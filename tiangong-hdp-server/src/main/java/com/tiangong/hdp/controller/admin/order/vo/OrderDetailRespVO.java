package com.tiangong.hdp.controller.admin.order.vo;

import com.tiangong.dto.product.response.TipInfosDTO;
import com.tiangong.entity.response.order.OrderFeeDto;
import com.tiangong.entity.vo.order.GuestInfo;
import com.tiangong.entity.vo.order.InvoiceInfoDto;
import com.tiangong.entity.vo.order.QueryOrderPriceItem;
import com.tiangong.entity.vo.order.RoomGuestNumberDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderDetailRespVO {
    /**
     * 房仓订单号
     */
    private String fcOrderCode;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    private String channelState;

    /**
     * 酒店确认号
     */
    private String hotelConfirmNo;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型id
     */
    private Long roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 价格计划id
     */
    private String ratePlanId;

    /**
     * 价格计划名称
     */
    private String ratePlanName;

    /**
     * 床型
     */
    private String bedType;

    /**
     * 入住时间
     */
    private String checkInDate;

    /**
     * 离店时间
     */
    private String checkOutDate;

    /**
     * 入住时间(钟点房)
     */
    private String checkInTime;

    /**
     * 离店时间(钟点房)
     */
    private String checkOutTime;

    /**
     * 到店时间(钟点房)
     */
    private String arriveTime;

    /**
     * 最新到店时间(钟点房)
     */
    private String latestArriveTime;

    /**
     * 房间数量
     */
    private Integer roomNum;

    /**
     * 总金额
     */
    private Double totalAmount;

    /**
     * 价格明细(按间明细)
     */
    private List<QueryOrderPriceItem> priceItems;

    /**
     * 费用明细(按间明细)
     */
    private List<OrderFeeDto> feeList;

    /**
     * 入住人信息
     */
    private List<GuestInfo> guestInfos;

    /**
     * 联系人信息
     */
    private String linkMan;

    /**
     * 联系人电话
     */
    private String linkPhone;

    /**
     * 联系人邮箱
     */
    private String email;

    /**
     * 发票模式
     */
    private Integer invoiceModel;

    /**
     * 发票信息
     */
    private InvoiceInfoDto invoiceInfo;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 入住类型：1-全日房，2-钟点房
     */
    private Integer checkInType;

    /**
     * 币种
     */
    private String currency;

    /**
     * 到店另付费用；针对海外酒店有效
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种；针对海外酒店有效
     */
    private String payAtHotelFeeCurrency;

    /**
     * 房间客人数量信息
     */
    private List<RoomGuestNumberDto> roomGuestNumbers;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 供应商标签名称（带启用）
     */
    private Integer productLabel;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    /**
     * 每日总底价(供应商金额)
     */
    private BigDecimal totalBasePrice;

    /**
     * 底价币种（供应商币种）
     */
    private String baseCurrency;

    /**
     * 快速处理标签
     */
    private Integer rapidProcessing;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;

    /**
     * 罚金金额
     */
    private BigDecimal penaltiesValue;

    /**
     * 服务费
     */
    private BigDecimal serviceCharge;

    /**
     * 支付超时时间
     * 默认超时时间：10分钟
     */
    private Long paymentTimeoutTime;
}