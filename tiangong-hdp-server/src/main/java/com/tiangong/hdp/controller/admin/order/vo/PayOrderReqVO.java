package com.tiangong.hdp.controller.admin.order.vo;

import com.tiangong.entity.base.BusinessRequest;
import com.tiangong.entity.vo.order.MixedPayInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PayOrderReqVO extends BusinessRequest {
    /**
     * 房仓订单号
     */
    private String fcOrderCode;

    /**
     * 支付方式 1-企业支付 2-个人支付
     */
    private Integer payMethod;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 支付金额（支付总金额）（包含订单服务费）
     */
    private BigDecimal payAmount;

    /**
     * 支付账户
     */
    private String payAccount;

    /**
     * 支付币种
     */
    private String payCurrency;

    /**
     * 是否混合支付 0-否 1-是
     */
    private Integer isMixedPay;

    /**
     * 混合支付列表
     */
    private List<MixedPayInfo> mixedPayList;

    /**
     * 渠道编码
     */
    private String channelCode;
}