package com.tiangong.hdp.controller.admin.product;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.hdp.controller.admin.product.vo.ProductReqVO;
import com.tiangong.hdp.controller.admin.product.vo.ProductRespVO;
import com.tiangong.hdp.service.product.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/hdp/product")
public class ProductController extends BaseController {

    @Autowired
    private ProductService productService;

    /**
     * 酒店实时产品查询
     */
    @PostMapping("/queryProductDetail")
    @SlsLog(level = "info", name = "酒店实时产品查询", message = "queryProductDetail", topic = "/hdp/product/queryProductDetail", source = "tiangong-hdp-server")
    public Response<List<ProductRespVO>> queryProductDetail(@RequestBody @Valid ProductReqVO reqVO) {
        return Response.success(productService.queryProductDetail(reqVO));
    }
}