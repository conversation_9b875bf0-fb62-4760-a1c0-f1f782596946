package com.tiangong.hdp.controller.admin.hotel;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.BaseinfoRegionDTO;
import com.tiangong.dto.hotel.AddressRespDTO;
import com.tiangong.dto.hotel.HotelSearchReq;
import com.tiangong.dto.hotel.HotelSearchResp;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.hdp.controller.admin.hotel.vo.*;
import com.tiangong.hdp.service.hotel.HotelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/hdp/hotel")
public class HotelController extends BaseController {

    @Autowired
    private HotelService hotelService;

    /**
     * 查询目的地
     */
    @PostMapping("/searchDestination")
    public Response<List<AddressRespDTO>> searchDestination(@RequestBody @Valid DestinationReqVO reqVO) {
        return Response.success(hotelService.searchDestination(reqVO));
    }

    /**
     * 获取国际区号
     */
    @PostMapping("getRegion")
    @AnonymousAccess
    public Response<List<BaseinfoRegionDTO>> getRegion() {
        return hotelService.getRegion();
    }

    /**
     * 查询酒店列表
     */
    @PostMapping("/hotelList")
    public Response<HotelListRespVO> hotelList(@RequestBody @Valid HotelListReqVO reqVO) {
        return Response.success(hotelService.hotelList(reqVO));
    }

    /**
     * 查询酒店产品起价
     */
    @PostMapping("/hotelLowestPrice")
    public Response<HotelLowestPriceRespVO> hotelLowestPrice(@RequestBody @Valid HotelLowestPriceReqVO reqVO) {
        return Response.success(hotelService.hotelLowestPrice(reqVO));
    }

    /**
     * 查询币种列表
     */
    @PostMapping("/currencyList")
    public Response<List<CurrencyRespVO>> currencyList() {
        List<CurrencyRespVO> list = new ArrayList<>();
        for (SettlementCurrencyEnum value : SettlementCurrencyEnum.values()) {
            CurrencyRespVO resp = new CurrencyRespVO();
            resp.setKey(value.getKey());
            resp.setValue(value.getCode());
            resp.setContent(value.getValue());
            list.add(resp);
        }
        return Response.success(list);
    }

    /**
     * 获取酒店过滤器数据
     */
    @PostMapping("getHotelSearch")
    public Response<HotelSearchResp> getHotelSearch(@RequestBody HotelSearchReq hotelSearchReq) {
        hotelSearchReq.setLanguage(getLanguage());
        return hotelService.getHotelSearch(hotelSearchReq);
    }

    /**
     * 查询酒店信息
     */
    @PostMapping("queryHotelInfo")
    public Response<HotelInfoCollectionDTO> queryHotelInfo(@RequestBody HotelInfoCollectionReq req) {
        req.setLanguageType(getLanguage());
        return hotelService.queryHotelInfo(req);
    }
}