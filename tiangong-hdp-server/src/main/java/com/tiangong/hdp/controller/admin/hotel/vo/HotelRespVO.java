package com.tiangong.hdp.controller.admin.hotel.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelRespVO {
    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店星级
     */
    private Integer hotelStar;

    /**
     * 酒店地址
     */
    private String hotelAddress;

    /**
     * 酒店起价
     */
    private BigDecimal lowestPrice;

    /**
     * 起价币种类型
     */
    private Integer saleCurrency;

    /**
     * 起价币种
     */
    private String saleCurrencyCode;

    /**
     * 酒店主图
     */
    private String hotelImg;

    /**
     * 行政区名称
     */
    private String districtName;

    /**
     * 商业区名称
     */
    private String businessName;

    /**
     * 酒店评分
     */
    private Double hotelScore;

    /**
     * 城市编码
     */
    private String countryCode;

    /**
     * 到店付金额
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店付币种类型
     */
    private Integer payAtHotelCurrency;

    /**
     * 到店付币种
     */
    private String payAtHotelCurrencyCode;

    /**
     * 谷歌经度
     */
    private BigDecimal lngGoogle;

    /**
     * 谷歌纬度
     */
    private BigDecimal latGoogle;

    /**
     * 标签列表
     */
    private Set<String> hotelLabelNameList;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 主图
     */
    private String mainUrl;
}