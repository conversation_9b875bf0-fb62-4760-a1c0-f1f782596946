package com.tiangong.hdp.controller.admin.hotel.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class HotelListReqVO {

    /**
     * 目的地id
     */
    @NotEmpty(message = "ADDRESS_BY_ID_NOT_EMPTY")
    private String destinationId;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 价格开始值
     */
    private Double priceBegin;

    /**
     * 价格结束值
     */
    private Double priceEnd;

    /**
     * 酒店标签
     */
    private List<Long> hotelLabelIds;

    /**
     * 酒店品牌
     */
    private List<String> hotelBrandCodes;

    /**
     * 酒店集团
     */
    private List<String> plateCodes;

    /**
     * 酒店分类
     */
    private List<String> hotelSubCategoryIds;

    /**
     * 酒店设施
     */
    private List<String> hotelFacilityCodes;

    /**
     * 房型设施
     */
    private List<String> roomFacilityCodes;

    /**
     * 酒店星级
     */
    private List<Long> hotelStars;

    /**
     * 设施id
     */
    private List<Long> facilitiesIds;

    /**
     * 1默认排序 2价格由低到高 3价格由高到低 4星级从高到底 5星级从低到高 6距离由近到远 7评分由高到低
     */
    private Integer sortBy = 1;

    /**
     * 语言
     */
    private String language = "en-US";

    /**
     * 入住日期 yyyy-MM-dd
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;

    /**
     * 离店日期 yyyy-MM-dd
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;

    /**
     * 当前页码
     */
    private Long currentPage = 1L;

    /**
     * 每页条数
     */
    private Long pageSize = 10L;
}