package com.tiangong.hdp.convert;

import com.tiangong.hdp.controller.admin.user.vo.AddOrUpdateUserReqVO;
import com.tiangong.hdp.domain.po.UserPO;
import com.tiangong.hdp.domain.resp.UserListResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    UserListResp userListRespConvert(UserPO userPO);

    UserPO userPOConvert(AddOrUpdateUserReqVO reqVO);
}