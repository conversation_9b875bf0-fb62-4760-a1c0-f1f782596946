package com.tiangong.hdp.service.order;

import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hdp.controller.admin.order.vo.*;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;

public interface OrderService {

    /**
     * 试预订(可订检查)接口
     */
    CheckBookingRespVO checkBooking(CheckBookingReqVO reqVO);

    /**
     * 创建订单
     */
    CreateOrderRespVO createOrder(CreateOrderReqVO dto);

    /**
     * 取消订单
     */
    CancelOrderRespVO cancelOrder(CancelOrderReqVO reqVO);

    /**
     * 查询订单详情
     */
    OrderDetailRespVO orderDetail(OrderDetailReqVO reqVO);

    /**
     * 订单支付接口
     */
    PayOrderRespVO payOrder(PayOrderReqVO reqVO);

    /**
     * 订单列表
     */
    Response<PaginationSupportDTO<OrderSimpleDTO>> orderPage(QueryOrderListDTO dto);
}
