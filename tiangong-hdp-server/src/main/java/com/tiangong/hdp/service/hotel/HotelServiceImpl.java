package com.tiangong.hdp.service.hotel;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.BaseinfoRegionDTO;
import com.tiangong.dis.remote.AreaRemote;
import com.tiangong.dto.hotel.AddressRespDTO;
import com.tiangong.dto.hotel.HotelListResp;
import com.tiangong.dto.hotel.HotelSearchReq;
import com.tiangong.dto.hotel.HotelSearchResp;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.entity.request.basic.AddressRequest;
import com.tiangong.entity.request.hotel.HotelListPageRequest;
import com.tiangong.entity.request.hotel.ProductDetailInfoRequest;
import com.tiangong.entity.response.basic.AddressResponse;
import com.tiangong.entity.response.hotel.HotelListPageResponse;
import com.tiangong.entity.response.hotel.ProductDetailInfoResponse;
import com.tiangong.entity.vo.hotel.ProductDetailDto;
import com.tiangong.entity.vo.hotel.RoomItemDto;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hdp.config.SettingsConstant;
import com.tiangong.hdp.controller.admin.hotel.vo.*;
import com.tiangong.hdp.convert.HotelConvert;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.base.HotelInfoService;
import com.tiangong.service.bean.AreaService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HotelServiceImpl implements HotelService {

    @Autowired
    private AreaService areaService;

    @Autowired
    private com.tiangong.service.base.ProductService productService;

    @Autowired
    private HotelInfoService hotelInfoService;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private AreaRemote areaRemote;

    @Override
    public List<AddressRespDTO> searchDestination(DestinationReqVO reqVO) {
        AddressRequest request = new AddressRequest();
        request.setKeyWord(reqVO.getKeyWord());
        AddressResponse address = areaService.getAddress(request);

        if (CollUtilX.isEmpty(address.getDestinationDTOList())) {
            return Collections.emptyList();
        }

        List<AddressRespDTO> destination = address.getDestinationDTOList();

        // 定义类型优先级顺序：城市(2), 酒店(1), POI(8), 商业区(4), 行政区(3), 旅游区(5), 比城市小的区(7), 城市及周边(6)
        List<Integer> priorityOrder = Arrays.asList(2, 1, 8, 4, 3, 5, 7, 6);

        // 按类型分组
        Map<Integer, List<AddressRespDTO>> typeMap = destination.stream()
                .collect(Collectors.groupingBy(AddressRespDTO::getDataType));

        // 按优先级顺序合并结果
        List<AddressRespDTO> result = new ArrayList<>();
        priorityOrder.forEach(type -> {
            List<AddressRespDTO> items = typeMap.get(type);
            if (CollUtilX.isNotEmpty(items)) {
                result.addAll(items);
            }
        });

        return result;
    }

    @Override
    public Response<List<BaseinfoRegionDTO>> getRegion() {
        return areaRemote.getRegion();
    }

    @Override
    public HotelLowestPriceRespVO hotelLowestPrice(HotelLowestPriceReqVO reqVO) {
        if (StrUtilX.isEmpty(reqVO.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        ProductDetailInfoRequest request = new ProductDetailInfoRequest();
        request.setHotelId(reqVO.getHotelId());
        request.setCheckInDate(reqVO.getCheckInDate());
        request.setCheckOutDate(reqVO.getCheckOutDate());
        request.setRoomNum(reqVO.getRoomNum());
        request.setRoomGuestNumbers(reqVO.getRoomGuestNumbers());
        request.setNeedClockRoomProduct(reqVO.getNeedClockRoomProduct());
        request.setRoomGuestNumber(reqVO.getRoomGuestNumber());
        request.setNationality(reqVO.getNationality());
        request.setAgentCode(reqVO.getAgentCode());
        // 获取客户信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, reqVO.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
        }
        request.setDomesticOrOverseas(agentAccountConfig.getDomesticOrOverseas());
        ProductDetailInfoResponse productDetailInfoResponse = productService.queryProductDetail(request);
        if (productDetailInfoResponse == null || CollUtilX.isEmpty(productDetailInfoResponse.getRoomItems())) {
            return HotelLowestPriceRespVO.builder().lowestPrice(BigDecimal.ZERO).build();
        }
        // 取房型最低均价
        List<BigDecimal> prices = new ArrayList<>();
        long dayNum = DateUtilX.getDay(DateUtilX.stringToDate(reqVO.getCheckInDate()), DateUtilX.stringToDate(reqVO.getCheckOutDate()));

        for (RoomItemDto roomItem : productDetailInfoResponse.getRoomItems()) {
            if (CollUtilX.isEmpty(roomItem.getProducts())) {
                continue;
            }
            for (ProductDetailDto product : roomItem.getProducts()) {
                if (product.getTotalSalePrice() != null && BigDecimal.ZERO.compareTo(product.getTotalSalePrice()) != 0) {
                    prices.add(product.getTotalSalePrice());
                    break;
                }
            }
        }

        if (CollUtilX.isEmpty(prices)) {
            return HotelLowestPriceRespVO.builder().lowestPrice(BigDecimal.ZERO).build();
        }

        // 排序
        Collections.sort(prices);
        // 取第一个价格为最低价，计算均价
        BigDecimal avgPrice = prices.get(0).divide(new BigDecimal(request.getRoomNum() * dayNum), 2, RoundingMode.UP);
        return HotelLowestPriceRespVO.builder().lowestPrice(avgPrice).build();
    }

    @Override
    public HotelListRespVO hotelList(HotelListReqVO reqVO) {
        HotelListRespVO hotelAllListResp = new HotelListRespVO();
        HotelListPageRequest request = HotelConvert.INSTANCE.hotelListPageRequestConvert(reqVO);
        request.setAgentCode(reqVO.getAgentCode());
        HotelListPageResponse hotelList = hotelInfoService.findHotelList(request);
        if (Objects.nonNull(hotelList)) {
            List<HotelListResp> hotelListRespList = JSONObject.parseObject(JSONObject.toJSONString(hotelList.getList()), new TypeReference<List<HotelListResp>>() {
            });
            List<HotelRespVO> list = new ArrayList<>();
            for (HotelListResp hotelListResp : hotelListRespList) {
                HotelRespVO resp = HotelConvert.INSTANCE.hotelListRespConvert(hotelListResp);
                resp.setHotelImg(hotelListResp.getMainUrl());
                resp.setMainUrl(null);
                list.add(resp);
            }
            hotelAllListResp.setHotels(list);
            hotelAllListResp.setPageSize(hotelList.getPageSize());
            hotelAllListResp.setCurrentPage(hotelList.getCurrentPage());
            hotelAllListResp.setTotalPage(hotelList.getTotalPage());
            hotelAllListResp.setTotalCount(hotelList.getTotalCount());
        }
        hotelAllListResp.setQueryLowestPriceSwitch(settingsConstant.getQueryHotelProductLowestPriceSwitch());
        return hotelAllListResp;
    }

    @Override
    public Response<HotelSearchResp> getHotelSearch(HotelSearchReq hotelSearchReq) {
        return hotelRemote.getHotelSearch(hotelSearchReq);
    }

    @Override
    public Response<HotelInfoCollectionDTO> queryHotelInfo(HotelInfoCollectionReq req) {
        return hotelRemote.queryHotelInfo(req);
    }
}