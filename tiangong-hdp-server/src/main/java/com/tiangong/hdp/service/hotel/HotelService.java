package com.tiangong.hdp.service.hotel;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.BaseinfoRegionDTO;
import com.tiangong.dto.hotel.AddressRespDTO;
import com.tiangong.dto.hotel.HotelSearchReq;
import com.tiangong.dto.hotel.HotelSearchResp;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.hdp.controller.admin.hotel.vo.*;

import java.util.List;

public interface HotelService {

    /**
     * 查询目的地
     */
    List<AddressRespDTO> searchDestination(DestinationReqVO reqVO);

    /**
     * 获取国际区号
     */
    Response<List<BaseinfoRegionDTO>> getRegion();

    /**
     * 查询酒店产品起价
     */
    HotelLowestPriceRespVO hotelLowestPrice(HotelLowestPriceReqVO reqVO);

    /**
     * 查询酒店列表
     */
    HotelListRespVO hotelList(HotelListReqVO reqVO);

    /**
     * 获取酒店过滤器数据
     */
    Response<HotelSearchResp> getHotelSearch(HotelSearchReq hotelSearchReq);

    /**
     * 查询酒店信息
     */
    Response<HotelInfoCollectionDTO> queryHotelInfo(HotelInfoCollectionReq req);
}
