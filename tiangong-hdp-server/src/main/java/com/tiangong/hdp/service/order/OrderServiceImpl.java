package com.tiangong.hdp.service.order;

import com.tiangong.common.Response;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.HotelOrderRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.entity.base.ResponseResult;
import com.tiangong.entity.request.order.*;
import com.tiangong.entity.response.order.*;
import com.tiangong.enums.ChannelEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hdp.controller.admin.order.vo.*;
import com.tiangong.hdp.convert.OrderConvert;
import com.tiangong.hdp.enums.CanBookStatusEnum;
import com.tiangong.hdp.util.HdpUtil;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.order.OrderModelService;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderModelService orderModelService;

    @Autowired
    private HotelOrderRemote hotelOrderRemote;

    @Override
    public CheckBookingRespVO checkBooking(CheckBookingReqVO reqVO) {
        if (StrUtilX.isEmpty(reqVO.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        CheckBookingRespVO resp = new CheckBookingRespVO();
        CheckBookingInfoRequest request = OrderConvert.INSTANCE.checkBookingInfoRequestConvert(reqVO);
        // 获取客户信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, reqVO.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
        }
        request.setDomesticOrOverseas(agentAccountConfig.getDomesticOrOverseas());
        ResponseResult<CheckBookingInfoResponse> responseResult = orderModelService.checkBooking(request);
        if (responseResult.isError()) {
            HdpUtil.convertError(responseResult.getReturnCode(), responseResult.getReturnMsg());
        }
        CheckBookingInfoResponse checkBookingResponse = responseResult.getBussinessResponse();
        // 比对价格
        if (checkBookingResponse.getCanBook() == CanBookStatusEnum.CAN_BOOK.getCode()) {
            // 可定，判断价格是否一致
            if (checkBookingResponse.getTotalSalePrice().compareTo(reqVO.getTotalSalePrice()) != 0) {
                // 金额不一致，产生变价
                resp = OrderConvert.INSTANCE.checkBookingRespVOConvert(checkBookingResponse);
                resp.setCanBook(CanBookStatusEnum.CHANGE_PRICE.getCode());
            } else {
                // 金额一致，则可定
                resp.setCanBook(CanBookStatusEnum.CAN_BOOK.getCode());
            }
        } else {
            // 不可订
            resp.setCanBook(CanBookStatusEnum.CAN_NOT_BOOK.getCode());
        }
        return resp;
    }

    @Override
    public CreateOrderRespVO createOrder(CreateOrderReqVO reqVO) {
        if (StrUtilX.isEmpty(reqVO.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        CreateOrderInfoRequest request = OrderConvert.INSTANCE.createOrderInfoRequestConvert(reqVO);
        // 获取客户信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, reqVO.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
        }
        request.setDomesticOrOverseas(agentAccountConfig.getDomesticOrOverseas());
        request.setChannelCode(ChannelEnum.HDP.key);
        ResponseResult<CreateOrderInfoResponse> responseResult = orderModelService.createOrder(request);
        if (responseResult.isError()) {
            HdpUtil.convertError(responseResult.getReturnCode(), responseResult.getReturnMsg());
        }
        CreateOrderInfoResponse createOrderInfoResponse = responseResult.getBussinessResponse();
        return OrderConvert.INSTANCE.createOrderRespVOConvert(createOrderInfoResponse);
    }

    @Override
    public CancelOrderRespVO cancelOrder(CancelOrderReqVO reqVO) {
        CancelOrderInfoRequest request = OrderConvert.INSTANCE.cancelOrderInfoRequestConvert(reqVO);
        request.setChannelCode(ChannelEnum.HDP.key);
        ResponseResult<CancelOrderInfoResponse> responseResult = orderModelService.cancelOrder(request);
        if (responseResult.isError()) {
            HdpUtil.convertError(responseResult.getReturnCode(), responseResult.getReturnMsg());
        }
        CancelOrderInfoResponse cancelOrderInfoResponse = responseResult.getBussinessResponse();
        return OrderConvert.INSTANCE.cancelOrderRespVOConvert(cancelOrderInfoResponse);
    }

    @Override
    public OrderDetailRespVO orderDetail(OrderDetailReqVO reqVO) {
        OrderDetailInfoRequest request = OrderConvert.INSTANCE.orderDetailInfoRequestConvert(reqVO);
        request.setChannelCode(ChannelEnum.HDP.key);
        ResponseResult<OrderDetailInfoResponse> responseResult = orderModelService.queryOrderDetail(request);
        if (responseResult.isError()) {
            HdpUtil.convertError(responseResult.getReturnCode(), responseResult.getReturnMsg());
        }
        OrderDetailInfoResponse orderDetailInfoResponse = responseResult.getBussinessResponse();
        OrderDetailRespVO orderDetailRespVO = OrderConvert.INSTANCE.orderDetailRespVOConvert(orderDetailInfoResponse);
        if (orderDetailRespVO != null) {
            // 设置超时时间 10分钟超时，返回剩余秒数，超过返回负数
            long second = DateUtilX.getSecond(DateUtilX.getCurrentDate(), Objects.requireNonNull(DateUtilX.stringToDate(orderDetailRespVO.getCreateTime(), DateUtilX.hour_format)));
            orderDetailRespVO.setPaymentTimeoutTime(second + 600L);
        }
        return orderDetailRespVO;
    }

    @Override
    public PayOrderRespVO payOrder(PayOrderReqVO reqVO) {
        PayOrderInfoRequest request = OrderConvert.INSTANCE.payOrderInfoRequestConvert(reqVO);
        request.setChannelCode(ChannelEnum.HDP.key);
        ResponseResult<PayOrderInfoResponse> responseResult = orderModelService.payOrder(request);
        if (responseResult.isError()) {
            HdpUtil.convertError(responseResult.getReturnCode(), responseResult.getReturnMsg());
        }
        PayOrderInfoResponse payOrderInfoResponse = responseResult.getBussinessResponse();
        return OrderConvert.INSTANCE.payOrderRespVOConvert(payOrderInfoResponse);
    }

    @Override
    public Response<PaginationSupportDTO<OrderSimpleDTO>> orderPage(QueryOrderListDTO dto) {
        Response<PaginationSupportDTO<OrderSimpleDTO>> response = hotelOrderRemote.queryOrderList(dto);
        // 转换错误编码，把no转换成code
        if (response != null && response.isError() && StrUtilX.isNotEmpty(response.getFailCode())) {
            String codeByNo = DhubReturnCodeEnum.getCodeByNo(response.getFailCode());
            if (StrUtilX.isNotEmpty(codeByNo)) {
                response.setFailCode(codeByNo);
            }
        }
        return response;
    }
}
