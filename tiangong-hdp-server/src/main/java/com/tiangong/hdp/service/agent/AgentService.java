package com.tiangong.hdp.service.agent;

import com.tiangong.hdp.controller.admin.agent.vo.AgentListReqVO;
import com.tiangong.hdp.controller.admin.agent.vo.AgentReqVO;
import com.tiangong.hdp.domain.resp.OrgCenterResp;

import java.util.Map;

public interface AgentService {

    /**
     * 查询客户列表
     */
    Map<String, Object> agentList(AgentListReqVO reqVO) throws Exception;

    /**
     * 查询客户详情
     */
    OrgCenterResp agentDetail(AgentReqVO reqVO);
}