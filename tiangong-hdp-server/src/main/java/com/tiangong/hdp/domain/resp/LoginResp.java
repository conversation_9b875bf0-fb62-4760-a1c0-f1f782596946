package com.tiangong.hdp.domain.resp;

import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/11/3 16:16
 */
@Data
@SensitiveClass
public class LoginResp {

    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户手机号
     */
    @EncryptField
    //@Sensitive(type = SensitiveTypeEnum.PHONE_NUM)
    private String userTel;

    /**
     * 国际区号
     */
    private String countryCode;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 企业名称
     */
    private String orgName;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 用户类型 详见UserTypeEnum
     */
    private Integer userType;

    /**
     * 是否是管理员 1是
     */
    private Integer isSuperAdmin;
}
