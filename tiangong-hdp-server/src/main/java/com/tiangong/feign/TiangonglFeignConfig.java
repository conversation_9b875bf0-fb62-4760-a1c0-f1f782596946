package com.tiangong.feign;

import com.tiangong.cloud.common.constant.HttpConstant;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.tiangong.cloud.common.constant.HttpConstant.RequestId;

/**
 * @ author: wang
 * @ date: 2021/4/26 16:08
 * @ Description:  由于使用feign发送请求，会丢失请求头，所以需要实现一个请求拦截器，把旧的请求头同步到新的请求里
 */
@Configuration
public class TiangonglFeignConfig {

    @Bean("requestInterceptor")
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String header = request.getHeader(HttpConstant.HEADER);
                requestTemplate.header(HttpConstant.HEADER, header);
                String requestId = request.getHeader(HttpConstant.RequestId);
                requestTemplate.header(RequestId, requestId);
            }
        };
    }
}
