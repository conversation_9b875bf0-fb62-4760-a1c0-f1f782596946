<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.fuzzyquery.mapper.FuzzyQueryMapper">

    <resultMap id="channelConfigMap" type="com.tiangong.fuzzyquery.dto.FuzzyChannelConfigDTO">
        <result column="channelCode" property="channelCode"/>
        <result column="channelName" property="channelName"/>
        <result column="channelCode" property="itemCode"/>
        <result column="channelName" property="itemName"/>
        <collection property="children" ofType="com.tiangong.fuzzyquery.dto.FuzzyShopConfigDTO">
            <result column="itemCode" property="itemCode"/>
            <result column="shopName" property="itemName"/>
            <result column="shopName" property="shopName"/>
        </collection>
    </resultMap>

    <select id="fuzzyQuerySupplier" parameterType="com.tiangong.fuzzyquery.dto.FuzzyQueryDTO"
            resultType="com.tiangong.fuzzyquery.dto.FuzzySupplierDTO">
        SELECT
        r.org_code supplierCode,
        r.org_name supplierName,
        s.settlement_currency settlementCurrency,
        r.hotel_id hotelId,
        s.s_company_id supplierId,
        s.domestic_or_overseas  domesticOrOverseas
        FROM
        t_org_organization r,
        t_org_company_supplier s
        WHERE
        r.org_id = s.org_id
        AND s.company_code = #{companyCode}
        <if test="null != availableStatus">
            AND s.available_status = 1
        </if>
        <if test="null != supplierName and supplierName != ''">
            AND r.org_name LIKE "%"#{supplierName}"%"
        </if>
        <if test="null != supplierCode and supplierCode != ''">
            <if test="codeLike != null and codeLike == 1">
                AND r.org_code LIKE "%"#{supplierCode}"%"
            </if>
            <if test="codeLike == null or codeLike == 0">
                AND r.org_code = #{supplierCode}
            </if>
        </if>
        <if test="null != domesticOrOverseas and domesticOrOverseas != ''">
            AND s.domestic_or_overseas = #{domesticOrOverseas}
        </if>
        <if test="null != hotelId and hotelId != ''">
            AND exists (select 1 from t_pro_product p,t_pro_sale_status ss
            where p.product_id = ss.product_id
            and p.active = 1
            and ss.active = 1
            and p.supplier_code = r.org_code
            and p.hotel_id = #{hotelId}
            and ss.company_code = #{companyCode})
        </if>
        <if test="supplierType != null and supplierType != -1">
            and r.org_type = #{supplierType}
        </if>
        order by r.org_id desc
    </select>

    <select id="fuzzyQueryAgent" parameterType="com.tiangong.fuzzyquery.dto.FuzzyQueryDTO"
            resultType="com.tiangong.fuzzyquery.dto.FuzzyAgentDTO">
        SELECT
            a.a_company_id      agentId,
            r.org_code        agentCode,
            r.org_name        agentName,
            a.settlement_currency settlementCurrency,
            a.domestic_or_overseas domesticOrOverseas,
            a.balance balance
        FROM
        t_org_organization r,
        t_org_company_agent a
        WHERE
        r.org_id = a.org_id
        AND a.company_code = #{companyCode}
        <if test="availableStatus != -1">
            AND a.available_status = 1
        </if>
        <if test="null != agentName and agentName != ''">
            AND r.org_name LIKE "%"#{agentName}"%"
        </if>
        <if test="null != agentType and agentType != ''">
            AND r.org_type = #{agentType}
        </if>
        <if test="null != agentCode and agentCode != ''">
            <if test="codeLike != null and codeLike == 1">
                AND r.org_code LIKE "%"#{agentCode}"%"
            </if>
            <if test="codeLike == null or codeLike == 0">
                AND r.org_code = #{agentCode}
            </if>
        </if>
        <if test="null != channelCode and channelCode != ''">
            AND r.channel_code = #{channelCode}
        </if>
        <if test="domesticOrOverseas != null">
            AND a.domestic_or_overseas = #{domesticOrOverseas}
        </if>
        <if test="null != settlementCurrency">
            AND a.settlement_currency = #{settlementCurrency}
        </if>
        <if test="null != excludeAgentCode and excludeAgentCode != ''">
            AND r.org_code != #{excludeAgentCode}
        </if>
        order by r.org_id desc
    </select>

    <select id="queryChannelConfig" resultMap="channelConfigMap">
        select
            a.channel_code channelCode,
            a.channel_name channelName
        from t_pro_m_channel m, t_dis_channel_agent a
        where a.company_code = #{companyCode} and a.main_channel = 1 and m.company_code = a.company_code and m.channel_code = a.channel_code
          and m.active = 1 and a.active = 1
    </select>

    <select id="queryAgentChannelCode" resultType="com.tiangong.fuzzyquery.dto.FuzzyChannelConfigDTO">
        SELECT
        c.channel_code channelCode,
        c.channel_name channelName,
        c.channel_code itemCode,
        c.channel_name itemName
        FROM t_pro_m_channel c
        WHERE c.active = 1
        AND c.company_code = #{companyCode}
    </select>

    <select id="queryGroupByGroupCodes" resultType="com.tiangong.dto.hotel.HotelGroupResp"
            parameterType="com.tiangong.hotel.domain.vo.HotelGroupDTO">
        SELECT
            group_id        hotelGroupCode,
            group_name      hotelGroupName
        FROM t_baseinfo_group
        WHERE group_id IN
        <foreach collection="groupCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND language = #{language}
        GROUP BY group_id
    </select>

    <select id="queryBrandByBrandCodes" resultType="com.tiangong.dto.hotel.HotelBrandResp"
            parameterType="com.tiangong.hotel.domain.vo.HotelBrandDTO">
        SELECT
            brand_id        hotelBrandCode,
            brand_name      hotelBrandName
        FROM t_baseinfo_group
        WHERE brand_id IN
        <foreach collection="brandCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND language = #{language}
    </select>

    <select id="getAvailableSupplierInfoByAgentCodeAndSupplierName"
            parameterType="com.tiangong.fuzzyquery.dto.FuzzyQueryDTO"
            resultType="com.tiangong.fuzzyquery.dto.FuzzySupplierDTO">
        select
        too.org_code supplierCode,
        too.org_name supplierName,
        tocs.settlement_currency settlementCurrency,
        too.hotel_id hotelId,
        tocs.s_company_id supplierId,
        tocs.domestic_or_overseas domesticOrOverseas
        from t_pro_org_available tpa
        left join t_org_organization too on tpa.supplier_code = too.org_code
        left join t_org_company_supplier tocs on too.org_id = tocs.org_id
        where tpa.agent_code = #{agentCode}
        <if test="supplierName != null and supplierName != ''">
            and too.org_name like "%"#{supplierName}"%"
        </if>
        and tpa.deleted = 0
        and tpa.available_type = 1
        and tpa.source = 1
        and tocs.available_status = 1
        and tocs.domestic_or_overseas = #{domesticOrOverseas}
    </select>


    <select id="getAvailableSupplierByAgentCodeAndSupplierName"
            parameterType="com.tiangong.fuzzyquery.dto.FuzzyQueryDTO"
            resultType="com.tiangong.fuzzyquery.dto.FuzzySupplierDTO">
        SELECT
        a.org_code supplierCode,
        a.org_name supplierName,
        tocs.settlement_currency settlementCurrency,
        a.hotel_id hotelId,
        tocs.s_company_id supplierId,
        tocs.domestic_or_overseas domesticOrOverseas
        from t_org_organization a ,t_pro_org_available b ,t_org_company_supplier tocs
        where a.org_code = b.supplier_code
        and a.org_id = tocs.org_id
        and a.type = 0
        and tocs.available_status = 1
        and b.source = 2
        and b.agent_code = #{agentCode}
        and b.deleted=0
        and b.available_type = 1
        and tocs.domestic_or_overseas = #{domesticOrOverseas}
        <if test="supplierCode != null and supplierCode != ''">
            and a.org_code = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and a.org_name like "%"#{supplierName}"%"
        </if>
        UNION
        SELECT
        a.org_code supplierCode,
        a.org_name supplierName,
        tocs.settlement_currency settlementCurrency,
        a.hotel_id hotelId,
        tocs.is_cached IsDirectlyConnectedSupplier,
        tocs.s_company_id supplierId,
        tocs.domestic_or_overseas domesticOrOverseas
        from t_org_organization a
        left join t_org_company_supplier tocs on a.org_id = tocs.org_id and tocs.available_status = 1
        LEFT JOIN t_pro_org_available b
        on a.org_code = b.supplier_code
        and b.source = 2 and b.agent_code = #{agentCode} and b.available_type = 2 and b.deleted=0
        where a.type = 0
        and b.supplier_code is null
        and tocs.domestic_or_overseas = #{domesticOrOverseas}
        <if test="supplierCode != null and supplierCode != ''">
            and a.org_code = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and a.org_name like "%"#{supplierName}"%"
        </if>
        and a.org_code not in (SELECT a.org_code
        FROM t_org_organization a,
        t_pro_org_available b
        where a.org_code = b.supplier_code
        AND b.source = 2
        and b.deleted=0
        AND b.agent_code != #{agentCode}
        AND b.available_type = 1
        <if test="supplierName != null and supplierName != ''">
            and a.org_name like "%"#{supplierName}"%"
        </if>
        and a.type = 0 )
    </select>
</mapper>