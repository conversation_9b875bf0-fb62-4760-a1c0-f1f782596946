package com.tiangong.task;

import com.tiangong.hotel.service.HotelAvgPriceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务
 */
@Slf4j
@Component
public class InitCalculateCityAvgPriceScoreHotelIdToRedisTask {

    @Autowired
    private HotelAvgPriceService hotelAvgPriceService;

    @XxlJob("initCalculateCityAvgPriceScoreHotelIdToRedisTask")
    public void initCalculateCityAvgPriceScoreHotelIdToRedisTask() {
        try {
            XxlJobHelper.log("执行初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务开始");
            String param = XxlJobHelper.getJobParam();
            hotelAvgPriceService.initCalculateCityAvgPriceScoreHotelIdToRedisTask(param);
            XxlJobHelper.log("执行初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务结束");
        } catch (Exception e) {
            log.error("执行初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务异常", e);
            XxlJobHelper.log("执行初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务异常", e);
        }
    }
}
