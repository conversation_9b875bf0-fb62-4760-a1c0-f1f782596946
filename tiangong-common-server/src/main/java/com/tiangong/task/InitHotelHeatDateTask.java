package com.tiangong.task;

import com.tiangong.hotel.service.HotelService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 初始化酒店热度数据任务
 */
@Slf4j
@Component
public class InitHotelHeatDateTask {

    @Autowired
    private HotelService hotelService;

    @XxlJob("initHotelHeatDateTask")
    public void initHotelHeatDateTask() {
        try {
            XxlJobHelper.log("执行初始化酒店热度数据任务开始");
            String param = XxlJobHelper.getJobParam();
            hotelService.initHotelHeatDateTask(param);
            XxlJobHelper.log("执行初始化酒店热度数据任务结束");
        } catch (Exception e) {
            log.error("执行初始化酒店热度数据任务异常", e);
            XxlJobHelper.log("执行初始化酒店热度数据任务异常", e);
        }
    }
}
