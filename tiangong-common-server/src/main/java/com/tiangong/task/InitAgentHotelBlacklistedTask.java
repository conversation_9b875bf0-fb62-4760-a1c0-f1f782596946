package com.tiangong.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.hotel.domain.HotelAvailablePO;
import com.tiangong.hotel.mapper.HotelAvailableMapper;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化客户酒店黑名单任务
 */
@Slf4j
@Component
public class InitAgentHotelBlacklistedTask {

    @Autowired
    private HotelAvailableMapper hotelAvailableMapper;

    @XxlJob("initAgentHotelBlacklistedTask")
    public void initAgentHotelBlacklistedTask() {
        try {
            XxlJobHelper.log("执行初始化客户酒店黑名单任务开始");
            List<HotelAvailablePO> poList = hotelAvailableMapper.selectList(new LambdaQueryWrapper<HotelAvailablePO>().eq(HotelAvailablePO::getAvailableType, 0));
            if (CollUtilX.isNotEmpty(poList)) {
                Map<String, String> map = new HashMap<>();
                for (HotelAvailablePO po : poList) {
                    map.put(po.getAgentCode() + "_" + po.getHotelId(), String.valueOf(po.getHotelId()));
                }

                RedisTemplateX.delete(RedisKey.AGENT_HOTEL_BLACKLISTED);
                RedisTemplateX.hashSetAll(RedisKey.AGENT_HOTEL_BLACKLISTED, map);
            }
            XxlJobHelper.log("执行初始化客户酒店黑名单任务结束");
        } catch (Exception e) {
            log.error("执行初始化客户酒店黑名单任务异常", e);
            XxlJobHelper.log("执行初始化客户酒店黑名单任务异常", e);
        }
    }
}
