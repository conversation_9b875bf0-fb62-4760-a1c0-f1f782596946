package com.tiangong.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.hotel.service.HotelAvgPriceService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 初始化需要计算酒店热度城市平均房价的城市编码到缓存任务
 */
@Slf4j
@Component
public class InitCalculateCityAvgPriceCityCodeToRedisTask {

    @Autowired
    private HotelAvgPriceService hotelAvgPriceService;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;

    @XxlJob("initCalculateCityAvgPriceCityCodeToRedisTask")
    public void initCalculateCityAvgPriceCityCodeToRedisTask() {
        try {
            XxlJobHelper.log("执行初始化需要计算酒店热度城市平均房价的城市编码到缓存任务开始");
            List<String> cityCodeList;
            String param = XxlJobHelper.getJobParam();
            String count = null;
            if (StrUtilX.isNotEmpty(param)) {
                Map<String, String> paramMap = JSON.parseObject(param, new TypeReference<Map<String, String>>() {
                });
                cityCodeList = StrUtilX.stringToList(paramMap.get("cityCodes"), ",");
                count = paramMap.get("count");
            } else {
                // 查询城市列表
                cityCodeList = hotelAvgPriceService.queryHotelAvgPriceCityCodes();
            }
            if (CollUtilX.isNotEmpty(cityCodeList)) {
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                // 每批次的大小
                int batchSize = 1000;
                if (StrUtilX.isNotEmpty(count)) {
                    batchSize = Integer.parseInt(count);
                }
                // 计算总批次数量
                int numberOfBatches = (int) Math.ceil((double) cityCodeList.size() / batchSize);
                for (int i = 0; i < numberOfBatches; i++) {
                    final int startIndex = i * batchSize;
                    final int endIndex = Math.min(startIndex + batchSize, cityCodeList.size());

                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        // 截取当前批次的数组
                        String[] batch = cityCodeList.subList(startIndex, endIndex).toArray(new String[0]);

                        // 对每个批次调用 RedisTemplateX.setAdd
                        RedisTemplateX.setAdd(RedisKey.CALCULATE_CITY_AVG_PRICE_CITY_CODE_KEY, batch);
                    }, processingCommonExecutor);
                    futures.add(future);
                }
                // 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                // 清空数据
                cityCodeList.clear();
            }
            // 计算数据之前删除旧缓存数据
            RedisTemplateX.delete(RedisKey.HOTEL_HEAT_CITY_AVG_PRICE_KEY);
            XxlJobHelper.log("执行初始化需要计算酒店热度城市平均房价的城市编码到缓存任务结束");
        } catch (Exception e) {
            log.error("执行初始化需要计算酒店热度城市平均房价的城市编码到缓存任务异常", e);
            XxlJobHelper.log("执行初始化需要计算酒店热度城市平均房价的城市编码到缓存任务异常", e);
        }
    }
}
