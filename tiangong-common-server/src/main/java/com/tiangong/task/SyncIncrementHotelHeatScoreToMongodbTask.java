package com.tiangong.task;

import com.tiangong.hotel.service.HotelHeatService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 同步增量酒店热度分数到mongodb任务
 */
@Slf4j
@Component
public class SyncIncrementHotelHeatScoreToMongodbTask {

    @Autowired
    private HotelHeatService hotelHeatService;

    @XxlJob("syncIncrementHotelHeatScoreToMongodbTask")
    public void syncIncrementHotelHeatScoreToMongodbTask() {
        try {
            XxlJobHelper.log("执行同步增量酒店热度分数到mongodb任务开始");
            String param = XxlJobHelper.getJobParam();
            hotelHeatService.syncIncrementHotelHeatScoreToMongodbTask(param);
            XxlJobHelper.log("执行同步增量酒店热度分数到mongodb任务结束");
        } catch (Exception e) {
            log.error("执行同步增量酒店热度分数到mongodb任务异常", e);
            XxlJobHelper.log("执行同步增量酒店热度分数到mongodb任务异常", e);
        }
    }
}
