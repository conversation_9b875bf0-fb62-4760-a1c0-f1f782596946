package com.tiangong.task;

import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.dto.SupplierLabelDTO;
import com.tiangong.organization.mapper.SupplierLabelConfigMapper;
import com.tiangong.product.remote.ProductRemote;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化供应商标签任务
 */
@Slf4j
@Component
public class InitSupplierLabelTask {

    @Autowired
    private SupplierLabelConfigMapper supplierLabelConfigMapper;

    @Autowired
    private ProductRemote productRemote;

    @XxlJob("InitSupplierLabelTask")
    public void initSupplierLabelTask() {
        try {
            XxlJobHelper.log("执行初始化供应商标签任务开始");
            List<SupplierLabelDTO> dtoList = supplierLabelConfigMapper.querySupplierLabelConfigList();
            if (CollUtilX.isNotEmpty(dtoList)) {
                Map<String, String> labelMap = new HashMap<>(dtoList.size());
                for (SupplierLabelDTO dto : dtoList) {
                    if (StrUtilX.isEmpty(dto.getAgentCode()) || StrUtilX.isEmpty(dto.getSupplierCode()) || dto.getLabelType() == null) {
                        continue;
                    }
                    labelMap.put(dto.getAgentCode() + "_" + dto.getSupplierCode(), dto.getLabelType() + "");
                }
                RedisTemplateX.hashSetAll(RedisKey.AGENT_SUPPLIER_LABEL, labelMap);

                // 刷新客户供应商标签信息
                RedisTemplateX.convertAndSend(RedisKey.REFRESH_AGENT_SUPPLIER_LABEL_INFO, "");
            }
            XxlJobHelper.log("执行初始化供应商标签任务结束");
        } catch (Exception e) {
            log.error("执行初始化供应商标签任务异常", e);
            XxlJobHelper.log("执行初始化供应商标签任务异常", e);
        }
    }
}
