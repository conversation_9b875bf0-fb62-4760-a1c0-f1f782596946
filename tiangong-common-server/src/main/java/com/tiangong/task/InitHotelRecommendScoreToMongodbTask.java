package com.tiangong.task;

import com.tiangong.config.SettingsConstant;
import com.tiangong.dto.hotel.AgentRecommendScoreReq;
import com.tiangong.dto.hotel.RecommendScoreReq;
import com.tiangong.dto.hotel.UpdateHotelScoreReq;
import com.tiangong.hotel.mapper.HotelRecommendMapper;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelRecommendResp;
import com.tiangong.hotel.server.HotelServer;
import com.tiangong.util.CollUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初始化酒店推荐分数到mongodb任务
 */
@Slf4j
@Component
public class InitHotelRecommendScoreToMongodbTask {

    @Autowired
    private HotelRecommendMapper hotelRecommendMapper;

    @Autowired
    private HotelServer hotelServer;

    @Autowired
    private SettingsConstant settingsConstant;

    @XxlJob("initHotelRecommendScoreToMongodbTask")
    public void initHotelRecommendScoreToMongodbTask() {
        try {
            XxlJobHelper.log("执行初始化酒店推荐分数到mongodb任务开始");
            handleHeatRecommendToMongodb();
            XxlJobHelper.log("执行初始化酒店推荐分数到mongodb任务结束");
        } catch (Exception e) {
            log.error("执行初始化酒店推荐分数到mongodb任务异常", e);
            XxlJobHelper.log("执行初始化酒店推荐分数到mongodb任务异常", e);
        }
    }

    /**
     * 处理酒店推荐分数
     */
    private void handleHeatRecommendToMongodb() {
        int offset = 0;

        while (true) {
            HotelRecommendReq hotelRecommendReq = new HotelRecommendReq();
            hotelRecommendReq.setOffset(offset);
            hotelRecommendReq.setBatchSize(settingsConstant.getBatchSize());
            List<HotelRecommendResp> respList = hotelRecommendMapper.selectHotelRecommendScorePage(hotelRecommendReq);

            if (CollUtilX.isEmpty(respList)) {
                break; // 如果没有更多记录，则退出循环
            }

            try {
                List<RecommendScoreReq> recommendScoreReqList = new ArrayList<>();
                Map<Long, List<HotelRecommendResp>> hotelRecommendMap = respList.stream().collect(Collectors.groupingBy(HotelRecommendResp::getHotelId));
                for (Map.Entry<Long, List<HotelRecommendResp>> entry : hotelRecommendMap.entrySet()) {
                    RecommendScoreReq recommendScoreReq = new RecommendScoreReq();
                    recommendScoreReq.setHotelId(entry.getKey());
                    List<AgentRecommendScoreReq> reqList = entry.getValue().stream().map(item -> {
                        AgentRecommendScoreReq agentRecommendScoreReq = new AgentRecommendScoreReq();
                        agentRecommendScoreReq.setAgentCode(item.getAgentCode());
                        agentRecommendScoreReq.setRecommendScore(Double.valueOf(item.getSumRecommendScore()));
                        return agentRecommendScoreReq;
                    }).collect(Collectors.toList());
                    recommendScoreReq.setAgentRecommendScoreReqList(reqList);
                    recommendScoreReqList.add(recommendScoreReq);
                }
                // 更新mongodb公共表里面的数据
                UpdateHotelScoreReq req = new UpdateHotelScoreReq();
                req.setRecommendScoreReqList(recommendScoreReqList);
                hotelServer.updateHotelScoreToMongodbBatch(req);

                // 更新偏移量
                offset += settingsConstant.getBatchSize();

                // 休眠1秒
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("处理酒店推荐分数异常", e);
            }
        }
    }
}
