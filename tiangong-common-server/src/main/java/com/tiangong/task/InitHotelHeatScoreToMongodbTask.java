package com.tiangong.task;

import com.tiangong.config.SettingsConstant;
import com.tiangong.dto.hotel.HeatValueReq;
import com.tiangong.dto.hotel.UpdateHotelScoreReq;
import com.tiangong.hotel.mapper.HotelHeatMapper;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelHeatResp;
import com.tiangong.hotel.server.HotelServer;
import com.tiangong.util.CollUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 初始化酒店热度分数到mongodb任务
 */
@Slf4j
@Component
public class InitHotelHeatScoreToMongodbTask {

    @Autowired
    private HotelHeatMapper hotelHeatMapper;

    @Autowired
    private HotelServer hotelServer;

    @Autowired
    private SettingsConstant settingsConstant;

    @XxlJob("initHotelHeatScoreToMongodbTask")
    public void initHotelHeatScoreToMongodbTask() {
        try {
            XxlJobHelper.log("执行初始化酒店热度分数到mongodb任务开始");
            handleHeatScoreToMongodb();
            XxlJobHelper.log("执行初始化酒店热度分数到mongodb任务结束");
        } catch (Exception e) {
            log.error("执行初始化酒店热度分数到mongodb任务异常", e);
            XxlJobHelper.log("执行初始化酒店热度分数到mongodb任务异常", e);
        }
    }

    /**
     * 处理酒店热度分数
     */
    private void handleHeatScoreToMongodb() {
        int offset = 0;

        while (true) {
            HotelHeatReq hotelHeatReq = new HotelHeatReq();
            hotelHeatReq.setOffset(offset);
            hotelHeatReq.setBatchSize(settingsConstant.getBatchSize());
            List<HotelHeatResp> respList = hotelHeatMapper.selectHotelHeatScorePage(hotelHeatReq);

            if (CollUtilX.isEmpty(respList)) {
                break; // 如果没有更多记录，则退出循环
            }

            try {
                // 更新mongodb公共表里面的数据
                List<HeatValueReq> heatValueReqList = respList.stream().map(item -> {
                    HeatValueReq heatValueReq = new HeatValueReq();
                    heatValueReq.setHotelId(item.getHotelId());
                    heatValueReq.setHeatValue(item.getHeatScore());
                    return heatValueReq;
                }).collect(Collectors.toList());
                UpdateHotelScoreReq req = new UpdateHotelScoreReq();
                req.setHeatValueReqList(heatValueReqList);
                hotelServer.updateHotelScoreToMongodbBatch(req);

                // 更新偏移量
                offset += settingsConstant.getBatchSize();

                // 休眠1秒
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("处理酒店热度分数异常", e);
            }
        }
    }
}
