package com.tiangong.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tiangong.cloud.common.enums.BaseEnum;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.entity.OrgAgentApiConfigEntity;
import com.tiangong.organization.mapper.OrgAgentApiConfigMapper;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.organization.remote.dto.AgentListRequest;
import com.tiangong.organization.remote.dto.QueryAgentListDTO;
import com.tiangong.organization.service.AgentService;
import com.tiangong.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 初始化分销商信息任务
 */
@Slf4j
@Component
public class InitAgentConfigTask {

    @Autowired
    private AgentService agentService;

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private OrgAgentApiConfigMapper orgAgentApiConfigMapper;

    @XxlJob("InitAgentConfigTask")
    public void initAgentConfigTask() {
        try {
            XxlJobHelper.log("执行初始化分销商信息任务开始");
            AgentListRequest request = new AgentListRequest();
            request.setCompanyCode(CompanyDTO.COMPANY_CODE);
            List<QueryAgentListDTO> list = orgMapper.queryAgentList(request);
            HashMap<String, String> map = new HashMap<>();
            list.forEach(i -> map.put(i.getAgentCode(), JSONObject.toJSONString(i)));
            RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, map);

            int partnerNum = 0;
            for (QueryAgentListDTO agent : list) {
                if (agent == null) {
                    continue;
                }
                //查询该客户下面的所有合作商，更新他们的缓存状态
                QueryWrapper<OrgAgentApiConfigEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("agent_code", agent.getAgentCode());
                wrapper.eq("deleted", BaseEnum.NOT_DELETE.getKey());
                List<OrgAgentApiConfigEntity> apiConfigEntityList = orgAgentApiConfigMapper.selectList(wrapper);
                for (OrgAgentApiConfigEntity entity : apiConfigEntityList) {
                    if (entity == null) {
                        continue;
                    }
                    HashMap<String, String> agentMap = new HashMap<>();
                    agentMap.put(entity.getPartnerCode(), JSON.toJSONString(entity));
                    RedisTemplateX.hPutAll(RedisKey.PARTNER_ACCOUNT_CONFIG, agentMap);
                    partnerNum++;
                }
            }
            XxlJobHelper.log("开始初始化合作商信息完成 数量是：" + partnerNum);
            XxlJobHelper.log("执行初始化分销商信息任务结束");
        } catch (Exception e) {
            log.error("执行初始化分销商信息任务异常", e);
            XxlJobHelper.log("执行初始化分销商信息任务异常", e);
        }
    }


    @XxlJob("loadAllAgentApiConfigToCache")
    public void loadAllAgentApiConfigToCache() {
        try {
            XxlJobHelper.log("执行初始化分销商API配置到缓存任务开始");
            agentService.loadAgentApiConfigToCache(null, null);
        } catch (Exception e) {
            log.error("执行初始化分销商API配置到缓存任务异常", e);
            XxlJobHelper.log("执行初始化分销商API配置到缓存任务异常");
        }
    }
}
