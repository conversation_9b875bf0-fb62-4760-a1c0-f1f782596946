package com.tiangong.user.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.user.domain.RolePO;
import com.tiangong.user.domain.req.*;
import com.tiangong.user.domain.resp.MenuListResp;
import com.tiangong.user.service.MenuRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/common/menuRole")
public class MenuRoleServer extends BaseController {

    @Autowired
    private MenuRoleService menuRoleService;

    /**
     * 新增或修改角色
     */
    @PostMapping("addOrUpdateRole")
    public Response<Object> addOrUpdateRole(@RequestBody AddOrUpdateRoleReq req){
        req.setOperator(super.getUserName());
        menuRoleService.addOrUpdateRole(req);
        return Response.success();
    }

    /**
     * 删除角色
     */
    @PostMapping("delRole")
    public Response<Object> delRole(@RequestBody DelRoleReq req){
        menuRoleService.delRole(req);
        return Response.success();
    }

    /**
     * 查看所有角色
     */
    @PostMapping("queryListRole")
    public Response<List<RolePO>> queryListRole(){
        return Response.success(menuRoleService.queryListRole());
    }

    /**
     * 查询所有菜单列表
     */
    @PostMapping("queryListMenu")
    public Response<List<MenuListResp>> queryListMenu(){
        return Response.success(menuRoleService.queryListMenu());
    }

    /**
     * 查看当前角色的菜单id
     */
    @PostMapping("queryListMenuRoleByRoleId")
    public Response<List<MenuListResp>> queryListMenuRoleByRoleId(@RequestBody DelRoleReq req){
        return Response.success(menuRoleService.queryListMenuRoleByRoleId(req));
    }

    /**
     * 获取当前用户的菜单权限
     */
    @PostMapping("queryUserMenuList")
    public Response<List<MenuListResp>> queryUserMenuList(){
        return Response.success(menuRoleService.queryUserMenuList(getUser()));
    }

    /**
     * 修改或新增角色的菜单id
     */
    @PostMapping("addOrUpdateRoleMenu")
    public Response<Object> addOrUpdateRoleMenu(@RequestBody AddOrUpdateRoleMenuReq req){
        req.setOperator(super.getUserName());
        menuRoleService.addOrUpdateRoleMenu(req);
        return Response.success();
    }

    /**
     * 角色配置菜单
     * @param req
     * @return
     */
    @PostMapping("/authRoleMenuConfig")
    public Response<Integer> authRoleMenuConfig(@Valid @RequestBody AuthRoleReq req) {
        req.setOperator(super.getUserName());
        return Response.success(menuRoleService.authRoleMenuConfig(req));
    }

    /**
     * 角色详情
     */
    @PostMapping("/authRoleDetail")
    public Response<AuthRoleResp> authRoleDetail(@RequestBody AuthRoleReq req){
        return Response.success(menuRoleService.authRoleDetail(req.getRoleId()));
    }

}
