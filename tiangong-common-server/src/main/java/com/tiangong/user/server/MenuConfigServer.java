package com.tiangong.user.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.user.domain.MenuConfig;
import com.tiangong.user.service.MenuConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/common/config")
public class MenuConfigServer extends BaseController {

    @Autowired
    private MenuConfigService menuConfigService;

    /**
     * 查询文档
     */
    @PostMapping("/queryNote")
    public Response<String> queryNote(@RequestBody MenuConfig menuConfig) {
        return Response.success(menuConfigService.queryNote(menuConfig.getPath()));
    }

    /**
     * 编辑文档
     */
    @PostMapping("/editNote")
    public Response<Object> editNote(@RequestBody MenuConfig menuConfig) {
        menuConfigService.editNote(menuConfig);
        return Response.success();
    }

}
