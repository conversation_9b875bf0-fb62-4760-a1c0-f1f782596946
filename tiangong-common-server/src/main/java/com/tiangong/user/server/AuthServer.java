package com.tiangong.user.server;

import com.alibaba.fastjson.JSON;
import com.tiangong.ValidatedFilter;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.AdminResultEnum;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.config.SettingsConstant;
import com.tiangong.constant.FieldConstants;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.operatelog.service.OperateLogService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.domain.AuthUser;
import com.tiangong.user.domain.JwtUser;
import com.tiangong.user.service.AuthService;
import com.tiangong.user.service.UserService;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Auther: wangzhong
 * @Date: 2020/4/27 17:13
 * @Description: 认证接口
 */
@Slf4j
@RestController
@RequestMapping("/common/auth")
public class AuthServer extends BaseController {

    private final TokenManager tokenManager;

    private final UserDetailsService userDetailsService;

    @Autowired
    private OperateLogService operateLogService;

    public AuthServer(AuthenticationManagerBuilder authenticationManagerBuilder,
                      TokenManager tokenManager,
                      UserDetailsService userDetailsService) {
        this.tokenManager = tokenManager;
        this.userDetailsService = userDetailsService;
    }

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private UserService userService;

    @Autowired
    private AuthService authService;

    @Autowired
    private SettingsConstant settingsConstant;

    @AnonymousAccess
    @PostMapping("/login")
    public Response<Map<String, Object>> login(HttpServletRequest request, @RequestBody AuthUser authUser) {
        //判断用户是否可以登录
        String orgCode = userService.queryOrgCodeByAccount(authUser.getUserAccount());
        if (StrUtilX.isEmpty(orgCode)) {
            return Response.error(AdminResultEnum.E_90061.code, AdminResultEnum.E_90061.message);
        }

        if (!CompanyDTO.COMPANY_CODE.equals(orgCode)) {
            return Response.error(AdminResultEnum.E_90060.code, AdminResultEnum.E_90060.message);
        }

        String encryptUserPassword = SM4Utils.encrypt(authUser.getUserPassword(), Sm4O.defaultKey);
        if (!ValidatedFilter.loginFailValidated(authUser.getUserAccount())) {
            return Response.error(AdminResultEnum.E_90045.code, AdminResultEnum.E_90045.message);
        }

        // 删除key
        RedisTemplateX.delete(RedisKey.USER_SECRET_KEY.concat(authUser.getUserAccount()));

        // 登录匹配过滤器
        try {
            String verificationCode = authUser.getVerificationCode();
            authUser.setVerificationCode(verificationCode);
            ValidatedFilter.filteringMatching(authUser);
            //验证码用过之后就失效
            RedisTemplateX.delete(ValidatedFilter.VERIFICATION_CODE.concat(authUser.getUserAccount()));
        } catch (Exception e) {
            log.error("登录校验异常", e);
            // 记录登录失败日志
            loginErrorLog(request, authUser.getUserAccount(), AdminResultEnum.getMesbyCode(e.getMessage()));
            if (!ValidatedFilter.loginFailRecord(authUser.getUserAccount())) {
                Map<String, String> requestMap = new HashMap<>();
                requestMap.put(FieldConstants.EMPLOYEE_ACCOUNT, authUser.getUserAccount());
                requestMap.put("accountStatus", "3");
                requestMap.put("ipAddress", super.getIpAddress());
                userService.updateStatus(requestMap);
                consecutiveLogonFailuresLog(request, authUser.getUserAccount());
                return Response.error(AdminResultEnum.E_90045.getCode(), AdminResultEnum.E_90045.getMessage());
            } else {
                return Response.error(e.getMessage(), AdminResultEnum.getMesbyCode(e.getMessage()));
            }
        }

        try {

            String username = authUser.getUserAccount();
            // 使用自定义的密码校验逻辑，获取jwt用户
            authUser.setUserPassword(encryptUserPassword);
            JwtUser jwtUser = authService.isPasswordMatch(authUser);

            try {
                //验证是否可以登录
                userService.checkUserAuth(request, jwtUser);
                userService.updateLastLoginTime(jwtUser.getUserId());
            } catch (Exception e) {
                log.error("登录失败异常", e);
                // 记录登录失败日志
                loginErrorLog(request, authUser.getUserAccount(), AdminResultEnum.getMesbyCode(e.getMessage()));
                if (!ValidatedFilter.loginFailRecord(authUser.getUserAccount())) {
                    Map<String, String> requestMap = new HashMap<>();
                    requestMap.put(FieldConstants.EMPLOYEE_ACCOUNT, authUser.getUserAccount());
                    requestMap.put("accountStatus", "3");
                    requestMap.put("ipAddress", super.getIpAddress());
                    userService.updateStatus(requestMap);
                    consecutiveLogonFailuresLog(request, authUser.getUserAccount());
                    return Response.error(AdminResultEnum.E_90045.getCode(), AdminResultEnum.E_90045.getMessage());
                } else {
                    return Response.error(e.getMessage(), AdminResultEnum.getMesbyCode(e.getMessage()));
                }
            }

            // 创建用户基础信息
            LoginUser loginUser = new LoginUser();
            loginUser.setUserId(jwtUser.getUserId());
            loginUser.setUserAccount(jwtUser.getUserAccount());
            loginUser.setUserName(jwtUser.getUsername());
            loginUser.setFullUserName(jwtUser.getUserAccount() + "(" + jwtUser.getUsername() + ")");
            loginUser.setCompanyCode(jwtUser.getCompanyCode());
            loginUser.setCompanyDomain(jwtUser.getCompanyDomain());
            loginUser.setIsSuperAdmin(jwtUser.getIsSuperAdmin());
            loginUser.setIsNotePermission(jwtUser.getIsNotePermission());

            //是否要更新密码
            if (jwtUser.getUpdatePwdTime() == null ||
                    DateUtilX.compare(DateUtilX.getDate(jwtUser.getUpdatePwdTime(), 3), DateUtilX.getCurrentDate()) < 1) {
                loginUser.setRestPwd(1);
            }

            String user = JSON.toJSONString(loginUser);
            // 生成令牌
            String token = tokenManager.createToken(user);
            // 没有用到roles，存ip
            if (settingsConstant.getAccountToken() == 0) {
                String result = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, loginUser.getUserAccount()));
                RedisTemplateX.delete(result);
            }
            RedisTemplateX.setAndExpire(RedisKey.LOGIN_TOKEN + token, IpUtil.getIpAddress(request), tokenManager.getTokenTime());
            RedisTemplateX.setAndExpire(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN, IpUtil.getIpAddress(request)), username, tokenManager.getTokenTime());
            RedisTemplateX.setAndExpire(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, username), token, tokenManager.getTokenTime());
            RedisTemplateX.setAndExpire(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, loginUser.getUserAccount()), token, tokenManager.getTokenTime());

            //查询t_auth_menu，得到用户对应有哪些接口权限，保存到redis
            // 返回 token 与 用户信息
            Map<String, Object> authInfo = new LinkedHashMap<String, Object>(2) {{
                put("token", token);
                put(FieldConstants.USER, loginUser);
            }};

            // 成功登录 删除登录失败记录 删除验证码
            RedisTemplateX.delete(ValidatedFilter.LOGIN_FAIL.concat(username));

            // 记录日志
            loginLog(request, loginUser.getFullUserName(), loginUser.getUserAccount());

            //判断是否有权限登录

            return Response.success(authInfo);
        } catch (BadCredentialsException | InternalAuthenticationServiceException | SysException e) {
            log.error("登录失败,err:{}", e.getMessage());
            // 记录登录失败日志
            loginErrorLog(request, authUser.getUserAccount(), AdminResultEnum.E_90006.getMessage());
            if (!ValidatedFilter.loginFailRecord(authUser.getUserAccount())) {
                Map<String, String> requestMap = new HashMap<>();
                requestMap.put(FieldConstants.EMPLOYEE_ACCOUNT, authUser.getUserAccount());
                requestMap.put("accountStatus", "3");
                requestMap.put("ipAddress", super.getIpAddress());
                userService.updateStatus(requestMap);
                consecutiveLogonFailuresLog(request, authUser.getUserAccount());
                return Response.error(AdminResultEnum.E_90045.getCode(), AdminResultEnum.E_90045.getMessage());
            } else {
                return Response.error(AdminResultEnum.E_90006.getCode(), AdminResultEnum.E_90006.getMessage());
            }
        }
    }


    /**
     * 用户退出
     */
    @AnonymousAccess
    @PostMapping("/logout")
    public Response<Object> logout(HttpServletRequest request) {
        //删除redis的token
        String token = TokenManager.getToken(request);
        if (StrUtilX.isNotEmpty(token)) {
            RedisTemplateX.delete(RedisKey.LOGIN_TOKEN + token);
            RedisTemplateX.delete(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN, IpUtil.getIpAddress(request)));
            RedisTemplateX.delete(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, super.getLoginName()));
            logoutLog(request, super.getUser().getFullUserName(), super.getUser().getUserAccount());
        }
        return Response.success();
    }

    /**
     * 查询用户信息
     */
    @GetMapping("/info")
    public Response<UserDetails> getUserInfo(HttpServletRequest request) {
        LoginUser loginUser = TokenManager.getUser(request);
        UserDetails userDetails = userDetailsService.loadUserByUsername(loginUser.getUserAccount());
        return Response.success(userDetails);
    }

    /**
     * 连续登陆失败记录到日志
     */
    public void consecutiveLogonFailuresLog(HttpServletRequest request, String userAccount) {
        OperateLogReqDTO operateLogReqDTO = new OperateLogReqDTO();
        String ip = IpUtil.getIpAddress(request);
        String ua = request.getHeader("User-Agent");
        operateLogReqDTO.setLogName("连续登录失败" + ValidatedFilter.maxFileCount() + "次,账号已被锁定");
        operateLogReqDTO.setLogType("error");
        operateLogReqDTO.setLogLevel(0);
        operateLogReqDTO.setOperationType(20);
        operateLogReqDTO.setOperationResult(0);
        operateLogReqDTO.setCreatedDt(LocalDateTime.now());
        operateLogReqDTO.setApplicationName("base");
        operateLogReqDTO.setRequestMethod(request.getMethod());
        operateLogReqDTO.setRequestIp(ip);
        operateLogReqDTO.setRequestUrl(request.getRequestURI());
        operateLogReqDTO.setRequestHeader(ua);
        operateLogReqDTO.setCreatedBy(userAccount);
        operateLogReqDTO.setUserAccount(userAccount);
        operateLogService.operateLogAdd(operateLogReqDTO);
    }

    /**
     * 登录记录
     */
    public void loginLog(HttpServletRequest request, String fullUserName, String userAccount) {
        OperateLogReqDTO operateLogReqDTO = new OperateLogReqDTO();
        String ip = IpUtil.getIpAddress(request);
        String ua = request.getHeader("User-Agent");
        operateLogReqDTO.setLogName("登录");
        operateLogReqDTO.setLogType("info");
        operateLogReqDTO.setLogLevel(0);
        operateLogReqDTO.setOperationType(8);
        operateLogReqDTO.setOperationResult(1);
        operateLogReqDTO.setCreatedDt(LocalDateTime.now());
        operateLogReqDTO.setApplicationName("base");
        operateLogReqDTO.setRequestIp(ip);
        operateLogReqDTO.setRequestHeader(ua);
        operateLogReqDTO.setCreatedBy(fullUserName);
        operateLogReqDTO.setRequestMethod(request.getMethod());
        operateLogReqDTO.setRequestUrl(request.getRequestURI());
        operateLogReqDTO.setUserAccount(userAccount);
        operateLogService.operateLogAdd(operateLogReqDTO);
    }

    /**
     * 登录失败记录
     */
    public void loginErrorLog(HttpServletRequest request, String userAccount, String message) {
        OperateLogReqDTO operateLogReqDTO = new OperateLogReqDTO();
        String ip = IpUtil.getIpAddress(request);
        String ua = request.getHeader("User-Agent");
        operateLogReqDTO.setLogName("登录失败原因：" + message);
        operateLogReqDTO.setLogType("error");
        operateLogReqDTO.setLogLevel(0);
        operateLogReqDTO.setOperationType(8);
        operateLogReqDTO.setOperationResult(0);
        operateLogReqDTO.setCreatedDt(LocalDateTime.now());
        operateLogReqDTO.setApplicationName("base");
        operateLogReqDTO.setRequestIp(ip);
        operateLogReqDTO.setRequestHeader(ua);
        operateLogReqDTO.setCreatedBy(userAccount);
        operateLogReqDTO.setRequestMethod(request.getMethod());
        operateLogReqDTO.setRequestUrl(request.getRequestURI());
        operateLogReqDTO.setUserAccount(userAccount);
        operateLogService.operateLogAdd(operateLogReqDTO);
    }

    /**
     * 登出记录
     */
    public void logoutLog(HttpServletRequest request, String fullUserName, String userAccount) {
        OperateLogReqDTO operateLogReqDTO = new OperateLogReqDTO();
        String ip = IpUtil.getIpAddress(request);
        String ua = request.getHeader("User-Agent");
        operateLogReqDTO.setLogName("退出登录");
        operateLogReqDTO.setLogType("info");
        operateLogReqDTO.setLogLevel(0);
        operateLogReqDTO.setOperationType(16);
        operateLogReqDTO.setOperationResult(1);
        operateLogReqDTO.setCreatedDt(LocalDateTime.now());
        operateLogReqDTO.setApplicationName("base");
        operateLogReqDTO.setRequestMethod(request.getMethod());
        operateLogReqDTO.setRequestIp(ip);
        operateLogReqDTO.setRequestHeader(ua);
        operateLogReqDTO.setCreatedBy(fullUserName);
        operateLogReqDTO.setRequestUrl(request.getRequestURI());
        operateLogReqDTO.setRequestParam("");
        operateLogReqDTO.setUserAccount(userAccount);
        operateLogService.operateLogAdd(operateLogReqDTO);
    }


    /**
     * 获取验证码
     */
    @AnonymousAccess
    @PostMapping("/sendVerificationCode")
    public Response<String> sendVerificationCode(@RequestBody AuthUser authUser) {
        if (StringUtils.isEmpty(authUser.getUserAccount())) {
            return Response.error(AdminResultEnum.E_90008.code, AdminResultEnum.E_90008.message);
        }
        if (StringUtils.isEmpty(authUser.getUserPassword())) {
            return Response.error(AdminResultEnum.E_90009.code, AdminResultEnum.E_90009.message);
        }
        String encryptUserPassword = SM4Utils.encrypt(authUser.getUserPassword(), Sm4O.defaultKey);
        if (!ValidatedFilter.loginFailValidated(authUser.getUserAccount())) {
            return Response.error(AdminResultEnum.E_90045.code, AdminResultEnum.E_90045.message);
        }

        // 删除key
        RedisTemplateX.delete(RedisKey.USER_SECRET_KEY.concat(authUser.getUserAccount()));
        try {
            // 使用自定义的密码校验逻辑，获取jwt用户
            authUser.setUserPassword(encryptUserPassword);
            JwtUser jwtUser = authService.isPasswordMatch(authUser);

            //验证是否可以登录
            userService.checkUserAuth(request, jwtUser);
        } catch (Exception e) {
            log.error("获取验证码异常", e);
            loginErrorLog(request, authUser.getUserAccount(), AdminResultEnum.getMesbyCode(e.getMessage()));
            if (!ValidatedFilter.loginFailRecord(authUser.getUserAccount())) {
                Map<String, String> requestMap = new HashMap<>();
                requestMap.put(FieldConstants.EMPLOYEE_ACCOUNT, authUser.getUserAccount());
                requestMap.put("accountStatus", "3");
                requestMap.put("ipAddress", super.getIpAddress());
                userService.updateStatus(requestMap);
                consecutiveLogonFailuresLog(request, authUser.getUserAccount());
                return Response.error(AdminResultEnum.E_90045.getCode(), AdminResultEnum.E_90045.getMessage());
            } else {
                return Response.error(e.getMessage(), AdminResultEnum.getMesbyCode(e.getMessage()));
            }
        }
        return userService.sendVerificationCode(authUser);
    }

    /**
     * 获取验证码时间
     */
    @AnonymousAccess
    @GetMapping("/getVerificationCodeTime")
    public Response<Long> getVerificationCodeTime(@RequestParam("userAccount") String userAccount) {
        return userService.getVerificationCodeTime(userAccount);
    }

}
