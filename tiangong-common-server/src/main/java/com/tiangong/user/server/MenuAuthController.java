package com.tiangong.user.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.user.domain.req.AuthMenuAditReq;
import com.tiangong.user.domain.req.AuthMenuTreeReq;
import com.tiangong.user.domain.resp.AuthMenuResp;
import com.tiangong.user.service.MenuAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 菜单表
 */
@RestController
@RequestMapping("/common/menu")
public class MenuAuthController extends BaseController {

    @Autowired
    private MenuAuthService menuAuthService;

    @PostMapping("/authMenuAdit")
    public Response<String> authMenuAdit(@Valid @RequestBody AuthMenuAditReq req){
        return Response.success(menuAuthService.authMenuAdd(req));
    }

    /**
     * 菜单Mind
     * @param reqVO
     * @return
     */
    @PostMapping("/authMenuMind")
    public Response<String> authMenuMind(@Valid @RequestBody AuthMenuTreeReq reqVO) {
        return Response.success(menuAuthService.authMenuMind(reqVO.getHasPermission()));
    }

    /**
     * 构建
     */
    @PostMapping("/authMenuTree")
    public Response<List<AuthMenuResp>> authMenuTree(){
        List<AuthMenuResp> list = menuAuthService.authMenuTree();
        return Response.success(list);
    }

    /**
     * 菜单Router
     * @return
     */
    @PostMapping("/authMenuRouter")
    public Response<List<Map<String, Object>>> authMenuRouter() {
        return Response.success(menuAuthService.authMenuRouter(getUserId()));
    }


}
