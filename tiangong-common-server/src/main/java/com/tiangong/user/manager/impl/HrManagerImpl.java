package com.tiangong.user.manager.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.dis.common.CommonUtils;
import com.tiangong.enums.SlsEnum;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.user.HrSystemUrl;
import com.tiangong.user.domain.HrRequest;
import com.tiangong.user.domain.HrResponse;
import com.tiangong.user.domain.req.SystemLogAddReq;
import com.tiangong.user.domain.req.UpdateAccountStatusReq;
import com.tiangong.user.domain.req.UserSubmitApprovalReq;
import com.tiangong.user.manager.HrManager;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.SlsLoggerUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("hrManager")
public class HrManagerImpl implements HrManager {

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    public <T> HrResponse<T> postForObject(Object requestClass, Class<T> responseType, String requestUrl, OrgPO company) {
        long startTime = System.currentTimeMillis();
        String url = null;
        String request = null;
        String result = null;
        // 错误描述
        String errorMsg = null;
        // 错误详细信息
        String error = null;
        // 开始时间
        Date start = new Date();
        try {

            // 拼接路径
            url = company.getHrUrl() + requestUrl;

            HrRequest<Object> requestCommon = new HrRequest<>();
            requestCommon.setData(requestClass);
            long timestamp = System.currentTimeMillis();
            String signature = CommonUtils.rsa256Sign(JSON.toJSONString(requestClass), company.getHrPrivateKey());
            requestCommon.setSignature(signature);
            requestCommon.setTimestamp(timestamp);
            requestCommon.setSystemCode(company.getHrSystemCode());

            request = JSONUtil.toJsonStr(requestCommon);
            // 请求hr接口
            result = HttpUtil.post(url, request);
            log.info("url={}, request={}, response={}", url, request, result);

            // 转换需要的对象
            HrResponse<String> resultDto = JSON.parseObject(result, new TypeReference<HrResponse<String>>() {
            });

            if (resultDto.getResult() == 0){
                return HrResponse.error(resultDto.getCode(), resultDto.getMsg());
            }
            T responseData = JSON.parseObject(resultDto.getData(), responseType);

            return HrResponse.success(responseData);
        } catch (Exception e) {
            // 记录其他异常
            errorMsg = e.getMessage();
            error = StrUtilX.getStackTraceAsString(e);
            log.error("url:{} 耗时 {}ms，发生异常，参数：{}，结果：{}", url, System.currentTimeMillis() - startTime, request, result, e);
        } finally {
            // 记录SLS（Structured Logging Service）日志信息
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), "info");
            map.put(SlsEnum.NAME.getType(), requestUrl);
            map.put(SlsEnum.MESSAGE.getType(), "异常信息：" + (errorMsg == null ? "none" : errorMsg));

            map.put("url", url);
            map.put("request", request);
            map.put("response", result);
            map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
            if (StrUtilX.isNotEmpty(error)) {
                map.put("error", error);
            }

            slsLoggerUtil.saveLog(map, "调用hr接口", "tiangong-common-server");
        }
        return HrResponse.error();
    }

    @Override
    public HrResponse<String> wxSubmitApprovalTemplate(UserSubmitApprovalReq req, OrgPO company) {
        return postForObject(req, String.class, HrSystemUrl.WX_SUBMIT_APPROVAL_TEMPLATE_URL, company);
    }

    @Override
    public HrResponse<String> updateAccountStatus(UpdateAccountStatusReq req, OrgPO company) {
        return postForObject(req, String.class, HrSystemUrl.UPDATE_ACCOUNT_STATUS_RUL, company);
    }

    @Override
    public HrResponse<String> systemLogAdd(SystemLogAddReq req, OrgPO company) {
        return postForObject(req, String.class, HrSystemUrl.SYSTEM_LOG_ADD_URL, company);
    }
}
