package com.tiangong.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.ValidatedFilter;
import com.tiangong.enums.EmployeeAccountStatusEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.organization.domain.UserApplyPO;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.domain.UserRolePO;
import com.tiangong.user.dto.EmployeeRoleDTO;
import com.tiangong.user.dto.EmployeeUserDTO;
import com.tiangong.user.dto.UserApplyDTO;
import com.tiangong.user.mapper.UserApplyMapper;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.user.mapper.UserRoleMapper;
import com.tiangong.user.service.UserApplyService;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserApplyServiceImpl implements UserApplyService {

    @Autowired
    private UserApplyMapper userApplyMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Override
    public PageInfo<UserApplyDTO> queryUserApplyList(Map<String, String> requestMap) {
        PageHelper.startPage(Integer.parseInt(requestMap.get(CommonConstants.PARAM_CURRENT_PAGE)), Integer.parseInt(requestMap.get(CommonConstants.PARAM_PAGE_SIZE)));
        List<UserApplyDTO> userApplyList = userApplyMapper.queryUserApplyList(requestMap);
        return new PageInfo<>(userApplyList);
    }

    @Override
    public int userApply(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("approvalStatus")) || StrUtilX.isEmpty(requestMap.get("applyId"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        UserApplyPO userApplyPO = userApplyMapper.selectByPrimaryKey(Integer.valueOf(requestMap.get("applyId")));
        if (userApplyPO == null || userApplyPO.getApprovalStatus() != 0) {
            return -1;
        }

        // 验重
        if (userApplyPO.getOperationType() == 1) {
            boolean flag = false;
            UserPO queryUserPO = new UserPO();
            queryUserPO.setUserId(userApplyPO.getUserId());
            if (userApplyPO.getOperationContent().contains("手机号")) {
                queryUserPO.setUserTel(userApplyPO.getUserTel());
                flag = true;
            }
            if (userApplyPO.getOperationContent().contains("身份证")) {
                queryUserPO.setUserIDNumber(userApplyPO.getUserIDNumber());
                flag = true;
            }
            if (flag) {
                int count = userMapper.queryUserCount(queryUserPO);
                if (count > 0) {
                    return -2;
                }
            }
        }

        int approvalStatus = Integer.parseInt(requestMap.get("approvalStatus"));
        String modifiedUser = requestMap.get("modifiedUser");

        // 审核通过
        if (approvalStatus == 1) {
            UserPO userPO = new UserPO();
            userPO.setUserId(userApplyPO.getUserId());
            userPO.setUpdatedBy(modifiedUser);
            userPO.setUpdatedDt(DateUtilX.dateToString(new Date(), CommonConstants.DATE_TIME_FORMAT));
            if (userApplyPO.getOperationType() == 0) {
                // 审核通过激活员工
                userPO.setActive(1);
                userPO.setAvailableStatus(1);
                userPO.setLastLoginTime(new Date());
                userPO.setAccountStatus(EmployeeAccountStatusEnum.VALID.no);
            } else {
                //修改员工密码
                if (userApplyPO.getOperationContent().contains("密码")) {
                    userPO.setUserPwd(userApplyPO.getUserPwd());
                    userPO.setUpdatePwdTime(DateUtilX.getDate(new Date(), -4));

                    // 密码加签
                    userPO.setSignaturePwd(SM3Utils.encrypt(userApplyPO.getUserPwd()));

                    // 修改用户密码成功需重新登录
                    if (RedisTemplateX.hasKey(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(userApplyPO.getUserAccount()))) {
                        String token = RedisTemplateX.get(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(userApplyPO.getUserAccount()));
                        String ip = RedisTemplateX.get(token);
                        RedisTemplateX.delete(token);
                        RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN.concat(ip));
                        RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(userApplyPO.getUserAccount()));
                    }
                } else {
                    // 修改员工
                    BeanUtils.copyProperties(userApplyPO, userPO);

                    List<EmployeeRoleDTO> list = JSON.parseObject(userApplyPO.getUserRoles(), new TypeReference<List<EmployeeRoleDTO>>() {
                    });
                    List<UserPO> userPOS = Collections.singletonList(new UserPO(userPO.getUserId()));
                    List<EmployeeRoleDTO> employeeRoleDTOS = userMapper.getRoleListByUserId(userPOS);

                    Map<Integer, EmployeeRoleDTO> roleMap = employeeRoleDTOS.stream().collect(Collectors.toMap(EmployeeRoleDTO::getEmployeeRoleId, Function.identity(), (key1, key2) -> key2));

                    List<UserRolePO> addEmployeeUserRolePOS = new ArrayList<UserRolePO>();
                    List<UserRolePO> delEmployeeUserRolePOS = new ArrayList<UserRolePO>();
                    if (list != null && list.size() > 0) {
                        for (EmployeeRoleDTO employeeRoleDTO : list) {
                            if (roleMap.containsKey(employeeRoleDTO.getEmployeeRoleId())) {
                                roleMap.remove(employeeRoleDTO.getEmployeeRoleId());
                            } else {
                                UserRolePO userRolePO = new UserRolePO();
                                userRolePO.setUserId(userPO.getUserId());
                                userRolePO.setRoleId(employeeRoleDTO.getEmployeeRoleId());
                                userRolePO.setCreatedBy(modifiedUser);
                                userRolePO.setCreatedDt(DateUtilX.dateToString(new Date(), CommonConstants.DATE_TIME_FORMAT));
                                addEmployeeUserRolePOS.add(userRolePO);
                            }
                        }

                        if (CollUtilX.isNotEmpty(addEmployeeUserRolePOS)) {
                            userRoleMapper.insertList(addEmployeeUserRolePOS);
                        }

                        if (roleMap.size() > 0) {
                            for (EmployeeRoleDTO employeeRoleDTO : roleMap.values()) {
                                UserRolePO deleteUserRole = new UserRolePO();
                                deleteUserRole.setId(employeeRoleDTO.getUserRoleId());

                                delEmployeeUserRolePOS.add(deleteUserRole);
                            }
                            userMapper.deleteUserRole(delEmployeeUserRolePOS);
                        }
                    }
                }
            }
            userMapper.updateByPrimaryKeySelective(userPO);
        } else {
            // 审核驳回
            if (userApplyPO.getOperationType() == 0) {
                // 审核不通过，员工无效
                UserPO userPO = new UserPO();
                userPO.setUserId(userApplyPO.getUserId());
                userPO.setActive(0);
                userPO.setAvailableStatus(0);
                userPO.setAccountStatus(EmployeeAccountStatusEnum.NO_AVAIL.no);
                userMapper.updateByPrimaryKeySelective(userPO);
            }
            userApplyPO.setRefusedReason(requestMap.get("refusedReason"));
        }
        userApplyPO.setApprovalStatus(approvalStatus);
        userApplyPO.setUpdatedBy(modifiedUser);
        userApplyPO.setUpdatedDt(DateUtilX.dateToString(new Date(), CommonConstants.DATE_TIME_FORMAT));
        userApplyMapper.updateByPrimaryKeySelective(userApplyPO);
        return 1;
    }

    @Override
    public EmployeeUserDTO queryUserApplyDetail(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("applyId"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        EmployeeUserDTO employeeUserDTO = userApplyMapper.queryUserApplyDetail(requestMap);
        List<EmployeeRoleDTO> list = JSON.parseObject(employeeUserDTO.getUserRoles(), new TypeReference<List<EmployeeRoleDTO>>() {
        });
        employeeUserDTO.setEmployeeRoleList(list);
        return employeeUserDTO;
    }
}
