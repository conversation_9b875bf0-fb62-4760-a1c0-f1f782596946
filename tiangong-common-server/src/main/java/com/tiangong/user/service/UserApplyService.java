package com.tiangong.user.service;

import com.github.pagehelper.PageInfo;
import com.tiangong.user.dto.EmployeeUserDTO;
import com.tiangong.user.dto.UserApplyDTO;

import java.util.Map;

public interface UserApplyService {

    /**
     * 查询员工增改申请列表
     */
    PageInfo<UserApplyDTO> queryUserApplyList(Map<String, String> requestMap);

    /**
     * 审核员工
     */
    int userApply(Map<String, String> requestMap);

    /**
     * 查询员工申请信息
     */
    EmployeeUserDTO queryUserApplyDetail(Map<String, String> requestMap);
}
