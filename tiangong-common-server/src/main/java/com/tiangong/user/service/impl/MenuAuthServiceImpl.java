package com.tiangong.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.cloud.common.enums.BaseEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.NavBarEnum;
import com.tiangong.exception.SysException;
import com.tiangong.user.domain.AuthMenu2;
import com.tiangong.user.domain.req.AuthMenuQueryReq;
import com.tiangong.user.mapper.MenuAuthMapper;
import com.tiangong.user.service.UserService;
import com.tiangong.util.TreeUtilX;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.convert.AuthMenuConvert;
import com.tiangong.user.domain.req.AuthMenuAditReq;
import com.tiangong.user.domain.resp.AuthMenuResp;
import com.tiangong.user.service.MenuAuthService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单表
 */
@Slf4j
@Service
public class MenuAuthServiceImpl implements MenuAuthService {

    private static int sort = 1;

    private static String key = "menu:permission:config";

    @Autowired
    private MenuAuthMapper menuAuthMapper;

    @Autowired
    private UserService userService;

    private final static String IS_ADMIN = "1";

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public String authMenuAdd(AuthMenuAditReq reqVO) {

        String menuTreeStr = reqVO.getMenuList();
        if (StrUtilX.isEmpty(menuTreeStr)) {
            throw new SysException(ErrorCodeEnum.AUTH_MENU_IS_NOT_EMPTY);
        }

        List<Map> resMap = new ArrayList<>();
        long menuSeq = 10000L;
        Long max = menuAuthMapper.max();
        if (max != null) {
            menuSeq = max;
        }
        menuAuthMapper.delete(null);
        List<AuthMenu2> list2 = iteraJson2(menuTreeStr, menuSeq);
        menuAuthMapper.insertList(list2);
        String resJson = JSON.toJSONString(resMap);

        // 删除redis的按钮配置
        RedisTemplateX.deleteKey(key);
        return resJson;
    }


    private List<AuthMenu2> iteraJson2(String menuTreeStr, long menuSeq) {
        List<AuthMenu2> newDOs = new ArrayList<>();
        // 处理1级菜单
        JSONArray jasonArray = JSONObject.parseArray(menuTreeStr);

        for (int i = 0; i < jasonArray.size(); i++) {
            sort += 1;
            JSONObject jsonObject = jasonArray.getJSONObject(i);
            if (jsonObject.containsKey("itemName")) {
                String itemId = jsonObject.get("itemId") == null ? "" : jsonObject.get("itemId").toString();
                String itemName = jsonObject.get("itemName") == null ? "" : jsonObject.get("itemName").toString();
                String remark = jsonObject.get("remark") == null ? "" : jsonObject.get("remark").toString();
                if (StrUtilX.isEmpty(itemName) && StrUtilX.isEmpty(remark)) {
                    continue;
                }

                // 获取子数据
                String children = jsonObject.get("children") == null ? "" : jsonObject.get("children").toString();

                AuthMenu2 authMenuDO = new AuthMenu2();

                authMenuDO.setMenuId(itemId);

                authMenuDO.setMenuName(itemName);
                authMenuDO.setPath(remark);

                if (StrUtilX.isEmpty(itemId)) {
                    // 新增的菜单
                    menuSeq += 1;
                    authMenuDO.setMenuId(menuSeq + "");
                    authMenuDO.setMenuSeq(menuSeq);
                } else {
                    authMenuDO.setMenuSeq(Long.parseLong(itemId));
                }

                authMenuDO.setParentItemId("0");
                authMenuDO.setParentItemIds("0");

                if (StrUtilX.isEmpty(remark)) {
                    // 没路劲是目录
                    authMenuDO.setType(1);
                } else {
                    // 有路劲是菜单
                    authMenuDO.setType(2);
                }
                authMenuDO.setSort(sort);
                newDOs.add(authMenuDO);

                List<String> pidList = new ArrayList<>();
                pidList.add(authMenuDO.getMenuId());
                if (StrUtilX.isNotEmpty(children)) {
                    menuSeq = digui(children, pidList, newDOs, menuSeq);
                }
            }
        }

        return newDOs;
    }

    private long digui(String menuTreeStr, List<String> pidList, List<AuthMenu2> newDOs, long menuSeq) {

        // 获取所有子集
        JSONArray jasonArray = JSONObject.parseArray(menuTreeStr);

        for (int i = 0; i < jasonArray.size(); i++) {
            sort += 1;
            JSONObject jsonObject = jasonArray.getJSONObject(i);
            if (jsonObject.containsKey("itemName")) {
                String itemId = jsonObject.get("itemId") == null ? "" : jsonObject.get("itemId").toString();
                String itemName = jsonObject.get("itemName") == null ? "" : jsonObject.get("itemName").toString();
                String remark = jsonObject.get("remark") == null ? "" : jsonObject.get("remark").toString();
                if (StrUtilX.isEmpty(itemName) && StrUtilX.isEmpty(remark)) {
                    continue;
                }

                // 获取子数据
                String children = jsonObject.get("children") == null ? "" : jsonObject.get("children").toString();

                AuthMenu2 authMenuDO = new AuthMenu2();
                authMenuDO.setMenuId(itemId);
                authMenuDO.setMenuName(itemName);
                authMenuDO.setPath(remark);

                if (StrUtilX.isEmpty(itemId)) {
                    // 新增的菜单
                    menuSeq += 1;
                    authMenuDO.setMenuId(menuSeq + "");
                    authMenuDO.setMenuSeq(menuSeq);
                } else {
                    authMenuDO.setMenuSeq(Long.parseLong(itemId));
                }

                String pids = CollUtilX.getPids(pidList);
                String pid = pidList.get(pidList.size() - 1);
                authMenuDO.setParentItemId(pid);
                authMenuDO.setParentItemIds(pids);

                String[] split = itemName.split("-");
                if (split.length == 2) {
                    // 按钮
                    authMenuDO.setType(3);
                    authMenuDO.setPermissions(split[1]);
                } else {
                    if (StrUtilX.isEmpty(remark)) {
                        // 没路劲是目录
                        authMenuDO.setType(1);
                    } else {
                        // 有路劲是菜单
                        authMenuDO.setType(2);
                    }
                }

                authMenuDO.setSort(sort);
                newDOs.add(authMenuDO);

                if (StrUtilX.isNotEmpty(children)) {
                    List<String> cpidList = new ArrayList<>();
                    cpidList.addAll(pidList);
                    cpidList.add(authMenuDO.getMenuId());
                    menuSeq = digui(children, cpidList, newDOs, menuSeq);
                }
            }
        }
        return menuSeq;
    }


    /**
     * 根据用户id，获取树形菜单
     * <p>
     * rank：
     */
    private List<Map<String, Object>> buildMenu(Integer userId) {

        Integer isAdmin = 0;
        //获取用户的角色id
        List<String> roleIds = userService.getRoleIdByUserId(userId);
        if (CollUtilX.isEmpty(roleIds)) {
            throw new SysException(ErrorCodeEnum.AUTH_LOGIN_BAD_ROLE);
        }
        if (roleIds.get(0).equals(IS_ADMIN)){
            isAdmin = 1;
        }

        List<AuthMenu2> list;
        if (isAdmin == BaseEnum.ADMIN.getKey()) {
            // 是admin用户，获取所有菜单,1，2级菜单
            AuthMenuQueryReq authMenuQueryReq = new AuthMenuQueryReq();
            authMenuQueryReq.setTypeList(Arrays.asList(1, 2));
            list = menuAuthMapper.selectList(authMenuQueryReq);
        } else {
            // 不是admin, 获取当前用户角色对应的菜单

            // 根据角色id，获取当前用户角色对应的菜单
            Collection<AuthMenu2> menuDOS = menuAuthMapper.getMenuByRoleId(roleIds);
            if (CollUtilX.isEmpty(menuDOS)) {
                throw new SysException(ErrorCodeEnum.AUTH_LOGIN_BAD_MENU);
            }

            // 根据角色的权限菜单，生成前端路由
            list = buildRoter_1_2(menuDOS);
        }

        // 未配置菜单
        if (CollUtilX.isEmpty(list)) {
            throw new SysException(ErrorCodeEnum.AUTH_LOGIN_BAD_MENU2);
        }

        // 正序
        Collections.sort(list);

        List<AuthMenuResp> menuResplist = AuthMenuConvert.INSTANCE.convertList(list);

        // 转成树形结构
        List<AuthMenuResp> tree = TreeUtilX.listToTree(menuResplist);

        List<Map<String, Object>> maps = menu_digui(tree);
        return maps;
    }

    /**
     * 根据不同类型的菜单，构建1，2，3级菜单路由
     */
    private List<AuthMenu2> buildRoter_1_2(Collection<AuthMenu2> menuDOS) {
        List<AuthMenu2> listDO = new ArrayList<>();

        Set<String> pidSet = new HashSet<>();
        for (AuthMenu2 entity : menuDOS) {
            String pids = entity.getParentItemIds();
            String[] split = pids.split(",");
            if (entity.getType() == NavBarEnum.BUTTON.getKey()) {
                // type，1：目录，2：菜单，3：按钮
                // 如果是按钮级，把父级菜单id保存在pidSet里
                for (String pid : split) {
                    pidSet.add(pid);
                }
            } else if (entity.getType() == NavBarEnum.MENU.getKey()) {
                // 如果是菜单级，把父级菜单id保存在pidSet里，把当前菜单保存在menuSet集合
                for (String pid : split) {
                    pidSet.add(pid);
                }
                // 加上自己
                pidSet.add(entity.getMenuId());
            } else {
                // 目录
                // 如果子集目录，把父级菜单id保存在pidSet里，把当前目录保存在menuSet集合
                for (String pid : split) {
                    pidSet.add(pid);
                }
                // 加上自己
                pidSet.add(entity.getMenuId());

                // 加上所有的子菜单，不包括按钮
                List<String> list = menuAuthMapper.selectChildrenMenuList(entity.getMenuId());
                pidSet.addAll(list);
            }
        }

        // 遍历找出该用户的层级菜单
        if (CollUtilX.isNotEmpty(pidSet)) {
            listDO = menuAuthMapper.selectListInId(pidSet);
        }
        return listDO;

    }

    /**
     * 递归封装菜单
     */
    private List<Map<String, Object>> menu_digui(List<AuthMenuResp> menuResps) {
        List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
        for (AuthMenuResp menuResp : menuResps) {
            Map<String, Object> map = new HashMap<>();
            map.put("menuId", menuResp.getMenuId());
            map.put("menuName", menuResp.getMenuName());
            map.put("hidden", menuResp.getHidden());
            map.put("icon", menuResp.getIcon());
            map.put("buttonName", menuResp.getButtonName());
            if (StrUtilX.isEmpty(menuResp.getPath())) {
                map.put("path", "");
            } else {
                map.put("path", "/" + menuResp.getPath());
            }

            List<AuthMenuResp> children = menuResp.getChildren();
            if (CollUtilX.isNotEmpty(children) && menuResps.size() != maps.size()) {
                List<Map<String, Object>> maps1 = menu_digui(children);
                map.put("children", maps1);
                maps.add(map);
                //递归
            } else if (CollUtilX.isEmpty(children) && menuResps.size() != maps.size()) {
                maps.add(map);
            } else {
                return maps;
            }
        }
        return maps;
    }

    @Override
    public List<AuthMenuResp> authMenuTree() {
        List<AuthMenu2> list = menuAuthMapper.selectAll();

        // 正序
        Collections.sort(list);

        List<AuthMenuResp> collect = list.stream().map(item -> {
            AuthMenuResp convert = AuthMenuConvert.INSTANCE.convert(item);
            convert.setItemId(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            convert.setPath(null);
            return convert;
        }).collect(Collectors.toList());

        List<AuthMenuResp> authMenuResps = TreeUtilX.listToTree(collect);
        return authMenuResps;
    }

    @Override
    public List<Map<String, Object>> authMenuRouter(Integer userId) {
        // 查询当前用户的菜单
        return buildMenu(userId);
    }

    @Override
    public String authMenuMind(Integer hasPermission) {
        List<AuthMenu2> list = menuAuthMapper.selectAll();
        // 正序
        Collections.sort(list);

        List<AuthMenuResp> collect = list.stream().map(item -> {
            AuthMenuResp convert = AuthMenuConvert.INSTANCE.convert(item);
            convert.setItemId(item.getMenuId());
            convert.setItemName(item.getMenuName());
            convert.setRemark(item.getPath());
            return convert;
        }).collect(Collectors.toList());

        List<AuthMenuResp> authMenuResps = TreeUtilX.listToTree(collect);

        String jsonString = JSON.toJSONString(authMenuResps);
        return jsonString;
    }

}