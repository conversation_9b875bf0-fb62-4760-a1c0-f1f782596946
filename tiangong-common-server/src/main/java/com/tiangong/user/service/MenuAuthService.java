package com.tiangong.user.service;

import com.tiangong.user.domain.req.AuthMenuAditReq;
import com.tiangong.user.domain.resp.AuthMenuResp;

import java.util.List;
import java.util.Map;

/**
 * 菜单表
 */
public interface MenuAuthService {

    /**
     * 菜单表新增
     */
    String authMenuAdd(AuthMenuAditReq req);

    /**
     * 菜单表查询
     */
    List<AuthMenuResp> authMenuTree();

    /**
     * 查询左侧菜单栏
     * @return
     */
    List<Map<String, Object>> authMenuRouter(Integer userId );

    /**
     * 查询思维导图
     * @param hasPermission
     * @return
     */
    String authMenuMind(Integer hasPermission);

}

