package com.tiangong.user.service.impl;

import com.tiangong.cloud.common.enums.result.AdminResultEnum;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.ApprovalStatusEnum;
import com.tiangong.exception.SysException;
import com.tiangong.user.domain.JwtUser;
import com.tiangong.user.dto.FirstMenuDTO;
import com.tiangong.user.dto.MenuDTO;
import com.tiangong.user.dto.MenuLevelDTO;
import com.tiangong.user.dto.UserDTO;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * security的service，实现loadUserByUsername方法
 */
@Slf4j
@Service("userDetailsService" )
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public UserDetails loadUserByUsername(String username) {

        Map<String,String> requestMap = new HashMap<>();
        requestMap.put("userAccount",username);
        UserDTO user = userMapper.queryLoginUser(requestMap);
        log.info("登陆2,{}" , user);
        if (user == null) {
            throw new SysException(AdminResultEnum.E_90006.getCode(), AdminResultEnum.E_90006.getMessage());
        } else if (user.getAvailableStatus().equals(0)) {
            log.info("登录用户禁用");
            throw new SysException(AdminResultEnum.E_90007.getCode(), AdminResultEnum.E_90007.getMessage());
        } else if (user.getActive().equals(0)) {
            log.info("登录用户无效");
            throw new SysException(AdminResultEnum.E_90063.getCode(), AdminResultEnum.E_90063.getMessage());
        } else if (user.getApprovalStatus().equals(ApprovalStatusEnum.PENDING.getCode()) || user.getApprovalStatus().equals(ApprovalStatusEnum.IN_PROGRESS.getCode()) || user.getApprovalStatus().equals(ApprovalStatusEnum.REJECTED.getCode())) {
            log.info("登录用户审核状态无法登录");
            throw new SysException(AdminResultEnum.E_90062.getCode(), AdminResultEnum.E_90062.getMessage());
        }

        //菜单
        Map<String, String> request = new HashMap<String, String>();
        request.put("userAccount", user.getLoginAccount());
        List<MenuDTO> menuDTOS = userMapper.queryMenuListByLoginName(request);
        List<FirstMenuDTO> firstMenuDTOS = combinationMenu(menuDTOS,null);
        user.setMenus(firstMenuDTOS);

        return createJwtUser(user, null, null);
    }

    /**
     * 创建jwtUser对象，封装
     *
     * @param user
     * @return
     */
    private UserDetails createJwtUser(UserDTO user, Collection<GrantedAuthority> authorities, String roleNames) {
        return new JwtUser(
                user.getLoginId(),
                user.getLoginAccount(),
                user.getLoginName(),
                user.getLoginPwd(),
                user.getSignaturePwd(),
                null,
                null,
                user.getCompanyCode(),
                user.getCompanyDomain(),
                roleNames,
                user.getUpdatePwdTime(),
                user.getVisitStartTime(),
                user.getVisitEndTime(),
                user.getVisitIp(),
                user.getAccountStatus(),
                user.getUnlockTime(),
                user.getIsSuperAdmin(),
                user.getIsNotePermission(),
                user.getMenus(),
                true,
                authorities);
    }

    /**
     * 组装目录
     * @return
     */
    private List<FirstMenuDTO> combinationMenu(List<MenuDTO> menuDTOS,Map<Integer,Integer> selectedMap){
        List<FirstMenuDTO> firstMenuDTOS = new ArrayList<FirstMenuDTO>();

        for(MenuDTO menuDTO : menuDTOS){
            if(menuDTO.getMenuLevel() == 1){
                FirstMenuDTO firstMenuDTO = CommonDtoConvert.INSTANCE.firstMenuDTOConvert(menuDTO);
                firstMenuDTO.setPath(menuDTO.getFrontEndUrl() == null? null:menuDTO.getFrontEndUrl());
                firstMenuDTO.setFirstMenu(menuDTO.getMenuName());
                if (null == selectedMap || null != selectedMap.get(menuDTO.getMenuId())) {
                    firstMenuDTO.setSelected(1);
                }
                firstMenuDTOS.add(firstMenuDTO);

            }
        }

        Collections.sort(firstMenuDTOS, new Comparator<FirstMenuDTO>() {
            @Override
            public int compare(FirstMenuDTO o1, FirstMenuDTO o2) {
                return o1.getMenuRank() - o2.getMenuRank();
            }
        });


        Map<String, FirstMenuDTO> menuMap = firstMenuDTOS.stream().collect(Collectors.toMap(FirstMenuDTO::getMenuCode, Function.identity(), (key1, key2) -> key2));

        for(MenuDTO menuDTO : menuDTOS){
            if(menuDTO.getMenuLevel() == 2){
                List<MenuLevelDTO> secondMenuDTOs = new ArrayList<MenuLevelDTO>();
                if(CollUtilX.isNotEmpty(menuMap.get(menuDTO.getParentCode()).getSecondMenuList())) {
                    secondMenuDTOs = menuMap.get(menuDTO.getParentCode()).getSecondMenuList();
                }
                MenuLevelDTO menuLevelDTO = CommonDtoConvert.INSTANCE.menuLevelDTOConvert(menuDTO);
                menuLevelDTO.setPath(menuDTO.getFrontEndUrl() == null?null:menuDTO.getFrontEndUrl());
                menuLevelDTO.setSecondMenu(menuDTO.getMenuName());
                if (null == selectedMap || null != selectedMap.get(menuDTO.getMenuId())) {
                    menuLevelDTO.setSelected(1);
                }
                secondMenuDTOs.add(menuLevelDTO);
                menuMap.get(menuDTO.getParentCode()).setSecondMenuList(secondMenuDTOs);
            }
        }


        for(FirstMenuDTO firstMenuDTO: firstMenuDTOS){
            if(CollUtilX.isNotEmpty(firstMenuDTO.getSecondMenuList())) {
                Collections.sort(firstMenuDTO.getSecondMenuList(), new Comparator<MenuLevelDTO>() {
                    @Override
                    public int compare(MenuLevelDTO o1, MenuLevelDTO o2) {
                        return o1.getMenuRank() - o2.getMenuRank();
                    }
                });
            }
        }

        Iterator<FirstMenuDTO> firstMenuDTOIterator = firstMenuDTOS.iterator();
        while (firstMenuDTOIterator.hasNext()){
            FirstMenuDTO firstMenuDTO = firstMenuDTOIterator.next();
            if (firstMenuDTO.getSecondMenuList() == null && StrUtilX.isEmpty(firstMenuDTO.getPath())){
                firstMenuDTOIterator.remove();
            }
        }

        return firstMenuDTOS;
    }
//
//    @Override
//    public UserDetails loadUserByUsername(String username) {
//
//        QueryWrapper<AuthUserEntity> wrapper = new QueryWrapper<>();
//        wrapper.eq("user_account" , username);
//        wrapper.eq("deleted" , BaseEnum.NOT_DELETE.getKey());
//        AuthUserEntity user = authUserMapper.selectOne(wrapper);
//        log.info("登陆2,{}" , user);
//        if (user == null) {
//            throw new SysException(AdminResultEnum.E_90006.getCode(), AdminResultEnum.E_90006.getMessage());
//        } else {
//            //用户禁用
//            if (0 == user.getEnable()) {
//                throw new SysException(AdminResultEnum.E_90007.getCode(), AdminResultEnum.E_90007.getMessage());
//            }
//        }
//
//        Set<AuthRoleEntity> roles = authUserMapper.findByUsersId(user.getUserId());
//        String roleNames = null;
//        Set<String> permissions = new HashSet<>();
//        for (AuthRoleEntity role : roles) {
//            if (StrUtilX.isNotEmpty(role.getRoleCode())) {
//                // 添加角色标识
//                permissions.add(role.getRoleCode());
//            }
//
//            if (roleNames == null) {
//                roleNames = role.getRoleName();
//            } else {
//                roleNames = roleNames + "/" + role.getRoleName();
//            }
//
//        }
//        //添加菜单的权限标识
//        permissions.addAll(
//                roles.stream().flatMap(role -> role.getMenus().stream())
//                        .filter(menu -> StrUtilX.isNotEmpty(menu.getPermissions()))
//                        .map(AuthMenuEntity::getPermissions).collect(Collectors.toSet())
//        );
//
//        Collection<GrantedAuthority> collect = permissions.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toList());
//
//        return createJwtUser(user, collect, roleNames);
//    }
//
//    /**
//     * 创建jwtUser对象，封装
//     *
//     * @param user
//     * @return
//     */
//    private UserDetails createJwtUser(AuthUserEntity user, Collection<GrantedAuthority> authorities, String roleNames) {
//        return new JwtUser(
//                user.getUserId(),
//                user.getUserAccount(),
//                user.getUserName(),
//                user.getUserPassword(),
//                user.getPhoneNumber(),
//                user.getEmail(),
//                user.getOrgCode(),
//                roleNames,
//                true,
//                authorities);
//    }
}
