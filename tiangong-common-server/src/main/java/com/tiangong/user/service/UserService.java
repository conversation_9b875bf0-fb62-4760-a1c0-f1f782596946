package com.tiangong.user.service;

import com.github.pagehelper.PageInfo;
import com.tiangong.common.Response;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.user.domain.AuthUser;
import com.tiangong.user.domain.HrRequest;
import com.tiangong.user.domain.JwtUser;
import com.tiangong.user.domain.req.EmployeeApprovalStatusReq;
import com.tiangong.user.dto.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface UserService {

    /**
     * 增加超级管理员
     */
    int addAdminUser(UserPO userPO, int type);

    /**
     * 修改超级管理员信息（除密码）
     */
    int modifyAdminUser(UserPO userPO);

    /**
     * 重置超级管理员密码
     */
    int modifyAdminUserPwd(UserPO userPO);

    /**
     * 获取采购管理员列表
     */
    List<PurchaseManagerDTO> getPurchaseManagerList(Map<String, String> requestMap);

    /**
     * 获取销售经理列表
     */
    List<SaleManagerDTO> getSaleManagerList(Map<String, String> requestMap);

    /**
     * 获取角色和域名
     */
    RoleAndDomainDTO getRoleAndDomain(Map<String, String> requestMap);

    /**
     * 获取企业员工列表
     */
    PageInfo<EmployeeUserDTO> getEmployeeList(Map<String, String> requestMap);

    /**
     * 根据名称模糊查询用户信息列表
     */
    List<UserInfoDTO> queryEmployeeLikeName(String keyword);

    /**
     * 增加企业员工
     */
    int addEmployeeUser(EmployeeUserDTO employeeUserDTO);

    /**
     * 修改企业员工信息
     */
    int modifyEmployeeUser(EmployeeUserDTO employeeUserDTO);

    /**
     * 修改企业员工有效状态
     */
    int modifyEmployeeUserActive(EmployeeUserDTO employeeUserDTO);

    /**
     * 重新提交审核
     */
    int employeeResubmit(EmployeeUserDTO employeeUserDTO);

    /**
     * 接收hr员工审批状态
     */
    int getEmployeeApprovalStatus(HrRequest<EmployeeApprovalStatusReq> request, String ip);

    /**
     * 重置员工密码
     */
    int modifyEmployeePwd(UserPO userPO, boolean flag);

    /**
     * 删除员工
     */
    int deleteEmployeeUser(UserPO userPO, String modifiedAccount, HttpServletRequest request);

    /**
     * 获取企业员工详情
     */
    EmployeeUserDTO getEmployeeUserDetail(Map<String, String> requestMap);

    /**
     * 个人信息
     */
    EmployeeUserDTO getCurrentUserDetail(Map<String, String> requestMap);

    /**
     * 检验是否存在该账号名
     */
    int examineUserAccount(String userAccount);

    /**
     * 检验是否存在该账号名
     */
    int examineUserAccountOrUserTel(UserPO userPO);

    /**
     * 通过登陆名查询目录
     */
    List<MenuDTO> queryMenuListByLoginName(Map<String, String> requestMap);

    /**
     * 登录
     */
    UserDTO login(UserDTO userDTO) throws Exception;

    /**
     * 查询角色列表
     */
    PageInfo<RoleDTO> queryRoleList(Map<String, String> requestMap);

    /**
     * 查询菜单列表
     */
    List<FirstMenuDTO> queryMenuList(Map<String, String> requestMap);

    /**
     * 查询角色菜单列表
     */
    RoleDTO queryRoleMenuList(Map<String, String> requestMap);

    /**
     * 角色编辑
     */
    void editRole(RoleDTO roleDTO);

    /**
     * 删除角色
     */
    Response<Object> roleDel(Map<String, String> requestMap);

    /**
     * 获取登录验证码
     */
    Response<String> sendVerificationCode(AuthUser authUser);

    /**
     * 获取发送验证码类型
     */
    String querySendVerificationCodeType();

    /**
     * 修改发送验证码类型
     */
    void updateSendVerificationCodeType(String sendVerificationCodeTypeValue);

    /**
     * 配置系统设置
     */
    int configSystemSetting(SystemSettingDTO systemSettingDTO);

    /**
     * 查询系统配置
     */
    Map<String, String> querySystemSetting();

    /**
     * 更新状态
     */
    int updateStatus(Map<String, String> requestMap);

    /**
     * 根据用户名获取用户信息
     */
    UserDTO queryLoginUser(Map<String, String> requestMap);

    /**
     * 自动注销账号
     */
    void autoLogoutAccount(String param);

    /**
     * 自动休眠账号
     */
    void autoDormantAccount();

    /**
     * 验证用户是否可以登录
     */
    boolean checkUserAuth(HttpServletRequest request, JwtUser jwtUser);

    /**
     * 更新登录时间
     */
    void updateLastLoginTime(Integer userId);

    /**
     * 通过menuCode获取对应的apiUrl
     */
    List<String> getUserAllApiUrl(List<String> list);

    /**
     * 获取验证码时间
     */
    Response<Long> getVerificationCodeTime(String userAccount);

    /**
     * 检验手机号或身份证是否已存在
     */
    int examinePhoneOrIdCard(Map<String, String> requestMap);

    /**
     * 检验密码
     */
    int examinePwd(Map<String, String> requestMap);


    /**
     * 修改员工手机号
     */
    int modifyEmployeeUserPhone(EmployeeUserDTO employeeUserDTO) throws Exception;

    /**
     * 修改员工身份证
     */
    int modifyEmployeeUserIdCard(EmployeeUserDTO employeeUserDTO);

    /**
     * 验证密码查看手机号
     */
    Response<String> verifyPasswordQueryInfo(EmployeeUserDTO employeeUserDTO, Integer infoType);

    /**
     * 根据账号查询客户编码
     */
    String queryOrgCodeByAccount(String userAccount);

    /**
     * 查询账号或者手机号是否存在
     */
    int queryUserAccountAndUserTel(String userAccount, String userTel, String agentCode);

    /**
     * 根据用户id查询角色
     * @param userId
     * @return
     */
    List<String> getRoleIdByUserId(Integer userId);
}
