package com.tiangong.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.tiangong.cloud.common.enums.result.AdminResultEnum;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.ApprovalStatusEnum;
import com.tiangong.exception.SysException;
import com.tiangong.user.domain.AuthUser;
import com.tiangong.user.domain.JwtUser;
import com.tiangong.user.dto.FirstMenuDTO;
import com.tiangong.user.dto.MenuDTO;
import com.tiangong.user.dto.MenuLevelDTO;
import com.tiangong.user.dto.UserDTO;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.user.service.AuthService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service("authService")
public class AuthServiceImpl implements AuthService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;
    @Override
    public JwtUser isPasswordMatch(AuthUser authUser) {

        log.info("验证用户登录密码：authUser:{}", JSON.toJSONString(authUser));
        String userAccount = authUser.getUserAccount();
        String password = authUser.getUserPassword();

        Map<String,String> requestMap = new HashMap<>();
        requestMap.put("userAccount",userAccount);
        UserDTO oldDTO =  userMapper.queryLoginUser(requestMap);
        log.info("验证用户登录密码：UserDTO:{}", JSON.toJSONString(oldDTO));
        if (oldDTO == null) {
            throw new SysException(AdminResultEnum.E_90061.getCode());
        } else if (oldDTO.getAvailableStatus().equals(0)) {
            throw new SysException(AdminResultEnum.E_90007.getCode());
        } else if (oldDTO.getActive().equals(0)) {
            log.info("登录用户无效");
            throw new SysException(AdminResultEnum.E_90063.getCode());
        } else if (oldDTO.getApprovalStatus().equals(ApprovalStatusEnum.PENDING.getCode()) || oldDTO.getApprovalStatus().equals(ApprovalStatusEnum.IN_PROGRESS.getCode()) || oldDTO.getApprovalStatus().equals(ApprovalStatusEnum.REJECTED.getCode())) {
            log.info("登录用户审核状态无法登录");
            throw new SysException(AdminResultEnum.E_90062.getCode());
        }

        // 校验密码
        boolean matches = password.equals(oldDTO.getLoginPwd());
        if (!matches) {
            throw new SysException(AdminResultEnum.E_90006.getCode());
        }

        //菜单
        oldDTO.setMenus(null);

        return createJwtUser(oldDTO, null, null);

    }

    /**
     * 创建jwtUser对象，封装
     *
     * @param user
     * @return
     */
    private JwtUser createJwtUser(UserDTO user, Collection<GrantedAuthority> authorities, String roleNames) {
        return new JwtUser(
                user.getLoginId(),
                user.getLoginAccount(),
                user.getLoginName(),
                user.getLoginPwd(),
                user.getSignaturePwd(),
                null,
                null,
                user.getCompanyCode(),
                user.getCompanyDomain(),
                roleNames,
                user.getUpdatePwdTime(),
                user.getVisitStartTime(),
                user.getVisitEndTime(),
                user.getVisitIp(),
                user.getAccountStatus(),
                user.getUnlockTime(),
                user.getIsSuperAdmin(),
                user.getIsNotePermission(),
                user.getMenus(),
                true,
                authorities);
    }

    /**
     * 组装目录
     * @return
     */
    private List<FirstMenuDTO> combinationMenu(List<MenuDTO> menuDTOS,Map<Integer,Integer> selectedMap){
        List<FirstMenuDTO> firstMenuDTOS = new ArrayList<FirstMenuDTO>();

        for(MenuDTO menuDTO : menuDTOS){
            if(menuDTO.getMenuLevel() == 1){
                FirstMenuDTO firstMenuDTO = CommonDtoConvert.INSTANCE.FirstMenuConvert(menuDTO);
                firstMenuDTO.setPath(menuDTO.getFrontEndUrl() == null? null:menuDTO.getFrontEndUrl());
                firstMenuDTO.setFirstMenu(menuDTO.getMenuName());
                if (null == selectedMap || null != selectedMap.get(menuDTO.getMenuId())) {
                    firstMenuDTO.setSelected(1);
                }
                firstMenuDTOS.add(firstMenuDTO);

            }
        }

        Collections.sort(firstMenuDTOS, new Comparator<FirstMenuDTO>() {
            @Override
            public int compare(FirstMenuDTO o1, FirstMenuDTO o2) {
                return o1.getMenuRank() - o2.getMenuRank();
            }
        });


        Map<String, FirstMenuDTO> menuMap = firstMenuDTOS.stream().collect(Collectors.toMap(FirstMenuDTO::getMenuCode, Function.identity(), (key1, key2) -> key2));

        for(MenuDTO menuDTO : menuDTOS){
            if(menuDTO.getMenuLevel() == 2){
                List<MenuLevelDTO> secondMenuDTOs = new ArrayList<MenuLevelDTO>();
                if(CollUtilX.isNotEmpty(menuMap.get(menuDTO.getParentCode()).getSecondMenuList())) {
                    secondMenuDTOs = menuMap.get(menuDTO.getParentCode()).getSecondMenuList();
                }
                MenuLevelDTO menuLevelDTO = CommonDtoConvert.INSTANCE.MenuLevelConvert(menuDTO);
                menuLevelDTO.setPath(menuDTO.getFrontEndUrl() == null?null:menuDTO.getFrontEndUrl());
                menuLevelDTO.setSecondMenu(menuDTO.getMenuName());
                if (null == selectedMap || null != selectedMap.get(menuDTO.getMenuId())) {
                    menuLevelDTO.setSelected(1);
                }
                secondMenuDTOs.add(menuLevelDTO);
                menuMap.get(menuDTO.getParentCode()).setSecondMenuList(secondMenuDTOs);
            }
        }


        for(FirstMenuDTO firstMenuDTO: firstMenuDTOS){
            if(CollUtilX.isNotEmpty(firstMenuDTO.getSecondMenuList())) {
                Collections.sort(firstMenuDTO.getSecondMenuList(), new Comparator<MenuLevelDTO>() {
                    @Override
                    public int compare(MenuLevelDTO o1, MenuLevelDTO o2) {
                        return o1.getMenuRank() - o2.getMenuRank();
                    }
                });
            }
        }

        Iterator<FirstMenuDTO> firstMenuDTOIterator = firstMenuDTOS.iterator();
        while (firstMenuDTOIterator.hasNext()){
            FirstMenuDTO firstMenuDTO = firstMenuDTOIterator.next();
            if (firstMenuDTO.getSecondMenuList() == null && StrUtilX.isEmpty(firstMenuDTO.getPath())){
                firstMenuDTOIterator.remove();
            }
        }

        return firstMenuDTOS;
    }
}
