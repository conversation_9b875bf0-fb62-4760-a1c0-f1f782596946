package com.tiangong.user.service;

import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.user.domain.RolePO;
import com.tiangong.user.domain.req.*;
import com.tiangong.user.domain.resp.MenuListResp;

import java.util.List;

public interface MenuRoleService {

    /**
     * 角色配置
     */
    void addOrUpdateRole(AddOrUpdateRoleReq req);

    /**
     * 角色删除
     */
    void delRole(DelRoleReq req);

    /**
     * 角色列表
     */
    List<RolePO> queryListRole();

    /**
     * 菜单列表
     */
    List<MenuListResp> queryListMenu();

    /**
     * 角色菜单列表
     */
    List<MenuListResp> queryListMenuRoleByRoleId(DelRoleReq req);

    /**
     * 用户菜单列表
     */
    List<MenuListResp> queryUserMenuList(LoginUser loginUser);

    /**
     * 角色菜单配置
     */
    void addOrUpdateRoleMenu(AddOrUpdateRoleMenuReq req);

    /**
     * 角色菜单配置
     */
    Integer authRoleMenuConfig(AuthRoleReq req);

    /**
     * 角色详情
     */
    AuthRoleResp authRoleDetail(Integer roleId);
}
