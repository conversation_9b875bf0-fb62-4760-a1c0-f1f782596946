package com.tiangong.user.service.impl;

import com.tiangong.ValidatedFilter;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.convert.AuthRoleConvert;
import com.tiangong.user.domain.MenuPO;
import com.tiangong.user.domain.RoleMenuPO;
import com.tiangong.user.domain.RolePO;
import com.tiangong.user.domain.req.*;
import com.tiangong.user.domain.resp.MenuDTO;
import com.tiangong.user.domain.resp.MenuListResp;
import com.tiangong.user.mapper.MenuMapper;
import com.tiangong.user.mapper.RoleMapper;
import com.tiangong.user.mapper.RoleMenuMapper;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.user.service.MenuRoleService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MenuRoleServiceImpl implements MenuRoleService {

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private RoleMenuMapper roleMenuMapper;

    @Resource
    private UserMapper userMapper;

    @Override
    public void addOrUpdateRole(AddOrUpdateRoleReq req) {
        RolePO po = new RolePO();
        po.setRoleName(req.roleName);
        RolePO po1 = roleMapper.selectOne(po);
        // 获取最大角色id 用于设置角色编码
        Integer maxRoleId = roleMapper.getMaxRoleId();
        int num = 10000;
        if (maxRoleId != null && maxRoleId > 0) {
            num = maxRoleId + 1;
        }
        String roleCode = "M" + num;
        if (req.getRoleId() == null) {
            // 新增
            if (po1 != null) {
                throw new SysException(ErrorCodeEnum.ROLE_NAME_IS_ALREADY);
            }
            po = CommonDtoConvert.INSTANCE.RolePOConvert(req);
            po.setRoleCode(roleCode);
            po.setType(0);
            po.setActive(1);
            po.setCreatedBy(req.getOperator());
            po.setCreatedDt(DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format));
            roleMapper.insert(po);
        } else {
            if (po1 != null && !Objects.equals(po1.getRoleId(), req.getRoleId())) {
                throw new SysException(ErrorCodeEnum.ROLE_NAME_IS_ALREADY);
            }
            po = CommonDtoConvert.INSTANCE.RolePOConvert(req);
            po.setRoleCode(roleCode);
            po.setActive(1);
            po.setType(0);
            po.setUpdatedBy(req.getOperator());
            po.setUpdatedDt(DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format));
            roleMapper.updateByPrimaryKey(po);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delRole(DelRoleReq req) {
        if (req.getRoleId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        RolePO po = CommonDtoConvert.INSTANCE.RoleConvert(req);
        roleMapper.deleteByPrimaryKey(po);
        RoleMenuPO roleMenuPO = new RoleMenuPO();
        roleMenuPO.setRoleId(req.getRoleId());
        roleMenuMapper.delete(roleMenuPO);
    }

    @Override
    public List<RolePO> queryListRole() {
        List<RolePO> rolePOS = roleMapper.selectAll();
        return rolePOS.stream().filter(o -> o.getRoleId() != 1).collect(Collectors.toList());
    }

    @Override
    public List<MenuListResp> queryListMenuRoleByRoleId(DelRoleReq req) {
        // 先查询所有菜单
        List<MenuPO> menuPOS = menuMapper.selectAll();

        Map<Integer, List<MenuPO>> collect = menuPOS.stream().collect(Collectors.groupingBy(MenuPO::getMenuLevel));

        //查询该角色的所有权限
        RoleMenuPO po = new RoleMenuPO();
        po.setRoleId(req.getRoleId());
        List<RoleMenuPO> select = roleMenuMapper.select(po);
        Map<Integer, RoleMenuPO> roleMenuMap = select.stream().collect(Collectors.toMap(RoleMenuPO::getMenuId, Function.identity(), (key1, key2) -> key2));

        //第一层级数据
        List<MenuPO> menuPOS1 = collect.get(1);
        //第二层数据
        List<MenuPO> menuPOS2 = collect.get(2);
        //返回的格式
        List<MenuListResp> list = new ArrayList<>();

        //组装数据
        for (MenuPO menu : menuPOS1) {
            MenuListResp resp = new MenuListResp();
            resp.setId(menu.getId());
            resp.setMenuName(menu.getMenuName());
            List<MenuDTO> menuDTOList = new ArrayList<>();
            for (MenuPO menu2 : menuPOS2) {
                if (menu.getMenuCode().equals(menu2.getParentCode())) {
                    MenuDTO menuDTO = CommonDtoConvert.INSTANCE.MenuDTOConvert(menu2);
                    RoleMenuPO po1 = roleMenuMap.get(menu2.getId());
                    if (po1 != null) {
                        menuDTO.setIsSelect(1);
                    } else {
                        menuDTO.setIsSelect(0);
                    }
                    menuDTOList.add(menuDTO);
                }
            }
            RoleMenuPO po1 = roleMenuMap.get(menu.getId());
            if (po1 != null) {
                resp.setIsSelect(1);
            } else {
                resp.setIsSelect(0);
            }
            resp.setChildren(menuDTOList);
            list.add(resp);
        }
        return list;
    }

    @Override
    public List<MenuListResp> queryUserMenuList(LoginUser user) {
        Integer userId = user.getUserId();
        List<MenuDTO> menuListDTOS = userMapper.queryUserMenuList(userId);
        if (Objects.isNull(menuListDTOS) || menuListDTOS.isEmpty()) {
            String token = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, user.getUserAccount()));
            RedisTemplateX.delete(token);
            throw new SysException(ErrorCodeEnum.NO_ROLE_AUTH);
        }

        Map<Integer, List<MenuDTO>> collect = menuListDTOS.stream().collect(Collectors.groupingBy(MenuDTO::getMenuLevel));


        //第一层级数据
        List<MenuDTO> menuPOS1 = collect.get(1);
        //第二层数据
        List<MenuDTO> menuPOS2 = collect.get(2);
        //返回的格式
        List<MenuListResp> list = new ArrayList<>();

        //验空
        if (Objects.isNull(menuPOS1) || menuPOS1.isEmpty()) {
            return list;
        }
        //组装数据
        for (MenuDTO menu : menuPOS1) {
            if (Objects.isNull(menu)) {
                continue;
            }
            if (StringUtils.isEmpty(menu.getMenuName())) {
                continue;
            }
            MenuListResp resp = new MenuListResp();
            resp.setId(menu.getId());
            resp.setMenuName(menu.getMenuName());
            resp.setPath(menu.getPath());
            List<MenuDTO> menuDTOList = new ArrayList<>();
            if (Objects.isNull(menuPOS2) || menuPOS2.isEmpty()) {
                resp.setChildren(menuDTOList);
                list.add(resp);
                continue;
            }
            for (MenuDTO menu2 : menuPOS2) {
                if (Objects.isNull(menu2)) {
                    continue;
                }
                if (StringUtils.isEmpty(menu2.getMenuName())) {
                    continue;
                }
                if (menu.getMenuCode().equals(menu2.getParentCode())) {
                    MenuDTO menuDTO = CommonDtoConvert.INSTANCE.MenuConvert(menu2);
                    menuDTOList.add(menuDTO);
                }
            }
            resp.setChildren(menuDTOList);
            list.add(resp);
        }
        return list;
    }

    @Override
    public List<MenuListResp> queryListMenu() {
        //先查询所有菜单
        List<MenuPO> menuPOS = userMapper.queryAllMenuList();

        Map<Integer, List<MenuPO>> collect = menuPOS.stream().collect(Collectors.groupingBy(MenuPO::getMenuLevel));


        //第一层级数据
        List<MenuPO> menuPOS1 = collect.get(1);
        //第二层数据
        List<MenuPO> menuPOS2 = collect.get(2);
        //返回的格式
        List<MenuListResp> list = new ArrayList<>();

        //组装数据
        for (MenuPO menu : menuPOS1) {
            MenuListResp resp = new MenuListResp();
            resp.setId(menu.getId());
            resp.setMenuName(menu.getMenuName());
            List<MenuDTO> menuDTOList = new ArrayList<>();
            for (MenuPO menu2 : menuPOS2) {
                if (menu.getMenuCode().equals(menu2.getParentCode())) {
                    MenuDTO menuDTO = CommonDtoConvert.INSTANCE.MenuDTOConvert(menu2);
                    menuDTOList.add(menuDTO);
                }
            }
            resp.setChildren(menuDTOList);
            list.add(resp);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateRoleMenu(AddOrUpdateRoleMenuReq req) {
        if (req.getRoleId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        RoleMenuPO po = new RoleMenuPO();
        po.setRoleId(req.getRoleId());
        roleMenuMapper.delete(po);

        //存放上级
        Map<Integer, Integer> map = new HashMap<>();
        Map<Integer, Integer> map2 = new HashMap<>();
        List<RoleMenuPO> list = new ArrayList<>();
        for (Integer menuId : req.getList()) {

            MenuPO menuPO = new MenuPO();
            menuPO.setId(menuId);
            List<MenuPO> select = menuMapper.select(menuPO);
            if (select == null || select.size() <= 0) {
                continue;
            }

            if (select.get(0).getMenuLevel() == 1) {
                //是最顶级，则把他下面的子集查出来
                MenuPO menuPO1 = new MenuPO();
                menuPO1.setParentCode(select.get(0).getMenuCode());
                List<MenuPO> select1 = menuMapper.select(menuPO1);
                for (MenuPO po1 : select1) {
                    RoleMenuPO roleMenuPO = new RoleMenuPO();
                    roleMenuPO.setMenuId(po1.getId());
                    roleMenuPO.setRoleId(req.getRoleId());
                    map2.put(po1.getId(), po1.getId());
                }
                map.put(menuId, menuId);
            } else {
                //不是最顶级，则吧最顶级查出来
                MenuPO menuPO1 = new MenuPO();
                menuPO1.setMenuCode(select.get(0).getParentCode());
                List<MenuPO> select1 = menuMapper.select(menuPO1);
                if (select1.get(0).getMenuLevel() != 1) {
                    map2.putIfAbsent(menuId, menuId);
                    continue;
                }
                map.computeIfAbsent(select1.get(0).getId(), k -> select1.get(0).getId());
                map2.putIfAbsent(menuId, menuId);
            }
        }
        // 当前时间
        String currentDate = DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format);
        for (Integer id : map.keySet()) {
            Integer i = map.get(id);
            RoleMenuPO roleMenuPO = new RoleMenuPO();
            roleMenuPO.setMenuId(i);
            roleMenuPO.setRoleId(req.getRoleId());
            roleMenuPO.setCreatedBy(req.getOperator());
            roleMenuPO.setCreatedDt(currentDate);
            list.add(roleMenuPO);
        }
        for (Integer id : map2.keySet()) {
            Integer i = map2.get(id);
            RoleMenuPO roleMenuPO = new RoleMenuPO();
            roleMenuPO.setMenuId(i);
            roleMenuPO.setRoleId(req.getRoleId());
            roleMenuPO.setCreatedBy(req.getOperator());
            roleMenuPO.setCreatedDt(currentDate);
            list.add(roleMenuPO);
        }
        if (list.size() > 0) {
            roleMenuMapper.insertList(list);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer authRoleMenuConfig(AuthRoleReq req) {
        if (req.getRoleId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }

        // 删除旧的
        RoleMenuPO roleMenuPO = new RoleMenuPO();
        roleMenuPO.setRoleId(req.getRoleId());
        roleMenuMapper.delete(roleMenuPO);

        int i = 0;
        if (CollUtilX.isEmpty(req.getMenuIdList())) {
            return i;
        }

        // 当前时间
        String currentDate = DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format);

        // 添加新的
        List<RoleMenuPO> collect = req.getMenuIdList().stream().map(menuId -> {
            RoleMenuPO authRoleMenuDO = new RoleMenuPO();
            authRoleMenuDO.setRoleId(req.getRoleId());
            authRoleMenuDO.setMenuId(menuId);
            authRoleMenuDO.setCreatedBy(req.getOperator());
            authRoleMenuDO.setCreatedDt(currentDate);
            return authRoleMenuDO;
        }).collect(Collectors.toList());
        roleMenuMapper.insertList(collect);

        i = collect.size();
        return i;
    }

    @Override
    public AuthRoleResp authRoleDetail(Integer roleId) {
        RolePO oldDO = roleMapper.selectByPrimaryKey(roleId);
        if (oldDO == null) {
            return null;
        }

        AuthRoleResp resp = AuthRoleConvert.INSTANCE.convert(oldDO);

        // 查询菜单
        RoleMenuPO roleMenuPO = new RoleMenuPO();
        roleMenuPO.setRoleId(roleId);
        List<RoleMenuPO> roleMenuDOS = roleMenuMapper.select(roleMenuPO);

        List<Integer> collect = roleMenuDOS.stream().map(RoleMenuPO::getMenuId).collect(Collectors.toList());
        List<String> stringList = collect.stream().map(String::valueOf).collect(Collectors.toList());
        resp.setMenuIdList(stringList);
        return resp;
    }
}
