package com.tiangong.user.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.organization.domain.UserApplyPO;
import com.tiangong.user.dto.EmployeeUserDTO;
import com.tiangong.user.dto.UserApplyDTO;

import java.util.List;
import java.util.Map;

public interface UserApplyMapper extends MyMapper<UserApplyPO> {

    /**
     * 查询员工增改申请列表
     *
     * @param requestMap
     * @return
     */
    List<UserApplyDTO> queryUserApplyList(Map<String, String> requestMap);

    /**
     * 查询用户申请详情
     *
     * @param requestMap
     * @return
     */
    EmployeeUserDTO queryUserApplyDetail(Map<String, String> requestMap);
}
