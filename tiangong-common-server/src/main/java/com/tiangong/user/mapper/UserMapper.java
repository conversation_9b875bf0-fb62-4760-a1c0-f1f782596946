package com.tiangong.user.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.organization.domain.dto.UpdateAccountPwdDTO;
import com.tiangong.user.domain.ExamineUserDTO;
import com.tiangong.user.domain.MenuPO;
import com.tiangong.user.domain.UserRolePO;
import com.tiangong.user.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface UserMapper extends MyMapper<UserPO> {

    /**
     * 查询是否符合增加管理员的要求
     *
     * @param userPO
     * @return
     */
    List<UserPO> queryExistAdminUser(UserPO userPO);

    /**
     * 查询采购经理List
     *
     * @return
     */
    List<PurchaseManagerDTO> queryPurchaseManagerList(Map<String, String> requestMap);

    /**
     * 获取销售经理列表
     *
     * @return
     */
    List<SaleManagerDTO> getSaleManagerList(Map<String, String> requestMap);

    /**
     * 获取域名和角色
     *
     * @param requestMap
     * @return
     */
    RoleAndDomainDTO getRoleAndDomain(Map<String, String> requestMap);

    /**
     * 获取企业员工列表
     *
     * @param requestMap
     * @return
     */
    List<EmployeeUserDTO> getEmployeeList(Map<String, String> requestMap);

    /**
     * 获取角色列表
     *
     * @param employeeUserDTOS
     * @return
     */
    List<EmployeeRoleDTO> getRoleList(List<EmployeeUserDTO> employeeUserDTOS);

    /**
     * 通过用户id获取角色信息
     *
     * @param userPOs
     * @return
     */
    List<EmployeeRoleDTO> getRoleListByUserId(List<UserPO> userPOs);

    /**
     * 通过用户id获取角色
     *
     * @param userId
     * @return
     */
    String getRoleByUserId(Integer userId);

    /**
     * 删除用户-角色
     *
     * @param userRolePOS
     * @return
     */
    void deleteUserRole(List<UserRolePO> userRolePOS);

    /**
     * 查询不是超级管理员的用户
     *
     * @param userId
     * @return
     */
    UserPO getUserWithoutAdmin(@Param("userId") Integer userId);

    /**
     * 获取企业员工详情
     *
     * @param requestMap
     * @return
     */
    EmployeeUserDTO queryEmployeeDetail(Map<String, String> requestMap);

    /**
     * 获取当前用户信息
     *
     * @param requestMap
     * @return
     */
    EmployeeUserDTO getCurrentUserDetail(Map<String, String> requestMap);

    /**
     * 通过登录名查询目录
     *
     * @param requestMap
     * @return
     */
    List<MenuDTO> queryMenuListByLoginName(Map<String, String> requestMap);

    /**
     * 通过角色Id查询目录
     *
     * @param requestMap
     * @return
     */
    List<MenuDTO> queryMenuListByRoleId(Map<String, String> requestMap);

    /**
     * 查询登录的信息
     *
     * @param userAccount
     * @return
     */
    UserDTO queryLoginUser(Map<String, String> requestMap);

    /**
     * 查询角色列表
     */
    List<RoleDTO> queryRoleList(Map<String, String> requestMap);

    /**
     * 查询角色下的用户数
     */
    Integer queryRoleUserCount(Map<String, String> requestMap);

    /**
     * 查询用户数量
     *
     * @param queryUserPO
     * @return
     */
    int queryUserCount(UserPO queryUserPO);

    /**
     * 更新账号状态
     *
     * @param requestMap
     * @return
     */
    int updateAccountStatus(Map<String, String> requestMap);

    /**
     * 查询登录的信息
     *
     * @param userAccount
     * @return
     */
    UserDTO queryUserPwd(@Param(value = "userAccount") String userAccount);

    /**
     * 查询需注销账号
     *
     * @return
     */
    List<EmployeeUserDTO> queryLogoutAccount();

    /**
     * 注销账号
     */
    void autoLogoutAccount();

    /**
     * 休眠账号
     */
    void autoDormantAccount();

    /**
     * 查询账号是否存在
     *
     * @param userAccount
     * @return
     */
    int queryUserAccount(@Param(value = "userAccount") String userAccount);

    /**
     * 查询账号是否存在
     *
     * @param userPO
     * @return
     */
    int examineUserAccountOrUserTel(ExamineUserDTO userPO);



    /**
     * 查询账号或者手机号是否存在
     *
     * @param userAccount
     * @param userTel
     * @return
     */
    int queryUserAccountAndUserTel(@Param(value = "userAccount") String userAccount, @Param(value = "userTel") String userTel, @Param("agentCode") String agentCode);

    /**
     * 根据账号查询客户编码
     *
     * @param userAccount
     * @return
     */
    String queryOrgCodeByAccount(@Param(value = "userAccount") String userAccount);

    /**
     * 重置客户管理员密码
     * @param dto
     * @return
     */
    boolean resetAccountPassword(UpdateAccountPwdDTO dto);

    /**
     * 获取用户菜单权限列表
     * @param userId
     * @return
     */
    List<com.tiangong.user.domain.resp.MenuDTO> queryUserMenuList(Integer userId);

    /**
     * 查询所有菜单权限列表
     * @return
     */
    List<MenuPO> queryAllMenuList();
}
