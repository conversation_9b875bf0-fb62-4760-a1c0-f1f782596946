package com.tiangong.user.domain.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色表
 * 返回参数
 */
@Data
public class AuthRoleResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private Integer enable;

    private List<String> menuIdList;

}