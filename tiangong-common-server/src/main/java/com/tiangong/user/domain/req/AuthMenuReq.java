package com.tiangong.user.domain.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 菜单表
 * 请求参数
 */
@Data
public class AuthMenuReq implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    private String menuId;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 父级ID
     */
    private String parentItemId;
    /**
     * 后端权限
     */
    private String permissions;
    /**
     * 前端按钮
     */
    private String buttonName;
    /**
     * 前端路由
     */
    private String path;
    /**
     * 是否隐藏
     */
    private Integer hidden;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 图标
     */
    private String icon;
}