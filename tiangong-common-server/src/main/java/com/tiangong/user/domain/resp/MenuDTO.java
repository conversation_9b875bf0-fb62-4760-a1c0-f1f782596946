package com.tiangong.user.domain.resp;

import lombok.Data;

@Data
public class MenuDTO {
    private Integer id;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 目录等级
     */
    private Integer menuLevel;

    /**
     * 菜单在当前层级排序
     */
    private Integer menuRank;

    /**
     * 父节点code
     */
    private String parentCode;

    /**
     * 菜单url
     */
    private String path;

    /**
     * 哪个系统的角色(0.运营端，1.供应端， 2.平台端，3.营销端)
     */
    private Integer type;

    /**
     * 有效性
     */
    private Integer active;

    private Integer isSelect;
}
