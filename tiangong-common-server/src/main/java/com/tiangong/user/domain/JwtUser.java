package com.tiangong.user.domain;

import com.tiangong.user.dto.FirstMenuDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @Auther: wangzhong
 * @Date: 2019/9/30 17:20
 * @Description: 用户返回实体类
 */
@Getter
@AllArgsConstructor
public class JwtUser implements UserDetails {

    private final Integer userId;
    private final String userAccount;
    private final String username;
    private final String password;
    private final String signaturePwd;
    private final String phoneNumber;
    private final String email;
    private final String companyCode;
    private final String companyDomain;
    private final String roleNames;
    private final Date updatePwdTime;
    private final String visitStartTime;
    private final String visitEndTime;
    private final String visitIp;
    private final Integer accountStatus;
    private final Date unlockTime;
    private final Integer isSuperAdmin;
    private final Integer isNotePermission;
    private final List<FirstMenuDTO> menus;
    /**
     * 标识这个属性不返回
     */
    private final boolean enabled;
    private final Collection<GrantedAuthority> authorities;
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    public Collection getRoles() {
        //获取用户角色信息，封装成set
        return null;
//        return authorities.stream().map(GrantedAuthority::getAuthority).collect(Collectors.toSet());
    }
}
