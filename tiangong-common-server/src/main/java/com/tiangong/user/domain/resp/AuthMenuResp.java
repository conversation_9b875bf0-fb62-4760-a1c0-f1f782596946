package com.tiangong.user.domain.resp;

import com.tiangong.cloud.common.domain.BaseTree;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单表
 * 返回参数
 */
@Data
public class AuthMenuResp extends BaseTree implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    private String menuId;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 后端权限
     */
    private String permissions;
    /**
     * 前端按钮
     */
    private String buttonName;
    /**
     * 前端路由
     */
    private String path;
    /**
     * 是否隐藏
     */
    private Integer hidden;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 图标
     */
    private String icon;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private String createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private String updatedDt;

    private List<AuthMenuResp> children;
}