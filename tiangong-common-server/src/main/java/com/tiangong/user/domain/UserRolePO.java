package com.tiangong.user.domain;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
/**
 * <AUTHOR>
 */
@Data
@Table(name = "t_auth_user_role")
public class UserRolePO extends BasePO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户id
     */
    private Integer roleId;

    /**
     * 用户id
     */
    private Integer userId;

}
