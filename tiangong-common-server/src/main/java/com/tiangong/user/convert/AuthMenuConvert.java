package com.tiangong.user.convert;

import com.tiangong.user.domain.AuthMenu2;
import com.tiangong.user.domain.req.AuthMenuReq;
import com.tiangong.user.domain.resp.AuthMenuResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AuthMenuConvert {

    AuthMenuConvert INSTANCE = Mappers.getMapper(AuthMenuConvert.class);

    /**
     * 将菜单请求对象转换为菜单实体对象
     *
     * @param bean 菜单请求对象，包含菜单信息
     * @return 返回转换后的菜单实体对象
     */
    AuthMenu2 convert(AuthMenuReq bean);

    /**
     * 将菜单实体对象转换为菜单响应对象
     *
     * @param bean 菜单实体对象，包含菜单数据
     * @return 返回转换后的菜单响应对象
     */
    AuthMenuResp convert(AuthMenu2 bean);

    /**
     * 将菜单实体对象列表转换为菜单响应对象列表
     *
     * @param list 菜单实体对象列表
     * @return 返回转换后的菜单响应对象列表
     */
    @Mappings({
            @Mapping(source = "menuId", target = "iteamCode"),
            @Mapping(source = "menuName", target = "itemName")
    })
    List<AuthMenuResp> convertList(List<AuthMenu2> list);

}
