package com.tiangong.user.convert;

import com.tiangong.user.domain.RolePO;
import com.tiangong.user.domain.req.AuthRoleReq;
import com.tiangong.user.domain.req.AuthRoleResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/11 15:46
 */
@Mapper
public interface AuthRoleConvert {

    AuthRoleConvert INSTANCE = Mappers.getMapper(AuthRoleConvert.class);

    /**
     * 将角色请求对象转换为角色持久化对象
     *
     * @param bean 角色请求对象，包含角色信息
     * @return 返回转换后的角色持久化对象
     */
    RolePO convert(AuthRoleReq bean);

    /**
     * 将角色持久化对象转换为角色响应对象
     *
     * @param bean 角色持久化对象，包含角色数据
     * @return 返回转换后的角色响应对象
     */
    AuthRoleResp convert(RolePO bean);

    /**
     * 将角色持久化对象列表转换为角色响应对象列表
     *
     * @param list 角色持久化对象列表
     * @return 返回转换后的角色响应对象列表
     */
    List<AuthRoleResp> convertList(List<RolePO> list);
}
