package com.tiangong.constant;

/**
 * 统一字段常量管理类
 * 用于管理系统中所有使用字符串字面量操作属性的字段名
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface FieldConstants {

    // ==================== JSON 字段常量 ====================
    
    /**
     * 语言类型字段
     */
    String LANGUAGE_TYPE = "languageType";
    
    /**
     * 酒店ID列表字段
     */
    String HOTEL_IDS = "hotelIds";
    
    /**
     * 设置字段
     */
    String SETTINGS = "settings";
    
    /**
     * 城市编码字段
     */
    String CITY_CODE = "cityCode";
    
    /**
     * 关键字字段
     */
    String KEY_WORD = "keyWord";

    /**
     * 关键词字段
     */
    String KEYWORD = "keyword";
    
    /**
     * 语言字段
     */
    String LANGUAGE = "language";
    
    /**
     * 数据字段
     */
    String DATA = "data";
    
    /**
     * 序列名称字段
     */
    String SEQ_NAME = "seqName";

    // ==================== 系统设置字段常量 ====================
    
    /**
     * 最大在线数量
     */
    String MAX_ONLINE_COUNT = "maxOnlineCount";
    
    /**
     * 最大失败次数
     */
    String MAX_FAIL_COUNT = "maxFailCount";
    
    /**
     * 失败锁定时间
     */
    String FAIL_LOCK_TIME = "failLockTime";

    // ==================== 数据库字段常量 ====================
    
    /**
     * 币种字段
     */
    String FIELD_COIN = "coin";

    // ==================== 用户相关字段常量 ====================
    
    /**
     * 用户账号字段
     */
    String USER_ACCOUNT = "userAccount";

    /**
     * 用户ID字段
     */
    String USER_ID = "userId";
    
    /**
     * 员工ID字段
     */
    String EMPLOYEE_ID = "employeeId";
    
    /**
     * 员工密码字段
     */
    String EMPLOYEE_PWD = "employeePwd";

    /**
     * 当前员工密码字段
     */
    String CURRENT_EMPLOYEE_PWD = "currentEmployeePwd";

    /**
     * 旧员工密码字段
     */
    String OLD_EMPLOYEE_PWD = "oldEmployeePwd";

    /**
     * 员工身份证号字段
     */
    String EMPLOYEE_ID_NUMBER = "employeeIDNumber";

    /**
     * 员工电话字段
     */
    String EMPLOYEE_TEL = "employeeTel";

    /**
     * 员工账号字段
     */
    String EMPLOYEE_ACCOUNT = "employeeAccount";

    /**
     * 修改账号字段
     */
    String MODIFIED_ACCOUNT = "modifiedAccount";

    /**
     * 发送验证码类型字段
     */
    String SEND_VERIFICATION_CODE_TYPE = "sendVerificationCodeType";

    /**
     * 用户字段
     */
    String USER = "user";

    /**
     * 更新人字段
     */
    String UPDATED_BY = "updatedBy";

    /**
     * 目的地ID字段
     */
    String DESTINATION_ID = "destinationId";


    // ==================== 分页相关字段常量 ====================
    
    /**
     * 当前页字段
     */
    String CURRENT_PAGE = "currentPage";
    
    /**
     * 页面大小字段
     */
    String PAGE_SIZE = "pageSize";
    
    /**
     * 代理商编码字段
     */
    String AGENT_CODE = "agentCode";
    /**
     * 组织编码字段
     */
    String ORG_CODE = "orgCode";
    
    /**
     * 编码字段
     */
    String CODE = "code";

    /**
     * 账号状态字段
     */
    String ACCOUNT_STATUS = "accountStatus";

    /**
     * 是否超级管理员字段
     */
    String IS_SUPER_ADMIN = "isSuperAdmin";

    /**
     * 用户名称字段
     */
    String USER_NAME = "userName";

    /**
     * 账号解锁时间字段
     */
    String UNLOCK_TIME = "unlockTime";
}
