package com.tiangong.message.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.tiangong.message.service.ShortMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ShortMessageServiceImpl implements ShortMessageService {

    public static final String SIGN_NAME = "芒果松";

    public static final String ACCESS_KEY_ID = "LTAIDtOPtUgIUHAi";

    public static final String ACCESS_SECRET = "0QkYqpXY0MyXHlatZ2Y4LKppYqVCvJ";

    @Override
    public void sendPreWarningShortMessage(String telPhones, String code, String failureRate) {
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", ACCESS_KEY_ID, ACCESS_SECRET);
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", "cn-hangzhou");
        request.putQueryParameter("PhoneNumbers", telPhones);
        request.putQueryParameter("SignName", SIGN_NAME);
        request.putQueryParameter("TemplateCode", "SMS_195261460");
        request.putQueryParameter("TemplateParam", "{\"code\":\"code1\",\"failureRate\":\"failureRate1\"}"
                .replace("code1", code).replace("failureRate1", failureRate));
        try {
            log.info("发送短信的请求参数为: {}", JSON.toJSONString(request));
            CommonResponse response = client.getCommonResponse(request);
            log.info("发送短信的返回参数为: {}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("发送短信异常", e);
        }
    }
}
