package com.tiangong.message.server;

import com.tiangong.common.Response;
import com.tiangong.message.service.ShortMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/common/shortMessage")
public class ShortMessageServer {

    @Autowired
    private ShortMessageService shortMessageService;

    /**
     * 发送短信
     */
    @PostMapping("/sendPreWarning")
    public Response<Object> sendPreWarningShortMessage(@RequestParam("telPhones") String telPhones, @RequestParam("code") String code,
                                               @RequestParam("failureRate") String failureRate) {
        shortMessageService.sendPreWarningShortMessage(telPhones, code, failureRate);
        return Response.success();
    }
}
