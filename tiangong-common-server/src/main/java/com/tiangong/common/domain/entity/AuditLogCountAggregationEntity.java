package com.tiangong.common.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/3/14 20:38
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@TableName("t_audit_log")
public class AuditLogCountAggregationEntity {

    /**
     * 新增的一列数据 用于存储eventCount字段
     * 注解非常关键
     */
    @TableField(value = "count(*)", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer eventCount;

    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 事件类型
     * 0：系统级事件
     * <p>
     * 1：业务级事件
     */
    private Integer eventType;
    /**
     * 事件描述
     */
    private String eventDescription;
    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private Integer eventResult;
    /**
     * 登陆账号
     */
    private String userAccount;


}
