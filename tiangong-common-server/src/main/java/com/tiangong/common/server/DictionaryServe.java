package com.tiangong.common.server;

import com.tiangong.common.Response;
import com.tiangong.common.domain.entity.DictionaryEntity;
import com.tiangong.common.domain.resp.DictionaryResp;
import com.tiangong.common.req.QueryDictionaryReq;
import com.tiangong.common.service.DictionaryService;
import com.tiangong.dto.common.PaginationSupportDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/18 11:27
 */
@RestController
@RequestMapping("/common/dictionary")
public class DictionaryServe {

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 获取字典类型列表--分页
     */
    @PostMapping("queryDictionaryPage")
    public Response<PaginationSupportDTO<DictionaryEntity>> queryDictionaryPage(@RequestBody QueryDictionaryReq req){
        return Response.success(dictionaryService.queryDictionaryPage(req));
    }

    /**
     * 获取字典类型列表
     */
    @PostMapping("queryDictionaryList")
    public Response<List<DictionaryResp>> queryDictionaryList(@RequestBody QueryDictionaryReq req){
        return Response.success(dictionaryService.queryDictionaryList(req));
    }

    /**
     * 查询字典分类
     */
    @PostMapping("queryDictionaryType")
    public Response<List<DictionaryEntity>> queryDictionaryType(){
        return Response.success(dictionaryService.queryDictionaryTypeList());
    }

    /**
     * 新增字典表数据
     */
    @PostMapping("addOrUpdateDictionary")
    public Response<Object> addOrUpdateDictionary(@Validated @RequestBody DictionaryEntity dictionaryEntity){
        dictionaryService.addOrUpdateDictionary(dictionaryEntity);
        return Response.success();
    }

}
