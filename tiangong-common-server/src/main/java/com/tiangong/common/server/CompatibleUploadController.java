package com.tiangong.common.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.common.remote.CompatibleFileReq;
import com.tiangong.enums.CompatibleFileEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.order.remote.OrderRemote;
import com.tiangong.order.remote.SupplyOrderRemote;
import com.tiangong.order.remote.request.DeleteOrderAttachmentByLogicDTO;
import com.tiangong.order.remote.request.DeleteSupplyOrderAttachmentByLogicDTO;
import com.tiangong.order.remote.request.SaveOrderAttachmentDTO;
import com.tiangong.order.remote.request.SaveSupplyAttachmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件服务
 */
@RestController
@Slf4j
@RequestMapping(value = "/common/file")
public class CompatibleUploadController extends BaseController {

    @Autowired
    private OrderRemote orderRemote;

    @Autowired
    private SupplyOrderRemote supplyOrderRemote;

    @Autowired
    HttpServletResponse response;

    /**
     * 文件上传
     */
    @RequestMapping(value = "/compatibleUpload", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public Response<Object> compatibleUpload(@RequestBody CompatibleFileReq req) {
        Integer type = req.getType();

        if (CompatibleFileEnum.ORDER_ATTACHMENT.type.equals(type)) {
            SaveOrderAttachmentDTO saveOrderAttachmentDTO = new SaveOrderAttachmentDTO();
            saveOrderAttachmentDTO.setOrderId(Integer.valueOf(req.getObjId()));
            saveOrderAttachmentDTO.setUrl(req.getFileUrl());
            saveOrderAttachmentDTO.setName(req.getFileName());
            saveOrderAttachmentDTO.setFileFormat(req.getFileFormat());
            saveOrderAttachmentDTO.setFileSize(req.getFileSize().toString());
            saveOrderAttachmentDTO.setOperator(super.getUserName());
            saveOrderAttachmentDTO.setOrderOwnerName(super.getLoginName());
            return orderRemote.saveOrderAttachment(saveOrderAttachmentDTO);
        } else if (CompatibleFileEnum.SUPPLY_ORDER_ATTACHMENT.type.equals(type)) {
            SaveSupplyAttachmentDTO saveSupplyAttachmentDTO = new SaveSupplyAttachmentDTO();
            saveSupplyAttachmentDTO.setSupplyOrderId(Integer.valueOf(req.getObjId()));
            saveSupplyAttachmentDTO.setUrl(req.getFileUrl());
            saveSupplyAttachmentDTO.setName(req.getFileName());
            saveSupplyAttachmentDTO.setOperator(super.getUserName());
            saveSupplyAttachmentDTO.setOrderOwnerName(super.getLoginName());
            return supplyOrderRemote.saveSupplyAttachment(saveSupplyAttachmentDTO);
        } else {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
    }

    /**
     * 文件删除
     */
    @RequestMapping(value = "/compatibleDelete", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public Response<Object> compatibleDelete(@RequestBody CompatibleFileReq req) {
        Integer type = req.getType();

        if (CompatibleFileEnum.ORDER_ATTACHMENT.type.equals(type)) {
            DeleteOrderAttachmentByLogicDTO deleteOrderAttachmentByLogicDTO = new DeleteOrderAttachmentByLogicDTO();
            deleteOrderAttachmentByLogicDTO.setUpdatedBy(super.getUserName());
            deleteOrderAttachmentByLogicDTO.setOrderId(Integer.valueOf(req.getObjId()));
            deleteOrderAttachmentByLogicDTO.setFileUrl(req.getFileUrl());
            return orderRemote.deleteOrderAttachmentByLogic(deleteOrderAttachmentByLogicDTO);
        } else if (CompatibleFileEnum.SUPPLY_ORDER_ATTACHMENT.type.equals(type)) {
            DeleteSupplyOrderAttachmentByLogicDTO deleteSupplyOrderAttachmentByLogicDTO = new DeleteSupplyOrderAttachmentByLogicDTO();
            deleteSupplyOrderAttachmentByLogicDTO.setUpdatedBy(super.getUserName());
            deleteSupplyOrderAttachmentByLogicDTO.setSupplyOrderId(Integer.valueOf(req.getObjId()));
            deleteSupplyOrderAttachmentByLogicDTO.setFileUrl(req.getFileUrl());
            return supplyOrderRemote.deleteSupplyOrderAttachmentByLogic(deleteSupplyOrderAttachmentByLogicDTO);
        } else {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
    }

}
