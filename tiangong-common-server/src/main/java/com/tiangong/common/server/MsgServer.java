package com.tiangong.common.server;

import com.tiangong.common.Response;
import com.tiangong.common.domain.entity.MsgEntity;
import com.tiangong.common.domain.req.QueryMsgPage;
import com.tiangong.common.domain.req.ReadMsgReq;
import com.tiangong.common.service.MsgService;
import com.tiangong.dto.common.AddMsgDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/15 10:58
 */
@RestController
@RequestMapping("/common/msg")
public class MsgServer {

    @Autowired
    private MsgService msgService;

    /**
     * 分页查询消息列表
     */
    @PostMapping("queryMsgPage")
    @PreAuthorize("@syyo.check('msg')")
    public Response<PaginationSupportDTO<MsgEntity>> queryMsgPage(@RequestBody QueryMsgPage queryMsgPage){
        return Response.success(msgService.queryMsgPage(queryMsgPage));
    }

    /**
     * 分页查询消息列表
     */
    @PostMapping("queryMsgListToNoRead")
    @PreAuthorize("@syyo.check('msg')")
    public Response<List<MsgEntity>> queryMsgListToNoRead(){
        return Response.success(msgService.queryMsgListToNoRead());
    }

    /**
     * 新增消息
     */
    @PostMapping("addMsg")
    @PreAuthorize("@syyo.check('msg')")
    public Response<Object> addMsg(@Validated @RequestBody AddMsgDTO addMsgDTO){
        msgService.addMsg(addMsgDTO);
        return Response.success();
    }

    /**
     * 一键读取消息
     */
    @PostMapping("readMsg")
    @PreAuthorize("@syyo.check('msg')")
    public Response<Object> readMsg(){
        msgService.readMsg();
        return Response.success();
    }

    /**
     * 读取单条消息
     */
    @PostMapping("readOneMsg")
    @PreAuthorize("@syyo.check('msg')")
    public Response<Object> readOneMsg(@Validated @RequestBody ReadMsgReq readMsgReq){
        msgService.readOneMsg(readMsgReq);
        return Response.success();
    }

}
