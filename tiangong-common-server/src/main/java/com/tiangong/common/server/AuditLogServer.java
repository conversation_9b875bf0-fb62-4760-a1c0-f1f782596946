package com.tiangong.common.server;

import com.tiangong.enums.OperationTypeEnum;
import com.tiangong.operatelog.domain.OperateLogSearchDTO;
import com.tiangong.operatelog.domain.OperateLogSearchReq;
import com.tiangong.operatelog.domain.QueryOperateLogDTO;
import com.tiangong.operatelog.mapper.OperateLogMapper;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.CommonConstants;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023/3/29 17:46
 * @Description:
 */
@RestController
@Slf4j
@RequestMapping(value = "/common")
public class AuditLogServer {

    @Autowired
    private OperateLogMapper operateLogMapper;

    /**
     * 查询操作日志列表（分页）
     */
    @RequestMapping(value = "/auditLog/exportAuditLog", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public void exportAuditLog(@RequestBody OperateLogSearchReq req, HttpServletResponse response) {
        QueryOperateLogDTO queryOperateLogDTO = new QueryOperateLogDTO();
        queryOperateLogDTO.setOperateAccount(req.getOperateAccount());
        queryOperateLogDTO.setLogType(req.getLogType());
        if (req.getOperateStartDate() != null && req.getOperateEndDate() != null) {
            queryOperateLogDTO.setOperateStartDate(DateUtilX.dateToString(req.getOperateStartDate()));
            queryOperateLogDTO.setOperateEndDate(DateUtilX.dateToString(DateUtilX.addDate(req.getOperateEndDate(), 1)));
        }
        queryOperateLogDTO.setOperateResult(req.getOperateResult());
        queryOperateLogDTO.setOperationType(req.getOperationType());

        OutputStream output = null;
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        ByteArrayInputStream byteArrayInputStream = null;

        try {

            SXSSFWorkbook workbook = new SXSSFWorkbook(5000);
            Row row = null;
            Sheet sheet = workbook.createSheet("审计日志");
            Row rowTop = sheet.createRow(0);
            rowTop.createCell(0).setCellValue("操作时间");
            rowTop.createCell(1).setCellValue("操作人");
            rowTop.createCell(2).setCellValue("IP地址");
            rowTop.createCell(3).setCellValue("事件类型");
            rowTop.createCell(4).setCellValue("操作类型");
            rowTop.createCell(5).setCellValue("操作内容");
            rowTop.createCell(6).setCellValue("操作结果");

            int currentPage = 0;
            long pageSize = 10000L;
            int breakNum = 10000;
            int rowNum = 1;
            queryOperateLogDTO.setPageSize(pageSize);
            while (currentPage < breakNum) {
                queryOperateLogDTO.setCurrentPage(currentPage * pageSize);
                List<OperateLogSearchDTO> operateLogSearchDTOS = operateLogMapper.queryListPage(queryOperateLogDTO);
                if (CollUtilX.isNotEmpty(operateLogSearchDTOS)) {
                    for (OperateLogSearchDTO operateLogSearchDTO : operateLogSearchDTOS) {
                        row = sheet.createRow(rowNum++);
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConstants.DATE_TIME_FORMAT);
                        String createdDt = operateLogSearchDTO.getCreatedDt().format(formatter);
                        row.createCell(0).setCellValue(createdDt);
                        row.createCell(1).setCellValue(operateLogSearchDTO.getCreatedBy());
                        row.createCell(2).setCellValue(operateLogSearchDTO.getRequestIp());
                        if (operateLogSearchDTO.getLogLevel() != null) {
                            row.createCell(3).setCellValue(operateLogSearchDTO.getLogLevel() == 0 ? "系统级事件" : "业务级事件");
                        }
                        String operationNameByOperationType = OperationTypeEnum.getOperationNameByOperationType(operateLogSearchDTO.getOperationType());
                        if (StrUtilX.isNotEmpty(operationNameByOperationType)) {
                            row.createCell(4).setCellValue(operationNameByOperationType);
                        }
                        if (StrUtilX.isNotEmpty(operateLogSearchDTO.getRequestParam())) {
                            row.createCell(5).setCellValue(operateLogSearchDTO.getLogName() + ": " + operateLogSearchDTO.getRequestParam());
                        } else {
                            row.createCell(5).setCellValue(operateLogSearchDTO.getLogName());
                        }
                        if (operateLogSearchDTO.getOperationResult() != null) {
                            row.createCell(6).setCellValue(operateLogSearchDTO.getOperationResult() == 0 ? "失败" : "成功");
                        }
                    }
                } else {
                    break;
                }
                currentPage++;
            }
            sheet.setColumnWidth(0, 25 * 256);
            sheet.setColumnWidth(1, 25 * 256);
            sheet.setColumnWidth(2, 25 * 256);
            sheet.setColumnWidth(3, 25 * 256);
            sheet.setColumnWidth(4, 25 * 256);
            sheet.setColumnWidth(5, 25 * 256);
            sheet.setColumnWidth(6, 25 * 256);

            ByteArrayOutputStream outputTmp = new ByteArrayOutputStream();
            workbook.write(outputTmp);
            outputTmp.flush();
            byte[] byteArray = outputTmp.toByteArray();
            IOUtils.closeQuietly(outputTmp);
            byteArrayInputStream = new ByteArrayInputStream(byteArray, 0, byteArray.length);

            //告诉浏览器用什么软件可以打开此文件
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            // 下载文件的默认名称
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("审计日志.xlsx", "UTF-8"));
            output = response.getOutputStream();
            bis = new BufferedInputStream(byteArrayInputStream);
            bos = new BufferedOutputStream(output);
            //每次50M
            byte[] buff = new byte[1024 * 1024 * 50];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("exportAuditLog has error", e);
        } finally {
            if (null != bos) {
                IOUtils.closeQuietly(bos);
            }
            if (null != bis) {
                IOUtils.closeQuietly(bis);
            }
            if (null != output) {
                IOUtils.closeQuietly(output);
            }
            if (null != byteArrayInputStream) {
                IOUtils.closeQuietly(byteArrayInputStream);
            }
        }
    }

}
