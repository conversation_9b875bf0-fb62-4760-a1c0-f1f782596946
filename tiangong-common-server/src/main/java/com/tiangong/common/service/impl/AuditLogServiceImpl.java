package com.tiangong.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.NumberConstant;
import com.tiangong.common.domain.entity.AuditLogCountAggregationEntity;
import com.tiangong.common.domain.entity.AuditLogEntity;
import com.tiangong.common.domain.req.AuditLogReq;
import com.tiangong.common.domain.req.AuditLogSearchReq;
import com.tiangong.common.domain.req.AuditLogStatisticsReq;
import com.tiangong.common.domain.resp.AuditLogResp;
import com.tiangong.common.domain.resp.AuditLogSearchResp;
import com.tiangong.common.domain.resp.AuditLogStatisticsResp;
import com.tiangong.common.mapper.AuditLogCountAggregationMapper;
import com.tiangong.common.mapper.AuditLogMapper;
import com.tiangong.common.service.AuditLogService;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.AuditLogAddReq;
import com.tiangong.enums.AuditLogEnum;
import com.tiangong.enums.EventResultEnum;
import com.tiangong.enums.OperationTypeEnum;
import com.tiangong.enums.TargetTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.operatelog.domain.*;
import com.tiangong.operatelog.mapper.OperateLogCountAggregationMapper;
import com.tiangong.operatelog.mapper.OperateLogMapper;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
@Slf4j
@Service
public class AuditLogServiceImpl extends ServiceImpl<AuditLogMapper, AuditLogEntity> implements AuditLogService {

    /**
     * 日期偏移量（天）
     */
    private static final int DATE_OFFSET_DAYS = 1;

    /**
     * 最小配置容量
     */
    private static final int MIN_CONFIGURE_CAPACITY = 1;

    /**
     * 默认配置容量
     */
    private static final double DEFAULT_CONFIGURE_CAPACITY = 1.0;

    /**
     * 用户类型日志范围起始值
     */
    private static final int USER_LOG_RANGE_START = 1;

    /**
     * 用户类型日志范围结束值
     */
    private static final int USER_LOG_RANGE_END = 10000;

    /**
     * 用户解析类型日志范围起始值
     */
    private static final int USER_PARSE_LOG_RANGE_START = 10001;

    /**
     * 用户解析类型日志范围结束值
     */
    private static final int USER_PARSE_LOG_RANGE_END = 20000;

    /**
     * 修改用户信息日志编号
     */
    private static final int UPDATE_USER_INFO_LOG_NO = 10001;

    @Autowired
    private AuditLogMapper auditLogMapper;

    @Autowired
    private AuditLogCountAggregationMapper auditLogCountAggregationMapper;

    @Autowired
    private OperateLogMapper operateLogMapper;

    @Autowired
    private OperateLogCountAggregationMapper operateLogCountAggregationMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    HttpServletRequest request;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditLogAdd(AuditLogAddReq req) {
        // 用户类型处理
        if (Objects.equals(req.getTargetType(), TargetTypeEnum.USER_TYPE.getNo()) || Objects.equals(req.getTargetType(), TargetTypeEnum.EMPLOYEE_USER.getNo())) {
            processUserAuditLogs(req);
        } else if (Objects.equals(req.getTargetType(), TargetTypeEnum.ORDER_TYPE.getNo())) {
            insertOrderCommonAuditLogs(req);
        } else {
            insertAuditLogsResult(req);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditLogDel(AuditLogReq req) {
        auditLogMapper.deleteById(req.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditLogEdit(AuditLogReq req) {
        AuditLogEntity entity = CommonDtoConvert.INSTANCE.convertAuditLogEntity(req);
        auditLogMapper.updateById(entity);
    }

    @Override
    public AuditLogResp auditLogDetail(AuditLogReq req) {
        AuditLogEntity entity = auditLogMapper.selectById(req.getId());
        if (entity == null) {
            return null;
        }
        return CommonDtoConvert.INSTANCE.convertAuditLogResp(entity);
    }

    @Override
    public List<AuditLogResp> auditLogList(AuditLogReq req) {
        List<AuditLogEntity> list = auditLogMapper.selectList(null);
        return CommonDtoConvert.INSTANCE.convertAuditLogResp(list);
    }

    @Override
    public PageVo auditLogPage(AuditLogSearchReq req) {
        Page<AuditLogEntity> auditLogPageInfo = new Page<AuditLogEntity>(req.getPage(), req.getPageSize());
        QueryWrapper<AuditLogEntity> wrapper = new QueryWrapper<>();
        // ===========开始业务==============

        wrapper.between(req.getOperateStartDate() != null && req.getOperateEndDate() != null, "created_dt", req.getOperateStartDate(), req.getOperateEndDate())
                .eq(StrUtilX.isNotEmpty(req.getOperateAccount()), "user_account", req.getOperateAccount())
                .eq(req.getLogType() != null, "event_type", req.getLogType())
                .eq(req.getOperateResult() != null, "event_result", req.getOperateResult());

        // ===========结束业务==============
        IPage<AuditLogEntity> ipage = auditLogMapper.selectPage(auditLogPageInfo, wrapper);

        List<AuditLogSearchResp> collect = ipage.getRecords().stream().map((item) -> {
            AuditLogSearchResp resp = new AuditLogSearchResp();
            BeanUtils.copyProperties(item, resp);
            resp.setCreatedBy(item.getUserAccount() + "/" + item.getCreatedBy());
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

    @Override
    public PageVo findAuditLogSearchPage(AuditLogSearchReq req) {
        Page<AuditLogEntity> auditLogPageInfo = new Page<>(req.getCurrentPage(), req.getPageSize());
        QueryWrapper<AuditLogEntity> wrapper = new QueryWrapper<>();
        // ===========开始业务==============
        wrapper.between(req.getOperateStartDate() != null && req.getOperateEndDate() != null, "created_dt", req.getOperateStartDate(), req.getOperateEndDate() == null ? null : DateUtilX.addDate(req.getOperateEndDate(), DATE_OFFSET_DAYS))
                .eq(StrUtilX.isNotEmpty(req.getOperateAccount()), "user_account", req.getOperateAccount())
                .eq(req.getLogType() != null, "event_type", req.getLogType())
                .eq(req.getOperateResult() != null, "event_result", req.getOperateResult())
                .orderByDesc("created_dt");

        // ===========结束业务==============
        IPage<AuditLogEntity> ipage = auditLogMapper.selectPage(auditLogPageInfo, wrapper);

        List<AuditLogSearchResp> collect = ipage.getRecords().stream().map((item) -> {
            AuditLogSearchResp resp = new AuditLogSearchResp();
            BeanUtils.copyProperties(item, resp);
            resp.setCreatedBy(item.getUserAccount() + "/" + item.getCreatedBy());
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

    @Override
    public PageVo findAuditLogStatisticsPage(AuditLogStatisticsReq req) {

        LoginUser loginUser = TokenManager.getUser(request);
        Page<AuditLogCountAggregationEntity> auditLogPageInfo = new Page<>(req.getCurrentPage(), req.getPageSize());
        QueryWrapper<AuditLogCountAggregationEntity> wrapper = new QueryWrapper<>();

        LambdaQueryWrapper<AuditLogCountAggregationEntity> auditLogCountAggregationEntityLambdaQueryWrapper = wrapper.lambda().select(AuditLogCountAggregationEntity::getUserAccount, AuditLogCountAggregationEntity::getCreatedBy, AuditLogCountAggregationEntity::getEventType, AuditLogCountAggregationEntity::getEventResult, AuditLogCountAggregationEntity::getEventCount)
                .between(req.getOperateStartDate() != null && req.getOperateEndDate() != null, AuditLogCountAggregationEntity::getCreatedDt, req.getOperateStartDate(), req.getOperateEndDate() == null ? null : DateUtilX.addDate(req.getOperateEndDate(), DATE_OFFSET_DAYS))
                .eq(StrUtilX.isNotEmpty(req.getOperateAccount()), AuditLogCountAggregationEntity::getUserAccount, req.getOperateAccount())
                .eq(req.getLogType() != null, AuditLogCountAggregationEntity::getEventType, req.getLogType())
                .eq(req.getOperateResult() != null, AuditLogCountAggregationEntity::getEventResult, req.getOperateResult())
                .groupBy(AuditLogCountAggregationEntity::getUserAccount, AuditLogCountAggregationEntity::getEventType, AuditLogCountAggregationEntity::getEventResult)
                .orderByDesc(AuditLogCountAggregationEntity::getUserAccount, AuditLogCountAggregationEntity::getEventType, AuditLogCountAggregationEntity::getEventResult);


        IPage<AuditLogCountAggregationEntity> ipage = auditLogCountAggregationMapper.selectPage(auditLogPageInfo, auditLogCountAggregationEntityLambdaQueryWrapper);

        List<AuditLogStatisticsResp> collect = ipage.getRecords().stream().map((item) -> {
            AuditLogStatisticsResp resp = new AuditLogStatisticsResp();
            BeanUtils.copyProperties(item, resp);
            resp.setCreatedBy(item.getUserAccount() + "/" + item.getCreatedBy());
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

    @Override
    public PageVo findAuditLogSearch(OperateLogSearchReq req) {
        Page<OperateLogEntity> auditLogPageInfo = new Page<OperateLogEntity>(req.getCurrentPage(), req.getPageSize());
        QueryWrapper<OperateLogEntity> wrapper = new QueryWrapper<>();
        // ===========开始业务==============
        wrapper.between(req.getOperateStartDate() != null && req.getOperateEndDate() != null, "created_dt", req.getOperateStartDate(), req.getOperateEndDate() == null ? null : DateUtilX.addDate(req.getOperateEndDate(), DATE_OFFSET_DAYS))
                .like(StrUtilX.isNotEmpty(req.getOperateAccount()), "user_account", req.getOperateAccount())
                .eq(req.getLogType() != null, "log_level", req.getLogType())
                .eq(req.getOperationType() != null, "operation_type", req.getOperationType())
                .eq(req.getOperateResult() != null, "operation_result", req.getOperateResult())
                .orderByDesc("created_dt");

        // ===========结束业务==============
        IPage<OperateLogEntity> ipage = operateLogMapper.selectPage(auditLogPageInfo, wrapper);

        List<OperateLogSearchResp> collect = ipage.getRecords().stream().map((item) -> {
            OperateLogSearchResp resp = new OperateLogSearchResp();
            resp.setCreatedDt(item.getCreatedDt());
            resp.setCreatedBy(item.getCreatedBy());
            resp.setEventType(item.getLogLevel());
            if (StrUtilX.isNotEmpty(item.getRequestParam())) {
                resp.setEventDescription(item.getLogName() + ": " + item.getRequestParam());
            } else {
                resp.setEventDescription(item.getLogName());
            }
            resp.setEventResult(item.getOperationResult());
            resp.setIpAddress(item.getRequestIp());
            String operationNameByOperationType = OperationTypeEnum.getOperationNameByOperationType(item.getOperationType());
            if (StrUtilX.isNotEmpty(operationNameByOperationType)) {
                resp.setOperateTypeName(operationNameByOperationType);
            }
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

    @Override
    public List<OperateLogStatisticsResp> findAuditLogStatistics(OperateLogStatisticsReq req) {
        QueryWrapper<OperateLogCountAggregationEntity> wrapper = new QueryWrapper<>();

        LambdaQueryWrapper<OperateLogCountAggregationEntity> operateLogCountAggregationEntityLambdaQueryWrapper = wrapper.lambda().select(OperateLogCountAggregationEntity::getOperationType, OperateLogCountAggregationEntity::getCount).groupBy(OperateLogCountAggregationEntity::getOperationType)
                .between(req.getOperateStartDate() != null && req.getOperateEndDate() != null, OperateLogCountAggregationEntity::getCreatedDt, req.getOperateStartDate(), req.getOperateEndDate() == null ? null : DateUtilX.addDate(req.getOperateEndDate(), DATE_OFFSET_DAYS))
                .like(StrUtilX.isNotEmpty(req.getOperateAccount()), OperateLogCountAggregationEntity::getUserAccount, req.getOperateAccount());


        List<Map<String, Object>> operateLogCountAggregationMaps = operateLogCountAggregationMapper.selectMaps(operateLogCountAggregationEntityLambdaQueryWrapper);
        List<OperateLogStatisticsResp> result = new ArrayList<>();
        if (CollUtilX.isNotEmpty(operateLogCountAggregationMaps)) {
            long sum = 0L;
            Map<Integer, Long> mapTemp = new HashMap<>();
            for (Map<String, Object> operateLogCountAggregationMap : operateLogCountAggregationMaps) {
                if (operateLogCountAggregationMap.get("operation_type") != null) {
                    Integer operationType = (Integer) operateLogCountAggregationMap.get("operation_type");
                    long count = operateLogCountAggregationMap.get("count") == null ? 0L : (Long) operateLogCountAggregationMap.get("count");
                    mapTemp.put(operationType, count);
                    sum += count;
                }
            }


            for (OperationTypeEnum value : OperationTypeEnum.values()) {
                OperateLogStatisticsResp operateLogStatisticsResp = new OperateLogStatisticsResp();
                operateLogStatisticsResp.setOperationName(value.operationName);
                operateLogStatisticsResp.setOperationCount(0L);
                if (mapTemp.containsKey(value.operationType)) {
                    operateLogStatisticsResp.setOperationCount(mapTemp.get(value.operationType));
                }
                BigDecimal bd = new BigDecimal((double) operateLogStatisticsResp.getOperationCount() / sum);
                BigDecimal bdMultiply = NumberConstant.ONE_HUNDRED;
                double temp = bd.setScale(4, RoundingMode.HALF_UP).multiply(bdMultiply).doubleValue();
                operateLogStatisticsResp.setPercentage(temp);
                result.add(operateLogStatisticsResp);
            }
        } else {
            result = null;
        }

        return result;
    }

    @Override
    public void saveAuditSetting(SaveAuditSettingReq req) {
        if (req.getConfigureCapacity() < MIN_CONFIGURE_CAPACITY) {
            throw new SysException(ResultEnum.E_1005.getCode(), ResultEnum.E_1005.getMessage());
        }
        // 将审计日志设置保存到redis
        stringRedisTemplate.opsForValue().set(RedisKey.AUDIT_SETTING, JSON.toJSONString(req));
    }

    @Override
    public SaveAuditSettingReq queryAuditSetting() {
        String auditSetting = stringRedisTemplate.opsForValue().get(RedisKey.AUDIT_SETTING);
        SaveAuditSettingReq saveAuditSettingReq = null;
        if (StrUtilX.isNotEmpty(auditSetting)) {
            saveAuditSettingReq = JSON.parseObject(auditSetting, SaveAuditSettingReq.class);
        } else {
            saveAuditSettingReq = new SaveAuditSettingReq();
            saveAuditSettingReq.setConfigureCapacity(DEFAULT_CONFIGURE_CAPACITY);
            saveAuditSettingReq.setIgnoreStartTime(new Date());
            saveAuditSettingReq.setIgnoreEndTime(DateUtilX.addDate(new Date(), DATE_OFFSET_DAYS));
        }
        return saveAuditSettingReq;
    }

    /**
     * 保存审计日志
     */
    public void insertAuditLogsResult(AuditLogAddReq req) {
        AuditLogEntity auditLogEntity = CommonDtoConvert.INSTANCE.convertAuditLogEntity(req);
        auditLogEntity.setEventType(AuditLogEnum.getEventTypeByNo(req.getAuditLogEnumNo()));
        auditLogEntity.setEventDescription(AuditLogEnum.getDescByNo(req.getAuditLogEnumNo()) + EventResultEnum.getDescByNo(req.getEventResult()));
        auditLogMapper.insert(auditLogEntity);
    }

    /**
     * 保存审计日志
     */
    public void insertOrderCommonAuditLogs(AuditLogAddReq req) {
        AuditLogEntity auditLogEntity = CommonDtoConvert.INSTANCE.convertAuditLogEntity(req);
        auditLogEntity.setEventType(AuditLogEnum.getEventTypeByNo(req.getAuditLogEnumNo()));
        auditLogEntity.setEventDescription(req.getTarget());
        auditLogMapper.insert(auditLogEntity);
    }

    /**
     * 保存审计日志
     */
    public void processUserAuditLogs(AuditLogAddReq req) {
        // 用户类型日志范围表示不需要解析，直接取描述+操作结果即可
        if (req.getAuditLogEnumNo() >= USER_LOG_RANGE_START && req.getAuditLogEnumNo() <= USER_LOG_RANGE_END) {
            AuditLogEntity auditLogEntity = CommonDtoConvert.INSTANCE.convertAuditLogEntity(req);
            auditLogEntity.setEventType(AuditLogEnum.getEventTypeByNo(req.getAuditLogEnumNo()));
            auditLogEntity.setEventDescription(AuditLogEnum.getDescByNo(req.getAuditLogEnumNo()) + EventResultEnum.getDescByNo(req.getEventResult()));
            auditLogMapper.insert(auditLogEntity);
        } else if (req.getAuditLogEnumNo() >= USER_PARSE_LOG_RANGE_START && req.getAuditLogEnumNo() <= USER_PARSE_LOG_RANGE_END) {
            // 用户解析类型日志范围表示需要解析,根据实际情况记录对应的日志信息
            // 修改用户信息身份证,密码,手机号省略
            if (req.getAuditLogEnumNo() == UPDATE_USER_INFO_LOG_NO) {
                UserPO userPO = JSON.parseObject(req.getTarget(), UserPO.class);
                userPO.setUserIDNumber(userPO.getUserIDNumber() == null ? null : "******");
                userPO.setUserTel(userPO.getUserTel() == null ? null : "******");
                userPO.setUserPwd(userPO.getUserPwd() == null ? null : "******");
                AuditLogEntity auditLogEntity = CommonDtoConvert.INSTANCE.convertAuditLogEntity(req);
                auditLogEntity.setEventType(AuditLogEnum.getEventTypeByNo(req.getAuditLogEnumNo()));
                auditLogEntity.setEventDescription("用户信息修改为:" + JSON.toJSONString(userPO));
                auditLogMapper.insert(auditLogEntity);
            }
        }

    }


}