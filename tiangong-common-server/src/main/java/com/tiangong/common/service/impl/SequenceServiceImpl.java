package com.tiangong.common.service.impl;

import com.tiangong.common.StrPool;
import com.tiangong.common.domain.SequencePO;
import com.tiangong.common.mapper.SequenceMapper;
import com.tiangong.common.service.SequenceService;
import com.tiangong.enums.SystemCodeEnum;
import com.tiangong.finance.OrgDTO;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用途：TODO
 *
 * @author：Owen
 */
@Slf4j
@Service("sequenceService")
public class SequenceServiceImpl implements SequenceService {

    @Autowired
    private SequenceMapper sequenceMapper;
    public static final String FLAG = "_flag";

    public static final String SEQNAME = "seqName";

    public static final String CREATECODE = "createcode_flag";

    /**
     * 日期格式化类型
     */
    private static final int DATE_FORMAT_TYPE = 2;

    /**
     * 日期字符串截取起始位置
     */
    private static final int DATE_SUBSTRING_START = 2;

    /**
     * 循环起始值
     */
    private static final int LOOP_START = 1;

    /**
     * 批量生成数量
     */
    private static final int BATCH_GENERATE_COUNT = 100;

    /**
     * 批量生成数量（Long类型）
     */
    private static final long BATCH_GENERATE_COUNT_LONG = 100L;

    /**
     * 分隔符替换结果
     */
    private static final String SEPARATOR_REPLACE_RESULT = "";

    @Override
    public void createCode(Map<String, String> requestMap) {
        try {
            if (null == RedisTemplateX.get(CREATECODE)) {
                // 流程加锁
                RedisTemplateX.set(CREATECODE, "1");
                List<SequencePO> sequencePOList = sequenceMapper.selectAll();
                if (null != requestMap && StrUtilX.isNotEmpty(requestMap.get(SEQNAME))) {
                    for (SequencePO sequencePO : sequencePOList) {
                        if (sequencePO.getSeqName().equals(requestMap.get(SEQNAME))) {
                            createCode(sequencePO);
                        }
                    }
                } else {
                    for (SequencePO sequencePO : sequencePOList) {
                        createCode(sequencePO);
                    }
                }
                // 释放锁
                RedisTemplateX.delete(CREATECODE);
            }
        } catch (Exception e) {
            log.error("批量生成编码失败", e);
        } finally {
            // 释放锁
            RedisTemplateX.delete(CREATECODE);
        }
    }

    /**
     * 生成编码
     */
    private void createCode(SequencePO sequencePO) {
        try {
            // 获取锁
            if (null == RedisTemplateX.get(sequencePO.getSeqName() + FLAG)) {
                Long listCount = RedisTemplateX.lLen(sequencePO.getSeqName());
                // list查询错误或者格式不对，直接删除
                if (null == listCount) {
                    RedisTemplateX.delete(sequencePO.getSeqName());
                }

                // 列表为空时，创建编码
                if (listCount.equals(0L)) {
                    // 加锁
                    RedisTemplateX.set(sequencePO.getSeqName() + FLAG, "1");
                    List<String> codeList = new ArrayList<>();
                    String startLetter = SystemCodeEnum.getLetterByCode(sequencePO.getSeqName());
                    // 订单编码或供货单编码需要加商家简码
                    if (sequencePO.getSeqName().equals(SystemCodeEnum.ORDERCODE.code) || sequencePO.getSeqName().equals(SystemCodeEnum.SUPPLYORDERCODE.code)) {
                        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                        if (StrUtilX.isNotEmpty(orgDTO.getOrgBrevityCode())) {
                            startLetter += orgDTO.getOrgBrevityCode();
                        }
                    }
                    if (sequencePO.getSeqName().equals(SystemCodeEnum.ORDERCODE.code) || sequencePO.getSeqName().equals(SystemCodeEnum.SUPPLYORDERCODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SUPPLIERSETTLEWORKORDERCODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_SETTLE_JOB_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_SETTLE_PAY_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_ORDER_REFUND_TASK_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_ORDER_DEDUCTION_TASK_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_ORDER_STATEMENT_TASK_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.CHANGE_WORD_ORDER_TASK_CODE.code)) {
                        String dayStr = DateUtilX.getCurrentDateStr(DATE_FORMAT_TYPE).replace(StrPool.DASHED, SEPARATOR_REPLACE_RESULT);
                        startLetter += dayStr.substring(DATE_SUBSTRING_START, dayStr.length());
                    }

                    for (int i = LOOP_START; i <= BATCH_GENERATE_COUNT; i++) {
                        codeList.add(startLetter + (sequencePO.getCurrentVal() + i));
                    }

                    sequencePO.setCurrentVal(sequencePO.getCurrentVal() + BATCH_GENERATE_COUNT_LONG);
                    // 将最新值写入数据库
                    sequenceMapper.updateByPrimaryKey(sequencePO);
                    // 将编码写入redis
                    RedisTemplateX.lLeftPushAll(sequencePO.getSeqName(), codeList);
                    // 释放锁
                    RedisTemplateX.delete(sequencePO.getSeqName() + FLAG);
                }
            }
        } catch (Exception e) {
            log.error("生成编码失败！", e);
        } finally {
            // 释放锁
            RedisTemplateX.delete(sequencePO.getSeqName() + FLAG);
        }
    }
}
