package com.tiangong.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.domain.entity.DictionaryEntity;
import com.tiangong.common.domain.resp.DictionaryResp;
import com.tiangong.common.req.QueryDictionaryReq;
import com.tiangong.dto.common.PaginationSupportDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/18 11:28
 */
public interface DictionaryService extends IService<DictionaryEntity> {

    /**
     * 查询字典表分页
     *
     * @param req 查询字典请求，包含分页和查询条件
     * @return 返回分页的字典实体数据
     */
    PaginationSupportDTO<DictionaryEntity> queryDictionaryPage(QueryDictionaryReq req);

    /**
     * 查询字典表列表
     *
     * @param req 查询字典请求，包含查询条件
     * @return 返回字典响应列表
     */
    List<DictionaryResp> queryDictionaryList(QueryDictionaryReq req);

    /**
     * 查询字典表分类类型
     *
     * @return 返回字典实体列表，包含所有分类类型
     */
    List<DictionaryEntity> queryDictionaryTypeList();

    /**
     * 新增或修改字典表数据
     *
     * @param dictionaryEntity 字典实体，包含字典数据信息
     */
    void addOrUpdateDictionary(DictionaryEntity dictionaryEntity);
}
