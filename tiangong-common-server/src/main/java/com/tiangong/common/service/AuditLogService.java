package com.tiangong.common.service;


import com.baomidou.mybatisplus.extension.service.IService;

import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.dto.common.AuditLogAddReq;
import com.tiangong.common.domain.entity.AuditLogEntity;
import com.tiangong.common.domain.req.AuditLogReq;
import com.tiangong.common.domain.req.AuditLogSearchReq;
import com.tiangong.common.domain.req.AuditLogStatisticsReq;
import com.tiangong.common.domain.resp.AuditLogResp;
import com.tiangong.operatelog.domain.OperateLogSearchReq;
import com.tiangong.operatelog.domain.OperateLogStatisticsReq;
import com.tiangong.operatelog.domain.OperateLogStatisticsResp;
import com.tiangong.operatelog.domain.SaveAuditSettingReq;


import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
public interface AuditLogService extends IService<AuditLogEntity> {

    /**
     * 新增审计日志
     *
     * @param req 审计日志新增请求参数，包含日志信息
     */
    void auditLogAdd(AuditLogAddReq req);

    /**
     * 删除审计日志
     *
     * @param req 审计日志删除请求参数，包含删除条件
     */
    void auditLogDel(AuditLogReq req);

    /**
     * 编辑审计日志
     *
     * @param req 审计日志编辑请求参数，包含更新信息
     */
    void auditLogEdit(AuditLogReq req);

    /**
     * 审计日志详情
     *
     * @param req 审计日志详情查询请求参数，包含查询条件
     * @return 返回审计日志详情信息
     */
    AuditLogResp auditLogDetail(AuditLogReq req);

    /**
     * 审计日志列表（全部）
     *
     * @param req 审计日志列表查询请求参数，包含查询条件
     * @return 返回审计日志列表数据
     */
    List<AuditLogResp> auditLogList(AuditLogReq req);

    /**
     * 审计日志列表（分页）
     *
     * @param req 审计日志分页查询请求参数，包含分页和查询条件
     * @return 返回分页的审计日志数据
     */
    PageVo auditLogPage(AuditLogSearchReq req);

    /**
     * 审计日志查询(分页)
     *
     * @param req 审计日志搜索分页请求参数，包含搜索和分页条件
     * @return 返回分页的审计日志搜索结果
     */
    PageVo findAuditLogSearchPage(AuditLogSearchReq req);

    /**
     * 审计统计查询(分页)
     *
     * @param req 审计日志统计分页请求参数，包含统计和分页条件
     * @return 返回分页的审计日志统计结果
     */
    PageVo findAuditLogStatisticsPage(AuditLogStatisticsReq req);

    /**
     * 审计日志查询(分页)(新)
     *
     * @param req 操作日志搜索请求参数，包含搜索和分页条件
     * @return 返回分页的操作日志搜索结果
     */
    PageVo findAuditLogSearch(OperateLogSearchReq req);

    /**
     * 审计统计查询(分页)(新)
     *
     * @param req 操作日志统计请求参数，包含统计条件
     * @return 返回操作日志统计结果列表
     */
    List<OperateLogStatisticsResp> findAuditLogStatistics(OperateLogStatisticsReq req);

    /**
     * 保存审计设置
     *
     * @param req 审计设置保存请求参数，包含设置信息
     */
    void saveAuditSetting(SaveAuditSettingReq req);

    /**
     * 查询审计日志设置
     *
     * @return 返回当前的审计设置信息
     */
    SaveAuditSettingReq queryAuditSetting();

}

