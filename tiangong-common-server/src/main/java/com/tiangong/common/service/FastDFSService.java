package com.tiangong.common.service;


import com.tiangong.cloud.common.domain.FileUpload;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Auther: wangzhong
 * @Date: 2020/8/18 10:17
 * @Description:
 */
public interface FastDFSService {

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    FileUpload uploadFast(MultipartFile file);


    /**
     * 下载文件
     *
     * @param url 文件URL地址，用于下载文件
     */
    void download(String url);

    /**
     * 删除文件
     *
     * @param url 文件URL地址，用于删除文件
     * @return 返回删除操作结果状态码
     */
    int delete(String url);
}
