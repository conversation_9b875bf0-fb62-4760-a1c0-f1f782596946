package com.tiangong.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.domain.entity.AuthMenuEntity;
import com.tiangong.common.domain.entity.ProProductFilterStrategyEntity;
import com.tiangong.common.domain.req.AddMenuPermissionsReq;
import com.tiangong.common.domain.req.AddRoleAndMenuReq;
import com.tiangong.common.domain.req.EditLogSettingReq;
import com.tiangong.common.domain.resp.QueryLogSettingResp;
import com.tiangong.common.mapper.AuthMenuMapper;
import com.tiangong.common.mapper.ProProductFilterStrategyMapper;
import com.tiangong.common.service.OtherApiService;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.user.domain.RoleMenuPO;
import com.tiangong.user.mapper.RoleMenuMapper;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/7/25 14:55
 * @Description:
 */

@Slf4j
@Service
public class OtherApiServiceImpl implements OtherApiService {

    @Autowired
    HttpServletRequest request;

    final int fixedNumber = 1000;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RoleMenuMapper roleMenuMapper;

    @Autowired
    private ProProductFilterStrategyMapper proProductFilterStrategyMapper;

    @Autowired
    private AuthMenuMapper authMenuMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRoleAndMenu(AddRoleAndMenuReq req) {
        if (req.getRoleId() == null || req.getMenuId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        Example example = new Example(RoleMenuPO.class);
        example.createCriteria().andEqualTo("roleId", req.getRoleId()).andEqualTo("menuId", req.getMenuId());
        List<RoleMenuPO> roleMenuPOS = roleMenuMapper.selectByExample(example);
        if (CollUtilX.isNotEmpty(roleMenuPOS)) {
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "已存在!");
        }

        RoleMenuPO roleMenuPO = new RoleMenuPO();
        roleMenuPO.setRoleId(req.getRoleId());
        roleMenuPO.setMenuId(req.getMenuId());
        roleMenuMapper.insertSelective(roleMenuPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMenuPermissions(AddMenuPermissionsReq req) {
        LoginUser loginUser = TokenManager.getUser(request);

        if (StrUtilX.isEmpty(req.getParentCode()) || StrUtilX.isEmpty(req.getMenuName()) || StrUtilX.isEmpty(req.getFrontEndUrl())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        List<AuthMenuEntity> authMenuList = authMenuMapper.selectList(new QueryWrapper<AuthMenuEntity>().lambda().eq(AuthMenuEntity::getParentCode, req.getParentCode())
                .eq(AuthMenuEntity::getFrontEndUrl, req.getFrontEndUrl()));
        if (CollUtilX.isNotEmpty(authMenuList)) {
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "已存在!");
        }

        AuthMenuEntity authMenuEntity = AuthMenuEntity.builder()
                .menuName(req.getMenuName())
                .menuCode("999999")
                .menuLevel(3)
                .parentCode(req.getParentCode())
                .frontEndUrl(req.getFrontEndUrl())
                .type(1)
                .active(1)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date()).build();
        authMenuMapper.insert(authMenuEntity);

        if (authMenuEntity.getId() != null) {
            String menuCodeStr = "000000" + authMenuEntity.getId();
            authMenuMapper.update(null, new UpdateWrapper<AuthMenuEntity>().lambda()
                    .set(AuthMenuEntity::getMenuCode, "M" + menuCodeStr.substring(menuCodeStr.length() - 6))
                    .eq(AuthMenuEntity::getId, authMenuEntity.getId()));
        } else {
            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
        }
    }

    @Override
    public QueryLogSettingResp queryLogSetting() {
        String logSetting = stringRedisTemplate.opsForValue().get(RedisKey.LOG_SETTING);
        QueryLogSettingResp queryLogSettingResp = new QueryLogSettingResp();
        if (StrUtilX.isNotEmpty(logSetting)) {
            queryLogSettingResp.setLogSetting(Integer.valueOf(logSetting));
        } else {
            queryLogSettingResp.setLogSetting(0);
            stringRedisTemplate.opsForValue().set(RedisKey.LOG_SETTING, "0");
        }
        return queryLogSettingResp;

    }

    @Override
    public void editLogSetting(EditLogSettingReq req) {
        if (req.getLogSetting() != null) {
            stringRedisTemplate.opsForValue().set(RedisKey.LOG_SETTING, String.valueOf(req.getLogSetting()));
        }
    }

    @Override
    public void forceUpdateProductFilterStrategyCache() {
        //删除已缓存的
        log.info("redis--key--prefix:{}", RedisKey.PRODUCT_FILTER_STRATEGY.substring(0, RedisKey.PRODUCT_FILTER_STRATEGY.length() - 1));
        Set<String> keys = stringRedisTemplate.keys(RedisKey.PRODUCT_FILTER_STRATEGY.substring(0, RedisKey.PRODUCT_FILTER_STRATEGY.length() - 1) + "*");
        log.info("all--keys:{}", JSON.toJSONString(keys));
        stringRedisTemplate.delete(keys);
        int count = proProductFilterStrategyMapper.selectCount(new QueryWrapper<>());
        int numberOfTimes = (int) Math.ceil((double) count / fixedNumber);
        for (int i = 1; i <= numberOfTimes; i++) {
            Page<ProProductFilterStrategyEntity> proProductFilterStrategyPage = new Page<>(i, fixedNumber);
            IPage<ProProductFilterStrategyEntity> ipage = proProductFilterStrategyMapper.selectPage(proProductFilterStrategyPage, new QueryWrapper<>());
            //适用范围(0所有酒店 1指定酒店集团 2指定酒店)
            Map<Integer, Map<String, Map<String, List<ProProductFilterStrategyEntity>>>> resultMap = new HashMap<>();
            for (ProProductFilterStrategyEntity proProductFilterStrategyEntity : ipage.getRecords()) {
                String agentCode = proProductFilterStrategyEntity.getAgentCode();
                if (proProductFilterStrategyEntity.getScopeOfApplication() == 0) {
                    Map<String, Map<String, List<ProProductFilterStrategyEntity>>> scopeOfApplicationMap = resultMap.get(0);
                    if (scopeOfApplicationMap != null) {
                        Map<String, List<ProProductFilterStrategyEntity>> agentCodeMap = scopeOfApplicationMap.get(agentCode);
                        if (agentCodeMap != null && agentCodeMap.size() > 0) {
                            List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = agentCodeMap.get(proProductFilterStrategyEntity.getSupplierCode());
                            if (proProductFilterStrategyEntities != null) {
                                proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                                agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                                scopeOfApplicationMap.put(agentCode, agentCodeMap);
                                resultMap.put(0, scopeOfApplicationMap);
                            } else {
                                proProductFilterStrategyEntities = new ArrayList<>();
                                proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                                agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                                scopeOfApplicationMap.put(agentCode, agentCodeMap);
                                resultMap.put(0, scopeOfApplicationMap);
                            }
                        } else {
                            List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = new ArrayList<>();
                            agentCodeMap = new HashMap<>();
                            proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                            agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                            scopeOfApplicationMap.put(agentCode, agentCodeMap);
                            resultMap.put(0, scopeOfApplicationMap);
                        }
                    } else {
                        List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = new ArrayList<>();
                        scopeOfApplicationMap = new HashMap<>();
                        Map<String, List<ProProductFilterStrategyEntity>> agentCodeMap = new HashMap<>();
                        proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                        agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                        scopeOfApplicationMap.put(agentCode, agentCodeMap);
                        resultMap.put(0, scopeOfApplicationMap);
                    }
                } else if (proProductFilterStrategyEntity.getScopeOfApplication() == 1) {
                    Map<String, Map<String, List<ProProductFilterStrategyEntity>>> scopeOfApplicationMap = resultMap.get(1);
                    if (scopeOfApplicationMap != null) {
                        Map<String, List<ProProductFilterStrategyEntity>> agentCodeMap = scopeOfApplicationMap.get(agentCode);
                        if (agentCodeMap != null) {
                            List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = agentCodeMap.get(proProductFilterStrategyEntity.getSupplierCode());
                            if (proProductFilterStrategyEntities != null) {
                                proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                                agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                                scopeOfApplicationMap.put(agentCode, agentCodeMap);
                                resultMap.put(1, scopeOfApplicationMap);
                            } else {
                                proProductFilterStrategyEntities = new ArrayList<>();
                                proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                                agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                                scopeOfApplicationMap.put(agentCode, agentCodeMap);
                                resultMap.put(1, scopeOfApplicationMap);
                            }
                        } else {
                            List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = new ArrayList<>();
                            agentCodeMap = new HashMap<>();
                            proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                            agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                            scopeOfApplicationMap.put(agentCode, agentCodeMap);
                            resultMap.put(1, scopeOfApplicationMap);
                        }
                    } else {
                        List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = new ArrayList<>();
                        scopeOfApplicationMap = new HashMap<>();
                        Map<String, List<ProProductFilterStrategyEntity>> agentCodeMap = new HashMap<>();
                        proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                        agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                        scopeOfApplicationMap.put(agentCode, agentCodeMap);
                        resultMap.put(1, scopeOfApplicationMap);
                    }
                } else if (proProductFilterStrategyEntity.getScopeOfApplication() == 2) {
                    Map<String, Map<String, List<ProProductFilterStrategyEntity>>> scopeOfApplicationMap = resultMap.get(2);
                    if (scopeOfApplicationMap != null) {
                        Map<String, List<ProProductFilterStrategyEntity>> agentCodeMap = scopeOfApplicationMap.get(agentCode);
                        if (agentCodeMap != null) {
                            List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = agentCodeMap.get(proProductFilterStrategyEntity.getSupplierCode());
                            if (proProductFilterStrategyEntities != null) {
                                proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                                agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                                scopeOfApplicationMap.put(agentCode, agentCodeMap);
                                resultMap.put(2, scopeOfApplicationMap);
                            } else {
                                proProductFilterStrategyEntities = new ArrayList<>();
                                proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                                agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                                scopeOfApplicationMap.put(agentCode, agentCodeMap);
                                resultMap.put(2, scopeOfApplicationMap);
                            }
                        } else {
                            List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = new ArrayList<>();
                            agentCodeMap = new HashMap<>();
                            proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                            agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                            scopeOfApplicationMap.put(agentCode, agentCodeMap);
                            resultMap.put(2, scopeOfApplicationMap);
                        }
                    } else {
                        List<ProProductFilterStrategyEntity> proProductFilterStrategyEntities = new ArrayList<>();
                        scopeOfApplicationMap = new HashMap<>();
                        Map<String, List<ProProductFilterStrategyEntity>> agentCodeMap = new HashMap<>();
                        proProductFilterStrategyEntities.add(proProductFilterStrategyEntity);
                        agentCodeMap.put(proProductFilterStrategyEntity.getSupplierCode(), proProductFilterStrategyEntities);
                        scopeOfApplicationMap.put(agentCode, agentCodeMap);
                        resultMap.put(2, scopeOfApplicationMap);
                    }
                }
            }

            //存入redis
            for (Map.Entry<Integer, Map<String, Map<String, List<ProProductFilterStrategyEntity>>>> scopeOfApplicationMap : resultMap.entrySet()) {
                Integer scopeOfApplication = scopeOfApplicationMap.getKey();
                if (scopeOfApplication == 0) {
                    Map<String, Map<String, List<ProProductFilterStrategyEntity>>> agentCodeMap = scopeOfApplicationMap.getValue();
                    for (Map.Entry<String, Map<String, List<ProProductFilterStrategyEntity>>> agentCodeMapTmp : agentCodeMap.entrySet()) {
                        String agentCode = agentCodeMapTmp.getKey();
                        Map<String, List<ProProductFilterStrategyEntity>> supplierCodeMap = agentCodeMapTmp.getValue();
                        Map<String, String> map = new HashMap<>();
                        for (Map.Entry<String, List<ProProductFilterStrategyEntity>> supplierCodeMapTmp : supplierCodeMap.entrySet()) {
                            map.put(supplierCodeMapTmp.getKey(), JSON.toJSONString(supplierCodeMapTmp.getValue().get(0)));
                        }
                        stringRedisTemplate.opsForHash().putAll(StrUtilX.concat(RedisKey.PRODUCT_FILTER_STRATEGY, agentCode, ":0"), map);
                    }
                } else {
                    Map<String, Map<String, List<ProProductFilterStrategyEntity>>> agentCodeMap = scopeOfApplicationMap.getValue();
                    for (Map.Entry<String, Map<String, List<ProProductFilterStrategyEntity>>> agentCodeMapTmp : agentCodeMap.entrySet()) {
                        String agentCode = agentCodeMapTmp.getKey();
                        Map<String, List<ProProductFilterStrategyEntity>> supplierCodeMap = agentCodeMapTmp.getValue();
                        Map<String, Map<String, String>> targetCodeMap = new HashMap<>();
                        for (Map.Entry<String, List<ProProductFilterStrategyEntity>> supplierCodeMapTmp : supplierCodeMap.entrySet()) {
                            List<ProProductFilterStrategyEntity> proProductFilterStrategyEntityList = supplierCodeMapTmp.getValue();
                            for (ProProductFilterStrategyEntity proProductFilterStrategyEntity : proProductFilterStrategyEntityList) {
                                String targetCode = proProductFilterStrategyEntity.getTargetCode();
                                Map<String, String> map = targetCodeMap.get(targetCode);
                                if (map != null) {
                                    map.put(proProductFilterStrategyEntity.getSupplierCode(), JSON.toJSONString(proProductFilterStrategyEntity));
                                    targetCodeMap.put(targetCode, map);
                                } else {
                                    map = new HashMap<>();
                                    map.put(proProductFilterStrategyEntity.getSupplierCode(), JSON.toJSONString(proProductFilterStrategyEntity));
                                    targetCodeMap.put(targetCode, map);
                                }
                            }
                        }
                        for (Map.Entry<String, Map<String, String>> targetCodeMapTmp : targetCodeMap.entrySet()) {
                            stringRedisTemplate.opsForHash().putAll(StrUtilX.concat(RedisKey.PRODUCT_FILTER_STRATEGY, agentCode, ":", scopeOfApplication.toString(), ":", targetCodeMapTmp.getKey()), targetCodeMapTmp.getValue());
                        }
                    }

                }
            }
        }
    }

}
