package com.tiangong;

import com.tiangong.cloud.common.enums.result.AdminResultEnum;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.domain.AuthUser;
import com.tiangong.util.IpUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.regex.Pattern;

@Slf4j
public class ValidatedFilter {

    /**
     * 口令长度不小于8位 字符，应为大写字母、小写字 母、数字、特殊字符中三种或 三种以上的组合；
     */
    public static final String PARTTEN = "^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,20}$";


    /**
     * 登录失败key
     */
    public static final String LOGIN_FAIL = "AUTH:LOGIN_FAIL:";


    /**
     * 验证码校验key
     */
    public static final String VERIFICATION_CODE = "AUTH:VERIFICATION_CODE:";


    /**
     * 同一用户不同用户登录记录key
     */
    public static final String MULTI_USER_LOGIN = "AUTH:MULTI_USER_LOGIN:";

    /**
     * 同一用户不同用户登录记录key
     */
    public static final String MULTI_USER_LOGIN_ACCOUNT = "AUTH:MULTI_USER_LOGIN_ACCOUNT:";

    /**
     * 最大登录失败数
     */
    public static final String LOGIN_MAX_FAIL_COUNT = "AUTH:LOGIN_MAX_FAIL_COUNT";

    /**
     * 最大同时在线员工数
     */
    public static final String USER_MAX_ONLINE_COUNT = "AUTH:USER_MAX_ONLINE_COUNT";

    /**
     * 锁定时间限制（单位：分钟）
     */
    public static final String FAIL_LOCK_TIME = "AUTH:FAIL_LOCK_TIME";

    /**
     * 锁定的用户
     */
    public static final String LOCK_STATUS_USER = "AUTH:LOCK_STATUS_USER:";


    public static void filteringMatching(AuthUser authUser) {
        //双因子校验
        doubleFactorValidated(authUser);
    }

    /**
     * 验证码校验
     */
    public static boolean verificationCodeValidated(String userAccount, String verificationCode) {
        String cacheVCode = RedisTemplateX.get(StrUtilX.concat(VERIFICATION_CODE, userAccount));
        Assert.isTrue(Objects.equals(cacheVCode, verificationCode), AdminResultEnum.E_90041.code);
        return true;
    }


    /**
     * 密码规则校验
     */
    public static boolean passWordRuleValidated(String passWord, String userAccount) {
        Assert.isTrue(Pattern.matches(PARTTEN, passWord), AdminResultEnum.E_90040.code);
        Assert.isTrue(!passWord.contains(userAccount), AdminResultEnum.E_90040.code);
        return true;
    }


    /**
     * 验证码加双因子校验
     */
    public static boolean doubleFactorValidated(AuthUser authUser) {
        return verificationCodeValidated(authUser.getUserAccount(), authUser.getVerificationCode()) && passWordRuleValidated(authUser.getUserPassword(), authUser.getUserAccount());
    }

    /**
     * 登录失败次数
     */
    public static boolean loginFailValidated(String username) {
        String key = LOCK_STATUS_USER.concat(username);
        return !RedisTemplateX.hasKey(key);
    }

    /**
     * 连续登录失败记录
     */
    public static boolean loginFailRecord(String username) {
        String key = LOGIN_FAIL.concat(username);
        // 最大登录失败数
        int maxCount = 10;
        if (RedisTemplateX.hasKey(LOGIN_MAX_FAIL_COUNT)) {
            maxCount = Integer.parseInt(RedisTemplateX.get(LOGIN_MAX_FAIL_COUNT));
        }
        if (RedisTemplateX.hasKey(key)) {
            int count = Integer.parseInt(RedisTemplateX.get(key)) + 1;
            if (count < maxCount) {
                RedisTemplateX.incr(key, 1);
            } else {
                RedisTemplateX.delete(key);
                return false;
            }
        } else {
            RedisTemplateX.setAndExpire(key, "1", 60 * 60 * 24);
        }

        return true;
    }

    /**
     * 连续登录失败最大数
     */
    public static int maxFileCount() {
        if (RedisTemplateX.hasKey(LOGIN_MAX_FAIL_COUNT)) {
            return Integer.parseInt(RedisTemplateX.get(LOGIN_MAX_FAIL_COUNT));
        }
        return 10;
    }

    /**
     * 同一客户端禁止重复登录
     */
    public static boolean replicatedLogin(String username) {
        String userName = RedisTemplateX.get(StrUtilX.concat(MULTI_USER_LOGIN_ACCOUNT, username));
        Assert.isTrue(Objects.isNull(userName), AdminResultEnum.E_90043.code);
        return true;
    }


    /**
     * 统一客户端, 禁止登录多个用户账号
     */
    public static boolean multiUserLogin(HttpServletRequest request, String username) {
        String userName = RedisTemplateX.get(StrUtilX.concat(MULTI_USER_LOGIN, IpUtil.getIpAddress(request)));
        Assert.isTrue((Objects.isNull(userName) || Objects.equals(userName, username)), AdminResultEnum.E_90044.code);
        return true;
    }

}
