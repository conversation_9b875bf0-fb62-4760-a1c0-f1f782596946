package com.tiangong.fuzzyquery.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.fuzzyquery.dto.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 11:35
 * @Description:模糊查询service
 */
public interface FuzzyQueryService {

    /**
     * 模糊查询供应商
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含供应商查询条件
     * @return 返回分页的模糊供应商DTO数据
     */
    PaginationSupportDTO<FuzzySupplierDTO> querySupplier(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 模糊查询分销商
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含分销商查询条件
     * @return 返回分页的模糊分销商DTO数据
     */
    PaginationSupportDTO<FuzzyAgentDTO> queryAgent(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 查询渠道配置
     *
     * @param companyCode 公司编码，用于查询指定公司的渠道配置
     * @return 返回模糊渠道配置DTO列表
     */
    List<FuzzyChannelConfigDTO> queryChannelConfig(String companyCode);

    /**
     * 查询客户渠道编码
     *
     * @param requestMap 请求参数映射，包含客户渠道查询条件
     * @return 返回模糊渠道配置DTO列表
     */
    List<FuzzyChannelConfigDTO> queryAgentChannelCode(Map<String, String> requestMap);

    /**
     * 查询分销商列表
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含分销商查询条件
     * @return 返回模糊分销商DTO列表
     */
    List<FuzzyAgentDTO> queryAgentList(FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 查询供应商列表
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含供应商查询条件
     * @return 返回模糊供应商DTO列表
     */
    List<FuzzySupplierDTO> querySupplierList(FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 根据客户编码和供应商名称(模糊匹配)获取此客户下的所有有效供应商信息
     *
     * @param fuzzyQueryDTO 模糊查询DTO，包含客户编码和供应商名称查询条件
     * @return 返回分页的可用模糊供应商DTO数据
     */
    PaginationSupportDTO<FuzzySupplierDTO> queryAvailableSupplierByAgentCodeAndSupplierName(FuzzyQueryDTO fuzzyQueryDTO);
}
