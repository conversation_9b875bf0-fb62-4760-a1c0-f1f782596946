package com.tiangong.convert.agent;

import com.tiangong.organization.domain.AgentConfirmationLetterConfigPO;
import com.tiangong.organization.domain.req.AgentConfirmationLetterConfigAditReq;
import com.tiangong.organization.remote.dto.AgentConfirmationLetterConfigResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @program: glink_tiangong
 * @ClassName AgentConfirmationLetterConfigConvert
 * @description:
 * @author: 湫
 * @create: 2025/04/15/ 14:44
 * @Version 1.0
 **/

@Mapper
public interface AgentConfirmationLetterConfigConvert {

    AgentConfirmationLetterConfigConvert INSTANCE = Mappers.getMapper(AgentConfirmationLetterConfigConvert.class);

    /**
     * 将客户确认函配置请求对象转换为持久化对象
     * 用于新增或修改客户确认函配置时的数据转换
     * @param bean 客户确认函配置请求对象，包含配置的各项参数
     * @return 客户确认函配置持久化对象，用于数据库操作
     */
    AgentConfirmationLetterConfigPO convert(AgentConfirmationLetterConfigAditReq bean);

    /**
     * 将客户确认函配置持久化对象转换为响应对象
     * 用于查询客户确认函配置时的数据转换
     * @param bean 客户确认函配置持久化对象，从数据库查询得到
     * @return 客户确认函配置响应对象，返回给前端或其他服务
     */
    AgentConfirmationLetterConfigResp convert(AgentConfirmationLetterConfigPO bean);
}
