package com.tiangong.convert;

import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.common.domain.entity.AuditLogEntity;
import com.tiangong.common.domain.req.AuditLogReq;
import com.tiangong.common.domain.resp.AuditLogResp;
import com.tiangong.dto.common.AuditLogAddReq;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.dto.hotel.FacilityDto;
import com.tiangong.dto.hotel.HotCityClickDTO;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.file.domain.FileLogPO;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import com.tiangong.finance.OrgDTO;
import com.tiangong.fuzzyquery.dto.FuzzyCityDTO;
import com.tiangong.hotel.domain.*;
import com.tiangong.hotel.domain.req.ExportHotelRecommendReq;
import com.tiangong.hotel.domain.req.ImportHotelRecommendReq;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceConfigVo;
import com.tiangong.hotel.domain.vo.ImportHotelRecommendReqDTO;
import com.tiangong.hotel.dto.BaseinfoAreadataDTO;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;
import com.tiangong.hotel.req.*;
import com.tiangong.operatelog.domain.OperateLogEntity;
import com.tiangong.operatelog.domain.OperateLogResp;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.organization.domain.*;
import com.tiangong.organization.domain.req.ImportSupplierLabelConfigExportReq;
import com.tiangong.organization.domain.req.ImportSupplierLabelConfigReq;
import com.tiangong.organization.domain.req.SupplierLabelConfigReq;
import com.tiangong.organization.domain.req.SupplierUpdateReq;
import com.tiangong.organization.remote.dto.AddSupplierReq;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.user.domain.ExamineUserDTO;
import com.tiangong.user.domain.MenuPO;
import com.tiangong.user.domain.RolePO;
import com.tiangong.user.domain.req.AddOrUpdateRoleReq;
import com.tiangong.user.domain.req.DelRoleReq;
import com.tiangong.user.dto.FirstMenuDTO;
import com.tiangong.user.dto.MenuDTO;
import com.tiangong.user.dto.MenuLevelDTO;
import com.tiangong.vip.domain.dto.AgentVipCacheDTO;
import com.tiangong.vip.domain.po.AgentVipClientPO;
import com.tiangong.vip.dto.AgentVipClientDTO;
import com.tiangong.vip.domain.vo.AgentVipClientVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @program: glink_tiangong
 * @ClassName CommonDtoConvert
 * @description:
 * @author: 湫
 * @create: 2025/01/16/ 12:27
 * @Version 1.0
 **/
@Mapper
public interface CommonDtoConvert {
    CommonDtoConvert INSTANCE = Mappers.getMapper(CommonDtoConvert.class);


    /**
     * 将酒店平均价格请求列表转换为酒店平均价格实体列表
     *
     * @param reqList 酒店平均价格请求列表
     * @return 返回转换后的酒店平均价格实体列表
     */
    List<HotelAvgPriceEntity> HotelAvgPriceListConvert(List<HotelAvgPriceReq> reqList);

    /**
     * 将区域主要推广价格请求转换为区域主要推广价格实体
     *
     * @param req 区域主要推广价格请求对象
     * @return 返回转换后的区域主要推广价格实体
     */
    RegionMainlyPopularizePriceEntity RegionMainlyPopularizePriceConvert(RegionMainlyPopularizePriceReq req);

    /**
     * 将城市平均价格请求列表转换为城市平均价格实体列表
     *
     * @param reqList 城市平均价格请求列表
     * @return 返回转换后的城市平均价格实体列表
     */
    List<CityAvgPriceEntity> CityAvgPriceConvert(List<CityAvgPriceReq> reqList);

    /**
     * 将菜单DTO转换为一级菜单DTO
     *
     * @param menuDTO 菜单数据传输对象
     * @return 返回转换后的一级菜单DTO
     */
    FirstMenuDTO FirstMenuConvert(MenuDTO menuDTO);

    /**
     * 将文件响应对象转换为文件日志持久化对象
     *
     * @param resp 文件响应对象
     * @return 返回转换后的文件日志持久化对象
     */
    FileLogPO FileConvert(FileResp resp);

    /**
     * 将文件日志持久化对象转换为文件响应对象
     *
     * @param fileLogPO 文件日志持久化对象
     * @return 返回转换后的文件响应对象
     */
    FileResp fileRespConvert(FileLogPO fileLogPO);

    /**
     * 将文件请求对象转换为文件日志持久化对象
     *
     * @param req 文件请求对象
     * @return 返回转换后的文件日志持久化对象
     */
    FileLogPO fileLogPOConvert(FileReq req);

    /**
     * 将操作日志请求DTO转换为操作日志实体
     *
     * @param newDO 操作日志请求DTO对象
     * @return 返回转换后的操作日志实体
     */
    OperateLogEntity OperateLogConvert(OperateLogReqDTO newDO);

    /**
     * 将用户持久化对象转换为审核用户DTO
     *
     * @param userPO 用户持久化对象
     * @return 返回转换后的审核用户DTO
     */
    ExamineUserDTO ExamineUserConvert(UserPO userPO);

    /**
     * 将菜单DTO转换为菜单级别DTO
     *
     * @param menuDTO 菜单数据传输对象
     * @return 返回转换后的菜单级别DTO
     */
    MenuLevelDTO MenuLevelConvert(MenuDTO menuDTO);

    /**
     * 将用户持久化对象转换为用户申请持久化对象
     *
     * @param existPO 现有用户持久化对象
     * @return 返回转换后的用户申请持久化对象
     */
    UserApplyPO UserApplyPOConvert(UserPO existPO);

    /**
     * 将组织持久化对象转换为组织DTO
     *
     * @param orgPO 组织持久化对象
     * @return 返回转换后的组织DTO
     */
    OrgDTO orgDTOConvert(OrgPO orgPO);

    /**
     * 将设施DTO转换为基础信息设施对象
     *
     * @param baseinfoFacility 设施DTO对象
     * @return 返回转换后的基础信息设施对象
     */
    BaseinfoFacility facilityConvert(FacilityDto baseinfoFacility);

    /**
     * 将基础信息设施对象转换为设施DTO
     *
     * @param facility 基础信息设施对象
     * @return 返回转换后的设施DTO
     */
    FacilityDto facilityDtoConvert(BaseinfoFacility facility);

    /**
     * 将新增或更新角色请求转换为角色持久化对象
     *
     * @param req 新增或更新角色请求对象
     * @return 返回转换后的角色持久化对象
     */
    RolePO RolePOConvert(AddOrUpdateRoleReq req);

    /**
     * 将删除角色请求转换为角色持久化对象
     *
     * @param req 删除角色请求对象
     * @return 返回转换后的角色持久化对象
     */
    RolePO RoleConvert(DelRoleReq req);

    /**
     * 将菜单持久化对象转换为菜单响应DTO
     *
     * @param menu2 菜单持久化对象
     * @return 返回转换后的菜单响应DTO
     */
    com.tiangong.user.domain.resp.MenuDTO MenuDTOConvert(MenuPO menu2);

    /**
     * 将菜单响应DTO转换为菜单响应DTO（复制转换）
     *
     * @param menu2 菜单响应DTO对象
     * @return 返回转换后的菜单响应DTO
     */
    com.tiangong.user.domain.resp.MenuDTO MenuConvert(com.tiangong.user.domain.resp.MenuDTO menu2);

    /**
     * 将基础信息区域数据实体列表转换为基础信息区域数据DTO列表
     *
     * @param list 基础信息区域数据实体列表
     * @return 返回转换后的基础信息区域数据DTO列表
     */
    List<BaseinfoAreadataDTO> baseinfoAreadataDTOConvert(List<BaseinfoAreadataEntity> list);

    /**
     * 将基础信息区域实体列表转换为基础信息区域DTO列表
     *
     * @param list 基础信息区域实体列表
     * @return 返回转换后的基础信息区域DTO列表
     */
    List<BaseinfoRegionDTO> baseinfoRegionDTOConvert(List<BaseinfoRegionEntity> list);

    /**
     * 将热门城市点击DTO转换为热门城市点击实体
     *
     * @param req 热门城市点击DTO对象
     * @return 返回转换后的热门城市点击实体
     */
    HotCityClickEntity hotCityClickEntityConvert(HotCityClickDTO req);

    /**
     * 将新增供应商请求转换为组织持久化对象
     *
     * @param req 新增供应商请求对象
     * @return 返回转换后的组织持久化对象
     */
    OrgPO orgPOConvert(AddSupplierReq req);

    /**
     * 将新增供应商请求转换为供应商公司持久化对象
     *
     * @param req 新增供应商请求对象
     * @return 返回转换后的供应商公司持久化对象
     */
    SupplierCompanyPO supplierCompanyPOConvert(AddSupplierReq req);

    /**
     * 将新增供应商请求转换为用户持久化对象
     *
     * @param req 新增供应商请求对象
     * @return 返回转换后的用户持久化对象
     */
    UserPO adminPOConvert(AddSupplierReq req);

    /**
     * 将组织持久化对象转换为组织DTO（通用版本）
     *
     * @param orgPO 组织持久化对象
     * @return 返回转换后的组织DTO
     */
    com.tiangong.organization.remote.dto.OrgDTO orgDTOComConvert(OrgPO orgPO);

    /**
     * 将供应商更新请求转换为新增供应商请求
     *
     * @param req 供应商更新请求对象
     * @return 返回转换后的新增供应商请求
     */
    AddSupplierReq supplierConvert(SupplierUpdateReq req);

    /**
     * 将供应商标签配置请求转换为供应商标签配置持久化对象
     *
     * @param req 供应商标签配置请求对象
     * @return 返回转换后的供应商标签配置持久化对象
     */
    SupplierLabelConfigPO supplierLabelConfigPOConvert(SupplierLabelConfigReq req);

    /**
     * 将导入供应商标签配置请求转换为导入供应商标签配置导出请求
     *
     * @param importReq 导入供应商标签配置请求对象
     * @return 返回转换后的导入供应商标签配置导出请求
     */
    ImportSupplierLabelConfigExportReq importSupplierLabelConfigExportReqConvert(ImportSupplierLabelConfigReq importReq);

    /**
     * 将酒店可用性持久化对象转换为酒店可用性DTO
     *
     * @param hotelAvailablePO 酒店可用性持久化对象
     * @return 返回转换后的酒店可用性DTO
     */
    HotelAvailableDTO hotelAvailableDTOConvert(HotelAvailablePO hotelAvailablePO);

    /**
     * 将新增酒店可用性请求转换为酒店可用性持久化对象
     *
     * @param req 新增酒店可用性请求对象
     * @return 返回转换后的酒店可用性持久化对象
     */
    HotelAvailablePO hotelAvailablePOConvert(AddHotelAvailableReq req);

    /**
     * 将酒店标签配置请求转换为酒店标签配置实体
     *
     * @param req 酒店标签配置请求对象
     * @return 返回转换后的酒店标签配置实体
     */
    HotelLabelConfigEntity hotelLabelConfigEntityConvert(HotelLabelConfigReq req);

    /**
     * 将提示持久化对象转换为提示响应对象
     *
     * @param tipsPO 提示持久化对象
     * @return 返回转换后的提示响应对象
     */
    com.tiangong.dto.common.TipsResp respConvert(TipsPO tipsPO);

    /**
     * 将提示持久化对象列表转换为提示响应对象列表
     *
     * @param poList 提示持久化对象列表
     * @return 返回转换后的提示响应对象列表
     */
    List<com.tiangong.dto.common.TipsResp> tipsRespConvert(List<TipsPO> poList);

    /**
     * 将菜单DTO转换为一级菜单DTO（重复方法）
     *
     * @param menuDTO 菜单数据传输对象
     * @return 返回转换后的一级菜单DTO
     */
    FirstMenuDTO firstMenuDTOConvert(MenuDTO menuDTO);

    /**
     * 将菜单DTO转换为菜单级别DTO（重复方法）
     *
     * @param menuDTO 菜单数据传输对象
     * @return 返回转换后的菜单级别DTO
     */
    MenuLevelDTO menuLevelDTOConvert(MenuDTO menuDTO);

    /**
     * 将酒店推荐请求转换为酒店推荐实体
     *
     * @param req 酒店推荐请求对象
     * @return 返回转换后的酒店推荐实体
     */
    HotelRecommendEntity hgotelRecommendEntityConvert(HotelRecommendReq req);

    /**
     * 将基础信息区域数据实体转换为基础信息热门城市实体
     *
     * @param bean 基础信息区域数据实体
     * @return 返回转换后的基础信息热门城市实体
     */
    BaseinfoHotCityEntity baseinfoHotCityEntityConvert(BaseinfoAreadataEntity bean);

    /**
     * 将审计日志请求转换为审计日志实体
     *
     * @param req 审计日志请求对象
     * @return 返回转换后的审计日志实体
     */
    AuditLogEntity convertAuditLogEntity(AuditLogReq req);

    /**
     * 将审计日志新增请求转换为审计日志实体
     *
     * @param req 审计日志新增请求对象
     * @return 返回转换后的审计日志实体
     */
    AuditLogEntity convertAuditLogEntity(AuditLogAddReq req);

    /**
     * 将审计日志实体转换为审计日志响应对象
     *
     * @param auditLogEntity 审计日志实体
     * @return 返回转换后的审计日志响应对象
     */
    AuditLogResp convertAuditLogResp(AuditLogEntity auditLogEntity);

    /**
     * 将审计日志实体列表转换为审计日志响应对象列表
     *
     * @param auditLogEntityList 审计日志实体列表
     * @return 返回转换后的审计日志响应对象列表
     */
    List<AuditLogResp> convertAuditLogResp(List<AuditLogEntity> auditLogEntityList);

    /**
     * 将基础信息区域数据实体列表转换为模糊城市DTO列表
     *
     * @param baseinfoAreadataEntityList 基础信息区域数据实体列表
     * @return 返回转换后的模糊城市DTO列表
     */
    List<FuzzyCityDTO> convertFuzzyCityDTO(List<BaseinfoAreadataEntity> baseinfoAreadataEntityList);

    /**
     * 将操作日志实体转换为操作日志响应对象
     *
     * @param operateLogEntity 操作日志实体
     * @return 返回转换后的操作日志响应对象
     */
    OperateLogResp convertOperateLogResp(OperateLogEntity operateLogEntity);

    /**
     * 将操作日志请求DTO转换为操作日志响应对象
     *
     * @param operateLogReqDTO 操作日志请求DTO
     * @return 返回转换后的操作日志响应对象
     */
    OperateLogResp convertOperateLogResp(OperateLogReqDTO operateLogReqDTO);

    /**
     * 将自动更新最低价格城市VO列表转换为自动更新最低价格城市实体列表
     *
     * @param lowestPriceCity 自动更新最低价格城市VO列表
     * @return 返回转换后的自动更新最低价格城市实体列表
     */
    List<AutoUpdateLowestPriceCityEntity> lowestPriceCityEntityConvert(List<AutoUpdateLowestPriceCityVo> lowestPriceCity);

    /**
     * 将自动更新最低价格配置VO转换为自动更新最低价格配置实体
     *
     * @param lowestPriceTask 自动更新最低价格配置VO
     * @return 返回转换后的自动更新最低价格配置实体
     */
    AutoUpdateLowestPriceConfigEntity lowestPriceTaskConvert(AutoUpdateLowestPriceConfigVo lowestPriceTask);

    /**
     * 将导入酒店推荐请求DTO转换为导出酒店推荐请求
     *
     * @param req 导入酒店推荐请求DTO
     * @return 返回转换后的导出酒店推荐请求
     */
    ExportHotelRecommendReq ExportHotelRecommendReqConvert(ImportHotelRecommendReqDTO req);

    /**
     * 将导入酒店推荐请求转换为导入酒店推荐请求DTO
     *
     * @param req 导入酒店推荐请求
     * @return 返回转换后的导入酒店推荐请求DTO
     */
    ImportHotelRecommendReqDTO ImportHotelRecommendReqDTOConvert(ImportHotelRecommendReq req);

    /**
     * 将代理账户配置转换为代理配置DTO
     *
     * @param agentAccountConfig 代理账户配置对象
     * @return 返回转换后的代理配置DTO
     */
    AgentConfigDTO convert(AgentAccountConfig agentAccountConfig);

    /**
     * 将代理VIP客户端VO转换为代理VIP客户端DTO
     *
     * @param vo 代理VIP客户端VO对象
     * @return 返回转换后的代理VIP客户端DTO
     */
    AgentVipClientDTO convertVOToDTO(AgentVipClientVO vo);

    /**
     * 将代理VIP客户端持久化对象转换为代理VIP客户端VO
     *
     * @param vo 代理VIP客户端持久化对象
     * @return 返回转换后的代理VIP客户端VO
     */
    AgentVipClientVO convertPOToVO(AgentVipClientPO vo);

    /**
     * 将代理VIP客户端持久化对象转换为代理VIP缓存DTO
     *
     * @param po 代理VIP客户端持久化对象
     * @return 返回转换后的代理VIP缓存DTO
     */
    AgentVipCacheDTO convert(AgentVipClientPO po);

    /**
     * 将代理VIP客户端DTO转换为代理VIP缓存DTO
     *
     * @param dto 代理VIP客户端DTO对象
     * @return 返回转换后的代理VIP缓存DTO
     */
    AgentVipCacheDTO convert(AgentVipClientDTO dto);
}
