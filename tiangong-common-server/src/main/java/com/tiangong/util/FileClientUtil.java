package com.tiangong.util;


import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.tiangong.config.AlyOssConfigProperties;
import com.tiangong.enums.FileEnum;
import com.tiangong.file.resp.FileResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;


/**
 * <AUTHOR>
 * @date 2021/12/22 12:01
 * <p>
 * 阿里客户端
 */
@Slf4j
@Component
public class FileClientUtil {

    @Autowired
    private AlyOssConfigProperties alyOssConfigProperties;

    /**
     * 上传文件
     */
    public FileResp upload(byte[] bytes, String fileName) {

        // 上传文件
        FileResp fileResp = doUpload(bytes, fileName);

        // 返回
        fileResp.setOssType(FileEnum.ali.getKey());
        return fileResp;
    }

    /**
     * 下载文件
     */
    private FileResp doUpload(byte[] bytes, String fileName) {
        FileResp fileResp = FileUtil.init(fileName, "");
        long fileSize = bytes.length;
        fileResp.setFileSize(fileSize);

        OSS ossClient = null;
        ByteArrayInputStream inputStream = null;
        try {
            inputStream = new ByteArrayInputStream(bytes);

            String fileUrl = fileResp.getFileUrl();
            String key = fileUrl.substring(1);
            // 创建OSSClient实例。
            ossClient = new OSSClientBuilder().build(alyOssConfigProperties.getEndpoint(), alyOssConfigProperties.getAccessKey(), alyOssConfigProperties.getAccessSecret());
            ossClient.putObject(alyOssConfigProperties.getBucketName(), key, inputStream);
            fileResp.setFileUrl("https://" + alyOssConfigProperties.getBucketName() + "." + alyOssConfigProperties.getEndpoint() + "/" + key);
        } catch (Exception e1) {
            log.error("下载文件异常", e1);
        } finally {
            // 关闭OSSClient。
            if (ossClient != null) {
                ossClient.shutdown();
            }
            // 关闭流
            FileUtil.close(inputStream);
        }
        return fileResp;
    }

    /**
     * 删除文件
     */
    public void delFile(String fileUrl) {
        String key = fileUrl.substring(1);
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(alyOssConfigProperties.getEndpoint(), alyOssConfigProperties.getAccessKey(), alyOssConfigProperties.getAccessSecret());
        ossClient.deleteObject(alyOssConfigProperties.getBucketName(), key);
        // 关闭OSSClient。
        ossClient.shutdown();
    }
}
