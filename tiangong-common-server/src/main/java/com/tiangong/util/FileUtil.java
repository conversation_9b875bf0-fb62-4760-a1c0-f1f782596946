package com.tiangong.util;


import com.tiangong.file.resp.FileImage;
import com.tiangong.file.resp.FileItem;
import com.tiangong.file.resp.FileResp;
import com.tiangong.file.resp.ThumbnailImage;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
public class FileUtil {


    /**
     * 定义GB的计算常量，1024 * 1024 * 1024
     */
    private static final int GB = 1073741824;
    /**
     * 定义MB的计算常量，1024 * 1024
     */
    private static final int MB = 1048576;
    /**
     * 定义KB的计算常量
     */
    private static final int KB = 1024;
    /**
     * 缩略图大小范围
     */
    public static final int KB_50 = KB * 50;
    /**
     * 格式化小数
     */
    private static final DecimalFormat DF = new DecimalFormat("0.00");
    /**
     * 缓冲区大小
     */
    private static final int BUFFER_SIZE_4KB = 4096;

    /**
     * 获取文件后缀名，不带 .
     */
    public static String getFileSuffix(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }

    /**
     * 计算文件大小
     */
    public static String formetFileSize(long fileS) {//转换文件大小
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";
        if (fileS < KB) {
            fileSizeString = df.format((double) fileS) + "B";
        } else if (fileS < MB) {
            fileSizeString = df.format((double) fileS / KB) + "K";
        } else if (fileS < GB) {
            fileSizeString = df.format((double) fileS / MB) + "M";
        } else {
            fileSizeString = df.format((double) fileS / GB) + "G";
        }
        return fileSizeString;
    }

    /**
     * 根据后缀判断文件类型，1：图片，2：文档，3：音乐，4：视频，5：其他,0:下载包
     */
    public static String getFileType(String fileSuffix) {
        String image = "bmp dib pcp dif wmf gif jpg tif eps psd cdr iff tga pcd mpt png jpeg";
        String documents = "txt doc pdf ppt pps xlsx xls docx";
        String music = "mp3 wav wma mpa ram ra aac aif m4a";
        String video = "avi mpg mpe mpeg asf wmv mov qt rm mp4 flv m4v webm ogv ogg";
        if (image.contains(fileSuffix)) {
            return "image";
        } else if (documents.contains(fileSuffix)) {
            return "documents";
        } else if (music.contains(fileSuffix)) {
            return "music";
        } else if (video.contains(fileSuffix)) {
            return "video";
        } else {
            return "other";
        }
    }

    /**
     * 根据文件类型获取类型的前置路劲名，有配置文件就用配置文件的路劲
     * 1：图片，2：文档，3：音乐，4：视频，5：其他
     * image  documents  music  video  other
     */
    public static String getFilePathType(String fileType, String path) {
        String pathType = getFileType(fileType);
        switch (pathType) {
            case "image":
                return path + "/image";
            case "documents":
                return path + "/documents";
            case "music":
                return path + "/music";
            case "video":
                return path + "/video";
            default:
                return path + "/other";
        }

    }

    /**
     * 获取图片缩略等比的宽高
     *
     * @return
     */
    public static ThumbnailImage getThumbnailWidthAndHeight(FileResp fileResp) {
        ThumbnailImage thumbnailImage = new ThumbnailImage();
        if (fileResp.getFileSize() > FileUtil.KB_50) {
            //  文件大于50kb，增加缩略图
            int l = (int) (fileResp.getFileSize() / FileUtil.KB_50);
            int width = fileResp.getFileWidth() / l;
            int height = fileResp.getFileHeight() / l;
            thumbnailImage.setWidth(width);
            thumbnailImage.setHeight(height);
        } else {
            thumbnailImage.setWidth(fileResp.getFileWidth());
            thumbnailImage.setHeight(fileResp.getFileHeight());
        }
        return thumbnailImage;
    }

    /**
     * 删除文件
     */
    public static boolean del(String fileName) {
        File file = new File(fileName);
        return FileUtil.del(file);
    }

    /**
     * 删除文件
     */
    public static boolean del(File file) {
        boolean flag = false;
        if (file.exists() && file.isFile()) {
            flag = file.delete();
            return flag;
        }
        return flag;
    }


    /**
     * 关闭流
     */
    public static void close(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException var2) {
            }
        }
    }

    /**
     * 输入流写进输出流
     */
    private static int doCopy(InputStream in, OutputStream out) throws IOException {
        int byteCount = 0;
        int bytesRead;
        for (byte[] buffer = new byte[BUFFER_SIZE_4KB]; (bytesRead = in.read(buffer)) != -1; byteCount += bytesRead) {
            out.write(buffer, 0, bytesRead);
        }
        out.flush();
        return byteCount;
    }

    /**
     * 本地上传
     */
    public static FileResp uploadLocal(byte[] bytes, String fileName, String filePath) {
        FileResp fileResp = init(fileName, filePath);
        String fileUrl = fileResp.getFileUrl();
        long fileSize = bytes.length;
        fileResp.setFileSize(fileSize);

        // 本地上传
        FileUtil.uploadLocal(bytes, fileUrl);

        FileImage fileImage = FileUtil.getIamgeWidthAndHeight(bytes);

        if (fileImage != null) {
            // 是图片
            fileResp.setFileWidth(fileImage.getWidth());
            fileResp.setFileHeight(fileImage.getHeight());

            // 生成缩略图
            ThumbnailImage thumbnailImage = FileUtil.getThumbnailWidthAndHeight(fileResp);
            String thumbnailUrl = fileUrl.replace(fileResp.getAliasFileName(), "thumbnail_" + fileResp.getAliasFileName());
            ThumbnailatorUtils.generateFixedSizeImage(bytes, thumbnailUrl, thumbnailImage.getWidth(), thumbnailImage.getHeight());
            fileResp.setThumbnailUrl(thumbnailUrl);
        }

        return fileResp;
    }

    public static FileImage getIamgeWidthAndHeight(byte[] bytes) {
        FileImage fileImage = null;
        ByteArrayInputStream imageInputStream = null;
        try {
            // 判断是不是图片
            imageInputStream = new ByteArrayInputStream(bytes);
            BufferedImage image = ImageIO.read(imageInputStream);
            int width = image.getWidth();
            int height = image.getHeight();

            fileImage = new FileImage();
            fileImage.setWidth(width);
            fileImage.setHeight(height);

        } catch (Exception e) {
            log.error("本地上传异常", e);
        } finally {
            close(imageInputStream);
        }
        return fileImage;
    }

    /**
     * 本地上传
     */
    public static void uploadLocal(byte[] bytes, String fileUrl) {
        File outFile = new File(fileUrl);
        if (!outFile.getParentFile().exists()) {
            // 父级文件不存在就创建
            outFile.getParentFile().mkdirs();
        }
        FileOutputStream out = null;
        try {
            // TODO:这里如果是大文件会有问题，现在没想好处理方法
            out = new FileOutputStream(outFile);
            out.write(bytes);
        } catch (IOException e) {
            log.error("本地上传异常", e);
        } finally {
            close(out);
        }
    }


    /**
     * 初始化文件属性
     */
    public static FileResp init(String fileName, String filePath) {
        String nowStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddhhmmssS")) + (int) (Math.random() * 9000 + 1000);
        String suffix = getFileSuffix(fileName);
        String fileType = getFileType(suffix);
        FileResp fileResp = new FileResp();
        String aliasFileName = nowStr + "." + suffix;
        fileResp.setAliasFileName(aliasFileName);
        String fileUrl = filePath + "/" + fileType + "/" + aliasFileName;
        fileResp.setFileName(fileName);
        fileResp.setFileFormat(suffix);
        fileResp.setFileUrl(fileUrl);
        return fileResp;
    }


    /**
     * 本地文件下载
     */
    public static FileItem downloadLocal(String url) {
        FileItem fileItem = new FileItem();

        // linux系统，拼接域名，https://www.mgs.com + /image/touxiang_202203290129427.jpg

        String fullfileName = "";
        String fileName = "";
        String fileType = "";

        String[] split = url.split("/");
        if (split.length > 0) {
            fullfileName = split[split.length - 1];
            String[] split1 = fullfileName.split("\\.");
            if (split1.length > 0) {
                fileName = split1[0];
                fileType = split1[1];

            }
            fileItem.setFileName(fileName);
            fileItem.setFileFormat(fileType);
            InputStream in = null;
            ByteArrayOutputStream out = null;
            try {
                // 根据url请求文件流
                in = new FileInputStream(new File(url));
                out = new ByteArrayOutputStream(BUFFER_SIZE_4KB);
                int byteCount = 0;
                int bytesRead;
                for (byte[] buffer = new byte[BUFFER_SIZE_4KB]; (bytesRead = in.read(buffer)) != -1; byteCount += bytesRead) {
                    out.write(buffer, 0, bytesRead);
                }
                out.flush();
                fileItem.setBytes(out.toByteArray());
                fileItem.setSize(byteCount);
            } catch (Exception e) {
                log.error("本地文件下载异常", e);
            } finally {
                close(in);
                close(out);
            }
        }
        return fileItem;
    }


    /**
     * 本地文件下载
     */
    public static FileItem downloadLocal2(String url) {
        FileItem fileItem = new FileItem();
        InputStream in = null;
        ByteArrayOutputStream out = null;
        try {
            in = new FileInputStream(new File(url));
            out = new ByteArrayOutputStream(BUFFER_SIZE_4KB);
            int byteCount = 0;
            int bytesRead;
            for (byte[] buffer = new byte[BUFFER_SIZE_4KB]; (bytesRead = in.read(buffer)) != -1; byteCount += bytesRead) {
                out.write(buffer, 0, bytesRead);
            }
            out.flush();
            fileItem.setBytes(out.toByteArray());
            fileItem.setSize(byteCount);
        } catch (Exception e) {
            log.error("本地文件下载异常", e);
        } finally {
            close(in);
            close(out);
        }
        return fileItem;
    }

}
