package com.tiangong.util;

import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Positions;
import net.coobird.thumbnailator.name.Rename;

import javax.imageio.ImageIO;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;

/**
 * @description 生成缩略图和水印非常好用, 具体帮助文档 https://github.com/coobird/thumbnailator/wiki/Maven
 * 缩略图
 * 水印
 * 选择
 * 格式转换
 * @Create 2024-01-20
 */
public class ThumbnailatorUtils {

    private static final String FROM_PATH = "C:\\Users\\<USER>\\Desktop\\beiyong\\image\\suolvetu.jpg";
    private static final String TO_PATH = "C:\\Users\\<USER>\\Desktop\\beiyong\\image\\demo1\\";



    public static void main(String[] args) {
        //使用给定的图片生成指定大小的图片
//        generateFixedSizeImage();

        //对原图加水印,然后顺时针旋转90度,最后压缩为80%保存
//        generateRotationWatermark();


        //按比例缩放图片
//        generateScale();

        //生成缩略图到指定的目录
        generateThumbnail2Directory();

        //将指定目录下所有图片生成缩略图
        //generateDirectoryThumbnail();
    }

    /**
     * 使用给定的图片生成指定大小的图片
     */
    public static void generateFixedSizeImage(byte[] bytes,String thumbnailUrl,int width, int height) {
        File file = new File(thumbnailUrl);
        if (!file.exists()){
            file.getParentFile().mkdirs();
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        try {
//            Thumbnails.of(inputStream)
//                    .size(width, height)
//                    .toFiles(new File(TO_PATH), Rename.NO_CHANGE);//指定的目录一定要存在,否则报错
            Thumbnails.of(inputStream)
                    .size(width, height)
                    .toFile(file);
        } catch (IOException e) {
            System.out.println("原因: " + e.getMessage());
        }finally {
            FileUtil.close(inputStream);
        }
//        // 生成缩略图，返回文件输出流
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(4096);
//        try {
//
//            Thumbnails.of(FROM_PATH).size(width, height).toOutputStream(outputStream);
//        } catch (IOException e) {
//            System.out.println("原因: " + e.getMessage());
//        }
//        return outputStream;


    }

    /**
     * 对原图加水印,然后顺时针旋转90度,最后压缩为80%保存
     */
    public static void generateRotationWatermark() {
        String fileName = System.currentTimeMillis() + ".jpg";
        try {
            Thumbnails.of(FROM_PATH)
                    // 缩放大小
                    .size(300, 160)
//                    .rotate(90) // 顺时针旋转90度
                    // 水印位于右下角,半透明
                    .watermark(Positions.BOTTOM_RIGHT, ImageIO.read(new File(FROM_PATH)), 0.5f)
                    // 图片压缩80%质量
                    .outputQuality(0.8)
                    .toFile(TO_PATH + fileName);
        } catch (IOException e) {
            System.out.println("原因: " + e.getMessage());
        }
    }

    /**
     * 按比例缩放图片
     */
    public static void generateScale(double ratio) {
        String fileName = System.currentTimeMillis() + ".jpg";
        try {
            Thumbnails.of(FROM_PATH)
                    // 图片缩放指定比例, 不能和size()一起使用
                    .scale(ratio)
                    // 图片质量压缩指定比例
                    .outputQuality(ratio)
                   .toFile(TO_PATH + fileName);
        } catch (IOException e) {
            System.out.println("原因: " + e.getMessage());
        }
    }

    /**
     * 生成缩略图到指定的目录
     */
    public static void generateThumbnail2Directory() {
        String fileName = System.currentTimeMillis() + ".jpg";

        try {
            Thumbnails.of(FROM_PATH)
                    // 图片缩放50%, 不能和size()一起使用
                    .scale(0.5)
                    // 指定的目录一定要存在,否则报错
                    .toFiles(new File(TO_PATH), Rename.NO_CHANGE);
        } catch (IOException e) {
            System.out.println("原因: " + e.getMessage());
        }
    }

    /**
     * 将指定目录下所有图片生成缩略图
     */
    public static void generateDirectoryThumbnail() {
        String fileName = System.currentTimeMillis() + ".jpg";
        try {
            Thumbnails.of(new File(FROM_PATH).listFiles()).
                    //scalingMode(ScalingMode.BICUBIC).
                            // 图片缩放80%, 不能和size()一起使用
                    scale(0.8).
                    // 指定的目录一定要存在,否则报错
                    toFiles(new File(TO_PATH), Rename.SUFFIX_HYPHEN_THUMBNAIL);
        } catch (IOException e) {
            System.out.println("原因: " + e.getMessage());
        }
    }
}
