package com.tiangong.util;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 国际化工具类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class I18nUtil {

    private static MessageSource messageSource;

    static {
        I18nUtil.messageSource = SpringUtil.getBean(MessageSource.class);
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息代码
     * @return 国际化消息
     */
    public static String getMessage(String code) {
        return getMessage(code, null);
    }

    /**
     * 获取国际化消息
     *
     * @param code 消息代码
     * @param args 参数
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args) {
        return getMessage(code, args, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息
     *
     * @param code   消息代码
     * @param args   参数
     * @param locale 语言环境
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, Locale locale) {
        try {
            return messageSource.getMessage(code, args, locale);
        } catch (Exception e) {
            log.warn("获取国际化消息失败，code: {}, locale: {}, error: {}", code, locale, e.getMessage());
            return code;
        }
    }
}
