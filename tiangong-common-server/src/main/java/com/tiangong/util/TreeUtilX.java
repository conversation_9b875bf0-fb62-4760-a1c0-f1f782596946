package com.tiangong.util;

import com.tiangong.user.domain.resp.AuthMenuResp;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * @Description: 树形结构
 */
public class TreeUtilX {

    /**
     * 菜单转换树形结构
     *
     * @param list
     * @return
     */
    public static List<AuthMenuResp> listToTree(List<AuthMenuResp> list) {
        //用递归找子。
        List<AuthMenuResp> treeList = new CopyOnWriteArrayList<>();
        for (AuthMenuResp tree : list) {
            if ("0".equals(tree.getParentItemId())) {
                treeList.add(findChildren(tree, list));
            }
        }
        return treeList;
    }

    /**
     * 寻找子节点
     */
    private static AuthMenuResp findChildren(AuthMenuResp tree, List<AuthMenuResp> list) {
        for (AuthMenuResp node : list) {
            if (node.getParentItemId().equals(tree.getMenuId())) {
                if (tree.getChildren() == null) {
                    tree.setChildren(new CopyOnWriteArrayList<>());
                }
                tree.getChildren().add(findChildren(node, list));
            }
        }
        return tree;
    }

}
