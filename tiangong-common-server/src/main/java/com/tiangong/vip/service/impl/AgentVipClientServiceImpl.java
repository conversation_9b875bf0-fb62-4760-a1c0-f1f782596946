package com.tiangong.vip.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.StrPool;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.AgentVipScopeTypeEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.organization.domain.dto.OrgBaseDTO;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.*;
import com.tiangong.vip.domain.dto.AgentVipCacheDTO;
import com.tiangong.vip.domain.po.AgentVipClientPO;
import com.tiangong.vip.domain.vo.AgentVipClientPageVO;
import com.tiangong.vip.dto.AgentVipClientDTO;
import com.tiangong.vip.dto.AgentVipClientLevelResultDTO;
import com.tiangong.vip.dto.AgentVipClientResultDTO;
import com.tiangong.vip.mapper.AgentVipClientMapper;
import com.tiangong.vip.service.AgentVipClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

/**
 * VIP客户名单Service实现类
 * <AUTHOR>
 * @date 2025/6/9
 * @Description VIP客户名单业务逻辑实现，包含缓存管理和事务处理
 */
@Slf4j
@Service
public class AgentVipClientServiceImpl extends ServiceImpl<AgentVipClientMapper, AgentVipClientPO> implements AgentVipClientService {

    @Autowired
    private AgentVipClientMapper vipClientMapper;

    @Autowired
    private AgentVipClientServiceImpl agentVipClientService;

    @Autowired
    private OrgMapper orgMapper;
    private final static Integer YES = 1;
    private static final String VIP_CLIENT_CACHE_KEY = "VIP:CLIENT_DATA:";
    private static final String EMPTY_CACHE_VALUE = "[]";
    private static final TypeReference<List<AgentVipCacheDTO>> VIP_LIST_TYPE_REF =
            new TypeReference<List<AgentVipCacheDTO>>() {
            };

    private static final int VIP_CUSTOMER_BATCH_LIMIT = 20;

    @Override
    public IPage<AgentVipClientDTO> selectVipClientPageList(AgentVipClientPageVO vo) {
        IPage<AgentVipClientDTO> page = new Page<>(vo.getCurrentPage(), vo.getPageSize());
        return vipClientMapper.selectVipClientPageList(page, vo);
    }


    @Override
    public void saveOrUpdate(AgentVipClientDTO vipClientDTO) {
        Integer oldId = vipClientDTO.getId();
        // 参数校验
        validateBatchSaveParams(vipClientDTO);
        // 解析输入数据
        List<String> agentCodes = parseAgentCodes(vipClientDTO.getAgentCode());
        // 验证客户编码数量不超过20个
        if (agentCodes.size() > VIP_CUSTOMER_BATCH_LIMIT) {
            throw new SysException(ErrorCodeEnum.VIP_CUSTOMER_BATCH_LIMIT_EXCEEDED);
        }
        List<String> vipNames = parseVipNames(vipClientDTO.getVipName());
        //校验存在的重复项
        Set<String> duplicateAgentCodes = new HashSet<>(agentCodes);
        Set<String> duplicateVipNames = new HashSet<>(vipNames);
        if (duplicateAgentCodes.size() != agentCodes.size()) {
            throw new SysException(ErrorCodeEnum.VIP_AGENT_CODE_DUPLICATE);
        }
        if (duplicateVipNames.size() != vipNames.size()) {
            throw new SysException(ErrorCodeEnum.VIP_NAME_DUPLICATE);
        }
        // 查询客户列表 k:客户编码 v:客户信息
        List<OrgBaseDTO> orgBaseDTOS = orgMapper.selectByOrgCodes(agentCodes);
//        if (orgBaseDTOS.size() != agentCodes.size()) {
//            throw new SysException(ErrorCodeEnum.VIP_AGENT_CODE_INVALID);
//        }
        Map<String, OrgBaseDTO> orgMap = orgBaseDTOS.stream()
                .collect(Collectors.toMap(OrgBaseDTO::getOrgCode, e -> e, (oldVal, newVal) -> oldVal));
        // 查询已存在数据并构建三层嵌套 映射三层嵌套Map：<客户编码, <客户范围, <VIP姓名, VIP信息>>>
        List<AgentVipClientDTO> existingClients = vipClientMapper.selectVipClientByAgentCodes(agentCodes);
        Map<String, Map<Integer, Map<String, AgentVipClientDTO>>> existingMap = buildExistingDataMap(existingClients);
        // 构建要保存的数据
        List<AgentVipClientPO> vipClientsToSave = buildNeedSaveData(vipClientDTO, agentCodes, vipNames);
        // 旧数据处理
        if (oldId != null) {
            AgentVipClientPO oldVipClient = getById(oldId);
            settingOldVipClient(oldVipClient, vipClientsToSave, vipClientDTO.getCreatedBy());
        }
        // 与重复的数据进行校验
        for (AgentVipClientPO vipClientPO : vipClientsToSave) {
            // 检查VIP客户是否存在
            OrgBaseDTO orgBaseDTO = orgMap.get(vipClientPO.getAgentCode());
            AgentVipClientDTO vipClientExists = getVipClientExists(existingMap, vipClientPO.getAgentCode(), vipClientPO.getScopeType(), vipClientPO.getVipName(), orgBaseDTO);
            if (vipClientExists != null) {
                //若该数据是编辑前已存在的数据 并且不为空则跳过
                boolean exist = oldId != null && oldId.equals(vipClientExists.getId());
                if (!exist) {
                    String duplicateMessage = buildDuplicateMessage(vipClientPO.getAgentCode(), vipClientPO.getScopeType(), vipClientPO.getVipName());
                    throw new SysException(ErrorCodeEnum.VIP_CUSTOMER_INFO_DUPLICATE.errorCode, duplicateMessage);
                }
            }
            //设置客户信息
            vipClientPO.setAgentName(orgBaseDTO.getOrgName());
            vipClientPO.setPartnerCode(orgBaseDTO.getPartnerCode());
        }
        agentVipClientService.batchSaveOrUpdateDb(vipClientsToSave);
    }

    /**
     * 批量保存 并同步缓存
     * @param vipClientsToSave 本次需要更新/新增的数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveOrUpdateDb(List<AgentVipClientPO> vipClientsToSave) {
        agentVipClientService.saveOrUpdateBatch(vipClientsToSave);

        // 使用事务同步机制，确保在事务提交后更新缓存
        Set<String> agentCodes = vipClientsToSave.stream().map(AgentVipClientPO::getAgentCode).collect(Collectors.toSet());
        registerCacheUpdateAfterTransaction(agentCodes);
    }

    /**
     * 处理旧数据 并设置旧数据id
     * @param oldVipClient 旧数据
     * @param vipClientsToSave 待插入数据
     * @param operator 操作人
     */
    private void settingOldVipClient(AgentVipClientPO oldVipClient, List<AgentVipClientPO> vipClientsToSave, String operator) {
        if (vipClientsToSave.size() > 1) {
            throw new SysException(ErrorCodeEnum.VIP_CUSTOMER_VIP_NAME_UPDATE_ERROR);
        }
        AgentVipClientPO vipClientPO = vipClientsToSave.get(0);
        vipClientPO.setId(oldVipClient.getId());
        vipClientPO.setCreatedBy(null);
        vipClientPO.setCreatedDt(null);
        vipClientPO.setUpdatedDt(DateUtilX.dateToString(new Date(), CommonConstants.DATE_TIME_FORMAT));
        vipClientPO.setUpdatedBy(operator);
    }

    /**
     * 参数校验
     */
    private void validateBatchSaveParams(AgentVipClientDTO vipClientDTO) {
        if (vipClientDTO == null) {
            throw new SysException(ErrorCodeEnum.VIP_CLIENT_DTO_NULL);
        }

        if (StrUtilX.isEmpty(vipClientDTO.getAgentCode())) {
            throw new SysException(ErrorCodeEnum.VIP_AGENT_CODE_EMPTY);
        }
    }

    /**
     * 解析客户编码列表
     */
    private List<String> parseAgentCodes(String agentCodeStr) {
        List<String> agentCodes = StrUtilX.stringToList(agentCodeStr, ",");
        if (CollUtilX.isEmpty(agentCodes)) {
            throw new SysException(ErrorCodeEnum.VIP_AGENT_CODE_EMPTY);
        }
        return agentCodes;
    }

    /**
     * 解析VIP姓名列表
     */
    private List<String> parseVipNames(String vipNameStr) {
        if (StrUtilX.isEmpty(vipNameStr)) {
            return Collections.emptyList();
        }
        return StrUtilX.stringToList(vipNameStr, ",");
    }


    /**
     * 查询已存在数据并构建映射
     *
     * @param vipClientDTOList VIP客户列表
     * @return 三层嵌套Map：<客户编码, <客户范围, <VIP姓名, VIP信息>>> 客户范围为全部人员时 vipName为空
     */
    private Map<String, Map<Integer, Map<String, AgentVipClientDTO>>> buildExistingDataMap(List<AgentVipClientDTO> vipClientDTOList) {
        Map<String, Map<Integer, Map<String, AgentVipClientDTO>>> existingMap = new HashMap<>();

        if (CollUtilX.isNotEmpty(vipClientDTOList)) {
            for (AgentVipClientDTO vipClient : vipClientDTOList) {
                String agentCode = vipClient.getAgentCode();
                Integer scopeType = vipClient.getScopeType();
                String vipNameStr = vipClient.getVipName();
                // 指定全部人员的情况
                if (AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey().equals(scopeType)) {
                    // VIP姓名为空（null或空字符串），表示指定所有人员，对应的map中应该为空
                    existingMap.computeIfAbsent(agentCode, k -> new HashMap<>())
                            .computeIfAbsent(scopeType, k -> new HashMap<>())
                            .put("", vipClient);

                } else {
                    // 指定人员的情况
                    // 第一层：客户编码
                    existingMap.computeIfAbsent(agentCode, k -> new HashMap<>())
                            // 第二层：客户范围
                            .computeIfAbsent(scopeType, k -> new HashMap<>())
                            // 第三层：VIP姓名（单个姓名）
                            .put(vipNameStr, vipClient);
                }
            }
        }
        return existingMap;
    }
    /**
     * 查询已存在数据并构建缓存映射
     * 构建三层嵌套的VIP数据映射结构，用于快速查找和匹配
     * @param vipClientDTOList VIP客户缓存数据列表
     * @return 三层嵌套Map：<客户编码, <客户范围, <VIP姓名, VIP信息>>> 客户范围为全部人员时 vipName为空
     */
    private Map<String, Map<Integer, Map<String, AgentVipCacheDTO>>> buildExistingDataCacheMap(List<AgentVipCacheDTO> vipClientDTOList) {
        Map<String, Map<Integer, Map<String, AgentVipCacheDTO>>> existingMap = new HashMap<>();

        if (CollUtilX.isNotEmpty(vipClientDTOList)) {
            for (AgentVipCacheDTO vipClient : vipClientDTOList) {
                String agentCode = vipClient.getAgentCode();
                Integer scopeType = vipClient.getScopeType();
                String vipNameStr = vipClient.getVipName();
                // 指定全部人员的情况
                if (AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey().equals(scopeType)) {
                    // VIP姓名为空（null或空字符串），表示指定所有人员，对应的map中应该为空
                    existingMap.computeIfAbsent(agentCode, k -> new HashMap<>())
                            .computeIfAbsent(scopeType, k -> new HashMap<>())
                            .put("", vipClient);

                } else {
                    // 指定人员的情况
                    // 第一层：客户编码
                    existingMap.computeIfAbsent(agentCode, k -> new HashMap<>())
                            // 第二层：客户范围
                            .computeIfAbsent(scopeType, k -> new HashMap<>())
                            // 第三层：VIP姓名（单个姓名）
                            .put(vipNameStr, vipClient);
                }
            }
        }
        return existingMap;
    }
    /**
     * 检查VIP客户是否存在
     *
     * @param existingMap 已存在数据的三层嵌套Map
     * @param agentCode   客户编码
     * @param scopeType   客户范围
     * @param vipName     VIP姓名
     * @return 是否存在
     */
    private AgentVipClientDTO getVipClientExists(Map<String, Map<Integer, Map<String, AgentVipClientDTO>>> existingMap,
                                                 String agentCode, Integer scopeType, String vipName, OrgBaseDTO orgBaseDTO) {
        if (orgBaseDTO == null) {
            //异常提示：无效的客户编码
            throw new SysException(ErrorCodeEnum.VIP_AGENT_CODE_INVALID.getErrorCode(),buildInvalidAgentCodeMessage(agentCode));
        }
        if (!YES.equals(orgBaseDTO.getAvailableStatus())) {
            //异常提示：客户编码已禁用
            throw new SysException(ErrorCodeEnum.VIP_AGENT_CODE_DISABLED.getErrorCode(),buildUnableAgentCodeMessage(agentCode));
        }
        // 检查客户编码和范围是否存在
        if (!existingMap.containsKey(agentCode) || !existingMap.get(agentCode).containsKey(scopeType)) {
            return null;
        }
        Map<String, AgentVipClientDTO> vipNameMap = existingMap.get(agentCode).get(scopeType);
        //范围全部取仅存在的一条 范围指定取指定姓名的一条
        if (AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey().equals(scopeType)) {
            return vipNameMap.get("");
        } else {
            return vipNameMap.get(vipName);
        }
    }

    /**
     * 构建要保存的数据并检查重复
     */
    private List<AgentVipClientPO> buildNeedSaveData(AgentVipClientDTO vipClientDTO, List<String> agentCodes, List<String> vipNames) {

        List<AgentVipClientPO> vipClientsToSave = new ArrayList<>(agentCodes.size() * vipNames.size());
        String createdDt = DateUtilX.dateToString(new Date(), CommonConstants.DATE_TIME_FORMAT);
        for (String agentCode : agentCodes) {
            String trimAgentCode = agentCode.trim();
            //全部人员
            if (AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey().equals(vipClientDTO.getScopeType())) {
                // 构建要保存的数据
                AgentVipClientPO vipClientPO = new AgentVipClientPO();
                vipClientPO.setAgentCode(trimAgentCode);
                vipClientPO.setScopeType(vipClientDTO.getScopeType());
                vipClientPO.setVipName("");
                vipClientPO.setVipLevel(vipClientDTO.getVipLevel());
                vipClientPO.setRemark(vipClientDTO.getRemark());
                vipClientPO.setCreatedBy(vipClientDTO.getCreatedBy());
                vipClientPO.setCreatedDt(createdDt);
                vipClientsToSave.add(vipClientPO);
            } else {
                for (String vipName : vipNames) {
                    String trimVipName = vipName.trim();
                    // 构建要保存的数据
                    AgentVipClientPO vipClientPO = new AgentVipClientPO();
                    vipClientPO.setAgentCode(trimAgentCode);
                    vipClientPO.setScopeType(vipClientDTO.getScopeType());
                    vipClientPO.setVipName(trimVipName);
                    vipClientPO.setVipLevel(vipClientDTO.getVipLevel());
                    vipClientPO.setRemark(vipClientDTO.getRemark());
                    vipClientPO.setCreatedBy(vipClientDTO.getCreatedBy());
                    vipClientPO.setCreatedDt(createdDt);
                    vipClientsToSave.add(vipClientPO);
                }
            }
        }
        return vipClientsToSave;
    }

    /**
     * 构建重复数据错误信息
     */
    private String buildDuplicateMessage(String agentCode, Integer scopeType, String vipName) {
        String scopeTypeDesc = I18nUtil.getMessage(AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey().equals(scopeType) ? "vip.scope.type.all.personnel" : "vip.scope.type.specified.personnel");
        Object[] args = {agentCode, scopeTypeDesc, vipName};
        return I18nUtil.getMessage(AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey().equals(scopeType)?"vip.client.duplicate.message2":"vip.client.duplicate.message", args);
    }
    /**
     * 构建重复数据错误信息
     */
    private String buildInvalidAgentCodeMessage(String agentCode) {
        Object[] args = {agentCode};
        return I18nUtil.getMessage("vip.client.agentCode.invalid.message",args);
    }
    /**
     * 构建重复数据错误信息
     */
    private String buildUnableAgentCodeMessage(String agentCode) {
        Object[] args = {agentCode};
        return I18nUtil.getMessage("vip.client.agentCode.unable.message",args);
    }
    @Override
    public boolean deleteVipClientById(Integer id) {
        AgentVipClientPO vipClientPO = getById(id);
        if (vipClientPO == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        return agentVipClientService.removeDb(id, vipClientPO);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean removeDb(Integer id, AgentVipClientPO vipClientPO) {
        boolean b = removeById(id);

        // 使用事务同步机制，确保在事务提交后更新缓存
        registerCacheUpdateAfterTransaction(Collections.singleton(vipClientPO.getAgentCode()));
        return b;
    }

    @Override
    public List<AgentVipClientDTO> listVipClientByAgentCodes(List<String> agentCodes) {
        log.info("根据客户编码列表批量查询VIP客户名单，参数：{}", agentCodes);
        // 参数校验
        if (CollUtilX.isEmpty(agentCodes)) {
            return new ArrayList<>();
        }
        return vipClientMapper.selectVipClientByAgentCodes(agentCodes);
    }

    @Override
    public List<AgentVipClientResultDTO> vipSearch(List<AgentVipClientResultDTO> vipClientResultDTOList) {
        log.info("是否VIP订单获取参数：{}", vipClientResultDTOList);
        List<String> agentCodeList = vipClientResultDTOList.stream().map(AgentVipClientResultDTO::getAgentCode).distinct().collect(Collectors.toList());
        Map<String, Map<Integer, Map<String, AgentVipCacheDTO>>> vipMap = batchGetVipDataFromCache(agentCodeList);

        for (AgentVipClientResultDTO vipClientResultDTO : vipClientResultDTOList) {
            vipClientResultDTO.setIsVip(0);

            Map<Integer, Map<String, AgentVipCacheDTO>> vipClientMap = vipMap.get(vipClientResultDTO.getAgentCode());
            // 客户编码不存在于VIP 该客户不是VIP
            if (vipClientMap == null) {
                continue;
            }

            // 检查是否在“全部人员”范围内
            Map<String, AgentVipCacheDTO> allPersonnelMap = vipClientMap.get(AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey());
            if (allPersonnelMap != null && allPersonnelMap.containsKey("")) {
                // 客户在全部人员范围内，直接设置为VIP
                vipClientResultDTO.setIsVip(1);
                continue;
            }

            // 检查是否在“指定人员”范围内
            Map<String, AgentVipCacheDTO> specifiedPersonnelMap = vipClientMap.get(AgentVipScopeTypeEnum.SPECIFIED_PERSONNEL.getKey());
            if (specifiedPersonnelMap == null || specifiedPersonnelMap.isEmpty()) {
                continue;
            }

            // 处理指定人员的VIP姓名匹配
            String inputVipName = vipClientResultDTO.getVipName();
            if (StrUtilX.isNotEmpty(inputVipName)) {
                // 将输入的VIP姓名按逗号分割
                String[] inputVipNames = inputVipName.split(",");

                for (String singleInputName : inputVipNames) {
                    if (StrUtilX.isEmpty(singleInputName)) {
                        continue;
                    }

                    // 检查是否在指定人员名单中
                    if (isVipNameMatched(specifiedPersonnelMap, singleInputName)) {
                        vipClientResultDTO.setIsVip(1);
                        break;
                    }
                }
            }
        }
        return vipClientResultDTOList;
    }

    @Override
    public List<AgentVipClientLevelResultDTO> vipLevelSearch(List<AgentVipClientLevelResultDTO> req) {
        log.info("VIP等级获取参数：{}", req);
        List<String> agentCodeList = req.stream().map(AgentVipClientLevelResultDTO::getAgentCode).distinct().collect(Collectors.toList());
        Map<String, Map<Integer, Map<String, AgentVipCacheDTO>>> vipMap = batchGetVipDataFromCache(agentCodeList);
        for (AgentVipClientLevelResultDTO vipClientResultDTO : req) {
            Map<Integer, Map<String, AgentVipCacheDTO>> vipClientMap = vipMap.get(vipClientResultDTO.getAgentCode());
            // 客户编码不存在于VIP 该客户不是VIP
            if (vipClientMap == null) {
                continue;
            }
            // 检查是否在“指定人员”范围内
            Map<String, AgentVipCacheDTO> specifiedPersonnelMap = vipClientMap.get(AgentVipScopeTypeEnum.SPECIFIED_PERSONNEL.getKey());
            if (specifiedPersonnelMap != null) {
                AgentVipCacheDTO agentVipCacheDTO = getAgentVipCacheDTO(vipClientResultDTO.getVipName(), specifiedPersonnelMap);
                if(agentVipCacheDTO!=null){
                    vipClientResultDTO.setLevel(agentVipCacheDTO.getVipLevel());
                    continue;
                }
            }
            // 检查是否在“全部人员”范围内
            Map<String, AgentVipCacheDTO> allPersonnelMap = vipClientMap.get(AgentVipScopeTypeEnum.ALL_PERSONNEL.getKey());
            if (allPersonnelMap != null && allPersonnelMap.containsKey("")) {
                // 客户在全部人员范围内，直接设置为VIP
                vipClientResultDTO.setLevel(allPersonnelMap.get("").getVipLevel());
            }
        }
        return req;
    }

    private static AgentVipCacheDTO getAgentVipCacheDTO(String inputVipName, Map<String, AgentVipCacheDTO> specifiedPersonnelMap) {
        if(specifiedPersonnelMap.containsKey(inputVipName)){
            return specifiedPersonnelMap.get(inputVipName);
        }
        // 若inputVipName含/ 判断inputVipName 是否含英文
        if (inputVipName.contains(StrPool.SLASH)) {
            // 判断是否含英文字母
            boolean containsEnglish = inputVipName.matches(".*[a-zA-Z].*");

            String processedName;
            if (containsEnglish) {
                // 若含英文 将/替换成空格,进行匹配
                processedName = inputVipName.replace(StrPool.SLASH, StrPool.SPACE);
            } else {
                // 若不含英文 将/去除,进行匹配
                processedName = inputVipName.replace(StrPool.SLASH, "");
            }

            // 检查处理后的名称是否匹配
            if (specifiedPersonnelMap.containsKey(processedName)) {
                return specifiedPersonnelMap.get(processedName);
            }
        }
        return null;
    }

    /**
     * 检查VIP姓名是否匹配
     *
     * @param specifiedPersonnelMap 指定人员Map
     * @param inputVipName          输入的VIP姓名
     * @return 是否匹配
     */
    private boolean isVipNameMatched(Map<String, AgentVipCacheDTO> specifiedPersonnelMap, String inputVipName) {
        // 直接匹配：检查是否存在完全相同的key
        if (specifiedPersonnelMap.containsKey(inputVipName)) {
            return true;
        }
        // 若inputVipName含/ 判断inputVipName 是否含英文
        if (inputVipName.contains(StrPool.SLASH)) {
            // 判断是否含英文字母
            boolean containsEnglish = inputVipName.matches(".*[a-zA-Z].*");

            String processedName;
            if (containsEnglish) {
                // 若含英文 将/替换成空格,进行匹配
                processedName = inputVipName.replace(StrPool.SLASH, StrPool.SPACE);
            } else {
                // 若不含英文 将/去除,进行匹配
                processedName = inputVipName.replace(StrPool.SLASH, "");
            }

            // 检查处理后的名称是否匹配
            if (specifiedPersonnelMap.containsKey(processedName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取批量VIP数据
     * 使用新的主动缓存维护策略，直接从缓存获取数据
     *
     * @param agentCodeList 客户编号
     * @return 批量VIP数据
     */
    private Map<String, Map<Integer, Map<String, AgentVipCacheDTO>>> batchGetVipDataFromCache(List<String> agentCodeList) {
        log.info("从缓存获取VIP数据，客户编码数量：{}", agentCodeList.size());

        List<AgentVipCacheDTO> allVipData = new ArrayList<>();

        try {
            // 构建缓存key列表
            List<String> cacheKeys = agentCodeList.stream()
                    .map(agentCode -> VIP_CLIENT_CACHE_KEY + agentCode)
                    .collect(Collectors.toList());

            // 批量获取缓存
            List<String> cachedValues = RedisTemplateX.multiGet(cacheKeys);

            // 处理缓存结果
            for (int i = 0; i < cachedValues.size(); i++) {
                String cachedValue = cachedValues.get(i);
                String agentCode = agentCodeList.get(i);

                if (cachedValue != null && !EMPTY_CACHE_VALUE.equals(cachedValue)) {
                    // 缓存命中且有数据
                    try {
                        List<AgentVipCacheDTO> parsedData = JSON.parseObject(cachedValue, VIP_LIST_TYPE_REF.getType());
                        if (CollUtilX.isNotEmpty(parsedData)) {
                            allVipData.addAll(parsedData);
                        }
                    } catch (Exception e) {
                        log.error("解析VIP缓存数据失败，客户编码：{}", agentCode, e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("从缓存获取VIP数据失败，降级为数据库查询", e);
            // 缓存异常时降级为数据库查询
            allVipData = vipClientMapper.selectList(Wrappers.<AgentVipClientPO>lambdaQuery()
                            .in(AgentVipClientPO::getAgentCode, agentCodeList)
                            .select(AgentVipClientPO::getAgentCode,
                                    AgentVipClientPO::getScopeType,
                                    AgentVipClientPO::getVipName,
                                    AgentVipClientPO::getVipLevel))
                    .stream()
                    .map(CommonDtoConvert.INSTANCE::convert)
                    .collect(Collectors.toList());
        }
        return buildExistingDataCacheMap(allVipData);
    }

    /**
     * 重置VIP缓存
     * 使用无损重置策略，避免清空缓存导致权益丢失
     */
    @Override
    public void resetVipCache() {
        try {
            // 使用无损重置策略：先加载新数据，再替换旧数据
            reloadAllVipDataToCache();
        } catch (Exception e) {
            log.error("VIP缓存重置失败", e);
            throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 无损重新加载所有VIP数据到缓存
     * 使用原子性更新，避免清空缓存导致权益丢失
     */
    private void reloadAllVipDataToCache() {
        try {
            // 查询所有VIP数据
            List<AgentVipCacheDTO> allVipData = vipClientMapper.selectList(Wrappers.<AgentVipClientPO>lambdaQuery()
                            .select(AgentVipClientPO::getAgentCode,
                                    AgentVipClientPO::getScopeType,
                                    AgentVipClientPO::getVipName,
                                    AgentVipClientPO::getVipLevel))
                    .stream()
                    .map(CommonDtoConvert.INSTANCE::convert)
                    .collect(Collectors.toList());

            // 按客户编码分组构建缓存数据
            Map<String, String> cacheDataMap = new HashMap<>();

            if (CollUtilX.isNotEmpty(allVipData)) {
                // 有VIP数据的客户
                Map<String, String> vipDataMap = allVipData.stream()
                        .collect(Collectors.groupingBy(
                                AgentVipCacheDTO::getAgentCode,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        JSON::toJSONString
                                )
                        ))
                        .entrySet()
                        .stream()
                        .collect(Collectors.toMap(
                                entry -> VIP_CLIENT_CACHE_KEY + entry.getKey(),
                                Map.Entry::getValue
                        ));
                cacheDataMap.putAll(vipDataMap);
            }

            // 查询所有已缓存的客户编码，为没有VIP数据的客户设置空值缓存
            Set<String> existingCacheKeys = RedisTemplateX.keys(VIP_CLIENT_CACHE_KEY + "*");
            if (CollUtilX.isNotEmpty(existingCacheKeys)) {
                Set<String> existingAgentCodes = existingCacheKeys.stream()
                        .map(key -> key.replace(VIP_CLIENT_CACHE_KEY, ""))
                        .collect(Collectors.toSet());

                Set<String> vipAgentCodes = allVipData.stream()
                        .map(AgentVipCacheDTO::getAgentCode)
                        .collect(Collectors.toSet());

                // 为没有VIP数据但已缓存的客户设置空值
                existingAgentCodes.stream()
                        .filter(agentCode -> !vipAgentCodes.contains(agentCode))
                        .forEach(agentCode -> cacheDataMap.put(VIP_CLIENT_CACHE_KEY + agentCode, EMPTY_CACHE_VALUE));
            }

            // 原子性批量更新缓存
            if (!cacheDataMap.isEmpty()) {
                RedisTemplateX.multiSet(cacheDataMap);
                log.info("成功重新加载{}个客户的VIP数据到缓存", cacheDataMap.size());
            }
        } catch (Exception e) {
            log.error("重新加载VIP数据到缓存失败", e);
            throw e;
        }
    }

    /**
     * 注册事务提交后的缓存更新操作
     * 使用Spring事务同步机制，确保在事务成功提交后才更新缓存
     * @param agentCodes 需要更新缓存的客户编码集合
     */
    private void registerCacheUpdateAfterTransaction(Set<String> agentCodes) {
        if (CollUtilX.isEmpty(agentCodes)) {
            return;
        }

        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    log.info("事务提交成功，开始更新VIP缓存，客户编码：{}", agentCodes);
                    updateVipCacheForAgentCodesAfterCommit(agentCodes);
                }
            });
        } else {
            // 如果没有事务上下文，直接更新缓存
            log.warn("没有事务上下文，直接更新VIP缓存，客户编码：{}", agentCodes);
            updateVipCacheForAgentCodesAfterCommit(agentCodes);
        }
    }

    /**
     * 事务提交后更新指定客户的VIP缓存
     * 此时可以获取到最新的数据库数据
     * @param agentCodes 需要更新缓存的客户编码集合
     */
    private void updateVipCacheForAgentCodesAfterCommit(Set<String> agentCodes) {
        if (CollUtilX.isEmpty(agentCodes)) {
            return;
        }

        log.info("开始更新客户VIP缓存，客户编码：{}", agentCodes);
        // 事务已提交，可以获取到最新数据
        List<AgentVipCacheDTO> vipDataList = vipClientMapper.selectVipClientByAgentCodes(new ArrayList<>(agentCodes))
                .stream().map(CommonDtoConvert.INSTANCE::convert).collect(Collectors.toList());

        // 构建缓存数据
        Map<String, String> cacheDataMap = new HashMap<>();

        // 按客户编码分组
        Map<String, List<AgentVipCacheDTO>> groupedData = vipDataList.stream()
                .collect(Collectors.groupingBy(AgentVipCacheDTO::getAgentCode));

        // 为每个客户编码构建缓存
        for (String agentCode : agentCodes) {
            String cacheKey = VIP_CLIENT_CACHE_KEY + agentCode;
            List<AgentVipCacheDTO> agentVipData = groupedData.get(agentCode);

            if (CollUtilX.isNotEmpty(agentVipData)) {
                // 有VIP数据，缓存实际数据
                cacheDataMap.put(cacheKey, JSON.toJSONString(agentVipData));
            } else {
                // 无VIP数据，缓存空值防止缓存穿透
                cacheDataMap.put(cacheKey, EMPTY_CACHE_VALUE);
            }
        }

        // 批量更新缓存
        if (!cacheDataMap.isEmpty()) {
            RedisTemplateX.multiSet(cacheDataMap);
            log.info("成功更新{}个客户的VIP缓存", cacheDataMap.size());
        }
    }
}
