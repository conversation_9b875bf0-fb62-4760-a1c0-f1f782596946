package com.tiangong.vip.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.vip.dto.AgentVipClientDTO;
import com.tiangong.vip.domain.po.AgentVipClientPO;
import com.tiangong.vip.domain.vo.AgentVipClientPageVO;
import com.tiangong.vip.dto.AgentVipClientLevelResultDTO;
import com.tiangong.vip.dto.AgentVipClientResultDTO;

import java.util.List;

/**
 * VIP客户名单Service接口
 * <AUTHOR>
 * @date 2025/6/9
 * @Description VIP客户名单业务逻辑接口定义
 */
public interface AgentVipClientService extends IService<AgentVipClientPO> {

    /**
     * 分页查询VIP客户名单列表（使用专用VO）
     *
     * @param vo 分页查询条件
     * @return VIP客户名单列表
     */
    IPage<AgentVipClientDTO> selectVipClientPageList(AgentVipClientPageVO vo);

    /**
     * 更新VIP客户名单
     *
     * @param dto VIP客户名单信息
     */
    void saveOrUpdate(AgentVipClientDTO dto);

    /**
     * 根据ID删除VIP客户名单
     *
     * @param id 主键ID
     * @return 删除结果
     */
    boolean deleteVipClientById(Integer id);

    /**
     * 根据客户编码列表批量查询VIP客户名单
     *
     * @param agentCodes 客户编码列表
     * @return VIP客户名单列表
     */
    List<AgentVipClientDTO> listVipClientByAgentCodes(List<String> agentCodes);
    /**
     * 传入客户编码、客户信息列表，返回VIP客户信息
     *
     * @param vipClientResultDTOList 需要查询是否为vip的信息列表
     * @return 返回VIP客户信息 isVip 0非 1是
     */
    List<AgentVipClientResultDTO> vipSearch(List<AgentVipClientResultDTO> vipClientResultDTOList);

    /**
     * 传入客户编码、客户信息列表，返回VIP客户等级信息
     * @param req 请求参数
     * @return 返回VIP客户等级信息
     */
    List<AgentVipClientLevelResultDTO> vipLevelSearch(List<AgentVipClientLevelResultDTO> req);

    /**
     * 重置VIP缓存
     * VIP相关缓存并重新加载
     */
    void resetVipCache();
}
