package com.tiangong.vip.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * VIP客户名单VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class AgentVipClientVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 范围类型：0-指定人员，1-全部人员
     */
    private Integer scopeType;

    /**
     * VIP姓名
     */
    private String vipName;

    /**
     * VIP等级
     */
    private Integer vipLevel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合作商编码
     */
    private String partnerCode;
}
