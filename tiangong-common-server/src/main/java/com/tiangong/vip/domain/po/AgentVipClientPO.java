package com.tiangong.vip.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.*;

/**
 * VIP客户名单表
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@TableName("t_agent_vip_client")
@Table(name = "t_agent_vip_client")
public class AgentVipClientPO extends BasePO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 范围类型：0-指定人员，1-全部人员
     */
    private Integer scopeType;

    /**
     * VIP姓名
     */
    private String vipName;

    /**
     * VIP等级
     */
    private Integer vipLevel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合作商编码
     */
    private String partnerCode;
}
