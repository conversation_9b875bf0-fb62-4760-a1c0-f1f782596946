package com.tiangong.vip.domain.vo;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * VIP客户名单分页查询VO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentVipClientPageVO extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 范围类型：0-指定人员，1-全部人员
     */
    private Integer scopeType;

    /**
     * VIP姓名
     */
    private String vipName;

    /**
     * VIP等级
     */
    private Integer vipLevel;

    /**
     * 合作商编码
     */
    private String partnerCode;
}
