package com.tiangong.vip.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.dto.common.MyMapper;
import com.tiangong.vip.domain.po.AgentVipClientPO;
import com.tiangong.vip.domain.vo.AgentVipClientVO;
import com.tiangong.vip.dto.AgentVipClientDTO;
import com.tiangong.vip.domain.vo.AgentVipClientPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * VIP客户名单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface AgentVipClientMapper extends MyMapper<AgentVipClientPO>, BaseMapper<AgentVipClientPO> {

    /**
     * 分页查询VIP客户名单列表
     * 根据查询条件分页查询VIP客户名单信息
     * @param page 分页对象，包含分页参数
     * @param vo 分页查询条件，包含客户筛选条件
     * @return VIP客户名单分页列表
     */
    IPage<AgentVipClientDTO> selectVipClientPageList(IPage<AgentVipClientDTO> page, @Param("vipClientVO") AgentVipClientPageVO vo);

    /**
     * 查询VIP客户名单列表
     * 根据查询条件查询所有符合条件的VIP客户名单信息
     * @param vo 查询条件对象，包含客户筛选条件
     * @return VIP客户名单列表
     */
    List<AgentVipClientDTO> selectVipClientList(@Param("vipClientVO") AgentVipClientVO vo);

    /**
     * 根据客户编码列表查询VIP客户信息
     * 批量查询指定客户编码对应的VIP客户详细信息
     * @param agentCodes 客户编码列表
     * @return VIP客户信息列表
     */
    List<AgentVipClientDTO> selectVipClientByAgentCodes(@Param("agentCodes") List<String> agentCodes);
}
