package com.tiangong.vip.server;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.vip.domain.po.AgentVipClientPO;
import com.tiangong.vip.domain.vo.AgentVipClientPageVO;
import com.tiangong.vip.domain.vo.AgentVipClientUpdateVO;
import com.tiangong.vip.domain.vo.AgentVipClientVO;
import com.tiangong.vip.dto.AgentVipClientDTO;
import com.tiangong.vip.dto.AgentVipClientLevelResultDTO;
import com.tiangong.vip.dto.AgentVipClientResultDTO;
import com.tiangong.vip.service.AgentVipClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * VIP客户名单Controller
 * <AUTHOR>
 * @date 2025/6/9
 * @Description VIP客户名单控制器，处理VIP相关的HTTP请求
 */
@Slf4j
@RestController
@RequestMapping("/common/vip/client")
@Validated
public class AgentVipClientServer extends BaseController {

    @Autowired
    private AgentVipClientService vipClientService;

    /**
     * 查询VIP客户名单列表（分页）
     */
    @PostMapping("/page")
    public Response<PaginationSupportDTO<AgentVipClientDTO>> selectVipClientPage(@RequestBody @Valid AgentVipClientPageVO vo) {
        IPage<AgentVipClientDTO> agentVipClientDTOIPage = vipClientService.selectVipClientPageList(vo);
        return Response.success(convertDtoToVo(agentVipClientDTOIPage));
    }


    /**
     * 根据ID查询VIP客户名单详情
     */
    @PostMapping("/detail")
    public Response<AgentVipClientVO> selectVipClientById(@RequestBody @Valid AgentVipClientVO vo) {
        AgentVipClientPO byId = vipClientService.getById(vo.getId());
        return Response.success(CommonDtoConvert.INSTANCE.convertPOToVO(byId));
    }

    /**
     * 新增/编辑VIP客户名单
     */
    @PostMapping("/saveOrUpdate")
    public Response<AgentVipClientUpdateVO> insertVipClient(@RequestBody AgentVipClientVO vo) {
        AgentVipClientDTO convert = CommonDtoConvert.INSTANCE.convertVOToDTO(vo);
        convert.setCreatedBy(getUserName());
        AgentVipClientUpdateVO vipClientUpdateVO = new AgentVipClientUpdateVO();
        //第一次处理有换行的 ,第二次处理换行 替换后的逗号问题
        if (convert.getAgentCode() != null) {
            String replace = StrUtil.replace(StrUtil.replace(convert.getAgentCode(), "\n", ","), ",,", ",");
            convert.setAgentCode(replace);
        }
        if (convert.getVipName() != null) {
            String replace = StrUtil.replace(StrUtil.replace(convert.getVipName(), "\n", ","), ",,", ",");
            convert.setVipName(replace);
        }
        try {
            vipClientService.saveOrUpdate(convert);
        } catch (SysException e) {
            if (e.getCode().equals(ErrorCodeEnum.VIP_CUSTOMER_INFO_DUPLICATE.getErrorCode())
            || e.getCode().equals(ErrorCodeEnum.VIP_AGENT_CODE_INVALID.getErrorCode())
            || e.getCode().equals(ErrorCodeEnum.VIP_AGENT_CODE_DISABLED.getErrorCode())) {
                vipClientUpdateVO.setErrorMessage(e.getMessage());
                vipClientUpdateVO.setSuccess(false);
                return Response.success(vipClientUpdateVO);
            } else {
                throw e;
            }
        }
        vipClientUpdateVO.setSuccess(true);
        return Response.success(vipClientUpdateVO);
    }

    /**
     * 根据ID删除VIP客户名单
     */
    @PostMapping("/delete")
    public Response<Boolean> deleteVipClientById(@RequestBody AgentVipClientVO vo) {
        return Response.success(vipClientService.deleteVipClientById(vo.getId()));
    }

    /**
     * 根据客户编码列表批量查询VIP客户名单
     */
    @PostMapping("/listByAgentCodes")
    public Response<List<AgentVipClientDTO>> listVipClientByAgentCodes(@RequestBody List<String> agentCodes) {
        log.info("根据客户编码列表批量查询VIP客户名单，参数：{}", agentCodes);
        List<AgentVipClientDTO> result = vipClientService.listVipClientByAgentCodes(agentCodes);
        return Response.success(result);
    }

    /**
     * 传入客户编码、客户信息列表，返回VIP客户信息
     *
     * @param vipClientResultDTOList 需要查询是否为vip的信息列表
     * @return 返回VIP客户信息 isVip 0非 1是
     */
    @PostMapping("/vipSearch")
    @AnonymousAccess
    Response<List<AgentVipClientResultDTO>> vipSearch(@RequestBody List<AgentVipClientResultDTO> vipClientResultDTOList) {
        return Response.success(vipClientService.vipSearch(vipClientResultDTOList));
    }

    /**
     * 传入客户编码、客户信息列表，返回VIP客户等级信息
     *
     * @param req 请求参数
     * @return 返回VIP客户等级信息
     */
    @PostMapping("/vipLevelSearch")
    @AnonymousAccess
    Response<List<AgentVipClientLevelResultDTO>> vipLevelSearch(@RequestBody List<AgentVipClientLevelResultDTO> req) {
        return Response.success(vipClientService.vipLevelSearch(req));
    }

    /**
     * 重置VIP缓存
     * 清空所有VIP相关缓存并重新加载
     */
    @PostMapping("/resetCache")
    public Response<Boolean> resetVipCache() {
        vipClientService.resetVipCache();
        return Response.success(true);
    }
    /**
     * DTO分页结果转VO分页结果
     * @param page DTO分页结果对象
     * @return VO分页结果对象
     */
    private PaginationSupportDTO<AgentVipClientDTO> convertDtoToVo(IPage<AgentVipClientDTO> page) {
        PaginationSupportDTO<AgentVipClientDTO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(page.getRecords());
        paginationSupportDTO.setCurrentPage((int) page.getCurrent());
        paginationSupportDTO.setPageSize((int) page.getSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage((int) page.getPages());
        return paginationSupportDTO;
    }
}
