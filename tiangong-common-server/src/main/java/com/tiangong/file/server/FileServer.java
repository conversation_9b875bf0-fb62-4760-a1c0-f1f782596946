package com.tiangong.file.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.file.service.FileService;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/common/file")
public class FileServer {

    @Resource
    private FileService fileService;

    /**
     * 上传
     */
    @AnonymousAccess
    @ResponseBody
    @PostMapping("/upload")
    public Response<FileResp> upload(@RequestParam("file") MultipartFile file) throws IOException {
        return Response.success(fileService.upload(file));
    }

    /**
     * 下载
     */
    @PostMapping("/download")
    public Response download(@RequestBody FileReq req){
        return Response.success(fileService.download(req));
    }

    /**
     * 删除
     */
    @PostMapping("/del")
    public Response<Object> del(@RequestBody FileReq req){
        fileService.del(req);
        return Response.success();
    }

    /**
     * 根据文件id
     * 文件详情
     */
    @PostMapping("/list")
    @AnonymousAccess
    public Response<List<FileResp>> list(@RequestBody FileReq req){
        return Response.success(fileService.listNameByObjId(req));
    }

    /**
     * 更新文件
     */
    @AnonymousAccess
    @PostMapping("/update")
    public Response<Object> update(@RequestBody FileReq req){
        fileService.update(req);
        return Response.success();
    }

}
