package com.tiangong.file.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.file.domain.FileLogPO;
import com.tiangong.file.mapper.FileLogMapper;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import com.tiangong.file.service.FileService;
import com.tiangong.util.FileClientUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
public class FileServiceImpl extends ServiceImpl<FileLogMapper, FileLogPO> implements FileService {

    @Autowired
    private FileClientUtil fileClientUtil;

    @Override
    public FileResp upload(MultipartFile file) throws IOException {
        // 上传文件
        FileResp resp = fileClientUtil.upload(file.getBytes(), file.getOriginalFilename());

        // 保存到数据库
        FileLogPO fileLogPO = CommonDtoConvert.INSTANCE.FileConvert(resp);
        this.baseMapper.insert(fileLogPO);

        // 返回
        resp.setFileId(fileLogPO.getFileId());
        return resp;
    }


    @Override
    public FileResp upload(byte[] bytes, String fileName, String tableName, String fieldName){
        // 上传文件
        FileResp resp = fileClientUtil.upload(bytes, fileName);

        // 保存到数据库
        FileLogPO fileLogPO = CommonDtoConvert.INSTANCE.FileConvert(resp);
        fileLogPO.setTableName(tableName);
        fileLogPO.setFieldName(fieldName);
        this.baseMapper.insert(fileLogPO);

        // 返回
        return resp;
    }

    @Override
    public String download(FileReq req) {
        FileLogPO fileLogDO = this.baseMapper.selectById(req.getFileId());
        if (fileLogDO == null) {
            throw new SysException(ErrorCodeEnum.FILE_NOT_EXISTS);
        }
        return fileLogDO.getFileUrl();
    }

    @Override
    public void del(FileReq req) {
        FileLogPO fileLogPO = this.baseMapper.selectById(req.getFileId());
        if (fileLogPO == null) {
            throw new SysException(ErrorCodeEnum.FILE_NOT_EXISTS);
        }
        // 删除文件日志
        this.baseMapper.deleteById(req.getFileId());

        // 删除第三方服务的文件
        fileClientUtil.delFile(fileLogPO.getFileUrl());
    }

    @Override
    public FileLogPO detailDO(String fileId) {
        FileLogPO fileLogPO = this.baseMapper.selectById(fileId);
        fileLogPO.setThumbnailUrl(fileLogPO.getFileUrl());
        return fileLogPO;
    }

    @Override
    public List<FileLogPO> listDO(List<String> fileIds) {
        return this.baseMapper.selectBatchIds(fileIds);
    }

    @Override
    public List<FileResp> listNameByObjId(FileReq req) {
        QueryWrapper<FileLogPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("obj_id", req.getObjId());
        queryWrapper.eq("table_name", req.getTableName());

        List<FileLogPO> fileLogPOList = this.baseMapper.selectList(queryWrapper);
        List<FileResp> fileRespList = new ArrayList<>();
        for (FileLogPO fileLogPO : fileLogPOList) {
            FileResp fileResp = CommonDtoConvert.INSTANCE.fileRespConvert(fileLogPO);
            fileRespList.add(fileResp);
        }
        return fileRespList;
    }

    @Override
    public void update(FileReq req) {
        FileLogPO fileLogPO = CommonDtoConvert.INSTANCE.fileLogPOConvert(req);
        this.baseMapper.updateById(fileLogPO);
    }
}
