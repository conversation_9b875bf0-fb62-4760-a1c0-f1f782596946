package com.tiangong.file.service;

import com.tiangong.file.domain.FileLogPO;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface FileService {

    /**
     * 上传文件
     *
     * @param file 多部分文件，要上传的文件对象
     * @return 返回文件响应信息
     * @throws IOException 文件IO异常，当文件读取失败时抛出
     */
    FileResp upload(MultipartFile file) throws IOException;

    /**
     * 上传文件（字节数组方式）
     *
     * @param bytes 文件字节数组，文件的二进制数据
     * @param fileName 文件名称，上传文件的名称
     * @param tableName 表名，关联的数据表名
     * @param fieldName 字段名，关联的字段名
     * @return 返回文件响应信息
     */
    FileResp upload(byte[] bytes, String fileName, String tableName, String fieldName);

    /**
     * 下载文件
     *
     * @param req 文件请求，包含文件下载参数
     * @return 返回文件下载URL或路径
     */
    String download(FileReq req);

    /**
     * 删除文件
     *
     * @param req 文件请求，包含要删除的文件信息
     */
    void del(FileReq req);

    /**
     * 获取文件详情
     *
     * @param fileId 文件ID，用于查询文件详情
     * @return 返回文件日志持久化对象
     */
    FileLogPO detailDO(String fileId);

    /**
     * 批量获取文件列表
     *
     * @param fileIds 文件ID列表，用于批量查询
     * @return 返回文件日志持久化对象列表
     */
    List<FileLogPO> listDO(List<String> fileIds);

    /**
     * 根据对象ID获取文件名称列表
     *
     * @param req 文件请求，包含对象ID查询条件
     * @return 返回文件响应列表
     */
    List<FileResp> listNameByObjId(FileReq req);

    /**
     * 更新文件信息
     *
     * @param req 文件请求，包含要更新的文件信息
     */
    void update(FileReq req);
}
