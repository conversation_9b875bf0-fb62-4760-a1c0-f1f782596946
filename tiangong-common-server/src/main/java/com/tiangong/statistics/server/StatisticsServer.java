package com.tiangong.statistics.server;


import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.statistics.dto.BusinessReturnDTO;
import com.tiangong.statistics.dto.QueryBusinessStatisticsDTO;
import com.tiangong.statistics.service.StatisticsService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@Slf4j
@RequestMapping(value = "/common")
public class StatisticsServer extends BaseController {

    @Autowired
    private StatisticsService statisticsService;
    /**
     * 按月统计
     */
    private final static int STATISTICS_TYPE_MONTH=2;
    /**
     * 离店日期查询
     */
    private final static Integer DATE_QUERY_TYPE_OF_END_TIME=2;
    /**
     * 经营概况 2.0
     */
    @PostMapping(value = "/statistics/queryBusinessStatistics", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<PaginationSupportDTO<BusinessReturnDTO>> queryBusinessStatistics(@RequestBody QueryBusinessStatisticsDTO request) {
        if (request.getDateQueryType() != null && request.getDateQueryType() == 0 && request.getStatisticsType() == 1) {
            request.setStartDate(DateUtilX.stringToDate(DateUtilX.dateToString(request.getStartDate()) + " " + request.getStartTime(), "yyyy-MM-dd HH:mm"));
            request.setEndDate(DateUtilX.stringToDate(DateUtilX.dateToString(request.getEndDate()) + " " + request.getEndTime(), "yyyy-MM-dd HH:mm"));
        }

        if (request.getStatisticsType() == 1 && CollUtilX.isEmpty(request.getWeekList())) {
            return Response.success(null);
        }

        if (request.getStatisticsType() == 0) {
            request.setDateQueryType(2);
            request.setStartDate(DateUtilX.stringToDate(DateUtilX.dateToString(request.getStartDate()) + " 00:00", "yyyy-MM-dd HH:mm"));
            String endDate = DateUtilX.dateToString(request.getEndDate());
            Date date = new Date();
            //当天时间只取已过时间数据
            if (endDate.equals(DateUtilX.dateToString(date))) {
                int hour = date.getHours();
                request.setEndDate(DateUtilX.stringToDate(endDate + " " + hour + ":59", "yyyy-MM-dd HH:mm"));
            } else {
                request.setEndDate(DateUtilX.stringToDate(endDate + " 23:59", "yyyy-MM-dd HH:mm"));
            }
        }
        if (request.getStatisticsType() == STATISTICS_TYPE_MONTH) {
            request.setDateQueryType(DATE_QUERY_TYPE_OF_END_TIME);
        }
        // 统一全部按照下单时间去计算
        request.setDateQueryType(0);

        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(super.getCompanyCode());
        }

        return Response.success(statisticsService.queryOperateStatistics(request));
    }
}
