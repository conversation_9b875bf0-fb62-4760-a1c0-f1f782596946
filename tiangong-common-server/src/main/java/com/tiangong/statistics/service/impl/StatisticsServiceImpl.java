package com.tiangong.statistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.fuzzyquery.dto.FuzzyAgentDTO;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.mapper.FuzzyQueryMapper;
import com.tiangong.statistics.dto.AgentInfoStatisticsDTO;
import com.tiangong.statistics.dto.BusinessReturnDTO;
import com.tiangong.statistics.dto.QueryBusinessStatisticsDTO;
import com.tiangong.statistics.mapper.StatisticsMapper;
import com.tiangong.statistics.service.StatisticsService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Autowired
    private FuzzyQueryMapper fuzzyQueryMapper;

    @Override
    public PaginationSupportDTO<BusinessReturnDTO> queryOperateStatistics(QueryBusinessStatisticsDTO request) {
        List<BusinessReturnDTO> list = statisticsMapper.queryOperateStatistics(request);

        List<String> dateList = null;
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        try {
            if (request.getStatisticsType() == 0) {
                dateList = DateUtilX.findDates("H", startDate, endDate, 1);
            } else if (CollUtilX.isNotEmpty(request.getWeekList()) && request.getStatisticsType() == 1) {
                dateList = DateUtilX.getDayOfWeekWithinDateInterval(DateUtilX.dateToString(startDate), DateUtilX.dateToString(endDate), request.getWeekList());
            } else if (request.getStatisticsType() == 1) {
                dateList = DateUtilX.findDates("D", request.getStartDate(), request.getEndDate(), 1);
            } else {
                dateList = DateUtilX.findDates("M", request.getStartDate(), request.getEndDate(), 1);
            }
            Collections.reverse(dateList);
        } catch (Exception e) {
            log.error("拆分日期异常", e);
            return null;
        }

        //客户列表
        List<FuzzyAgentDTO> agentDTOS = request.getAgentList();
        if (!CollUtilX.isNotEmpty(request.getAgentList())) {
            FuzzyQueryDTO fuzzyQueryDTO = new FuzzyQueryDTO();
            fuzzyQueryDTO.setCompanyCode(request.getCompanyCode());
            agentDTOS = fuzzyQueryMapper.fuzzyQueryAgent(fuzzyQueryDTO);
        }

        //转成map，方便查询
        Map<String, List<AgentInfoStatisticsDTO>> businessMap = new HashMap<>();
        for (BusinessReturnDTO businessReturnDTO : list) {
            businessMap.put(businessReturnDTO.getPeriod(), businessReturnDTO.getAgentStatisticsList());
        }
        List<BusinessReturnDTO> businessReturnList = new ArrayList<>();

        //按时间分页
        List<String> datePageList = startPage(dateList, request.getCurrentPage(), request.getPageSize());

        // 商家下有分销商才处理数据
        if (null != agentDTOS && CollUtilX.isNotEmpty(datePageList)) {
            if (agentDTOS.size() > 1) {
                //多个客户需合计
                for (String date : datePageList) {
                    BusinessReturnDTO businessReturnDTO = new BusinessReturnDTO();
                    //获取每个时间段的信息
                    List<AgentInfoStatisticsDTO> agentStatisticsList = businessMap.get(date);
                    if (agentStatisticsList == null) {
                        //客户无数据，则只需要合计数据
                        agentStatisticsList = new ArrayList<>();
                        AgentInfoStatisticsDTO statisticsDTO = getAgentStatisticsInfo("合计", "");
                        agentStatisticsList.add(statisticsDTO);
                    } else {
                        //存在则合计，并只存有数据的客户
                        Map<String, AgentInfoStatisticsDTO> statisticsMap = new HashMap<>();
                        for (AgentInfoStatisticsDTO statisticsDTO : agentStatisticsList) {
                            statisticsMap.put(statisticsDTO.getAgentCode(), statisticsDTO);
                        }
                        agentStatisticsList = new ArrayList<>();
                        for (FuzzyAgentDTO agentDTO : agentDTOS) {
                            AgentInfoStatisticsDTO statisticsDTO = statisticsMap.get(agentDTO.getAgentCode());
                            if (statisticsDTO == null) {
                                continue;
                            } else {
                                setAgentStatisticsInfo(statisticsDTO);
                            }
                            agentStatisticsList.add(statisticsDTO);
                        }

                        AgentInfoStatisticsDTO agentDTO = getAgentStatisticsInfo("合计", "");
                        for (AgentInfoStatisticsDTO statisticsDTO : agentStatisticsList) {
                            agentDTO.setBaseCurrency(statisticsDTO.getBaseCurrency());
                            agentDTO.setBaseRate(statisticsDTO.getBaseRate());
                            agentDTO.setSaleCurrency(statisticsDTO.getSaleCurrency());
                            agentDTO.setSaleRate(statisticsDTO.getSaleRate());
                            agentDTO.setSales(agentDTO.getSales().add(statisticsDTO.getSales()));
                            agentDTO.setProfit(agentDTO.getProfit().add(statisticsDTO.getProfit()));
                            agentDTO.setOrderQty(agentDTO.getOrderQty() + statisticsDTO.getOrderQty());
                            agentDTO.setNightQty(agentDTO.getNightQty().add(statisticsDTO.getNightQty()));
                            agentDTO.setSupplyOrderAmt(agentDTO.getSupplyOrderAmt().add(statisticsDTO.getSupplyOrderAmt()));
                        }
                        setAgentStatisticsInfo(agentDTO);
                        agentStatisticsList.add(0, agentDTO);

                    }
                    businessReturnDTO.setPeriod(date);
                    businessReturnDTO.setAgentStatisticsList(agentStatisticsList);
                    businessReturnList.add(businessReturnDTO);
                }
            } else {
                //单个客户不用合计
                if (CollUtilX.isNotEmpty(agentDTOS)) {
                    FuzzyAgentDTO agentDTO = agentDTOS.get(0);
                    for (String date : datePageList) {
                        BusinessReturnDTO businessReturnDTO = new BusinessReturnDTO();
                        List<AgentInfoStatisticsDTO> agentStatisticsList = businessMap.get(date);
                        if (agentStatisticsList == null) {
                            agentStatisticsList = new ArrayList<>();
                            AgentInfoStatisticsDTO statisticsDTO = getAgentStatisticsInfo(agentDTO.getAgentName(), agentDTO.getAgentCode());
                            agentStatisticsList.add(statisticsDTO);
                        } else {
                            AgentInfoStatisticsDTO statisticsDTO = agentStatisticsList.get(0);
                            setAgentStatisticsInfo(statisticsDTO);
                        }
                        businessReturnDTO.setPeriod(date);
                        businessReturnDTO.setAgentStatisticsList(agentStatisticsList);
                        businessReturnList.add(businessReturnDTO);
                    }
                }
            }
        }

        log.info("查询结果{}", JSON.toJSONString(businessReturnList));
        PaginationSupportDTO<BusinessReturnDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(businessReturnList);
        paginationSupport.setPageSize(request.getPageSize());
        paginationSupport.setTotalCount(dateList.size());
        paginationSupport.setCurrentPage(request.getCurrentPage());
        return paginationSupport;
    }

    /**
     * 默认数据为空
     */
    private AgentInfoStatisticsDTO getAgentStatisticsInfo(String agentName, String agentCode) {
        AgentInfoStatisticsDTO statisticsDTO = new AgentInfoStatisticsDTO();
        statisticsDTO.setAgentCode(agentCode);
        statisticsDTO.setAgentName(agentName);
        statisticsDTO.setNightQty(new BigDecimal(0));
        statisticsDTO.setOrderQty(0);
        statisticsDTO.setProfitRate("0");
        statisticsDTO.setSales(BigDecimal.ZERO);
        statisticsDTO.setProfit(BigDecimal.ZERO);
        statisticsDTO.setSupplyOrderAmt(BigDecimal.ZERO);
        statisticsDTO.setSaleRate(BigDecimal.ONE);
        statisticsDTO.setSaleCurrency(0);
        statisticsDTO.setBaseRate(BigDecimal.ONE);
        statisticsDTO.setBaseCurrency(0);
        return statisticsDTO;
    }

    /**
     * 转换数据
     */
    private void setAgentStatisticsInfo(AgentInfoStatisticsDTO statisticsDTO) {
        statisticsDTO.setSales(new BigDecimal(statisticsDTO.getSales().intValue()));
        statisticsDTO.setProfit(new BigDecimal(statisticsDTO.getProfit().intValue()));
        statisticsDTO.setSupplyOrderAmt(new BigDecimal(statisticsDTO.getSupplyOrderAmt().intValue()));
        if (statisticsDTO.getSales() == null || statisticsDTO.getSales().compareTo(BigDecimal.ZERO) == 0) {
            statisticsDTO.setSales(new BigDecimal("0"));
            statisticsDTO.setProfitRate("0%");
        } else {
            statisticsDTO.setProfitRate(statisticsDTO.getProfit().divide(statisticsDTO.getSales(),
                    2, RoundingMode.UP).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%");
        }
    }

    /**
     * 对list分页
     */
    private List<String> startPage(List<String> list, Integer pageNum, Integer pageSize) {
        if (list == null) {
            return null;
        }
        if (list.isEmpty()) {
            return null;
        }

        // 记录总数
        Integer count = list.size();
        // 页数
        int pageCount = 0;
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }

        // 开始索引
        int fromIndex = 0;
        // 结束索引
        int toIndex = 0;

        if (!Objects.equals(pageNum, pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }

        return list.subList(fromIndex, toIndex);
    }

}
