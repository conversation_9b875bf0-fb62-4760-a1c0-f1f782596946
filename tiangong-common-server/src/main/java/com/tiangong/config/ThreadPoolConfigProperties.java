package com.tiangong.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * 实时更新nacos配置
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "common.thread")
public class ThreadPoolConfigProperties {

    /**
     * 池中一直保持的线程的数量
     */
    private Integer coreSize;

    /**
     * 池中允许的最大的线程数
     */
    private Integer maxSize;

    /**
     * 当线程数大于核心线程数的时候，线程在最大多长时间没有接到新任务就会终止释放，最终线程池维持在 corePoolSize 大
     */
    private Integer keepAliveTime;

    /**
     * 用来缓冲执行任务的队列
     */
    private Integer queueCapacity;
}
