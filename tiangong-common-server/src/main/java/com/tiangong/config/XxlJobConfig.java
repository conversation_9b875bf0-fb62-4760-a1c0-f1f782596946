package com.tiangong.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@RefreshScope
public class XxlJobConfig {

    /**
     * 调度中心地址[选填]，如调度中心集群部署存在多个地址则用逗号分隔
     * 执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"，为空则关闭自动注册
     */
    @Value("${xxl.job.admin.addresses:}")
    private String adminAddresses;

    /**
     * 执行器通讯[选填]，非空时启用
     */
    @Value("${xxl.job.accessToken:}")
    private String accessToken;

    /**
     * 执行器 [选填]，执行器心跳注册分组依据，为空则关闭自动注册
     */
    @Value("${xxl.job.executor.appname:}")
    private String appname;

    /**
     * 执行器地址
     */
    @Value("${xxl.job.executor.address:}")
    private String address;

    /**
     * 默认为空表示自动获取，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用，地址信息用于 “执行器注册” 和 “调度中心请求并触发任务”
     */
    @Value("${xxl.job.executor.ip:}")
    private String ip;

    /**
     * 执行器端口号 [选填]，小于等于0则自动获取，默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口
     */
    @Value("${xxl.job.executor.port:}")
    private int port;

    /**
     * 执行器运行日志文件存储磁盘路径 [选填] ，需要对该路径拥有读写权限，为空则使用默认路径
     */
    @Value("${xxl.job.executor.logpath:}")
    private String logPath;

    /**
     * 执行器日志保存天数 [选填] ，值大于3时生效，启用执行器Log文件定期清理功能，否则不生效
     */
    @Value("${xxl.job.executor.logretentiondays:}")
    private int logRetentionDays;


    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appname);
        xxlJobSpringExecutor.setAddress(address);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);

        return xxlJobSpringExecutor;
    }

}
