package com.tiangong.config;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;

/**
 * 语言配置类（仅保留语言取值逻辑）
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Configuration
public class LocaleConfig{
    @Bean
    public MessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename("classpath:i18n/messages/messages");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }
    /**
     * 配置LocaleResolver - 使用Header方式
     */
    @Bean
    public LocaleResolver localeResolver() {
        return new HeaderLocaleResolver();
    }
}
