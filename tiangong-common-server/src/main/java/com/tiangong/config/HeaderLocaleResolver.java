package com.tiangong.config;

import com.tiangong.cloud.common.constant.HttpConstant;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * 基于Header的语言解析器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class HeaderLocaleResolver implements LocaleResolver {

    /**
     * 语言前缀常量
     */
    private static final String LANGUAGE_PREFIX_EN = "en";
    private static final String LANGUAGE_PREFIX_ZH = "zh";

    @Override
    public Locale resolveLocale(HttpServletRequest request) {
        // 1. 优先从自定义header获取语言
        String language = request.getHeader(HttpConstant.Language);

        if (StringUtils.hasText(language)) {
            return parseLocale(language);
        }

        // 3. 默认返回中文
        return Locale.SIMPLIFIED_CHINESE;
    }

    @Override
    public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
        // Header方式不支持设置Locale
    }

    /**
     * 解析语言字符串为Locale
     */
    private Locale parseLocale(String language) {
        if (StringUtils.hasText(language)) {
            language = language.toLowerCase().trim();

            // 支持的语言格式：en, en-US, zh, zh-CN, zh_CN
            if (language.startsWith(LANGUAGE_PREFIX_EN)) {
                return Locale.US;
            } else if (language.startsWith(LANGUAGE_PREFIX_ZH)) {
                return Locale.SIMPLIFIED_CHINESE;
            }
        }

        return Locale.SIMPLIFIED_CHINESE;
    }
}
