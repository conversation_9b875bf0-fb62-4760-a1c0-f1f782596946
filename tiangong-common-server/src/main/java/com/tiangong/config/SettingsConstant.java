package com.tiangong.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * @Description: 系统配置类
 * <p>
 * 全局参数配置
 * @RefreshScope 实时更新nacos配置
 */
@Data
@Configuration
@RefreshScope
public class SettingsConstant {
    /**
     * 是否允许同一个账号多地登录：0否 1是
     */
    @Value("${account.token}")
    private int accountToken;
    /**
     * 基础信息主地址
     */
    @Value("${baseInfo.url}")
    public String url;
    /**
     * 每次消费计算热度集团分数酒店数量
     */
    @Value("${consumer.calculateGroupScore.count}")
    private Integer consumerCalculateGroupScoreCount;
    /**
     * 计算热度评分分数值
     */
    @Value("${consumer.calculate.gradeScore}")
    private BigDecimal gradeScore;
    /**
     * 计算热度集团，不属于集团的标识
     */
    @Value("${consumer.calculate.notGroupIds}")
    private String notGroupIds;
    /**
     * 计算热度集团，指定品牌加分的标识
     */
    @Value("${consumer.calculate.brandIds}")
    private String brandIds;

    /**
     * 每次消费计算热度城市平均房价城市数量
     */
    @Value("${consumer.calculateCityAvgPrice.count}")
    private Integer consumerCalculateCityAvgPriceCount;

    /**
     * 每次消费计算热度城市平均房价分数(没有平均价)酒店数量
     */
    @Value("${consumer.calculateCityAvgPriceNotPriceScore.count}")
    private Integer consumerCalculateCityAvgPriceScoreNotPriceCount;

    /**
     * 每次消费计算热度城市平均房价分数(有平均价)城市数量
     */
    @Value("${consumer.calculateCityAvgPriceHavePriceScore.count}")
    private Integer consumerCalculateCityAvgPriceScoreHavePriceCount;

    /**
     * 每批获取的记录数
     */
    @Value("${consumer.batchSize.count}")
    private Integer batchSize;

    /**
     * 每次消费酒店热度同步到mongodb酒店id数量
     */
    @Value("${consumer.hotelScoreHotelId.count}")
    private Integer consumerHotelScoreHotelIdCount;


    /**
     * shub mongodb连接
     */
    @Value("${spring.data.mongodb.uri}")
    private String mongoUri1;

    /**
     * 基础信息mongodb连接
     */
    @Value("${database2.uri}")
    private String mongoUri2;

    @Value("${shub.config.url.domain}")
    private String domainUrl;

    /**
     * 查询酒店列表方式 true  内存排序，false  mongodb数据库排序
     */
    @Value("${searchHotelListMethods:false}")
    private Boolean searchHotelListMethods;
}
