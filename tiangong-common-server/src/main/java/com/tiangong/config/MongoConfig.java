package com.tiangong.config;

import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

@Configuration
public class MongoConfig {

    @Autowired
    private SettingsConstant settingsConstant;

    @Bean(name = "mongoTemplateBasic")
    public MongoTemplate mongoTemplateBasic() {
        MongoDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(MongoClients.create(settingsConstant.getMongoUri2()), "mhotel_basics");
        return new MongoTemplate(factory);
    }

    @Bean(name = "mongoTemplate")
    @Primary
    public MongoTemplate mongoTemplate() {
        MongoDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(MongoClients.create(settingsConstant.getMongoUri1()), "shub");
        return new MongoTemplate(factory);
    }
}

