package com.tiangong.organization.mapper;


import com.tiangong.dto.common.MyMapper;
import com.tiangong.dto.hotel.QueryCityTimeZoneReq;
import com.tiangong.dto.hotel.QueryCityTimeZoneResp;
import com.tiangong.dto.hotel.UpdateTimeZoneDTO;
import com.tiangong.hotel.domain.BaseinfoAreadataEntity;
import com.tiangong.hotel.domain.req.AddOrUpdateHotCityReq;
import com.tiangong.hotel.domain.resp.HotCityMangerResp;
import com.tiangong.hotel.req.QueryAreaDataReq;
import com.tiangong.dto.hotel.HotCityResp;
import com.tiangong.hotel.resp.QueryCityResp;
import com.tiangong.organization.domain.AreaDataPO;
import com.tiangong.organization.remote.dto.AreaDataDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface AreaDataMapper  extends MyMapper<AreaDataPO> {

    /**
     * 查询区域列表
     *
     * @param map 查询参数映射，包含区域查询条件
     * @return 返回区域数据DTO列表
     */
    List<AreaDataDTO> queryAreaData(Map<String, Object> map);

    /**
     * 根据区域类型查询区域集合
     *
     * @param req 查询区域数据请求，包含区域类型等查询条件
     * @param tXTable 表名参数，用于动态表名
     * @return 返回基础信息区域数据实体列表
     */
    List<BaseinfoAreadataEntity> queryAreaDataListByType(@Param("req") QueryAreaDataReq req, @Param("tXTable") String tXTable);

    /**
     * 查询热门城市
     *
     * @param tXTable 表名参数，用于动态表名
     * @return 返回热门城市响应列表
     */
    List<HotCityResp> queryHotCityList(@Param("tXTable") String tXTable);

    /**
     * 查询热门城市管理信息
     *
     * @return 返回热门城市管理响应列表
     */
    List<HotCityMangerResp> queryHotCityListManger();

    /**
     * 修改或新增热门城市
     *
     * @param req 新增或更新热门城市请求，包含城市信息
     * @return 返回影响的行数
     */
    int addOrUpdateHotCity(AddOrUpdateHotCityReq req);

    /**
     * 根据类型删除区域数据
     *
     * @param areaType 区域类型，指定要删除的区域类型
     * @param tXTable 表名参数，用于动态表名
     * @return 返回删除的行数
     */
    Integer delAreaDataByType(@Param("areaType") Integer areaType, @Param("tXTable") String tXTable);

    /**
     * 批量新增区域数据
     *
     * @param list 基础信息区域数据实体列表，要新增的区域数据
     * @param tXTable 表名参数，用于动态表名
     * @return 返回新增的行数
     */
    Integer insertAreaData(@Param("list") List<BaseinfoAreadataEntity> list, @Param("tXTable") String tXTable);

    /**
     * 查询国家下面所有城市数据
     *
     * @param list 城市代码列表，指定要查询的城市
     * @param tXTable 表名参数，用于动态表名
     * @param areaName 区域名称，用于筛选条件
     * @return 返回查询城市响应列表
     */
    List<QueryCityResp> queryCity(@Param("list") List<String> list, @Param("tXTable") String tXTable, @Param("areaName") String areaName);

    /**
     * 更新时区
     *
     * @param updateTimeZoneDTO 时区更新DTO，包含时区更新信息
     * @return 返回更新的行数
     */
    int updateAreaTimeZeroByCityCode(UpdateTimeZoneDTO updateTimeZoneDTO);

    /**
     * 根据城市编码获取城市时区
     *
     * @param req 查询城市时区请求，包含城市编码
     * @return 返回城市时区响应信息
     */
    QueryCityTimeZoneResp queryCityTimeZone(QueryCityTimeZoneReq req);

    /**
     * 查询城市信息
     *
     * @param tXTable 表名参数，用于动态表名
     * @param cityCode 城市编码，用于查询指定城市
     * @return 返回基础信息区域数据实体
     */
    BaseinfoAreadataEntity selectCity(@Param("tXTable") String tXTable, @Param("cityCode") String cityCode);
}
