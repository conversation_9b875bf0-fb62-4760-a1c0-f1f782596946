package com.tiangong.organization.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.organization.domain.AgentCompanyPO;
import com.tiangong.organization.domain.req.AgentCompanyReq;
import com.tiangong.organization.domain.resp.AgentCompanyResp;
import com.tiangong.organization.remote.dto.AgentCreditWarningConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/26 21:36
 **/
@Mapper
public interface AgentCompanyMapper extends MyMapper<AgentCompanyPO> {

    /**
     * 查询需要推送结算成本客户编码
     * 查询所有启用状态且开启结算成本推送功能的客户编码列表
     * @return 客户编码列表，用于推送结算成本信息
     */
    List<String> selectPushSettleCostSwitchAgentCodeList();

    /**
     * 查询客户列表
     * 根据客户编码列表查询对应的客户公司信息
     * @param req 客户公司查询请求对象，包含客户编码列表等查询条件
     * @return 客户公司响应对象列表，包含客户的基本信息和状态
     */
    List<AgentCompanyResp> selectAgentCompanyList(AgentCompanyReq req);

    /**
     * 额度预警配置
     *
     * @param agentCodeList
     * @return
     */
    List<AgentCreditWarningConfig> selectWarningConfigList(@Param("agentCodeList") List<String> agentCodeList);
}
