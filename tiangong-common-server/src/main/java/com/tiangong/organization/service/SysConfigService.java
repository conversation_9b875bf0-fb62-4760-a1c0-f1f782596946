package com.tiangong.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.organization.domain.SysConfigPO;

import java.util.List;

public interface SysConfigService extends IService<SysConfigPO> {

    /**
     * 新增或修改系统配置
     */
    void addOrUpdate(SysConfigPO po);

    /**
     * 查询系统配置
     */
    SysConfigPO queryConfig(String key);

    /**
     * 查询系统配置列表
     */
    List<SysConfigPO> queryConfigList();

    /**
     * 删除系统配置
     */
    void del(int id);
}
