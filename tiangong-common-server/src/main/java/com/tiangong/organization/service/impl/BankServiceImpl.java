package com.tiangong.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.common.Response;
import com.tiangong.common.StrPool;
import com.tiangong.dto.common.BankBalanceChangeReq;
import com.tiangong.dto.common.BankListReq;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.BankLogTypeEnum;
import com.tiangong.enums.DeleteEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.exception.SysException;
import com.tiangong.organization.domain.BankLogPO;
import com.tiangong.organization.domain.BankPO;
import com.tiangong.organization.domain.dto.BankListDTO;
import com.tiangong.organization.domain.req.BankListPageReq;
import com.tiangong.organization.domain.req.BankLogReq;
import com.tiangong.organization.domain.req.ModifiedBankStatusReq;
import com.tiangong.organization.domain.resp.BankLogDTO;
import com.tiangong.organization.domain.resp.BankPageResp;
import com.tiangong.organization.mapper.BankLogMapper;
import com.tiangong.organization.mapper.BankMapper;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.organization.remote.dto.BankAddDTO;
import com.tiangong.organization.remote.dto.BankDetailResp;
import com.tiangong.organization.remote.dto.BankSupplierDTO;
import com.tiangong.organization.remote.dto.QueryBankDetailReq;
import com.tiangong.organization.service.BankService;
import com.tiangong.util.CommonConstants;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/21 21:59
 **/
@Service
@Slf4j
public class BankServiceImpl implements BankService {

    @Autowired
    private BankMapper bankMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Resource
    private BankLogMapper bankLogMapper;

    /**
     * 新增银行卡信息
     */
    @Override
    public void addBank(BankAddDTO bankAddDTO) {
        if (StrUtilX.isEmpty(bankAddDTO.getBankName()) || bankAddDTO.getOrgType() == null ||
                StrUtilX.isEmpty(bankAddDTO.getAccountName()) || StrUtilX.isEmpty(bankAddDTO.getAccountNumber())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (bankAddDTO.getOrgType().equals(CommonConstants.ORG_TYPE_MERCHANT) && bankAddDTO.getBankCurrency() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        BankPO bankPO = new BankPO();
        BeanUtils.copyProperties(bankAddDTO, bankPO);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE_TIME_FORMAT);
        //获取String类型的时间
        String createdate = sdf.format(date);
        bankPO.setCreatedDt(createdate);
        bankPO.setActive(DeleteEnum.STATUS_EXIST.key);
        if (CommonConstants.ORG_TYPE_MERCHANT.equals(bankPO.getOrgType())) {
            bankPO.setOrgCode(CompanyDTO.COMPANY_CODE);
        }
        //验重
        BankPO po = new BankPO();
        po.setOrgType(bankAddDTO.getOrgType());
        po.setAccountNumber(bankAddDTO.getAccountNumber());
        List<BankPO> one = bankMapper.select(po);
        if (one != null && one.size() > 0) {
            throw new SysException(ErrorCodeEnum.BANK_ACCOUNT_NUMBER_IS_ALREADY.errorCode, ErrorCodeEnum.BANK_ACCOUNT_NUMBER_IS_ALREADY.errorDesc);
        }
        bankPO.setBalance(BigDecimal.ZERO);
        bankMapper.insert(bankPO);
    }

    /**
     * 修改银行卡信息
     */
    @Override
    public void modifyBank(BankAddDTO bankAddDTO) {
        if (StrUtilX.isEmpty(bankAddDTO.getBankName()) || bankAddDTO.getBankId() == null ||
                StrUtilX.isEmpty(bankAddDTO.getAccountName()) || StrUtilX.isEmpty(bankAddDTO.getAccountNumber())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 验重
        BankPO po = new BankPO();
        po.setOrgType(bankAddDTO.getOrgType());
        po.setAccountNumber(bankAddDTO.getAccountNumber());
        BankPO one = bankMapper.selectOne(po);
        if (one != null && one.getBankId().intValue() != bankAddDTO.getBankId().intValue()) {
            throw new SysException(ErrorCodeEnum.BANK_ACCOUNT_NUMBER_IS_ALREADY.errorCode, ErrorCodeEnum.BANK_ACCOUNT_NUMBER_IS_ALREADY.errorDesc);
        }
        BankPO bankPO = new BankPO();
        BeanUtils.copyProperties(bankAddDTO, bankPO);
        if (bankAddDTO.getBankCode().contains(StrPool.ASTERISK)) {
            bankPO.setBankCode(null);
        }
        if (bankAddDTO.getAccountNumber().contains(StrPool.ASTERISK)) {
            bankPO.setAccountNumber(null);
        }
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE_TIME_FORMAT);
        // 获取String类型的时间
        String createdate = sdf.format(date);
        bankPO.setUpdatedDt(createdate);
        bankMapper.updateByPrimaryKeySelective(bankPO);
    }

    /**
     * 删除银行卡信息
     */
    @Override
    public void deleteBank(BankAddDTO bankAddDTO) {
        if (bankAddDTO.getBankId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        BankPO po = new BankPO();
        po.setBankId(bankAddDTO.getBankId());
        BankPO bankPO = bankMapper.selectByPrimaryKey(po);
        if (bankPO == null) {
            throw new SysException(ErrorCodeEnum.BANK_IS_NOT_ALREADY.errorCode, ErrorCodeEnum.BANK_IS_NOT_ALREADY.errorDesc);
        }
        // 判断是否是商户银行卡，商户银行卡删除校验：1禁用才可以删除，2无流水才可以删除
        if (CommonConstants.ORG_TYPE_MERCHANT.equals(bankPO.getOrgType())) {
            // 没有禁用不可以删除
            if (bankPO.getActive() == 1) {
                throw new SysException(ErrorCodeEnum.BANK_IS_NOT_DISABLE.errorCode, ErrorCodeEnum.BANK_IS_NOT_DISABLE.errorDesc);
            }
            // 判断改银行卡是否有流水记录
            List<BankLogPO> bankLogPOS = bankLogMapper.selectList(new LambdaQueryWrapper<BankLogPO>().eq(BankLogPO::getBankId, bankPO.getBankId()));
            if (bankLogPOS != null && bankLogPOS.size() > 0) {
                throw new SysException(ErrorCodeEnum.BANK_IS_ANY_BANK_LOG.errorCode, ErrorCodeEnum.BANK_IS_ANY_BANK_LOG.errorDesc);
            }
        }
        bankMapper.deleteByPrimaryKey(bankAddDTO.getBankId());
    }

    @Override
    public Response<List<BankSupplierDTO>> queryBankList(BankListReq req) {
        BankListDTO dto = new BankListDTO();
        dto.setOrgCode(req.getOrgCode());
        if (req.getActive() == null) {
            dto.setActive(DeleteEnum.STATUS_EXIST.key);
        } else {
            dto.setActive(req.getActive());
        }
        if (req.getAccountType() != null) {
            dto.setAccountType(req.getAccountType());
        }
        if (!StringUtils.isEmpty(req.getBankName())) {
            dto.setBankName(req.getBankName());
        }
        if (req.getBankType() != null) {
            dto.setBankType(req.getBankType());
        }
        List<BankSupplierDTO> bankSupplierDTOS = orgMapper.bankList(dto);
        for (BankSupplierDTO bankSupplierDTO : bankSupplierDTOS) {
            String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(bankSupplierDTO.getBankCurrency()));
            bankSupplierDTO.setBankMsg(bankSupplierDTO.getBankName() + " | " +
                    bankSupplierDTO.getAccountName() + " | " +
                    bankSupplierDTO.getAccountNumber() + " | " +
                    currency);

            bankSupplierDTO.setBankCurrencyStr(currency);

        }
        return Response.success(bankSupplierDTOS);
    }

    @Override
    public Response<PaginationSupportDTO<BankPageResp>> queryBankPage(BankListPageReq req) {
        IPage<BankPageResp> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<BankPageResp> bankPageRespIPage = orgMapper.bankPage(page, req);
        PaginationSupportDTO<BankPageResp> dto = new PaginationSupportDTO<>();
        dto = dto.getPaginationSupportDTO(bankPageRespIPage);
        return Response.success(dto);
    }

    @Override
    public Response<PaginationSupportDTO<BankLogDTO>> bankLogPage(BankLogReq req) {
        IPage<BankLogDTO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        if (!StringUtils.isEmpty(req.getChangeEndTime())) {
            req.setChangeEndTime(DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(req.getChangeEndTime()), 1)));
        }
        IPage<BankLogDTO> bankLogPOIPage = orgMapper.bankLogList(page, req);
        PaginationSupportDTO<BankLogDTO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(bankLogPOIPage);
        return Response.success(paginationSupportDTO);
    }

    @Override
    public void bankBalanceChange(BankBalanceChangeReq req) {
        BankSupplierDTO oneBank = orgMapper.getOneBank(req.getOrgCode(), req.getBankId());
        if (oneBank == null) {
            throw new SysException(ErrorCodeEnum.BANK_ID_ERROR.errorCode, ErrorCodeEnum.BANK_ID_ERROR.errorDesc);
        }
        BankLogPO po = new BankLogPO();
        BeanUtils.copyProperties(req, po);

        BigDecimal amount = BigDecimal.ZERO;
        if (BankLogTypeEnum.INCOME.getCode().equals(req.getBankLogType()) ) {
            //收入的时候防止用户输入负数
            if (req.getChangeAmt().compareTo(BigDecimal.ZERO) < 0) {
                req.setChangeAmt(req.getChangeAmt().abs());
            }
            po.setIncome(req.getChangeAmt());
            amount = req.getChangeAmt();
        } else if (BankLogTypeEnum.EXPENDITURE.getCode().equals(req.getBankLogType())) {
            //支出的时候防止用户输入负数
            if (req.getChangeAmt().compareTo(BigDecimal.ZERO) < 0) {
                req.setChangeAmt(req.getChangeAmt().negate());
            }
            po.setExpenditure(req.getChangeAmt());
            amount = req.getChangeAmt().negate();
        } else {
            throw new SysException(ErrorCodeEnum.BANK_LOG_TYPE_ERROR.errorCode, ErrorCodeEnum.BANK_LOG_TYPE_ERROR.errorDesc);
        }
        po.setBalance(CommonTgUtils.formatBigDecimal(oneBank.getBalance()).add(amount));
        boolean updateBankBalance = orgMapper.updateBankBalance(req.getBankId(), amount);
        if (updateBankBalance) {
            po.setCreatedDt(DateUtilX.getDateTimeStr());
            bankLogMapper.insert(po);
        } else {
            throw new SysException(ErrorCodeEnum.BALANCE_CHANGE_ERROR.errorCode, ErrorCodeEnum.BALANCE_CHANGE_ERROR.errorDesc);
        }
    }

    @Override
    public Response<Object> modifiedBankStatus(ModifiedBankStatusReq req) {
        int i = orgMapper.updateBankStatus(req);
        if (i == 0) {
            return Response.error(ErrorCodeEnum.FAIL.errorCode, ErrorCodeEnum.FAIL.errorDesc);
        }
        return Response.success();
    }

    @Override
    public Response<BankDetailResp> bankDetail(QueryBankDetailReq req) {
        Response<BankDetailResp> response = new Response<>();
        try {
            response.setResult(ResultCodeEnum.SUCCESS.code);
            BankDetailResp bankDetailResp = orgMapper.bankDetail(req.getBankId());
            response.setModel(bankDetailResp);
            return response;
        } catch (Exception e) {
            response.setResult(ResultCodeEnum.FAILURE.code);
        }
        return response;
    }
}
