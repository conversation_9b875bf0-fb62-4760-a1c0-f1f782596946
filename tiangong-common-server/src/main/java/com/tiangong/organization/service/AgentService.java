package com.tiangong.organization.service;

import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderAgentDetailResp;
import com.tiangong.organization.remote.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/26 20:19
 **/
public interface AgentService {
    /**
     * 新增客户
     * 创建新的客户账户，包括基本信息、财务配置、API配置等
     * @param agentAddDTO 客户新增请求对象，包含客户的完整信息
     */
    void addAgent(AgentAddReq agentAddDTO);

    /**
     * 新增客户合作商
     * 为现有客户创建合作商关系，用于多级代理模式
     * @param req 合作商新增请求对象，包含合作商信息和关联关系
     */
    void addPartner(AddPartnerReq req);

    /**
     * 修改客户启用状态
     * 启用或禁用指定的客户账户
     * @param agentAddDTO 客户状态更新请求对象，包含客户编码和目标状态
     */
    void modifyAgentStatus(AgentUpdateStatusReq agentAddDTO);

    /**
     * 修改合作商启用状态
     * 启用或禁用指定的合作商账户
     * @param req 合作商状态更新请求对象，包含合作商编码和目标状态
     */
    void modifyPartnerStatus(PartnerStatusReq req);

    /**
     * 修改客户基本信息
     * 更新客户的基本信息，如名称、联系方式等
     * @param agentAddDTO 客户信息更新请求对象，包含要修改的客户信息
     */
    void modifyAgent(AgentUpdateReq agentAddDTO);

    /**
     * 修改客户数学计算配置
     * 更新客户的小数位数和取整方式配置，用于价格计算
     * @param agentAddDTO 客户数学配置更新请求对象，包含小数位和取整方式
     */
    void modifyAgentMath(AgentUpdateMathReq agentAddDTO);

    /**
     * 修改客户API配置信息
     * 更新客户的API相关配置，如回调地址、密钥等
     * @param request 客户API配置更新请求对象，包含API配置信息
     */
    void modifyAgentApiConfig(AgentApiConfigUpdateReq request);

    /**
     * 修改客户API接口调用频次配置
     * 更新客户API接口的调用频次限制配置
     * @param request 客户API调用频次配置请求对象，包含频次限制信息
     */
    void modifyAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request);

    /**
     * 添加客户API接口调用频次配置
     * 为客户新增API接口调用频次限制配置
     * @param request 客户API调用频次配置请求对象，包含频次限制信息
     */
    void addAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request);

    /**
     * 新增或更新客户API接口调用频次配置
     * 如果配置存在则更新，不存在则新增API接口调用频次配置
     * @param request 客户API调用频次配置请求对象，包含频次限制信息
     */
    void addOrEditAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request);

    /**
     * 根据客户编码查询客户详情
     * 查询指定客户的完整详细信息，包括基本信息、财务信息、配置信息等
     * @param agentAddDTO 客户详情查询请求对象，包含客户编码
     * @return 客户详情信息，包含客户的完整信息
     */
    AgentSelectDTO queryAgentDetail(AgentQueryDetailByCodeReq agentAddDTO);

    /**
     * 根据客户编码查询客户基本信息
     * 查询指定客户的基本信息，不包含敏感的财务配置信息
     * @param request 客户基本信息查询请求对象，包含客户编码
     * @return 客户基本信息
     */
    AgentSelectDTO queryAgentBaseInfo(AgentQueryDetailByCodeReq request);

    /**
     * 分页查询客户列表
     * 根据查询条件分页查询客户列表信息
     * @param request 客户列表查询请求对象，包含查询条件和分页参数
     * @return 分页的客户列表数据
     */
    PaginationSupportDTO<QueryAgentListDTO> queryAgentList(AgentListRequest request);

    /**
     * 扣减或退还客户额度
     * 批量处理客户额度的扣减或退还操作，用于订单相关的额度变更
     * @param agentCreditLineList 额度变更列表，包含客户编码、变更金额等信息
     * @return 操作结果，包含成功更新的记录条数
     */
    AgentCreditLineResultDTO deductRefundCreditLine(List<AgentCreditLineDTO> agentCreditLineList);

    /**
     * 修改客户API密钥
     * 更新指定客户的API访问密钥
     * @param orgAgentApiConfigReq API密钥修改请求对象，包含客户编码和新密钥
     */
    void modifySecret(OrgAgentApiSecretReq orgAgentApiConfigReq);

    /**
     * 验证密码获取手机号
     * 通过密码验证获取客户的手机号码信息
     * @param contact 联系人验证请求对象，包含密码等验证信息
     * @return 验证通过后返回的手机号码
     */
    String getPhoneAsPassword(ContactAsPhoneDTO contact);

    /**
     * 修改客户手机号
     * 更新指定客户的联系手机号码
     * @param contact 联系人信息更新请求对象，包含新的手机号码
     */
    void modifyAgentPhone(ContactAsPhoneDTO contact);

    /**
     * 绑定客户个人银行卡
     * 为指定客户添加个人收款银行账户信息
     * @param req 客户银行账户请求对象，包含银行账户信息
     */
    void addAgentBank(AgentBankReq req);

    /**
     * 移除客户个人银行卡
     * 删除指定客户的个人收款银行账户信息
     * @param req 客户银行账户删除请求对象，包含要删除的账户信息
     */
    void delAgentBank(AgentBankIdReq req);

    /**
     * 重置客户管理员密码
     * 重置指定客户账户的管理员登录密码
     * @param agentAddDTO 密码重置请求对象，包含客户编码等信息
     */
    void resetAccountPassword(AgentQueryDetailByCodeReq agentAddDTO);

    /**
     * 查询订单相关的客户信息
     * 根据客户编码查询订单处理所需的客户详细信息
     * @param agentDetailReq 订单客户信息查询请求对象，包含客户编码等查询条件
     * @return 订单客户详情响应对象，包含订单处理所需的客户信息
     */
    Response<QueryOrderAgentDetailResp> queryOrderAgentDetail(QueryOrderAgentDetailReq agentDetailReq);

    /**
     * 初始化推送协议订单结算成本客户编码到缓存任务
     * 将需要推送结算成本的客户编码列表加载到Redis缓存中，用于后续的成本推送任务
     * @param param 参数字符串，可以指定特定的客户编码列表，为空时查询所有符合条件的客户
     */
    void initPushProtocolOrderSettlementCostAgentCodeToRedisTask(String param);

    /**
     * 修改客户附加信息
     * 更新客户的附加配置信息，如特殊标识、备注等
     * @param agentAddDTO 客户附加信息更新请求对象，包含要修改的附加信息
     */
    void modifyAgentAdditionInfo(AgentAdditionInfoReq agentAddDTO);

    /**
     * 加载合作商配置信息到缓存
     * 将指定合作商的配置信息加载到Redis缓存中，提高访问性能
     * @param agentCod 客户编码
     * @param partnerCode 合作商编码
     */
    void loadAgentApiConfigToCache(String partnerCode, String agentCod);

    /**
     * 修改客户优选产品状态
     * 启用或禁用客户的优选产品功能
     * @param request 优选产品状态修改请求对象，包含客户编码和目标状态
     */
    void modifyPreferredProductsStatus(PreferredProductsStatusReq request);

    /**
     * 获取客户配置
     *
     * @param partnerCode 合作编码
     * @return
     */
    AgentConfigDTO getAgentConfig(String partnerCode);

    /**
     * 设置支付超时自动取消配置
     *
     * @param request
     */
    void setPaymentOvertimeCancelConfig(PaymentOvertimeCancelConfigReq request);
}
