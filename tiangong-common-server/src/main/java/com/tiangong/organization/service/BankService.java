package com.tiangong.organization.service;

import com.tiangong.common.Response;
import com.tiangong.dto.common.BankBalanceChangeReq;
import com.tiangong.dto.common.BankListReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.req.BankListPageReq;
import com.tiangong.organization.domain.req.BankLogReq;
import com.tiangong.organization.domain.req.ModifiedBankStatusReq;
import com.tiangong.organization.domain.resp.BankLogDTO;
import com.tiangong.organization.domain.resp.BankPageResp;
import com.tiangong.organization.remote.dto.BankAddDTO;
import com.tiangong.organization.remote.dto.BankDetailResp;
import com.tiangong.organization.remote.dto.BankSupplierDTO;
import com.tiangong.organization.remote.dto.QueryBankDetailReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/21 21:58
 **/
public interface BankService {
    /**
     * 新增银行
     *
     * @param BankAddDTO 银行新增DTO，包含银行卡信息
     */
    void addBank(BankAddDTO BankAddDTO);

    /**
     * 修改银行卡信息
     *
     * @param BankAddDTO 银行新增DTO，包含要修改的银行卡信息
     */
    void modifyBank(BankAddDTO BankAddDTO);

    /**
     * 删除银行卡信息
     *
     * @param BankAddDTO 银行新增DTO，包含要删除的银行卡标识
     */
    void deleteBank(BankAddDTO BankAddDTO);

    /**
     * 银行卡列表信息
     *
     * @param req 银行列表查询请求，包含查询条件
     * @return 返回银行供应商DTO列表的响应结果
     */
    Response<List<BankSupplierDTO>> queryBankList(BankListReq req);

    /**
     * 银行卡分页
     *
     * @param req 银行列表分页请求，包含分页和查询条件
     * @return 返回分页的银行页面响应数据
     */
    Response<PaginationSupportDTO<BankPageResp>> queryBankPage(BankListPageReq req);

    /**
     * 查询银行流水分页
     *
     * @param req 银行日志请求，包含流水查询条件
     * @return 返回分页的银行日志DTO数据
     */
    Response<PaginationSupportDTO<BankLogDTO>> bankLogPage(BankLogReq req);

    /**
     * 银行卡金额变动
     *
     * @param req 银行余额变动请求，包含变动信息
     */
    void bankBalanceChange(BankBalanceChangeReq req);

    /**
     * 修改银行卡状态
     *
     * @param req 修改银行状态请求，包含状态变更信息
     * @return 返回状态修改操作的响应结果
     */
    Response<Object> modifiedBankStatus(ModifiedBankStatusReq req);

    /**
     * 查询银行卡详情
     *
     * @param req 查询银行详情请求，包含银行卡标识
     * @return 返回银行详情响应数据
     */
    Response<BankDetailResp> bankDetail(QueryBankDetailReq req);
}
