package com.tiangong.organization.service;

import com.tiangong.organization.remote.dto.ContactAddDTO;

/**
 * <AUTHOR>
 * @date 2019/6/22 11:43
 **/
public interface ContactService {
    /**
     * 新增联系人
     *
     * @param ContactAddDTO 联系人新增DTO，包含联系人基本信息
     */
    void addContact(ContactAddDTO ContactAddDTO);

    /**
     * 修改联系人信息
     *
     * @param ContactAddDTO 联系人新增DTO，包含要修改的联系人信息
     */
    void modifyContact(ContactAddDTO ContactAddDTO);

    /**
     * 删除联系人信息
     *
     * @param contactAddDTO 联系人新增DTO，包含要删除的联系人标识
     */
    void deleteContact(ContactAddDTO contactAddDTO);
}
