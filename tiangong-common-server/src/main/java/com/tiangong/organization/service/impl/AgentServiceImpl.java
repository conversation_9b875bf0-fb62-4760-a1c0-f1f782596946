package com.tiangong.organization.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.tiangong.ValidatedFilter;
import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.cloud.common.enums.BaseEnum;
import com.tiangong.common.Response;
import com.tiangong.common.StrPool;
import com.tiangong.common.remote.SequenceRemote;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.CustomException;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.AgentCompanyPO;
import com.tiangong.organization.domain.AgentCreditLinePO;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.organization.domain.dto.BankListDTO;
import com.tiangong.organization.domain.dto.UpdateAccountPwdDTO;
import com.tiangong.organization.domain.dto.UserListDTO;
import com.tiangong.organization.domain.entity.OrgAgentApiConfigEntity;
import com.tiangong.organization.domain.entity.OrgAgentApiInvokeConfigEntity;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderAgentDetailResp;
import com.tiangong.organization.mapper.*;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.organization.service.AgentBankService;
import com.tiangong.organization.service.AgentConfirmationLetterConfigService;
import com.tiangong.organization.service.AgentCreditWarningService;
import com.tiangong.organization.service.AgentService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.user.service.UserService;
import com.tiangong.util.*;
import com.tiangong.constant.FieldConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/6/26 20:32
 **/
@Service
@Slf4j
public class AgentServiceImpl implements AgentService {

    /**
     * 随机字符串长度
     */
    private static final int SECRET_KEY_LENGTH = 32;

    /**联系人类型：非业主联系人 */
    private static final int CONTACT_TYPE_NON_OWNER = 0;

    /**联系人类型：业主联系人 */
    private static final int CONTACT_TYPE_OWNER = 1;

    /**
     * 不共享供应商状态
     */
    private static final int NOT_SHARE_SUPPLIER = 0;

    /**
     * 合作商编码分割后的索引位置
     */
    private static final int PARTNER_CODE_INDEX = 1;

    /**
     * 操作失败返回值
     */
    private static final int OPERATION_FAILED = -1;

    /**
     * 结算类型：非预付模式
     */
    private static final int NON_PREPAID_SETTLEMENT_TYPE = 3;

    /**
     * 默认金额字符串
     */
    private static final String DEFAULT_AMOUNT_STRING = "0";

    /**
     * 操作成功返回值（大于0）
     */
    private static final int OPERATION_SUCCESS_THRESHOLD = 0;

    /**
     * 随机字符串字符集
     */
    private static final String RANDOM_CHAR_SET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*";

    /**
     * 数组初始大小
     */
    private static final int ARRAY_INITIAL_SIZE = 0;

    /**
     * 最大等待支付时间（小时）
     */
    private static final double MAX_PAYMENT_WAIT_TIME_HOURS = 24.0;

    /**
     * 时间精度处理阈值
     */
    private static final double TIME_PRECISION_THRESHOLD = 0.5;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private AgentCompanyMapper agentCompanyMapper;

    @Autowired
    private AgentConfirmationLetterConfigService agentConfirmationLetterConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private AgentCreditLineMapper agentCreditLineMapper;

    @Autowired
    private OrgAgentApiConfigMapper orgAgentApiConfigMapper;

    @Autowired
    private OrgAgentApiInvokeConfigMapper orgAgentApiInvokeConfigMapper;

    @Autowired
    private SequenceRemote sequenceRemote;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AgentBankService agentBankService;
    @Autowired
    private AgentCreditWarningService agentCreditWarningService;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;
    /**
     * 新增客户
     */
    @Transient
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAgent(AgentAddReq req) {
        // 校验客户名是否重复
        OrgPO orgPoQuery = new OrgPO();
        orgPoQuery.setType(CommonConstants.ORG_TYPE_AGENT);
        orgPoQuery.setOrgName(req.getAgentName());
        List<OrgPO> org = orgMapper.select(orgPoQuery);
        if (org != null && !org.isEmpty()) {
            throw new SysException(ErrorCodeEnum.AGENT_NAME_IS_ALREADY);
        }

        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE_TIME_FORMAT);
        //获取String类型的时间
        String createdate = sdf.format(date);

        OrgPO orgPo = new OrgPO();
        orgPo.setOrgType(req.getAgentType());
        orgPo.setOrgName(req.getAgentName());
        orgPo.setOrgDomian(req.getOrgDomain());
        orgPo.setCreatedDt(createdate);
        orgPo.setCreatedBy(req.getCreatedBy());
        orgPo.setAvailableStatus(AvailableEnum.Start.key);
        orgPo.setType(OrgEnum.Org_Agent.key);
        orgPo.setChannelCode(req.getChannelCode());
        orgPo.setIsShareSupplier(NOT_SHARE_SUPPLIER);
        orgPo.setOrgUniqueCode(req.getOrgUniqueCode());
        orgPo.setCountryCode(req.getCountryCode());
        orgPo.setCountryName(req.getCountryName());
        orgPo.setProvinceCode(req.getProvinceCode());
        orgPo.setProvinceName(req.getProvinceName());
        orgPo.setCityCode(req.getCityCode());
        orgPo.setCityName(req.getCityName());
        orgPo.setOrgAddress(req.getOrgAddress());
        orgPo.setEstablishedDate(req.getEstablishedDate());

        //获取编码
        String code = getPartnerCode(SystemCodeEnum.CUSTOMERCODE.code);
        orgPo.setOrgCode(code);
        orgMapper.insert(orgPo);

        AgentCompanyPO agentCompanyPo = new AgentCompanyPO();

        UserPO userPo = new UserPO();
        userPo.setUserName(req.getAdminName());
        if (StrUtilX.isNotEmpty(req.getAdminTel())) {
            userPo.setUserTel(req.getAdminTel());
        }
        userPo.setUserAccount(req.getAdminAccount());
        userPo.setUserPwd(req.getAdminPassword());
        userPo.setCreatedBy(req.getCreatedBy());

        userPo.setOrgCode(code);
        int examine = userService.examineUserAccountOrUserTel(userPo);
        if (examine > OPERATION_SUCCESS_THRESHOLD) {
            throw new CustomException(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorNo, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
        }
        int userId = userService.addAdminUser(userPo, EndTypeEnum._3.no);
        if (userId == OPERATION_FAILED) {
            throw new CustomException(ErrorCodeEnum.MANAGE_ALREADY.errorNo, ErrorCodeEnum.MANAGE_ALREADY.errorCode, ErrorCodeEnum.MANAGE_ALREADY.errorDesc);
        }

        agentCompanyPo.setUserName(req.getAdminName());
        if (req.getSettlementType() != NON_PREPAID_SETTLEMENT_TYPE) {
            agentCompanyPo.setBalance(new BigDecimal(req.getCreditLine()));
            agentCompanyPo.setCreditLine(req.getCreditLine());
        }
        agentCompanyPo.setUserTel(req.getAdminTel());
        agentCompanyPo.setUserNumber(req.getAdminAccount());
        agentCompanyPo.setSaleManagerId(userId);
        agentCompanyPo.setSettlementType(req.getSettlementType());
        agentCompanyPo.setSettlementCurrency(req.getSettlementCurrency());
        agentCompanyPo.setCompanyCode(req.getCompanyCode());
        agentCompanyPo.setAvailableStatus(AvailableEnum.Start.key);
        agentCompanyPo.setCreatedDt(createdate);
        agentCompanyPo.setCreatedBy(req.getCreatedBy());
        agentCompanyPo.setOrgId(orgPo.getOrgId());
        agentCompanyPo.setDecimalPlaces(req.getDecimalPlaces());
        agentCompanyPo.setRoundingType(req.getRoundingType());
        agentCompanyPo.setInvoiceId(req.getInvoiceId());
        agentCompanyPo.setSettledType(req.getSettledType());
        agentCompanyPo.setRemark(req.getRemark());
        agentCompanyPo.setDomesticOrOverseas(req.getDomesticOrOverseas());
        agentCompanyPo.setSettlementStrategy(req.getSettlementStrategy());
        agentCompanyPo.setPushSettleCostSwitch(req.getPushSettleCostSwitch());
        agentCompanyPo.setPushSettleCostUrl(req.getPushSettleCostUrl());
        // 设置额度预警相关字段
        agentCompanyPo.setCreditWarningEnabled(req.getCreditWarningEnabled());
        agentCompanyPo.setWarningAmount(req.getWarningAmount());
        agentCompanyPo.setWarningEmails(req.getWarningEmails());
        // 设置额度账号类型
        agentCompanyPo.setLineAccountType(req.getLineAccountType());
        // 设置额度账号
        if (req.getLineAccountType() != null && req.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            agentCompanyPo.setLineAccount(req.getLineAccount());
        } else {
            agentCompanyPo.setLineAccount(code);
        }
        agentCompanyMapper.insert(agentCompanyPo);



        // 加载分销商配置到redis中 AgentAccountConfig
        Map<String, String> accountMap = Maps.newHashMap();
        AgentAccountConfig agentAccountConfig = new AgentAccountConfig();
        agentAccountConfig.setId(agentCompanyPo.getOrgId());
        agentAccountConfig.setAgentName(req.getAgentName());
        agentAccountConfig.setAgentCode(code);
        agentAccountConfig.setChannelName(ChannelEnum.B2B.value);
        agentAccountConfig.setCompanyCode(req.getCompanyCode());
        agentAccountConfig.setAvailableStatus(AvailableEnum.Start.key);
        agentAccountConfig.setLinkmanTel(req.getAgentTel());
        agentAccountConfig.setLinkman(req.getAgentName());
        agentAccountConfig.setSettlementType(req.getSettlementType());
        agentAccountConfig.setBalance(agentCompanyPo.getBalance());
        agentAccountConfig.setSaleManagerId(req.getSaleManagerId());
        agentAccountConfig.setDecimalPlaces(req.getDecimalPlaces());
        agentAccountConfig.setRoundingType(req.getRoundingType());
        agentAccountConfig.setSettlementCurrency(req.getSettlementCurrency());
        agentAccountConfig.setInvoiceId(req.getInvoiceId());
        agentAccountConfig.setSettledType(req.getSettledType());
        agentAccountConfig.setRemark(req.getRemark());
        agentAccountConfig.setDomesticOrOverseas(req.getDomesticOrOverseas());
        agentAccountConfig.setSettlementStrategy(req.getSettlementStrategy());
        agentAccountConfig.setPushSettleCostSwitch(req.getPushSettleCostSwitch());
        agentAccountConfig.setPushSettleCostUrl(req.getPushSettleCostUrl());
        // 设置额度账号类型
        agentAccountConfig.setLineAccountType(req.getLineAccountType());
        // 设置额度账号
        if (req.getLineAccountType() != null && req.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            agentAccountConfig.setLineAccount(req.getLineAccount());
        } else {
            agentAccountConfig.setLineAccount(code);
        }
        accountMap.put(code, JSON.toJSONString(agentAccountConfig));
        RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, accountMap);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加载客户供应商可见性
                loadAgentSupplyAvailable(code);
            }
        });
        //消息预警
        agentCreditWarningService.sendWarningMsg(agentAccountConfig.getAgentCode());
    }

    /**
     * 加载客户供应商可见性
     */
    private void loadAgentSupplyAvailable(String agentCode) {
        CompletableFuture.runAsync(() -> RedisTemplateX.setAdd(RedisKey.RELOAD_AGENT_AVAILABLE_CACHE, agentCode),processingCommonExecutor);
    }

    @Override
    public void addPartner(AddPartnerReq req) {
        OrgAgentApiConfigEntity entity = new OrgAgentApiConfigEntity();
        if (req.getId() == null) {
            // 新增分销商api配置信息
            BeanUtils.copyProperties(req, entity);
            char[] zfc = RANDOM_CHAR_SET.toCharArray();
            String secretKey = RandomStringUtils.random(SECRET_KEY_LENGTH, zfc);
            entity.setSecretKey(secretKey);
            entity.setCreatedDt(new Date());

            //获取编码
            String code = getPartnerCode(SystemCodeEnum.PARTNERCODE.code);
            String[] partnerCodeStr = code.split(SystemCodeEnum.PARTNERCODE.letter);
            //组装合作商编码 TP1--国内  TP2--海外
            String partnerCode = SystemCodeEnum.PARTNERCODE.letter + entity.getDomesticOrOverseas() + partnerCodeStr[PARTNER_CODE_INDEX];
            entity.setPartnerCode(partnerCode);
            //判断该类型的合作商是否创建过了
            OrgAgentApiConfigEntity agentApiConfigEntity = orgAgentApiConfigMapper.selectOne(new LambdaQueryWrapper<OrgAgentApiConfigEntity>()
                    .eq(OrgAgentApiConfigEntity::getAgentCode, entity.getAgentCode())
                    .eq(OrgAgentApiConfigEntity::getDomesticOrOverseas, entity.getDomesticOrOverseas())
            );
            if (agentApiConfigEntity != null) {
                throw new SysException(ErrorCodeEnum.PARTNER_IS_ALREADY);
            }
            entity.setAvailableStatus(AvailableEnum.Start.key);
            orgAgentApiConfigMapper.insert(entity);

            // 加载初始化频率到redis中
            List<OrgAgentApiInvokeConfigResp> orgAgentApiInvokeConfigResps = orgAgentApiInvokeConfigMapper.queryInitInvoke();
            Map<String, String> map = Maps.newHashMap();
            orgAgentApiInvokeConfigResps.forEach(orgAgentApiInvokeConfigResp -> {
                String key = partnerCode.concat(StrPool.UNDERLINE).concat(orgAgentApiInvokeConfigResp.getMethodCode());
                orgAgentApiInvokeConfigResp.setAgentCode(req.getAgentCode());
                map.put(key, JSON.toJSONString(orgAgentApiInvokeConfigResp));
            });
            RedisTemplateX.hPutAll(RedisKey.AGENT_API_CONFIG_REDIS_KEY, map);

        } else {
            entity = orgAgentApiConfigMapper.selectById(req.getId());
            String partnerCode = entity.getPartnerCode();
            BeanUtils.copyProperties(req, entity);
            entity.setPartnerCode(partnerCode);
            entity.setUpdatedDt(new Date());

            orgAgentApiConfigMapper.updateById(entity);
        }
        HashMap<String, String> agentMap = new HashMap<>();
        agentMap.put(entity.getPartnerCode(), JSON.toJSONString(entity));
        RedisTemplateX.hPutAll(RedisKey.PARTNER_ACCOUNT_CONFIG, agentMap);

        // 绑定合作商编码和客户编码
        RedisTemplateX.set(RedisKey.PARTNER_CODE_GET_AGENT_CODE.concat(entity.getPartnerCode()), entity.getAgentCode());
    }

    /**
     * 修改客户api配置信息
     */
    @Override
    public void modifyAgentApiConfig(AgentApiConfigUpdateReq request) {
        OrgAgentApiConfigEntity entity = new OrgAgentApiConfigEntity();
        BeanUtils.copyProperties(request, entity);
        entity.setUpdatedDt(new Date());

        orgAgentApiConfigMapper.updateById(entity);
        OrgAgentApiConfigEntity orgAgentApiConfigEntity = orgAgentApiConfigMapper.selectById(entity.getId());
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, orgAgentApiConfigEntity.getAgentCode()), AgentAccountConfig.class);
        Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
        {
            agentAccount.setOrderStatusPushUrl(entity.getOrderStatusNotifyUrl());
            agentAccount.setOrderCheckDetailNotifyUrl(entity.getOrderCheckDetailNotifyUrl());
            agentAccount.setOrderRefundNotifyUrl(entity.getOrderRefundNotifyUrl());
            agentAccount.setInvoiceNotifyUrl(entity.getInvoiceNotifyUrl());
            agentAccount.setOrderPushNotifyUrl(entity.getOrderPushNotifyUrl());
            agentAccount.setWarrantiesNotifyUrl(entity.getWarrantiesNotifyUrl());
            //agentAccount.setSecretkey(entity.getSecretKey());
            HashMap<String, String> map = new HashMap<>();
            map.put(orgAgentApiConfigEntity.getAgentCode(), JSON.toJSONString(agentAccount));
            RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, map);
        });
    }

    /**
     * 生产唯一合作商编码
     */
    public String getPartnerCode(String codeName) {
        String code = RedisTemplateX.lRightPop(codeName);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put(FieldConstants.SEQ_NAME, codeName);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(codeName);
        }
        return code;
    }

    /**
     * 修改客户启用状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyAgentStatus(AgentUpdateStatusReq req) {
        OrgPO orgPo = new OrgPO();
        UserPO userPo = new UserPO();
        AgentCompanyPO agentCompanyPo = new AgentCompanyPO();
        orgPo.setAvailableStatus(req.getAvailableStatus());
        orgPo.setOrgId(req.getAgentId());
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE_TIME_FORMAT);
        //获取String类型的时间
        String createdate = sdf.format(date);
        orgPo.setUpdatedBy(req.getUpdatedBy());
        orgPo.setUpdatedDt(createdate);
        orgPo.setOrgId(req.getAgentId());
        agentCompanyPo.setUpdatedBy(req.getUpdatedBy());
        agentCompanyPo.setUpdatedDt(createdate);
        agentCompanyPo.setOrgId(req.getAgentId());
        agentCompanyPo.setAvailableStatus(req.getAvailableStatus());
        OrgPO org = orgMapper.selectByPrimaryKey(orgPo);
        Integer userId = orgMapper.getUserId(org.getOrgCode());
        if (userId != null) {
            userPo.setOrgCode(org.getOrgCode());
            userPo.setAvailableStatus(req.getAvailableStatus());
            userPo.setUpdatedBy(req.getUpdatedBy());
            userPo.setUpdatedDt(createdate);
            orgMapper.updateAdminStatus(userPo);

            //判断是否需要删除客户登录token
            if (req.getAvailableStatus().intValue() == AvailableEnum.End.key) {
                //查询改客户下的可登录用户
                List<UserListDTO> userListByOrgCode = orgMapper.getUserListByOrgCode(org.getOrgCode());
                for (UserListDTO dto : userListByOrgCode) {
                    if (dto == null) {
                        break;
                    }
                    String token = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, dto.getUserAccount()));
                    if (StrUtilX.isNotEmpty(token)) {
                        RedisTemplateX.delete(token);
                    }
                }
            }
        }
        orgMapper.updateAgentStatus(agentCompanyPo);
        orgMapper.updateByPrimaryKeySelective(orgPo);

        //客户禁用之后，合作商全部禁用
        if (agentCompanyPo.getAvailableStatus().intValue() == AvailableEnum.End.key.intValue()) {
            PartnerStatusReq statusReq = new PartnerStatusReq();
            statusReq.setAgentCode(req.getAgentCode());
            statusReq.setAvailableStatus(req.getAvailableStatus());
            orgMapper.updateAgentPartnerStatus(statusReq);

            //查询该客户下面的所有合作商，更新他们的缓存状态
            QueryWrapper<OrgAgentApiConfigEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("agent_code", req.getAgentCode());
            wrapper.eq("deleted", BaseEnum.NOT_DELETE.getKey());
            List<OrgAgentApiConfigEntity> list = orgAgentApiConfigMapper.selectList(wrapper);
            for (OrgAgentApiConfigEntity entity : list) {
                OrgAgentApiConfigEntity orgAgentApiConfigEntity = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.PARTNER_ACCOUNT_CONFIG, entity.getPartnerCode()), OrgAgentApiConfigEntity.class);
                Optional.ofNullable(orgAgentApiConfigEntity).ifPresent(apiConfigEntity ->
                {
                    apiConfigEntity.setAvailableStatus(req.getAvailableStatus());
                    HashMap<String, String> agentMap = new HashMap<>();
                    agentMap.put(entity.getPartnerCode(), JSON.toJSONString(apiConfigEntity));
                    RedisTemplateX.hPutAll(RedisKey.PARTNER_ACCOUNT_CONFIG, agentMap);
                });
            }
        }

        // 禁用启用
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, org.getOrgCode()), AgentAccountConfig.class);
        Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
        {
            agentAccountConfig.setAvailableStatus(req.getAvailableStatus());
            RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, org.getOrgCode(), JSON.toJSONString(agentAccountConfig));
        });

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加载客户供应商可见性
                loadAgentSupplyAvailable(req.getAgentCode());
            }
        });
    }

    @Override
    public void modifyPartnerStatus(PartnerStatusReq req) {
        // 修改合作商的启用状态
        orgMapper.updateAgentPartnerStatus(req);

        // 更新缓存状态
        OrgAgentApiConfigEntity orgAgentApiConfigEntity = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.PARTNER_ACCOUNT_CONFIG, req.getPartnerCode()), OrgAgentApiConfigEntity.class);
        Optional.ofNullable(orgAgentApiConfigEntity).ifPresent(apiConfigEntity ->
        {
            apiConfigEntity.setAvailableStatus(req.getAvailableStatus());
            HashMap<String, String> agentMap = new HashMap<>();
            agentMap.put(req.getPartnerCode(), JSON.toJSONString(apiConfigEntity));
            RedisTemplateX.hPutAll(RedisKey.PARTNER_ACCOUNT_CONFIG, agentMap);
        });
    }

    /**
     * 修改客户信息
     */
    @Override
    @Transient
    public void modifyAgent(AgentUpdateReq req) {
        // 校验客户名是否重复
        OrgPO orgPo = new OrgPO();
        orgPo.setType(1);
        orgPo.setOrgName(req.getAgentName());
        List<OrgPO> org = orgMapper.select(orgPo);
        if (org != null) {
            for (OrgPO item : org) {
                if (item.getOrgId().equals(req.getAgentId())) {
                    continue;
                }
                throw new SysException(ErrorCodeEnum.AGENT_NAME_IS_ALREADY);
            }
        }

        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE_TIME_FORMAT);
        //获取String类型的时间
        String createdate = sdf.format(date);

        //if (req.getChannelCode().equals(ChannelEnum.B2B.key) || req.getChannelCode().equals(ChannelEnum.B2C.key)) {
        OrgPO po = new OrgPO();
        po.setOrgName(req.getAgentName());
        po.setOrgId(req.getAgentId());
        po.setUpdatedDt(createdate);
        po.setUpdatedBy(req.getUpdatedBy());
        po.setAvailableStatus(AvailableEnum.Start.key);
        po.setUpdatedDt(createdate);
        po.setUpdatedBy(req.getUpdatedBy());
        po.setChannelCode(req.getChannelCode());
        po.setOrgDomian(req.getOrgDomain());
        po.setOrgUniqueCode(req.getOrgUniqueCode());
        po.setCountryName(req.getCountryName());
        po.setCountryCode(req.getCountryCode());
        //修复#10575 【客户管理-基础信息】所在国家地区字段后端没有返回该字段
        po.setProvinceName(req.getProvinceName());
        po.setProvinceCode(req.getProvinceCode());
        //修复#10575 【客户管理-基础信息】所在国家地区字段后端没有返回该字段
        po.setCityName(req.getCityName());
        po.setCityCode(req.getCityCode());
        po.setOrgAddress(req.getOrgAddress());
        po.setEstablishedDate(DateUtilX.stringToDate(req.getEstablishedDate()));
        orgMapper.updateByPrimaryKeySelective(po);

        if (StrUtilX.isNotEmpty(req.getAdminName()) && StrUtilX.isNotEmpty(req.getAdminAccount())) {
            UserPO userPo = new UserPO();
            userPo.setUserName(req.getAdminName());
            userPo.setUserAccount(req.getAdminAccount());
            userPo.setUserTel(req.getAdminTel());
            userPo.setCreatedBy(req.getCreatedBy());

            OrgPO agent = orgMapper.selectByPrimaryKey(req.getAgentId());
            Integer userId = orgMapper.getUserId(agent.getOrgCode());
            if (userId == null) {
                int examine = userService.examineUserAccountOrUserTel(userPo);
                if (examine > OPERATION_SUCCESS_THRESHOLD) {
                    throw new CustomException(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorNo, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
                }
                userPo.setOrgCode(agent.getOrgCode());
                userPo.setCreatedBy(req.getCreatedBy());
                userService.addAdminUser(userPo, EndTypeEnum._3.no);
            } else {
                userPo.setUpdatedBy(req.getUpdatedBy());
                userPo.setUserId(userId);
                int examine = userService.examineUserAccountOrUserTel(userPo);
                if (examine > OPERATION_SUCCESS_THRESHOLD) {
                    throw new CustomException(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorNo, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
                }
                int modifyAdminUser = userService.modifyAdminUser(userPo);
                if (modifyAdminUser == OPERATION_FAILED) {
                    throw new CustomException(ErrorCodeEnum.UPDATE_MANAGE_MESSAGE_ERROR.errorNo, ErrorCodeEnum.UPDATE_MANAGE_MESSAGE_ERROR.errorCode, ErrorCodeEnum.UPDATE_MANAGE_MESSAGE_ERROR.errorDesc);
                }
            }
        }

        AgentCompanyPO agentCompanyPo = new AgentCompanyPO();
        Integer agentCompany = orgMapper.getAgentCompany(req.getAgentId());
        AgentCompanyPO oldAgentCompany = agentCompanyMapper.selectByPrimaryKey(agentCompany);
        BeanUtils.copyProperties(oldAgentCompany, agentCompanyPo);
        //修改
        agentCompanyPo.setUserTel(req.getAgentTel());
        agentCompanyPo.setUserName(req.getAdminName());
        agentCompanyPo.setUserNumber(req.getAdminAccount());
        agentCompanyPo.setSettlementType(req.getSettlementType());
        agentCompanyPo.setCompanyCode(CompanyDTO.COMPANY_CODE);
        agentCompanyPo.setAvailableStatus(AvailableEnum.Start.key);
        agentCompanyPo.setCreditLine(req.getCreditLine());
        BigDecimal sum = StrUtilX.isNotEmpty(req.getCreditLine()) ? new BigDecimal(req.getCreditLine()):BigDecimal.ZERO;

        sum = sum.subtract(new BigDecimal(oldAgentCompany.getCreditLine() == null ? DEFAULT_AMOUNT_STRING : oldAgentCompany.getCreditLine())).add(oldAgentCompany.getBalance() == null ? BigDecimal.ZERO : oldAgentCompany.getBalance());
        agentCompanyPo.setBalance(sum);
        agentCompanyPo.setACompanyId(agentCompany);
        agentCompanyPo.setInvoiceId(req.getInvoiceId());
        agentCompanyPo.setSettledType(req.getSettledType());
        agentCompanyPo.setRemark(req.getRemark());
        agentCompanyPo.setSettlementStrategy(req.getSettlementStrategy());
        agentCompanyPo.setPushSettleCostSwitch(req.getPushSettleCostSwitch());
        agentCompanyPo.setPushSettleCostUrl(req.getPushSettleCostUrl());
        // 设置额度预警相关字段
        agentCompanyPo.setCreditWarningEnabled(req.getCreditWarningEnabled());
        agentCompanyPo.setWarningAmount(req.getWarningAmount());
        agentCompanyPo.setWarningEmails(req.getWarningEmails());
        // 设置额度账号类型
        agentCompanyPo.setLineAccountType(req.getLineAccountType());
        // 设置额度账号
        if (req.getLineAccountType() != null && req.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            agentCompanyPo.setLineAccount(req.getLineAccount());
        } else {
            agentCompanyPo.setLineAccount(req.getAgentCode());
        }
        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPo);

        // 修改缓存信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            agentAccountConfig = new AgentAccountConfig();
        }
        BeanUtils.copyProperties(agentCompanyPo, agentAccountConfig);
        agentAccountConfig.setLinkmanTel(req.getAgentTel());
        agentAccountConfig.setLinkman(req.getAgentName());
        agentAccountConfig.setAgentName(req.getAgentName());
        RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode(), JSON.toJSONString(agentAccountConfig));
        //消息预警
        agentCreditWarningService.sendWarningMsg(agentAccountConfig.getAgentCode());
    }

    @Override
    public void modifyAgentMath(AgentUpdateMathReq req) {
        Integer agentCompany = orgMapper.getAgentCompany(req.getAgentId());
        AgentCompanyPO agentCompanyPo = new AgentCompanyPO();
        agentCompanyPo.setRoundingType(req.getRoundingType());
        agentCompanyPo.setDecimalPlaces(req.getDecimalPlaces());

        agentCompanyPo.setACompanyId(agentCompany);

        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPo);

        //更新分销商缓存里面的数据
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
        {
            agentAccount.setDecimalPlaces(req.getDecimalPlaces());
            agentAccount.setRoundingType(req.getRoundingType());
            HashMap<String, String> agentMap = new HashMap<>();
            agentMap.put(req.getAgentCode(), JSON.toJSONString(agentAccount));
            RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, agentMap);
        });
    }


    /**
     * 修改客户api接口频次信息
     */
    @Override
    public void modifyAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request) {
        OrgAgentApiInvokeConfigEntity entity = new OrgAgentApiInvokeConfigEntity();
        BeanUtils.copyProperties(request, entity);
        entity.setUpdatedDt(new Date());
        orgAgentApiInvokeConfigMapper.updateById(entity);
        // 加载到缓存中
        cacheAgentApiConfig(entity);
    }

    /**
     * 添加客户api接口频次信息
     */
    @Override
    public void addAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request) {
        OrgAgentApiInvokeConfigEntity entity = new OrgAgentApiInvokeConfigEntity();
        BeanUtils.copyProperties(request, entity);
        entity.setCreatedDt(new Date());
        orgAgentApiInvokeConfigMapper.insert(entity);
        // 加载到缓存中
        cacheAgentApiConfig(entity);
    }

    @Override
    public void addOrEditAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request) {
        if (request.getId() != null) {
            modifyAgentApiInvokeConfig(request);
        } else {
            List<OrgAgentApiInvokeConfigEntity> list = orgAgentApiInvokeConfigMapper.selectList(new LambdaQueryWrapper<OrgAgentApiInvokeConfigEntity>()
                            .eq(OrgAgentApiInvokeConfigEntity::getPartnerCode, request.getPartnerCode())
                            .eq(OrgAgentApiInvokeConfigEntity::getMethodCode, request.getMethodCode())
                            .eq(OrgAgentApiInvokeConfigEntity::getDeleted, 0));
            if (CollUtilX.isNotEmpty(list)) {
                request.setId(list.get(0).getId());
                modifyAgentApiInvokeConfig(request);
            } else {
                addAgentApiInvokeConfig(request);
            }
        }
    }

    /**
     * 根据客户code,查询客户详细信息
     */
    @Override
    public AgentSelectDTO queryAgentDetail(AgentQueryDetailByCodeReq req) {
        AgentSelectDTO agentSelectDTO = orgMapper.agentList(req.getAgentCode());
        if (agentSelectDTO == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 查询确认函配置
        AgentConfirmationLetterConfigResp agentConfirmationLetterConfigResp = agentConfirmationLetterConfigService.getByAgentCode(req.getAgentCode());
        agentSelectDTO.setAgentConfirmationLetterConfig(agentConfirmationLetterConfigResp);


        BankListDTO bankListDTO = new BankListDTO();
        bankListDTO.setOrgCode(req.getAgentCode());
        bankListDTO.setActive(DeleteEnum.STATUS_EXIST.key);
        List<BankSupplierDTO> bankAddDtoList = orgMapper.bankList(bankListDTO);
        List<ContactSupplierDTO> contactAddDtoList = orgMapper.concactList(req.getAgentCode(), DeleteEnum.STATUS_EXIST.key, CONTACT_TYPE_NON_OWNER);
        List<ContactSupplierDTO> ownContactAddDtoList = orgMapper.concactList(req.getAgentCode(), DeleteEnum.STATUS_EXIST.key, CONTACT_TYPE_OWNER);
        //查询分销商api配置信息
        QueryWrapper<OrgAgentApiConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_code", req.getAgentCode());
        wrapper.eq("deleted", BaseEnum.NOT_DELETE.getKey());
        List<OrgAgentApiConfigEntity> list = orgAgentApiConfigMapper.selectList(wrapper);
        if (CollUtilX.isNotEmpty(list)) {
            List<OrgAgentApiConfigListResp> listResp = new ArrayList<>();
            for (OrgAgentApiConfigEntity entity : list) {
                OrgAgentApiConfigListResp configListResp = new OrgAgentApiConfigListResp();
                OrgAgentApiConfigResp orgAgentApiConfigResp = new OrgAgentApiConfigResp();
                BeanUtils.copyProperties(entity, orgAgentApiConfigResp);
                List<OrgAgentApiInvokeConfigResp> orgAgentApiInvokeConfigRespList = orgAgentApiInvokeConfigMapper
                        .queryInvokeByAgentCode(entity.getPartnerCode());

                configListResp.setOrgAgentApiConfig(orgAgentApiConfigResp);
                configListResp.setInvokeList(orgAgentApiInvokeConfigRespList);
                configListResp.setDomesticOrOverseas(entity.getDomesticOrOverseas());
                listResp.add(configListResp);
            }

            //封装分销商配置信息
            agentSelectDTO.setOrgAgentApiConfigList(listResp);
        }

        if (!bankAddDtoList.isEmpty()) {
            agentSelectDTO.setBankCardList(bankAddDtoList);
        }
        if (!contactAddDtoList.isEmpty()) {
            agentSelectDTO.setContactList(contactAddDtoList);
        }
        if (!ownContactAddDtoList.isEmpty()) {
            agentSelectDTO.setOwnContactAddDTOS(ownContactAddDtoList);
        }

        //封装个人信息付款账户
        List<BankAgentListDTO> bankAgentListDtoList = orgMapper.bankListByAgentId(agentSelectDTO.getAgentId());
        agentSelectDTO.setBankAgentListDTOList(bankAgentListDtoList);

        //客户是否开启优选产品
        Object preferredProductsStatusObject = RedisTemplateX.hashGet(RedisKey.AGENT_PREFERRED_PRODUCTS_STATUS, agentSelectDTO.getAgentCode());
        if (Objects.isNull(preferredProductsStatusObject) || String.valueOf(CommonConstants.YES).equals(preferredProductsStatusObject.toString())) {
            //默认开启
            agentSelectDTO.setPreferredProductsStatus(CommonConstants.YES);
        } else {
            agentSelectDTO.setPreferredProductsStatus(CommonConstants.NO);
        }

        // 设置额度账户名称
        if (agentSelectDTO.getLineAccountType() != null && agentSelectDTO.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentSelectDTO.getLineAccount()), AgentAccountConfig.class);
            if (agentAccountConfig != null) {
                agentSelectDTO.setLineAccountName(agentAccountConfig.getAgentName());
                agentSelectDTO.setLineAccountBalance(agentAccountConfig.getBalance());
            }
        } else {
            agentSelectDTO.setLineAccountName(agentSelectDTO.getAgentName());
            if (StrUtilX.isNotEmpty(agentSelectDTO.getBalance())) {
                agentSelectDTO.setLineAccountBalance(new BigDecimal(agentSelectDTO.getBalance()));
            }
        }
        return agentSelectDTO;
    }

    @Override
    public AgentSelectDTO queryAgentBaseInfo(AgentQueryDetailByCodeReq request) {
        return orgMapper.agentList(request.getAgentCode());
    }

    @Override
    public PaginationSupportDTO<QueryAgentListDTO> queryAgentList(AgentListRequest request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<QueryAgentListDTO> list = orgMapper.queryAgentList(request);
        PageInfo<QueryAgentListDTO> page = new PageInfo<>(list);
        PaginationSupportDTO<QueryAgentListDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgentCreditLineResultDTO deductRefundCreditLine(List<AgentCreditLineDTO> agentCreditLineList) {
        int successCount = 0;

        for (AgentCreditLineDTO agentCreditLineDTO : agentCreditLineList) {
            //记录日志
            AgentSelectDTO agentSelectDTO = orgMapper.agentList(agentCreditLineDTO.getAgentCode());
            AgentCreditLinePO agentCreditLinePo = new AgentCreditLinePO();
            agentCreditLinePo.setAgentCode(agentCreditLineDTO.getAgentCode());
            agentCreditLinePo.setOrderCode(agentCreditLineDTO.getOrderCode());
            agentCreditLinePo.setCreatedBy(agentCreditLineDTO.getCreatedBy());
            agentCreditLinePo.setAgentCreditLineId(agentCreditLineDTO.getAgentCreditLineId());
            agentCreditLinePo.setDeductRefundCreditLine(CommonTgUtils.formatBigDecimal(agentCreditLineDTO.getDeductRefundCreditLine()).toString());
            agentCreditLinePo.setCreatedDt(DateUtilX.dateToString(new Date(), CommonConstants.DATE_TIME_FORMAT));
            agentCreditLineMapper.insert(agentCreditLinePo);
            boolean balance = orgMapper.updateBalance(agentSelectDTO.getAgentId(), agentCreditLineDTO.getDeductRefundCreditLine());
            // 统计成功更新的记录数
            if (balance) {
                successCount++;
            }
            // 同时修改缓存配额
            if (balance && agentCreditLineDTO.getDeductRefundCreditLine() != null) {
                String agentCode = agentCreditLineList.get(0).getAgentCode();
                String agent = RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode).toString();
                AgentAccountConfig accountConfig = JSON.parseObject(agent, AgentAccountConfig.class);
                if (accountConfig.getBalance() != null) {
                    accountConfig.setBalance(accountConfig.getBalance().add(agentCreditLineDTO.getDeductRefundCreditLine()));
                    RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode, JSONObject.toJSONString(accountConfig));
                }
            }
        }
        //发送额度预警检查消息
        if(CollUtilX.isNotEmpty(agentCreditLineList)){
            for (String agentCode : agentCreditLineList.stream().map(AgentCreditLineDTO::getAgentCode).filter(StrUtilX::isNotEmpty)
                    .distinct().collect(Collectors.toList())) {
                agentCreditWarningService.sendWarningMsg(agentCode);
            }
        }
        AgentCreditLineResultDTO agentCreditLineResultDTO = new AgentCreditLineResultDTO();
        agentCreditLineResultDTO.setUpdateCount(successCount);
        return agentCreditLineResultDTO;
    }


    /**
     * 缓存客户api频率限制
     */
    private void cacheAgentApiConfig(OrgAgentApiInvokeConfigEntity entity) {
        String key = entity.getPartnerCode().concat(StrPool.UNDERLINE).concat(entity.getMethodCode());
        Map<String, String> map = Maps.newHashMap();
        map.put(key, JSON.toJSONString(entity));
        RedisTemplateX.hPutAll(RedisKey.AGENT_API_CONFIG_REDIS_KEY, map);
    }

    /**
     * 修改API密钥
     */
    @Override
    public void modifySecret(OrgAgentApiSecretReq req) {
        OrgAgentApiConfigEntity orgAgentApiConfig = new OrgAgentApiConfigEntity();
        orgAgentApiConfig.setId(req.getId());
        orgAgentApiConfig.setSecretKey(req.getSecretKey());
        orgAgentApiConfig.setUpdatedBy(req.getUpdatedBy());
        orgAgentApiConfig.setUpdatedDt(new Date());
        orgAgentApiConfigMapper.updateById(orgAgentApiConfig);
    }

    /**
     * 验证密码获取手机号
     */
    @Override
    public String getPhoneAsPassword(ContactAsPhoneDTO contact) {
        Assert.notNull(contact.getPassword(), ErrorCodeEnum.PASSWORD_IS_NOT_EMPTY.errorDesc);

        // 查询用户并验证密码
        UserPO user = userMapper.selectByPrimaryKey(contact.getUserId());
        String encryptUserPassword = SM4Utils.encrypt(contact.getPassword(), Sm4O.defaultKey);
        if (!encryptUserPassword.equals(user.getUserPwd())) {
            throw new SysException(ErrorCodeEnum.PASSWORD_IS_ERROR.errorCode, ErrorCodeEnum.PASSWORD_IS_ERROR.errorDesc);
        }
        AgentCompanyPO companyPo = new AgentCompanyPO();
        companyPo.setOrgId(contact.getOrgId());
        AgentCompanyPO org = agentCompanyMapper.selectOne(companyPo);
        String userTel = null;
        if (org != null && StrUtilX.isNotEmpty(org.getUserTel())) {
            userTel = org.getUserTel();
        }
        return userTel;
    }

    /**
     * 修改客户手机号
     */
    @Override
    public void modifyAgentPhone(ContactAsPhoneDTO contact) {
        // 如果手机号存在需要验证
        if (StrUtilX.isNotEmpty(contact.getTel()) && !Validator.isMobile(contact.getTel())) {
            throw new SysException(ErrorCodeEnum.PLEASE_INPUT_CORRECT_PHONE.errorCode, ErrorCodeEnum.PLEASE_INPUT_CORRECT_PHONE.errorDesc);
        }
        // 判断新旧手机号是否一致
        AgentCompanyPO companyPo = new AgentCompanyPO();
        companyPo.setOrgId(contact.getOrgId());
        AgentCompanyPO select = agentCompanyMapper.selectOne(companyPo);
        if (contact.getTel().equals(select.getUserTel())) {
            throw new SysException(ErrorCodeEnum.OLD_PHONE_IS_SAME.errorCode, ErrorCodeEnum.OLD_PHONE_IS_SAME.errorDesc);
        }

        companyPo.setUserTel(contact.getTel());
        Example example = new Example(AgentCompanyPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", contact.getOrgId());
        int i = agentCompanyMapper.updateByExampleSelective(companyPo, example);
        if (i == OPERATION_SUCCESS_THRESHOLD) {
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
    }

    @Override
    public void addAgentBank(AgentBankReq req) {
        agentBankService.addAgentBank(req);
    }

    @Override
    public void delAgentBank(AgentBankIdReq req) {
        agentBankService.delAgentBank(req);
    }

    @Override
    public void resetAccountPassword(AgentQueryDetailByCodeReq agentAddDTO) {
        UpdateAccountPwdDTO dto = new UpdateAccountPwdDTO();
        String password = SM4Utils.encrypt(CompanyDTO.PWD, Sm4O.defaultKey);
        dto.setUserPwd(password);
        dto.setSignaturePwd(SM3Utils.encrypt(password));
        dto.setOrgCode(agentAddDTO.getAgentCode());
        boolean resetAccountPassword = userMapper.resetAccountPassword(dto);
        if (!resetAccountPassword) {
            throw new SysException(ErrorCodeEnum.RESET_PASSWORD_IS_ERROR.errorCode, ErrorCodeEnum.RESET_PASSWORD_IS_ERROR.errorDesc);
        }
    }

    @Override
    public Response<QueryOrderAgentDetailResp> queryOrderAgentDetail(QueryOrderAgentDetailReq agentDetailReq) {
        QueryOrderAgentDetailResp resp = orgMapper.queryOrderAgentDetail(agentDetailReq);
        if (resp != null) {
            // 设置额度账户名称
            if (resp.getLineAccountType() != null && resp.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
                agentDetailReq.setAgentCode(resp.getLineAccount());
                QueryOrderAgentDetailResp agentDetailResp = orgMapper.queryOrderAgentDetail(agentDetailReq);
                if (agentDetailResp != null) {
                    resp.setLineAccountName(agentDetailResp.getAgentName());
                    resp.setCreditLine(agentDetailResp.getCreditLine());
                    resp.setBalance(agentDetailResp.getBalance());
                }
            } else {
                resp.setLineAccountName(resp.getAgentName());
            }
        }
        return Response.success(resp);
    }

    @Override
    public void initPushProtocolOrderSettlementCostAgentCodeToRedisTask(String param) {
        List<String> agentCodeList;
        if (StrUtilX.isNotEmpty(param)) {
            agentCodeList = StrUtilX.stringToList(param, StrPool.COMMA);
        } else {
            agentCodeList = agentCompanyMapper.selectPushSettleCostSwitchAgentCodeList();
        }
        if (CollUtilX.isNotEmpty(agentCodeList)) {
            String[] agentCodes = agentCodeList.toArray(new String[ARRAY_INITIAL_SIZE]);
            RedisTemplateX.setAdd(RedisKey.PUSH_PROTOCOL_ORDER_SETTLEMENT_COST_AGENT_CODE, agentCodes);
        }
    }

    @Override
    public void modifyAgentAdditionInfo(AgentAdditionInfoReq req) {
        if (req.getAgentId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        if (StrUtilX.isEmpty(req.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        Integer agentCompany = orgMapper.getAgentCompany(req.getAgentId());
        AgentCompanyPO agentCompanyPo = new AgentCompanyPO();
        agentCompanyPo.setACompanyId(agentCompany);
        agentCompanyPo.setPushSettleCostUrl(req.getPushSettleCostUrl());
        agentCompanyPo.setUpdatedBy(req.getUpdatedBy());
        agentCompanyPo.setUpdatedDt(DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format));
        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPo);

        // 修改缓存信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig != null) {
            agentAccountConfig.setPushSettleCostUrl(req.getPushSettleCostUrl());
            RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode(), JSON.toJSONString(agentAccountConfig));
        }
    }

    @Override
    public void loadAgentApiConfigToCache(String partnerCode,String agentCode) {
        if(StrUtil.isNotBlank(partnerCode) && StrUtil.isNotBlank(agentCode)){
            // 加载指定客户APi 配置信息到缓存
            syncAgentApiConfigToCache(partnerCode,agentCode);
        }else {
            // 分页加载所有客户信息到缓存
            int pageNum = 1;
            int pageSize = 100;
            while (true) {
                AgentListRequest agentListRequest = new AgentListRequest();
                agentListRequest.setPageSize(pageSize);
                agentListRequest.setCurrentPage(pageNum);
                agentListRequest.setAvailableStatus(CommonConstants.YES);
                agentListRequest.setCompanyCode(CompanyDTO.COMPANY_CODE);
                PaginationSupportDTO<QueryAgentListDTO> queryAgentListDTOPaginationSupportDto = queryAgentList(agentListRequest);
                // 判断是否最后一页
                if (queryAgentListDTOPaginationSupportDto == null || CollectionUtil.isEmpty(queryAgentListDTOPaginationSupportDto.getItemList())) {
                    return;
                }
                // 循环加载数据到缓存
                for (QueryAgentListDTO queryAgentListDTO : queryAgentListDTOPaginationSupportDto.getItemList()) {
                    syncAgentApiConfigToCache(queryAgentListDTO.getPartnerCode(), queryAgentListDTO.getAgentCode());
                }
                // 判断是否最后一页
                if (queryAgentListDTOPaginationSupportDto.getItemList().size() < pageSize) {
                    return;
                }
                pageNum++;
            }
        }
    }

    @Override
    public void modifyPreferredProductsStatus(PreferredProductsStatusReq request) {
        if (Objects.isNull(request)
                || Objects.isNull(request.getPreferredProductsStatus())
                || (!request.getPreferredProductsStatus().equals(CommonConstants.NO) && !request.getPreferredProductsStatus().equals(CommonConstants.YES))) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
        //校验客户编码是否合法
        OrgPO orgPo = new OrgPO();
        orgPo.setType(CommonConstants.ORG_TYPE_AGENT);
        orgPo.setOrgCode(request.getAgentCode());
        List<OrgPO> org = orgMapper.select(orgPo);
        if (CollectionUtil.isEmpty(org) || org.size() > 1) {
            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID.errorCode, ErrorCodeEnum.AGENT_IS_INVALID.errorDesc);
        }
        RedisTemplateX.hashSet(RedisKey.AGENT_PREFERRED_PRODUCTS_STATUS, request.getAgentCode(), String.valueOf(request.getPreferredProductsStatus()));
    }

    @Override
    public AgentConfigDTO getAgentConfig(String partnerCode) {
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.PARTNER_ACCOUNT_CONFIG, partnerCode), AgentAccountConfig.class);
        if (null == agentAccountConfig){
            return null;
        }
        if (Objects.equals(agentAccountConfig.getAvailableStatus(), CommonConstants.YES)) {
            return CommonDtoConvert.INSTANCE.convert(agentAccountConfig);
        }
        return null;
    }

    private void syncAgentApiConfigToCache(String partnerCode, String agentCode) {
        if(StrUtil.isBlank(partnerCode) || StrUtil.isBlank(agentCode)){
            return;
        }
        // 查询客户配置信息
        List<OrgAgentApiInvokeConfigResp> orgAgentApiInvokeConfigRespList = orgAgentApiInvokeConfigMapper.queryInvokeByAgentCode(partnerCode);
        Map<String, String> map = Maps.newHashMap();
        orgAgentApiInvokeConfigRespList.forEach(orgAgentApiInvokeConfigResp -> {
            String key = partnerCode.concat(StrPool.UNDERLINE).concat(orgAgentApiInvokeConfigResp.getMethodCode());
            orgAgentApiInvokeConfigResp.setAgentCode(agentCode);
            map.put(key, JSON.toJSONString(orgAgentApiInvokeConfigResp));
        });
        // 更新缓存
        RedisTemplateX.hPutAll(RedisKey.AGENT_API_CONFIG_REDIS_KEY, map);
    }

    @Override
    public void setPaymentOvertimeCancelConfig(PaymentOvertimeCancelConfigReq request) {
        //校验客户编码是否合法
        Integer agentCompany = orgMapper.getAgentCompany(request.getAgentId());
        if (Objects.isNull(agentCompany)) {
            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID.errorCode, ErrorCodeEnum.AGENT_IS_INVALID.errorDesc);
        }

        // 处理等待支付时间
        Double waitTime = request.getPaymentOvertimeCancelTime();
        if (waitTime != null) {
            // 超过最大等待时间设置为最大值
            if (waitTime > MAX_PAYMENT_WAIT_TIME_HOURS) {
                waitTime = MAX_PAYMENT_WAIT_TIME_HOURS;
            }

            // 处理时间精度：小数位小于阈值向下取整，大于阈值向上取整，等于阈值保留
            double decimal = waitTime % 1;
            if (decimal > 0 && decimal < TIME_PRECISION_THRESHOLD) {
                waitTime = Math.floor(waitTime);
            } else if (decimal > TIME_PRECISION_THRESHOLD && decimal < 1) {
                waitTime = Math.ceil(waitTime);
            }
        }

        AgentCompanyPO agentCompanyPo = new AgentCompanyPO();
        agentCompanyPo.setACompanyId(agentCompany);
        agentCompanyPo.setPaymentOvertimeCancelSwitch(request.getPaymentOvertimeCancelSwitch());
        agentCompanyPo.setPaymentOvertimeCancelTime(waitTime);
        agentCompanyPo.setUpdatedBy(request.getUpdatedBy());
        agentCompanyPo.setUpdatedDt(DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format));
        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPo);

        // 构建配置对象
        PaymentOvertimeCancelConfigDTO config = new PaymentOvertimeCancelConfigDTO();
        config.setPaymentOvertimeCancelSwitch(request.getPaymentOvertimeCancelSwitch());
        config.setPaymentOvertimeCancelTime(waitTime);

        // 存储到Redis
        RedisTemplateX.hashSet(RedisKey.AGENT_PAYMENT_OVERTIME_CONFIG, request.getAgentCode(), JSONUtil.toJsonStr(config));
    }
}
