package com.tiangong.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.organization.domain.TipsPO;
import com.tiangong.organization.domain.resp.TipsReq;
import com.tiangong.organization.domain.resp.TipsResp;
import com.tiangong.organization.mapper.TipsMapper;
import com.tiangong.organization.service.TipsService;
import com.tiangong.util.StrUtilX;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class TipsServiceImpl extends ServiceImpl<TipsMapper, TipsPO> implements TipsService {

    @Override
    public void addOrUpdate(TipsPO po) {
        if (po.getType() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_TYPE);
        } else if (StrUtilX.isEmpty(po.getSupplierCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
        } else if (StrUtilX.isEmpty(po.getLanguage())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_LANGUAGETYPE);
        }
        TipsPO one = this.getOne(new LambdaQueryWrapper<TipsPO>()
                .eq(TipsPO::getType, po.type)
                .eq(TipsPO::getSupplierCode, po.getSupplierCode())
                .eq(TipsPO::getLanguage, po.getLanguage())
                .last(" limit 1"));
        if (po.getId() == null) {
            if (one != null) {
                throw new SysException(ErrorCodeEnum.TYPE_SUPPLIER_CODE_IS_ALREADY);
            }
            this.save(po);
        } else {
            if (one != null && !Objects.equals(one.getId(), po.getId())) {
                throw new SysException(ErrorCodeEnum.TYPE_SUPPLIER_CODE_IS_ALREADY);
            }
            this.updateById(po);
        }
    }

    @Override
    public PaginationSupportDTO<TipsResp> queryConfigList(TipsReq req) {
        IPage<TipsResp> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<TipsResp> list = this.getBaseMapper().queryTipsList(page);

        PaginationSupportDTO<TipsResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(list);
        return paginationSupportDTO;
    }

    @Override
    public void del(int id) {
        this.removeById(id);
    }
}
