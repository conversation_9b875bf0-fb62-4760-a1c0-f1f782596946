package com.tiangong.organization.service;

import com.tiangong.organization.domain.req.AgentConfirmationLetterConfigAditReq;
import com.tiangong.organization.remote.dto.AgentConfirmationLetterConfigResp;


/**
 * 客户确认函配置服务接口
 */
public interface AgentConfirmationLetterConfigService {

    /**
     * 新增客户确认函配置
     * 为指定客户创建新的确认函配置，包括自动发送设置、语言配置、价格显示等
     * @param req 客户确认函配置请求对象，包含所有配置参数
     * @return 新增配置的ID
     */
    Long confirmationLetterAdd(AgentConfirmationLetterConfigAditReq req);

    /**
     * 修改客户确认函配置
     * 更新指定客户的确认函配置信息
     * @param req 客户确认函配置请求对象，包含要修改的配置参数和ID
     * @return 修改配置的ID
     */
    Long confirmationLetterEdit(AgentConfirmationLetterConfigAditReq req);

    /**
     * 删除客户确认函配置
     * 根据配置ID删除对应的客户确认函配置
     * @param id 配置ID
     */
    void delete(Long id);

    /**
     * 根据客户编码获取确认函配置
     * 查询指定客户的确认函配置信息
     * @param agentCode 客户编码
     * @return 客户确认函配置响应对象，包含完整的配置信息
     */
    AgentConfirmationLetterConfigResp getByAgentCode(String agentCode);


}