package com.tiangong.organization.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.remote.dto.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/28 11:26
 **/
public interface CompanyService {
    /**
     * 新增企业信息
     *
     * @param companyAddDTO 公司新增DTO，包含企业基本信息
     */
    void addCompany(CompanyAddDTO companyAddDTO);

    /**
     * 修改企业信息
     *
     * @param companyAddDTO 公司新增DTO，包含要修改的企业信息
     */
    void modifyCompany(CompanyAddDTO companyAddDTO);

    /**
     * 修改企业启动状态
     *
     * @param companyAddDTO 公司新增DTO，包含状态变更信息
     */
    void modifyCompanyStatus(CompanyAddDTO companyAddDTO);

    /**
     * 根据企业编码查询企业详细信息
     *
     * @param companyAddDTO 公司新增DTO，包含查询条件
     * @return 返回公司选择DTO，包含企业详细信息
     */
    CompanySelectDTO queryCompanyDetail(CompanyAddDTO companyAddDTO);

    /**
     * 运营商列表
     *
     * @param request 公司列表请求，包含查询和分页条件
     * @return 返回分页的查询公司列表DTO数据
     */
    PaginationSupportDTO<QueryCompanyListDTO> queryCompanyList(CompanyListRequest request);

    /**
     * 查询域名是否存在
     *
     * @param companyAddDTO 公司新增DTO，包含域名信息
     * @return 返回是否存在DTO，表示域名是否已存在
     */
    isExitDTO isCompanyExit(CompanyAddDTO companyAddDTO);

    /**
     * 查询机构名称是否已存在
     *
     * @param examineOrgNameDTO 审核机构名称DTO，包含机构名称
     * @return 返回整数值，表示机构名称是否存在
     */
    Integer examineOrgName(ExamineOrgNameDTO examineOrgNameDTO);

    /**
     * 修改企业公章
     *
     * @param request 请求参数映射，包含公章修改信息
     */
    void modifyCompanyOfficialSeal(Map<String, String> request);

    /**
     * 删除企业公章
     *
     * @param request 请求参数映射，包含公章删除信息
     */
    void deleteCompanyOfficialSeal(Map<String, String> request);

    /**
     * 删除企业logo和营业执照
     *
     * @param requestMap 请求参数映射，包含要删除的图片信息
     */
    void deleteOrgPicture(Map<String, String> requestMap);

    /**
     * 上传营业执照
     *
     * @param companyBusinessLicenseUrls 公司营业执照URL DTO列表，包含营业执照信息
     * @param updatedBy 更新人员，记录操作人员
     */
    void uploadCompanyBusinessLicenseUrl(List<CompanyBusinessLicenseUrlDTO> companyBusinessLicenseUrls, String updatedBy);

    /**
     * 上传公司logo
     *
     * @param companyLogoUrlDTO 公司logo URL DTO，包含logo信息
     */
    void uploadCompanyLogoUrl(CompanyLogoUrlDTO companyLogoUrlDTO);

    /**
     * 修改客服电话
     *
     * @param companyAddDTO 公司新增DTO，包含客服电话信息
     */
    void modifyCompanyCustomerTel(CompanyAddDTO companyAddDTO);

    /**
     * 修改企业hr配置信息
     *
     * @param companyAddDTO 公司新增DTO，包含hr配置信息
     */
    void modifyCompanyHrConfig(CompanyAddDTO companyAddDTO);
}
