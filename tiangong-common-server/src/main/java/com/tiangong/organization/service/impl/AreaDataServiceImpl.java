package com.tiangong.organization.service.impl;


import com.tiangong.organization.remote.dto.AreaDataDTO;
import com.tiangong.organization.mapper.AreaDataMapper;
import com.tiangong.organization.service.AreaDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AreaDataServiceImpl implements AreaDataService {

    @Autowired
    private AreaDataMapper areaDataMapper;


    @Override
    public List<AreaDataDTO> queryAreaData(AreaDataDTO areaDataDTO) {
        List<Object> firstLetter = new ArrayList<>();
        if (areaDataDTO.getFirstLetter() != null && !"".equals(areaDataDTO.getFirstLetter())) {
            int a = areaDataDTO.getFirstLetter().length();
            for (int i = 0; i < a; i++) {
                firstLetter.add(areaDataDTO.getFirstLetter().toUpperCase().charAt(i));
            }
        }


        Map<String, Object> params = new HashMap<>();
        params.put("superId", areaDataDTO.getSuperId());
        if (firstLetter.size() > 0) {
            params.put("firstLetter", firstLetter);
        }
        params.put("level", areaDataDTO.getLevel());
        params.put("cityName", areaDataDTO.getCityName());
        return areaDataMapper.queryAreaData(params);
    }
}
