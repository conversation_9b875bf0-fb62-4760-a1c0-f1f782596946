package com.tiangong.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.dto.common.BasePO;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.OrgTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.organization.domain.InvoicePO;
import com.tiangong.organization.domain.req.DelInvoiceReq;
import com.tiangong.organization.domain.req.InvoiceReq;
import com.tiangong.organization.domain.req.QueryInvoiceReq;
import com.tiangong.organization.mapper.InvoiceMapper;
import com.tiangong.organization.remote.dto.InvoiceDTO;
import com.tiangong.organization.service.InvoiceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/12 11:00
 */
@Service
public class InvoiceServiceImpl extends ServiceImpl<InvoiceMapper, InvoicePO> implements InvoiceService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateInvoice(InvoiceReq req) {
        InvoicePO po = new InvoicePO();
        BeanUtils.copyProperties(req, po);
        if (req.getType() == OrgTypeEnum.COMPANY.type) {
            po.setOrgCode(CompanyDTO.COMPANY_CODE);
        }
        InvoicePO one = this.getOne(new LambdaQueryWrapper<InvoicePO>()
                .eq(InvoicePO::getType, req.getType())
                .eq(InvoicePO::getCompanyName, req.getCompanyName())
                .eq(InvoicePO::getOrgCode, req.getOrgCode())
                .last(" limit 1")
        );
        if (req.getId() == null) {
            // 新增
            if (one != null) {
                throw new SysException(ErrorCodeEnum.INVOICE_COMPANY_NAME_IS_ALREADY);
            }
        } else {
            // 修改
            if (one != null && req.getId().intValue() != one.getId().intValue()) {
                throw new SysException(ErrorCodeEnum.INVOICE_COMPANY_NAME_IS_ALREADY);
            }
        }

        this.saveOrUpdate(po);
    }

    @Override
    public PaginationSupportDTO<InvoicePO> invoiceListPage(QueryInvoiceReq req) {
        IPage<InvoicePO> invoicePOIPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        LambdaQueryWrapper<InvoicePO> wrapper = new LambdaQueryWrapper<>();
        if (req.getType() != null) {
            wrapper.eq(InvoicePO::getType, req.getType());
        }
        if (!StringUtils.isEmpty(req.getOrgCode())) {
            wrapper.eq(InvoicePO::getOrgCode, req.getOrgCode());
        }
        if (!StringUtils.isEmpty(req.getCompanyName())) {
            wrapper.like(InvoicePO::getCompanyName, req.getCompanyName());
        }
        wrapper.orderByDesc(BasePO::getCreatedDt);
        IPage<InvoicePO> page = this.getBaseMapper().selectPage(invoicePOIPage, wrapper);
        PaginationSupportDTO<InvoicePO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(page);

        return paginationSupportDTO;
    }

    @Override
    public List<InvoicePO> invoiceList(QueryInvoiceReq req) {
        return this.list(new LambdaQueryWrapper<InvoicePO>().eq(InvoicePO::getType, req.getType()).orderByDesc(BasePO::getCreatedDt));
    }

    @Override
    public void delInvoice(DelInvoiceReq req) {
        this.removeById(req.getId());
    }

    @Override
    public List<InvoicePO> companyInvoiceList(QueryInvoiceReq req) {
        LambdaQueryWrapper<InvoicePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InvoicePO::getType, OrgTypeEnum.COMPANY.type)
                .eq(InvoicePO::getOrgCode, CompanyDTO.COMPANY_CODE);
        return this.list(wrapper);
    }

    @Override
    public InvoiceDTO queryInvoice(Integer invoiceId) {
        InvoiceDTO invoiceDTO = new InvoiceDTO();
        InvoicePO invoicePo = this.getById(invoiceId);
        BeanUtils.copyProperties(invoicePo, invoiceDTO);
        return invoiceDTO;
    }
}
