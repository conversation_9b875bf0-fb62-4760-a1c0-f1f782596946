package com.tiangong.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.organization.domain.AgentBankPO;
import com.tiangong.organization.domain.req.AgentBankIdReq;
import com.tiangong.organization.domain.req.AgentBankReq;
import com.tiangong.organization.mapper.AgentBankMapper;
import com.tiangong.organization.service.AgentBankService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023/12/16 16:42
 */
@Service
public class AgentBankServiceImpl extends ServiceImpl<AgentBankMapper, AgentBankPO> implements AgentBankService {

    @Override
    public void addAgentBank(AgentBankReq req) {
        AgentBankPO one = this.getOne(new LambdaQueryWrapper<AgentBankPO>().eq(AgentBankPO::getAgentId, req.getAgentId()).last(" limit 1"));
        if (one != null) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_ADDED_A_COLLECTION_ACCOUNT);
        }
        one = new AgentBankPO();
        BeanUtils.copyProperties(req, one);
        this.save(one);
    }

    @Override
    public void delAgentBank(AgentBankIdReq req) {
        int delete = this.getBaseMapper().delete(new LambdaQueryWrapper<AgentBankPO>().eq(AgentBankPO::getAgentId, req.getAgentId()));
        if (delete == 0) {
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
    }
}
