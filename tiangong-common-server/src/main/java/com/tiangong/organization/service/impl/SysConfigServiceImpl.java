package com.tiangong.organization.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.organization.domain.SysConfigPO;
import com.tiangong.organization.mapper.SysConfigMapper;
import com.tiangong.organization.service.SysConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfigPO> implements SysConfigService {

    @Override
    public void addOrUpdate(SysConfigPO po) {
        if (po.getId() == null) {
            this.save(po);
        } else {
            this.updateById(po);
        }
    }

    @Override
    public SysConfigPO queryConfig(String key) {
        return this.getOne(new LambdaQueryWrapper<SysConfigPO>().eq(SysConfigPO::getStrKey, key));
    }

    @Override
    public List<SysConfigPO> queryConfigList() {
        return this.list();
    }

    @Override
    public void del(int id) {
        this.removeById(id);
    }
}
