package com.tiangong.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.organization.domain.AgentBankPO;
import com.tiangong.organization.domain.req.AgentBankIdReq;
import com.tiangong.organization.domain.req.AgentBankReq;

/**
 * <AUTHOR>
 * @create 2023/12/16 16:41
 */
public interface AgentBankService extends IService<AgentBankPO> {

    /**
     * 新增客户个人收款账户
     * 为指定客户添加个人收款银行账户信息，每个客户只能添加一个收款账户
     * @param req 客户银行账户请求对象，包含客户ID和银行账户信息
     * @throws SysException 当客户已存在收款账户时抛出异常
     */
    void addAgentBank(AgentBankReq req);

    /**
     * 移除客户个人收款账户
     * 根据客户ID删除对应的个人收款银行账户信息
     * @param req 客户银行账户ID请求对象，包含要删除的客户ID
     * @throws SysException 当删除操作失败时抛出异常
     */
    void delAgentBank(AgentBankIdReq req);
}
