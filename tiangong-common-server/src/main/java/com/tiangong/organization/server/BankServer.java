package com.tiangong.organization.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.BankBalanceChangeReq;
import com.tiangong.dto.common.BankListReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.req.BankListPageReq;
import com.tiangong.organization.domain.req.BankLogReq;
import com.tiangong.organization.domain.req.ModifiedBankStatusReq;
import com.tiangong.organization.domain.resp.BankLogDTO;
import com.tiangong.organization.domain.resp.BankPageResp;
import com.tiangong.organization.remote.dto.BankAddDTO;
import com.tiangong.organization.remote.dto.BankDetailResp;
import com.tiangong.organization.remote.dto.BankSupplierDTO;
import com.tiangong.organization.remote.dto.QueryBankDetailReq;
import com.tiangong.organization.service.BankService;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/21 22:36
 **/
@RestController
@Slf4j
@RequestMapping("/common/bank")
public class BankServer extends BaseController {

    /**
     * 机构类型：公司
     */
    private static final Integer ORG_TYPE_COMPANY = 2;

    @Autowired
    private BankService bankService;

    /**
     * 新增银行卡信息
     */
    @PostMapping("/addBank")
    @PreAuthorize("@syyo.check('bank')")
    public Response<Object> addBank(@RequestBody BankAddDTO request) {
        request.setCreatedBy(getUserName());
        if (request.getOrgType().equals(ORG_TYPE_COMPANY)) {
            request.setOrgCode(getCompanyCode());
        }
        bankService.addBank(request);
        return Response.success();
    }

    /**
     * 修改银行卡信息
     */
    @PostMapping("/modifyBank")
    @PreAuthorize("@syyo.check('bank')")
    public Response<Object> modifyBank(@RequestBody BankAddDTO request) {
        request.setUpdatedBy(getUserName());
        bankService.modifyBank(request);
        return Response.success();
    }

    /**
     * 删除银行卡
     */
    @PostMapping("/deleteBank")
    @PreAuthorize("@syyo.check('bank')")
    public Response<Object> deleteBank(@RequestBody BankAddDTO request) {
        request.setUpdatedBy(getUserName());
        bankService.deleteBank(request);
        return Response.success();
    }

    /**
     * 银行卡列表
     * @param req 银行卡列表查询请求参数
     * @return 银行卡列表响应
     */
    @PostMapping("/queryBankList")
    @AnonymousAccess
    public Response<List<BankSupplierDTO>> queryBankList(@Validated @RequestBody BankListReq req) {
        return bankService.queryBankList(req);
    }

    /**
     * 银行卡分页列表
     */
    @PostMapping("/queryBankPage")
    @PreAuthorize("@syyo.check('bank')")
    public Response<PaginationSupportDTO<BankPageResp>> queryBankPage(@Validated @RequestBody BankListPageReq req) {
        return bankService.queryBankPage(req);
    }

    /**
     * 银行卡流水分页
     */
    @PostMapping("/bankLogPage")
    @PreAuthorize("@syyo.check('bank')")
    public Response<PaginationSupportDTO<BankLogDTO>> bankLogPage(@Validated @RequestBody BankLogReq req) {
        return bankService.bankLogPage(req);
    }

    /**
     * 银行卡余额变动
     */
    @PostMapping("/bankBalanceChange")
    @AnonymousAccess
    public Response<Object> bankBalanceChange(@Validated @RequestBody BankBalanceChangeReq req) {
        if (StrUtilX.isEmpty(req.getCreatedBy())) {
            req.setCreatedBy(getUserName());
        }
        bankService.bankBalanceChange(req);
        return Response.success();
    }

    /**
     * 修改银行卡状态
     */
    @PostMapping("/modifiedBankStatus")
    @PreAuthorize("@syyo.check('bank')")
    public Response<Object> modifiedBankStatus(@Validated @RequestBody ModifiedBankStatusReq req) {
        return bankService.modifiedBankStatus(req);
    }

    /**
     * 查询银行卡信息
     */
    @PostMapping("/bankDetail")
    @AnonymousAccess
    public Response<BankDetailResp> bankDetail(@Validated @RequestBody QueryBankDetailReq req) {
        return bankService.bankDetail(req);
    }
}
