package com.tiangong.organization.server;


import com.tiangong.agent.req.QueryAgentConfirmationLetterConfigReq;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.organization.domain.req.AgentConfirmationLetterConfigAditReq;
import com.tiangong.organization.domain.req.AgentConfirmationLetterConfigDelReq;
import com.tiangong.organization.service.AgentConfirmationLetterConfigService;
import com.tiangong.util.DateUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 客户确认函配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/common/")
public class AgentConfirmationLetterConfigServer extends BaseController {

    @Autowired
    private AgentConfirmationLetterConfigService agentConfirmationLetterConfigService;

    @PostMapping("/confirmationLetterAdit")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Long> confirmationLetterAdd(@RequestBody @Validated AgentConfirmationLetterConfigAditReq req) {
        Long id = null;
        if (req.getId() == null) {
            req.setCreatedBy(getUserName());
            req.setCreatedDt(DateUtilX.getDateTimeStr());
            id = agentConfirmationLetterConfigService.confirmationLetterAdd(req);
        } else {
            req.setUpdatedBy(getUserName());
            req.setUpdatedDt(DateUtilX.getDateTimeStr());
            id = agentConfirmationLetterConfigService.confirmationLetterEdit(req);
        }
        return Response.success(id);
    }


    @PostMapping("/confirmationLetterDel")
    @PreAuthorize("@syyo.check('agent')")
    public Response delete(@RequestBody @Validated AgentConfirmationLetterConfigDelReq req) {
        agentConfirmationLetterConfigService.delete(req.getId());
        return Response.success();
    }

    @PostMapping("/queryConfirmationLetterConfig")
    @AnonymousAccess
    public Response queryConfirmationLetterConfig(@RequestBody @Validated QueryAgentConfirmationLetterConfigReq req) {
        return Response.success(agentConfirmationLetterConfigService.getByAgentCode(req.getAgentCode()));
    }

}