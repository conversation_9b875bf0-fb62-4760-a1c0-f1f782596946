package com.tiangong.organization.server;

import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.common.StrPool;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderAgentDetailResp;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.organization.service.AgentService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/6/26 20:39
 **/
@RestController
@Slf4j
@RequestMapping("/common/agent")
public class AgentServer extends BaseController {

    /**
     * 查询全部状态的标识值
     */
    private static final int QUERY_ALL_STATUS = -1;

    /**
     * 默认机构域名
     */
    private static final String DEFAULT_ORG_DOMAIN = "tiangong";

    @Autowired
    private AgentService agentService;
    @Autowired
    private OrgMapper orgMapper;
    /**
     * 新增客户
     */
    @PostMapping("/addAgent")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> addAgent(@Validated @RequestBody AgentAddReq request) {
        if (StrUtilX.isEmpty(request.getOrgDomain())) {
            request.setOrgDomain(DEFAULT_ORG_DOMAIN);
        }
        request.setCreatedBy(getUserName());
        request.setCompanyCode(getCompanyCode());
        agentService.addAgent(request);
        return Response.success();
    }
    /**
     * 新增合作商
     */
    @PostMapping("/addPartner")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> addPartner(@Validated @RequestBody AddPartnerReq req) {
        req.setCompanyCode(getCompanyCode());
        req.setCreatedBy(getUserName());
        agentService.addPartner(req);
        return Response.success();
    }

    /**
     * 修改客户启用状态
     */
    @PostMapping("/modifyAgentStatus")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyAgentStatus(@Validated @RequestBody AgentUpdateStatusReq request) {
        request.setUpdatedBy(getUserName());
        request.setCompanyCode(getCompanyCode());
        agentService.modifyAgentStatus(request);
        return Response.success();
    }

    /**
     * 修改合作商启用状态
     */
    @PostMapping("/modifyPartnerStatus")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyPartnerStatus(@Validated @RequestBody PartnerStatusReq request) {
        request.setUpdatedBy(getUserName());
        request.setUpdatedDt(DateUtilX.getDateStr());
        agentService.modifyPartnerStatus(request);
        return Response.success();
    }

    /**
     * 修改客户信息
     */
    @PostMapping("/modifyAgent")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyAgent(@Validated @RequestBody AgentUpdateReq request) {
        request.setUpdatedBy(getUserName());
        request.setCreatedBy(getUserName());
        if (StrUtilX.isNotEmpty(request.getAgentTel()) && request.getAgentTel().contains(StrPool.ASTERISK)) {
            request.setAdminTel(null);
        }
        agentService.modifyAgent(request);
        return Response.success();
    }

    /**
     * 修改客户信息
     */
    @PostMapping("/modifyAgentMath")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyAgentMath(@Validated @RequestBody AgentUpdateMathReq request) {
        request.setUpdatedBy(getUserName());
        request.setCreatedBy(getUserName());
        agentService.modifyAgentMath(request);
        return Response.success();
    }

    /**
     * 修改客户api配置信息
     */
    @PostMapping("/modifyAgentApiConfig")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyAgentApiConfig(@Validated @RequestBody AgentApiConfigUpdateReq request) {
        request.setUpdatedBy(getUserName());
        agentService.modifyAgentApiConfig(request);
        return Response.success();
    }

    /**
     * 修改客户api接口频次信息
     */
    @PostMapping("/modifyAgentApiInvokeConfig")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyAgentApiInvokeConfig(@Validated @RequestBody OrgAgentApiInvokeConfigReq request) {
        request.setCreatedBy(getUserName());
        request.setUpdatedBy(getUserName());
        agentService.addOrEditAgentApiInvokeConfig(request);
        return Response.success();
    }

    /**
     * 根据客户Code,查询客户详情
     */
    @PostMapping("/queryAgentDetail")
    @PreAuthorize("@syyo.check('agent')")
    public Response<AgentSelectDTO> queryAgentDetail(@RequestBody AgentQueryDetailByCodeReq request) {
        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(getCompanyCode());
        }
        return Response.success(agentService.queryAgentDetail(request));
    }

    /**
     * 根据客户Code,查询客户基本信息
     */
    @AnonymousAccess
    @PostMapping("/queryAgentBaseInfo")
    public Response<AgentSelectDTO> queryAgentBaseInfo(@RequestBody AgentQueryDetailByCodeReq request) {
        return Response.success(agentService.queryAgentBaseInfo(request));
    }

    /**
     * 查询客户列表
     */
    @PostMapping("/queryAgentList")
    @PreAuthorize("@syyo.check('agent')")
    public Response<PaginationSupportDTO<QueryAgentListDTO>> queryAgentList(@RequestBody AgentListRequest request) {
        request.setCompanyCode(getCompanyCode());
        if (request.getAvailableStatus() != null) {
            if (request.getAvailableStatus() == QUERY_ALL_STATUS) {
                request.setAvailableStatus(null);
            }
        }
        if (request.getAgentType() != null) {
            if (request.getAgentType() == QUERY_ALL_STATUS) {
                request.setAgentType(null);
            }
        }
        return Response.success(agentService.queryAgentList(request));
    }

    /**
     * 扣退额度
     */
    @PostMapping("/modifyDeductRefundCreditLine")
    @AnonymousAccess
    public Response<AgentCreditLineResultDTO> modifyDeductRefundCreditLine(@RequestBody List<AgentCreditLineDTO> agentCreditLineList) {
        AgentCreditLineResultDTO result = agentService.deductRefundCreditLine(agentCreditLineList);
        return Response.success(result);
    }

    /**
     * 修改API密钥
     */
    @PostMapping("/modifySecret")
    @AnonymousAccess
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifySecret(@RequestBody OrgAgentApiSecretReq req) {
        req.setUpdatedBy(super.getUserName());
        agentService.modifySecret(req);
        return Response.success();
    }

    /**
     * 验证密码获取手机号
     */
    @PostMapping(value = "/getPhoneAsPassword", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('contact')")
    public Response<String> getPhoneAsPassword(@RequestBody ContactAsPhoneDTO contact) {
        return Response.success(agentService.getPhoneAsPassword(contact));
    }

    /**
     * 修改客户手机号
     */
    @PostMapping(value = "/modifyAgentPhone", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> modifySupplierPassword(@RequestBody ContactAsPhoneDTO contact) {
        agentService.modifyAgentPhone(contact);
        return Response.success();
    }

    /**
     * 绑定个人账户付款银行卡
     */
    @PostMapping("addAgentBank")
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> addAgentBank(@Validated @RequestBody AgentBankReq req) {
        req.setCreatedBy(getUserName());
        req.setCreatedDt(DateUtilX.getDateStr());
        agentService.addAgentBank(req);
        return Response.success();
    }

    /**
     * 移除个人账户付款银行卡
     */
    @PostMapping("delAgentBank")
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> addAgentdelAgentBankBank(@Validated @RequestBody AgentBankIdReq req) {
        agentService.delAgentBank(req);
        return Response.success();
    }

    /**
     * 重置客户管理员密码
     */
    @PostMapping(value = "/resetAccountPassword", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> resetAccountPassword(@Validated @RequestBody AgentQueryDetailByCodeReq contact) {
        agentService.resetAccountPassword(contact);
        return Response.success();
    }

    /**
     * 查询订单客户信息
     */
    @PostMapping(value = "/queryOrderAgentDetail", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('agent')")
    public Response<QueryOrderAgentDetailResp> queryOrderAgentDetail(@Validated @RequestBody QueryOrderAgentDetailReq agentDetailReq) {
        return agentService.queryOrderAgentDetail(agentDetailReq);
    }

    /**
     * 修改客户附加信息
     */
    @PostMapping("/modifyAgentAdditionInfo")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyAgentAdditionInfo(@RequestBody AgentAdditionInfoReq request) {
        request.setUpdatedBy(getUserName());
        agentService.modifyAgentAdditionInfo(request);
        return Response.success();
    }

    /**
     * 初始化推送协议订单结算成本客户编码到缓存任务
     */
    @AnonymousAccess
    @PostMapping("/initPushProtocolOrderSettlementCostAgentCodeToRedisTask")
    public Response<Object> initPushProtocolOrderSettlementCostAgentCodeToRedisTask() {
        agentService.initPushProtocolOrderSettlementCostAgentCodeToRedisTask(null);
        return Response.success();
    }

    /**
     * 修改客户优选产品状态
     */
    @PostMapping(value = "/modifyPreferredProductsStatus", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> modifyPreferredProductsStatus(@Validated @RequestBody PreferredProductsStatusReq request) {
        agentService.modifyPreferredProductsStatus(request);
        return Response.success();
    }
    /**
     * 根据合作商编码获取合作商账号配置信息
     *
     * @param partnerCode 合作商编码
     * @return 合作商配置信息，包含partnerCode和异步通知地址
     */
    @AnonymousAccess
    @GetMapping("/config/getAgentConfig")
    Response<AgentConfigDTO> getAgentConfig(@RequestParam("partnerCode") String partnerCode){
        return Response.success(agentService.getAgentConfig(partnerCode));
    }

    /**
     * 设置支付超时自动取消配置
     */
    @PostMapping("/setPaymentOvertimeCancelConfig")
    @PreAuthorize("@syyo.check('agent')")
    public Response<Object> setPaymentOvertimeCancelConfig(@Validated @RequestBody PaymentOvertimeCancelConfigReq request) {
        request.setUpdatedBy(getUserName());
        agentService.setPaymentOvertimeCancelConfig(request);
        return Response.success();
    }
    /**
     * 获取供应商列表信息
     * @param orgBaseInfoReq 请求参数
     * @return
     */
    @PostMapping("/listSupplierBaseInfoByAgentCode")
    Response<List<SupplierBaseInfoResp>> listSupplierBaseInfoByAgentCode(@RequestBody SupplierBaseInfoReq orgBaseInfoReq){
        if(CollUtilX.isEmpty(orgBaseInfoReq.getCompanyCode())){
            return Response.success(Collections.emptyList());
        }
        return Response.success(orgMapper.selectSupplierBaseInfo(orgBaseInfoReq.getCompanyCode()));
    }
}
