package com.tiangong.organization.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.organization.remote.dto.ContactAddDTO;
import com.tiangong.organization.service.ContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/6/22 12:08
 **/
@RestController
@Slf4j
@RequestMapping("/common/contact")
public class ContactServer extends BaseController {

    @Autowired
    private ContactService contactService;

    /**
     * 新增联系人信息
     */
    @PostMapping("/addContact")
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> addContact(@RequestBody ContactAddDTO request) {
        request.setCreatedBy(getUserName());
        contactService.addContact(request);
        return Response.success();
    }

    /**
     * 修改联系人信息
     */
    @PostMapping("/modifyContact")
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> updateContact(@RequestBody ContactAddDTO request) {
        request.setUpdatedBy(getUserName());
        contactService.modifyContact(request);
        return Response.success();
    }

    /**
     * 删除联系人信息
     */
    @PostMapping("/deleteContact")
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> deleteContact(@RequestBody ContactAddDTO request) {
        request.setUpdatedBy(getUserName());
        contactService.deleteContact(request);
        return Response.success();
    }
}
