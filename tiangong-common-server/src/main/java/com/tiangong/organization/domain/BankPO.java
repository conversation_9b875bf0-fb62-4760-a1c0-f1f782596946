package com.tiangong.organization.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/6/19 11:05
 **/
@Data
@Table(name = "t_org_bank")
public class BankPO extends BasePO {
    /**
     * 银行信息Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer bankId;
    /**
     * 银行卡名称
     */
    @Column(name = "bank_name")
    private String bankName;
    /**
     * 开户名
     */
    @Column(name = "account_name")
    private String accountName;
    /**
     * 账号
     */
    @Column(name = "account_number")
    private String accountNumber;
    /**
     * 行号
     */
    @Column(name = "bank_code")
    private String bankCode;

    /**
     * 银行卡状态
     */
    @Column(name = "active")
    private Integer active;


    /**
     * 机构Id
     */
    @Column(name = "org_code")
    private String orgCode;
    /**
     * 机构类型
     */
    @Column(name = "org_type")
    private Integer orgType;

    /**
     * 账号类型：1企业账号 2个人账号
     */
    @Column(name = "account_type")
    private Integer accountType;

    /**
     * 银行币种
     */
    @Column(name = "bank_currency")
    private Integer bankCurrency;

    /**
     * 公司发票主体id（t_invoice表）
     */
    @Column(name = "company_invoice_id")
    private Integer companyInvoiceId;

    /**
     * 发票类型：1普票2专票
     */
    @Column(name = "invoice_type")
    private Integer invoiceType;

    /**
     * 发票税点
     */
    @Column(name = "invoice_ratio")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private BigDecimal invoiceRatio;

    /**
     * 余额
     */
    @Column(name = "balance")
    private BigDecimal balance;

    /**
     * 开户行类型：0网商银行 1其他
     */
    @Column(name = "bank_type")
    private Integer bankType;

}
