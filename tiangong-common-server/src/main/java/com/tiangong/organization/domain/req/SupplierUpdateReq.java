package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import com.tiangong.enums.SupplierFormEnum;
import com.tiangong.enums.SupplyOneLevelChannelTypeEnum;
import com.tiangong.enums.SupplyTwoLevelChannelTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/10/21 17:58
 */
@Data
public class SupplierUpdateReq extends BasePO {
    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    @NotNull(message = "EMPTY_PARAM_ADJUSTMENTTYPE")
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    @NotNull(message = "EMPTY_PARAM_MODIFIEDAMT")
    private BigDecimal modifiedAmt;

    /**
     * 保留的小数位
     */
    @NotNull(message = "EMPTY_PARAM_DECIMALPLACES")
    private Integer decimalPlaces;

    /**
     * 发票类型：1普票 2专票
     */
    @NotNull(message = "EMPTY_PARAM_INVOICETYPE")
    private Integer invoiceType;

    /**
     * 发票模式: 1商家开票 2酒店前台开票
     */
    @NotNull(message = "EMPTY_PARAM_INVOICEMODEL")
    private Integer invoiceModel;

    /**
     * 是否不落地 1不落地 2落地
     */
    @NotNull(message = "EMPTY_PARAM_ISCACHED")
    private Integer isCached;

    /**
     * 供应商类型 0-自签 1-酒店 2-API
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLYTYPE")
    private Integer supplierType;

    /**
     * 结算方式
     */
    @NotNull(message = "EMPTY_PARAM_SETTLEMENTTYPE")
    private Integer settlementType;


    /**
     * 供应商企业名称
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLIERNAME")
    private String supplierName;

    /**
     * 供应商Id
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLIERID")
    private Integer supplierId;
    /**
     * 总管理员姓名
     */
    private String adminName;
    /**
     * 总管理员账号
     */
    private String adminAccount;
    /**
     * 总管理员手机号
     */
    private String adminTel;
    /**
     * 我司采购经理Id
     */
    private Integer purchaseManagerId;

    /**
     * 公章id
     */
    private Integer officialSealId;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 是否支持下凌晨房订单
     * 0:否
     * 1:是
     */
    private Integer isSupportEarlyMorningRoomOrder;

    /**
     * 域名
     */
    private String orgDomain;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 供应商编码
     */
    private String supplierCode;


    /**
     * 供应商分类：1单体酒店，2集团酒店，3OTA,4批发商，5房券， 6其他
     * @see SupplierFormEnum
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLIERFORM")
    private Integer supplierForm;

    /**
     * 快速处理标签开关
     */
    @NotNull(message = "EMPTY_PARAM_QUICKPROCESSINGSWITCH")
    private Integer quickProcessingSwitch;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 集团id
     */
    private Integer groupId;

    /**
     * 集团名称
     */
    private String groupName;

    /**
     * 是否同步入住明细：0否 1是
     */
    private Integer isSyncCheckDetail;

    /**
     * 我方合作主题
     */
    @NotNull(message = "EMPTY_PARAM_INVOICEID")
    private Integer invoiceId;

    /**
     * 是否协议托管：0否 1是
     */
    @NotNull(message = "EMPTY_PARAM_ENABLE_PROTOCOL")
    private Integer enableProtocol;

    /**
     * 是否待结算
     */
    private Integer enableSettled;

    /**
     * 描述
     */
    private String remark;

    /**
     * 一级渠道类型
     * @see SupplyOneLevelChannelTypeEnum
     */
    private Integer oneLevelChannelType;

    /**
     * 二级渠道类型
     * @see SupplyTwoLevelChannelTypeEnum
     */
    private Integer twoLevelChannelType;

    /**
     * 三级渠道类型
     */
    private String threeLevelChannelType;
}
