package com.tiangong.organization.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/12 11:04
 */
@Data
public class QueryInvoiceReq extends BasePage {

    @NotNull(message = "EMPTY_PARAM_TYPE")
    private Integer type;

    private String orgCode;

    /**
     * 公司名称
     */
    private String companyName;
}
