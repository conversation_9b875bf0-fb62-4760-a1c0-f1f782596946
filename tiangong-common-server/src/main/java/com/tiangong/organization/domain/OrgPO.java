package com.tiangong.organization.domain;

import com.tiangong.dto.common.BasePO;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/6/18 21:02
 **/
@Data
@SensitiveClass
@Table(name = "t_org_organization")
public class OrgPO extends BasePO {
    /**
     * 机构Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer orgId;
    /**
     * 机构名称
     */
    @Column(name = "org_name")
    private String orgName;
    /**
     * 机构编码
     */
    @Column(name = "org_code")
    private String orgCode;
    /**
     * 机构类型
     * 1-包房商，2-个人
     */
    @Column(name = "org_type")
    private Integer orgType;
    /**
     * 企业域名
     */
    @Column(name = "org_domian")
    private String orgDomian;
    /**
     * 企业电话
     */
    @EncryptField
    @Column(name = "org_tel")
    private String orgTel;
    /**
     * 企业地址
     */
    @Column(name = "org_address")
    private String orgAddress;

    /**
     * 企业成立日期
     */
    @Column(name = "established_date")
    private Date establishedDate;
    /**
     * 企业简介
     */
    @Column(name = "org_summary")
    private String orgSummary;
    /**
     * /**
     * 数据类型
     * 0-供应商，1-客户 2-运营商
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 启用状态
     */
    @Column(name = "available_status")
    private Integer availableStatus;
    /**
     * 酒店信息权限
     */
    @Column(name = "hotel_info_permissions")
    private Integer hotelInfoPermissions;

    /**
     * 公章url
     */
    private Integer officialSealId;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 是否为共享供应商(0 否, 1 是)
     */
    private Integer isShareSupplier;

    /**
     * 是否支持下凌晨房订单
     * 0:否
     * 1:是
     */
    @Column(name = "is_support_early_morning_room_order")
    private Integer isSupportEarlyMorningRoomOrder;

    /**
     * 企业唯一码
     */
    @Column(name = "org_unique_code")
    private String orgUniqueCode;

    /**
     * 国家编码
     */
    @Column(name = "country_code")
    private String countryCode;

    /**
     * 国家名称
     */
    @Column(name = "country_name")
    private String countryName;


    /**
     * 省份编码
     */
    @Column(name = "province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @Column(name = "province_name")
    private String provinceName;


    /**
     * 城市编码
     */
    @Column(name = "city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @Column(name = "city_name")
    private String cityName;

    /**
     * 客服电话
     */
    @Column(name = "customer_tel")
    private String customerTel;

    /**
     * 商户币种
     */
    @Column(name = "org_currency")
    private String orgCurrency;

    /**
     * 是否开启hr对接配置 0-否 1-是
     */
    @Column(name = "hr_is_open")
    private Integer hrIsOpen;

    /**
     * hr对接地址
     */
    @Column(name = "hr_url")
    private String hrUrl;

    /**
     * hr系统编码
     */
    @Column(name = "hr_system_code")
    private String hrSystemCode;

    /**
     * hr系统私钥
     */
    @Column(name = "hr_private_key")
    private String hrPrivateKey;

    /**
     * hr系统公钥
     */
    @Column(name = "hr_public_key")
    private String hrPublicKey;

    /**
     * hr回调地址
     */
    @Column(name = "hr_callback_url")
    private String hrCallbackUrl;

    /**
     * 机构简码
     */
    @Column(name = "org_brevity_code")
    private String orgBrevityCode;
}
