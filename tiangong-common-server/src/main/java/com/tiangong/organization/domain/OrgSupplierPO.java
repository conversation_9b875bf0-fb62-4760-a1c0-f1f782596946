package com.tiangong.organization.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 **/
@Data
public class OrgSupplierPO {

    /**
     * 机构Id
     */
    private Integer orgId;

    /**
     * 企业名称
     */
    private String supplierName;

    /**
     * 企业编码
     */
    private String supplierCode;

    /**
     * 我司采购经理Id
     */
    private Integer purchaseManagerId;

    /**
     * 结算方式
     */
    private Integer settlementType;

    /**
     * 是否支持下凌晨房订单
     * 0:否
     * 1:是
     */
    private Integer isSupportEarlyMorningRoomOrder;

    /**
     * 结算币种类型
     */
    private Integer settlementCurrency;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    private BigDecimal modifiedAmt;

    /**
     * 发票类型：1普票 2专票
     */
    private Integer invoiceType;

    /**
     * 发票模式: 1商家开票 2酒店前台开票
     */
    private Integer invoiceModel;

    /**
     * 是否不落地 1不落地 2落地
     */
    private Integer isCached;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    private Integer availableStatus;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

    /**
     * 是否同步入住明细：0否 1是
     */
    private Integer isSyncCheckDetail;

    /**
     * 供应商类型
     * @see com.tiangong.enums.SupplierFormEnum
     */
    private Integer supplierForm;

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 供应商类型 0-自签 1-酒店 2-API
     */
    private Integer supplierType;
}
