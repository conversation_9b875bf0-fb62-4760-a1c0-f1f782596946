package com.tiangong.organization.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

/**
 * 客户确认函配置表
 */
@Data
@TableName("t_org_agent_confirmation_letter_config")
public class AgentConfirmationLetterConfigPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 自动发送 （"0"：不自动，"1"：自动）
     */
    private Integer auotoFlag;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 密送邮箱
     */
    private String blindCarbonCopy;

    /**
     * 抄送邮箱
     */
    private String carbonCopy;

    /**
     * 价格显示 ("0"："展示"，"1":不展示)
     */
    private Integer priceShow;

    /**
     * 确认函语言 ["zh-CN":"中文","en-US":"英文","zh-CN_en-US":"中英双语"]
     */
    private String language;

    /**
     * 附件携带 （"0":  不携带，"1"：携带）
     */
    private Integer annexCarry;

    /**
     * 客户专属客户电话
     */
    private String agentPhone;

    /**
     * logo 地址
     */
    private String logoUrl;

    /**
     * 删除状态 0 无效，1 有效
     */
    private Integer deleted;
}