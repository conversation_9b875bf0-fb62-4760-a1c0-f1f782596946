package com.tiangong.organization.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/18 11:27
 */
@Data
public class QuerySupplierLabelConfigReq extends BasePage {

    /**
     * 客户id
     */
    @NotNull(message = "EMPTY_PARAM_AGENTID")
    private Integer agentId;

    /**
     * 业务类型
     */
    private Integer serviceType;

    /**
     * 标签类型
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer labelType;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;
}
