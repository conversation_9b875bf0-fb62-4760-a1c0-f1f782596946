package com.tiangong.hotel.task;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.tiangong.dto.common.HotelAndAgentCodeLabelDTO;
import com.tiangong.dto.common.HotelPublicDTO;
import com.tiangong.hotel.domain.vo.HotelLabelAllDTO;
import com.tiangong.hotel.mapper.HotelRecommendMapper;
import com.tiangong.hotel.service.HotelService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 同步酒店标签到mongodb公共表任务
 */
@Slf4j
@Component
public class InitHotelLabelConfigIdsToHotelPublicCollectionTask {

    @Resource
    private HotelRecommendMapper hotelRecommendMapper;

    @Autowired
    private HotelService hotelService;

    @XxlJob("initHotelLabelConfigIdsToHotelPublicCollectionTask")
    public void initHotelLabelConfigIdsToHotelPublicCollectionTask() {
        try {
            XxlJobHelper.log("执行同步酒店标签到mongodb公共表任务开始");
            List<HotelLabelAllDTO> hotelLabelAllDTOS = hotelRecommendMapper.queryHotelLabelAll();
            //组装数据hotelId-agentCode:hotelLabelIds
            Map<String, Set<Integer>> hotelAgentCodeMap = new HashMap<>();
            for (HotelLabelAllDTO allDTO : hotelLabelAllDTOS) {
                if (allDTO.getHotelId() == null || StringUtils.isEmpty(allDTO.getAgentCode())) {
                    continue;
                }
                Set<Integer> labelIds = hotelAgentCodeMap.get(allDTO.getHotelId() + "-" + allDTO.getAgentCode());
                if (CollectionUtils.isEmpty(labelIds)) {
                    labelIds = new HashSet<>();
                    labelIds.add(allDTO.getHotelLabelConfigId());
                } else {
                    labelIds.add(allDTO.getHotelLabelConfigId());
                }
                hotelAgentCodeMap.put(allDTO.getHotelId() + "-" + allDTO.getAgentCode(), labelIds);
            }

            //组装参数格式hotelId：（agentCode,hotelLabelId）
            Map<String, List<HotelAndAgentCodeLabelDTO>> hotelIdMap = new HashMap<>();
            for (String key : hotelAgentCodeMap.keySet()) {
                String hotelId = key.split("-")[0];
                String agentCode = key.split("-")[1];
                Set<Integer> integers = hotelAgentCodeMap.get(key);
                HotelAndAgentCodeLabelDTO dto = new HotelAndAgentCodeLabelDTO();
                dto.setAgentCode(agentCode);
                dto.setHotelLabelConfigIds(integers);
                List<HotelAndAgentCodeLabelDTO> hotelAndAgentCodeLabelDTOS = hotelIdMap.get(hotelId);
                if (CollectionUtils.isEmpty(hotelAndAgentCodeLabelDTOS)) {
                    hotelAndAgentCodeLabelDTOS = new ArrayList<>();
                    hotelAndAgentCodeLabelDTOS.add(dto);
                } else {
                    hotelAndAgentCodeLabelDTOS.add(dto);
                }
                hotelIdMap.put(hotelId, hotelAndAgentCodeLabelDTOS);
            }

            //更新数据到mongodb
            for (String key : hotelIdMap.keySet()) {
                HotelPublicDTO hotelPublicDTO = new HotelPublicDTO();
                hotelPublicDTO.setHotelId(Long.parseLong(key));
                hotelPublicDTO.setHotelLabelConfigIds(hotelIdMap.get(key));
                hotelService.updateHotelMongodbByHotelId(hotelPublicDTO);
            }
            XxlJobHelper.log("执行同步酒店标签到mongodb公共表任务结束");
        } catch (Exception e) {
            log.error("执行同步酒店标签到mongodb公共表任务异常", e);
            XxlJobHelper.log("执行同步酒店标签到mongodb公共表任务异常", e);
        }
    }
}
