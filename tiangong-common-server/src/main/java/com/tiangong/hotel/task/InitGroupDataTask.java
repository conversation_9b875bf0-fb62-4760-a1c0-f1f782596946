package com.tiangong.hotel.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.config.SettingsConstant;
import com.tiangong.constant.FieldConstants;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.domain.BaseinfoGroupPO;
import com.tiangong.hotel.service.BaseinfoGroupService;
import com.tiangong.util.HttpUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 初始化集团品牌信息任务
 *
 * <AUTHOR>
 * @create 2023/12/16 11:57
 */
@Slf4j
@Component
public class InitGroupDataTask {

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private BaseinfoGroupService baseinfoGroupService;

    @XxlJob("InitGroupDataTask")
    public void initGroupDataTask() {
        try {
            XxlJobHelper.log("执行初始化集团品牌信息任务开始");
            for (LanguageTypeEnum languageTypeEnum : LanguageTypeEnum.values()) {
                //先删除，在更新
                baseinfoGroupService.remove(new LambdaQueryWrapper<BaseinfoGroupPO>().eq(BaseinfoGroupPO::getLanguage, languageTypeEnum.getValue()));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(FieldConstants.LANGUAGE_TYPE, languageTypeEnum.getValue());
                String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.GROUP_AND_BRAND, jsonObject.toJSONString());
                JSONObject groupAndBrand = JSONObject.parseObject(result);
                if (groupAndBrand != null && groupAndBrand.getInteger("result") == 1) {
                    JSONArray array = groupAndBrand.getJSONArray("data");
                    if (array == null || array.size() == 0) {
                        continue;
                    }
                    List<BaseinfoGroupPO> baseinfoGroupPOS = array.toJavaList(BaseinfoGroupPO.class);
                    List<BaseinfoGroupPO> list = baseinfoGroupPOS.stream().peek(o -> o.setLanguage(languageTypeEnum.getValue())).collect(Collectors.toList());
                    baseinfoGroupService.saveBatch(list);
                }
            }
            XxlJobHelper.log("执行初始化集团品牌信息任务结束");
        } catch (Exception e) {
            log.error("执行初始化集团品牌信息任务异常", e);
            XxlJobHelper.log("执行初始化集团品牌信息任务异常", e);
        }
    }
}
