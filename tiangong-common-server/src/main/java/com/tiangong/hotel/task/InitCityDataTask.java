package com.tiangong.hotel.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.config.SettingsConstant;
import com.tiangong.constant.FieldConstants;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.domain.BaseinfoAreadataEntity;
import com.tiangong.hotel.req.QueryAreaDataReq;
import com.tiangong.hotel.service.BaseinfoAreadataService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.HttpUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 拉取城市信息任务
 * <AUTHOR>
 * @create 2023/10/24 14:09
 */
@Slf4j
@Component
public class InitCityDataTask {

    @Autowired
    private BaseinfoAreadataService baseinfoAreadataService;

    @Autowired
    private SettingsConstant settingsConstant;

    @XxlJob("InitCityDataTask")
    public void initCityDataTask() {
        try {
            XxlJobHelper.log("执行拉取城市信息任务开始");
            //先删除城市信息，在新增
            for (LanguageTypeEnum languageTypeEnum : LanguageTypeEnum.values()) {
                baseinfoAreadataService.delAreaDataByType(2, languageTypeEnum.getValue());
                baseinfoAreadataService.delAreaDataByType(3, languageTypeEnum.getValue());
                //获取国家信息
                QueryAreaDataReq req = new QueryAreaDataReq();
                req.setLanguage(languageTypeEnum.getValue());
                req.setAreaType(1);
                List<BaseinfoAreadataEntity> areadataEntityList = baseinfoAreadataService.queryAreaDataListByType(req);
                for (BaseinfoAreadataEntity areadataEntity : areadataEntityList) {
                    //判断是国内还是海外数据
                    Integer supplyType = 1;
                    if (!"CN".equals(areadataEntity.getAreaCode())) {
                        supplyType = 2;
                    }
                    // 获取酒店基础信息数据
                    JSONObject send = new JSONObject();
                    send.put(FieldConstants.LANGUAGE_TYPE, languageTypeEnum.getValue());
                    send.put("countryCode", areadataEntity.getAreaCode());
                    String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.CITY_LIST_RUL, send.toJSONString());
                    JSONObject areaData = JSONObject.parseObject(result);
                    JSONArray data = areaData.getJSONArray("data");
                    if (data == null) {
                        log.info("--------拉取城市信息数据：为空----------------------------+" + areadataEntity.getAreaName() + ":" + areadataEntity.getAreaCode());
                        continue;
                    }
                    for (int i = 0; i < data.size(); i++) {
                        List<BaseinfoAreadataEntity> list = new ArrayList<>();
                        List<String> listCode = new ArrayList<>();
                        JSONObject area = data.getJSONObject(i);
                        BaseinfoAreadataEntity entity = new BaseinfoAreadataEntity();
                        entity.setAreaCode(area.getString("provinceCode"));
                        entity.setAreaName(area.getString("provinceName"));
                        entity.setAreaType(2);
                        entity.setPinyin(area.getString("provincePinyin"));
                        entity.setAcronym(area.getString("provinceAcronymPinyin"));
                        entity.setAreaEnglishName("");
                        entity.setParentAreaId(area.getString("countryCode"));
                        entity.setCreatedBy("basics--task");
                        entity.setCreatedDt(new Date());
                        entity.setSupplyType(supplyType);
                        list.add(entity);
                        JSONArray citys = area.getJSONArray("citys");
                        if (citys != null && citys.size() > 0) {
                            for (int j = 0; j < citys.size(); j++) {
                                JSONObject city = citys.getJSONObject(j);
                                BaseinfoAreadataEntity cityEntity = new BaseinfoAreadataEntity();
                                cityEntity.setAreaCode(city.getString(FieldConstants.CITY_CODE));
                                cityEntity.setAreaName(city.getString("cityName"));
                                cityEntity.setParentAreaId(area.getString("provinceCode"));
                                cityEntity.setAreaType(3);
                                cityEntity.setPinyin(area.getString("cityPinyin"));
                                cityEntity.setAcronym(area.getString("cityAcronymPinyin"));
                                cityEntity.setLatitude(city.getBigDecimal("latBaidu"));
                                cityEntity.setLongitude(city.getBigDecimal("lngBaidu"));
                                cityEntity.setCityId(city.getString(FieldConstants.CITY_CODE));
                                cityEntity.setTimeZero(city.getString("timeZone"));
                                cityEntity.setCreatedBy("basics--task");
                                cityEntity.setCreatedDt(new Date());
                                cityEntity.setSupplyType(supplyType);
                                list.add(cityEntity);
                                listCode.add(cityEntity.getAreaCode());
                            }
                        }
                        baseinfoAreadataService.insertAreaData(list, languageTypeEnum.getValue());
                        //将城市编码存入缓存，用于新增酒店里面获取
                        RedisTemplateX.lLeftPushAll(RedisKey.CITY_AND_PROVINCE_CODE + "_" + languageTypeEnum.getValue(), listCode);
                    }
                }
            }
            XxlJobHelper.log("执行拉取城市信息任务结束");
        } catch (Exception e) {
            log.error("执行拉取城市信息任务异常", e);
            XxlJobHelper.log("执行拉取城市信息任务异常", e);
        }
    }
}
