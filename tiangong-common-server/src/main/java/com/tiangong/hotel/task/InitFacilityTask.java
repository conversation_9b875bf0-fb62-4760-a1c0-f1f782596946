package com.tiangong.hotel.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.config.SettingsConstant;
import com.tiangong.constant.FieldConstants;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.domain.FacilityEntity;
import com.tiangong.hotel.service.FacilityService;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.HttpUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 初始化设施信息任务
 *
 * <AUTHOR>
 * @create 2023/12/16 11:57
 */
@Component
@Slf4j
public class InitFacilityTask {

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private FacilityService facilityService;

    @XxlJob("InitFacilityTask")
    public void initFacilityTask() {
        try {
            XxlJobHelper.log("执行初始化设施信息任务开始");
            for (LanguageTypeEnum languageTypeEnum : LanguageTypeEnum.values()) {
                //先删除，在更新
                facilityService.remove(new LambdaQueryWrapper<FacilityEntity>().eq(FacilityEntity::getLanguage, languageTypeEnum.getValue()));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(FieldConstants.LANGUAGE_TYPE, languageTypeEnum.getValue());
                String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.QUERY_FACILITY_LIST, jsonObject.toJSONString());
                JSONObject groupAndBrand = JSONObject.parseObject(result);
                if (groupAndBrand != null && groupAndBrand.getInteger("result") == 1) {
                    JSONArray array = groupAndBrand.getJSONArray("data");
                    if (array == null || array.size() == 0) {
                        continue;
                    }
                    List<FacilityEntity> facilityEntityList = array.toJavaList(FacilityEntity.class);
                    List<FacilityEntity> list = facilityEntityList.stream().peek(o -> {
                        o.setLanguage(languageTypeEnum.getValue());
                        o.setCreatedDt(DateUtilX.getDateStr());
                    }).collect(Collectors.toList());
                    facilityService.saveBatch(list);
                }
            }
            XxlJobHelper.log("执行初始化设施信息任务结束");
        } catch (Exception e) {
            log.error("执行初始化设施信息任务异常", e);
            XxlJobHelper.log("执行初始化设施信息任务异常", e);
        }
    }
}
