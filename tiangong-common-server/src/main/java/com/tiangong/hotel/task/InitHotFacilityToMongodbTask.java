package com.tiangong.hotel.task;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongodb.client.result.UpdateResult;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.StrPool;
import com.tiangong.hotel.domain.FacilityConfigEntity;
import com.tiangong.hotel.domain.collection.FacilityChangeHotelId;
import com.tiangong.hotel.domain.collection.HotelPublicCollection;
import com.tiangong.hotel.service.FacilityConfigService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 初始化热门设施大类到mongodb任务
 *
 * <AUTHOR>
 * @create 2023/12/16 11:57
 */
@Slf4j
@Component
public class InitHotFacilityToMongodbTask {

    @Autowired
    private FacilityConfigService facilityConfigService;

    @Autowired
    @Qualifier("mongoTemplateBasic")
    private MongoTemplate mongoTemplateBasic;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;
    private final static String INIT_FLAG="init";
    @XxlJob("initHotFacilityToMongodbTask")
    @AnonymousAccess
    public void initHotFacilityToMongodbTask() {
        try {
            XxlJobHelper.log("执行初始化热门设施大类到mongodb任务开始");
            String jobParam = XxlJobHelper.getJobParam();
            if (INIT_FLAG.equals(jobParam)) {
                initHotFacility();
            } else {
                String facilityCode = RedisTemplateX.lRightPop(RedisKey.HOT_FACILITY_QUEUE);
                // 异步拉取数据，查询设施变更的酒店
                CompletableFuture.runAsync(() -> {
                    Query query = new Query();
                    List<FacilityChangeHotelId> all = mongoTemplateBasic.find(query, FacilityChangeHotelId.class);
                    if (CollectionUtils.isNotEmpty(all)) {
                        for (FacilityChangeHotelId changeHotelId : all) {
                            Query remove = new Query();
                            remove.addCriteria(new Criteria("_id").is(changeHotelId.getHotelId()));
                            mongoTemplateBasic.remove(query, FacilityChangeHotelId.class);
                        }

                        // 把队列里面的数据全部删掉，再重新初始化
                        RedisTemplateX.delete(RedisKey.HOT_FACILITY_QUEUE);
                        initHotFacility();
                    }
                }, processingCommonExecutor);
                if (StringUtils.isEmpty(facilityCode)) {
                    return;
                }
                FacilityConfigEntity one = facilityConfigService.getOne(new LambdaQueryWrapper<FacilityConfigEntity>().eq(FacilityConfigEntity::getFacilityCode, facilityCode));
                if (Objects.isNull(one)) {
                    return;
                }

                List<Long> facilityIds = new ArrayList<>();
                // 先删除之前的热门标识
                Query query = new Query(Criteria.where("hotFacility").in(facilityCode));
                Update update = new Update().pull("hotFacility", facilityCode);
                try {
                    UpdateResult updateResult = mongoTemplateBasic.updateMulti(query, update, HotelPublicCollection.class);
                    System.out.println(updateResult);
                } catch (Exception e) {
                    log.error("删除之前的热门标识异常", e);
                }

                // 再新增现在的设施id热门标识
                String facilityIdList = one.getFacilityIdList();
                if (facilityIdList != null) {
                    String[] split = facilityIdList.split(StrPool.COMMA);
                    for (String s : split) {
                        if (StringUtils.isEmpty(s)) {
                            continue;
                        }
                        facilityIds.add(Long.parseLong(s));
                    }
                }
                if (CollectionUtils.isNotEmpty(facilityIds)) {
                    Query query1 = new Query(Criteria.where("facilitiesIds").in(facilityIds));
                    Update update1 = new Update().push("hotFacility", one.getFacilityCode());
                    UpdateResult updateResult = mongoTemplateBasic.updateMulti(query1, update1, HotelPublicCollection.class);
                    System.out.println(updateResult);
                }
            }
            XxlJobHelper.log("执行初始化热门设施大类到mongodb任务结束");
        } catch (Exception e) {
            log.error("执行初始化热门设施大类到mongodb任务异常", e);
            XxlJobHelper.log("执行初始化热门设施大类到mongodb任务异常", e);
        }
    }

    /**
     * 初始化酒店设施
     */
    private void initHotFacility() {
        List<FacilityConfigEntity> list = facilityConfigService.list();
        List<Long> facilityIds = new ArrayList<>();
        for (FacilityConfigEntity entity : list) {
            // 先删除之前的热门标识
            Query query = new Query(Criteria.where("hotFacility").in(entity.getFacilityCode()));
            Update update = new Update().pull("hotFacility", entity.getFacilityCode());
            try {
                mongoTemplateBasic.updateMulti(query, update, HotelPublicCollection.class);
            } catch (Exception e) {
                log.error("删除之前的热门标识异常", e);
            }

            // 再新增现在的设施id热门标识
            String facilityIdList = entity.getFacilityIdList();
            if (facilityIdList != null) {
                String[] split = facilityIdList.split(StrPool.COMMA);
                for (String s : split) {
                    if (StringUtils.isEmpty(s)) {
                        continue;
                    }
                    facilityIds.add(Long.parseLong(s));
                }
            }
            if (CollectionUtils.isNotEmpty(facilityIds)) {
                Query query1 = new Query(Criteria.where("facilitiesIds").in(facilityIds));
                Update update1 = new Update().push("hotFacility", entity.getFacilityCode());
                mongoTemplateBasic.updateMulti(query1, update1, HotelPublicCollection.class);

                facilityIds = new ArrayList<>();
            }
        }
    }
}
