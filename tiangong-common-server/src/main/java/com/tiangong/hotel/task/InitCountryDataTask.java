package com.tiangong.hotel.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.config.SettingsConstant;
import com.tiangong.constant.FieldConstants;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.domain.BaseinfoAreadataEntity;
import com.tiangong.hotel.service.BaseinfoAreadataService;
import com.tiangong.util.HttpUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 初始化国家信息任务
 *
 * <AUTHOR>
 * @create 2023/10/24 14:09
 */
@Component
@Slf4j
public class InitCountryDataTask {

    @Autowired
    private BaseinfoAreadataService baseinfoAreadataService;

    @Autowired
    private SettingsConstant settingsConstant;

    @XxlJob("InitCountryDataTask")
    public void initCountryDataTask() {
        try {
            XxlJobHelper.log("执行初始化国家信息任务开始");
            for (LanguageTypeEnum languageTypeEnum : LanguageTypeEnum.values()) {
                JSONObject send = new JSONObject();
                send.put(FieldConstants.LANGUAGE_TYPE, languageTypeEnum.getValue());
                String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.COUNTRY_LIST_URL, send.toJSONString());
                JSONObject areaData = JSONObject.parseObject(result);
                JSONArray data = areaData.getJSONArray("data");
                if (data == null) {
                    log.info("--------拉取国家信息数据：为空----------------------------");
                    return;
                }
                List<BaseinfoAreadataEntity> list = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    JSONObject area = data.getJSONObject(i);
                    BaseinfoAreadataEntity entity = new BaseinfoAreadataEntity();
                    entity.setAreaCode(area.getString("countryCode"));
                    entity.setAreaName(area.getString("countryName"));
                    entity.setAreaType(1);
                    entity.setPinyin(area.getString("pinyin"));
                    entity.setAcronym(area.getString("acronymPinyin"));
                    entity.setAreaEnglishName("");
                    entity.setCreatedBy("basics--task");
                    entity.setCreatedDt(new Date());
                    entity.setSupplyType("CN".equals(entity.getAreaCode()) ? 1 : 2);
                    list.add(entity);
                }
                //先删除国家信息，在新增
                baseinfoAreadataService.delAreaDataByType(1, languageTypeEnum.getValue());
                baseinfoAreadataService.insertAreaData(list, languageTypeEnum.getValue());
            }
            XxlJobHelper.log("执行初始化国家信息任务结束");
        } catch (Exception e) {
            log.error("执行初始化国家信息任务异常", e);
            XxlJobHelper.log("执行初始化国家信息任务异常", e);
        }
    }
}
