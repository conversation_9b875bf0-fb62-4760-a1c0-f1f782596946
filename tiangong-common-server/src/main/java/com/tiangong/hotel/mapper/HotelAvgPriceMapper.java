package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.hotel.domain.HotelAvgPriceEntity;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.hotel.resp.HotelAvgPriceResp;

import java.util.List;

/**
 * 酒店平均价表
 */
public interface HotelAvgPriceMapper extends BaseMapper<HotelAvgPriceEntity> {

    /**
     * 查询酒店平均价城市编码列表
     *
     * @return 返回酒店平均价城市编码列表
     */
    List<String> selectHotelAvgPriceCityCodes();

    /**
     * 查询酒店平均价列表
     *
     * @param req 酒店平均价请求参数，包含查询条件
     * @return 返回酒店平均价响应列表
     */
    List<HotelAvgPriceResp> selectHotelAvgPriceList(HotelAvgPriceReq req);

    /**
     * 删除所有
     */
    void deleteAll();
}
