package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.hotel.domain.HotelAvailablePO;
import com.tiangong.hotel.domain.req.QueryHotelAvailableReq;
import com.tiangong.hotel.domain.resp.HotelAvailableListResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @create 2024/1/3 11:00
 */
@Mapper
public interface HotelAvailableMapper extends BaseMapper<HotelAvailablePO> {

    /**
     * 查询酒店可见性列表
     *
     * @param page 分页参数，包含页码和每页大小
     * @param req 查询酒店可见性请求参数，包含查询条件
     * @return 返回分页的酒店可见性列表响应数据
     */
    IPage<HotelAvailableListResp> queryHotelListAvailable(IPage<HotelAvailableListResp> page, @Param("req") QueryHotelAvailableReq req);
}
