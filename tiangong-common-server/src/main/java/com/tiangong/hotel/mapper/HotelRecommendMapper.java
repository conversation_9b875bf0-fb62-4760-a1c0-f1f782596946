package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.hotel.domain.HotelRecommendEntity;
import com.tiangong.hotel.domain.vo.HotelLabelAllDTO;
import com.tiangong.hotel.domain.vo.HotelLabelNameDTO;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelLabelConfigResp;
import com.tiangong.hotel.resp.HotelRecommendResp;
import com.tiangong.product.resp.HotelLabelDTO;

import java.util.List;

/**
 * 酒店推荐排序
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
public interface HotelRecommendMapper extends BaseMapper<HotelRecommendEntity> {

    /**
     * 查询酒店推荐列表
     *
     * @param page 分页参数，包含页码和每页大小
     * @param req 酒店推荐请求，包含查询条件
     * @return 返回分页的酒店推荐响应数据
     */
    IPage<HotelRecommendResp> selectHotelRecommendPage(IPage<?> page, HotelRecommendReq req);

    /**
     * 获取酒店标签信息
     *
     * @param req 酒店信息请求，包含酒店标签查询参数
     * @return 返回酒店标签配置响应列表
     */
    List<HotelLabelConfigResp> queryHotelLabelInfo(HotelInfoReq req);

    /**
     * 查询酒店标签列表
     *
     * @return 返回所有酒店标签DTO列表
     */
    List<HotelLabelAllDTO> queryHotelLabelAll();

    /**
     * 查询指定客户|所有客户酒店标签列表
     *
     * @param dto 酒店标签名称DTO，包含客户和标签查询条件
     * @return 返回酒店标签DTO列表
     */
    List<HotelLabelDTO> selectAgentHotelLabel(HotelLabelNameDTO dto);

    /**
     * 查询酒店推荐总分
     *
     * @param req 酒店推荐请求，包含推荐分数查询条件
     * @return 返回酒店推荐响应列表
     */
    List<HotelRecommendResp> selectHotelRecommendScoreList(HotelRecommendReq req);

    /**
     * 查询酒店推荐总分(分页)
     *
     * @param req 酒店推荐请求，包含分页和推荐分数查询条件
     * @return 返回酒店推荐响应列表
     */
    List<HotelRecommendResp> selectHotelRecommendScorePage(HotelRecommendReq req);

    /**
     * 查询deleted 为 0 的酒店排序
     * @return
     */
    List<HotelRecommendResp> selectAllRecommend();
}
