package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.dto.hotel.HotelLabelResp;
import com.tiangong.hotel.domain.HotelLabelConfigEntity;
import com.tiangong.hotel.domain.vo.HotelLabelDTO;
import com.tiangong.hotel.req.HotelLabelConfigReq;
import com.tiangong.hotel.resp.HotelLabelConfigResp;

import java.util.List;

public interface HotelLabelConfigMapper extends BaseMapper<HotelLabelConfigEntity> {

    /**
     * 酒店标签配置列表
     *
     * @param req 酒店标签配置请求，包含查询条件
     * @return 返回酒店标签配置响应列表
     */
    List<HotelLabelConfigResp> hotelLabelConfigList(HotelLabelConfigReq req);

    /**
     * 查询最大id
     *
     * @return 返回酒店标签配置的最大ID值
     */
    Integer selectHotelLabelConfigMaxId();

    /**
     * 根据酒店标签id查询酒店标签名称
     *
     * @param hotelLabelDTO 酒店标签DTO，包含酒店标签ID查询条件
     * @return 返回酒店标签响应列表
     */
    List<HotelLabelResp> queryHotelLabelByIds(HotelLabelDTO hotelLabelDTO);
}
