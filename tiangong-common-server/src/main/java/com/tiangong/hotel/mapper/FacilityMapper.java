package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.hotel.domain.FacilityEntity;
import com.tiangong.hotel.domain.resp.FacilityListResp;
import com.tiangong.hotel.domain.resp.FacilityNameResp;
import com.tiangong.dto.hotel.HotFacilityDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FacilityMapper extends BaseMapper<FacilityEntity> {

    /**
     * 查询设施信息
     *
     * @param facilityType 设施类型，用于筛选设施
     * @param language 语言设置，用于多语言查询
     * @return 返回设施列表响应数据
     */
    List<FacilityListResp> queryFacility(@Param("facilityType") Integer facilityType,
                                         @Param("language") String language);

    /**
     * 分页查询设施信息
     *
     * @param page 分页参数，包含页码和每页大小
     * @param facilityType 设施类型，用于筛选设施
     * @param facilityName 设施名称，用于模糊查询
     * @param facilityIdList 设施ID列表，用于批量查询
     * @param language 语言设置，用于多语言查询
     * @return 返回分页的设施名称响应数据
     */
    IPage<FacilityNameResp> queryFacilityPage(IPage<FacilityNameResp> page,
                                              @Param("facilityType") Integer facilityType,
                                              @Param("facilityName") String facilityName,
                                              @Param("facilityIdList") String facilityIdList,
                                              @Param("language") String language);

    /**
     * 根据设施编码查询设施名称
     *
     * @param page 分页参数，包含页码和每页大小
     * @param facilityCode 设施编码，用于精确查询
     * @param facilityName 设施名称，用于模糊查询
     * @param language 语言设置，用于多语言查询
     * @return 返回分页的设施名称响应数据
     */
    IPage<FacilityNameResp> selectFacilityNameByFacilityCode(IPage<FacilityNameResp> page,
                                              @Param("facilityCode") String facilityCode,
                                              @Param("facilityName") String facilityName,
                                              @Param("language") String language);

    /**
     * 查询热门设施编码
     *
     * @param list 设施编码列表，用于批量查询
     * @return 返回热门设施DTO列表
     */
    List<HotFacilityDTO> queryHotFacilityCode(@Param("list") List<String> list);
}
