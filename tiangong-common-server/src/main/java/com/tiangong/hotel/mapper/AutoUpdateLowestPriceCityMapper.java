package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceCityEntity;
import com.tiangong.hotel.domain.resp.AutoUpdateLowestPriceCityResp;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;

public interface AutoUpdateLowestPriceCityMapper extends BaseMapper<AutoUpdateLowestPriceCityEntity> {

     /**
      * 查询主动更新起价城市列表（分页）
      *
      * @param page 分页参数，包含页码和每页大小
      * @param vo 主动更新起价城市查询条件，包含筛选参数
      * @return 返回分页的主动更新起价城市响应数据
      */
     IPage<AutoUpdateLowestPriceCityResp> autoUpdateLowestPriceCityPage(IPage<?> page, AutoUpdateLowestPriceCityVo vo);
}