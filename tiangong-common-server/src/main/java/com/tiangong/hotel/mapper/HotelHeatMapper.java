package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.hotel.domain.HotelHeatEntity;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelHeatResp;

import java.util.List;
import java.util.Set;

/**
 * 酒店热度表
 */
public interface HotelHeatMapper extends BaseMapper<HotelHeatEntity> {

    /**
     * 查询酒店热度列表（分页）
     *
     * @param page 分页参数，包含页码和每页大小
     * @param req 酒店热度请求，包含查询条件
     * @return 返回分页的酒店热度响应数据
     */
    IPage<HotelHeatResp> selectHotelHeatPage(IPage<?> page, HotelHeatReq req);

    /**
     * 修改热度分数
     *
     * @param reqList 酒店热度请求列表，包含要更新的热度分数信息
     */
    void updateHotelHeatScore(List<HotelHeatReq> reqList);

    /**
     * 修改热度总分数
     *
     * @param req 酒店热度请求，包含热度总分数更新信息
     */
    void updateHotelHeatScoreTotalValue(HotelHeatReq req);

    /**
     * 查询酒店热度分数(分页)
     *
     * @param req 酒店热度请求，包含分页查询条件
     * @return 返回酒店热度响应列表
     */
    List<HotelHeatResp> selectHotelHeatScorePage(HotelHeatReq req);

    /**
     * 根据酒店id列表查询酒店热度分数
     *
     * @param req 酒店热度请求，包含酒店ID列表查询条件
     * @return 返回酒店热度响应列表
     */
    List<HotelHeatResp> selectHotelHeatScoreByHotelIds(HotelHeatReq req);

    /**
     * 批量修改酒店热度分数
     */
    void updateBatchFields(List<HotelHeatReq> batch);

    /**
     * 分页查询酒店热度分数
     */
    List<String> selectAllHotelHeatHotelIdPage(HotelHeatReq req);

    /**
     * 根据酒店id列表查询酒店热度酒店id
     */
    Set<String> selectHotelIdByHotelIds(HotelHeatReq req);
}
