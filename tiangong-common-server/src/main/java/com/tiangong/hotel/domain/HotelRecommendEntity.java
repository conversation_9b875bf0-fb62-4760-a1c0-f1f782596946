package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 酒店推荐排序
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
@Data
@TableName("t_hotel_recommend")
public class HotelRecommendEntity extends Model<HotelRecommendEntity> {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 酒店Id
     */
    private Long hotelId;
    /**
     * 推荐类型 1-推荐 2-协议
     */
    private Integer recommendType;
    /**
     * 推荐分值
     */
    private Integer recommendScore;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;

}
