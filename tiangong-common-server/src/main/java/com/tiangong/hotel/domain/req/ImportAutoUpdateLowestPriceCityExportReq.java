package com.tiangong.hotel.domain.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data

public class ImportAutoUpdateLowestPriceCityExportReq {

    /**
     * 城市id
     */
    @ExcelProperty("城市id")
    private String destinationId;

    /**
     * 酒店数量
     */
    @ExcelProperty("城市可跑的酒店数量")
    private Integer hotelCount;

    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    private String errorMsg;
}
