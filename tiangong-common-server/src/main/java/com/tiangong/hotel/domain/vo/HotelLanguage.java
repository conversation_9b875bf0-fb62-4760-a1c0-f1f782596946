package com.tiangong.hotel.domain.vo;

import lombok.Data;

@Data
public class HotelLanguage {

    public String language;

    /**
     * 酒店名称
     */
    public String HotelName;

    /**
     * 酒店地址
     */
    public String hotelAddress;

    public String localHotelAddress;

    /**
     * 国家名称
     */
    public String countryName;

    /**
     * 省份名称
     */
    public String provinceName;

    /**
     * 城市名称
     */
    public String cityName;

    /**
     * 商业区名称
     */
    public String businessZoneName;

    /**
     * 行政区名称
     */
    public String districtName;

    /**
     * 集团名称
     */
    public String groupName;

    /**
     * 品牌名称
     */
    public String brandName;

    /**
     * 简介
     */
    public String introduce;

}
