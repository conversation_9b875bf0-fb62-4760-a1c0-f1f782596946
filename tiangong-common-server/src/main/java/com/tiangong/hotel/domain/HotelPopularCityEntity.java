package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 酒店热门城市表
 */
@Data
@TableName("t_hotel_popular_city")
public class HotelPopularCityEntity {
    /**
     * 目的地id
     */
    @TableId(value = "destination_id", type = IdType.INPUT)
    private String destinationId;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
}
