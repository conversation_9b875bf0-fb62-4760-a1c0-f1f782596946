package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_baseinfo_facility")
public class BaseinfoFacility {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 停车场
     */
    private String parkingIds;

    /**
     * 会议室
     */
    private String meetingIds;

    /**
     * 泳池
     */
    private String swimmingIds;

    /**
     * 餐厅
     */
    private String restaurantIds;

    /**
     * 健身房
     */
    private String gymIds;

    /**
     * 机器人
     */
    private String robotIds;

    /**
     * 洗衣房
     */
    private String laundryRoomIds;

    /**
     * 行李寄存
     */
    private String luggageIds;

}
