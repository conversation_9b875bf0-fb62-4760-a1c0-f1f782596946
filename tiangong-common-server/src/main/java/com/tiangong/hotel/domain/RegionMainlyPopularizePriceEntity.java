package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 区域主推价格表
 */
@Data
@TableName("t_region_mainly_popularize_price")
public class RegionMainlyPopularizePriceEntity {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * '区域类型（1国家 3城市）
     */
    private Integer regionType;

    /**
     * '推荐最低价'
     */
    private BigDecimal startPrice;

    /**
     * '推荐最高价'
     */
    private BigDecimal endPrice;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
