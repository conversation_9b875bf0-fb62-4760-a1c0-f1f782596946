package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/12/16 11:50
 */
@Data
@TableName("t_baseinfo_group")
public class BaseinfoGroupPO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 集团id
     */
    private String groupId;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 集团名称
     */
    private String groupName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 语言
     */
    private String language;

    private Date createdDt;
}
