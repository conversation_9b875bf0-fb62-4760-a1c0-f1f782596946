package com.tiangong.hotel.domain.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExportHotelRecommendReq {

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String clientName;

    /**
     * 酒店名称
     */
    @ExcelProperty("酒店名称")
    private String hotelName;

    /**
     * 酒店标签
     */
    @ExcelProperty("酒店标签")
    private String hotelLabelConfigIds;

    /**
     * 推荐分值
     */
    @ExcelProperty("推荐分值")
    private String recommendScore;

    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    private String failReason;
}
