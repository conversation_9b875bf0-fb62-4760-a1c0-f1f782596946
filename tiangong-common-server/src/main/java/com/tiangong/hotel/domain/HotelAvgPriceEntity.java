package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店平均价表
 */
@Data
@TableName("t_hotel_avg_price")
public class HotelAvgPriceEntity {

    /**
     * 酒店id
     */
    @TableId(value = "hotel_id", type = IdType.INPUT)
    private Long hotelId;

    /**
     * 平均价
     */
    private BigDecimal avgPrice;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
