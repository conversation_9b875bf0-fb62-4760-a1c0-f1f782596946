package com.tiangong.hotel.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExcelTitleEnum {
    /**
     * 客户名称
     */
    CLIENT_NAME(0,"客户名称"),
    HOTEL_NAME(1,"酒店名称"),
    HOTEL_LABEL(2,"酒店标签"),
    HOTEL_RECOMMEND_SCORE(3,"酒店推荐分值")
    ;

    public final int key;
    public final String value;

    public static int getKeyByValue(String value) {
        int key = 0;
        for(ExcelTitleEnum titleEnum : ExcelTitleEnum.values()) {
            if(titleEnum.value.equals(value.trim())) {
                key = titleEnum.key;
                break;
            }
        }
        return key;
    }
}
