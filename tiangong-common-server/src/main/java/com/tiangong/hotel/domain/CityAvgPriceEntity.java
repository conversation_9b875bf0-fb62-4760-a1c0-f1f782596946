package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 城市平均价表
 */
@Data
@TableName("t_city_avg_price")
public class CityAvgPriceEntity {

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 平均价
     */
    private BigDecimal avgPrice;

    /**
     * 计算日期
     */
    private Date calculateDate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
