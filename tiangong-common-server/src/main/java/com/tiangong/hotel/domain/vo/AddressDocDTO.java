package com.tiangong.hotel.domain.vo;

import lombok.Data;

@Data
public class AddressDocDTO {

    /**
     * 目的地名称
     */
    private String name;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 集团编码
     */
    private String groupCode;

    /**
     * 集团名称
     */
    private String groupName;

    /**
     * 语言
     */
    private String language;

    private Double latGoogle;

    private Double lngGoogle;
}
