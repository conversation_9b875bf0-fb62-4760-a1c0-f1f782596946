package com.tiangong.hotel.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class FacilityEditReq {

    /**
     * 设施编码
     */
    @NotEmpty(message = "FACILITY_CODE_IS_NOT_EMPTY")
    private String facilityCode;

    /**
     * 类型：1酒店 2房型
     */
    @NotNull(message = "FACILITY_TYPE_IS_NOT_EMPTY")
    private Integer facilityType;

    /**
     * 设施id
     */
    private String facilityIdList;
}
