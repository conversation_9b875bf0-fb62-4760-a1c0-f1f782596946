package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("t_baseinfo_hot_city")
public class BaseinfoHotCityEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 城市名
     */
    private String cityName;

    private String cityEnName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区域类型
     */
    private Integer areaType;

    /**
     * 父级id
     */
    private String parentAreaId;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 时区
     */
    private String timeZone;

    private Integer supplyType;

    private Date updateTime;
}
