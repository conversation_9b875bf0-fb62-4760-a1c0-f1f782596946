package com.tiangong.hotel.domain.resp;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/1/3 11:24
 */
@Data
public class HotelAvailableListResp {

    /**
     * id
     */
    private Integer id;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 类型 0黑名单 1白名单
     */
    private Integer availableType;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;
}
