package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 主动更新起价配置
 */
@Data
@TableName("t_auto_update_lowest_price_config")
public class AutoUpdateLowestPriceConfigEntity {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 执行频率
     */
    private Integer frequency;

    /**
     * 执行时间
     */
    private Date executeDate;

    /**
     * 是否执行成功
     */
    private Integer isSucceed;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 删除状态
     */
    private Integer deleted;
}
