package com.tiangong.hotel.domain.collection;

import com.tiangong.dto.common.HotelAndAgentCodeLabelDTO;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexType;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Data
@Document(value = "hotelPublicCollection")
public class HotelPublicCollection {


    @Id
    private Long hotelId;

    @Field(value = "countryCode")
    @Indexed(name = "idx_country")
    private String countryCode;

    @Field(value = "provinceCode")
    @Indexed(name = "idx_province")
    private String provinceCode;

    @Field(value = "cityCode")
    @Indexed(name = "idx_city")
    private String cityCode;

    @Field(value = "districtCode")
    @Indexed(name = "idx_district")
    private String districtCode;

    @Field(value = "businessZoneCode")
    @Indexed(name = "idx_businessZone")
    private String businessZoneCode;

    @Field(value = "groupCode")
    @Indexed(name = "idx_group")
    private String groupCode;

    @Field(value = "brandCode")
    @Indexed(name = "idx_brand")
    private String brandCode;

    @Field(value = "facilitiesIds")
    @Indexed(name = "idx_facility")
    private List<Long> facilitiesIds;

    @Field(value = "lowestPriceAndRecommendDTO")
    @Indexed(name = "idx_lowestPriceAndRecommendDTO")
    private List<LowestPriceDTO> lowestPriceAndRecommendDTO;

    @Field(value = "hotelAndAgentCodeLabelDTO")
    @Indexed(name = "idx_hotelAndAgentCodeLabelDTO")
    private List<HotelAndAgentCodeLabelDTO> hotelAndAgentCodeLabelDTO;

    @Field(value = "hotelStar")
    @Indexed(name = "idx_hotelStar")
    private Integer hotelStar;

    @Field(value = "hotelSubCategory")
    @Indexed(name = "idx_hotelSubCategory")
    private String hotelSubCategory;

    @Field(value = "heatValue")
    @Indexed(name = "idx_heatValue")
    private Double heatValue;

    @Field(value = "hotelScore")
    @Indexed(name = "idx_hotelScore")
    private double hotelScore;

    private double distance;

    private Double latGoogle;

    private Double lngGoogle;

    private Double Infinity;

    private String available;

    @GeoSpatialIndexed(type = GeoSpatialIndexType.GEO_2DSPHERE)
    private GeoJsonPoint geoJsonPoint;

    @Field(value = "hotFacility")
    @Indexed(name = "idx_hotFacility")
    private List<String> hotFacility;
    public HotelPublicCollection() {}
}
