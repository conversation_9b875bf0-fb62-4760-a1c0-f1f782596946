package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/1/3 10:58
 */
@Data
@TableName("t_hotel_available")
public class HotelAvailablePO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 0黑名单 1白名单
     */
    private Integer availableType;
}
