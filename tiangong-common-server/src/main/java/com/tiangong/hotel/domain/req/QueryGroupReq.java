package com.tiangong.hotel.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/12/16 14:36
 */
@Data
public class QueryGroupReq {

    /**
     * 集团名
     */
    private String groupName;

    /**
     * 品牌名
     */
    private String brandName;

    /**
     * 语言
     */
    @NotEmpty(message = "EMPTY_PARAM_LANGUAGETYPE")
    private String language;
}
