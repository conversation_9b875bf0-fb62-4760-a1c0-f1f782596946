package com.tiangong.hotel.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class FacilityAllReq extends BaseRequest {

    @NotEmpty(message = "FACILITY_CODE_IS_NOT_EMPTY")
    private String facilityCode;

    @NotNull(message = "FACILITY_TYPE_IS_NOT_EMPTY")
    private Integer facilityType;

    private String facilityName;

    private String language = "zh-CN";

    /**
     * 1修改 2删除
     */
    @NotNull(message = "ADD_OR_UPDATE_NOT_EMPTY")
    private Integer addOrDel;
}
