package com.tiangong.hotel.domain.vo;

import com.tiangong.hotel.domain.collection.LowestPriceDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class HotelInfoLanguage {

    private Long id;

    private String countryCode;

    private String provinceCode;

    private String cityCode;

    private String businessZoneCode;

    private String districtCode;

    private String groupCode;

    private String brandCode;

    /**
     * 酒店主图地址
     */
    private String hotelPhoneAddress;

    /**
     * 基础数据
     */
    private List<HotelLanguage> info;

    /**
     * 经纬度（百度、谷歌、高德）
     */
    private GeoInfoDTO geoInfo;

    /**
     * 星级
     */
    private HotelStar hotelStar;

    /**
     * 评分
     */
    private HotelRatingsDTO rating;

    /**
     * 起价
     */
    private LowestPriceDTO lowestPriceDTO;

    /**
     * 天宫排序字段
     */
    private Long recommendScore;

    /**
     * 天宫排序
     */
    private Long sort;

    /**
     * 星级
     */
    private Integer star;

    private BigDecimal lowestPrice;
}
