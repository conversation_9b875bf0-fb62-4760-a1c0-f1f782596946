package com.tiangong.hotel.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class FacilityScoreEditReq {

    /**
     * 设施编码
     */
    @NotEmpty(message = "FACILITY_CODE_IS_NOT_EMPTY")
    private String facilityCode;

    /**
     * 类型分数
     */
    @NotNull(message = "TYPE_SCORE_IS_NOT_EMPTY")
    private Integer typeScore;

}
