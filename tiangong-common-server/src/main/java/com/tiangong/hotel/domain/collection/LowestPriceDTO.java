package com.tiangong.hotel.domain.collection;

import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document(value = "lowestPriceAndRecommendDTO")
public class LowestPriceDTO {

    /**
     * 客户编码
     */
    @Field(value = "agentCode")
    @Indexed(name = "idx_agentCode")
    private String agentCode;

    /**
     * 起价
     */
    @Field(value = "lowestPrice")
    @Indexed(name = "idx_lowestPrice")
    private Double lowestPrice;

    /**
     * 币种
     */
    private Integer saleCurrency;

    /**
     * 到店付币种
     */
    private Integer payAtHotelCurrency;

    /**
     * 到店付金额
     */
    private Double payAtHotelFee;

    /**
     * 推荐值
     */
    private Double recommendScore;

    public LowestPriceDTO() {}
}
