package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 酒店热度值
 */
@Data
@TableName("t_hotel_heat")
public class HotelHeatEntity {

    /**
     * 酒店id
     */
    @TableId(value = "hotel_id", type = IdType.INPUT)
    private Long hotelId;

    /**
     * 热度分数
     */
    private Long heatScore;

    /**
     * 可订分数
     */
    private Long bookableScore;

    /**
     * 评分分数
     */
    private Long gradeScore;

    /**
     * 所属集团分数
     */
    private Long groupScore;

    /**
     * 城市平均房价分数
     */
    private Long cityAvgPriceScore;

    /**
     * 预订间夜分数
     */
    private Long roomNightScore;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
