package com.tiangong.hotel.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class AutoUpdateLowestPriceConfigVo {

    /**
     * id
     */
    private Integer id;

    /**
     * 客户编码
     */
    @NotNull(message = "客户编码不能为空")
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 执行频率
     */
    @NotNull(message = "执行频率不能为空")
    private Integer frequency;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;
}
