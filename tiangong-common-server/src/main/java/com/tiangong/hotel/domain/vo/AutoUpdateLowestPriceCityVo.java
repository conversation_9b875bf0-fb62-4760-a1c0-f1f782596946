package com.tiangong.hotel.domain.vo;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import java.util.Set;

@Data
public class AutoUpdateLowestPriceCityVo extends BasePage {

    /**
     * id
     */
    private Integer id;

    /**
     * 目的地id
     */
    private String destinationId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店数量
     */
    private Integer hotelCount;

    /**
     * 城市id集合
     */
    private Set<String> ids;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 语言
     */
    private String language;
}
