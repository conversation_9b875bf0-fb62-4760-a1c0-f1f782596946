package com.tiangong.hotel.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.HotelRecommendEntity;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelRecommendResp;
import org.springframework.web.multipart.MultipartFile;

import java.util.Set;

/**
 * 酒店推荐排序
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
public interface HotelRecommendService extends IService<HotelRecommendEntity> {

    /**
     * 酒店推荐排序新增
     *
     * @param req 酒店推荐请求，包含要新增的推荐排序信息
     */
    void hotelRecommendAdd(HotelRecommendReq req);

    /**
     * 酒店推荐排序删除
     *
     * @param req 酒店推荐请求，包含要删除的推荐排序标识
     * @return 返回删除操作影响的行数
     */
    int hotelRecommendDel(HotelRecommendReq req);

    /**
     * 酒店推荐排序编辑
     *
     * @param req 酒店推荐请求，包含要编辑的推荐排序信息
     * @return 返回编辑操作影响的行数
     */
    int hotelRecommendEdit(HotelRecommendReq req);

    /**
     * 酒店推荐排序列表（分页）
     *
     * @param req 酒店推荐请求，包含分页查询条件
     * @return 返回分页的酒店推荐响应数据
     */
    PaginationSupportDTO<HotelRecommendResp> hotelRecommendPage(HotelRecommendReq req);

    /**
     * 获取酒店标签信息
     *
     * @param req 酒店信息请求，包含酒店标签查询参数
     * @return 返回酒店标签信息集合
     */
    Set<String> queryHotelLabelInfo(HotelInfoReq req);

    /**
     * 导入酒店排序
     * @param file
     * @param userName
     * @return
     */
    Response<Object> importHotelRecommend(MultipartFile file, String userName);
}

