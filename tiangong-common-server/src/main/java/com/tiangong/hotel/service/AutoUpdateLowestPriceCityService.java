package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceCityEntity;
import com.tiangong.hotel.domain.resp.AutoUpdateLowestPriceCityResp;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 主动更新起价城市
 */
public interface AutoUpdateLowestPriceCityService extends IService<AutoUpdateLowestPriceCityEntity> {

    /**
     * 新增主动更新起价城市
     *
     * @param vo 主动更新起价城市参数，包含城市信息和配置
     */
    void autoUpdateLowestPriceCityAdd(AutoUpdateLowestPriceCityVo vo);

    /**
     * 更新主动更新起价城市
     *
     * @param vo 主动更新起价城市参数，包含更新的城市信息
     */
    void autoUpdateLowestPriceCityEdit(AutoUpdateLowestPriceCityVo vo);

    /**
     * 删除主动更新起价城市
     *
     * @param vo 主动更新起价城市参数，包含要删除的城市标识
     */
    void autoUpdateLowestPriceCityDel(AutoUpdateLowestPriceCityVo vo);

    /**
     * 查询主动更新起价城市列表（分页）
     *
     * @param vo 主动更新起价城市查询参数，包含分页和筛选条件
     * @return 返回分页的主动更新起价城市响应数据
     */
    PaginationSupportDTO<AutoUpdateLowestPriceCityResp> autoUpdateLowestPriceCityPage(AutoUpdateLowestPriceCityVo vo);

    /**
     * 导入主动更新起价城市
     *
     * @param file 导入的Excel文件，包含城市数据
     * @param language 语言设置，用于数据处理
     * @param operator 操作人员，用于记录操作信息
     * @return 返回导入操作的响应结果
     */
    Response<Object> importAutoUpdateLowestPriceCity(MultipartFile file, String language, String operator);

    /**
     * 初始化主动更新起价城市下酒店id到缓存任务
     *
     * @param param 任务参数，包含酒店ID等信息
     */
    void initCalculateCityAvgPriceScoreHotelIdToRedisTask(String param);
}
