package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceConfigEntity;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceConfigVo;

/**
 * 主动更新起价配置
 */
public interface AutoUpdateLowestPriceConfigService extends IService<AutoUpdateLowestPriceConfigEntity> {

    /**
     * 编辑起价任务配置
     *
     * @param vo 自动更新最低价格配置参数，包含配置信息
     */
    void saveAutoUpdateLowestPriceConfig(AutoUpdateLowestPriceConfigVo vo);

    /**
     * 查询起价任务配置
     *
     * @return 返回自动更新最低价格配置实体信息
     */
    AutoUpdateLowestPriceConfigEntity queryAutoUpdateLowestPriceConfig();
}
