package com.tiangong.hotel.service;

import com.tiangong.hotel.domain.BaseinfoAreadataEntity;
import com.tiangong.hotel.domain.BaseinfoHotCityEntity;
import com.tiangong.hotel.domain.req.AddOrUpdateHotCityReq;
import com.tiangong.hotel.req.QueryAreaDataReq;
import com.tiangong.dto.hotel.HotCityResp;
import com.tiangong.hotel.resp.QueryCityResp;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/10/30 14:02
 */
public interface BaseinfoAreadataService {

    /**
     * 根据区域类型查询区域集合
     *
     * @param req 查询区域数据请求，包含区域类型等查询条件
     * @return 返回基础信息区域数据实体列表
     */
    List<BaseinfoAreadataEntity> queryAreaDataListByType(QueryAreaDataReq req);

    /**
     * 查询热门城市
     *
     * @param language 语言设置，用于多语言查询
     * @return 返回热门城市响应列表
     */
    List<HotCityResp> queryHotCityList(String language);

    /**
     * 查询热门城市（管理端）
     *
     * @return 返回基础信息热门城市实体列表
     */
    List<BaseinfoHotCityEntity> queryHotCityListManger();

    /**
     * 修改或新增热门城市
     *
     * @param req 新增或更新热门城市请求，包含城市信息
     */
    void addOrUpdateHotCity(AddOrUpdateHotCityReq req);

    /**
     * 根据类型删除区域数据
     *
     * @param areaType 区域类型，指定要删除的区域类型
     * @param language 语言设置，用于多语言处理
     */
    void delAreaDataByType(Integer areaType, String language);

    /**
     * 批量新增区域数据
     *
     * @param list 基础信息区域数据实体列表，要新增的区域数据
     * @param language 语言设置，用于多语言处理
     */
    void insertAreaData(List<BaseinfoAreadataEntity> list, String language);

    /**
     * 查询国家下面所有城市数据
     *
     * @param list 城市代码列表，指定要查询的城市
     * @param language 语言设置，用于多语言查询
     * @param areaName 区域名称，用于筛选条件
     * @return 返回查询城市响应列表
     */
    List<QueryCityResp> queryCity(List<String> list, String language, String areaName);
}
