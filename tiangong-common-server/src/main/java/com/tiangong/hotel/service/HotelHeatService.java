package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.HotelHeatEntity;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelHeatResp;

import java.util.List;

/**
 * 酒店热度表
 */
public interface HotelHeatService extends IService<HotelHeatEntity> {

    /**
     * 查询酒店热度列表（分页）
     *
     * @param req 酒店热度请求，包含分页查询条件
     * @return 返回分页的酒店热度响应数据
     */
    PaginationSupportDTO<HotelHeatResp> queryHotelHeatPage(HotelHeatReq req);

    /**
     * 修改热度分数
     *
     * @param reqList 酒店热度请求列表，包含要更新的热度分数信息
     */
    void updateHotelHeatScore(List<HotelHeatReq> reqList);

    /**
     * 同步增量酒店热度分数到mongodb任务
     *
     * @param param 任务参数，包含同步所需的参数信息
     */
    void syncIncrementHotelHeatScoreToMongodbTask(String param);

    /**
     * 查询酒店热度表中所有酒店id列表（分页）
     *
     * @param req 酒店热度请求，包含分页查询条件
     * @return 返回酒店ID列表
     */
    List<String> queryAllHotelHeatHotelIdPage(HotelHeatReq req);
}

