package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.BaseinfoFacility;
import com.tiangong.hotel.domain.req.FacilityAllReq;
import com.tiangong.hotel.domain.req.FacilityEditReq;
import com.tiangong.hotel.domain.req.FacilityListReq;
import com.tiangong.hotel.domain.req.FacilityScoreEditReq;
import com.tiangong.hotel.domain.resp.FacilityInfoAndConfigResp;
import com.tiangong.hotel.domain.resp.FacilityNameResp;
import com.tiangong.hotel.domain.resp.FacilityResp;

public interface BaseinfoFacilityService extends IService<BaseinfoFacility> {

    /**
     * 新增或修改设施
     *
     * @param baseinfoFacility 基础信息设施对象，包含设施信息
     */
    void addOrUpdateFacility(BaseinfoFacility baseinfoFacility);

    /**
     * 查询设施信息
     *
     * @return 返回基础信息设施对象
     */
    BaseinfoFacility queryFacility();

    /**
     * 查询酒店设施管理信息
     *
     * @return 返回设施信息和配置响应数据
     */
    FacilityInfoAndConfigResp queryFacilityManage();

    /**
     * 查询格式化设施列表
     *
     * @param req 设施列表查询请求，包含查询条件
     * @return 返回设施响应数据
     */
    FacilityResp queryFacilityList(FacilityListReq req);

    /**
     * 编辑设施
     * @param req
     */
    void facilityEdit(FacilityEditReq req);

    /**
     * 分类查询设施
     * @param req
     * @return
     */
    PaginationSupportDTO<FacilityNameResp> facilityPage(FacilityAllReq req);

    /**
     * 修改设施分数
     * @param req
     */
    void facilityScoreEdit(FacilityScoreEditReq req);

}
