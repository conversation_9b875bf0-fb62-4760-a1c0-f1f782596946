package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.HotelLabelConfigEntity;
import com.tiangong.hotel.req.HotelLabelConfigReq;
import com.tiangong.hotel.resp.HotelLabelConfigLogResp;
import com.tiangong.hotel.resp.HotelLabelConfigResp;

import java.util.List;

/**
 * 酒店标签配置
 */
public interface HotelLabelConfigService extends IService<HotelLabelConfigEntity>  {

    /**
     * 新增酒店标签配置
     *
     * @param req 酒店标签配置请求，包含新增的标签配置信息
     */
    void hotelLabelConfigAdd(HotelLabelConfigReq req);

    /**
     * 酒店标签配置列表（分页）
     *
     * @param req 酒店标签配置请求，包含分页查询条件
     * @return 返回分页的酒店标签配置响应数据
     */
    PaginationSupportDTO<HotelLabelConfigResp> hotelLabelConfigPage(HotelLabelConfigReq req);

    /**
     * 酒店标签配置列表
     *
     * @param req 酒店标签配置请求，包含查询条件
     * @return 返回酒店标签配置响应列表
     */
    List<HotelLabelConfigResp> hotelLabelConfigList(HotelLabelConfigReq req);

    /**
     * 编辑酒店标签配置
     *
     * @param req 酒店标签配置请求，包含要编辑的标签配置信息
     */
    void hotelLabelConfigEdit(HotelLabelConfigReq req);

    /**
     * 删除酒店标签配置
     *
     * @param req 酒店标签配置请求，包含要删除的标签配置标识
     */
    void hotelLabelConfigDel(HotelLabelConfigReq req);

    /**
     * 校验酒店标签配置
     *
     * @param req 酒店标签配置请求，包含校验参数
     * @return 返回校验结果状态码
     */
    int checkHotelLabelConfig(HotelLabelConfigReq req);

    /**
     * 酒店标签配置日志列表（分页）
     *
     * @param req 酒店标签配置请求，包含日志查询和分页条件
     * @return 返回分页的酒店标签配置日志响应数据
     */
    PaginationSupportDTO<HotelLabelConfigLogResp> hotelLabelConfigLogPage(HotelLabelConfigReq req);

    /**
     * 查询全部的标签
     * @return
     */
    List<HotelLabelConfigEntity> selectAllHotelLabel();
}
