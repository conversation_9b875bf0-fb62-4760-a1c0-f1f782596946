package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.HotelPopularCityDTO;
import com.tiangong.dto.hotel.HotelPopularCityVO;
import com.tiangong.hotel.domain.HotelPopularCityEntity;
import com.tiangong.hotel.domain.req.HotelPopularCityReq;
import com.tiangong.hotel.domain.resp.HotelPopularCityResp;

import java.util.List;

/**
 * 热门城市
 */
public interface HotelPopularCityService extends IService<HotelPopularCityEntity> {

    /**
     * 添加热门城市
     *
     * @param req 酒店热门城市请求，包含要添加的热门城市信息
     */
    void hotelPopularCityAdd(HotelPopularCityReq req);

    /**
     * 删除热门城市
     *
     * @param req 酒店热门城市请求，包含要删除的热门城市标识
     */
    void hotelPopularCityDel(HotelPopularCityReq req);

    /**
     * 热门城市列表
     *
     * @param req 酒店热门城市请求，包含查询条件
     * @return 返回酒店热门城市响应列表
     */
    List<HotelPopularCityResp> hotelPopularCityList(HotelPopularCityReq req);

    /**
     * 热门城市列表（分页）
     *
     * @param req 酒店热门城市请求，包含分页查询条件
     * @return 返回分页的酒店热门城市响应数据
     */
    PaginationSupportDTO<HotelPopularCityResp> hotelPopularCityPage(HotelPopularCityReq req);

    /**
     * 热门城市列表
     *
     * @param vo 酒店热门城市VO，包含查询条件
     * @return 返回酒店热门城市DTO列表
     */
    List<HotelPopularCityDTO> queryHotelPopularCityList(HotelPopularCityVO vo);
}

