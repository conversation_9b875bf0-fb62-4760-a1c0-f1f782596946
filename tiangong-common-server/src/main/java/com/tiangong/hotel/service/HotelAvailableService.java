package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.hotel.domain.HotelAvailablePO;
import com.tiangong.hotel.domain.req.DelHotelAvailableReq;
import com.tiangong.hotel.domain.req.QueryHotelAvailableReq;
import com.tiangong.hotel.domain.resp.HotelAvailableListResp;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/3 11:00
 */
public interface HotelAvailableService extends IService<HotelAvailablePO> {

    /**
     * 新增酒店可见性
     *
     * @param req 新增酒店可见性请求参数，包含酒店可见性配置信息
     */
    void addHotelAvailable(AddHotelAvailableReq req);

    /**
     * 删除酒店可见性
     *
     * @param req 删除酒店可见性请求参数，包含要删除的酒店可见性标识
     */
    void delHotelAvailable(DelHotelAvailableReq req);

    /**
     * 查询酒店可见性列表
     *
     * @param req 查询酒店可见性请求参数，包含查询条件
     * @return 返回分页的酒店可见性列表响应数据
     */
    PaginationSupportDTO<HotelAvailableListResp> queryHotelListAvailable(QueryHotelAvailableReq req);

    /**
     * 查询酒店是否是黑名单
     *
     * @param req 新增酒店可见性请求参数，包含酒店和客户信息
     * @return 返回酒店可见性DTO，包含黑名单状态信息
     */
    HotelAvailableDTO queryHotelAvailable(AddHotelAvailableReq req);

    /**
     * 查询客户酒店黑名单 酒店id列表
     *
     * @param req 新增酒店可见性请求参数，包含客户信息
     * @return 返回客户黑名单酒店ID列表
     */
    List<Long> queryAgentBlacklistHotelIds(AddHotelAvailableReq req);
}
