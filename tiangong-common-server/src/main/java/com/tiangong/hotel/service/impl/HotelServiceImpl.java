package com.tiangong.hotel.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.utils.IDUtilS;
import com.tiangong.common.Constant;
import com.tiangong.constant.FieldConstants;
import com.tiangong.common.Response;
import com.tiangong.config.SettingsConstant;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.ProductRemote;
import com.tiangong.dto.common.*;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.FacilityPublicDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.product.request.AvailableHotelRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.response.HotelLowestPriceResponse;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzyRoomDTO;
import com.tiangong.fuzzyquery.mapper.FuzzyQueryMapper;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.constant.enums.HotelStarEnum;
import com.tiangong.hotel.domain.*;
import com.tiangong.hotel.domain.collection.HotelLanguageCollection;
import com.tiangong.hotel.domain.collection.HotelPublicCollection;
import com.tiangong.hotel.domain.collection.LowestPriceDTO;
import com.tiangong.hotel.domain.req.QueryGroupReq;
import com.tiangong.hotel.domain.resp.CityResp;
import com.tiangong.hotel.domain.resp.SelectCityListReq;
import com.tiangong.hotel.domain.vo.*;
import com.tiangong.hotel.dto.BaseinfoAreadataDTO;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;
import com.tiangong.hotel.mapper.*;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.req.QueryAreaDataReq;
import com.tiangong.hotel.service.*;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.mapper.AreaDataMapper;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.table.TXTable;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.geo.Metrics;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.NearQuery;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class HotelServiceImpl implements HotelService {

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private BaseinfoAreadataService baseinfoAreadataService;

    @Autowired
    private BaseinfoRegionService baseinfoRegionService;

    @Resource
    private AreaDataMapper areaDataMapper;

    @Autowired
    private BaseinfoGroupService baseinfoGroupService;

    @Resource
    private HotCityClickMapper hotCityClickMapper;

    @Autowired
    private HotelHeatMapper hotelHeatMapper;

    @Autowired
    private HotelLabelConfigMapper hotelLabelConfigMapper;

    @Autowired
    private FuzzyQueryMapper fuzzyQueryMapper;

    @Autowired
    private HotelHeatService hotelHeatService;

    @Autowired
    @Qualifier("mongoTemplateBasic")
    private MongoTemplate mongoTemplateBasic;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor executor;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;

    @Autowired
    private HotelRecommendMapper hotelRecommendMapper;

    @Autowired
    private FacilityConfigMapper facilityConfigMapper;

    @Autowired
    private FacilityMapper facilityMapper;

    @Autowired
    private ProductRemote productRemote;

    /**
     * 静态变量，避免重复创建
     */
    private static final String[] FACILITY_KEYS = {"swimmingPool", "fitnessCenter", "spaAndWellnessCenter", "restaurant", "hourFrontDesk_24",
            "bar", "freeBreakfast", "parkingLot", "freeWiFi", "petFriendly", "chargingStation",
            "currencyExchange", "meetingRoom", "laundryFacilities", "shuttleService", "luggageStorage"};
    private static final String[] FACILITY_CODES = {"FC1001", "FC1002", "FC1003", "FC1004", "FC1005", "FC1006", "FC1007", "FC1008",
            "FC1009", "FC1010", "FC1011", "FC1012", "FC1013", "FC1014", "FC1015", "FC1016"};

    /**
     * 静态变量，避免重复创建
     */
    private static final JSONObject HOT_FACILITY_DEFAULT = new JSONObject();

    /**
     * 关键词
     */
    private static final String FIELD_KEYWORD = "keyWord";

    /**
     * JSON响应字段名
     */
    private static final String JSON_FIELD_RESULT = "result";
    /**
     * JSON响应
     */
    private static final String JSON_FIELD_DATA = "data";

    /**
     * 酒店设施编码和名称映射
     */
    private static final String FACILITY_CODE_FC1001 = "FC1001";
    private static final String FACILITY_NAME_SWIMMING_POOL = "swimmingPool";
    private static final String FACILITY_CODE_FC1002 = "FC1002";
    private static final String FACILITY_NAME_FITNESS_CENTER = "fitnessCenter";
    private static final String FACILITY_CODE_FC1003 = "FC1003";
    private static final String FACILITY_NAME_SPA_WELLNESS = "spaAndWellnessCenter";
    private static final String FACILITY_CODE_FC1004 = "FC1004";
    private static final String FACILITY_NAME_RESTAURANT = "restaurant";
    private static final String FACILITY_CODE_FC1005 = "FC1005";
    private static final String FACILITY_NAME_24H_FRONT_DESK = "hourFrontDesk_24";
    private static final String FACILITY_CODE_FC1006 = "FC1006";
    private static final String FACILITY_NAME_BAR = "bar";
    private static final String FACILITY_CODE_FC1007 = "FC1007";
    private static final String FACILITY_NAME_FREE_BREAKFAST = "freeBreakfast";
    private static final String FACILITY_CODE_FC1008 = "FC1008";
    private static final String FACILITY_NAME_PARKING_LOT = "parkingLot";
    private static final String FACILITY_CODE_FC1009 = "FC1009";
    private static final String FACILITY_NAME_FREE_WIFI = "freeWiFi";
    private static final String FACILITY_CODE_FC1010 = "FC1010";
    private static final String FACILITY_NAME_PET_FRIENDLY = "petFriendly";
    private static final String FACILITY_CODE_FC1011 = "FC1011";
    private static final String FACILITY_NAME_CHARGING_STATION = "chargingStation";
    private static final String FACILITY_CODE_FC1012 = "FC1012";
    private static final String FACILITY_NAME_CURRENCY_EXCHANGE = "currencyExchange";
    private static final String FACILITY_CODE_FC1013 = "FC1013";
    private static final String FACILITY_NAME_MEETING_ROOM = "meetingRoom";
    private static final String FACILITY_CODE_FC1014 = "FC1014";
    private static final String FACILITY_NAME_LAUNDRY_FACILITIES = "laundryFacilities";
    private static final String FACILITY_CODE_FC1015 = "FC1015";
    private static final String FACILITY_NAME_SHUTTLE_SERVICE = "shuttleService";
    private static final String FACILITY_CODE_FC1016 = "FC1016";
    private static final String FACILITY_NAME_LUGGAGE_STORAGE = "luggageStorage";
    private static final Integer FACILITY_TYPE = 1;

    /**
     * 地理坐标角度常量
     */
    private static final int COORDINATE_ANGLE_90 = 90;
    private static final int COORDINATE_ANGLE_180 = 180;
    /**
     * 价格由低到高  价格为空 排在最后
     */
    private static final Integer SORT_PRICE_TYPE_LOW_TO_HIGH = 2;
    /**
     * 价格由高到低 排序 价格为空 排在最后
     */
    private static final Integer SORT_PRICE_TYPE_HIGH_TO_LOW = 3;

    private static final String FIELD_CITY_CODE = "cityCode";

    private static final String FIELD_DATA_TYPE = "dataType";

    private static final String RESPONSE_SUCCESS_CODE = "20000";
    static {
        HOT_FACILITY_DEFAULT.put("swimmingPool", 0);
        HOT_FACILITY_DEFAULT.put("fitnessCenter", 0);
        HOT_FACILITY_DEFAULT.put("spaAndWellnessCenter", 0);
        HOT_FACILITY_DEFAULT.put("restaurant", 0);
        HOT_FACILITY_DEFAULT.put("hourFrontDesk_24", 0);
        HOT_FACILITY_DEFAULT.put("bar", 0);
        HOT_FACILITY_DEFAULT.put("freeBreakfast", 0);
        HOT_FACILITY_DEFAULT.put("parkingLot", 0);
        HOT_FACILITY_DEFAULT.put("freeWiFi", 0);
        HOT_FACILITY_DEFAULT.put("petFriendly", 0);
        HOT_FACILITY_DEFAULT.put("chargingStation", 0);
        HOT_FACILITY_DEFAULT.put("currencyExchange", 0);
        HOT_FACILITY_DEFAULT.put("meetingRoom", 0);
        HOT_FACILITY_DEFAULT.put("laundryFacilities", 0);
        HOT_FACILITY_DEFAULT.put("shuttleService", 0);
        HOT_FACILITY_DEFAULT.put("luggageStorage", 0);
    }

    /**
     * 缓存热门设施配置
     */
    private Map<String, FacilityConfigEntity> facilityConfigCache;

    @Override
    public DistrictOrBusinessResp queryBusinessAndDistrictList(DistrictOrBusinessReq req) {
        DistrictOrBusinessResp resp = new DistrictOrBusinessResp();
        JSONObject hotelReq = new JSONObject();
        if (StringUtils.isEmpty(req.getLanguage())) {
            hotelReq.put(FieldConstants.LANGUAGE_TYPE, LanguageTypeEnum.en_US.getValue());
        } else {
            hotelReq.put(FieldConstants.LANGUAGE_TYPE, req.getLanguage());
        }
        if (!StringUtils.isEmpty(req.getDistrictName())) {
            hotelReq.put(FieldConstants.KEY_WORD, req.getDistrictName());
        }
        hotelReq.put("parentCode", req.getCityCode());
        // 4行政区 5商业区
        hotelReq.put("dataType", 4);
        hotelReq.put(CommonConstants.PARAM_CURRENT_PAGE, "1");
        hotelReq.put(CommonConstants.PARAM_PAGE_SIZE, "500");
        log.info("查询行政区参数是：{}", hotelReq);
        String hotelBase = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.BUSINESS_AND_DISTRICT_LIST, hotelReq.toJSONString());
        JSONObject jsonObject = JSONObject.parseObject(hotelBase);
        log.info("查询行政区返回的数据是：{}", jsonObject);
        if (jsonObject != null) {
            Integer result = jsonObject.getInteger("result");
            if (result == 1) {
                JSONArray data = jsonObject.getJSONArray("data");
                resp.setDistrictList(data);
            }
        }

        //商业区
        if (!StringUtils.isEmpty(req.getBusinessZoneName())) {
            hotelReq.put(FIELD_KEYWORD, req.getBusinessZoneName());
        }
        hotelReq.put("parentCode", req.getCityCode());
        // 4行政区 5商业区
        hotelReq.put("dataType", 5);
        hotelReq.put(CommonConstants.PARAM_CURRENT_PAGE, "1");
        hotelReq.put(CommonConstants.PARAM_PAGE_SIZE, "500");
        log.info("查询商业区参数是：{}", hotelReq);
        hotelBase = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.BUSINESS_AND_DISTRICT_LIST, hotelReq.toJSONString());
        jsonObject = JSONObject.parseObject(hotelBase);
        log.info("查询商业区返回的数据是：{}", jsonObject);
        if (jsonObject != null) {
            Integer result = jsonObject.getInteger("result");
            if (result == 1) {
                JSONArray data = jsonObject.getJSONArray("data");
                resp.setBusinessList(data);
            }
        }
        return resp;
    }

    @Override
    public List<BaseinfoAreadataDTO> queryCountryList(CountryReq req) {
        QueryAreaDataReq queryAreaDataReq = new QueryAreaDataReq();
        queryAreaDataReq.setLanguage(req.getLanguage());
        queryAreaDataReq.setAreaName(req.getCountryName());
        queryAreaDataReq.setAreaCode(req.getCountryCode());
        // 标识编码需要模糊查询
        queryAreaDataReq.setCodeLike(req.getCodeLike());
        queryAreaDataReq.setAreaType(1);
        List<BaseinfoAreadataEntity> list = baseinfoAreadataService.queryAreaDataListByType(queryAreaDataReq);
        if (CollUtilX.isNotEmpty(list)) {
            return CommonDtoConvert.INSTANCE.baseinfoAreadataDTOConvert(list);
        }
        return null;
    }

    @Override
    public List<CityResp> queryAllCityList(com.tiangong.hotel.domain.req.CityReq req) {
        if (StringUtils.isEmpty(req.getCityName())) {
            return null;
        }

        String language = LanguageTypeEnum.zh_CN.getValue();
        if (!StringUtils.isEmpty(req.getLanguage())) {
            language = req.getLanguage();
        }
        if (req.getSupplyType() == null) {
            req.setSupplyType(1);
        }
        // 城市名称为空时，直接返回空数组
        if (StringUtils.isEmpty(req.getCityName())) {
            return new ArrayList<>();
        }
        JSONObject object = new JSONObject();
        object.put(FieldConstants.LANGUAGE_TYPE, language);
        object.put("keyWord", req.getCityName());
        object.put("innerOutFlag", req.getSupplyType());

        String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.SELECT_CITY_LIST_BY_CITY_NAME, JSON.toJSONString(object));
        log.info("查询城市结构数据，返回的数据是：{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getInteger(JSON_FIELD_RESULT) == 1) {
            if (CollUtilX.isNotEmpty(jsonObject.getJSONArray(JSON_FIELD_DATA))) {
                return JSON.parseObject(jsonObject.getJSONArray(JSON_FIELD_DATA).toJSONString(), new TypeReference<List<CityResp>>() {
                });
            }
            return new ArrayList<>();
        } else {
            log.error("获取数据失败----result={}", result);
            throw new SysException(ErrorCodeEnum.OUTER_IF_EXCEPTION);
        }
    }

    @Override
    public List<BaseinfoRegionDTO> getRegion() {
        List<BaseinfoRegionEntity> list = baseinfoRegionService.list(new LambdaQueryWrapper<BaseinfoRegionEntity>().orderByAsc(BaseinfoRegionEntity::getKeyword));
        if (CollUtilX.isEmpty(list)) {
            return null;
        }
        return CommonDtoConvert.INSTANCE.baseinfoRegionDTOConvert(list);
    }

    @Override
    public List<HotCityResp> queryCityAndHotelByKeyword(SearchCityAndHotelReq req) {
        // 语言必穿
        if (StringUtils.isEmpty(req.getLanguage())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        JSONObject hotelReq = new JSONObject();
        hotelReq.put(FieldConstants.LANGUAGE_TYPE, req.getLanguage());
        hotelReq.put(FieldConstants.KEY_WORD, req.getKeyWord());
        hotelReq.put(FieldConstants.CITY_CODE, req.getCityCode());

        log.info("查询城市和酒店传递的参数是：{}", hotelReq);
        String hotelBase = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.QUERY_HOTEL_AND_CITY, hotelReq.toJSONString());
        log.info("返回的数据是：{}", hotelBase);
        JSONObject object = JSONObject.parseObject(hotelBase);
        if (object != null) {
            Integer result = object.getInteger("result");
            if (result == 1) {
                JSONObject data = object.getJSONObject("data");
                JSONArray array = data.getJSONArray("list");
                List<HotCityResp> list = new ArrayList<>();
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    HotCityResp resp = new HotCityResp();
                    resp.setCityCode(jsonObject.getString(FieldConstants.CITY_CODE));
                    resp.setCityName(jsonObject.getString("cityName"));
                    resp.setLatitude(jsonObject.getBigDecimal("latBaidu"));
                    resp.setLongitude(jsonObject.getBigDecimal("lngBaidu"));
                    resp.setLatGoogle(jsonObject.getBigDecimal("latGoogle"));
                    resp.setLngGoogle(jsonObject.getBigDecimal("lngGoogle"));
                    resp.setHotelId(jsonObject.getLong("hotelId"));
                    resp.setHotelName(jsonObject.getString("hotelName"));
                    resp.setCountryCode(jsonObject.getString("countryCode"));
                    resp.setCountryName(jsonObject.getString("countryName"));
                    resp.setProvinceCode(jsonObject.getString("provinceCode"));
                    resp.setProvinceName(jsonObject.getString("provinceName"));
                    if (!AreaTypesEnum.INLAND.getValue().equals(resp.getCountryCode())) {
                        resp.setSupplyType(AreaTypesEnum.OUTLAND.getKey());
                    } else {
                        resp.setSupplyType(AreaTypesEnum.INLAND.getKey());
                    }
                    resp.setTimeZone(jsonObject.getString("timeZone"));
                    list.add(resp);
                }
                return list;
            } else {
                throw new SysException(ErrorCodeEnum.QUERY_FAIL);
            }
        } else {
            throw new SysException(ResultEnum.E_9999.code, ResultEnum.E_9999.message);
        }
    }

    @Override
    public void updateTimeZone(UpdateTimeZoneDTO updateTimeZoneDTO) {
        for (LanguageTypeEnum languageTypeEnum : LanguageTypeEnum.values()) {
            updateTimeZoneDTO.setTXTable(TXTable.getAreaTXTable(languageTypeEnum.getValue()));
            areaDataMapper.updateAreaTimeZeroByCityCode(updateTimeZoneDTO);
        }
        //通知基础信息修改时区
        String req = JSON.toJSONString(updateTimeZoneDTO);
        String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.UPDATE_CITY_TIME_ZONE, req);
        log.info("更新结果：req={}，resp={}", req, result);
    }

    @Override
    public QueryCityTimeZoneResp queryCityTimeZone(QueryCityTimeZoneReq req) {
        req.setTXTable(TXTable.getAreaTXTable(null));
        return areaDataMapper.queryCityTimeZone(req);
    }

    @Override
    public Object selectCityList(SelectCityListReq req) {
        String language = LanguageTypeEnum.zh_CN.getValue();
        if (!StringUtils.isEmpty(req.getLanguage())) {
            language = req.getLanguage();
        }
        // 城市名称为空时，直接返回空数组
        if (StringUtils.isEmpty(req.getCityName())) {
            return Response.success(new ArrayList<>());
        }
        JSONObject object = new JSONObject();
        object.put(FieldConstants.LANGUAGE_TYPE, language);
        object.put("keyWord", req.getCityName());
        if (!StringUtils.isEmpty(req.getCountryCode())) {
            object.put("countryCode", req.getCountryCode());
        }
        String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.SELECT_CITY_LIST, JSON.toJSONString(object));
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject.getInteger(JSON_FIELD_RESULT) == 1) {
            return jsonObject.getJSONArray(JSON_FIELD_DATA);
        } else {
            log.error("获取城市数据失败----result={}", result);
            throw new SysException(ErrorCodeEnum.OUTER_IF_EXCEPTION);
        }
    }

    @Override
    public List<BaseinfoGroupPO> queryGroupList(QueryGroupReq req) {
        LambdaQueryWrapper<BaseinfoGroupPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseinfoGroupPO::getLanguage, req.getLanguage());
        if (!StringUtils.isEmpty(req.getGroupName())) {
            wrapper.like(BaseinfoGroupPO::getGroupName, req.getGroupName());
            wrapper.groupBy(BaseinfoGroupPO::getGroupId);
            wrapper.select(BaseinfoGroupPO::getGroupName, BaseinfoGroupPO::getGroupId);
            wrapper.orderByDesc(BaseinfoGroupPO::getCreatedDt);
        }
        if (!StringUtils.isEmpty(req.getBrandName())) {
            wrapper.like(BaseinfoGroupPO::getBrandName, req.getBrandName());
            wrapper.select(BaseinfoGroupPO::getBrandId, BaseinfoGroupPO::getBrandName);
            wrapper.orderByDesc(BaseinfoGroupPO::getCreatedDt);
        }
        return baseinfoGroupService.list(wrapper);
    }

    @Override
    public void addHotCityClick(HotCityClickDTO req) {
        HotCityClickEntity hotCityClickEntity = hotCityClickMapper.selectOne(new LambdaQueryWrapper<HotCityClickEntity>().eq(HotCityClickEntity::getCityCode, req.getCityCode()).last(" limit 1"));
        if (hotCityClickEntity == null) {
            hotCityClickEntity = CommonDtoConvert.INSTANCE.hotCityClickEntityConvert(req);
            hotCityClickEntity.setHotNum(1);
            hotCityClickMapper.insert(hotCityClickEntity);
        } else {
            hotCityClickEntity.setHotNum(hotCityClickEntity.getHotNum() + 1);
            hotCityClickMapper.updateById(hotCityClickEntity);
        }
    }

    @Override
    public HotelInfoCollectionDTO queryHotelInfo(HotelInfoCollectionReq req) {
        String language = LanguageTypeEnum.en_US.getValue();
        if (!StringUtils.isEmpty(req.getLanguageType())) {
            language = req.getLanguageType();
        }

        while (true) {
            String request = JSONUtil.toJsonStr(req);
            String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.HOTEL_BASE_INFO, request);
            if (StrUtilX.isEmpty(result)) {
                log.error("获取酒店信息异常返回结果为空，req={}", request);
                return null;
            }
            ResultX<List<HotelInfoCollectionDTO>> resultX = JSON.parseObject(result, new TypeReference<ResultX<List<HotelInfoCollectionDTO>>>() {
            });
            if (resultX.isError()) {
                log.error("获取酒店信息异常，返回失败，req={}，resp={}", request, result);
                return null;
            }
            if (CollUtilX.isEmpty(resultX.getData())) {
                log.error("获取酒店信息返回结果为空，req={}，resp={}", request, result);
                if (!LanguageTypeEnum.en_US.getValue().equals(language)) {
                    // 切换到英文并继续循环
                    language = LanguageTypeEnum.en_US.getValue();
                    req.setLanguageType(language);
                    continue;
                }
                return null;
            }
            // 设置热门设施
            setHotFacility(resultX.getData().get(0));
            return resultX.getData().get(0);
        }
    }

    @Override
    public List<HotelInfoCollectionDTO> queryHotelInfoList(HotelInfoCollectionReq req) {
        String language = LanguageTypeEnum.en_US.getValue();
        if (!StringUtils.isEmpty(req.getLanguageType())) {
            language = req.getLanguageType();
        }

        while (true) {
            String request = JSONUtil.toJsonStr(req);
            String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.HOTEL_BASE_INFO, request);
            if (StrUtilX.isEmpty(result)) {
                log.error("获取酒店信息异常，req={}", request);
                return null;
            }
            ResultX<List<HotelInfoCollectionDTO>> resultX = JSON.parseObject(result, new TypeReference<ResultX<List<HotelInfoCollectionDTO>>>() {
            });
            if (resultX.isError()) {
                log.error("获取酒店信息异常，req={}，resp={}", request, result);
                return null;
            }
            if (CollUtilX.isEmpty(resultX.getData())) {
                log.error("获取酒店信息返回为空，req={}，resp={}", request, result);
                if (!LanguageTypeEnum.en_US.getValue().equals(language)) {
                    // 切换到英文并继续循环
                    language = LanguageTypeEnum.en_US.getValue();
                    req.setLanguageType(language);
                    continue;
                }
                return null;
            }
            return resultX.getData();
        }
    }

    /**
     * 设置酒店设施
     */
    private void setHotFacility(HotelInfoCollectionDTO hotelInfo) {
        List<FacilityPublicDTO> facilities = hotelInfo.getFacilities();
        Map<String, Integer> facilityMap = new HashMap<>();

        // 初始化热门设施状态为0
        for (String key : FACILITY_KEYS) {
            facilityMap.put(key, 0);
        }

        if (CollectionUtils.isNotEmpty(facilities)) {
            // 从缓存中获取热门设施配置
            if (facilityConfigCache == null) {
                facilityConfigCache = facilityConfigMapper.selectList(new LambdaQueryWrapper<FacilityConfigEntity>()
                                .eq(FacilityConfigEntity::getFacilityType, 1))
                        .stream()
                        .collect(Collectors.toMap(FacilityConfigEntity::getFacilityCode, Function.identity()));
            }

            for (FacilityPublicDTO facility : facilities) {
                if (facility.getStatus() == null || !"1".equals(facility.getStatus())) {
                    continue;
                }

                for (int j = 0; j < FACILITY_CODES.length; j++) {
                    FacilityConfigEntity config = facilityConfigCache.get(FACILITY_CODES[j]);
                    if (config != null && StringUtils.isNotEmpty(config.getFacilityIdList())) {
                        List<String> facilityIdList = Arrays.asList(config.getFacilityIdList().split(","));
                        if (facilityIdList.contains(String.valueOf(facility.getFacilityId()))) {
                            facilityMap.put(FACILITY_KEYS[j], 1);
                        }
                    }
                }
            }
        }
        hotelInfo.setFacilityMap(facilityMap);
    }

    /**
     * 清除设施配置缓存
     */
    @Override
    public void clearFacilityConfigCache() {
        if (facilityConfigCache != null) {
            facilityConfigCache = null;
        }
    }

    @Override
    public List<EsHotelDto> searchDestinationHotel(DestinationReq req) {
        if (CollUtilX.isEmpty(req.getDestinationIds()) && CollUtilX.isEmpty(req.getHotelIds()) && StrUtilX.isEmpty(req.getHotelName())
                && StrUtilX.isEmpty(req.getCountryCode()) && StrUtilX.isEmpty(req.getProvinceCode()) && StrUtilX.isEmpty(req.getCityCode())) {
            return new ArrayList<>();
        }
        // 构建查询目的地酒店请求参数
        buildSearchDestinationHotelReq(req);
        // 查询目的地
        List<DestinationDTO> dtoList = queryDestination(req);

        List<EsHotelDto> respList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(dtoList)) {
            for (DestinationDTO dto : dtoList) {
                if (CollUtilX.isEmpty(dto.getDestinationName())) {
                    continue;
                }
                for (DestinationDesc desc : dto.getDestinationName()) {
                    EsHotelDto esHotelDto = getEsHotelDto(dto, desc);
                    respList.add(esHotelDto);
                }
            }
        }
        return respList;
    }

    /**
     * 构建 EsHotelDto 对象
     */
    private static EsHotelDto getEsHotelDto(DestinationDTO dto, DestinationDesc desc) {
        EsHotelDto esHotelDto = new EsHotelDto();
        esHotelDto.setDestinationId(dto.getDestinationId());
        esHotelDto.setHotelId(desc.getHotelId());
        esHotelDto.setHotelName(desc.getName());
        esHotelDto.setCountryCode(desc.getCountryCode());
        esHotelDto.setCountryName(desc.getCountryName());
        esHotelDto.setProvinceCode(desc.getProvinceCode());
        esHotelDto.setProvinceName(desc.getProvinceName());
        esHotelDto.setCityCode(desc.getCityCode());
        esHotelDto.setCityName(desc.getCityName());
        return esHotelDto;
    }

    /**
     * 构建查询目的地酒店参数
     */
    private void buildSearchDestinationHotelReq(DestinationReq req) {
        req.setDestinationType(new Integer[]{1});
        // 基础信息接收的是keyWord
        req.setKeyWord(req.getHotelName());
        // 如果有传destinationIds，则设置dataSize为destinationIds的长度，否则默认为20
        if (CollUtilX.isNotEmpty(req.getDestinationIds())) {
            req.setDataSize(req.getDestinationIds().size());
        }
        // 如果有传hotelIds，则设置dataSize为hotelIds的长度，否则默认为20
        if (CollUtilX.isNotEmpty(req.getHotelIds())) {
            req.setDataSize(req.getHotelIds().size());
        }
        if (req.getDataSize() == 0) {
            req.setDataSize(20);
        }
    }

    @Override
    public List<EsCityDto> searchDestinationCity(DestinationReq req) {
        if (CollUtilX.isEmpty(req.getDestinationIds()) && StrUtilX.isEmpty(req.getCityName())) {
            return new ArrayList<>();
        }
        // 构建查询目的地城市请求参数
        buildSearchDestinationCityReq(req);
        // 查询目的地
        List<DestinationDTO> dtoList = queryDestination(req);

        List<EsCityDto> respList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(dtoList)) {
            for (DestinationDTO dto : dtoList) {
                if (CollUtilX.isEmpty(dto.getDestinationName())) {
                    continue;
                }
                for (DestinationDesc desc : dto.getDestinationName()) {
                    EsCityDto esCityDto = buildEsCityDto(dto, desc);
                    respList.add(esCityDto);
                }
            }
        }
        return respList;
    }

    /**
     * 构建 EsCityDto 对象
     */
    private static EsCityDto buildEsCityDto(DestinationDTO dto, DestinationDesc desc) {
        EsCityDto esCityDto = new EsCityDto();
        esCityDto.setDestinationId(dto.getDestinationId());
        esCityDto.setCityName(desc.getName());
        esCityDto.setCityCode(desc.getCityCode());
        esCityDto.setProvinceName(desc.getProvinceName());
        esCityDto.setProvinceCode(desc.getProvinceCode());
        esCityDto.setCountryName(desc.getCountryName());
        esCityDto.setCountryCode(desc.getCountryCode());
        return esCityDto;
    }

    /**
     * 构建查询目的地城市参数
     */
    private void buildSearchDestinationCityReq(DestinationReq req) {
        req.setDestinationType(new Integer[]{2});
        // 基础信息接收的是keyWord
        req.setKeyWord(req.getCityName());
        // 如果有传destinationIds，则设置dataSize为destinationIds的长度，否则默认为20
        if (CollUtilX.isNotEmpty(req.getDestinationIds())) {
            req.setDataSize(req.getDestinationIds().size());
        }
        if (req.getDataSize() == 0) {
            req.setDataSize(20);
        }
    }

    @Override
    public List<DestinationDTO> queryDestination(DestinationReq req) {
        try {
            String url = settingsConstant.getUrl() + BaseHotelInfoUrl.SEARCH_DESTINATION;
            String reqStr = JSONUtil.toJsonStr(req);
            String result = HttpUtilX.post(url, reqStr);
            if (StrUtilX.isEmpty(result)) {
                log.error("查询目的地信息失败，返回结果为空，url={}，req={}", url, reqStr);
            }
            ResultX<List<DestinationDTO>> resultX = JSONObject.parseObject(result, new TypeReference<ResultX<List<DestinationDTO>>>() {
            });
            if (resultX.isSuccess()) {
                return resultX.getData();
            }
            log.error("查询目的地信息失败，返回结果为失败，url={}，req={}，resp={}", url, reqStr, result);
        } catch (Exception e) {
            log.error("查询目的地信息异常", e);
        }
        return new ArrayList<>();
    }

    @Override
    public void updateHotelMongodbByHotelIdBatch(List<HotelPublicDTO> publicDTOS) {
        if (CollectionUtils.isEmpty(publicDTOS)) {
            return;
        }

        // 创建 BulkOperations 对象，使用 ORDERED 模式
        BulkOperations bulkOps = mongoTemplateBasic.bulkOps(BulkOperations.BulkMode.ORDERED, HotelPublicCollection.class);

        // 收集需要更新的酒店 ID
        List<Long> hotelIds = new ArrayList<>();
        for (HotelPublicDTO hotelPublicDTO : publicDTOS) {
            if (CollectionUtils.isNotEmpty(hotelPublicDTO.getHotelLabelConfigIds())) {
                hotelIds.add(hotelPublicDTO.getHotelId());
            }
        }

        // 批量查询酒店数据
        Query query = new Query(Criteria.where("_id").in(hotelIds));
        List<HotelPublicCollection> hotelPublicCollections = mongoTemplateBasic.find(query, HotelPublicCollection.class);
        Map<Long, HotelPublicCollection> hotelCollectionMap = new HashMap<>();
        for (HotelPublicCollection collection : hotelPublicCollections) {
            hotelCollectionMap.put(collection.getHotelId(), collection);
        }

        for (HotelPublicDTO hotelPublicDTO : publicDTOS) {
            if (CollectionUtils.isNotEmpty(hotelPublicDTO.getHotelLabelConfigIds())) {
                HotelPublicCollection hotelPublicCollection = hotelCollectionMap.get(hotelPublicDTO.getHotelId());

                // 数据不存在则跳过
                if (Objects.isNull(hotelPublicCollection)) {
                    continue;
                }

                // 获取待处理的标签配置列表
                List<HotelAndAgentCodeLabelDTO> incomingLabels = hotelPublicDTO.getHotelLabelConfigIds();
                if (CollectionUtils.isEmpty(incomingLabels)) {
                    continue;
                }

                // 使用 Map 优化数据操作（agentCode -> labelDTO）
                Map<String, HotelAndAgentCodeLabelDTO> labelMap = new HashMap<>();

                // 初始化现有数据到 Map（如果存在）
                List<HotelAndAgentCodeLabelDTO> existingLabels = hotelPublicCollection.getHotelAndAgentCodeLabelDTO();
                if (CollectionUtils.isNotEmpty(existingLabels)) {
                    existingLabels.forEach(dto -> labelMap.put(dto.getAgentCode(), dto));
                }

                // 批量处理标签更新
                boolean modified = false;
                for (HotelAndAgentCodeLabelDTO newLabel : incomingLabels) {
                    String agentCode = newLabel.getAgentCode();
                    HotelAndAgentCodeLabelDTO existingLabel = labelMap.get(agentCode);

                    // 判断是否需要更新（新增或修改）
                    if (existingLabel == null || isLabelEqual(existingLabel, newLabel)) {
                        labelMap.put(agentCode, newLabel);
                        modified = true;
                    }
                }

                // 仅当数据发生变更时添加到批量操作中
                if (modified) {
                    // 转换 Map 回 List 格式
                    List<HotelAndAgentCodeLabelDTO> updatedLabels = new ArrayList<>(labelMap.values());

                    // 构建单次更新操作
                    Query singleQuery = new Query(Criteria.where("_id").is(hotelPublicCollection.getHotelId()));
                    Update singleUpdate = new Update().set("hotelAndAgentCodeLabelDTO", updatedLabels);

                    // 添加到批量操作中
                    bulkOps.upsert(singleQuery, singleUpdate);
                }
            }
            // 执行批量操作
            bulkOps.execute();
        }
    }

    @Override
    public List<HotelLanguageCollection> findMongoDBHotelList(String hotelName) {
        DestinationReq req = new DestinationReq();
        req.setDestinationType(new Integer[]{1});
        // 基础信息接收的是keyWord
        req.setKeyWord(hotelName);
        if (req.getDataSize() == 0) {
            req.setDataSize(20);
        }
        List<DestinationDTO> dtoList = queryDestination(req);

        List<HotelLanguageCollection> respList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(dtoList)) {
            for (DestinationDTO dto : dtoList) {
                if (CollUtilX.isEmpty(dto.getDestinationName())) {
                    continue;
                }
                for (DestinationDesc desc : dto.getDestinationName()) {
                    HotelLanguageCollection hotelLanguageCollection = new HotelLanguageCollection();
                    hotelLanguageCollection.setHotelId(desc.getHotelId());
                    hotelLanguageCollection.setHotelName(desc.getName());
                    respList.add(hotelLanguageCollection);
                }
            }
        }
        return respList;

    }

    @Override
    public List<CountryProvinceCityDto> searchCountryProvinceCity(CountryProvinceCityReq countryProvinceCityReq) {
        DestinationReq req = new DestinationReq();
        req.setDestinationType(new Integer[]{2,9,10});
        // 基础信息接收的是keyWord
        req.setKeyWord(countryProvinceCityReq.getKeyword());
        if (req.getDataSize() == 0) {
            req.setDataSize(countryProvinceCityReq.getDataSize());
        }
        // 查询目的地
        List<DestinationDTO> dtoList = queryDestination(req);

        List<CountryProvinceCityDto> respList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(dtoList)) {
            for (DestinationDTO dto : dtoList) {
                if (CollUtilX.isEmpty(dto.getDestinationName())) {
                    continue;
                }
                for (DestinationDesc desc : dto.getDestinationName()) {
                    CountryProvinceCityDto countryProvinceCityDto = new CountryProvinceCityDto();
                    countryProvinceCityDto.setCountryProvinceCityCode(dto.getDataTypeId());
                    countryProvinceCityDto.setDataType(dto.getDataType());
                    String fullName = Stream.of(
                                    desc.getCityName(),
                                    desc.getProvinceName(),
                                    desc.getCountryName()
                            )
                            // 过滤 null
                            .filter(Objects::nonNull)
                            // 过滤空字符串
                            .filter(s -> !s.isEmpty())
                            // 用 ", " 连接非空元素
                            .collect(Collectors.joining("，"));


                    countryProvinceCityDto.setFullName(fullName);
                    respList.add(countryProvinceCityDto);
                }
            }
        }
        return respList;
    }

    @Override
    public String queryHotelInfoStrList(HotelInfoReq req) {
        String language = LanguageTypeEnum.en_US.getValue();
        if (!StringUtils.isEmpty(req.getLanguageType())) {
            language = req.getLanguageType();
        }

        while (true) {
            JSONObject hotelReq = new JSONObject();
            hotelReq.put(FieldConstants.LANGUAGE_TYPE, language);
            hotelReq.put(FieldConstants.HOTEL_IDS, req.getHotelIds());
            hotelReq.put(FieldConstants.SETTINGS, req.getSettings());

            String hotelBase = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.HOTEL_BASE_INFO, hotelReq.toJSONString());
            JSONObject object = JSONObject.parseObject(hotelBase);
            JSONArray data = object.getJSONArray(FieldConstants.DATA);

            if (data == null || data.isEmpty()) {
                if (!LanguageTypeEnum.en_US.getValue().equals(language)) {
                    // 切换到英文并继续循环
                    language = LanguageTypeEnum.en_US.getValue();
                    req.setLanguageType(language);
                    continue;
                }
                return null;
            } else {
                Map<Long, HotelPublicCollection> hotelPublic = getHotelPublic(req.getHotelIds());
                for (int i = 0; i < data.size(); i++) {
                    JSONObject jsonObject = data.getJSONObject(i);
                    Long hotelId = jsonObject.getLong("hotelId");
                    HotelPublicCollection hotelPublicCollection = hotelPublic.get(hotelId);
                    List<String> hotFacilityList = hotelPublicCollection != null ? hotelPublicCollection.getHotFacility() : new ArrayList<>();

                    JSONObject hotFacility = getHotFacility(hotFacilityList);
                    jsonObject.put("hotFacility", hotFacility);
                }
                return JSONUtil.toJsonStr(data);
            }
        }
    }

    @Override
    public List<FuzzyRoomDTO> queryRoomLikeName(FuzzyQueryDTO req) {
        if (null == req.getHotelId()) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_HOTELID);
        }
        // 查询酒店基本信息
        HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
        hotelInfoReq.setHotelIds(Collections.singletonList(req.getHotelId()));
        hotelInfoReq.setLanguageType(req.getLanguage());
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.location);
        settings.add(BaseHotelInfoUrl.rooms);
        hotelInfoReq.setSettings(settings);
        HotelInfoCollectionDTO dto = this.queryHotelInfo(hotelInfoReq);
        if (dto == null || CollUtilX.isEmpty(dto.getRooms())) {
            return Collections.emptyList();
        }
        if (StrUtilX.isNotEmpty(req.getRoomName())) {
            return dto.getRooms().stream().filter(item -> item.getRoomtypeName().contains(req.getRoomName())).map(item -> {
                FuzzyRoomDTO fuzzyRoomDTO = new FuzzyRoomDTO();
                fuzzyRoomDTO.setHotelId(req.getHotelId());
                fuzzyRoomDTO.setRoomId(item.getRoomtypeId().intValue());
                fuzzyRoomDTO.setRoomName(item.getRoomtypeName());
                fuzzyRoomDTO.setMaxChildAge(String.valueOf(item.getMinChildAge()));
                fuzzyRoomDTO.setMaxAdultQty(item.getMaxGrown());
                fuzzyRoomDTO.setMaxChildQty(item.getMaxChild());
                return fuzzyRoomDTO;
            }).collect(Collectors.toList());
        }
        return dto.getRooms().stream().map(item -> {
            FuzzyRoomDTO fuzzyRoomDTO = new FuzzyRoomDTO();
            fuzzyRoomDTO.setHotelId(req.getHotelId());
            fuzzyRoomDTO.setRoomId(item.getRoomtypeId().intValue());
            fuzzyRoomDTO.setRoomName(item.getRoomtypeName());
//            fuzzyRoomDTO.setBedTypes();
            fuzzyRoomDTO.setMaxChildAge(String.valueOf(item.getMinChildAge()));
            fuzzyRoomDTO.setMaxAdultQty(item.getMaxGrown());
            fuzzyRoomDTO.setMaxChildQty(item.getMaxChild());
            return fuzzyRoomDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取最热门设施
     */
    private JSONObject getHotFacility(List<String> facilityList) {
        if (CollectionUtils.isEmpty(facilityList)) {
            return new JSONObject(HOT_FACILITY_DEFAULT);
        }

        JSONObject hotFacility = new JSONObject(HOT_FACILITY_DEFAULT);
        Set<String> facilitySet = new HashSet<>(facilityList);

        if (facilitySet.contains(FACILITY_CODE_FC1001)) {
            hotFacility.put(FACILITY_NAME_SWIMMING_POOL, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1002)) {
            hotFacility.put(FACILITY_NAME_FITNESS_CENTER, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1003)) {
            hotFacility.put(FACILITY_NAME_SPA_WELLNESS, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1004)) {
            hotFacility.put(FACILITY_NAME_RESTAURANT, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1005)) {
            hotFacility.put(FACILITY_NAME_24H_FRONT_DESK, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1006)) {
            hotFacility.put(FACILITY_NAME_BAR, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1007)) {
            hotFacility.put(FACILITY_NAME_FREE_BREAKFAST, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1008)) {
            hotFacility.put(FACILITY_NAME_PARKING_LOT, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1009)) {
            hotFacility.put(FACILITY_NAME_FREE_WIFI, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1010)) {
            hotFacility.put(FACILITY_NAME_PET_FRIENDLY, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1011)) {
            hotFacility.put(FACILITY_NAME_CHARGING_STATION, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1012)) {
            hotFacility.put(FACILITY_NAME_CURRENCY_EXCHANGE, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1013)) {
            hotFacility.put(FACILITY_NAME_MEETING_ROOM, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1014)) {
            hotFacility.put(FACILITY_NAME_LAUNDRY_FACILITIES, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1015)) {
            hotFacility.put(FACILITY_NAME_SHUTTLE_SERVICE, FACILITY_TYPE);
        }
        if (facilitySet.contains(FACILITY_CODE_FC1016)) {
            hotFacility.put(FACILITY_NAME_LUGGAGE_STORAGE, FACILITY_TYPE);
        }

        return hotFacility;
    }

    /**
     * 获取酒店公共信息
     */
    public Map<Long, HotelPublicCollection> getHotelPublic(List<Long> hotelIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(hotelIds));
        query.fields().include("hotFacility");
        List<HotelPublicCollection> hotelPublicCollectionList = mongoTemplateBasic.find(query, HotelPublicCollection.class, "hotelPublicCollection");
        return hotelPublicCollectionList.stream()
                .collect(Collectors.toMap(HotelPublicCollection::getHotelId, Function.identity(), (s1, s2) -> s1));
    }


    /**
     * 获取酒店公共信息
     */
    @Deprecated
    public IPage<HotelPublicResp> findHotelIds(HotelPageReq hotelPageReq) {
        IPage<HotelPublicResp> iPage = new Page<>(hotelPageReq.getCurrentPage(), hotelPageReq.getPageSize());
        AddressDocVo addressById = getAddressById(hotelPageReq.getDestinationId());
        if (Objects.isNull(addressById)) {
            throw new SysException(ErrorCodeEnum.ADDRESS_BY_ID_IS_ERROR);
        }

        if (addressById.getDataType().intValue() == AddressEnum.PROVINCE.getKey() || addressById.getDataType().intValue() == AddressEnum.COUNTRY.getKey()) {
            throw new SysException(ErrorCodeEnum.COUNTRY_PROVINCE_NOT_SUPPORTED);
        }

        //判断区域级别的数据，如果区域下面没有酒店id，则直接返回
        if (addressById.getDataType() > AddressEnum.BUSINESS.getKey() && addressById.getDataType() < AddressEnum.PROVINCE.getKey()) {
            if (CollectionUtils.isEmpty(addressById.getHotelIds())) {
                //直接返回
                return iPage;
            }
        }

        // 查询可售
        Set<Long> preferredHotelIds = new HashSet<>();
        if (CollectionUtil.isNotEmpty(addressById.getDestinationName())) {
            AddressDocDTO address = addressById.getDestinationName().iterator().next();
            AvailableHotelRequest availableHotelRequest = new AvailableHotelRequest();
            availableHotelRequest.setAgentCode(hotelPageReq.getAgentCode());
            availableHotelRequest.setCheckInDate(hotelPageReq.getCheckInDate());
            availableHotelRequest.setCheckOutDate(hotelPageReq.getCheckOutDate());
            availableHotelRequest.setCityCode(address.getCityCode());
            preferredHotelIds = new HashSet<>(productRemote.queryAvailableHotelList(availableHotelRequest));
        }

        if (hotelPageReq.getPriceBegin() != null
                && hotelPageReq.getPriceEnd() != null
                && CollectionUtil.isEmpty(preferredHotelIds)) {
            // 存在价格区间，且没有可售酒店 （起价） 那么直接结束 返回空结果
            iPage.setRecords(new ArrayList<>());
            iPage.setCurrent(hotelPageReq.getCurrentPage());
            iPage.setSize(hotelPageReq.getPageSize());
            iPage.setTotal(0);
            //计算总页数
            iPage.setPages(0);
            return iPage;
        }


        Set<Long> hotelIds = new HashSet<>();

        // 存在搜索关键字 且区域类型不等于酒店 则需要根据关键字搜索酒店列表
        if (StrUtil.isNotBlank(hotelPageReq.getKeyWord()) && addressById.getDataType() != 1) {
            hotelIds = findHotelIdsByCityCodeAndKeyword(addressById.getDestinationName().iterator().next().getCityCode(), hotelPageReq.getKeyWord(), hotelPageReq.getLanguage());
            if (CollectionUtil.isEmpty(hotelIds)) {
                // 没有符合条件酒店Id 直接返回
                iPage.setRecords(new ArrayList<>());
                iPage.setCurrent(hotelPageReq.getCurrentPage());
                iPage.setSize(hotelPageReq.getPageSize());
                iPage.setTotal(0);
                //计算总页数
                iPage.setPages(0);
                return iPage;
            }
        }

        HotelReq req = null;
        try {
            req = getHotelReqKeyWordHotelIds(addressById, hotelIds);
        } catch (Exception e) {
            // 没有符合条件酒店Id 直接返回 ，不存在交集
            iPage.setRecords(new ArrayList<>());
            iPage.setCurrent(hotelPageReq.getCurrentPage());
            iPage.setSize(hotelPageReq.getPageSize());
            iPage.setTotal(0);
            //计算总页数
            iPage.setPages(0);
            return iPage;
        }

        Double latitude = null;
        Double longitude = null;
        for (AddressDocDTO next : addressById.getDestinationName()) {
            if (next.getLngGoogle() != null && next.getLatGoogle() != null) {
                latitude = next.getLatGoogle();
                longitude = next.getLngGoogle();
                break;
            }
        }
        String hotelDestinationPrefix = AddressEnum.HOTEL.getAbbreviation() + AddressEnum.HOTEL.getType();
        // 指定目的地为酒店类型，且酒店不在可售列表中，则直接返回
        if (hotelPageReq.getDestinationId().startsWith(hotelDestinationPrefix)
                && hotelPageReq.getPriceBegin() != null
                && hotelPageReq.getPriceEnd() != null) {
            // 前边有校验目的地id 所以此处可以直接使用
            Long hotelId = Long.valueOf(hotelPageReq.getDestinationId().substring(3));
            // 是否包含在可售酒店列表中
            if (!preferredHotelIds.contains(hotelId)) {
                // 存在价格区间，且没有可售酒店 （起价） 那么直接结束 返回空结果
                iPage.setRecords(new ArrayList<>());
                iPage.setCurrent(hotelPageReq.getCurrentPage());
                iPage.setSize(hotelPageReq.getPageSize());
                iPage.setTotal(0);
                //计算总页数
                iPage.setPages(0);
                return iPage;
            }
        }

        Criteria criteriaList ;
        Criteria criteriaTotal;

        try {
            criteriaList = getCriteria(hotelPageReq, req, preferredHotelIds);
            criteriaTotal = getCriteriaTotalCount(hotelPageReq, req, preferredHotelIds);
        } catch (Exception e) {
            // 没有符合条件酒店Id 直接返回 ，不存在交集
            iPage.setRecords(new ArrayList<>());
            iPage.setCurrent(hotelPageReq.getCurrentPage());
            iPage.setSize(hotelPageReq.getPageSize());
            iPage.setTotal(0);
            //计算总页数
            iPage.setPages(0);
            return iPage;
        }


        //1默认排序 2价格由低到高 3价格由高到低 4星级从高到底 5星级从低到高 6距离由近到远 7评分由高到低，默认传1
        //排序方式
        Sort sort = getSort(hotelPageReq, latitude, longitude);

        //根据排序方式取决于使用那个projectionOperation
        ProjectionOperation projectionOperation = getProjection(hotelPageReq, preferredHotelIds, longitude, latitude);

        MatchOperation match;
        // 默认排序
        if (hotelPageReq.getSortBy() == 1) {
            match = Aggregation.match(new Criteria().orOperator(
                    new Criteria().and("lowestPriceAndRecommendDTO.agentCode").in(hotelPageReq.getAgentCode(), "0")
            ));
        } else {
            match = Aggregation.match(new Criteria().orOperator(
                    Criteria.where("lowestPriceAndRecommendDTO.agentCode").is("0")
                            .and("lowestPriceAndRecommendDTO.lowestPrice").is(req.getPriceBegin()),
                    Criteria.where("lowestPriceAndRecommendDTO.agentCode").is(hotelPageReq.getAgentCode())
                            .and("lowestPriceAndRecommendDTO.lowestPrice").gte(req.getPriceBegin()).lte(req.getPriceEnd())
            ));
        }
        // 判断是否有价格区间 存在为true 不存在为false
        boolean hasPriceRange = hotelPageReq.getPriceBegin() != null && hotelPageReq.getPriceEnd() != null;


        //聚合查询
        Aggregation aggregation = null;
        Aggregation aggregationList = null;
        if (latitude != null && latitude > -COORDINATE_ANGLE_90 && longitude > -COORDINATE_ANGLE_180 && latitude < COORDINATE_ANGLE_90 && longitude < COORDINATE_ANGLE_180) {
            //计算经纬度，并且参与排序
            GroupOperation groupOperation =    Aggregation.group("hotelId")
                    // 将所有匹配的 lowestPriceDTO 推送到列表中
                    .push("lowestPriceAndRecommendDTO").as("lowestPriceAndRecommendDTO")
                    .first("hotelId").as("hotelId")
                    .first("heatValue").as("heatValue")
                    .first("recommendScore").as("recommendScore")
                    .first("lowestPrice").as("lowestPrice")
                    .first("distance").as("distance")
                    .first("hotelStar").as("hotelStar")
                    .first("hotelScore").as("hotelScore");
            // 存在价格区间 则不返回 available 排序
            if (!hasPriceRange) {
                groupOperation = groupOperation.first("available").as("available");
            }

            aggregation = Aggregation.newAggregation(
                    Aggregation.geoNear(NearQuery.near(new GeoJsonPoint(longitude, latitude)).maxDistance(10, Metrics.KILOMETERS).spherical(false), "distance"),
                    // 普通条件
                    Aggregation.match(criteriaList),
                    // 拆分 lowestPriceDTO 数组
                    Aggregation.unwind("lowestPriceAndRecommendDTO"),
                    match,
                    projectionOperation,
                    groupOperation,
                    Aggregation.sort(sort),
                    Aggregation.skip((hotelPageReq.getCurrentPage() - 1) * hotelPageReq.getPageSize()),
                    Aggregation.limit(hotelPageReq.getPageSize())
            );
            //统计总条数
            aggregationList = Aggregation.newAggregation(
                    Aggregation.geoNear(NearQuery.near(new GeoJsonPoint(longitude, latitude)).maxDistance(10, Metrics.KILOMETERS).spherical(false), "distance"),
                    // 普通条件
                    Aggregation.match(criteriaTotal),
                    // 拆分 lowestPriceDTO 数组
                    Aggregation.unwind("lowestPriceAndRecommendDTO"),
                    match,
                    Aggregation.group("hotelId"),
                    Aggregation.count().as("count")
            );
        } else {
            //除去经纬度
            GroupOperation groupOperation = Aggregation.group("hotelId")
                    // 将所有匹配的 lowestPriceDTO 推送到列表中
                    .push("lowestPriceAndRecommendDTO").as("lowestPriceAndRecommendDTO")
                    .first("hotelId").as("hotelId")
                    .first("heatValue").as("heatValue")
                    .first("recommendScore").as("recommendScore")
                    .first("lowestPrice").as("lowestPrice")
                    .first("hotelStar").as("hotelStar")
                    .first("hotelScore").as("hotelScore");
            // 存在价格区间 则不返回 available 排序
            if (!hasPriceRange) {
                groupOperation = groupOperation.first("available").as("available");
            }
            aggregation = Aggregation.newAggregation(
                    // 普通条件
                    Aggregation.match(criteriaList),
                    // 拆分 lowestPriceDTO 数组
                    Aggregation.unwind("lowestPriceAndRecommendDTO"),
                    match,
                    projectionOperation,
                    groupOperation,
                    Aggregation.sort(sort),
                    Aggregation.skip((hotelPageReq.getCurrentPage() - 1) * hotelPageReq.getPageSize()),
                    Aggregation.limit(hotelPageReq.getPageSize())
            );
            //统计总条数
            aggregationList = Aggregation.newAggregation(
                    // 普通条件
                    Aggregation.match(criteriaTotal),
                    // 拆分 lowestPriceDTO 数组
                    Aggregation.unwind("lowestPriceAndRecommendDTO"),
                    match,
                    Aggregation.group("hotelId"),
                    Aggregation.count().as("count")
            );
        }
        CountDownLatch hotelInfoLanguageCountDownLatch = new CountDownLatch(2);


        // 分页结果
        List<HotelPublicResp> hotelPublicRespDTOS = new ArrayList<>();

        Aggregation finalAggregation = aggregation;
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    AggregationResults<HotelPublicResp> aggregate = mongoTemplateBasic.aggregate(finalAggregation, HotelPublicCollection.class,HotelPublicResp.class);
                    hotelPublicRespDTOS.addAll(aggregate.getMappedResults());
                } catch (Exception e) {
                    log.error("queryHotelId error!", e);
                } finally {
                    hotelInfoLanguageCountDownLatch.countDown();
                }
            }
        });

        // 获取总条数
        List<JSONObject> mappedResults = new ArrayList<>();
        Aggregation finalAggregationList = aggregationList;
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    AggregationResults<JSONObject> aggregateList = mongoTemplateBasic.aggregate(finalAggregationList, HotelPublicCollection.class, JSONObject.class);
                    mappedResults.addAll(aggregateList.getMappedResults());
                } catch (Exception e) {
                    log.error("queryHotelCount error!", e);
                } finally {
                    hotelInfoLanguageCountDownLatch.countDown();
                }
            }
        });

        try {
            hotelInfoLanguageCountDownLatch.await(20000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("queryHotelCount error!", e);
        }

        // 获取总数
        long total = 0;
        if (CollectionUtils.isNotEmpty(mappedResults)) {
            total = mappedResults.get(0).getLong("count");
        }

        iPage.setRecords(hotelPublicRespDTOS);
        iPage.setTotal(total);
        iPage.setCurrent(hotelPageReq.getCurrentPage());
        // 计算总页数
        long pages = 0;
        if (total > 0) {
            pages = hotelPageReq.getPageSize() / total;
            if (total % hotelPageReq.getPageSize() != 0 && total > hotelPageReq.getPageSize()) {
                pages++;
            }
        }
        iPage.setPages(pages);
        iPage.setSize(hotelPageReq.getPageSize());
        return iPage;
    }

    /**
     * 根据城市编码和关键字查询酒店列表
     */
    private Set<Long> findHotelIdsByCityCodeAndKeyword(String cityCode, String keyWord, String language) {
        String result = null;
        try {
            JSONObject hotelReq = new JSONObject();
            hotelReq.put(FieldConstants.CITY_CODE, cityCode);
            hotelReq.put(FieldConstants.KEY_WORD, keyWord);
            hotelReq.put(FieldConstants.LANGUAGE, language);
            result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.FIND_HOTELIDS_BYCITYCODE_AND_KEYWORD, hotelReq.toJSONString());
            if (StrUtilX.isEmpty(result)) {
                log.error("根据城市编码和关键字查询酒店列表----基础信息返回结果为空 req:{}", hotelReq.toJSONString());
                return new HashSet<>();
            }
            return JSON.parseObject(result, new TypeReference<ResultX<Set<Long>>>() {
            }).getData();
        } catch (Exception e) {
            log.error("根据城市编码和关键字查询酒店列表系统异常----基础信息返回的数据是：{}", result, e);
            return new HashSet<>();
        }
    }

    /**
     * 获取mongodb投影
     * @param hotelPageReq 请求参数
     * @param preferredHotelIds 可售酒店列表
     * @param longitude 纬度
     * @param latitude 经度
     * @return 投影
     */
    private ProjectionOperation getProjection(HotelPageReq hotelPageReq, Set<Long> preferredHotelIds, Double longitude, Double latitude) {
        ProjectionOperation projectionOperation = null;
        // 判断价格区间是否不为空
        boolean hasPriceRange = hotelPageReq.getPriceBegin() != null && hotelPageReq.getPriceEnd() != null;

        if (hotelPageReq.getSortBy().equals(SortEnum.PRICE_LOW_TO_HIGH.code)) {
            projectionOperation = Aggregation.project("hotelId", "lowestPriceAndRecommendDTO", "heatValue", "distance", "hotelScore", "hotelStar")
                    // 如果 lowestPrice 等于 0，返回 9999999
                    .andExpression(
                            "cond(eq(lowestPriceAndRecommendDTO.lowestPrice, 0), 9999999, " +
                                    "cond(eq(lowestPriceAndRecommendDTO.lowestPrice, null), 9999999, lowestPriceAndRecommendDTO.lowestPrice))"
                            // 使用 if 语句处理价格
                    ).as("lowestPrice")
                    .and("lowestPriceAndRecommendDTO.recommendScore").as("recommendScore");
            if (!hasPriceRange) {
                projectionOperation = projectionOperation.and(ConditionalOperators.when(Criteria.where("_id").in(preferredHotelIds)).then(true).otherwise(false)).as("available");
            }
        } else if (hotelPageReq.getSortBy().equals(SortEnum.STAR_HIGH_TO_LOW.code)) {
            projectionOperation = Aggregation.project("hotelId", "lowestPriceAndRecommendDTO", "heatValue", "distance", "hotelScore")
                    // 如果 hotelStar 等于 0，返回 9999999
                    .andExpression(
                            "cond(eq(hotelStar, 0), 9999999, " +
                                    "cond(eq(hotelStar, null), 9999999, hotelStar))"
                            // 使用 if 语句处理价格
                    ).as("hotelStar")
                    .and("lowestPriceAndRecommendDTO.lowestPrice").as("lowestPrice")
                    .and("lowestPriceAndRecommendDTO.recommendScore").as("recommendScore");
            if (!hasPriceRange) {
                projectionOperation = projectionOperation.and(ConditionalOperators.when(Criteria.where("_id").in(preferredHotelIds)).then(true).otherwise(false)).as("available");
            }
        } else if (latitude != null && latitude > -COORDINATE_ANGLE_90 && longitude > -COORDINATE_ANGLE_180 && latitude < COORDINATE_ANGLE_90 && longitude < COORDINATE_ANGLE_180) {
            projectionOperation = Aggregation.project("hotelId", "lowestPriceAndRecommendDTO", "heatValue", "distance", "hotelScore", "hotelStar");
            if (!hasPriceRange) {
                projectionOperation = projectionOperation.and(ConditionalOperators.when(Criteria.where("_id").in(preferredHotelIds)).then(true).otherwise(false)).as("available");
            }
            projectionOperation = projectionOperation.and("lowestPriceAndRecommendDTO.lowestPrice").as("lowestPrice").and("lowestPriceAndRecommendDTO.recommendScore").as("recommendScore");
        } else {
            projectionOperation = Aggregation.project("hotelId", "lowestPriceAndRecommendDTO", "heatValue", "hotelScore", "hotelStar");
            if (!hasPriceRange) {
                projectionOperation = projectionOperation.and(ConditionalOperators.when(Criteria.where("_id").in(preferredHotelIds)).then(true).otherwise(false)).as("available");
            }
            projectionOperation = projectionOperation.and("lowestPriceAndRecommendDTO.lowestPrice").as("lowestPrice")
                    .and("lowestPriceAndRecommendDTO.recommendScore").as("recommendScore");
        }
        return projectionOperation;
    }


    /**
     * 酒店排序规则
     *
     * @param hotelPageReq 请求
     * @param latitude     经度
     * @param lngitude     纬度
     * @return 2024-12-27 所有排序规则中  available 是否可售排在最前面，那么就可以保证 在前面的都是可售酒店 author:湫
     */
    private static Sort getSort(HotelPageReq hotelPageReq, Double latitude, Double lngitude) {
        List<Sort.Order> orders = new ArrayList<>();
        // 存在价格区间 那么就一定是在可售酒店列表中的 故此 不需要增加 available 排序规则
        if (hotelPageReq.getPriceBegin() == null || hotelPageReq.getPriceEnd() == null) {
            orders.add(Sort.Order.desc("available"));
        }
        if (SortEnum.DEFAULT.code.equals(hotelPageReq.getSortBy())) {
            orders.add(Sort.Order.desc("recommendScore"));
            orders.add(Sort.Order.desc("heatValue"));
        } else if (SortEnum.PRICE_LOW_TO_HIGH.code.equals(hotelPageReq.getSortBy())) {
            orders.add(Sort.Order.asc("lowestPrice"));
            orders.add(Sort.Order.desc("recommendScore"));
            orders.add(Sort.Order.desc("heatValue"));
        } else if (SortEnum.PRICE_HIGH_TO_LOW.code.equals(hotelPageReq.getSortBy())) {
            orders.add(Sort.Order.desc("lowestPrice"));
            orders.add(Sort.Order.desc("recommendScore"));
            orders.add(Sort.Order.desc("heatValue"));
        } else if (SortEnum.STAR_HIGH_TO_LOW.code.equals(hotelPageReq.getSortBy())) {
            orders.add(Sort.Order.asc("hotelStar"));
            orders.add(Sort.Order.desc("recommendScore"));
            orders.add(Sort.Order.desc("heatValue"));
        } else if (SortEnum.STAR_LOW_TO_HIGH.code.equals(hotelPageReq.getSortBy())) {
            orders.add(Sort.Order.desc("hotelStar"));
            orders.add(Sort.Order.desc("recommendScore"));
            orders.add(Sort.Order.desc("heatValue"));
        } else if (SortEnum.DISTANCE_NEAR_TO_FAR.code.equals(hotelPageReq.getSortBy())) {
            if (latitude != null && lngitude != null) {
                orders.add(Sort.Order.asc("distance"));
            }
            orders.add(Sort.Order.desc("recommendScore"));
            orders.add(Sort.Order.desc("heatValue"));
        } else if (SortEnum.SCORE_HIGH_TO_LOW.code.equals(hotelPageReq.getSortBy())) {
            orders.add(Sort.Order.desc("hotelScore"));
            orders.add(Sort.Order.desc("recommendScore"));
            orders.add(Sort.Order.desc("heatValue"));
        }

        return Sort.by(orders);
    }

    /**
     * 获取查询条件用于获取结果列表
     */
    private static Criteria getCriteria(HotelPageReq hotelPageReq, HotelReq req, Set<Long> preferredHotelIds) {
        // 嵌套查询
        Criteria criteria = new Criteria();
        if (req.getHotelId() != null) {
            criteria.and("_id").is(req.getHotelId());
        }
        if (CollectionUtils.isNotEmpty(req.getHotelIds())) {
            criteria.and("hotelId").in(req.getHotelIds());
        }
        if (StringUtils.isNotEmpty(req.getCityCode())) {
            criteria.and("cityCode").is(req.getCityCode());
        }
        if (StringUtils.isNotEmpty(req.getDistrictCode())) {
            criteria.and("districtCode").is(req.getDistrictCode());
        }
        if (StringUtils.isNotEmpty(req.getBusinessZoneCode())) {
            criteria.and("businessZoneCode").is(req.getBusinessZoneCode());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getFacilitiesIds())) {
            // all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("facilitiesIds").all(hotelPageReq.getFacilitiesIds());
        }
        // 组装热门设施
        List<String> hotFacilityCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelFacilityCodes())) {
            hotFacilityCodes.addAll(hotelPageReq.getHotelFacilityCodes());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getRoomFacilityCodes())) {
            hotFacilityCodes.addAll(hotelPageReq.getRoomFacilityCodes());
        }
        if (CollectionUtils.isNotEmpty(hotFacilityCodes)) {
            // all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("hotFacility").all(hotFacilityCodes);
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelStars())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("hotelStar").in(hotelPageReq.getHotelStars());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelLabelIds())) {
            criteria.and("hotelAndAgentCodeLabelDTO").elemMatch(Criteria.where("agentCode").in(hotelPageReq.getAgentCode(), "0")
                    .and("hotelLabelConfigIds").all(hotelPageReq.getHotelLabelIds()));
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelBrandCodes())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("brandCode").in(hotelPageReq.getHotelBrandCodes());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getPlateCodes())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("groupCode").in(hotelPageReq.getPlateCodes());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelSubCategoryIds())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("hotelSubCategory").in(hotelPageReq.getHotelSubCategoryIds());
        }
        String hotelDestinationPrefix = AddressEnum.HOTEL.getAbbreviation() + AddressEnum.HOTEL.getType();
        // 指定价格区间酒店列表
        if (hotelPageReq.getPriceBegin() != null && hotelPageReq.getPriceEnd() != null) {
            req.setPriceBegin(hotelPageReq.getPriceBegin());
            req.setPriceEnd(hotelPageReq.getPriceEnd());

            if (!hotelPageReq.getDestinationId().startsWith(hotelDestinationPrefix)) {
                if (CollectionUtil.isNotEmpty(req.getHotelIds()) && CollectionUtil.isNotEmpty(preferredHotelIds)) {
                    // 存在价格区间和关键字 取交集
                    criteria.and("_id").in(filterHotelIds(new HashSet<>(req.getHotelIds()), preferredHotelIds));
                } else {
                    // 存在价格区间，那么 必须是可售酒店有价格
                    criteria.and("_id").in(preferredHotelIds);
                }
            }
        } else {
            req.setPriceBegin(0d);
            req.setPriceEnd(Double.MAX_VALUE);
        }

        // 不存在价格区间，并且区域类型不为指定酒店并且 当前页小于等于可售酒店数量
        if ((hotelPageReq.getPriceBegin() == null || hotelPageReq.getPriceEnd() == null)
                && !hotelPageReq.getDestinationId().startsWith(hotelDestinationPrefix)
                && (hotelPageReq.getCurrentPage() * hotelPageReq.getPageSize()) <= preferredHotelIds.size()) {
            criteria.and("_id").in(preferredHotelIds);
        }
        return criteria;
    }

    /**
     * 获取查询条件 用于获取符条件的数量
     * @param hotelPageReq 查询参数
     * @param req 请求参数
     * @param preferredHotelIds 可售酒店列表
     * @return 查询条件
     */
    private static Criteria getCriteriaTotalCount(HotelPageReq hotelPageReq, HotelReq req, Set<Long> preferredHotelIds) {
        //嵌套查询
        Criteria criteria = new Criteria();
        if (req.getHotelId() != null) {
            criteria.and("_id").is(req.getHotelId());
        }
        if (CollectionUtils.isNotEmpty(req.getHotelIds())) {
            criteria.and("hotelId").in(req.getHotelIds());
        }
        if (StringUtils.isNotEmpty(req.getCityCode())) {
            criteria.and("cityCode").is(req.getCityCode());
        }
        if (StringUtils.isNotEmpty(req.getDistrictCode())) {
            criteria.and("districtCode").is(req.getDistrictCode());
        }
        if (StringUtils.isNotEmpty(req.getBusinessZoneCode())) {
            criteria.and("businessZoneCode").is(req.getBusinessZoneCode());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getFacilitiesIds())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("facilitiesIds").all(hotelPageReq.getFacilitiesIds());
        }
        //组装热门设施
        List<String> hotFacilityCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelFacilityCodes())) {
            hotFacilityCodes.addAll(hotelPageReq.getHotelFacilityCodes());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getRoomFacilityCodes())) {
            hotFacilityCodes.addAll(hotelPageReq.getRoomFacilityCodes());
        }
        if (CollectionUtils.isNotEmpty(hotFacilityCodes)) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("hotFacility").all(hotFacilityCodes);
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelStars())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("hotelStar").in(hotelPageReq.getHotelStars());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelLabelIds())) {
            criteria.and("hotelAndAgentCodeLabelDTO").elemMatch(Criteria.where("agentCode").in(hotelPageReq.getAgentCode(), "0")
                    .and("hotelLabelConfigIds").all(hotelPageReq.getHotelLabelIds()));
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelBrandCodes())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("brandCode").in(hotelPageReq.getHotelBrandCodes());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getPlateCodes())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("groupCode").in(hotelPageReq.getPlateCodes());
        }
        if (CollectionUtils.isNotEmpty(hotelPageReq.getHotelSubCategoryIds())) {
            //all满足所有id的才输出，in只要满足其中一项即可输出
            criteria.and("hotelSubCategory").in(hotelPageReq.getHotelSubCategoryIds());
        }
        String hotelDestinationPrefix = AddressEnum.HOTEL.getAbbreviation() + AddressEnum.HOTEL.getType();
        // 指定价格区间酒店列表
        if (hotelPageReq.getPriceBegin() != null && hotelPageReq.getPriceEnd() != null) {
            req.setPriceBegin(hotelPageReq.getPriceBegin());
            req.setPriceEnd(hotelPageReq.getPriceEnd());

            if (!hotelPageReq.getDestinationId().startsWith(hotelDestinationPrefix)) {
                if (CollectionUtil.isNotEmpty(req.getHotelIds()) && CollectionUtil.isNotEmpty(preferredHotelIds)) {
                    // 存在价格区间和关键字 取交集
                    criteria.and("_id").in(filterHotelIds(new HashSet<>(req.getHotelIds()), preferredHotelIds));
                } else {
                    // 存在价格区间，那么 必须是可售酒店有价格
                    criteria.and("_id").in(preferredHotelIds);
                }
            }
        } else {
            req.setPriceBegin(0d);
            req.setPriceEnd(Double.MAX_VALUE);
        }

        return criteria;
    }

    @Override
    public void updateHotelMongodbByHotelId(HotelPublicDTO hotelPublicDTO) {
        // 修改起价
        if (CollUtilX.isNotEmpty(hotelPublicDTO.getLowestPriceDTOList())) {
            List<LowestPrice> lowestPriceDTOList = hotelPublicDTO.getLowestPriceDTOList();
            // 分批处理，每批100条数据
            List<List<LowestPrice>> batches = ListUtil.split(lowestPriceDTOList, 100);
            for (List<LowestPrice> batch : batches) {
                try {
                    // 1. 批量获取hotelIds并去重
                    Set<Long> hotelIds = batch.stream()
                            .map(LowestPrice::getHotelId)
                            .collect(Collectors.toSet());

                    // 2. 批量查询HotelPublicCollection
                    List<HotelPublicCollection> hotels = mongoTemplateBasic.find(
                            Query.query(Criteria.where("_id").in(hotelIds)), HotelPublicCollection.class);
                    Map<Long, HotelPublicCollection> hotelMap = hotels.stream()
                            .collect(Collectors.toMap(HotelPublicCollection::getHotelId, Function.identity()));

                    // 3. 按hotelId分组处理价格数据
                    Map<Long, List<LowestPrice>> pricesByHotelId = batch.stream()
                            .collect(Collectors.groupingBy(LowestPrice::getHotelId));

                    BulkOperations bulkOps = mongoTemplateBasic.bulkOps(BulkOperations.BulkMode.UNORDERED, HotelPublicCollection.class);
                    int operationCount = 0;

                    for (Map.Entry<Long, List<LowestPrice>> entry : pricesByHotelId.entrySet()) {
                        Long hotelId = entry.getKey();
                        List<LowestPrice> prices = entry.getValue();

                        HotelPublicCollection hotel = hotelMap.get(hotelId);
                        if (hotel == null) {
                            continue;
                        }

                        List<LowestPriceDTO> priceDTOs = Optional.ofNullable(hotel.getLowestPriceAndRecommendDTO())
                                .orElse(new ArrayList<>());
                        boolean modified = false;

                        // 4. 使用Map快速查找现有DTO
                        Map<String, LowestPriceDTO> priceDTOMap = priceDTOs.stream()
                                .collect(Collectors.toMap(LowestPriceDTO::getAgentCode, Function.identity()));

                        for (LowestPrice price : prices) {
                            String agentCode = price.getAgentCode();
                            LowestPriceDTO existingDTO = priceDTOMap.get(agentCode);

                            if (existingDTO != null) {
                                // 5. 仅在实际变化时更新
                                if (updateExistingDTO(existingDTO, price)) {
                                    modified = true;
                                }
                            } else {
                                // 6. 创建新DTO时使用更高效的方式
                                LowestPriceDTO newDTO = createNewDTO(price);
                                priceDTOs.add(newDTO);
                                priceDTOMap.put(agentCode, newDTO);
                                modified = true;
                            }
                        }

                        if (modified) {
                            // 7. 使用更高效的更新方式
                            Update update = new Update().set("lowestPriceAndRecommendDTO", priceDTOs);
                            bulkOps.updateOne(Query.query(Criteria.where("_id").is(hotelId)), update);
                            operationCount++;
                        }
                    }

                    if (operationCount > 0) {
                        bulkOps.execute();
                    }
                } catch (BeansException e) {
                    log.error("批量更新酒店低价信息异常，批次数据：{}", batch, e);
                }
            }
        }

        // 修改酒店标签
        if (CollectionUtils.isNotEmpty(hotelPublicDTO.getHotelLabelConfigIds())) {
            // 获取目标酒店数据
            HotelPublicCollection hotelPublicCollection = mongoTemplateBasic.findById(hotelPublicDTO.getHotelId(), HotelPublicCollection.class);

            // 数据不存在时提前返回
            if (Objects.isNull(hotelPublicCollection)) {
                return;
            }

            // 获取待处理的标签配置列表
            List<HotelAndAgentCodeLabelDTO> incomingLabels = hotelPublicDTO.getHotelLabelConfigIds();
            if (CollectionUtils.isEmpty(incomingLabels)) {
                return;
            }

            // 使用Map优化数据操作（agentCode -> labelDTO）
            Map<String, HotelAndAgentCodeLabelDTO> labelMap = new HashMap<>();

            // 初始化现有数据到Map（如果存在）
            List<HotelAndAgentCodeLabelDTO> existingLabels = hotelPublicCollection.getHotelAndAgentCodeLabelDTO();
            if (CollectionUtils.isNotEmpty(existingLabels)) {
                existingLabels.forEach(dto -> labelMap.put(dto.getAgentCode(), dto));
            }

            // 批量处理标签更新
            boolean modified = false;
            for (HotelAndAgentCodeLabelDTO newLabel : incomingLabels) {
                String agentCode = newLabel.getAgentCode();
                HotelAndAgentCodeLabelDTO existingLabel = labelMap.get(agentCode);

                // 判断是否需要更新（新增或修改）
                if (existingLabel == null || isLabelEqual(existingLabel, newLabel)) {
                    labelMap.put(agentCode, newLabel);
                    modified = true;
                }
            }

            // 仅当数据发生变更时执行更新
            if (modified) {
                // 转换Map回List格式
                List<HotelAndAgentCodeLabelDTO> updatedLabels = new ArrayList<>(labelMap.values());

                // 构建单次更新操作
                Query query = new Query(Criteria.where("_id").is(hotelPublicCollection.getHotelId()));
                Update update = new Update().set("hotelAndAgentCodeLabelDTO", updatedLabels);

                // 使用upsert防止并发创建
                mongoTemplateBasic.upsert(query, update, HotelPublicCollection.class);
            }
        }
    }

    /**
     * 判断是否需要更新
     */
    private boolean updateExistingDTO(LowestPriceDTO existingDTO, LowestPrice newPrice) {
        boolean modified = false;
        if (!Objects.equals(existingDTO.getPayAtHotelFee(), newPrice.getPayAtHotelFee())) {
            existingDTO.setPayAtHotelFee(newPrice.getPayAtHotelFee());
            modified = true;
        }
        if (!Objects.equals(existingDTO.getPayAtHotelCurrency(), newPrice.getPayAtHotelCurrency())) {
            existingDTO.setPayAtHotelCurrency(newPrice.getPayAtHotelCurrency());
            modified = true;
        }
        if (!Objects.equals(existingDTO.getLowestPrice(), newPrice.getLowestPrice())) {
            existingDTO.setLowestPrice(newPrice.getLowestPrice());
            modified = true;
        }
        if (!Objects.equals(existingDTO.getSaleCurrency(), newPrice.getSaleCurrency())) {
            existingDTO.setSaleCurrency(newPrice.getSaleCurrency());
            modified = true;
        }
        return modified;
    }

    /**
     * 创建新对象
     */
    private LowestPriceDTO createNewDTO(LowestPrice price) {
        LowestPriceDTO dto = new LowestPriceDTO();
        // 使用直接赋值代替反射以提高性能
        dto.setAgentCode(price.getAgentCode());
        dto.setPayAtHotelFee(price.getPayAtHotelFee());
        dto.setPayAtHotelCurrency(price.getPayAtHotelCurrency());
        dto.setLowestPrice(price.getLowestPrice());
        dto.setSaleCurrency(price.getSaleCurrency());
        dto.setRecommendScore(0d);
        return dto;
    }

    /**
     * 比较数据
     */
    private boolean isLabelEqual(HotelAndAgentCodeLabelDTO a, HotelAndAgentCodeLabelDTO b) {
        return !Objects.equals(a.getHotelLabelConfigIds(), b.getHotelLabelConfigIds()) ||
                !Objects.equals(a.getAgentCode(), b.getAgentCode());
    }

    /**
     * 获取酒店信息
     */
    public List<HotelInfoLanguage> getHotelInfoLanguage(List<Long> hotelIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(hotelIds));
        List<HotelInfoLanguage> list = new ArrayList<>();
        try {
            List<HotelInfoLanguage> hotelLanguageCollection = mongoTemplateBasic.find(query, HotelInfoLanguage.class, "hotelLanguageCollection");
            //转换成map，按照传入的顺序进行输出

            if (CollUtil.isNotEmpty(hotelLanguageCollection)) {
                Map<Long, HotelInfoLanguage> collect = hotelLanguageCollection.stream().collect(Collectors.toMap(HotelInfoLanguage::getId, Function.identity(), (s1, s2) -> s1));
                for (Long id : hotelIds) {
                    HotelInfoLanguage hotelInfoLanguage = collect.get(id);
                    if (Objects.isNull(hotelInfoLanguage)) {
                        continue;
                    }
                    list.add(hotelInfoLanguage);
                }
            }
        } catch (Exception e) {
            log.error("getHotelInfoLanguage", e);
        }
        return list;
    }

    private BigDecimal add(BigDecimal... value) {
        BigDecimal result = BigDecimal.ZERO;
        if (value != null) {
            for (BigDecimal bigDecimal : value) {
                if (bigDecimal != null) {
                    result = result.add(bigDecimal);
                }
            }
        }
        return result;
    }

    @Override
    public PaginationSupportDTO<HotelListResp> findHotelList(HotelPageReq req) {
        PaginationSupportDTO<HotelListResp> paginationSupportDTO = new PaginationSupportDTO<>();
        //校验价格
        if (req.getPriceBegin() != null && req.getPriceEnd() == null) {
            throw new SysException(ErrorCodeEnum.PRICE_END_IS_NOT_EMPTY);
        } else if (req.getPriceEnd() != null && req.getPriceBegin() == null) {
            throw new SysException(ErrorCodeEnum.PRICE_BEGIN_IS_NOT_EMPTY);
        } else if (req.getPriceBegin() != null && req.getPriceBegin() >= req.getPriceEnd()) {
            throw new SysException(ErrorCodeEnum.PRICE_BEGIN_LOWEST_PRICE_END);
        } else if (req.getPriceBegin() != null && req.getPriceBegin() <= 0) {
            throw new SysException(ErrorCodeEnum.PRICE_BEGIN_GREATER_ZERO);
        }

        // 查询酒店列表 true  内存排序 优化后效率提高，false  mongodb数据库排序
        IPage<HotelPublicResp> list =  findHotelIds(req);
        paginationSupportDTO.setCurrentPage(req.getCurrentPage().intValue());
        paginationSupportDTO.setPageSize((int) list.getSize());
        paginationSupportDTO.setTotalCount(list.getTotal());
        paginationSupportDTO.setTotalPage((int) list.getPages());
        List<HotelInfoLanguage> hotelInfoLanguage = new ArrayList<>();

        //酒店id和起价结构
        Map<Long, HotelPublicResp> lowestPriceDTOMap = new HashMap<>();

        //酒店id和酒店标签结构
        Map<Long, Set<String>> hotelLabelDTOMap = new HashMap<>();

        Map<Long, List<HotelListResp>> hotelPriceMap = new HashMap<>();

        // 当入离日期大于30且 hotelPriceMap 不存在价格时，使用兜底起价，解决未来很久没有起价的情况
        Map<Long, List<HotelListResp>> backupHotelPriceMap = new HashMap<>();
        //酒店id
        Date checkInDate = DateUtilX.stringToDate(req.getCheckInDate());
        Date checkOutDate = DateUtilX.stringToDate(req.getCheckOutDate());
        Date thisDate = new Date();
        // 是否使用保底备份起价
        boolean isBackup = DateUtilX.getDay(thisDate, checkInDate) > 30 || DateUtilX.getDay(thisDate, checkOutDate) > 30;
        List<Long> hotelIds = new ArrayList<>();
        if (CollUtilX.isNotEmpty(list.getRecords())) {
            //取出酒店id
            List<HotelPublicResp> records = list.getRecords();
            hotelIds = records.stream().map(HotelPublicResp::getId).collect(Collectors.toList());
            //转换格式：hotelId--lowestPrice
            lowestPriceDTOMap = records.stream().collect(Collectors.toMap(HotelPublicResp::getId, Function.identity(), (s1, s2) -> s1));

            int count = 2;
            // 当入离日期大于30时，使用兜底起价， 这时候需要三个并发线程去查询
            if (isBackup) {
                count = 3;
            }
            CountDownLatch hotelInfoLanguageCountDownLatch = new CountDownLatch(count);
            // 获取酒店基础信息与标签
            List<Long> finalHotelIds = hotelIds;
            executor.execute(() -> {
                try {
                    hotelInfoLanguage.addAll(getHotelInfoLanguage(finalHotelIds));
                    HotelLabelNameDTO labelNameDTO = new HotelLabelNameDTO();
                    labelNameDTO.setAgentCode(req.getAgentCode());
                    labelNameDTO.setHotelIdList(finalHotelIds);
                    List<com.tiangong.product.resp.HotelLabelDTO> hotelLabelDTOS = hotelRecommendMapper.selectAgentHotelLabel(labelNameDTO);
                    if (CollectionUtils.isNotEmpty(hotelLabelDTOS)) {
                        for (com.tiangong.product.resp.HotelLabelDTO hotelLabelDTO : hotelLabelDTOS) {
                            hotelLabelDTOMap.put(hotelLabelDTO.getHotelId(), hotelLabelDTO.getHotelLabelNameList());
                        }
                    }
                } catch (Exception e) {
                    log.error("获取酒店标签异常", e);
                } finally {
                    hotelInfoLanguageCountDownLatch.countDown();
                }
            });

            // 获取酒店起价
            executor.execute(() -> {
                try {
                    HotelLowestPriceRequest hotelLowestPriceRequest = new HotelLowestPriceRequest();
                    hotelLowestPriceRequest.setAgentCode(req.getAgentCode());
                    hotelLowestPriceRequest.setHotelIds(finalHotelIds);
                    hotelLowestPriceRequest.setCheckInDate(req.getCheckInDate());
                    hotelLowestPriceRequest.setCheckOutDate(req.getCheckOutDate());
                    ResponseResult<HotelLowestPriceResponse> hotelLowestPriceResponseResponseResult = productRemote.queryHotelLowestPrice(hotelLowestPriceRequest);
                    if (hotelLowestPriceResponseResponseResult.getReturnCode().equals(DhubReturnCodeEnum.SUCCESS.no)
                            && CollUtilX.isNotEmpty(hotelLowestPriceResponseResponseResult.getBussinessResponse().getHotelLowestPrices())) {
                        for (HotelLowestPrice hotelLowestPrice : hotelLowestPriceResponseResponseResult.getBussinessResponse().getHotelLowestPrices()) {
                            if (CollUtilX.isNotEmpty(hotelLowestPrice.getPriceItems())) {
                                for (HotelPriceItem priceItem : hotelLowestPrice.getPriceItems()) {
                                    List<HotelListResp> hotelListResps = hotelPriceMap.get(hotelLowestPrice.getHotelId());
                                    if (hotelListResps == null) {
                                        hotelListResps = new ArrayList<>();
                                    }

                                    HotelListResp hotelListResp = new HotelListResp();
                                    hotelListResp.setLowestPrice(priceItem.getSalePrice());
                                    hotelListResp.setSaleCurrency(priceItem.getSaleCurrency());
                                    String currencyCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(priceItem.getSaleCurrency()));
                                    hotelListResp.setSaleCurrencyCode(currencyCode);
                                    if (priceItem.getPayAtHotelFee() != null) {
                                        hotelListResp.setPayAtHotelFee(priceItem.getPayAtHotelFee());
                                    }
                                    if (priceItem.getPayAtHotelCurrency() != null) {
                                        hotelListResp.setPayAtHotelCurrency(priceItem.getPayAtHotelCurrency());
                                        String payCurrencyCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(priceItem.getPayAtHotelCurrency()));
                                        hotelListResp.setPayAtHotelCurrencyCode(payCurrencyCode);
                                    }
                                    hotelListResps.add(hotelListResp);
                                    hotelPriceMap.put(hotelLowestPrice.getHotelId(), hotelListResps);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("获取起价系统异常", e);
                } finally {
                    hotelInfoLanguageCountDownLatch.countDown();
                }
            });

            if (isBackup) {
                //获取第10天的酒店价格，避免 不存在价格
                executor.execute(() -> {
                    try {
                        HotelLowestPriceRequest hotelLowestPriceRequest = new HotelLowestPriceRequest();
                        hotelLowestPriceRequest.setAgentCode(req.getAgentCode());
                        hotelLowestPriceRequest.setHotelIds(finalHotelIds);
                        hotelLowestPriceRequest.setCheckInDate(DateUtilX.dateToString(DateUtilX.getDate(thisDate, 10, 0)));
                        hotelLowestPriceRequest.setCheckOutDate(DateUtilX.dateToString(DateUtilX.getDate(thisDate, 11, 0)));
                        ResponseResult<HotelLowestPriceResponse> hotelLowestPriceResponseResponseResult = productRemote.queryHotelLowestPrice(hotelLowestPriceRequest);
                        if (hotelLowestPriceResponseResponseResult.getReturnCode().equals(DhubReturnCodeEnum.SUCCESS.no)
                                && CollUtilX.isNotEmpty(hotelLowestPriceResponseResponseResult.getBussinessResponse().getHotelLowestPrices())) {
                            for (HotelLowestPrice hotelLowestPrice : hotelLowestPriceResponseResponseResult.getBussinessResponse().getHotelLowestPrices()) {
                                if (CollUtilX.isNotEmpty(hotelLowestPrice.getPriceItems())) {
                                    for (HotelPriceItem priceItem : hotelLowestPrice.getPriceItems()) {
                                        List<HotelListResp> hotelListResps = backupHotelPriceMap.get(hotelLowestPrice.getHotelId());
                                        if (hotelListResps == null) {
                                            hotelListResps = new ArrayList<>();
                                        }

                                        HotelListResp hotelListResp = new HotelListResp();
                                        hotelListResp.setLowestPrice(priceItem.getSalePrice());
                                        hotelListResp.setSaleCurrency(priceItem.getSaleCurrency());
                                        String currencyCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(priceItem.getSaleCurrency()));
                                        hotelListResp.setSaleCurrencyCode(currencyCode);
                                        if (priceItem.getPayAtHotelFee() != null) {
                                            hotelListResp.setPayAtHotelFee(priceItem.getPayAtHotelFee());
                                        }
                                        if (priceItem.getPayAtHotelCurrency() != null) {
                                            hotelListResp.setPayAtHotelCurrency(priceItem.getPayAtHotelCurrency());
                                            String payCurrencyCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(priceItem.getPayAtHotelCurrency()));
                                            hotelListResp.setPayAtHotelCurrencyCode(payCurrencyCode);
                                        }
                                        hotelListResps.add(hotelListResp);
                                        backupHotelPriceMap.put(hotelLowestPrice.getHotelId(), hotelListResps);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取起价系统异常", e);
                    } finally {
                        hotelInfoLanguageCountDownLatch.countDown();
                    }
                });
            }

            try {
                // 等待两个线程执行完毕 时常 1000ms
                hotelInfoLanguageCountDownLatch.await(20000, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("并发获取酒店基础信息与起价系统异常", e);
            }
        } else {
            return paginationSupportDTO;
        }

        // 查询分销商
        AgentAccountConfig agentAccountConfig = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()).toString(), AgentAccountConfig.class);

        long day = DateUtilX.getDay(checkInDate, checkOutDate);

        if (CollUtilX.isNotEmpty(hotelInfoLanguage)) {
            //组装数据
            List<HotelListResp> infoLanguageList = new ArrayList<>();
            for (HotelInfoLanguage hotelInfoDTO : hotelInfoLanguage) {
                HotelListResp resp = new HotelListResp();
                //取出星级
                Integer hotelStar = lowestPriceDTOMap.get(hotelInfoDTO.getId()).getHotelStar();
                //199临时赋值的虚拟数据，做空处理
                if (Objects.nonNull(hotelStar) && hotelStar != 199) {
                    hotelInfoDTO.setStar(hotelStar);
                }

                //组装b2b数据进行输出
                resp.setCityCode(hotelInfoDTO.getCityCode());
                resp.setHotelId(hotelInfoDTO.getId());
                resp.setHotelStar(hotelInfoDTO.getStar());
                if ((resp.getHotelStar() != null && resp.getHotelStar() == 0)
                        || (resp.getHotelStar() != null && resp.getHotelStar().equals(9999999))) {
                    resp.setHotelStar(null);
                }
                resp.setMainUrl(hotelInfoDTO.getHotelPhoneAddress());
                resp.setCountryCode(hotelInfoDTO.getCountryCode());
                //获取公共数据
                List<HotelLanguage> info = hotelInfoDTO.getInfo();
                if (CollUtilX.isNotEmpty(info)) {
                    List<HotelLanguage> collect = info.stream().filter(o -> o.getLanguage().equals(req.getLanguage())).collect(Collectors.toList());
                    if (CollUtilX.isNotEmpty(collect)) {
                        HotelLanguage hotelLanguage = collect.get(0);
                        resp.setCityName(hotelLanguage.getCityName());
                        resp.setHotelName(hotelLanguage.getHotelName());
                        resp.setHotelAddress(hotelLanguage.getHotelAddress());
                        resp.setDistrictName(hotelLanguage.getDistrictName());
                        resp.setBusinessName(hotelLanguage.getBusinessZoneName());
                    }
                }

                //获取评分
                Double rating = lowestPriceDTOMap.get(hotelInfoDTO.getId()).getHotelScore();
                if (rating != null && rating > 0) {
                    resp.setHotelScore(rating);
                }

                //获取经纬度
                GeoInfoDTO geoInfo = hotelInfoDTO.getGeoInfo();
                if (Objects.nonNull(geoInfo)) {
                    resp.setLongitude(geoInfo.getLngBaidu());
                    resp.setLatitude(geoInfo.getLatBaidu());
                    resp.setLngGoogle(geoInfo.getLngGoogle());
                    resp.setLatGoogle(geoInfo.getLatGoogle());
                }

                //取出距离
                Double distance = lowestPriceDTOMap.get(hotelInfoDTO.getId()).getDistance();
                if (distance != null) {
                    BigDecimal bigDecimal =  BigDecimal.valueOf(distance).setScale(2, RoundingMode.DOWN);
                    resp.setDistance(bigDecimal.doubleValue());
                }

                //赋值酒店标签
                resp.setHotelLabelNameList(hotelLabelDTOMap.get(hotelInfoDTO.getId()));

                //取出起价 2024-12-26 author:湫 起价使用mongodb 实时维护起价
                List<HotelListResp> hotelListResp = hotelPriceMap.get(hotelInfoDTO.getId());
                if (CollectionUtil.isNotEmpty(hotelListResp) && hotelListResp.size() == day) {
                    //入离日期内每一天所有天内都存在起价，使用实时起价
                    HotelListResp hashValue = hotelListResp.get(0);
                    resp.setSaleCurrencyCode(hashValue.getSaleCurrencyCode());
                    resp.setSaleCurrency(hashValue.getSaleCurrency());

                    int i = 0;
                    BigDecimal lowestPrice = BigDecimal.ZERO;
                    BigDecimal payAtHotelFee = BigDecimal.ZERO;
                    for (HotelListResp listResp : hotelListResp) {
                        if (listResp.getPayAtHotelCurrency() != null && listResp.getPayAtHotelCurrency() != 0) {
                            resp.setPayAtHotelCurrency(listResp.getPayAtHotelCurrency());
                            resp.setPayAtHotelCurrencyCode(listResp.getPayAtHotelCurrencyCode());
                        }
                        if (listResp.getPayAtHotelCurrency() != null) {
                            i++;
                            payAtHotelFee = add(payAtHotelFee, listResp.getPayAtHotelFee());
                        }
                        lowestPrice = add(lowestPrice, listResp.getLowestPrice());
                    }
                    // 取平均
                    resp.setLowestPrice(lowestPrice.divide(new BigDecimal(hotelListResp.size()), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    if (payAtHotelFee.compareTo(BigDecimal.ZERO) != 0) {
                        resp.setPayAtHotelFee(CommonTgUtils.setScale(payAtHotelFee.divide(new BigDecimal(i)), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    }

                } else if (CollectionUtil.isNotEmpty(hotelListResp) && hotelListResp.size() != day) {
                    //入离日期内 部分有价格，则不输出起价
                    continue;
                } else if (CollectionUtil.isEmpty(hotelListResp) && isBackup && CollectionUtil.isNotEmpty(backupHotelPriceMap)) {
                    //入离日期内 一天都没有价格，且 入离日期 有任意一天距离当前时间超时30天，则使用第10天的起价
                    List<HotelListResp> hotelLists = backupHotelPriceMap.get(hotelInfoDTO.getId());
                    if (CollectionUtil.isNotEmpty(hotelLists)) {
                        HotelListResp backHotel = hotelLists.get(0);
                        resp.setSaleCurrencyCode(backHotel.getSaleCurrencyCode());
                        resp.setSaleCurrency(backHotel.getSaleCurrency());
                        resp.setPayAtHotelCurrency(backHotel.getPayAtHotelCurrency());
                        resp.setPayAtHotelCurrencyCode(backHotel.getPayAtHotelCurrencyCode());
                        resp.setLowestPrice(backHotel.getLowestPrice());
                        resp.setPayAtHotelFee(backHotel.getPayAtHotelFee());
                    }
                }
                infoLanguageList.add(resp);
            }
            if (req.getSortBy().equals(SORT_PRICE_TYPE_LOW_TO_HIGH)) {
                // 2价格由低到高 排序 价格为空 排在最后
                infoLanguageList.sort(Comparator.comparing(HotelListResp::getLowestPrice, Comparator.nullsLast(Comparator.naturalOrder())));
            } else if (req.getSortBy().equals(SORT_PRICE_TYPE_HIGH_TO_LOW)) {
                // 3价格由高到低 排序 价格为空 排在最后
                infoLanguageList.sort(Comparator.comparing(HotelListResp::getLowestPrice, Comparator.nullsLast(Comparator.reverseOrder())));
            } else {
                // 自定义比较器
                Comparator<HotelListResp> customComparator = (hotel1, hotel2) -> {
                    BigDecimal price1 = hotel1.getLowestPrice();
                    BigDecimal price2 = hotel2.getLowestPrice();

                    // 如果两个价格都不为空，则认为它们相等，保持原有顺序
                    if (price1 != null && price2 != null) {
                        return 0;
                    }

                    // 如果第一个价格为空，第二个不为空，则第一个排在后面
                    if (price1 == null && price2 != null) {
                        return 1;
                    }

                    // 如果第二个价格为空，第一个不为空，则第二个排在后面
                    if (price1 != null) {
                        return -1;
                    }

                    // 如果两个价格都为空，则保持原有顺序（返回0）
                    return 0;
                };
                infoLanguageList.sort(customComparator);
            }

            paginationSupportDTO.setItemList(infoLanguageList);
            return paginationSupportDTO;
        } else {
            return paginationSupportDTO;
        }
    }

    @Override
    public void initHotelHeatDateTask(String param) {
        if (StrUtilX.isNotEmpty(param)) {
            List<String> hotelIds = StrUtilX.stringToList(param, ",");
            if (CollUtilX.isNotEmpty(hotelIds)) {
                List<Long> collect = hotelIds.stream().map(Long::valueOf).collect(Collectors.toList());
                // 处理数据
                processData(collect);
            }
        } else {
            int offset = 0;
            int batchSize = 2000;
            while (true) {
                HotelHeatReq hotelHeatReq = new HotelHeatReq();
                hotelHeatReq.setOffset(offset);
                hotelHeatReq.setBatchSize(batchSize);

                String request = JSONUtil.toJsonStr(hotelHeatReq);
                String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.PUBLIC_HOTEL_IDS_URL, request);
                if (StrUtilX.isEmpty(result)) {
                    log.error("获取基础信息酒店id异常，req={}", request);
                    break;
                }
                ResultX<List<Long>> resultX = JSON.parseObject(result, new TypeReference<ResultX<List<Long>>>() {
                });
                if (resultX.isError()) {
                    log.error("获取基础信息酒店id异常，req={}，resp={}", request, result);
                    break;
                }

                List<Long> hotelIds = resultX.getData();
                // 如果没有更多记录，则退出循环
                if (CollUtilX.isEmpty(hotelIds)) {
                    break;
                }

                // 处理数据
                processData(hotelIds);
                // 更新偏移量
                offset += batchSize;
            }
        }
    }

    /**
     * 处理数据
     */
    private void processData(List<Long> hotelIds) {
        if (CollUtilX.isEmpty(hotelIds)) {
            return;
        }
        // 查询热度表酒店id列表
        HotelHeatReq req = new HotelHeatReq();
        req.setHotelIds(hotelIds);
        Set<String> heatHotelIds = hotelHeatMapper.selectHotelIdByHotelIds(req);
        // 临时存放数据
        List<HotelHeatEntity> hotelHeatEntities = new ArrayList<>();
        Date currentDate = DateUtilX.getCurrentDate();
        for (Long hotelId : hotelIds) {
            // 存在不新增
            if (CollUtilX.isNotEmpty(heatHotelIds) && heatHotelIds.contains(String.valueOf(hotelId))) {
                continue;
            }
            HotelHeatEntity entity = getHotelHeatEntity(hotelId, currentDate);
            hotelHeatEntities.add(entity);
        }

        // 保存数据
        if (CollUtilX.isNotEmpty(hotelHeatEntities)) {
            hotelHeatService.saveBatch(hotelHeatEntities);
        }
    }

    /**
     * 获取酒店热度表实体
     * @param hotelId     酒店id
     * @param currentDate 当前日期
     * @return 酒店热度实体
     */
    private static HotelHeatEntity getHotelHeatEntity(Long hotelId, Date currentDate) {
        HotelHeatEntity entity = new HotelHeatEntity();
        entity.setHotelId(hotelId);
        entity.setHeatScore(0L);
        entity.setBookableScore(0L);
        entity.setGradeScore(0L);
        entity.setGroupScore(0L);
        entity.setCityAvgPriceScore(0L);
        entity.setRoomNightScore(0L);
        entity.setCreatedBy(Constant.SYSTEM);
        entity.setCreatedDt(currentDate);
        entity.setUpdatedBy(Constant.SYSTEM);
        entity.setUpdatedDt(currentDate);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateHotelHeatGroupScoreTask(String param) {
        List<String> hotelIds;
        if (StrUtilX.isNotEmpty(param)) {
            hotelIds = StrUtilX.stringToList(param, ",");
        } else {
            if (!RedisTemplateX.hasKey(RedisKey.CALCULATE_GROUP_SCORE_HOTEL_ID_KEY)) {
                return;
            }
            hotelIds = RedisTemplateX.setPopCount(RedisKey.CALCULATE_GROUP_SCORE_HOTEL_ID_KEY, settingsConstant.getConsumerCalculateGroupScoreCount());
        }
        if (CollUtilX.isEmpty(hotelIds)) {
            return;
        }
        // 计算酒店热度评分和集团分数
        calculateHotelHeatGroupScore(hotelIds);
    }

    /**
     * 计算酒店热度评分和集团分数
     */
    private void calculateHotelHeatGroupScore(List<String> hotelIds) {
        // 参数校验
        if (CollUtil.isEmpty(hotelIds)) {
            return;
        }

        // 分布式锁配置
        final int maxRetries = 3;
        final long baseSleep = 1000;
        int retryCount = 0;
        String redisValue = IDUtilS.getId_36();

        while (retryCount < maxRetries) {
            // 尝试获取分布式锁
            Boolean lock = RedisTemplateX.setIfAbsent(RedisKey.HOTEL_HEAT_HEAT_SCORE_LOCK, redisValue, 10L);
            if (Boolean.TRUE.equals(lock)) {
                try {
                    // 1. 获取酒店信息
                    List<HotelSummary> hotelSummaries = fetchHotelSummaries(hotelIds);
                    if (CollUtil.isEmpty(hotelSummaries)) {
                        return;
                    }

                    // 2. 预处理配置数据
                    Set<String> notGroupIdSet = parseNotGroupIds(settingsConstant.getNotGroupIds());
                    Set<String> brandIdSet = parseBrandIds(settingsConstant.getBrandIds());
                    BigDecimal gradeThreshold = settingsConstant.getGradeScore();
                    Date currentDate = DateUtilX.getCurrentDate();

                    // 3. 批量处理酒店数据
                    List<Tuple2<Long, HotelHeatReq>> result = hotelSummaries.stream()
                            .map(item -> {
                                HotelHeatReq req = new HotelHeatReq();
                                req.setHotelId(item.getHotelId());
                                req.setGroupScore(calculateGroupScore(item, brandIdSet, notGroupIdSet));
                                req.setGradeScore(calculateGradeScore(item, gradeThreshold));
                                req.setUpdatedBy(Constant.SYSTEM);
                                req.setUpdatedDt(currentDate);
                                return Tuples.of(item.getHotelId(), req);
                            })
                            .collect(Collectors.toList());

                    // 4. 分批次更新数据
                    List<HotelHeatReq> collect = result.stream()
                            .map(Tuple2::getT2)
                            .collect(Collectors.toList());
                    List<Long> hotelIdList = result.stream()
                            .map(Tuple2::getT1)
                            .collect(Collectors.toList());

                    // 分批次更新具体项分数
                    int batchSize = 500;
                    CollUtil.split(collect, batchSize).forEach(batch ->
                            hotelHeatMapper.updateBatchFields(batch)
                    );

                    // 分批次更新总分值
                    CollUtil.split(hotelIdList, 1000).forEach(batch -> {
                        HotelHeatReq req = new HotelHeatReq();
                        req.setHotelIds(batch);
                        req.setUpdatedBy(Constant.SYSTEM);
                        req.setUpdatedDt(currentDate);
                        hotelHeatMapper.updateHotelHeatScoreTotalValue(req);
                    });

                    // 5. 异步保存酒店ID到缓存
                    CompletableFuture.runAsync(() -> {
                        String[] hotelIdArray = hotelIdList.stream().map(String::valueOf).toArray(String[]::new);
                        RedisTemplateX.setAdd(RedisKey.HOTEL_HEAT_SYNC_MONGODB_HOTEL_ID_KEY, hotelIdArray);
                    }, processingCommonExecutor);

                    return; // 成功执行后退出
                } catch (Exception e) {
                    log.error("计算酒店热度评分和集团分数任务异常", e);
                    // 异常重新插入到缓存
                    String[] ids = hotelIds.toArray(new String[0]);
                    RedisTemplateX.setAdd(RedisKey.CALCULATE_GROUP_SCORE_HOTEL_ID_KEY, ids);
                    // 需要回滚事务，重新抛出异常
                    throw e;
                } finally {
                    // 注册事务同步器
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            // 删除锁
                            RedisTemplateX.execute(RedisKey.HOTEL_HEAT_HEAT_SCORE_LOCK, redisValue);
                        }
                    });
                }
            }

            // 锁获取失败处理
            try {
                Thread.sleep(baseSleep * (1L << retryCount));
                retryCount++;
            } catch (InterruptedException e) {
                log.error("修改热度分数，锁获取异常", e);
                break;
            }
        }
        log.warn("计算酒店热度评分和集团分数失败，已达最大重试次数");
    }

    /**
     * 获取酒店信息
     */
    private List<HotelSummary> fetchHotelSummaries(List<String> hotelIds) {
        JSONObject hotelReq = new JSONObject();
        hotelReq.put(FieldConstants.LANGUAGE_TYPE, LanguageTypeEnum.en_US.getValue());
        hotelReq.put(FieldConstants.HOTEL_IDS, hotelIds);
        hotelReq.put("settings", CollUtil.newHashSet(
                BaseHotelInfoUrl.hotelName,
                BaseHotelInfoUrl.group,
                BaseHotelInfoUrl.brand,
                BaseHotelInfoUrl.rating
        ));

        String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.HOTEL_BASE_INFO, hotelReq.toJSONString());
        if (StrUtilX.isEmpty(result)) {
            return Collections.emptyList();
        }

        ResultX<List<HotelSummary>> resultX = JSONObject.parseObject(result, new TypeReference<ResultX<List<HotelSummary>>>() {
        });
        return resultX.isError() || CollUtilX.isEmpty(resultX.getData())
                ? Collections.emptyList()
                : resultX.getData();
    }

    /**
     * 解析非集团ID
     */
    private Set<String> parseNotGroupIds(String notGroupIds) {
        return StrUtilX.isNotEmpty(notGroupIds)
                ? new HashSet<>(Arrays.asList(notGroupIds.split(",")))
                : Collections.emptySet();
    }

    /**
     * 解析品牌ID
     */
    private Set<String> parseBrandIds(String brandIds) {
        return StrUtilX.isNotEmpty(brandIds)
                ? new HashSet<>(Arrays.asList(brandIds.split(",")))
                : Collections.emptySet();
    }

    /**
     * 计算集团分数
     */
    private Long calculateGroupScore(HotelSummary item, Set<String> brandIdSet, Set<String> notGroupIdSet) {
        if (isValidGroup(item.getGroup(), notGroupIdSet)) {
            return isSpecifiedBrand(item.getBrand(), brandIdSet) ? 30000L : 20000L;
        }
        return 10000L;
    }

    /**
     * 判断集团有效性
     */
    private boolean isValidGroup(GroupDTO group, Set<String> notGroupIdSet) {
        return group != null
                && StrUtilX.isNotEmpty(group.getGroupId())
                && !"0".equals(group.getGroupId())
                && (notGroupIdSet.isEmpty() || !notGroupIdSet.contains(group.getGroupId()));
    }

    /**
     * 判断品牌是否指定
     */
    private boolean isSpecifiedBrand(BrandDTO brand, Set<String> brandIdSet) {
        return brand != null
                && StrUtilX.isNotEmpty(brand.getBrandId())
                && !brandIdSet.isEmpty()
                && brandIdSet.contains(brand.getBrandId());
    }

    /**
     * 计算评分分数
     */
    private Long calculateGradeScore(HotelSummary item, BigDecimal threshold) {
        return (item.getRating() != null
                && item.getRating().getOverall() != null
                && item.getRating().getOverall().compareTo(threshold) >= 0)
                ? 2000000L
                : 1000000L;
    }

    @Override
    public Response<List<AddressRespDTO>> getAddress(Map<String, String> paramMap) {
        if (StringUtils.isEmpty(paramMap.get(FIELD_KEYWORD))) {
            return Response.error(ErrorCodeEnum.KEY_WORD_NOT_EMPTY.errorCode, ErrorCodeEnum.KEY_WORD_NOT_EMPTY.errorDesc);
        }
        JSONObject hotelReq = new JSONObject();
        hotelReq.put(FIELD_KEYWORD, paramMap.get(FIELD_KEYWORD));

        /*
         * 0605版本，目的地接口新增城市编码和目的地类型
         * 增加的2个参数可为空
         * by. 雷燕军
         */
        if (!StringUtils.isEmpty(paramMap.get(FIELD_CITY_CODE))) {
            hotelReq.put(FIELD_CITY_CODE, paramMap.get(FIELD_CITY_CODE));
        }

        if (!StringUtils.isEmpty(paramMap.get(FIELD_DATA_TYPE))) {
            hotelReq.put(FIELD_DATA_TYPE, paramMap.get(FIELD_DATA_TYPE));
        }


        String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.GET_ADDRESS, hotelReq.toJSONString());
        if (StrUtilX.isEmpty(result)) {
            log.error("获取地址数据失败----返回结果为空 req：{}", hotelReq.toJSONString());
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (Objects.nonNull(jsonObject)) {
            String code = jsonObject.getString("code");
            if (RESPONSE_SUCCESS_CODE.equals(code)) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (Objects.nonNull(data)) {
                    JSONArray records = data.getJSONArray("list");
                    List<AddressRespDTO> listResultX = JSON.parseObject(records.toJSONString(), new TypeReference<List<AddressRespDTO>>() {
                    });
                    return Response.success(listResultX);
                }
            }
        }
        log.error("获取地址数据失败----基础信息返回的数据是：{}", result);
        return Response.success(null);
    }

    /**
     * 获取地址数据
     */
    @Override
    public AddressDocVo getAddressById(String addressId) {
        JSONObject hotelReq = new JSONObject();
        hotelReq.put("destinationId", addressId);
        String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.GET_ADDRESS_BY_ID, hotelReq.toJSONString());
        if (StrUtilX.isEmpty(result)) {
            log.error("获取地址数据失败1----基础信息返回的数据是：{}", result);
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(result, JSONObject.class);
        if (Objects.nonNull(jsonObject)) {
            String code = jsonObject.getString("code");
            if (RESPONSE_SUCCESS_CODE.equals(code)) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (Objects.nonNull(data)) {
                    return JSON.parseObject(data.toJSONString(), AddressDocVo.class);
                }
            }
        }
        return null;
    }

    @Override
    public Response<HotelSearchResp> getHotelSearch(HotelSearchReq hotelSearchReq) {
        AddressDocVo address = getAddressById(hotelSearchReq.getDestinationId());
        if (Objects.isNull(address)) {
            throw new SysException(ErrorCodeEnum.ADDRESS_BY_ID_IS_ERROR);
        }
        HotelReq req = getHotelReq(address);
        //嵌套查询
        Criteria criteria = new Criteria();
        if (req.getHotelId() != null) {
            criteria.and("_id").is(req.getHotelId());
        }
        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(req.getCountryCode())) {
            criteria.and("countryCode").is(req.getCountryCode());
        }
        if (CollectionUtils.isNotEmpty(req.getHotelIds())) {
            criteria.and("hotelId").in(req.getHotelIds());
        }
        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(req.getProvinceCode())) {
            criteria.and("provinceCode").is(req.getProvinceCode());
        }
        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(req.getCityCode())) {
            criteria.and(FieldConstants.CITY_CODE).is(req.getCityCode());
        }
        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(req.getDistrictCode())) {
            criteria.and("districtCode").is(req.getDistrictCode());
        }
        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(req.getBusinessZoneCode())) {
            criteria.and("businessZoneCode").is(req.getBusinessZoneCode());
        }
        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(req.getGroupCode())) {
            criteria.and("groupCode").is(req.getGroupCode());
        }
        if (com.alibaba.nacos.common.utils.StringUtils.isNotBlank(req.getBrandCode())) {
            criteria.and("brandCode").is(req.getBrandCode());
        }

        HotelSearchResp resp = new HotelSearchResp();

        //过滤无条件的数据
        if (criteria.getCriteriaObject().isEmpty()) {
            return Response.success(resp);
        }

        CountDownLatch hotelSearchCountDownLatch = new CountDownLatch(6);

        executor.execute(new Runnable() {
            @Autowired
            @Override
            public void run() {
                try{
                    Date startDate = new Date();
                    //星级分组
                    Aggregation hotelStarAggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria), // 普通条件
                            Aggregation.group("hotelStar")
                    );

                    AggregationResults<JSONObject> hotelStar = mongoTemplateBasic.aggregate(hotelStarAggregation, HotelPublicCollection.class, JSONObject.class);
                    //组装星级数据
                    if (CollectionUtils.isNotEmpty(hotelStar.getMappedResults())) {
                        List<JSONObject> mappedResults = hotelStar.getMappedResults();
                        List<HotelStarResp> hotelStarRespList = new ArrayList<>();
                        for (JSONObject object : mappedResults) {
                            HotelStarEnum hotelStarEnum = HotelStarEnum.getEnumByKey(object.getString("_id"));
                            if (Objects.nonNull(hotelStarEnum)) {
                                HotelStarResp hotelStarResp = new HotelStarResp();
                                hotelStarResp.setHotelStarId(Integer.parseInt(hotelStarEnum.getStarCode()));
                                hotelStarResp.setHotelStarName(hotelStarEnum.getStarName());
                                hotelStarRespList.add(hotelStarResp);
                            }
                        }
                        hotelStarRespList.sort(Comparator.comparingInt(HotelStarResp::getHotelStarId));

                        resp.setHotelStarList(hotelStarRespList);
                    }
                    log.info("getHotelSearch.hotelStarAggregation.costTime:{}",System.currentTimeMillis() - startDate.getTime());
                }catch (Exception e){
                    log.error("getHotelSearch.hotelStarAggregationException", e);
                }finally {
                    hotelSearchCountDownLatch.countDown();
                }
            }
        });


        executor.execute(new Runnable() {
            @Autowired
            @Override
            public void run() {
                try {
                    Date startDate = new Date();
                    //品牌分组
                    Aggregation brandAggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria), // 普通条件
                            Aggregation.group("brandCode")
                    );
                    AggregationResults<JSONObject> brand = mongoTemplateBasic.aggregate(brandAggregation, HotelPublicCollection.class, JSONObject.class);
                    if (CollectionUtils.isNotEmpty(brand.getMappedResults())) {
                        List<JSONObject> mappedResults = brand.getMappedResults();
                        List<String> brandCodeList = new ArrayList<>();
                        for (JSONObject object : mappedResults) {
                            brandCodeList.add(object.getString("_id"));
                        }
                        HotelBrandDTO hotelBrandDTO = new HotelBrandDTO();
                        hotelBrandDTO.setBrandCodeList(brandCodeList);
                        hotelBrandDTO.setLanguage(hotelSearchReq.getLanguage());
                        List<HotelBrandResp> hotelBrandRespList = fuzzyQueryMapper.queryBrandByBrandCodes(hotelBrandDTO);

                        resp.setHotelBrandList(hotelBrandRespList);
                    }
                    log.info("getHotelSearch.brandAggregation.costTime:{}",System.currentTimeMillis() - startDate.getTime());
                } catch (Exception e) {
                    log.error("getHotelSearch.brandAggregationException", e);
                } finally {
                    hotelSearchCountDownLatch.countDown();
                }
            }
        });


        executor.execute(new Runnable() {
            @Autowired
            @Override
            public void run() {
                try {
                    Date startDate = new Date();
                    //集团分组
                    Aggregation groupAggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria), // 普通条件
                            Aggregation.group("groupCode")
                    );
                    AggregationResults<JSONObject> group = mongoTemplateBasic.aggregate(groupAggregation, HotelPublicCollection.class, JSONObject.class);

                    if (CollectionUtils.isNotEmpty(group.getMappedResults())) {
                        List<JSONObject> mappedResults = group.getMappedResults();
                        List<String> groupCodeList = new ArrayList<>();
                        for (JSONObject object : mappedResults) {
                            groupCodeList.add(object.getString("_id"));
                        }
                        HotelGroupDTO groupDTO = new HotelGroupDTO();
                        groupDTO.setGroupCodeList(groupCodeList);
                        groupDTO.setLanguage(hotelSearchReq.getLanguage());
                        List<HotelGroupResp> hotelGroupRespList = fuzzyQueryMapper.queryGroupByGroupCodes(groupDTO);

                        resp.setHotelGroupList(hotelGroupRespList);
                    }
                    log.info("getHotelSearch.groupAggregation.costTime:{}",System.currentTimeMillis() - startDate.getTime());
                } catch (Exception e) {
                    log.error("getHotelSearch.groupAggregationException", e);
                } finally {
                    hotelSearchCountDownLatch.countDown();
                }
            }
        });

        executor.execute(new Runnable() {
            @Autowired
            @Override
            public void run() {
                try {
                    Date startDate = new Date();
                    //酒店标签分组
                    Aggregation hotelLabelAggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria), // 普通条件
                            Aggregation.unwind("hotelAndAgentCodeLabelDTO"),
                            Aggregation.match(new Criteria().and("hotelAndAgentCodeLabelDTO.agentCode").in(hotelSearchReq.getAgentCode(), "0")),
                            Aggregation.unwind("hotelAndAgentCodeLabelDTO.hotelLabelConfigIds"),
                            Aggregation.group("hotelAndAgentCodeLabelDTO.hotelLabelConfigIds")
                    );
                    AggregationResults<JSONObject> hotelLabel = mongoTemplateBasic.aggregate(hotelLabelAggregation, HotelPublicCollection.class, JSONObject.class);
                    if (CollectionUtils.isNotEmpty(hotelLabel.getMappedResults())) {
                        List<JSONObject> mappedResults = hotelLabel.getMappedResults();
                        HotelLabelDTO labelDTO = new HotelLabelDTO();
                        List<Integer> list = new ArrayList<>();
                        for (JSONObject object : mappedResults) {
                            list.add(object.getInteger("_id"));
                        }
                        labelDTO.setList(list);
                        List<HotelLabelResp> hotelLabelRespList = hotelLabelConfigMapper.queryHotelLabelByIds(labelDTO);

                        resp.setHotelLabelList(hotelLabelRespList);
                    }
                    log.info("getHotelSearch.hotelLabelAggregation.costTime:{}",System.currentTimeMillis() - startDate.getTime());
                } catch (Exception e) {
                    log.error("getHotelSearch.hotelLabelAggregationException", e);
                } finally {
                    hotelSearchCountDownLatch.countDown();
                }
            }
        });

        executor.execute(new Runnable() {
            @Autowired
            @Override
            public void run() {
                try {
                    Date startDate = new Date();
                    //酒店类型分组
                    Aggregation hotelSubCategoryAggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria), // 普通条件
                            Aggregation.group("hotelSubCategory")
                    );
                    AggregationResults<JSONObject> hotelSubCategory = mongoTemplateBasic.aggregate(hotelSubCategoryAggregation, HotelPublicCollection.class, JSONObject.class);
                    //组装类型数据
                    if (CollectionUtils.isNotEmpty(hotelSubCategory.getMappedResults())) {
                        List<JSONObject> mappedResults = hotelSubCategory.getMappedResults();
                        List<HotelSubCategoryResp> hotelSubCategoryRespList = new ArrayList<>();
                        for (JSONObject object : mappedResults) {
                            SubCategoryEnum subCategoryEnum = SubCategoryEnum.getEnumBySubCategoryId(object.getString("_id"));
                            if (Objects.nonNull(subCategoryEnum)) {
                                HotelSubCategoryResp hotelSubCategoryResp = new HotelSubCategoryResp();
                                hotelSubCategoryResp.setHotelSubCategoryId(Integer.parseInt(subCategoryEnum.getSubCategoryId()));
                                hotelSubCategoryResp.setHotelSubCategoryName(subCategoryEnum.getSubCategoryChn());
                                hotelSubCategoryRespList.add(hotelSubCategoryResp);
                            }
                        }
                        resp.setHotelSubCategoryList(hotelSubCategoryRespList);
                    }
                    log.info("getHotelSearch.hotelSubCategoryAggregation.costTime:{}",System.currentTimeMillis() - startDate.getTime());
                } catch (Exception e) {
                    log.error("getHotelSearch.hotelSubCategoryAggregationException", e);
                } finally {
                    hotelSearchCountDownLatch.countDown();
                }
            }
        });


        executor.execute(new Runnable() {
            @Autowired
            @Override
            public void run() {
                try {
                    Date startDate = new Date();
                    //热门设施分组
                    Aggregation hotFacilityAggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria), // 普通条件
                            Aggregation.unwind("hotFacility"),
                            Aggregation.group("hotFacility")
                    );
                    AggregationResults<JSONObject> hotFacility = mongoTemplateBasic.aggregate(hotFacilityAggregation, HotelPublicCollection.class, JSONObject.class);
                    //组装热门设施数据
                    if (CollectionUtils.isNotEmpty(hotFacility.getMappedResults())) {
                        List<JSONObject> mappedResults = hotFacility.getMappedResults();
                        List<String> list = new ArrayList<>();
                        for (JSONObject object : mappedResults) {
                            list.add(object.getString("_id"));
                        }
                        List<HotFacilityDTO> hotFacilityDTOS = facilityMapper.queryHotFacilityCode(list);
                        List<HotFacilityDTO> hotelFacilityList = hotFacilityDTOS.stream().filter(o -> o.getFacilityType() == 1).collect(Collectors.toList());
                        List<HotFacilityDTO> roomFacilityList = hotFacilityDTOS.stream().filter(o -> o.getFacilityType() == 2).collect(Collectors.toList());

                        resp.setHotelFacilityList(hotelFacilityList);
                        resp.setRoomFacilityList(roomFacilityList);
                    }
                    log.info("getHotelSearch.hotFacilityAggregation.costTime:{}",System.currentTimeMillis() - startDate.getTime());
                } catch (Exception e) {
                    log.error("getHotelSearch.hotFacilityAggregationException", e);
                } finally {
                    hotelSearchCountDownLatch.countDown();
                }
            }
        });
        try {
            // 等待两个线程执行完毕 时常 1000ms
            hotelSearchCountDownLatch.await(20000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("并发获取酒店过滤条件超时异常", e);
        }
        return Response.success(resp);
    }

    @Override
    public void updateHotelScoreToMongodbBatch(UpdateHotelScoreReq req) {
        BulkOperations bulkOps = mongoTemplateBasic.bulkOps(BulkOperations.BulkMode.UNORDERED, HotelPublicCollection.class);

        //更新热度值
        if (CollectionUtils.isNotEmpty(req.getHeatValueReqList())) {
            List<HeatValueReq> heatValueReqList = req.getHeatValueReqList();
            int i = 0;
            for (HeatValueReq heatValueReq : heatValueReqList) {
                Query query = new Query();
                query.addCriteria(Criteria.where("_id").is(heatValueReq.getHotelId()));
                Update update1 = new Update()
                        .set("heatValue", heatValueReq.getHeatValue());
                bulkOps.updateOne(query, update1);
                i++;
                if (i % 100 == 0) {
                    bulkOps.execute();
                    bulkOps = mongoTemplateBasic.bulkOps(BulkOperations.BulkMode.UNORDERED, HotelPublicCollection.class);
                    i = 0;
                }
            }
            if (i > 0) {
                bulkOps.execute();
            }
        }

        //更新推荐值
        if (CollectionUtils.isNotEmpty(req.getRecommendScoreReqList())) {
            List<RecommendScoreReq> recommendScoreReqList = req.getRecommendScoreReqList();
            int i = 0;
            for (RecommendScoreReq recommendScoreReq : recommendScoreReqList) {
                HotelPublicCollection hotelPublicCollection = mongoTemplateBasic.findById(recommendScoreReq.getHotelId(), HotelPublicCollection.class);
                if (Objects.isNull(hotelPublicCollection)) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(recommendScoreReq.getAgentRecommendScoreReqList())) {
                    List<AgentRecommendScoreReq> agentRecommendScoreReqList = recommendScoreReq.getAgentRecommendScoreReqList();
                    for (AgentRecommendScoreReq agentRecommendScoreReq : agentRecommendScoreReqList) {
                        List<LowestPriceDTO> lowestPriceDTO = hotelPublicCollection.getLowestPriceAndRecommendDTO();
                        List<LowestPriceDTO> collect = lowestPriceDTO.stream().filter(o -> o.getAgentCode().equals(agentRecommendScoreReq.getAgentCode())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            LowestPriceDTO recommendScore = collect.get(0);
                            recommendScore.setRecommendScore(agentRecommendScoreReq.getRecommendScore());

                            lowestPriceDTO.removeAll(collect);
                            lowestPriceDTO.add(recommendScore);
                        } else {
                            LowestPriceDTO recommendScore = new LowestPriceDTO();
                            recommendScore.setAgentCode(agentRecommendScoreReq.getAgentCode());
                            recommendScore.setRecommendScore(agentRecommendScoreReq.getRecommendScore());
                            lowestPriceDTO.add(recommendScore);
                        }
                    }

                }
                Query query = new Query();
                query.addCriteria(Criteria.where("_id").is(recommendScoreReq.getHotelId()));
                Update update1 = new Update()
                        .set("lowestPriceAndRecommendDTO", hotelPublicCollection.getLowestPriceAndRecommendDTO());
                bulkOps.updateMulti(query, update1);
                i++;
                if (i % 100 == 0) {
                    bulkOps.execute();
                    bulkOps = mongoTemplateBasic.bulkOps(BulkOperations.BulkMode.UNORDERED, HotelPublicCollection.class);
                    i = 0;
                }
            }
            if (i > 0) {
                bulkOps.execute();
            }
        }
    }

    /**
     * 获取酒店信息
     */
    private static HotelReq getHotelReq(AddressDocVo address) {
        HotelReq req = new HotelReq();
        switch (address.getDataType()) {
            //酒店
            case 1:
                req.setHotelId(Long.parseLong(address.getDataTypeId()));
                break;
            //城市
            case 2:
                req.setCityCode(address.getDataTypeId());
                break;
            //行政区
            case 3:
                req.setDistrictCode(address.getDataTypeId());
                break;
            //商业区
            case 4:
                req.setBusinessZoneCode(address.getDataTypeId());
                break;
            //旅游区
            case 5:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //城市及周边
            case 6:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //比城市小的区
            case 7:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //地点
            case 8:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //省份
            case 9:
                req.setProvinceCode(address.getDataTypeId());
                break;
            //国家
            case 10:
                req.setCountryCode(address.getDataTypeId());
                break;
            //集团
            case 11:
                req.setGroupCode(address.getDataTypeId());
                break;
            //品牌
            case 12:
                req.setBrandCode(address.getDataTypeId());
                break;
            default:
                break;
        }
        return req;
    }

    /**
     * 获取酒店请求对象
     */
    private static HotelReq getHotelReqKeyWordHotelIds(AddressDocVo address, Set<Long> hotelIds) {
        HotelReq req = new HotelReq();
        switch (address.getDataType()) {
            //酒店
            case 1:
                req.setHotelId(Long.parseLong(address.getDataTypeId()));
                break;
            //城市
            case 2:
                req.setCityCode(address.getDataTypeId());
                if (CollectionUtil.isNotEmpty(hotelIds)) {
                    req.setHotelIds(new ArrayList<>(hotelIds));
                }
                break;
            //行政区
            case 3:
                req.setDistrictCode(address.getDataTypeId());
                if (CollectionUtil.isNotEmpty(hotelIds)) {
                    req.setHotelIds(new ArrayList<>(hotelIds));
                }
                break;
            //商业区
            case 4:
                req.setBusinessZoneCode(address.getDataTypeId());
                if (CollectionUtil.isNotEmpty(hotelIds)) {
                    req.setHotelIds(new ArrayList<>(hotelIds));
                }
                break;
            //旅游区
            case 5:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else if (CollectionUtils.isNotEmpty(address.getHotelIds()) && CollectionUtil.isNotEmpty(hotelIds)) {
                    req.setHotelIds(filterHotelIds(address.getHotelIds(), hotelIds));
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //城市及周边
            case 6:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else if (CollectionUtils.isNotEmpty(address.getHotelIds()) && CollectionUtil.isNotEmpty(hotelIds)) {
                    req.setHotelIds(filterHotelIds(address.getHotelIds(), hotelIds));
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //比城市小的区
            case 7:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else if (CollectionUtils.isNotEmpty(address.getHotelIds()) && CollectionUtil.isNotEmpty(hotelIds)) {
                    req.setHotelIds(filterHotelIds(address.getHotelIds(), hotelIds));
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //地点
            case 8:
                if (CollectionUtils.isEmpty(address.getHotelIds())) {
                    req.setHotelIds(new ArrayList<>());
                } else if (CollectionUtils.isNotEmpty(address.getHotelIds()) && CollectionUtil.isNotEmpty(hotelIds)) {
                    req.setHotelIds(filterHotelIds(address.getHotelIds(), hotelIds));
                } else {
                    req.setHotelIds(new ArrayList<>(address.getHotelIds()));
                }
                break;
            //省份
            case 9:
                req.setProvinceCode(address.getDataTypeId());
                break;
            //国家
            case 10:
                req.setCountryCode(address.getDataTypeId());
                break;
            //集团
            case 11:
                req.setGroupCode(address.getDataTypeId());
                break;
            //品牌
            case 12:
                req.setBrandCode(address.getDataTypeId());
                break;
            default:
                break;
        }
        return req;
    }

    /**
     * 筛选酒店id
     */
    private static List<Long> filterHotelIds(Set<Long> hotelIds, Set<Long> hotelIds1) {
        hotelIds.retainAll(hotelIds1);
        if (CollectionUtil.isEmpty(hotelIds)) {
            // 目的地所属酒店id 和 关键字筛选后酒店id 不存在交际,则直接抛出异常 外层拦截 返回空 ！！！
            throw new RuntimeException("不存在交集数据");
        }
        return new ArrayList<>(hotelIds);
    }
}
