package com.tiangong.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.FacilityDto;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.BaseinfoFacility;
import com.tiangong.hotel.domain.FacilityConfigEntity;
import com.tiangong.hotel.domain.req.FacilityAllReq;
import com.tiangong.hotel.domain.req.FacilityEditReq;
import com.tiangong.hotel.domain.req.FacilityListReq;
import com.tiangong.hotel.domain.req.FacilityScoreEditReq;
import com.tiangong.hotel.domain.resp.FacilityInfoAndConfigResp;
import com.tiangong.hotel.domain.resp.FacilityListResp;
import com.tiangong.hotel.domain.resp.FacilityNameResp;
import com.tiangong.hotel.domain.resp.FacilityResp;
import com.tiangong.hotel.mapper.BaseinfoFacilityMapper;
import com.tiangong.hotel.mapper.FacilityConfigMapper;
import com.tiangong.hotel.mapper.FacilityMapper;
import com.tiangong.hotel.service.BaseinfoFacilityService;
import com.tiangong.hotel.service.HotelService;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.SysConfigPO;
import com.tiangong.organization.service.SysConfigService;
import com.tiangong.redis.core.RedisTemplateX;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BaseinfoFacilityServiceImpl extends ServiceImpl<BaseinfoFacilityMapper, BaseinfoFacility> implements BaseinfoFacilityService {

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private FacilityMapper facilityMapper;

    @Autowired
    private FacilityConfigMapper facilityConfigMapper;

    @Autowired
    private HotelService hotelService;


    /**
     * 类型分数最大值
     */
    private final static int TYPE_SCORE_MAX_VALUE = 99;

    /**
     * 类型分数最小值
     */
    private final static  int TYPE_SCORE_MIN_VALUE = 0;
    @Override
    public void addOrUpdateFacility(BaseinfoFacility baseinfoFacility) {
        if (baseinfoFacility.getId() == null) {
            BaseinfoFacility facility = queryFacility();
            if (facility != null) {
                throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION);
            }
            this.save(baseinfoFacility);
        } else {
            this.updateById(baseinfoFacility);
        }
        // 清理缓存
        hotelService.clearFacilityConfigCache();
    }


    @Override
    public BaseinfoFacility queryFacility() {
        List<BaseinfoFacility> baseinfoFacilities = this.getBaseMapper().selectList(new LambdaQueryWrapper<>());
        if (baseinfoFacilities != null && baseinfoFacilities.size() > 0) {
            return baseinfoFacilities.get(0);
        }
        return null;
    }

    @Override
    public FacilityInfoAndConfigResp queryFacilityManage() {
        FacilityInfoAndConfigResp resp = new FacilityInfoAndConfigResp();
        List<BaseinfoFacility> baseinfoFacilities = this.getBaseMapper().selectList(new LambdaQueryWrapper<>());
        if (baseinfoFacilities != null && baseinfoFacilities.size() > 0) {
            BaseinfoFacility facility = baseinfoFacilities.get(0);
            FacilityDto dto = new FacilityDto();
            BeanUtils.copyProperties(facility, dto);
            resp.setFacilityDto(dto);
        }
        SysConfigPO travelNoticePolicy = sysConfigService.queryConfig("travelNoticePolicy");
        resp.setTravelNoticePolicy(travelNoticePolicy);

        SysConfigPO enableSendMsg = sysConfigService.queryConfig("enableSendMsg");
        resp.setEnableSendMsg(enableSendMsg);
        return resp;
    }

    @Override
    public FacilityResp queryFacilityList(FacilityListReq req) {
        FacilityResp resp = new FacilityResp();
        List<FacilityListResp> hotelFacility = facilityMapper.queryFacility(1, req.getLanguage());
        List<FacilityListResp> roomFacility = facilityMapper.queryFacility(2, req.getLanguage());

        resp.setHotelFacilityList(hotelFacility);
        resp.setRoomFacilityList(roomFacility);
        return resp;
    }

    @Override
    public void facilityEdit(FacilityEditReq req) {
        String facilityIds = null;
        if (StringUtils.isNotEmpty(req.getFacilityIdList())) {
            facilityIds = req.getFacilityIdList().replaceAll(",+", ",").replaceAll("^,|,$", "").trim();
        }
        facilityConfigMapper.update(new FacilityConfigEntity(), new UpdateWrapper<FacilityConfigEntity>().lambda()
                .set(FacilityConfigEntity::getFacilityIdList, facilityIds)
                .eq(FacilityConfigEntity::getFacilityType, req.getFacilityType())
                .eq(FacilityConfigEntity::getFacilityCode, req.getFacilityCode())

        );

        // 清理缓存
        hotelService.clearFacilityConfigCache();

        //将变更的热门设施推送到队列
        RedisTemplateX.lRightPush(RedisKey.HOT_FACILITY_QUEUE, req.getFacilityCode());
    }

    @Override
    public PaginationSupportDTO<FacilityNameResp> facilityPage(FacilityAllReq req) {

        IPage<FacilityNameResp> iPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<FacilityNameResp> page = new Page<>();
        if (req.getAddOrDel() == 1) {
            FacilityConfigEntity facilityConfigEntity = facilityConfigMapper.selectOne(new LambdaQueryWrapper<FacilityConfigEntity>().eq(FacilityConfigEntity::getFacilityCode, req.getFacilityCode()));
            page = facilityMapper.queryFacilityPage(iPage, req.getFacilityType(), req.getFacilityName(), facilityConfigEntity.getFacilityIdList(), req.getLanguage());
        }else {
            page = facilityMapper.selectFacilityNameByFacilityCode(iPage, req.getFacilityCode(), req.getFacilityName(), req.getLanguage());
        }
        //转换分页参数输出
        PaginationSupportDTO<FacilityNameResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PaginationSupportDTO<FacilityNameResp> paginationSupport = paginationSupportDTO.getPaginationSupportDTO(page);

        return paginationSupport;

    }

    @Override
    public void facilityScoreEdit(FacilityScoreEditReq req) {
        if (req.getTypeScore() < TYPE_SCORE_MIN_VALUE){
            throw new SysException(ErrorCodeEnum.TYPE_SCORE_MIN_ERROR);
        }
        if (req.getTypeScore() > TYPE_SCORE_MAX_VALUE){
            throw new SysException(ErrorCodeEnum.TYPE_SCORE_MAX_ERROR);
        }
        facilityConfigMapper.update(new FacilityConfigEntity(), new UpdateWrapper<FacilityConfigEntity>().lambda()
                .set(FacilityConfigEntity::getTypeScore, req.getTypeScore())
                .eq(FacilityConfigEntity::getFacilityCode, req.getFacilityCode())
        );
        // 清理缓存
        hotelService.clearFacilityConfigCache();
    }


}
