package com.tiangong.hotel.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.CityReq;
import com.tiangong.dis.dto.CountryReq;
import com.tiangong.dis.dto.DistrictOrBusinessReq;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dto.common.HotelPublicDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzyRoomDTO;
import com.tiangong.hotel.domain.BaseinfoGroupPO;
import com.tiangong.hotel.domain.BaseinfoHotCityEntity;
import com.tiangong.hotel.domain.req.AddOrUpdateHotCityReq;
import com.tiangong.hotel.domain.req.QueryGroupReq;
import com.tiangong.hotel.domain.resp.CityResp;
import com.tiangong.hotel.domain.resp.SelectCityListReq;
import com.tiangong.hotel.domain.vo.AddressDocVo;
import com.tiangong.hotel.dto.BaseinfoAreadataDTO;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;
import com.tiangong.hotel.service.BaseinfoAreadataService;
import com.tiangong.hotel.service.HotelService;
import com.tiangong.util.StrUtilX;
import com.tiangong.constant.FieldConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/common/hotel")
@Slf4j
public class HotelServer extends BaseController {

    @Autowired
    private HotelService hotelService;

    @Autowired
    private BaseinfoAreadataService baseinfoAreadataService;

    /**
     * 酒店基本信息详情, 按需输出
     */
    @PostMapping("/queryHotelInfo")
    @AnonymousAccess
    public Response<HotelInfoCollectionDTO> queryHotelInfo(@RequestBody HotelInfoCollectionReq req) {
        return Response.success(hotelService.queryHotelInfo(req));
    }

    /**
     * 查询酒店列表, 按需输出
     */
    @PostMapping("/queryHotelInfoList")
    @AnonymousAccess
    public Response<List<HotelInfoCollectionDTO>> queryHotelInfoList(@RequestBody HotelInfoCollectionReq req) {
        return Response.success(hotelService.queryHotelInfoList(req));
    }

    /**
     * 酒店基本信息详情, 按需输出
     */
    @PostMapping("/queryHotelInfoStrList")
    @AnonymousAccess
    public Response<String> queryHotelInfoStrList(@RequestBody HotelInfoReq req) {
        return Response.success(hotelService.queryHotelInfoStrList(req));
    }

    /**
     * 模糊查询房型
     */
    @PostMapping("/queryRoomLikeName")
    @AnonymousAccess
    public Response<List<FuzzyRoomDTO>> queryRoomLikeName(@RequestBody FuzzyQueryDTO req) {
        if (StrUtilX.isEmpty(req.getLanguage())) {
            req.setLanguage(getLanguage());
        }
        return Response.success(hotelService.queryRoomLikeName(req));
    }

    /**
     * 获取国际区号
     */
    @PostMapping("getRegion")
    @AnonymousAccess
    public Response<List<BaseinfoRegionDTO>> getRegion() {
        return Response.success(hotelService.getRegion());
    }

    /**
     * 根据关键字获取城市和酒店
     */
    @PostMapping("queryCityAndHotelByKeyword")
    @AnonymousAccess
    public Response<List<HotCityResp>> queryCityAndHotelByKeyword(@RequestBody SearchCityAndHotelReq req) {
        return Response.success(hotelService.queryCityAndHotelByKeyword(req));
    }

    /**
     * 根据城市编码获取行政区/商业区
     */
    @PostMapping("/queryBusinessAndDistrictList")
    @AnonymousAccess
    public Response<DistrictOrBusinessResp> queryBusinessAndDistrictList(@Validated @RequestBody DistrictOrBusinessReq req) {
        return Response.success(hotelService.queryBusinessAndDistrictList(req));
    }

    /**
     * 国家基本信息列表（分页）
     */
    @PostMapping("/queryCountryList")
    @AnonymousAccess
    public Response<List<BaseinfoAreadataDTO>> queryCountryList(@RequestBody CountryReq req) {
        return Response.success(hotelService.queryCountryList(req));
    }

    /**
     * 查询所有城市列表--不区分中英文
     */
    @PostMapping("/queryAllCityList")
    @AnonymousAccess
    public Response<List<CityResp>> queryAllCityList(@RequestBody com.tiangong.hotel.domain.req.CityReq req) {
        return Response.success(hotelService.queryAllCityList(req));
    }

    /**
     * 查询热门城市列表
     */
    @PostMapping("queryHotCityList")
    @AnonymousAccess
    public Response<List<HotCityResp>> queryHotCityList(@RequestBody CityReq req) {
        List<HotCityResp> list = baseinfoAreadataService.queryHotCityList(req.getLanguage());
        return Response.success(list);
    }

    /**
     * 获取热门城市列表
     */
    @PostMapping("queryHotCityListManger")
    @AnonymousAccess
    public Response<List<BaseinfoHotCityEntity>> queryHotCityListManger() {
        List<BaseinfoHotCityEntity> list = baseinfoAreadataService.queryHotCityListManger();
        return Response.success(list);
    }

    /**
     * 修改或新增热门城市
     */
    @PostMapping("addOrUpdateHotCity")
    @AnonymousAccess
    public Response<Object> addOrUpdateHotCity(@RequestBody AddOrUpdateHotCityReq req) {
        baseinfoAreadataService.addOrUpdateHotCity(req);
        return Response.success();
    }

    /**
     * 更新时区，转发给基础信息云化
     */
    @PostMapping("updateTimeZone")
    @AnonymousAccess
    public Response<Object> updateTimeZone(@RequestBody UpdateTimeZoneDTO updateTimeZoneDTO) {
        hotelService.updateTimeZone(updateTimeZoneDTO);
        return Response.success();
    }

    /**
     * 根据城市编码查询城市时区
     */
    @PostMapping("queryCityTimeZone")
    @AnonymousAccess
    public Response<QueryCityTimeZoneResp> queryCityTimeZone(@RequestBody QueryCityTimeZoneReq req) {
        return Response.success(hotelService.queryCityTimeZone(req));
    }

    /**
     * 查询城市，省份，国家结构数据
     *
     * @param req 城市数据
     */
    @PostMapping("selectCityList")
    public Response<Object> selectCityList(@Validated @RequestBody SelectCityListReq req) {
        if (StrUtilX.isEmpty(req.getLanguage())) {
            req.setLanguage(getLanguage());
        }
        return Response.success(hotelService.selectCityList(req));
    }

    /**
     * 查询集团品牌列表
     *
     * @param req 城市数据
     */
    @PostMapping("queryGroupList")
    public Response<List<BaseinfoGroupPO>> queryGroupList(@Validated @RequestBody QueryGroupReq req) {
        return Response.success(hotelService.queryGroupList(req));
    }

    /**
     * 点击城市
     *
     * @param hotCityClickDTO 城市数据
     */
    @PostMapping("addHotCityClick")
    public Response<Object> addHotCityClick(@RequestBody HotCityClickDTO hotCityClickDTO) {
        hotelService.addHotCityClick(hotCityClickDTO);
        return Response.success();
    }

    /**
     * 初始化酒店热度数据任务
     */
    @PostMapping("initHotelHeatDateTask")
    @AnonymousAccess
    public Response<Object> initHotelHeatDateTask(@RequestBody Map<String, String> paramMap) {
        hotelService.initHotelHeatDateTask(paramMap.get(FieldConstants.HOTEL_IDS));
        return Response.success();
    }

    /**
     * 计算酒店热度评分和集团分数任务
     */
    @PostMapping("calculateHotelHeatGroupScoreTask")
    @AnonymousAccess
    public Response<Object> calculateHotelHeatGroupScoreTask(@RequestBody Map<String, String> paramMap) {
        hotelService.calculateHotelHeatGroupScoreTask(paramMap.get(FieldConstants.HOTEL_IDS));
        return Response.success();
    }

    /**
     * 目的地搜索接口
     */
    @PostMapping("getAddress")
    @AnonymousAccess
    public Response<List<AddressRespDTO>> getAddress(@RequestBody Map<String, String> paramMap) {
        //0605版本，优化目的地接口需求 新增字段【热度值】、【目的地ID】
        //直接传递map对象
        //by. 雷燕军
        return hotelService.getAddress(paramMap);
    }

    /**
     * 更加目的地id获取目的地
     */
    @PostMapping("getAddressById")
    @AnonymousAccess
    public Response<AddressDocVo> getAddressById(@RequestBody Map<String, String> paramMap) {
        return Response.success(hotelService.getAddressById(paramMap.get(FieldConstants.DESTINATION_ID)));
    }

    /**
     * 获取酒店过滤器数据
     */
    @PostMapping("getHotelSearch")
    @AnonymousAccess
    public Response<HotelSearchResp> getHotelSearch(@RequestBody HotelSearchReq hotelSearchReq) {
        return hotelService.getHotelSearch(hotelSearchReq);
    }

    /**
     * 更新mongodb公共表里面的数据
     */
    @AnonymousAccess
    @PostMapping("updateHotelScoreToMongodbBatch")
    public void updateHotelScoreToMongodbBatch(@RequestBody UpdateHotelScoreReq req) {
        hotelService.updateHotelScoreToMongodbBatch(req);
    }

    /**
     * 获取酒店列表
     */
    @PostMapping("findHotelList")
    @AnonymousAccess
    public Response<PaginationSupportDTO<HotelListResp>> findHotelList(@RequestBody HotelPageReq req) {
        return Response.success(hotelService.findHotelList(req));
    }

    /**
     * 更新mongodb公共表里面的数据
     */
    @AnonymousAccess
    @PostMapping("updateHotelMongodbByHotelId")
    public void updateHotelMongodbByHotelId(@RequestBody HotelPublicDTO hotelPublicDTO) {
        hotelService.updateHotelMongodbByHotelId(hotelPublicDTO);
    }

    /**
     * 查询目的地酒店
     */
    @PostMapping("/searchDestinationHotel")
    public Response<List<EsHotelDto>> searchDestinationHotel(@RequestBody DestinationReq req) {
        if (StrUtilX.isEmpty(req.getLanguage())) {
            req.setLanguage(getLanguage());
        }
        return Response.success(hotelService.searchDestinationHotel(req));
    }

    /**
     * 查询目的地城市
     */
    @PostMapping("/searchDestinationCity")
    public Response<List<EsCityDto>> searchDestinationCity(@RequestBody DestinationReq req) {
        if (StrUtilX.isEmpty(req.getLanguage())) {
            req.setLanguage(getLanguage());
        }
        return Response.success(hotelService.searchDestinationCity(req));
    }

    /**
     * 查询国，省，市
     */
    @PostMapping("/searchCountryProvinceCity")
    public Response<List<CountryProvinceCityDto>> searchCountryProvinceCity(@RequestBody CountryProvinceCityReq req ){
        return Response.success(hotelService.searchCountryProvinceCity(req));
    }

    /**
     * 查询目的地酒店,不校验权限
     */
    @PostMapping("/searchDestinationHotel2")
    @AnonymousAccess
    public Response<List<EsHotelDto>> searchDestinationHotel2(@RequestBody DestinationReq req) {
        return Response.success(hotelService.searchDestinationHotel(req));
    }
}
