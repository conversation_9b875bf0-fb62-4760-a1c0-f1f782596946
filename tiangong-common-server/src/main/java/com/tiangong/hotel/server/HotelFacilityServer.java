package com.tiangong.hotel.server;

import com.tiangong.common.Response;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.FacilityDto;
import com.tiangong.hotel.domain.BaseinfoFacility;
import com.tiangong.hotel.domain.req.FacilityAllReq;
import com.tiangong.hotel.domain.req.FacilityEditReq;
import com.tiangong.hotel.domain.req.FacilityListReq;
import com.tiangong.hotel.domain.req.FacilityScoreEditReq;
import com.tiangong.hotel.domain.resp.FacilityInfoAndConfigResp;
import com.tiangong.hotel.domain.resp.FacilityNameResp;
import com.tiangong.hotel.domain.resp.FacilityResp;
import com.tiangong.hotel.service.BaseinfoFacilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/common/facility")
public class HotelFacilityServer {

    @Autowired
    private BaseinfoFacilityService baseinfoFacilityService;

    /**
     * 新增或修改酒店设施
     */
    @PostMapping(value = "/addOrUpdateFacility", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('common')")
    public Response<Object> addOrUpdateFacility(@RequestBody FacilityDto baseinfoFacility) {
        BaseinfoFacility facility = CommonDtoConvert.INSTANCE.facilityConvert(baseinfoFacility);
        baseinfoFacilityService.addOrUpdateFacility(facility);
        return Response.success();
    }


    /**
     * 查询酒店设施
     */
    @PostMapping(value = "/queryFacility", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('common')")
    public Response<FacilityDto> queryFacility() {
        BaseinfoFacility facility = baseinfoFacilityService.queryFacility();
        FacilityDto dto = CommonDtoConvert.INSTANCE.facilityDtoConvert(facility);
        return Response.success(dto);
    }

    /**
     * 查询酒店设施
     */
    @PostMapping(value = "/queryFacilityManage", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('common')")
    public Response<FacilityInfoAndConfigResp> queryFacilityManage() {
        FacilityInfoAndConfigResp facility = baseinfoFacilityService.queryFacilityManage();
        return Response.success(facility);
    }


    /**
     * 查询设施分类大类列表
     */
    @PostMapping(value = "/queryFacilityList", produces = {"application/json;charset=UTF-8"})
    public Response<FacilityResp> queryFacilityList(@Validated @RequestBody FacilityListReq req) {
        return Response.success(baseinfoFacilityService.queryFacilityList(req));
    }

    /**
     * 修改设施分类
     */
    @PostMapping(value = "/facilityEdit", produces = {"application/json;charset=UTF-8"})
    public Response<Object> facilityEdit(@Validated @RequestBody FacilityEditReq req) {
        baseinfoFacilityService.facilityEdit(req);
        return Response.success();
    }

    /**
     * 查询设施分页
     */
    @PostMapping(value = "/facilityPage", produces = {"application/json;charset=UTF-8"})
    public Response<PaginationSupportDTO<FacilityNameResp>> facilityPage(@Validated @RequestBody FacilityAllReq req) {
        return Response.success(baseinfoFacilityService.facilityPage(req));
    }

    /**
     * 修改设施分类
     */
    @PostMapping(value = "/facilityScoreEdit", produces = {"application/json;charset=UTF-8"})
    public Response<Object> facilityScoreEdit(@Validated @RequestBody FacilityScoreEditReq req) {
        baseinfoFacilityService.facilityScoreEdit(req);
        return Response.success();
    }
}
