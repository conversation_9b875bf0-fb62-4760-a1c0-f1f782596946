package com.tiangong.hotel.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.hotel.req.RegionMainlyPopularizePriceReq;
import com.tiangong.hotel.service.RegionMainlyPopularizePriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 区域主推价格
 */
@RestController
@RequestMapping("/common/regionMainlyPopularizePrice")
public class RegionMainlyPopularizePriceServer extends BaseController {

    @Autowired
    private RegionMainlyPopularizePriceService regionMainlyPopularizePriceService;

    /**
     * 新增区域主推价格
     */
    @PostMapping(value = "/regionMainlyPopularizePriceAdd", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<Object> regionMainlyPopularizePriceAdd(@RequestBody RegionMainlyPopularizePriceReq req) {
        regionMainlyPopularizePriceService.regionMainlyPopularizePriceAdd(req);
        return Response.success();
    }

    /**
     * 编辑区域主推价格
     */
    @PostMapping(value = "/regionMainlyPopularizePriceEdit", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<Object> regionMainlyPopularizePriceEdit(@RequestBody RegionMainlyPopularizePriceReq req) {
        regionMainlyPopularizePriceService.regionMainlyPopularizePriceEdit(req);
        return Response.success();
    }
}
