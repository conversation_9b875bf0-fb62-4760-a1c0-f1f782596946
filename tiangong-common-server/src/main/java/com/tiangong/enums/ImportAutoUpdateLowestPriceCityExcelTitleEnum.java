package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportAutoUpdateLowestPriceCityExcelTitleEnum {
    /**
     * 城市id
     */
    DESTINATION_ID(0,"城市id"),
    HOTEL_COUNT(1,"城市可跑的酒店数量"),
    ;

    public final int key;
    public final String value;

    public static int getKeyByValue(String value) {
        int key = 0;
        for(ImportAutoUpdateLowestPriceCityExcelTitleEnum titleEnum : ImportAutoUpdateLowestPriceCityExcelTitleEnum.values()) {
            if(titleEnum.value.equals(value.trim())) {
                key = titleEnum.key;
                break;
            }
        }
        return key;
    }

}
