package com.tiangong.operatelog.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023/3/28 19:54
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@TableName("s_log_operate_log")
public class OperateLogCountAggregationEntity {

    /**
     * 新增的一列数据 用于存储eventCount字段
     * 注解非常关键
     */
    @TableField(value = "count(*)", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer count;

    /**
     * 操作类型
     * 0:查询
     * 1:增加
     * 2:删除
     * 3:修改
     * 4:导出
     * 5:加锁
     * 6:确认
     * 7:发短信
     * 8:登录
     * 9:处理
     * 10:取消
     * 11:发单
     * 12:重置
     * 13:推送
     * 14:刷新
     * 15:解锁
     * 16:退出登录
     * 17:移动
     * 18:越权操作
     * 19:ip异常操作
     * 20:连续登录失败
     * 21:获取
     * 22:请求错误
     * 23:初始化
     * 24:通知
     * 25:校验
     * 26:导入
     */
    public Integer operationType;

    /**
     * 操作日期
     */
    private LocalDateTime createdDt;

    /**
     * 操作账号
     */
    private String userAccount;

}
