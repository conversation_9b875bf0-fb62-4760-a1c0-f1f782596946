package com.tiangong.operatelog.service.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.cloud.commonbean.utils.WebFrameworkUtilX;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.*;
import com.tiangong.keys.RedisKey;
import com.tiangong.operatelog.domain.*;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.operatelog.mapper.OperateLogMapper;
import com.tiangong.operatelog.service.OperateLogService;
import com.tiangong.order.enums.RemarkTypeEnum;
import com.tiangong.order.enums.SendingStatusEnum;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.domain.UserRolePO;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.user.mapper.UserRoleMapper;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
@Slf4j
@Service
public class OperateLogServiceImpl implements OperateLogService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    OperateLogMapper operateLogMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;
    /**
     * 日志分批数量
     */
    private final static int LOG_BATCH_SIZE = 1000;

    private final static String SENSITIVE_PASSWORD = "密码";
    private final static String SENSITIVE_PHONE = "手机";
    private final static String SENSITIVE_PHONE_NUMBER = "手机号";
    private final static String SENSITIVE_ID_CARD = "身份证";
    /**
     * 不存在
     */
    private final static int NOT_EXIST=-1;
    @Override
    public void operateLogAdd(OperateLogReqDTO req) {
        OperateLogEntity entity = CommonDtoConvert.INSTANCE.OperateLogConvert(req);

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        boolean flage = true;
        if (loginUser != null) {
            if (StrUtilX.isEmpty(entity.getCreatedBy())) {
                entity.setCreatedBy(loginUser.getFullUserName());
            }

            if (StrUtilX.isEmpty(entity.getUserAccount())) {
                entity.setUserAccount(loginUser.getUserAccount());
            }

            Example example = new Example(UserRolePO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("userId", loginUser.getUserId());
            List<UserRolePO> userRolePOS = userRoleMapper.selectByExample(example);
            // 用户角色集合
            Set<Integer> collect = userRolePOS.stream().map(UserRolePO::getRoleId).collect(Collectors.toSet());
            //拿到审计日志设置
            String auditSetting = RedisTemplateX.get(RedisKey.AUDIT_SETTING);
            SaveAuditSettingReq saveAuditSettingReq = null;
            if (StrUtilX.isNotEmpty(auditSetting)) {
                saveAuditSettingReq = JSON.parseObject(auditSetting, SaveAuditSettingReq.class);
            }
            Date nowTime = new Date();
            if (saveAuditSettingReq != null && saveAuditSettingReq.getIgnoreStartTime() != null && saveAuditSettingReq.getIgnoreEndTime() != null
                    && nowTime.after(saveAuditSettingReq.getIgnoreStartTime()) && nowTime.before(DateUtilX.addDate(saveAuditSettingReq.getIgnoreEndTime(), 1))) {
                Set<Integer> operateTypeSet = new HashSet<>();
                if (CollUtilX.isNotEmpty(saveAuditSettingReq.getOperateTypeList())) {
                    for (OperateTypeDTO operateTypeDTO : saveAuditSettingReq.getOperateTypeList()) {
                        if (operateTypeDTO.getIsChoose() == 1) {
                            operateTypeSet.add(operateTypeDTO.getOperateType());
                        }
                    }
                }

                boolean operateRoleSetIsNull = true;
                // 审计日志所设置
                Set<Integer> operateRoleSet = new HashSet<>();
                List<OperateRoleDTO> operateRoleList = saveAuditSettingReq.getOperateRoleList();
                if (CollUtilX.isNotEmpty(operateRoleList)) {
                    for (OperateRoleDTO operateRoleDTO : operateRoleList) {
                        if (operateRoleDTO.getIsChoose() == 1) {
                            operateRoleSet.add(operateRoleDTO.getOperateRole());
                            operateRoleSetIsNull = false;
                        }
                    }
                }

                operateRoleSet.retainAll(collect);
                // 操作角色和操作类型都有设置
                if (!operateRoleSetIsNull && CollUtilX.isNotEmpty(operateTypeSet)) {
                    if (CollUtilX.isNotEmpty(operateRoleSet) && operateTypeSet.contains(entity.getOperationType())) {
                        flage = false;
                    }
                } else if (!operateRoleSetIsNull && CollUtilX.isEmpty(operateTypeSet)) {
                    // 操作角色不为空
                    if (CollUtilX.isNotEmpty(operateRoleSet)) {
                        flage = false;
                    }
                } else if (operateRoleSetIsNull && CollUtilX.isNotEmpty(operateTypeSet)) {
                    // 操作类型不为空
                    if (operateTypeSet.contains(entity.getOperationType())) {
                        flage = false;
                    }
                }
            }

        }

        if (flage && needLog(entity.getRequestUrl())) {

            //日志格式化
            Map<String, String> columnMap = this.initColumnMap();
            String param = entity.getRequestParam();
            if (StrUtilX.isNotEmpty(param)) {
                param = param.replaceAll("\"", "").replaceAll(":", "为").replaceAll("}", ",}");
                StringBuilder sb = new StringBuilder(param);
                Set<String> existEnumTypeSet = this.existEnumTypeMap().keySet();
                for (String column : columnMap.keySet()) {
                    int count = 0;
                    while (sb.indexOf(column) != -1) {
                        int startIndex = sb.indexOf("为", sb.indexOf(column)) + 1;
                        int endIndex = sb.indexOf(",", sb.indexOf("为", sb.indexOf(column)));
                        if (existEnumTypeSet.contains(column)) {
                            String tmp = sb.substring(startIndex, endIndex);
                            String strTmp = existEnumTypeMap().get(column).get(tmp);
                            if (StrUtilX.isNotEmpty(strTmp)) {
                                sb.replace(startIndex, endIndex, strTmp);
                            }
                        }
                        sb.replace(sb.indexOf(column), sb.indexOf("为", sb.indexOf(column)), columnMap.get(column));
                        count++;
                        log.info("循环次数：{},column:{}", count, column);
                        // 避免出现死循环
                        if (count == 100) {
                            break;
                        }
                    }
                }
                //校验密码不记录明文
//                if (param.contains("员工密码为")) {
//                    param = "校验密码";
//                }
                requestParamStringDesensitization(sb);
                param = sb.toString().replaceAll(",}", "}").replaceAll("为", ":").replaceAll("null", "空");
            }

            if (StrUtilX.isNotEmpty(param) && param.length() > LOG_BATCH_SIZE) {
                entity.setRequestParam(param.substring(0,LOG_BATCH_SIZE));
            } else {
                entity.setRequestParam(param);
            }

            operateLogMapper.insert(entity);

            if (OperationTypeEnum.UNAUTHORIZED.getOperationType().equals(entity.getOperationType())) {
                UserPO existUser = new UserPO();
                existUser.setUserAccount(req.getCreatedBy());
                existUser = userMapper.selectOne(existUser);
                //越权访问需发送短信警告
                Example example = new Example(UserPO.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("orgCode", existUser.getOrgCode());
                criteria.andEqualTo("isSuperAdmin", 1);
                List<UserPO> userPOS = userMapper.selectByExample(example);
                //msgService.sendMsg(userPOS.get(0).getUserTel(), "越权访问：" + req.getCreatedBy());
            }
        }
    }

    private void requestParamStringDesensitization(StringBuilder sb) {
        if (sb.indexOf(SENSITIVE_PASSWORD) != NOT_EXIST) {
            requestParamStringFindAndReplace(SENSITIVE_PASSWORD, sb);
        }
        if (sb.indexOf(SENSITIVE_PHONE) != NOT_EXIST) {
            requestParamStringFindAndReplace(SENSITIVE_PHONE, sb);
        }
        if (sb.indexOf(SENSITIVE_ID_CARD) != NOT_EXIST) {
            requestParamStringFindAndReplace(SENSITIVE_ID_CARD, sb);
        }
    }

    private void requestParamStringFindAndReplace(String target, StringBuilder original) {
        int startIndex = original.indexOf("为", original.indexOf(target)) + 1;
        int endIndex = original.indexOf(",", original.indexOf("为", original.indexOf(target)));
        if (startIndex > 0) {
            if (endIndex == -1) {
                original.replace(startIndex, original.length() - 1, "******");
            } else {
                original.replace(startIndex, endIndex, "******");
            }
        }
    }

    /**
     * 排除加密key相关日志
     * @param requestUrl
     * @return
     */
    private boolean needLog(String requestUrl) {
        boolean needLog = true;
        if (StrUtilX.isNotEmpty(requestUrl)
                && requestUrl.contains("stateSecret")) {
            needLog = false;
        }
        return needLog;
    }

    @Override
    public Object operateLogDetail(Long id) {
        OperateLogEntity entity = operateLogMapper.selectById(id);
        OperateLogResp resp = CommonDtoConvert.INSTANCE.convertOperateLogResp(entity);
        return resp;
    }

    @Override
    public Object operateLogPage(OperateLogReq req) {
        Page<OperateLogEntity> page = new Page<OperateLogEntity>(req.getCurrentPage(), req.getPageSize());

        LambdaQueryWrapper<OperateLogEntity> wrapper = new LambdaQueryWrapper<>();

        IPage<OperateLogEntity> ipage = operateLogMapper.selectPage(page, wrapper);
        List<OperateLogResp> collect = ipage.getRecords().stream().map(CommonDtoConvert.INSTANCE::convertOperateLogResp).collect(Collectors.toList());
        return PageVo.result(ipage, collect);
    }


    private Map<String, Map<String, String>> existEnumTypeMap() {
        Map<String, Map<String, String>> existEnumTypeMap = new HashMap<>();
        existEnumTypeMap.put("isChoose", new HashMap<String, String>() {{
            put("0", "没选择");
            put("1", "有选择");
        }});

        existEnumTypeMap.put("operateRole", new HashMap<String, String>() {{
            put("1", "总管理员");
            put("2", "管理员");
            put("3", "采购经理");
            put("4", "产品员");
            put("5", "销售经理");
            put("6", "运营员");
            put("7", "订单处理员");
            put("8", "业务对账员");
            put("9", "出纳");
            put("10", "系统审计员");
            put("11", "审计员管理员");
            put("12", "系统配置员");
        }});

        existEnumTypeMap.put("operateType", new HashMap<String, String>() {{
            for (OperationTypeEnum value : OperationTypeEnum.values()) {
                put(value.getOperationType().toString(), value.getOperationName());
            }
        }});

        existEnumTypeMap.put("accountStatus", new HashMap<String, String>() {{
            put("0", "激活");
            put("1", "休眠");
            put("2", "注销");
            put("3", "锁定");
        }});

        existEnumTypeMap.put("employeeAccountType", new HashMap<String, String>() {{
            put("0", "长期");
            put("1", "短期");
        }});

        existEnumTypeMap.put("employeeStatus", new HashMap<String, String>() {{
            put("0", "离线");
            put("1", "在线");
        }});

        existEnumTypeMap.put("operateResult", new HashMap<String, String>() {{
            put("0", "失败");
            put("1", "成功");
        }});

        existEnumTypeMap.put("logType", new HashMap<String, String>() {{
            put("0", "系统级事件");
            put("1", "业务级事件");
        }});

        existEnumTypeMap.put("eventType", new HashMap<String, String>() {{
            put("0", "系统级事件");
            put("1", "业务级事件");
        }});

        existEnumTypeMap.put("eventResult", new HashMap<String, String>() {{
            put("0", "失败");
            put("1", "成功");
        }});

        existEnumTypeMap.put("isSuperAdmin", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("targetType", new HashMap<String, String>() {{
            put("0", "用户");
            put("1", "订单");
        }});

//        existEnumTypeMap.put("accountType",new HashMap<String, String>(){{
//            put("","");
//        }});

        existEnumTypeMap.put("active", new HashMap<String, String>() {{
            put("0", "无效");
            put("1", "有效");
        }});

        existEnumTypeMap.put("adjustmentType", new HashMap<String, String>() {{
            for (SaleAdjustmentTypeEnum value : SaleAdjustmentTypeEnum.values()) {
                put(String.valueOf(value.no), value.desc);
            }
        }});

        existEnumTypeMap.put("agentType", new HashMap<String, String>() {{
            put("0", "企业用户");
        }});

        existEnumTypeMap.put("applicationScope", new HashMap<String, String>() {{
            put("0", "共用");
            put("1", "国内");
            put("2", "海外");
        }});

        existEnumTypeMap.put("approvalStatus", new HashMap<String, String>() {{
            put("0", "待审核");
            put("1", "已通过");
            put("2", "已驳回");
        }});

        existEnumTypeMap.put("areaType", new HashMap<String, String>() {{
            for (AreaTypeEnum value : AreaTypeEnum.values()) {
                put(value.getNo().toString(), value.getDesc());
            }
        }});

        existEnumTypeMap.put("automaticStatus", new HashMap<String, String>() {{
            put("1", "开启");
            put("0", "关闭");
        }});

        existEnumTypeMap.put("available", new HashMap<String, String>() {{
            put("1", "是");
            put("0", "否");
        }});

        existEnumTypeMap.put("availableStatus", new HashMap<String, String>() {{
            put("1", "是");
            put("0", "否");
        }});

        existEnumTypeMap.put("availableType", new HashMap<String, String>() {{
            for (OrgAvailableTypeEnum value : OrgAvailableTypeEnum.values()) {
                put(String.valueOf(value.no), value.desc);
            }
        }});

        existEnumTypeMap.put("bathRoomType", new HashMap<String, String>() {{
            put("0", "私人");
            put("1", "公共");
        }});

        existEnumTypeMap.put("bedPosition", new HashMap<String, String>() {{
            put("0", "卧室");
            put("1", "客厅");
            put("2", "卫生间");
        }});

        existEnumTypeMap.put("bedRelation", new HashMap<String, String>() {{
            put("1", "且");
            put("2", "或");
        }});

        existEnumTypeMap.put("bedRoomSetting", new HashMap<String, String>() {{
            put("0", "单个房间设置");
            put("1", "多室多厅设置");
        }});

        existEnumTypeMap.put("bedTypes", new HashMap<String, String>() {{
            for (BedTypesEnum value : BedTypesEnum.values()) {
                put(value.key, value.value);
            }
        }});

        existEnumTypeMap.put("breakfastForm", new HashMap<String, String>() {{
            put("0", "单点");
            put("1", "固定套餐");
            put("2", "盒装视频/包装食品");
            put("3", "自助餐");
        }});

        existEnumTypeMap.put("breakfastOpenType", new HashMap<String, String>() {{
            put("0", "每日开放");
            put("1", "指定日期开放");
        }});

        existEnumTypeMap.put("breakfastState", new HashMap<String, String>() {{
            put("0", "默认状态");
            put("1", "自定义状态");
        }});

        existEnumTypeMap.put("breakfastType", new HashMap<String, String>() {{
            put("1", "中早");
            put("2", "西早");
            put("3", "自助早");
        }});

        existEnumTypeMap.put("broadband", new HashMap<String, String>() {{
            put("0", "全部房间免费");
            put("1", "部分房间免费");
            put("2", "全部房间收费");
            put("3", "部分房间收费");
            put("4", "无");
        }});

        existEnumTypeMap.put("cancellationType", new HashMap<String, String>() {{
            put("0", "一经预订不能取消");
            put("1", "可以取消");
        }});

        existEnumTypeMap.put("chargeForm", new HashMap<String, String>() {{
            put("1", "为金额值");
            put("2", "为百分比值");
        }});

        existEnumTypeMap.put("chargeManner", new HashMap<String, String>() {{
            put("0", "免费");
            put("1", "明确价格");
            put("2", "按房费比例");
        }});

        existEnumTypeMap.put("chargeType", new HashMap<String, String>() {{
            put("1", "每天");
            put("2", "每小时");
            put("3", "每周");
            put("4", "每次入住");
            put("5", "每次进出");
        }});

        existEnumTypeMap.put("checkInState", new HashMap<String, String>() {{
            for (CheckInstateEnum value : CheckInstateEnum.values()) {
                put(value.name, value.desc);
            }
        }});

        existEnumTypeMap.put("childrenBreakType", new HashMap<String, String>() {{
            put("1", "年龄");
            put("2", "身高（单位：厘米）");
        }});

        existEnumTypeMap.put("comparisonType", new HashMap<String, String>() {{
            for (ComparisonTypeEnum value : ComparisonTypeEnum.values()) {
                put(value.key.toString(), value.desc);
            }
        }});

        existEnumTypeMap.put("confirmationStatus", new HashMap<String, String>() {{
            put("0", "未确认");
            put("1", "确认");
            put("2", "已取消");
        }});

        existEnumTypeMap.put("confirmationType", new HashMap<String, String>() {{
            put("0", "在线");
        }});

        existEnumTypeMap.put("contactRole", new HashMap<String, String>() {{
            put("0", "没有分配角色的联系人");
            put("1", "业务联系人");
            put("2", "财务联系人");
        }});

        existEnumTypeMap.put("dateQueryType", new HashMap<String, String>() {{
            put("0", "下单日期");
            put("1", "入住日期");
            put("2", "离店日期");
        }});

        existEnumTypeMap.put("deficitStatus", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("deleted", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("facilityStatus", new HashMap<String, String>() {{
            put("0", "无");
            put("1", "全部有");
            put("2", "部分有");
        }});

        existEnumTypeMap.put("facilityTypeStatus", new HashMap<String, String>() {{
            put("1", "酒店设施");
            put("2", "房型设施");
        }});

        existEnumTypeMap.put("filterable", new HashMap<String, String>() {{
            put("T", "支持");
            put("F", "不支持");
        }});

        existEnumTypeMap.put("financeType", new HashMap<String, String>() {{
            put("0", "收款");
            put("1", "付款");
        }});

        existEnumTypeMap.put("guaranteeDateType", new HashMap<String, String>() {{
            put("1", "首晚");
            put("2", "全额");
        }});

        existEnumTypeMap.put("guaranteeFeeType", new HashMap<String, String>() {{
            put("0", "全额");
            put("1", "首晚");
        }});

        existEnumTypeMap.put("guaranteeType", new HashMap<String, String>() {{
            put("1", "入住担保");
            put("2", "在住担保");
        }});

        existEnumTypeMap.put("guestQtyType", new HashMap<String, String>() {{
            put("0", "默认");
            put("1", "自定义");
        }});

        existEnumTypeMap.put("handledResult", new HashMap<String, String>() {{
            put("1", "同意处理");
            put("0", "拒绝处理");
        }});

        existEnumTypeMap.put("hasProducts", new HashMap<String, String>() {{
            put("0", "没有产品");
            put("1", "有产品");
        }});

        existEnumTypeMap.put("hasWindow", new HashMap<String, String>() {{
            put("0", "无窗");
            put("1", "有窗");
            put("2", "部分有窗");
        }});

        existEnumTypeMap.put("hasWirelessBroadband", new HashMap<String, String>() {{
            put("0", "全部房间免费");
            put("1", "部分房间免费");
            put("2", "全部房间收费");
            put("3", "部分房间收费");
            put("4", "无");
        }});

        existEnumTypeMap.put("hotelRank", new HashMap<String, String>() {{
            for (HotelRankEnum value : HotelRankEnum.values()) {
                put(String.valueOf(value.no), value.desc);
            }
        }});

        existEnumTypeMap.put("hotelType", new HashMap<String, String>() {{
            put("0", "国内酒店");
            put("1", "国际酒店");
        }});

        existEnumTypeMap.put("idCardType", new HashMap<String, String>() {{
            put("1", "身份证");
            put("2", "驾驶证");
            put("3", "护照");
            put("4", "回乡证");
            put("5", "台胞证");
            put("6", "港澳台居民居住证");
        }});

        existEnumTypeMap.put("idleStatus", new HashMap<String, String>() {{
            put("1", "闲置中");
            put("0", "仓库中");
        }});

        existEnumTypeMap.put("isAbnormal", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isActive", new HashMap<String, String>() {{
            put("0", "无效");
            put("1", "有效");
        }});

        existEnumTypeMap.put("isAllowsPet", new HashMap<String, String>() {{
            put("0", "不允许");
            put("1", "允许");
        }});

        existEnumTypeMap.put("isBreakfast", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isChargePark", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isChildrenBreak", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isDelete", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isHot", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isLimitParking", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isMyOrder", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isNonSmokingRoom", new HashMap<String, String>() {{
            put("1", "是");
            put("2", "否");
            put("3", "部分");
        }});

        existEnumTypeMap.put("isOfferBed", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isOfferInfanette", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isPark", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isRefused", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isReserved", new HashMap<String, String>() {{
            put("0", "需要预订");
            put("1", "无需预订");
            put("2", "无法提前预订");
        }});

        existEnumTypeMap.put("isShownOnSupplyOrder", new HashMap<String, String>() {{
            put("0", "不展示");
            put("1", "展示");
        }});

        existEnumTypeMap.put("isShowWarn", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isSubstituted", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("isSuperAdmin", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("lockStatus", new HashMap<String, String>() {{
            put("0", "解锁");
            put("1", "加锁");
        }});

        existEnumTypeMap.put("manualOrder", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("mappingStatus", new HashMap<String, String>() {{
            put("0", "未绑定");
            put("1", "已绑定");
            put("2", "已解绑");
        }});

        existEnumTypeMap.put("markedStatus", new HashMap<String, String>() {{
            put("0", "未标");
            put("1", "已标");
        }});

        existEnumTypeMap.put("minor", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});

        existEnumTypeMap.put("notifyType", new HashMap<String, String>() {{
            for (NotifyTypeEnum value : NotifyTypeEnum.values()) {
                put(String.valueOf(value.no), value.desc);
            }
        }});

        existEnumTypeMap.put("offShelveStatus", new HashMap<String, String>() {{
            put("0", "停售");
            put("1", "可售");
        }});

        existEnumTypeMap.put("operationType", new HashMap<String, String>() {{
            for (OperationTypeEnum value : OperationTypeEnum.values()) {
                put(value.getOperationType().toString(), value.getOperationName());
            }
        }});

        existEnumTypeMap.put("orderSettlementType", new HashMap<String, String>() {{
            put("1", "月结");
            put("2", "半月结");
            put("3", "周结");
            put("4", "单结");
            put("5", "日结");
        }});

        existEnumTypeMap.put("orderStatus", new HashMap<String, String>() {{
            for (OrderStatusEnum value : OrderStatusEnum.values()) {
                put(String.valueOf(value.no), value.desc);
            }
        }});

        existEnumTypeMap.put("orgType", new HashMap<String, String>() {{
            put("1", "包房商");
            put("2", "个人");
        }});

        existEnumTypeMap.put("overDraftStatus", new HashMap<String, String>() {{
            for (OverDraftStatusEnum value : OverDraftStatusEnum.values()) {
                put(String.valueOf(value.no), value.desc);
            }
        }});

        existEnumTypeMap.put("overdueStatus", new HashMap<String, String>() {{
            put("0", "未逾期");
            put("1", "已逾期");
        }});

        existEnumTypeMap.put("paymentType", new HashMap<String, String>() {{
            put("1", "公司授信");
            put("2", "个人支付");
            put("3", "组合付款");
        }});

        existEnumTypeMap.put("purchaseType", new HashMap<String, String>() {{
            put("0", "自签协议房");
            put("1", "自签包房");
        }});

        existEnumTypeMap.put("quotaAdjustmentType", new HashMap<String, String>() {{
            put("0", "加");
            put("1", "减");
            put("2", "等于");
        }});

        existEnumTypeMap.put("remarkType", new HashMap<String, String>() {{
            for (RemarkTypeEnum value : RemarkTypeEnum.values()) {
                put(String.valueOf(value.key), value.value);
            }
        }});

        existEnumTypeMap.put("requestType", new HashMap<String, String>() {{
            for (RequestTypeEnum value : RequestTypeEnum.values()) {
                put(String.valueOf(value.key), value.desc);
            }
        }});

        existEnumTypeMap.put("result", new HashMap<String, String>() {{
            put("1", "成功");
            put("0", "失败");
        }});

        existEnumTypeMap.put("roomStatus", new HashMap<String, String>() {{
            put("1", "开房");
            put("0", "关房");
        }});

        existEnumTypeMap.put("selected", new HashMap<String, String>() {{
            put("0", "未选中");
            put("1", "选中");
        }});

        existEnumTypeMap.put("sendingStatus", new HashMap<String, String>() {{
            for (SendingStatusEnum value : SendingStatusEnum.values()) {
                put(String.valueOf(value.key), value.value);
            }
        }});

        existEnumTypeMap.put("settlementStatus", new HashMap<String, String>() {{
            put("0", "未结算");
            put("1", "已结算");
        }});

        existEnumTypeMap.put("settlementType", new HashMap<String, String>() {{
            put("0", "月结");
            put("1", "半月结");
            put("2", "周结");
            put("3", "单结");
            put("4", "日结");
        }});

        existEnumTypeMap.put("sourceType", new HashMap<String, String>() {{
            put("0", "平台");
            put("1", "分销");
            put("2", "供应");
        }});

        existEnumTypeMap.put("statementStatus", new HashMap<String, String>() {{
            put("0", "未对账");
            put("1", "对账中");
            put("2", "已确定");
            put("3", "已取消");
        }});

        //status
        existEnumTypeMap.put("status", new HashMap<String, String>() {{
            put("0", "未映射");
            put("1", "已解绑");
            put("2", "已绑定");
        }});

        existEnumTypeMap.put("suppliertimeType", new HashMap<String, String>() {{
            put("0", "所有时间");
            put("1", "自定义时间");
        }});

        //supplyType
        existEnumTypeMap.put("supplyType", new HashMap<String, String>() {{
            put("0", "运营商自建");
            put("1", "供应商EBK录入");
            put("2", "api对接");
        }});

        existEnumTypeMap.put("transportationType", new HashMap<String, String>() {{
            put("0", "驾车");
            put("1", "步行");
            put("2", "直线距离");
            put("3", "公交");
        }});

        existEnumTypeMap.put("userAccountType", new HashMap<String, String>() {{
            put("0", "长期");
            put("1", "短期");
        }});

        existEnumTypeMap.put("userAccountType", new HashMap<String, String>() {{
            put("0", "长期");
            put("1", "短期");
        }});

        existEnumTypeMap.put("windowType", new HashMap<String, String>() {{
            put("0", "无窗");
            put("1", "部分有窗");
            put("2", "有窗");
            put("3", "内窗");
            put("4", "天窗");
            put("5", "窗户位于走廊或过道");
            put("6", "有窗户但不能打开通风");
            put("7", "飘窗");
        }});

        existEnumTypeMap.put("workOrderStatus", new HashMap<String, String>() {{
            put("0", "未结算");
            put("1", "已结算");
        }});

        existEnumTypeMap.put("endType", new HashMap<String, String>() {{
            put("0", "供应商");
            put("1", "客户");
            put("2", "运营商");
        }});

        //是否协议产品
        existEnumTypeMap.put("isContractProduct", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});
        //是否在基础信息库
        existEnumTypeMap.put("isInBaseInfo", new HashMap<String, String>() {{
            put("0", "否");
            put("1", "是");
        }});
        //对账结果类型
        existEnumTypeMap.put("reconciliationResultType", new HashMap<String, String>() {{
            put("0", "一致");
            put("1", "金额不对");
            put("2", "供应商少单");
            put("3", "我方少单");
        }});

        existEnumTypeMap.put("billStatus", new HashMap<String, String>() {{
            put("0", "未在比对");
            put("1", "比对中");
        }});

        existEnumTypeMap.put("exportType", new HashMap<String, String>() {{
            put("0", "自动对账");
            put("1", "协议酒店映射明细");
            put("2", "审计日志");
            put("3", "销售明细表");
            put("4", "订单利润表");
            put("5", "协议酒店映射模板");
        }});

        existEnumTypeMap.put("exportStatus", new HashMap<String, String>() {{
            put("0", "导出中");
            put("1", "导出成功");
            put("2", "导出失败");
        }});

        existEnumTypeMap.put("deletedTimeRange", new HashMap<String, String>() {{
            put("0", "超过七天");
            put("1", "超过一个月");
        }});

        existEnumTypeMap.put("drainageSetting", new HashMap<String, String>() {{
            put("0", "不做资源引流");
            put("1", "过滤携程数据，引流给加力供应商");
        }});

        existEnumTypeMap.put("logSetting", new HashMap<String, String>() {{
            put("0", "关闭记录");
            put("1", "开启记录");
        }});

        existEnumTypeMap.put("additionalChargesType", new HashMap<String, String>() {{
            put("0", "其他");
        }});
        existEnumTypeMap.put("additionalChargesOrderType", new HashMap<String, String>() {{
            put("0", "订单");
            put("1", "供货单");
        }});
        existEnumTypeMap.put("isRepeat", new HashMap<String, String>() {{
            put("0", "不重发");
            put("1", "重发");
        }});

        return existEnumTypeMap;
    }


    /**
     * 初始化字段map
     * @return
     */
    private Map<String, String> initColumnMap() {
        Map<String, String> columnMap = new HashMap<>();
        columnMap.put("configureCapacity", "配置容量");
        columnMap.put("ignoreEndTime", "忽略结束时间");
        columnMap.put("ignoreStartTime", "忽略开始时间");
        columnMap.put("isChoose", "是否选择");
        columnMap.put("operateRole", "操作角色");
        columnMap.put("operateType", "操作类型");
        columnMap.put("operateTypeName", "操作类型");
        columnMap.put("operationCount", "操作数量");
        columnMap.put("operationName", "操作类型");
        columnMap.put("percentage", "占比");
        columnMap.put("eventCount", "事件次数");
        columnMap.put("visitStartTime", "企业员工访问开始时间");
        columnMap.put("visitEndTime", "企业员工访问结束时间");
        columnMap.put("employeeAccountPwd", "企业员工账号密码");
        columnMap.put("accountStatus", "企业员工账号状态");
        columnMap.put("employeeAccountType", "企业员工账号类型");
        columnMap.put("employeeIDNumber", "企业员工身份证号");
        columnMap.put("employeeStatus", "员工在线状态");
        columnMap.put("failLockTime", "失败锁定时间限制");
        columnMap.put("logSaveLimit", "审计记录存储上限");
        columnMap.put("currentEmployeePwd", "当前员工密码");
        columnMap.put("operateStartDate", "操作开始日期");
        columnMap.put("operationTime", "操作时间");
        columnMap.put("operationTypeName", "操作类型");
        columnMap.put("operateEndDate", "操作结束日期");
        columnMap.put("operateResult", "操作结果");
        columnMap.put("operateAccount", "操作账号");
        columnMap.put("logType", "日志类型");
        columnMap.put("maxOnlineCount", "最大同时在线员工数");
        columnMap.put("maxFailCount", "最大登录失败数");
        columnMap.put("userId", "用户Id");
        columnMap.put("visitIp", "访问Ip");
        columnMap.put("accountAvlStartTime", "账号有效期开始时间");
        columnMap.put("accountAvlEndTime", "账号有效期结束时间");
        columnMap.put("restPwd", "重置密码");
        columnMap.put("ipAddress", "ip地址 ");
        columnMap.put("eventDescription", "事件描述");
        columnMap.put("eventType", "事件类型");
        columnMap.put("eventResult", "事件结果");
        columnMap.put("phoneNumber", "手机号");
        columnMap.put("isSuperAdmin", "是否超级管理员");
        columnMap.put("updatePwdTime", "最新修改密码时间");
        columnMap.put("lastLoginTime", "最新登录时间");
        columnMap.put("userName", "用户名称");
        columnMap.put("userPwd", "用户密码");
        columnMap.put("userTel", "用户手机号");
        columnMap.put("target", "目标");
        columnMap.put("targetId", "目标Id");
        columnMap.put("targetContent", "目标内容");
        columnMap.put("targetType", "目标类型");
        columnMap.put("unlockTime", "账号解锁时间");
        columnMap.put("abnormalOrderQty", "问题单数");
        columnMap.put("account", "账号");
        columnMap.put("accountAvlEndTime", "账号有效期结束时间");
        columnMap.put("accountAvlStartTime", "账号有效期开始时间");
        columnMap.put("accountName", "开户名");
        columnMap.put("accountStatus", "企业员工账号状态");
        columnMap.put("accountType", "账号类型");
        columnMap.put("acronym", "首字母缩写");
        columnMap.put("active", "有效性");
        columnMap.put("adjustmentType", "加幅类型");
        columnMap.put("adjustValue", "调整配额数");
        columnMap.put("administrativeCode", "行政区编码");
        columnMap.put("administrativeName", "行政区名称");
        columnMap.put("adminName", "管理员姓名");
        columnMap.put("adminTel", "管理员手机号");
        columnMap.put("adultsBedExtra", "成人加床费用");
        columnMap.put("IdadvancePaymentId", "包房款");
        columnMap.put("advancePaymentName", "包房款名称");
        columnMap.put("agentCode", "客户编码");
        columnMap.put("agentCode", "分销商编码");
        columnMap.put("agentId", "客户Id");
        columnMap.put("agentList", "客户列表");
        columnMap.put("agentName", "客户名称");
        columnMap.put("agentName", "分销商名称");
        columnMap.put("agentTel", "客户手机号");
        columnMap.put("agentType", "客户类型");
        columnMap.put("allocationScope", "配备范围");
        columnMap.put("allowedAge", "允许几岁及以上儿童");
        columnMap.put("amapLatitude", "高德地图纬度");
        columnMap.put("amapLongtitude", "高德地图经度");
        columnMap.put("apParentAreaCode", "供应商地区父类编码");
        columnMap.put("applicationScope", "适用范围");
        columnMap.put("applyId", "申请Id");
        columnMap.put("approvalStatus", "审核状态");
        columnMap.put("area", "面积");
        columnMap.put("areaCode", "地区编码");
        columnMap.put("areaEnglishName", "地区英文名称");
        columnMap.put("areaName", "地区名称");
        columnMap.put("areaType", "地区类型");
        columnMap.put("atm", "金额");
        columnMap.put("attachmentList", "附件列表");
        columnMap.put("authUser", "用户");
        columnMap.put("automaticStatus", "自动发单状态");
        columnMap.put("automaticStatus", "自动取消状态");
        columnMap.put("available", "是否营业");
        columnMap.put("available", "自动发单状态");
        columnMap.put("available", "是否展示");
        columnMap.put("availableStatus", "客户状态");
        columnMap.put("availableType", "可见性类型");
        columnMap.put("averageScore", "点评平均数");
        columnMap.put("backEndUrl", "后台链接地址");
        columnMap.put("baiduMapLatitude", "百度地图纬度");
        columnMap.put("baiduMapLongtitude", "百度地图经度");
        columnMap.put("balance", "剩余金额");
        columnMap.put("bankCardList", "银行卡信息列表");
        columnMap.put("bankCode", "行号");
        columnMap.put("bankId", "开户行");
        columnMap.put("bankName", "开户行");
        columnMap.put("baseCurrency", "底价币种");
        columnMap.put("basePrice", "底价");
        columnMap.put("basePriceList", "底价列表");
        columnMap.put("bathRoomPosition", "卫生间位置");
        columnMap.put("bathRoomType", "卫生间类型");
        columnMap.put("bedNumber", "床数量");
        columnMap.put("bedPosition", "床型所处位置");
        columnMap.put("bedQty", "床数量");
        columnMap.put("bedQtyTable", "房型的床型总称");
        columnMap.put("bedQtyTable", "床数类型表");
        columnMap.put("bedRelation", "床型关系");
        columnMap.put("bedRoomSetting", "床型房间设置");
        columnMap.put("bedTypeCode", "床型编码");
        columnMap.put("bedTypeName", "床型名称");
        columnMap.put("bedTypes", "床型");
        columnMap.put("bedWidth", "床宽");
        columnMap.put("bedWidth", "床宽");
        columnMap.put("billName", "账单名称");
        columnMap.put("body", "请求参数");
        columnMap.put("brandCode", "酒店品牌编码");
        columnMap.put("brandIdList", "品牌id列表");
        columnMap.put("brandName", "酒店品牌名称");
        columnMap.put("brandName", "品牌名称");
        columnMap.put("breakfastDesc", "早餐描述");
        columnMap.put("breakfastForm", "早餐形式");
        columnMap.put("breakfastFormList", "早餐形式列表");
        columnMap.put("breakfastInfo", "早餐信息");
        columnMap.put("breakfastOpenType", "早餐开放时间类型");
        columnMap.put("breakfastPrice", "早餐价格");
        columnMap.put("breakfastQty", "早餐数");
        columnMap.put("breakfastState", "早餐状态");
        columnMap.put("breakfastType", "早餐类型列表");
        columnMap.put("breakfastType", "早餐类型");
        columnMap.put("breakfastTypeList", "早餐类型列表");
        columnMap.put("breakfastVariety", "早餐种类列表");
        columnMap.put("breakfastVariety", "早餐种类");
        columnMap.put("breakfastVarietyList", "早餐种类列表");
        columnMap.put("broadband", "是否有宽带");
        columnMap.put("broadRate", "宽带速率");
        columnMap.put("buffetAmount", "自助餐金额");
        columnMap.put("businessCode", "业务单号");
        columnMap.put("businessCodes", "业务单号列表");
        columnMap.put("businessDistrictCode", "商业区编码");
        columnMap.put("businessDistrictName", "商业区名称");
        columnMap.put("businessHours", "营业时间");
        columnMap.put("cancellationDueTime", "取消条款提前时间点");
        columnMap.put("cancellationTerm", "取消条款");
        columnMap.put("cancellationType", "取消条款类型");
        columnMap.put("cancelledContent", "取消内容");
        columnMap.put("cancelledReason", "取消原因");
        columnMap.put("cancelledRequestQty", "取消申请数");
        columnMap.put("changeNumber", "收费频次");
        columnMap.put("channelCode", "渠道编码");
        columnMap.put("channelList", "渠道列表");
        columnMap.put("channelName", "渠道名称");
        columnMap.put("channelOrderCode", "渠道订单号");
        columnMap.put("channels", "自动确认的渠道");
        columnMap.put("chargeForm", "收费形式");
        columnMap.put("chargeManner", "收费方式");
        columnMap.put("chargeMax", "收费最大值");
        columnMap.put("chargeMaxage", "收费最大年龄");
        columnMap.put("chargeMin", "收费最小值");
        columnMap.put("chargeMinage", "收费最小年龄");
        columnMap.put("chargeType", "收费类型");
        columnMap.put("chargingAmt", "收费金额");
        columnMap.put("chargingCurency", "收费币种");
        columnMap.put("chargingDetail", "收费明细");
        columnMap.put("chargingInfo", "充电信息");
        columnMap.put("chargingStandard", "收费标准描述");
        columnMap.put("chargingStationType", "充电桩类别描述");
        columnMap.put("chargingType", "充电方式");
        columnMap.put("checkInDetailList", "入住明细");
        columnMap.put("checkInState", "入住情况");
        columnMap.put("checkInStatus", "入住状态");
        columnMap.put("checkinTime", "规定入住时间");
        columnMap.put("checkInTodayOrderQty", "今日入住订单数");
        columnMap.put("checkoutTime", "规定退房时间");
        columnMap.put("childExtraBedPolicy", "儿童加床政策");
        columnMap.put("childMaxAge", "儿童最大年龄");
        columnMap.put("childMinAge", "儿童最小年龄");
        columnMap.put("childrenBedNum", "儿童共用床铺数");
        columnMap.put("childrenBreakType", "儿童早餐政策");
        columnMap.put("childrenBreakType", "儿童早餐政策类型");
        columnMap.put("cityCode", "城市编码");
        columnMap.put("cityId", "市级城市Id");
        columnMap.put("cityName", "供应商名称");
        columnMap.put("cityName", "城市名称");
        columnMap.put("collectionAmt", "收款金额");
        columnMap.put("commission", "原供货单佣金");
        columnMap.put("companyCode", "运营商编码");
        columnMap.put("companyName", "企业名称");
        columnMap.put("companySignature", "企业落款");
        columnMap.put("comparisonType", "预订天数条款类型");
        columnMap.put("confirmationCode", "确认号");
        columnMap.put("confirmationContent", "确认内容");
        columnMap.put("confirmationStatus", "确认状态");
        columnMap.put("confirmationType", "确认类型");
        columnMap.put("confirmer", "订单确认人");
        columnMap.put("confirmTime", "订单确认时间");
        columnMap.put("contactEmail", "联系人邮箱");
        columnMap.put("contactId", "联系人Id");
        columnMap.put("contactList", "联系人信息列表");
        columnMap.put("contactName", "联系人姓名");
        columnMap.put("contactPhone", "联系人手机");
        columnMap.put("contactRemark", "联系人备注");
        columnMap.put("contactRole", "联系人角色");
        columnMap.put("content", "操作内容");
        columnMap.put("content", "通知内容");
        columnMap.put("countryCode", "国家编码");
        columnMap.put("countryName", "国家名称");

        columnMap.put("createdBy", "备注人");
        columnMap.put("createdBy", "机号");
        columnMap.put("createdBy", "创建人");
        columnMap.put("createdDt", "操作时间");
        columnMap.put("createdDt", "创建时间");
        columnMap.put("createdTime", "备注时间");
        columnMap.put("createTime", "创建时间");
        columnMap.put("creditCardCode", "信用卡编码");
        columnMap.put("creditCardName", "信用卡名称");
        columnMap.put("creditCardName", "信用卡名");
        columnMap.put("creditLine", "信用额度");
        columnMap.put("currency", "币种");
        columnMap.put("currentEmployeePwd", "当前员工密码");
        columnMap.put("currentPage", "当前页");
        columnMap.put("currentPage", "当前页数");
        columnMap.put("date", "日期");
        columnMap.put("dateQueryType", "日期查询类型");
        columnMap.put("decoratedYear", "装修年份");
        columnMap.put("decorationDate", "装修日期");
        columnMap.put("deductedQuota", "扣配额数");
        columnMap.put("deficitStatus", "是否亏损单");
        columnMap.put("deleted", "删除状态");
        columnMap.put("desc", "描述(酒店和房型描述)");
        columnMap.put("deviceName", "设备名称");
        columnMap.put("deviceStatus", "设备状态");
        columnMap.put("disHotelTel", "供应商酒店电话");
        columnMap.put("distance", "酒店离坐标的距离");
        columnMap.put("dueDate", "应处理完日期");
        columnMap.put("duration", "到达花费时间");
        columnMap.put("email", "邮箱");
        columnMap.put("employeeAccount", "企业员工账号");
        columnMap.put("employeeAccountPwd", "企业员工账号密码");
        columnMap.put("employeeAccountType", "企业员工账号类型");
        columnMap.put("employeeAccountType", "用户账号类型");
        columnMap.put("employeeId", "企业员工Id");
        columnMap.put("employeeId", "企业成员Id");
        columnMap.put("employeeIDNumber", "企业员工身份证号");
        columnMap.put("employeeName", "企业员工姓名");
        columnMap.put("employeePwd", "员工密码");
        columnMap.put("employeeRoleId", "企业员工角色id");
        columnMap.put("employeeRoleList", "企业员工角色");
        columnMap.put("employeeRoleName", "企业员工角色名称");
        columnMap.put("employeeStatus", "员工在线状态");
        columnMap.put("employeeTel", "企业员工手机号");
        columnMap.put("endDate", "结束日期");
        columnMap.put("endTime", "营业结束时间");
        columnMap.put("englishArea", "英文地区");
        columnMap.put("eventCount", "事件次数");
        columnMap.put("eventDescription", "事件描述");
        columnMap.put("eventResult", "事件结果");
        columnMap.put("eventType", "事件类型");
        columnMap.put("exchangeBasePrice", "折合金额");
        columnMap.put("extraBedType", "加床和儿童政策");
        columnMap.put("extraBedType", "加床类型");
        columnMap.put("extraRoomInfo", "加床基础信息描述");
        columnMap.put("facilityDesc", "设施描述");
        columnMap.put("idfacilityId", "设施");
        columnMap.put("facilityItemName", "设施项名称");
        columnMap.put("facilityList", "设施列表");
        columnMap.put("facilityName", "设施名称");
        columnMap.put("facilityScope", "设备范围");
        columnMap.put("facilityScore", "设施分");
        columnMap.put("idfacilitySetId", "设施类型");
        columnMap.put("idfacilitySetId", "设施集合");
        columnMap.put("facilitySetName", "设施类型名称");
        columnMap.put("facilitySetName", "设施集合名称");
        columnMap.put("facilityStatus", "设施状态");
        columnMap.put("facilityTypeCode", "设备类别编码");
        columnMap.put("facilityTypeStatus", "设施类别状态");
        columnMap.put("failCode", "失败编码");
        columnMap.put("failLockTime", "失败锁定时间限制");
        columnMap.put("failReason", "失败原因");
        columnMap.put("fieldDemo", "参数项示例");
        columnMap.put("fieldDesc", "参数项描述");
        columnMap.put("fieldName", "字段名称");
        columnMap.put("fieldValue", "参数项值");
        columnMap.put("file", "文件对象");
        columnMap.put("fileFormat", "文件类型");
        columnMap.put("fileId", "文件Id");
        columnMap.put("fileName", "附件名称");
        columnMap.put("fileSize", "文件大小");
        columnMap.put("fileUrl", "文件链接地址");
        columnMap.put("filterable", "标签是否支持筛选");
        columnMap.put("financeType", "财务类型");
        columnMap.put("firstCategory", "酒店一级分类");
        columnMap.put("firstMenu", "一级菜单名称");
        columnMap.put("fixedAmount", "固定套餐金额");
        columnMap.put("floorHeight", "层高");
        columnMap.put("floorNumber", "楼层数");
        columnMap.put("fullUserName", "用户全名");
        columnMap.put("googleMapLatitude", "谷歌地图纬度");
        columnMap.put("googleMapLongtitude", "谷歌地图经度");
        columnMap.put("GPSLatitude", "GPS经度 ");
        columnMap.put("GPSLongtitude", "GPS纬度 ");
        columnMap.put("groupCode", "酒店集团编码");
        columnMap.put("groupIdList", "集团id列表 ");
        columnMap.put("groupName", "酒店集团名称");
        columnMap.put("guaranteeContent", "担保内容");
        columnMap.put("guaranteeDateType", "担保日期类型");
        columnMap.put("guaranteeFeeContent", "担保费用");
        columnMap.put("guaranteeFeeType", "担保房费类型");
        columnMap.put("guaranteeType", "担保条款类型");
        columnMap.put("guestList", "入住人");
        columnMap.put("guestList", "入住人列表");
        columnMap.put("guestName", "入住人");
        columnMap.put("guestQtyType", "住客数设置");
        columnMap.put("guests", "入住人");
        columnMap.put("handledResult", "结果");
        columnMap.put("handledResult", "处理结果");
        columnMap.put("hasProducts", "是否含产品");
        columnMap.put("hasWindow", "是否有窗");
        columnMap.put("hasWirelessBroadband", "是否有无线宽带");
        columnMap.put("hotelAddress", "酒店地址");
        columnMap.put("hotelBrand", "酒店品牌");
        columnMap.put("hotelCategoryList", "酒店分类列表");
        columnMap.put("hotelEnglishAddress", "酒店英文地址");
        columnMap.put("hotelEnglishName", "酒店英文名");
        columnMap.put("hotelEnglishSummary", "酒店英文介绍");
        columnMap.put("hotelFax", "酒店传真");
        columnMap.put("hotelGroup", "酒店集团");
        columnMap.put("hotelId", "酒店Id");
        columnMap.put("hotelKeywords", "酒店关键字");
        columnMap.put("hotelList", "酒店列表");
        columnMap.put("hotelName", "元酒店名称");
        columnMap.put("hotelName", "酒店名称");
        columnMap.put("hotelRank", "酒店星级");
        columnMap.put("hotelScore", "酒店评分");
        columnMap.put("hotelSubCategoryList", "详情分类列表");
        columnMap.put("hotelSummary", "酒店介绍");
        columnMap.put("hotelTel", "酒店电话");
        columnMap.put("hotelType", "酒店类型");
        columnMap.put("hygienicScore", "卫生分");
        columnMap.put("iconContent", "列表页图标内容");
        columnMap.put("idCardNo", "证件号码");
        columnMap.put("idCardNumber", "身份证");
        columnMap.put("idCardNumber", "身份证号码");
        columnMap.put("idCardType", "证件类型");
        columnMap.put("idleStatus", "酒店状态");
        columnMap.put("interfaceName", "接口名称");
        columnMap.put("interfaceType", "接口类型");
        columnMap.put("interfaceUrl", "接口地址");
        columnMap.put("invoiceNotifyUrl", "发票通知接口");
        columnMap.put("invokeList", "调用频次列表");
        columnMap.put("invokeTimes", "调用频次");
        columnMap.put("isAbnormal", "是否问题单");
        columnMap.put("isActive", "是否有效");
        columnMap.put("isAllowsPet", "是否允许携带宠物");
        columnMap.put("isBreakfast", "是否提供早餐");
        columnMap.put("isChargePark", "是否有充电车位");
        columnMap.put("isChildrenBreak", "是否包含儿童早餐");
        columnMap.put("isDelete", "是否删除");
        columnMap.put("isHot", "是否热门");
        columnMap.put("isLimitParking", "车位是否有限");
        columnMap.put("isMyOrder", "是否我的订单");
        columnMap.put("isNonSmokingRoom", "该房型是否不可吸烟");
        columnMap.put("isOfferBed", "是否提供加床");
        columnMap.put("isOfferInfanette", "是否提供婴儿床");
        columnMap.put("isPark", "是否有停车场");
        columnMap.put("isRefused", "是否拒单");
        columnMap.put("isReserved", "是否预定");
        columnMap.put("isShownOnSupplyOrder", "是否显示在供货单");
        columnMap.put("isShowWarn", "备注提醒是否展示");
        columnMap.put("isSubstituted", "是否代下单");
        columnMap.put("isSuperAdmin", "是否超级管理员");
        columnMap.put("itemList", "列表");
        columnMap.put("labelName", "标签名称");
        columnMap.put("landmarkCode", "地标编码");
        columnMap.put("landmarkName", "地标名称");
        columnMap.put("landmarkType", "地标类型");
        columnMap.put("lastLoginTime", "最新登录时间");
        columnMap.put("latitude", "纬度");
        columnMap.put("lineCode", "线路编码");
        columnMap.put("lineIndex", "线路排序");
        columnMap.put("lineName", "线路名称");
        columnMap.put("lineSort", "线路排序");
        columnMap.put("linkman", "联系人");
        columnMap.put("list", "列表");
        columnMap.put("lockName", "锁单人");
        columnMap.put("lockStatus", "锁类型");
        columnMap.put("lockUser", "锁单人账号");
        columnMap.put("logSaveLimit", "审计记录存储上限");
        columnMap.put("logType", "日志类型");
        columnMap.put("longitude", "gps经度 ");
        columnMap.put("longitude", "经度");
        columnMap.put("lowerPrice", "最低价格");
        columnMap.put("lowestPrice", "最低价格");
        columnMap.put("mainUrl", "房间主图");
        columnMap.put("mainUrlList", "房型主图列表");
        columnMap.put("manualOrder", "是否手工单");
        columnMap.put("mappingStatus", "映射状态");
        columnMap.put("markedOrderQty", "标星订单数");
        columnMap.put("markedStatus", "标星");
        columnMap.put("maxAdultQty", "最大入住成人数");
        columnMap.put("maxChildAge", "最大入住儿童数");
        columnMap.put("maxChildQty", "最大入住儿童年龄");
        columnMap.put("maxFailCount", "最大登录失败数");
        columnMap.put("maxGuestNumber", "最大入住人数");
        columnMap.put("maxOnlineCount", "最大同时在线员工数");
        columnMap.put("membershipCardNumber", "会员卡号");
        columnMap.put("menuCode", "一级菜单编码");
        columnMap.put("idmenuId", "菜单");
        columnMap.put("menuList", "菜单列表");
        columnMap.put("menuRank", "目录排序");
        columnMap.put("menus", "角色菜单");
        columnMap.put("merchantCode", "商家编码");
        columnMap.put("merchantCode", "运营商编码");
        columnMap.put("methodCode", "方法编码");
        columnMap.put("methodName", "方法名称");
        columnMap.put("minor", "是否可携带未成年人");
        columnMap.put("mobileNo", "手机号码");
        columnMap.put("model", "模型");
        columnMap.put("modifiedAmt", "加幅值");
        columnMap.put("modifiedbBasePrice", "调整底价价格数");
        columnMap.put("updatedBy", "修改人");
        columnMap.put("modifiedQuota", "配额数");
        columnMap.put("mustReadItem", "必读信息简语项");
        columnMap.put("mustReadSubitem", "必读信息子项");
        columnMap.put("myOrderQty", "我的订单数");
        columnMap.put("name", "名称");
        columnMap.put("nationality", "国籍");
        columnMap.put("networkFeeDesc", "网络资费说明");
        columnMap.put("newBedName", "变更后床型名称");
        columnMap.put("newBedTypeName", "变更后床型名称");
        columnMap.put("newCityCode", "变更后城市编码");
        columnMap.put("newCityName", "变更后城市名称");
        columnMap.put("newCommission", "待修改的佣金");
        columnMap.put("newCountryCode", "变更后国家编码");
        columnMap.put("newCountryName", "变更后国家名称");
        columnMap.put("newHotelAddress", "变更后酒店地址");
        columnMap.put("newHotelName", "变更后酒店名称");
        columnMap.put("newHotelTel", "变更后酒店电话");
        columnMap.put("newOrderQty", "新增明细数");
        columnMap.put("newRoomName", "变更后房型名称");
        columnMap.put("newSpBedName", "变更后供应商床型名称");
        columnMap.put("newSpBedTypeName", "变更后供应商床型名称");
        columnMap.put("newSpCityCode", "变更后供应商城市编码");
        columnMap.put("newSpCityName", "变更后供应商城市名称");
        columnMap.put("newSpCountryCode", "变更后供应商国家编码");
        columnMap.put("newSpCountryName", "变更后供应商国家名称");
        columnMap.put("newSpHotelAddress", "变更后供应商酒店地址");
        columnMap.put("newSpHotelName", "变更后供应商酒店名称");
        columnMap.put("newSpHotelTel", "变更后供应商酒店电话");
        columnMap.put("newSpRoomName", "变更后供应商房型名称");
        columnMap.put("newSupplierBedName", "变更后供应商床型名称");
        columnMap.put("newSupplierRoomName", "变更后供应商房型名称");
        columnMap.put("nightQty", "间夜数");
        columnMap.put("noticeContent", "重要通知信息");
        columnMap.put("noticeType", "通知类型");
        columnMap.put("noticeType", "重要通知类型");
        columnMap.put("notifyType", "通知类型");
        columnMap.put("objId", "对象Id");
        columnMap.put("officialSealId", "公章Id");
        columnMap.put("officialSealUrl", "企业公章");
        columnMap.put("offShelveChannels", "仓库中渠道");
        columnMap.put("offShelveStatus", "是否停售");
        columnMap.put("oldBedName", "原床型名称");
        columnMap.put("oldBedTypeName", "原床型名称");
        columnMap.put("oldCityCode", "原酒店城市编码");
        columnMap.put("oldCityName", "原酒店城市名称");
        columnMap.put("oldCountryCode", "原酒店国家编码");
        columnMap.put("oldCountryName", "原酒店国家名称");
        columnMap.put("oldHotelAddress", "原酒店地址");
        columnMap.put("oldHotelName", "原酒店名称");
        columnMap.put("oldHotelTel", "原酒店电话");
        columnMap.put("oldRoomName", "原房型名称");
        columnMap.put("oldSpBedName", "原供应商床型名称");
        columnMap.put("oldSpBedTypeName", "原供应商床型名称");
        columnMap.put("oldSpCityCode", "原酒店供应商城市编码");
        columnMap.put("oldSpCityName", "原酒店供应商城市名称");
        columnMap.put("oldSpCountryCode", "原酒店供应商国家编码");
        columnMap.put("oldSpCountryName", "原酒店供应商国家名称");
        columnMap.put("oldSpHotelAddress", "原供应商酒店地址");
        columnMap.put("oldSpHotelName", "原供应商酒店名称");
        columnMap.put("oldSpHotelTel", "原供应商酒店电话");
        columnMap.put("oldSpRoomName", "原供应商房型名称");
        columnMap.put("oldSupplierBedName", "原供应商床型名称");
        columnMap.put("oldSupplierRoomName", "原供应商房型名称");
        columnMap.put("onSaleChannels", "销售中渠道");
        columnMap.put("oOrderCard", "供应商");
        columnMap.put("oOrderCard", "表名");
        columnMap.put("openEndtime", "每日开放结束时间");
        columnMap.put("openingDate", "开业日期");
        columnMap.put("openingYear", "开业年份");
        columnMap.put("openStarttime", "每日开放开始时间");
        columnMap.put("operateAccount", "操作账号");
        columnMap.put("operateEndDate", "操作结束日期");
        columnMap.put("operateResult", "操作结果");
        columnMap.put("operateStartDate", "操作开始日期");
        columnMap.put("operationContent", "操作内容");
        columnMap.put("operationObj", "操作对象");
        columnMap.put("operationTime", "操作时间");
        columnMap.put("operationType", "操作类型");
        columnMap.put("operationTypeName", "操作类型");
        columnMap.put("operationUser", "操作人");
        columnMap.put("orderAmt", "订单金额");
        columnMap.put("idorderAttachmentId", "订单附件");
        columnMap.put("orderCode", "订单号");
        columnMap.put("orderCode", "订单编码");
        columnMap.put("idorderId", "订单");
        columnMap.put("orderOwner", "订单归属人");
        columnMap.put("orderOwnerName", "归属人");
        columnMap.put("orderQty", "订单数");
        columnMap.put("orderReceivableType", "订单应收");
        columnMap.put("orderRefundNotifyUrl", "订单退款接口");
        columnMap.put("idorderRequestId", "请求");
        columnMap.put("orderSettlementType", "订单结算方式");
        columnMap.put("orderStatus", "订单状态");
        columnMap.put("orderStatusNotifyUrl", "订单通知接口");
        columnMap.put("orderTagList", "订单标签");
        columnMap.put("orderTotalAmt", "订单总额");
        columnMap.put("orgAgentApiConfig", "分销商api配置信息 ");
        columnMap.put("orgCode", "机构编码");
        columnMap.put("orgMode", "机构模式");
        columnMap.put("orgModeType", "机构黑白名单类型");
        columnMap.put("orgName", "机构名称");
        columnMap.put("orgType", "机构类型");
        columnMap.put("orgVisibleType", "机构黑白名单类型");
        columnMap.put("otherTerm", "其它条款");
        columnMap.put("ourSupplierDirector", "我方供应商负责人");
        columnMap.put("outerTagCode", "外部标签编码");
        columnMap.put("overDraftStatus", "可超状态");
        columnMap.put("overdueDays", "逾期天数");
        columnMap.put("overdueStatus", "是否逾期");
        columnMap.put("idoverseasHotelId", "海外的酒店");
        columnMap.put("packedAmount", "盒装/包装食品金额");
        columnMap.put("page", "当前页");
        columnMap.put("pageSize", "每页显示记录数");
        columnMap.put("pageSize", "每页记录条数");
        columnMap.put("pageSize", "页数");
        columnMap.put("paidAmt", "实付");
        columnMap.put("params", "请求参数");
        columnMap.put("idparentAreaId", "父类地区");
        columnMap.put("parkingInfos", "停车场信息");
        columnMap.put("parkingLotPosition", "充电");
        columnMap.put("parkingLotPosition", "停车场位置描述");
        columnMap.put("parkingLotType", "停车场类型描述");
        columnMap.put("parkingServiceDesc", "停车服务描述");
        columnMap.put("path", "前端路由");
        columnMap.put("payableAmt", "应付金额");
        columnMap.put("payer", "付款方");
        columnMap.put("paymentAmt", "付款金额");
        columnMap.put("paymentType", "支付方式");
        columnMap.put("payTypeList", "可用支付方式");
        columnMap.put("pendingOrderQty", "未处理订单数");
        columnMap.put("petPolicy", "宠物政策");
        columnMap.put("petText", "携带宠物描述");
        columnMap.put("phone", "联系电话");
        columnMap.put("photoList", "凭证照片列表");
        columnMap.put("photoType", "图片类型");
        columnMap.put("urlphotoUrl", "图片");
        columnMap.put("pinyin", "拼音");
        columnMap.put("positionScore", "位置分");
        columnMap.put("postCode", "邮政编码");
        columnMap.put("prebookRead", "订房必读");
        columnMap.put("productId", "产品Id");
        columnMap.put("productList", "产品列表");
        columnMap.put("productName", "产品名称");
        columnMap.put("products", "产品列表");
        columnMap.put("profit", "利润");
        columnMap.put("profitRate", "利润率");
        columnMap.put("provinceCode", "省份编码");
        columnMap.put("provinceName", "省份名称");
        columnMap.put("idpurchaseManagerId", "采购经理");
        columnMap.put("purchaseManagerName", "采购经理名称");
        columnMap.put("purchaseType", "采购方式");
        columnMap.put("quota", "总配额数");
        columnMap.put("quotaAdjustmentType", "配额调整方式");
        columnMap.put("realpath", "文件真实地址");
        columnMap.put("reason", "不能删除原因");
        columnMap.put("receivedAmt", "已收金额");
        columnMap.put("receiver", "收款方");
        columnMap.put("recommendScore", "推荐分值");
        columnMap.put("recommendType", "推荐类型");
        columnMap.put("redecoratedYear", "装修年份");
        columnMap.put("refundFee", "退改费");
        columnMap.put("refundPrice", "退订费");
        columnMap.put("refusedOrderQty", "拒单数");
        columnMap.put("refusedReason", "拒绝原因");
        columnMap.put("remainingQuota", "剩余配额数");
        columnMap.put("remark", "内部备注");
        columnMap.put("remark", "备注信息");
        columnMap.put("remarkType", "备注类型");
        columnMap.put("request", "请求参数");
        columnMap.put("requestType", "请求类型");
        columnMap.put("reservationDueTime", "预定提前时间点");
        columnMap.put("response", "响应参数");
        columnMap.put("restPwd", "重置密码");
        columnMap.put("result", "结果");
        columnMap.put("roleAccountNum", "角色下账号数");
        columnMap.put("roleDescription", "角色说明");
        columnMap.put("roleId", "角色Id");
        columnMap.put("roleName", "角色名称");
        columnMap.put("roomCharging", "酒店床型收费信息");
        columnMap.put("roomChargingDetail", "现有床型收费描述");
        columnMap.put("roomDesc", "房型描述");
        columnMap.put("roomEnglishName", "房型英文名称");
        columnMap.put("roomFloor", "楼层");
        columnMap.put("IdroomId", "房型");
        columnMap.put("roomIdList", "适用房型集合");
        columnMap.put("roomInfo", "现有床型基础信息描述");
        columnMap.put("roomList", "房型列表");
        columnMap.put("roomName", "房型名称");
        columnMap.put("roomNumber", "房间号");
        columnMap.put("roomQty", "房间数量");
        columnMap.put("roomQty", "房型数量");
        columnMap.put("roomStatus", "房态");
        columnMap.put("saleAmt", "销售金额");
        columnMap.put("saleCurrency", "售价币种");
        columnMap.put("saleDate", "销售日期");
        columnMap.put("saleDate", "售卖日期");
        columnMap.put("saleItemList", "价格列表");
        columnMap.put("idsaleManagerId", "销售经理");
        columnMap.put("saleManagerName", "销售经理");
        columnMap.put("saleNightQty", "销售间夜");
        columnMap.put("salePrice", "售价");
        columnMap.put("salePrice", "房费");
        columnMap.put("salePriceList", "售卖列表");
        columnMap.put("secondCategory", "酒店二级分类");
        columnMap.put("secondMenu", "二级菜单名称");
        columnMap.put("secondMenuList", "二级菜单列表");
        columnMap.put("secretKey", "api密钥");
        columnMap.put("selected", "是否选中");
        columnMap.put("sendingStatus", "发单状态");
        columnMap.put("serialNumber", "排序");
        columnMap.put("serverType", "服务类型");
        columnMap.put("serviceScore", "服务分");
        columnMap.put("settleDate", "结算时间");
        columnMap.put("settledBy", "结算人");
        columnMap.put("settlementCurrency", "结算币种");
        columnMap.put("settlementDate", "结算日期");
        columnMap.put("settlementStatus", "结算状态");
        columnMap.put("settlementType", "结算方式");
        columnMap.put("sort", "排序");
        columnMap.put("sourceHotelId", "来源的酒店id ");
        columnMap.put("sourceSupplierCode", "来源的供应商编码");
        columnMap.put("sourceType", "数据来源");
        columnMap.put("spAreaCode", "供应商地区编码");
        columnMap.put("spAreaName", "供应商地区名称");
        columnMap.put("spAreaType", "供应商地区类型");
        columnMap.put("spBedTypes", "供应商床型");
        columnMap.put("spCityCode", "供应商城市编码");
        columnMap.put("spCityName", "供应商城市名称");
        columnMap.put("spCountryCode", "供应商国家编码");
        columnMap.put("spCountryName", "供应商国家名称");
        columnMap.put("specialOrderStatus", "特殊订单");
        columnMap.put("specialRequest", "特殊要求");
        columnMap.put("spHotelAddress", "供应商酒店地址");
        columnMap.put("spHotelBrandCode", "供应商品牌编码");
        columnMap.put("spHotelBrandName", "供应商地区编码");
        columnMap.put("spHotelId", "供应商酒店Id ");
        columnMap.put("spHotelName", "供应商酒店名称");
        columnMap.put("spHotelNameLike", "模糊查询供应商酒店名称");
        columnMap.put("spHotelTel", "供应商酒店电话");
        columnMap.put("spRoomId", "供应商房型id ");
        columnMap.put("spRoomName", "供应商房型名称");
        columnMap.put("starList", "星级列表");
        columnMap.put("startDate", "开始日期");
        columnMap.put("startTime", "营业开始时间");
        columnMap.put("statementAmt", "账单金额");
        columnMap.put("statementCode", "账单编码");
        columnMap.put("idstatementId", "账单");
        columnMap.put("statementOrderIdList", "账单明细id列表");
        columnMap.put("statementStatus", "账单状态");
        columnMap.put("status", "删除状态");
        columnMap.put("status", "状态");
        columnMap.put("subtitle", "子标题");
        columnMap.put("subwayStationCode", "地铁编码");
        columnMap.put("subwayStationName", "地铁名称");
        columnMap.put("subwayStationPinYing", "地铁站拼音");
        columnMap.put("summary", "生成简介");
        columnMap.put("supplierCode", "供应商编码");
        columnMap.put("supplierConfirmer", "供应商确认人");
        columnMap.put("supplierHotelName", "供应商酒店名称");
        columnMap.put("idsupplierId", "供应商");
        columnMap.put("supplierList", "供应商列表");
        columnMap.put("supplierName", "供应商名称");
        columnMap.put("supplierOrderCode", "供应商订单号");
        columnMap.put("suppliertimeType", "时间段类型");
        columnMap.put("idsupplyAttachmentId", "供货单附件");
        columnMap.put("supplyClass", "供应类型编码");
        columnMap.put("supplyClass", "供应商类型");
        columnMap.put("supplyCode", "供应商编码");
        columnMap.put("supplyName", "供应类型名称");
        columnMap.put("supplyOrderCode", "供货单编码");
        columnMap.put("idsupplyOrderId", "供货单");
        columnMap.put("supplyOrderIdList", "供货单id列表");
        columnMap.put("supplyOrderList", "供货单列表");
        columnMap.put("supplyOrderTotalAmt", "供货单总金额");
        columnMap.put("idsupplyProductId", "订单产品");
        columnMap.put("supplyType", "供货方式");
        columnMap.put("tableName", "表名");
        columnMap.put("tagCode", "标签编码");
        columnMap.put("tagDesc", "标签描述");
        columnMap.put("tAgentApiConfig", "分销商api配置表");
        columnMap.put("tagFirstSort", "标签第一排序");
        columnMap.put("tagName", "标签名称");
        columnMap.put("tagSecondSort", "标签第二排序");
        columnMap.put("tagTypeCode", "标签类型编码");
        columnMap.put("tagTypeName", "标签类型名称");
        columnMap.put("tagWeight", "标签权重");
        columnMap.put("target", "目标");
        columnMap.put("targetContent", "目标内容");
        columnMap.put("targetId", "目标id");
        columnMap.put("targetType", "目标类型");
        columnMap.put("tBaseinfoHotel", "酒店表");
        columnMap.put("telephone", "联系电话");
        columnMap.put("termsOfTheState", "条款状态");
        columnMap.put("tFacilityInfo", "设施表");
        columnMap.put("thirdPartyType", "第三方类型");
        columnMap.put("tHotel", "酒店表");
        columnMap.put("tHotelBreakfast", "酒店早餐表");
        columnMap.put("tHotelCharging", "酒店充电桩表");
        columnMap.put("tHotelComment", "酒店评分表");
        columnMap.put("tHotelCreditCard", "酒店信用卡");
        columnMap.put("tHotelFacility", "酒店/房型设备以及收费表");
        columnMap.put("tHotelLandmark", "酒店地标表");
        columnMap.put("tHotelLandmark1", "酒店地标表");
        columnMap.put("tHotelParking", "酒店停车场");
        columnMap.put("tHotelSource", "酒店来源表");
        columnMap.put("tipInfo", "提示信息");
        columnMap.put("tMetroLine", "地铁站表");
        columnMap.put("tMonitorMappingHotel", "酒店监控表");
        columnMap.put("tMonitorMappingRoom", "房型监控表");
        columnMap.put("tNoticeContent", "重要通知信息");
        columnMap.put("to", "接收对象");
        columnMap.put("todayNewOrderQty", "今日新单");
        columnMap.put("token", "令牌");
        columnMap.put("tOrgModeConfig", "表名");
        columnMap.put("tOrgVisibleConfig", "可见性模式表");
        columnMap.put("tOrgVisibleInfo", "可见性设置详情");
        columnMap.put("totalAmt", "总价");
        columnMap.put("totalCount", "总记录数");
        columnMap.put("totalPage", "总页数");
        columnMap.put("transportationType", "交通类型");
        columnMap.put("tRoomBedDetail", "房型床型明细表");
        columnMap.put("tRoomFacility", "房型设施关系表");
        columnMap.put("tRoomSource", "房型来源表");
        columnMap.put("tSubwayStation", "地铁站表");
        columnMap.put("tSupplyAreaMapping", "供应商地区映射");
        columnMap.put("tSupplyBrandMapping", "供应商品牌映射");
        columnMap.put("tTag", "标签表");
        columnMap.put("tTypeInfo", "数据字典类别表");
        columnMap.put("tTypeInfo1", "数据字典类别表");
        columnMap.put("tTypeInfoHellp", "数据字典类别表");
        columnMap.put("type", "类型编码");
        columnMap.put("type", "类型");
        columnMap.put("typeCode", "类型编码");
        columnMap.put("typeCode", "类别编码");
        columnMap.put("typeName", "信用卡名称");
        columnMap.put("typeName", "类别名称");
        columnMap.put("unconfirmedPaidAmt", "付款待确认");
        columnMap.put("unit", "统计单位");
        columnMap.put("unlockTime", "账号解锁时间");
        columnMap.put("unpaidAmt", "未付金额");
        columnMap.put("unreceivedAmt", "未收金额");
        columnMap.put("updatedBy", "操作人");
        columnMap.put("updatedBy", "更新人");
        columnMap.put("updatedContent", "操作时间");
        columnMap.put("updatedContent", "操作内容");
        columnMap.put("updatedDt", "修改时间");
        columnMap.put("updatedDt", "更新时间");
        columnMap.put("updatedOrderQty", "更新明细数");
        columnMap.put("updatedTime", "操作时间");
        columnMap.put("updatedTime", "更新时间");
        columnMap.put("updatedType", "更新类型");
        columnMap.put("updatePwdTime", "最新修改密码时间");
        columnMap.put("url", "链接地址");
        columnMap.put("user", "用户");
        columnMap.put("userAccount", "用户账号");
        columnMap.put("userAccountType", "用户账号类型");
        columnMap.put("userAccout", "用户账号");
        columnMap.put("iduserId", "用户");
        columnMap.put("userIdNumber", "身份证");
        columnMap.put("userName", "用户名");
        columnMap.put("userName", "用户名称");
        columnMap.put("userPassword", "用户密码");
        columnMap.put("userPwd", "用户密码");
        columnMap.put("userTel", "用户手机号");
        columnMap.put("visible", "标签是否展示");
        columnMap.put("visibleType", "模式类型");
        columnMap.put("visitEndTime", "企业员工访问结束时间");
        columnMap.put("visitIp", "访问IP");
        columnMap.put("visitStartTime", "企业员工访问开始时间");
        columnMap.put("weekList", "星期");
        columnMap.put("weeklyIndex", "每周开放指数");
        columnMap.put("weeklyList", "指定日期开放列表");
        columnMap.put("whetherCharging", "是否收费描述");
        columnMap.put("whetherCharing", "是否收费描述");
        columnMap.put("whetherReserving", "是否需预订描述");
        columnMap.put("windowType", "窗户类型");
        columnMap.put("windowType", "窗型类型");
        columnMap.put("workOrderId", "工单id ");
        columnMap.put("workOrderStatus", "工单状态");
        columnMap.put("percentage", "占比");
        columnMap.put("ignoreStartTime", "忽略开始时间");
        columnMap.put("ignoreEndTime", "忽略结束时间");
        columnMap.put("operationCount", "操作数量");
        columnMap.put("operateType", "操作类型");
        columnMap.put("operateTypeList", "操作类型");
        columnMap.put("operateTypeName", "操作类型");
        columnMap.put("operateTypeNamoe", "操作类型");
        columnMap.put("operationName", "操作类型");
        columnMap.put("operateRole", "操作角色");
        columnMap.put("operateRoleList", "操作角色");
        columnMap.put("isChoose", "是否选择");
        columnMap.put("configureCapacity", "配置容量");
        columnMap.put("staffStatus", "状态");
        columnMap.put("staffType", "类型");
        columnMap.put("endType", "类型");
        columnMap.put("countType", "统计类型");
        columnMap.put("fileUrl", "文件地址");
        columnMap.put("fileName", "文件名称");
        columnMap.put("fileFormat", "文件后缀");
        columnMap.put("importId", "导入ID");
        columnMap.put("receivableAmt", "应收金额");
        columnMap.put("supplierImportStatementId", "导入的供应商账单id");
        columnMap.put("updatedDt", "修改时间");
        columnMap.put("supplierStatementCode", "供应商账单编号");
        columnMap.put("supplierStatementName", "供应商账单名称");
        columnMap.put("supplierTotalReceivables", "供应商总要收");
        columnMap.put("reconciliationResultType", "对账结果类型");
        columnMap.put("supplyPaidAmt", "应付供应商金额");
        columnMap.put("supplyPaidAmtCurrency", "应付供应商金额币种");
        columnMap.put("supplierReceivedAmt", "供应商应收金额");
        columnMap.put("supplierReceivedAmtCurrency", "供应商应收金额币种");
        columnMap.put("supplierStatementIdList", "供应商账单编号列表");
        columnMap.put("statementId", "账单id");
        columnMap.put("importStartDate", "导入的开始日期");
        columnMap.put("importEndDate", "导入的结束日期");
        columnMap.put("consistentCount", "一致数量");
        columnMap.put("amountIncorrectCount", "金额不对数量");
        columnMap.put("supplierLessOrderCount", "供应商少单数量");
        columnMap.put("lessOrderCount", "我方少单数量");
        columnMap.put("receivableAmtWithCurrency", "应收金额带单位");
        columnMap.put("supplierStatementId", "供应商账单Id");
        columnMap.put("supplyPaidAmtWithCurrency", "应付供应商金额带单位");
        columnMap.put("supplierReceivedAmtWithCurrency", "供应商应收金额带单位");
        columnMap.put("comparativeAmount", "比较金额");
        columnMap.put("billStatus", "账单的状态");
        columnMap.put("exportCode", "导出编号");
        columnMap.put("exportType", "导出类型");
        columnMap.put("exportStatus", "导出状态");
        columnMap.put("fileUrl", "文件路径");
        columnMap.put("exportReportId", "导出报表Id");
        columnMap.put("deletedTimeRange", "删除时间范围");
        columnMap.put("drainageSetting", "引流设置");
        columnMap.put("menuId", "菜单Id");
        columnMap.put("menuName", "目录名称");
        columnMap.put("parentCode", "父目录编码");
        columnMap.put("frontEndUrl", "前端链接地址");
        columnMap.put("logSetting", "日志设置");
        columnMap.put("additionalCharges", "附加费");
        columnMap.put("sumSalePrice", "售价之和");
        columnMap.put("sumBasePrice", "底价之和");
        columnMap.put("orderAdditionalCharges", "订单附加项");
        columnMap.put("supplyOrderAdditionalChargesList", "供货单附加项列表");
        columnMap.put("orderId", "订单id");
        columnMap.put("additionalChargesType", "附加费类型");
        columnMap.put("additionalChargesName", "附加费名称");
        columnMap.put("additionalChargesDate", "附加费日期");
        columnMap.put("quantity", "数量");
        columnMap.put("supplyOrderId", "供货单id");
        columnMap.put("additionalChargesItemList", "附加项子项列表");
        columnMap.put("additionalChargesDateItem", "附加项日期项");
        columnMap.put("additionalChargesOrderType", "订单类型");
        columnMap.put("statementIdList", "账单Id列表");
        columnMap.put("isRepeat", "是否重发");
        columnMap.put("roomAdditionalCharges", "房间附加费");
        columnMap.put("orderAdditionalCharges", "订单总附加费");

        return columnMap;
    }
}

