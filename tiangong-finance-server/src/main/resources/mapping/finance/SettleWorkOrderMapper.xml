<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.finance.statement.mapper.SettleWorkOrderMapper">

    <select id="querySettleWorkOrderPage" resultType="com.tiangong.finance.remote.statement.response.SettleWorkOrderPageResponseDTO">
        SELECT
            id,
            task_code,
            settle_task_code,
            statement_create_status,
            statement_confirm_status,
            work_order_status,
            fail_reason,
            task_status
        FROM f_settle_work_order
        <where>
            <if test="taskCode != null and taskCode != ''">
                AND task_code LIKE CONCAT('%', #{taskCode}, '%')
            </if>
            <if test="settleTaskCode != null and settleTaskCode != ''">
                AND settle_task_code LIKE CONCAT('%', #{settleTaskCode}, '%')
            </if>
            <if test="taskStatus != null">
                AND (statement_create_status = #{taskStatus}
                    OR statement_confirm_status = #{taskStatus}
                    OR work_order_status = #{taskStatus})
            </if>
        </where>
        ORDER BY created_dt DESC
    </select>

    <select id="selectOneByStatementCode" resultType="java.lang.String">
        SELECT statement_code from f_settle_work_order WHERE statement_code = #{statementCode}
    </select>

    <update id="updateSettleWorkOrder">
        UPDATE f_settle_work_order
        <trim prefix="set" suffixOverrides=",">
            updated_dt = NOW(),
            updated_by = #{updatedBy},
            <if test="statementCreateStatus != null" >
                statement_create_status = #{statementCreateStatus},
            </if>
            <if test="statementConfirmStatus != null" >
                statement_confirm_status = #{statementConfirmStatus},
            </if>
            <if test="workOrderStatus != null" >
                work_order_status = #{workOrderStatus},
            </if>
            <if test="failReason != null and failReason != ''" >
                fail_reason = #{failReason},
            </if>
            <if test="statementId != null" >
                statement_id = #{statementId},
            </if>
            <if test="statementCode != null and statementCode != ''" >
                statement_code = #{statementCode},
            </if>
        </trim>
        WHERE settle_task_code = #{settleTaskCode}
    </update>
</mapper>