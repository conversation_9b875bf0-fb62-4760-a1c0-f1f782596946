package com.tiangong.finance.convert;

import com.tiangong.dis.dto.CurrencyExchangeRateDTO;
import com.tiangong.dto.exchange.ExchangeRateDTO;
import com.tiangong.file.resp.FileResp;
import com.tiangong.finance.domain.ExchangeRatePO;
import com.tiangong.finance.remote.workorder.request.ConfirmWorkOrderDTO;
import com.tiangong.finance.remote.workorder.request.WorkOrderAttchDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderDTO;
import com.tiangong.finance.statement.domain.NonVccAutoBillConfigPO;
import com.tiangong.finance.statement.domain.resp.GetNonVccAutoBillConfigDetailResp;
import com.tiangong.finance.statement.domain.req.SaveOrUpdateNonVccAutoBillConfigReq;
import com.tiangong.finance.workorder.domain.WorkOrderPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类型转换
 */
@Mapper
public interface DTOConvert {

    DTOConvert INSTANCE = Mappers.getMapper(DTOConvert.class);

    /**
     * 将汇率DTO转换为汇率持久化对象
     *
     * @param exchangeRateDTO 汇率DTO对象
     * @return 返回转换后的汇率持久化对象
     */
    ExchangeRatePO exchangeRatePOConvert(ExchangeRateDTO exchangeRateDTO);

    /**
     * 将汇率持久化对象转换为货币汇率DTO
     *
     * @param exchangeRatePO 汇率持久化对象
     * @return 返回转换后的货币汇率DTO
     */
    CurrencyExchangeRateDTO CurrencyExchangeRateConvert(ExchangeRatePO exchangeRatePO);

    /**
     * 将汇率持久化对象转换为货币汇率DTO（重复方法）
     *
     * @param exchangeRatePO 汇率持久化对象
     * @return 返回转换后的货币汇率DTO
     */
    CurrencyExchangeRateDTO currencyExchangeRateDTOConvert(ExchangeRatePO exchangeRatePO);

    /**
     * 将文件响应列表转换为工单附件DTO列表
     *
     * @param model 文件响应列表
     * @return 返回转换后的工单附件DTO列表
     */
    List<WorkOrderAttchDTO> workOrderAttchDTOConvert(List<FileResp> model);

    /**
     * 将确认工单DTO转换为工单持久化对象
     *
     * @param request 确认工单DTO对象
     * @return 返回转换后的工单持久化对象
     */
    WorkOrderPO workOrderUpdateConvert(ConfirmWorkOrderDTO request);

    /**
     * 将工单持久化对象转换为工单DTO
     *
     * @param workOrderPO 工单持久化对象
     * @return 返回转换后的工单DTO
     */
    WorkOrderDTO workOrderDTOConvert(WorkOrderPO workOrderPO);

    /**
     * 将保存或更新非VCC自动账单配置请求转换为非VCC自动账单配置持久化对象
     *
     * @param request 保存或更新非VCC自动账单配置请求
     * @return 返回转换后的非VCC自动账单配置持久化对象
     */
    NonVccAutoBillConfigPO nonVccAutoBillConfigPOConvert(SaveOrUpdateNonVccAutoBillConfigReq request);

    /**
     * 将非VCC自动账单配置持久化对象转换为获取非VCC自动账单配置详情响应
     *
     * @param request 非VCC自动账单配置持久化对象
     * @return 返回转换后的获取非VCC自动账单配置详情响应
     */
    GetNonVccAutoBillConfigDetailResp nonVccAutoBillConfigListDTOConvert(NonVccAutoBillConfigPO request);
}
