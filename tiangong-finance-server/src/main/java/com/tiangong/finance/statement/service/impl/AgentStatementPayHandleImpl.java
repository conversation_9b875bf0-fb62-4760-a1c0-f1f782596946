package com.tiangong.finance.statement.service.impl;

import com.tiangong.common.Response;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.CheckStatusEnum;
import com.tiangong.finance.enums.StatementStatusEnum;
import com.tiangong.finance.remote.statement.request.QueryStatementOrderListDTO;
import com.tiangong.finance.remote.statement.response.StatementOrderDTO;
import com.tiangong.finance.statement.domain.AgentStatementPO;
import com.tiangong.finance.statement.dto.CancelStatementWorkOrderDTO;
import com.tiangong.finance.statement.dto.ConfirmStatementWorkOrderDTO;
import com.tiangong.finance.statement.dto.UpdateOrderFinanceDTO;
import com.tiangong.finance.statement.mapper.AgentStatementMapper;
import com.tiangong.finance.statement.mapper.AgentStatementOrderMapper;
import com.tiangong.finance.statement.service.AgentStatementPayHandle;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.organization.remote.dto.AgentCreditLineDTO;
import com.tiangong.organization.remote.dto.AgentCreditLineResultDTO;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class AgentStatementPayHandleImpl implements AgentStatementPayHandle {

    @Autowired
    private AgentStatementMapper agentStatementMapper;

    @Autowired
    private AgentStatementOrderMapper agentStatementOrderMapper;

    @Autowired
    private AgentRemote agentRemote;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmStatementWorkOrder(ConfirmStatementWorkOrderDTO request) {
        // 1.更新账单结算金额
        boolean isFinishSettlement = false;
        AgentStatementPO agentStatementQuery = new AgentStatementPO();
        agentStatementQuery.setStatementCode(request.getStatementCode());
        AgentStatementPO agentStatementPo = agentStatementMapper.selectOne(agentStatementQuery);
        AgentStatementPO agentStatementUpdate = new AgentStatementPO();
        agentStatementUpdate.setId(agentStatementPo.getId());
        agentStatementUpdate.setReceivedAmt(agentStatementPo.getReceivedAmt().add(request.getConfirmAmt()));
        agentStatementUpdate.setUnreceivedAmt(agentStatementPo.getUnreceivedAmt().subtract(request.getConfirmAmt()));
        if (request.getNotifyAmt().compareTo(BigDecimal.ZERO) > 0) {
            agentStatementUpdate.setUnconfirmedReceivedAmt(agentStatementPo.getUnconfirmedReceivedAmt().subtract(request.getNotifyAmt()));
        } else {
            agentStatementUpdate.setUnconfirmedPaidAmt(agentStatementPo.getUnconfirmedPaidAmt().subtract(BigDecimal.ZERO.subtract(request.getNotifyAmt())));
        }
        if (agentStatementPo.getStatementAmt().compareTo(agentStatementUpdate.getReceivedAmt()) == 0) {
            isFinishSettlement = true;
        }

        Date currentDate = DateUtilX.getCurrentDate();
        if (isFinishSettlement && agentStatementPo.getStatementStatus().equals(StatementStatusEnum.CONFIRMED.key)) {
            agentStatementUpdate.setRealSettlementDate(currentDate);
            agentStatementUpdate.setSettlementStatus(1);
            // 2.更新订单的结算状态和结算金额
            UpdateOrderFinanceDTO updateOrderFinanceDTO = new UpdateOrderFinanceDTO();
            updateOrderFinanceDTO.setStatementId(agentStatementPo.getId());
            updateOrderFinanceDTO.setIsUpdateSettlementStatus(1);
            updateOrderFinanceDTO.setIsUpdateSettlementAmount(1);
            updateOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
            agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);

        }
        agentStatementUpdate.setUpdatedDt(currentDate);
        agentStatementUpdate.setUpdatedBy(request.getOperator());
        agentStatementMapper.updateByPrimaryKeySelective(agentStatementUpdate);
        // 返还客户额度
        List<AgentCreditLineDTO> list = new ArrayList<>();
        AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
        agentCreditLineDTO.setAgentCode(agentStatementPo.getAgentCode());
        if (StrUtilX.isNotEmpty(agentStatementPo.getSubtractLineAccount())) {
            agentCreditLineDTO.setAgentCode(agentStatementPo.getSubtractLineAccount());
        } else {
            agentCreditLineDTO.setAgentCode(agentStatementPo.getAgentCode());
        }
        agentCreditLineDTO.setDeductRefundCreditLine(BigDecimal.ZERO.subtract(agentStatementUpdate.getReceivedAmt()).compareTo(BigDecimal.ZERO) < 0 ? request.getConfirmAmt() : request.getConfirmAmt().negate());
        list.add(agentCreditLineDTO);
        Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(list);
        if (!creditLineResponse.isSuccess()) {
            throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
        }
        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加入到统计报表
                CompletableFuture.runAsync(() -> {
                    // 2024年03月04日
                    // 加入特殊流程
                    // 确认工单的过程（供应商结算、奖励结算、返佣结算），查询对应的订单列表，触发订单报表统计
                    try {
                        QueryStatementOrderListDTO query = new QueryStatementOrderListDTO();
                        query.setStatementId(agentStatementPo.getId());
                        List<StatementOrderDTO> orderList = agentStatementOrderMapper.queryStatementOrderList(query);
                        for (StatementOrderDTO statementOrderDTO : orderList) {
                            // 加入到统计报表队列中
                            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, statementOrderDTO.getOrderCode());
                        }
                    } catch (Exception e) {
                        log.error("【订单工单处理】加入到统计报表队列异常,error:", e);
                    }
                });
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelStatementWorkOrder(CancelStatementWorkOrderDTO request) {
        // 更新通知金额
        AgentStatementPO agentStatementQuery = new AgentStatementPO();
        agentStatementQuery.setStatementCode(request.getStatementCode());
        AgentStatementPO agentStatementPo = agentStatementMapper.selectOne(agentStatementQuery);
        AgentStatementPO agentStatementUpdate = new AgentStatementPO();
        agentStatementUpdate.setId(agentStatementPo.getId());
        if (request.getNotifyAmt().compareTo(BigDecimal.ZERO) > 0) {
            agentStatementUpdate.setUnconfirmedReceivedAmt(agentStatementPo.getUnconfirmedReceivedAmt().subtract(request.getNotifyAmt()));
        } else {
            agentStatementUpdate.setUnconfirmedPaidAmt(agentStatementPo.getUnconfirmedPaidAmt().subtract(BigDecimal.ZERO.subtract(request.getNotifyAmt())));
        }
        agentStatementMapper.updateByPrimaryKeySelective(agentStatementUpdate);
    }
}
