package com.tiangong.finance.statement.server;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.finance.remote.statement.request.ProtocolOrderSettlementCostVO;
import com.tiangong.finance.remote.statement.response.ProtocolOrderSettlementCostResponseDTO;
import com.tiangong.finance.statement.domain.req.ProtocolOrderSettlementCostReq;
import com.tiangong.finance.statement.domain.resp.ProtocolOrderSettleInfoResp;
import com.tiangong.finance.statement.service.ProtocolOrderSettlementCostService;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 协议订单结算成本
 */
@Slf4j
@RestController
@RequestMapping("/finance/protocolOrderSettlementCost")
public class ProtocolOrderSettlementCostServer extends BaseController {

    /**
     * ID参数字段名
     */
    private static final String IDS_FIELD = "ids";

    @Autowired
    private ProtocolOrderSettlementCostService protocolOrderSettleInfoService;

    @Autowired
    private AgentRemote agentRemote;

    /**
     * 导入协议订单结算成本
     */
    @PostMapping("/importProtocolOrderSettlementCost")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> importProtocolOrderSettlementCost(MultipartFile file) {
        return protocolOrderSettleInfoService.importProtocolOrderSettlementCost(file, super.getUserName());
    }

    /**
     * 查询协议订单结算成本列表(分页)
     */
    @PostMapping("/queryProtocolOrderSettlementCostPage")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<ProtocolOrderSettleInfoResp>> queryProtocolOrderSettlementCostPage(@RequestBody ProtocolOrderSettlementCostReq request) {
        return Response.success(protocolOrderSettleInfoService.queryProtocolOrderSettlementCostPage(request));
    }

    /**
     * 删除协议订单结算成本
     */
    @PostMapping("/delProtocolOrderSettlementCost")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> delProtocolOrderSettlementCost(@RequestBody ProtocolOrderSettlementCostReq request) {
        request.setOperator(super.getUserName());
        protocolOrderSettleInfoService.delProtocolOrderSettlementCost(request);
        return Response.success();
    }

    /**
     * 重试匹配协议订单
     */
    @PostMapping("/retryMatchingProtocolOrder")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> retryMatchingProtocolOrder(@RequestBody ProtocolOrderSettlementCostReq request) {
        request.setOperator(super.getUserName());
        protocolOrderSettleInfoService.retryMatchingProtocolOrder(request);
        return Response.success();
    }

    /**
     * 查询协议订单结算成本列表
     */
    @PostMapping("/queryProtocolOrderSettlementCostList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<List<ProtocolOrderSettlementCostResponseDTO>> queryProtocolOrderSettlementCostList(@RequestBody ProtocolOrderSettlementCostVO request) {
        return Response.success(protocolOrderSettleInfoService.queryProtocolOrderSettlementCostList(request));
    }

    /**
     * 下载结算成本导入模版
     */
    @PostMapping("/downLoadFile")
    public File downLoadFile(HttpServletResponse response) {
        InputStream in = null;
        try {
            String filename;
            if (LanguageTypeEnum.zh_CN.getValue().equals(getLanguage())) {
                filename = "结算成本导入模版";
            } else {
                filename = "settlementCostImportTemplate";
            }
            in = this.getClass().getClassLoader().getResourceAsStream("template/protocolOrderSettlementCost.xlsx");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            IOUtils.copy(in, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            log.error("下载结算成本导入模版异常", e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("下载结算成本导入模版异常", e);
                }
            }
        }
        return null;
    }

    /**
     * 接收协议订单结算成本信息
     */
    @AnonymousAccess
    @PostMapping("/receiveProtocolOrderSettlementCost")
    public Response<Object> receiveProtocolOrderSettlementCost(@RequestBody List<ProtocolOrderSettlementCostVO> voList) {
        protocolOrderSettleInfoService.receiveProtocolOrderSettlementCost(voList);
        return Response.success();
    }

    /**
     * 推送协议订单结算成本信息任务
     */
    @AnonymousAccess
    @PostMapping("/pushProtocolOrderSettlementCostInfoTask")
    public Response<Object> pushProtocolOrderSettlementCostInfoTask(@RequestBody Map<String, String> param) {
        if (StrUtilX.isNotEmpty(param.get(IDS_FIELD))) {
            protocolOrderSettleInfoService.pushProtocolOrderSettlementCostInfoTask(JSONObject.parseObject(param.get(IDS_FIELD), new TypeReference<List<Integer>>(){}));
        } else {
            agentRemote.initPushProtocolOrderSettlementCostAgentCodeToRedisTask();
        }
        return Response.success();
    }
}