package com.tiangong.finance.statement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.tiangong.common.remote.SequenceRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.DeleteEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.StatementTypeEnum;
import com.tiangong.enums.SystemCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.BusinessTypeEnum;
import com.tiangong.finance.enums.CheckStatusEnum;
import com.tiangong.finance.enums.OutgoingStateEnum;
import com.tiangong.finance.enums.StatementStatusEnum;
import com.tiangong.finance.lock.service.FinanceLockService;
import com.tiangong.finance.remote.ExchangeRateRemote;
import com.tiangong.finance.remote.lock.request.FinanceLockSupplyOrderDTO;
import com.tiangong.finance.remote.lock.response.GuestDTO;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.*;
import com.tiangong.finance.remote.workorder.request.NotifyCollectionDTO;
import com.tiangong.finance.remote.workorder.request.NotifyItemDTO;
import com.tiangong.finance.remote.workorder.request.NotifyPaymentDTO;
import com.tiangong.finance.statement.domain.*;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementEntity;
import com.tiangong.finance.statement.dto.*;
import com.tiangong.finance.statement.mapper.*;
import com.tiangong.finance.statement.service.SettleWorkOrderService;
import com.tiangong.finance.statement.service.StatementAmtLogService;
import com.tiangong.finance.statement.service.SupplierStatementService;
import com.tiangong.finance.workorder.service.FinanceNofityService;
import com.tiangong.finance.enums.TaskStatusEnum;
import com.tiangong.keys.RedisKey;
import com.tiangong.keys.SettleConstantKey;
import com.tiangong.organization.remote.dto.ContactSupplierDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SupplierStatementServiceImpl implements SupplierStatementService {

    private final Integer batchQueryCount = 2000;

    /**
     * 查询所有结算状态的标识值
     */
    private static final String ALL_SETTLEMENT_STATUS = "-1";

    /**
     * 查询所有逾期状态的标识值
     */
    private static final String ALL_OVERDUE_STATUS = "-1";

    /**
     * 查询所有账单状态的标识值
     */
    private static final String ALL_STATEMENT_STATUS = "-1";

    /**
     * 账单状态阈值
     */
    private static final Integer STATEMENT_STATUS_THRESHOLD = 2;

    /**
     * 分批处理大小
     */
    private static final Integer BATCH_SIZE = 200;

    @Autowired
    private SupplierStatementMapper supplierStatementMapper;

    @Autowired
    private SupplierStatementOrderMapper supplierStatementOrderMapper;

    @Autowired
    private SupplierImportStatementMapper supplierImportStatementMapper;

    @Autowired
    private FinanceNofityService financeNofityService;

    @Autowired
    private FinanceLockService financeLockService;

    @Autowired
    private SequenceRemote sequenceRemote;

    @Autowired
    private Executor asyncCreatBill;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private ContactMapper contactMapper;

    @Autowired
    private ExchangeRateRemote exchangeRateRemote;

    @Autowired
    private SettleWorkOrderService settleWorkOrderService;

    @Autowired
    private AgentStatementMapper agentStatementMapper;

    @Autowired
    private StatementAmtLogService statementAmtLogService;

    @Override
    public PaginationSupportDTO<SupplierStatementListResponseDTO> queryStatementList(QuerySupplierStatementListDTO request) {
        if (StrUtilX.isNotEmpty(request.getSettlementStatus()) && request.getSettlementStatus().equals(ALL_SETTLEMENT_STATUS)) {
            request.setSettlementStatus(null);
        }
        if (StrUtilX.isNotEmpty(request.getOverdueStatus()) && request.getOverdueStatus().equals(ALL_OVERDUE_STATUS)) {
            request.setOverdueStatus(null);
        }
        if (StrUtilX.isNotEmpty(request.getStatementStatus()) && request.getStatementStatus().equals(ALL_STATEMENT_STATUS)) {
            request.setStatementStatus(null);
        }
        //如果供货单编号不为空,则查询对应的账单编号
        List<SupplierStatementOrderPO> supplierStatementOrderPOS = new ArrayList<>();
        Set<Integer> statementIdSet = new HashSet<>();

        //订单编码或者酒店id不为空的场景
        if (StrUtilX.isNotEmpty(request.getSupplyOrderCodeOrOrderCode()) || null != request.getHotelId()) {
            List<Integer> statementIds = supplierStatementOrderMapper.queryStatementIds(request);
            if (statementIds.size() == 0) {
                statementIds.add(-1);
            }
            request.setStatementIdList(statementIds);
        }

        if (StrUtilX.isNotEmpty(request.getSupplyOrderCode())) {
            Example example = new Example(SupplierStatementOrderPO.class);
            example.createCriteria().andEqualTo("supplyOrderCode", request.getSupplyOrderCode());
            supplierStatementOrderPOS = supplierStatementOrderMapper.selectByExample(example);
            if (CollUtilX.isNotEmpty(supplierStatementOrderPOS)) {
                statementIdSet = supplierStatementOrderPOS.stream().map(SupplierStatementOrderPO::getStatementId).collect(Collectors.toSet());
                request.setStatementIdList(new ArrayList<>(statementIdSet));
            }
        }

        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            request.setSupplierName(null);
        }
        List<SupplierStatementListResponseDTO> list = supplierStatementMapper.queryStatementList(request);
        PageInfo<SupplierStatementListResponseDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<SupplierStatementListResponseDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public PaginationSupportDTO<UncheckOutSupplierDTO> queryUncheckOutSupplierList(QueryUncheckOutSupplierListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            request.setSupplierName(null);
        }
        List<UncheckOutSupplierDTO> list = supplierStatementMapper.queryUncheckOutSupplierList(request);
        // 获取缓存的未出账状态，如果状态为失败的话加上失败原因
        for (UncheckOutSupplierDTO uncheckOutSupplierDTO : list) {
            Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), uncheckOutSupplierDTO.getSupplierCode() + "_" + uncheckOutSupplierDTO.getCurrency());
            // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
            if (supplierAccountState == null) {
                supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
            }
            if (supplierAccountState != null) {
                OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
                if (outgoingStateDTO != null) {
                    BeanUtils.copyProperties(outgoingStateDTO, uncheckOutSupplierDTO);
                }
            } else {
                OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
                RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), uncheckOutSupplierDTO.getSupplierCode() + "_" + uncheckOutSupplierDTO.getCurrency(), JSONObject.toJSONString(stateDTO));
                uncheckOutSupplierDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            }
        }
        PageInfo<UncheckOutSupplierDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<UncheckOutSupplierDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Transactional
    @Override
    public void createStatement(CreateSupplierStatementDTO request) {
        long millis = System.currentTimeMillis();
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && outgoingStateDTO.getOutgoingState() != null && outgoingStateDTO.getOutgoingState().equals(OutgoingStateEnum.CHECKING.key)) {
            throw new SysException(ErrorCodeEnum.SUPPLIER_HAS_AN_OUTOFACCOUNT_STATUS);
        }
        // 设置供应商redis的出账状态
        InsertStatementSupplyOrderDTO insertStatementSupplyOrderDTO = new InsertStatementSupplyOrderDTO();
        BeanUtils.copyProperties(request, insertStatementSupplyOrderDTO);
        Integer integer = request.getOrderNumber() < 60 ? 2 : request.getOrderNumber() / 20;
        OutgoingStateDTO stateDTO = new OutgoingStateDTO();
        stateDTO.setCheckInDate(request.getStartDate());
        stateDTO.setCheckOutDate(request.getEndDate());
        stateDTO.setOutgoingState(OutgoingStateEnum.CHECKING.key);
        stateDTO.setCreatTime(millis - 800);
        stateDTO.setTime(integer);
        stateDTO.setDateQueryType(request.getDateQueryType());
        RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency(), JSONObject.toJSONString(stateDTO));
        // 异步创建账单
        if (StrUtilX.isNotEmpty(request.getSettleTaskCode())
                || (request.getIsSyncCreateStatement() != null && request.getIsSyncCreateStatement() == 1)) {
            //为自助结算创建账单
            creatSettleBill(request);
        } else {
            asyncCreatBill(request);
        }
    }

    @Override
    public SupplierStatementDetailDTO queryStatementDetail(StatementIdDTO request) {
        SupplierStatementDetailDTO supplierStatementDetailDTO = new SupplierStatementDetailDTO();
        SupplierStatementPO supplierStatementPO = new SupplierStatementPO();
        if (request.getStatementId() != null) {
            supplierStatementPO.setId(request.getStatementId());
        }
        if (StrUtilX.isNotEmpty(request.getStatementCode())) {
            supplierStatementPO.setStatementCode(request.getStatementCode());
        }
        supplierStatementPO = supplierStatementMapper.selectOne(supplierStatementPO);
        BeanUtils.copyProperties(supplierStatementPO, supplierStatementDetailDTO);
        supplierStatementDetailDTO.setStatementId(supplierStatementPO.getId());
        ContactSupplierDTO contactSupplierDTO = supplierStatementMapper.selectContact(supplierStatementPO.getSupplierCode(), DeleteEnum.STATUS_EXIST.key);
        if (!StringUtils.isEmpty(contactSupplierDTO)) {
            supplierStatementDetailDTO.setContactName(contactSupplierDTO.getContactName());
        }

        String statementCode = settleWorkOrderService.selectOneByStatementCode(supplierStatementPO.getStatementCode());
        if (StrUtilX.isNotEmpty(statementCode)) {
            supplierStatementDetailDTO.setIsSettle(1);
        } else {
            supplierStatementDetailDTO.setIsSettle(0);
        }

        if (supplierStatementDetailDTO.getStatementStatus() < STATEMENT_STATUS_THRESHOLD) {
            supplierStatementDetailDTO.setModifyBy(null);
            supplierStatementDetailDTO.setModifyDt(null);
        }
        long days = DateUtilX.getDay((supplierStatementPO.getSettlementDate()), (supplierStatementPO.getRealSettlementDate() == null ? DateUtilX.stringToDate(DateUtilX.dateToString(new Date())) : supplierStatementPO.getRealSettlementDate()));
        supplierStatementDetailDTO.setOverdueDays(days > 0 ? (int) days : 0);


        //查询新增明细数
        QueryUnCheckOutSupplyOrderDTO queryUnCheckOutSupplyOrderDTO = new QueryUnCheckOutSupplyOrderDTO();
        queryUnCheckOutSupplyOrderDTO.setPageSize(10000);
        queryUnCheckOutSupplyOrderDTO.setDateQueryType(1);
        queryUnCheckOutSupplyOrderDTO.setSupplierCode(supplierStatementDetailDTO.getSupplierCode());
        queryUnCheckOutSupplyOrderDTO.setCompanyCode(request.getCompanyCode());
        queryUnCheckOutSupplyOrderDTO.setStatementType(request.getStatementType());
        supplierStatementDetailDTO.setNewOrderQty(queryUnCheckOutSupplyOrder(queryUnCheckOutSupplyOrderDTO).getItemList().size());

        //查询更新明细数
        QueryUpdatedStatementOrderListDTO queryUpdatedStatementOrderListDTO = new QueryUpdatedStatementOrderListDTO();
        queryUpdatedStatementOrderListDTO.setStatementId(request.getStatementId());
        queryUpdatedStatementOrderListDTO.setPageSize(10000);
        queryUpdatedStatementOrderListDTO.setStatementType(request.getStatementType());
        supplierStatementDetailDTO.setUpdatedOrderQty(queryUpdatedStatementOrderList(queryUpdatedStatementOrderListDTO).getItemList().size());

        Example example = new Example(ContactPO.class);
        example.createCriteria().andEqualTo("orgCode", supplierStatementPO.getSupplierCode())
                .andEqualTo("active", DeleteEnum.STATUS_EXIST.key)
                .andEqualTo("contactType", 0)
                .andLike("contactRole", "%0%");
        List<ContactPO> contactPOS = contactMapper.selectByExample(example);
        System.out.println("查询联系人数据是：" + contactPOS);
        if (CollUtilX.isNotEmpty(contactPOS) && contactPOS.get(0) != null) {
            supplierStatementDetailDTO.setContactName(contactPOS.get(0).getContactName());
            supplierStatementDetailDTO.setContactTel(contactPOS.get(0).getContactTel());
        }
        return supplierStatementDetailDTO;
    }

    @Override
    public PaginationSupportDTO<StatementSupplyOrderDTO> queryStatementOrderList(QueryStatementSupplyOrderListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderList(request);
        PageInfo<StatementSupplyOrderDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<StatementSupplyOrderDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public PaginationSupportDTO<StatementSupplyOrderWithOrderCodeDTO> queryStatementOrderWithOrderCodeList(QueryStatementSupplyOrderListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<StatementSupplyOrderWithOrderCodeDTO> list = supplierStatementOrderMapper.queryStatementOrderWithOrderCodeList(request);
        for (StatementSupplyOrderWithOrderCodeDTO statementSupplyOrder : list) {
            statementSupplyOrder.setPaidOrgCurrencyAmt(statementSupplyOrder.getPaidAmt().multiply(statementSupplyOrder.getSaleRate()).setScale(2, RoundingMode.UP));
            statementSupplyOrder.setPayableOrgCurrencyAmt(statementSupplyOrder.getPayableAmt().multiply(statementSupplyOrder.getSaleRate()).setScale(2, RoundingMode.UP));
            statementSupplyOrder.setUnpaidOrgCurrencyAmt(statementSupplyOrder.getUnpaidAmt().multiply(statementSupplyOrder.getSaleRate()).setScale(2, RoundingMode.UP));
            statementSupplyOrder.setPayOrgCurrencyAmt(statementSupplyOrder.getPayAmt().multiply(statementSupplyOrder.getSaleRate()).setScale(2, RoundingMode.UP));
            if (StrUtilX.isNotEmpty(statementSupplyOrder.getRoomNumbers()) && CollUtilX.isNotEmpty(statementSupplyOrder.getGuests())) {
                Map<Integer, String> guestMap = new HashMap<>();
                for (GuestDTO guest : statementSupplyOrder.getGuests()) {
                    if (guestMap.containsKey(guest.getRoomNumber())) {
                        String name = guestMap.get(guest.getRoomNumber()) + "," + guest.getName();
                        guestMap.put(guest.getRoomNumber(), name);
                    } else {
                        guestMap.put(guest.getRoomNumber(), guest.getName());
                    }
                }
                List<String> roomNumbers = StrUtilX.stringToList(statementSupplyOrder.getRoomNumbers(), ",");
                StringBuilder sb = new StringBuilder();
                for (String roomNumber : roomNumbers) {
                    if (StrUtilX.isNotEmpty(sb.toString())) {
                        sb.append(",");
                    }
                    sb.append(guestMap.get(Integer.parseInt(roomNumber)));
                }
                statementSupplyOrder.setGuest(sb.toString());
            }
        }
        PageInfo<StatementSupplyOrderWithOrderCodeDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<StatementSupplyOrderWithOrderCodeDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public PaginationSupportDTO<UnCheckOutSupplyOrderDTO> queryUnCheckOutSupplyOrder(QueryUnCheckOutSupplyOrderDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<UnCheckOutSupplyOrderDTO> list = supplierStatementOrderMapper.queryUnCheckOutSupplyOrder(request);
        for (UnCheckOutSupplyOrderDTO unCheckOutSupplyOrder : list) {
            unCheckOutSupplyOrder.setPaidOrgCurrencyAmt(unCheckOutSupplyOrder.getPaidAmt().multiply(unCheckOutSupplyOrder.getSaleRate()).setScale(2, RoundingMode.UP));
            unCheckOutSupplyOrder.setPayableOrgCurrencyAmt(unCheckOutSupplyOrder.getPayableAmt().multiply(unCheckOutSupplyOrder.getSaleRate()).setScale(2, RoundingMode.UP));
            unCheckOutSupplyOrder.setUnpaidOrgCurrencyAmt(unCheckOutSupplyOrder.getPayableOrgCurrencyAmt().subtract(unCheckOutSupplyOrder.getPaidOrgCurrencyAmt()).setScale(2, RoundingMode.UP));

            if (StrUtilX.isNotEmpty(unCheckOutSupplyOrder.getRoomNumbers()) && CollUtilX.isNotEmpty(unCheckOutSupplyOrder.getGuests())) {
                Map<Integer, String> guestMap = new HashMap<>();
                for (GuestDTO guest : unCheckOutSupplyOrder.getGuests()) {
                    if (guestMap.containsKey(guest.getRoomNumber())) {
                        String name = guestMap.get(guest.getRoomNumber()) + "," + guest.getName();
                        guestMap.put(guest.getRoomNumber(), name);
                    } else {
                        guestMap.put(guest.getRoomNumber(), guest.getName());
                    }
                }
                List<String> roomNumbers = StrUtilX.stringToList(unCheckOutSupplyOrder.getRoomNumbers(), ",");
                StringBuilder sb = new StringBuilder();
                for (String roomNumber : roomNumbers) {
                    if (StrUtilX.isNotEmpty(sb.toString())) {
                        sb.append(",");
                    }
                    sb.append(guestMap.get(Integer.parseInt(roomNumber)));
                }
                unCheckOutSupplyOrder.setGuest(sb.toString());
            }
        }
        PageInfo<UnCheckOutSupplyOrderDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<UnCheckOutSupplyOrderDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Transactional
    @Override
    public void addStatementOrderList(AddStatementSupplyOrderListDTO request) {
        SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());

        //按条件批量增加，查询满足条件的订单
        if (null == request.getSupplyOrderIdList() || 0 == request.getSupplyOrderIdList().size()) {
            QueryUnCheckOutSupplyOrderDTO queryUnCheckOutSupplyOrderDTO = new QueryUnCheckOutSupplyOrderDTO();
            queryUnCheckOutSupplyOrderDTO.setSupplierCode(supplierStatementPO.getSupplierCode());
            queryUnCheckOutSupplyOrderDTO.setCompanyCode(request.getCompanyCode());
            queryUnCheckOutSupplyOrderDTO.setDateQueryType(request.getDateQueryType());
            queryUnCheckOutSupplyOrderDTO.setStartDate(request.getStartDate());
            queryUnCheckOutSupplyOrderDTO.setEndDate(request.getEndDate());
            queryUnCheckOutSupplyOrderDTO.setStatementType(request.getStatementType());
            List<UnCheckOutSupplyOrderDTO> list = supplierStatementOrderMapper.queryUnCheckOutSupplyOrder(queryUnCheckOutSupplyOrderDTO);
            if (CollUtilX.isNotEmpty(list)) {
                List<Integer> supplyOrderIdList = new ArrayList<>();
                for (UnCheckOutSupplyOrderDTO unCheckOutSupplyOrderDTO : list) {
                    supplyOrderIdList.add(unCheckOutSupplyOrderDTO.getSupplyOrderId());
                }
                request.setSupplyOrderIdList(supplyOrderIdList);
            } else {
                return;
            }
        }

        // 1.保存账单明细
        InsertStatementSupplyOrderDTO insertStatementSupplyOrderDTO = new InsertStatementSupplyOrderDTO();
        insertStatementSupplyOrderDTO.setStatementId(request.getStatementId());
        insertStatementSupplyOrderDTO.setSupplyOrderIdList(request.getSupplyOrderIdList());
        insertStatementSupplyOrderDTO.setCompanyCode(supplierStatementPO.getCompanyCode());
        insertStatementSupplyOrderDTO.setStatementType(request.getStatementType());
        supplierStatementOrderMapper.saveBatchStatementOrder(insertStatementSupplyOrderDTO);

        // 2.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        statementIdDTO.setCurrency(request.getCurrency());
        updateStatementAmount(statementIdDTO);

        //记录日志
        SupplierStatementPO po = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());

        StatementAmtLogDTO dto = new StatementAmtLogDTO();
        dto.setNewStatementAmt(po.getStatementAmt());
        dto.setOriginalStatementAmt(supplierStatementPO.getStatementAmt());
        dto.setType(request.getStatementType() + 1);
        dto.setContentType(0);
        dto.setOrderIdList(request.getSupplyOrderIdList());
        dto.setCreatedBy(request.getOperator());
        dto.setCurrency(request.getCurrency());
        dto.setStatementId(request.getStatementId());
        statementAmtLogService.insertStatementAmtLog(dto);

        // 3.更新订单对账状态为出账中，并将订单加锁
        UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
        updateSupplyOrderFinanceDTO.setStatementId(request.getStatementId());
        updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
        updateSupplyOrderFinanceDTO.setFinanceLockStatus(0);
        updateSupplyOrderFinanceDTO.setFinanceType(request.getStatementType());
        updateSupplyOrderFinanceDTO.setSupplyOrderIdList(request.getSupplyOrderIdList());
        supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
        if (Objects.equals(supplierStatementPO.getStatementStatus(), StatementStatusEnum.CHECKING.key)) {
            List<Integer> supplyOrderIdList = request.getSupplyOrderIdList();
            for (Integer supplyOrderId : supplyOrderIdList) {
                FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
                financeLockSupplyOrderDTO.setSupplyOrderId(supplyOrderId);
                financeLockSupplyOrderDTO.setLockStatus(1);
                financeLockSupplyOrderDTO.setFinanceType(request.getStatementType());
                financeLockService.lockSupplyOrder(financeLockSupplyOrderDTO);
            }
        }
    }

    @Transactional
    @Override
    public void deleteStatementOrderList(DeleteStatementOrderListDTO request) {
        long millis = System.currentTimeMillis();
        SupplierStatementPO oldAgentStatement = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());

        //按条件批量删除，查询满足条件的订单
        //List<Integer> orderIds = new ArrayList<>();
        if (null == request.getStatementOrderIdList() || 0 == request.getStatementOrderIdList().size()) {
            QueryStatementSupplyOrderListDTO queryStatementSupplyOrderListDTO = new QueryStatementSupplyOrderListDTO();
            queryStatementSupplyOrderListDTO.setStatementId(request.getStatementId());
            queryStatementSupplyOrderListDTO.setDateQueryType(request.getDateQueryType());
            queryStatementSupplyOrderListDTO.setStartDate(request.getStartDate());
            queryStatementSupplyOrderListDTO.setEndDate(request.getEndDate());
            List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderList(queryStatementSupplyOrderListDTO);
            if (CollUtilX.isNotEmpty(list)) {
                List<Integer> statementOrderIdList = new ArrayList<>();
                for (StatementSupplyOrderDTO statementOrderDTO : list) {
                    statementOrderIdList.add(statementOrderDTO.getStatementOrderId());
                }
                request.setStatementOrderIdList(statementOrderIdList);
            } else {
                return;
            }
        }

        // 1.更新结算状态为可出账
        UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
        updateSupplyOrderFinanceDTO.setStatementId(request.getStatementId());
        updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
        // 移除订单明细时候,解除订单锁定
        updateSupplyOrderFinanceDTO.setFinanceLockStatus(0);
        updateSupplyOrderFinanceDTO.setFinanceType(request.getStatementType());
        // 如果数据过多一次性删除会导致sql长度超出，采用分批删除
        if (request.getStatementOrderIdList().size() > BATCH_SIZE) {
            List<List<Integer>> groupList = Lists.partition(request.getStatementOrderIdList(), BATCH_SIZE);
            for (List<Integer> integers : groupList) {
                updateSupplyOrderFinanceDTO.setStatementOrderIdList(integers);
                supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
            }
        } else {
            updateSupplyOrderFinanceDTO.setStatementOrderIdList(request.getStatementOrderIdList());
            supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
        }

        List<Integer> orderIds = supplierStatementOrderMapper.selectStatementOrderListByOrderId(request.getStatementOrderIdList());

        supplierStatementOrderMapper.deleteStatementOrderList(request.getStatementOrderIdList());
        // 3.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        updateStatementAmount(statementIdDTO);

        //记录日志
        SupplierStatementPO po = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());
        StatementAmtLogDTO dto = new StatementAmtLogDTO();
        dto.setNewStatementAmt(po.getStatementAmt());
        dto.setOriginalStatementAmt(oldAgentStatement.getStatementAmt());
        dto.setType(request.getStatementType() + 1);
        dto.setContentType(2);
        dto.setOrderIdList(orderIds);
        dto.setCreatedBy(request.getOperator());
        dto.setCurrency(request.getCurrency());
        dto.setStatementId(request.getStatementId());
        statementAmtLogService.insertStatementAmtLog(dto);
    }

    @Transactional
    @Override
    public void modifyStatementStatus(ModifyStatementStatusDTO request) {
        // 1.确认账单前检查账单金额是否发生变化   2024-05-23：修改确认账单不校验金额是否发送变化
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key)) {
            StatementIdDTO statementIdDTO = new StatementIdDTO();
            statementIdDTO.setStatementId(request.getStatementId());
            statementIdDTO.setStatementType(request.getStatementType());
            BigDecimal statementAmount = supplierStatementOrderMapper.queryNewStatementAmount(statementIdDTO);
            if (statementAmount == null) {
                statementAmount = BigDecimal.ZERO;
            }
            SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());
            if (supplierStatementPO.getStatementAmt().compareTo(statementAmount) != 0 && (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CONFIRMED.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key))) {
                throw new SysException(ErrorCodeEnum.BILL_AMOUNT_HAS_CHANGED_PLEASE_UPDATE_THE_BILL_FIRST);
            }
            if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)
                    && (supplierStatementPO.getUnconfirmedPaidAmt() == null || supplierStatementPO.getUnconfirmedPaidAmt().compareTo(BigDecimal.ZERO) != 0
                    || supplierStatementPO.getUnconfirmedReceivedAmt() == null || supplierStatementPO.getUnconfirmedReceivedAmt().compareTo(BigDecimal.ZERO) != 0
                    || supplierStatementPO.getPaidAmt() == null || supplierStatementPO.getPaidAmt().compareTo(BigDecimal.ZERO) != 0)) {
                throw new SysException(ErrorCodeEnum.BILLS_HAVE_BEEN_RECEIVED_OR_PAID_NO_CANCEL);
            }
        }
        // 2.更新账单状态
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        supplierStatementUpdate.setId(request.getStatementId());

        UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
        updateSupplyOrderFinanceDTO.setStatementId(request.getStatementId());
        updateSupplyOrderFinanceDTO.setFinanceType(request.getStatementType());

        // 3.更新订单对账状态
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CONFIRMED.key)) {
            SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectOne(supplierStatementUpdate);
            supplierStatementUpdate.setUpdatedBy(request.getOperator());
            supplierStatementUpdate.setUpdatedDt(new Date());

            if (supplierStatementPO.getPaidAmt() != null && supplierStatementPO.getStatementAmt().compareTo(supplierStatementPO.getPaidAmt()) == 0) {
                updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                supplierStatementUpdate.setSettlementStatus(1);
                supplierStatementUpdate.setRealSettlementDate(new Date());
                updateSupplyOrderFinanceDTO.setIsUpdateSettlementStatus(1);
                updateSupplyOrderFinanceDTO.setIsUpdateSettlementAmount(1);
                //已结算则此账单对应的所有供应商账单的对比状态都变为已对比
                supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                        .eq(SupplierImportStatementEntity::getStatementId, request.getStatementId())
                        .eq(SupplierImportStatementEntity::getStatementStatus, 1)
                        .set(SupplierImportStatementEntity::getStatementStatus, 2));
            }
            updateSupplyOrderFinanceDTO.setFinanceLockStatus(1);
            if (request.getOperator().equals(SettleConstantKey.SETTLE_OPERATOR)) {
                updateSupplyOrderFinanceDTO.setFinanceLockStatus(2);
            }
            supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
        } else if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
            supplierStatementUpdate.setUpdatedBy(request.getOperator());
            supplierStatementUpdate.setUpdatedDt(new Date());

            //查询该订单下面的所有账单
            QueryStatementSupplyOrderListDTO queryStatementOrderListDTO = new QueryStatementSupplyOrderListDTO();
            queryStatementOrderListDTO.setStatementId(request.getStatementId());
            List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);

            //相当于将所有的订单明细删除
            if (CollUtilX.isNotEmpty(list)) {
                DeleteStatementOrderListDTO deleteStatementOrderListDTO = new DeleteStatementOrderListDTO();
                deleteStatementOrderListDTO.setStatementId(request.getStatementId());
                deleteStatementOrderListDTO.setStatementType(request.getStatementType());
                List<Integer> orderList = new ArrayList<>();
                for (StatementSupplyOrderDTO statementSupplyOrderDTO : list) {
                    orderList.add(statementSupplyOrderDTO.getStatementOrderId());
                }
                deleteStatementOrderListDTO.setStatementOrderIdList(orderList);
                deleteStatementOrderList(deleteStatementOrderListDTO);
            }
            updateSupplyOrderFinanceDTO.setFinanceLockStatus(0);
            supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
        }
        supplierStatementUpdate.setStatementStatus(request.getStatementStatus());
        supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
            QueryUpdatedStatementOrderListDTO queryUpdatedStatementOrderListDTO = new QueryUpdatedStatementOrderListDTO();
            queryUpdatedStatementOrderListDTO.setPageSize(10000);
            queryUpdatedStatementOrderListDTO.setStatementId(request.getStatementId());
            queryUpdatedStatementOrderListDTO.setStatementType(request.getStatementType());
            int size = queryUpdatedStatementOrderList(queryUpdatedStatementOrderListDTO).getItemList().size();
            if (size > 0) {
//                return new Response(ResultCodeEnum.FAILURE.code,null,"有尚未更新的账单明细，无法确认账单");
                StatementIdDTO statementIdDTO1 = new StatementIdDTO();
                statementIdDTO1.setStatementId(request.getStatementId());
                statementIdDTO1.setOperator(request.getOperator());
                updateStatementOrderList(statementIdDTO1);
                return;
            }
        }
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CONFIRMED.key)) {
            QueryStatementSupplyOrderListDTO queryStatementSupplyOrderListDTO = new QueryStatementSupplyOrderListDTO();
            queryStatementSupplyOrderListDTO.setStatementId(request.getStatementId());
            List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderList(queryStatementSupplyOrderListDTO);
            for (StatementSupplyOrderDTO statementSupplyOrderDTO : list) {
                FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
                Example example = new Example(SupplierStatementOrderPO.class);
                example.createCriteria().andEqualTo("supplyOrderCode", statementSupplyOrderDTO.getSupplyOrderCode())
                        .andEqualTo("statementId", request.getStatementId());
                SupplierStatementOrderPO supplierStatementOrderPO = supplierStatementOrderMapper.selectOneByExample(example);
                financeLockSupplyOrderDTO.setSupplyOrderId(supplierStatementOrderPO.getSupplyOrderId());
                financeLockSupplyOrderDTO.setLockStatus(1);
                financeLockSupplyOrderDTO.setFinanceType(request.getStatementType());
                financeLockService.lockSupplyOrder(financeLockSupplyOrderDTO);
            }
        } else if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.UN_CHECK.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
            QueryStatementSupplyOrderListDTO queryStatementSupplyOrderListDTO = new QueryStatementSupplyOrderListDTO();
            queryStatementSupplyOrderListDTO.setStatementId(request.getStatementId());
            List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderList(queryStatementSupplyOrderListDTO);
            for (StatementSupplyOrderDTO statementSupplyOrderDTO : list) {
                FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
                Example example = new Example(SupplierStatementOrderPO.class);
                example.createCriteria().andEqualTo("supplyOrderCode", statementSupplyOrderDTO.getSupplyOrderCode())
                        .andEqualTo("statementId", request.getStatementId());
                SupplierStatementOrderPO supplierStatementOrderPO = supplierStatementOrderMapper.selectOneByExample(example);
                financeLockSupplyOrderDTO.setSupplyOrderId(supplierStatementOrderPO.getSupplyOrderId());
                financeLockSupplyOrderDTO.setLockStatus(0);
                financeLockSupplyOrderDTO.setFinanceType(request.getStatementType());
                financeLockService.lockSupplyOrder(financeLockSupplyOrderDTO);
            }
        }
    }

    @Transactional
    @Override
    public void cancelStatement(ModifyStatementStatusDTO request) {
        // 1.确认账单前检查账单金额是否发生变化   2024-05-23：修改确认账单不校验金额是否发送变化
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key)) {
            SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());
            if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)
                    && (supplierStatementPO.getUnconfirmedPaidAmt() == null || supplierStatementPO.getUnconfirmedPaidAmt().compareTo(BigDecimal.ZERO) != 0
                    || supplierStatementPO.getUnconfirmedReceivedAmt() == null || supplierStatementPO.getUnconfirmedReceivedAmt().compareTo(BigDecimal.ZERO) != 0
                    || supplierStatementPO.getPaidAmt() == null || supplierStatementPO.getPaidAmt().compareTo(BigDecimal.ZERO) != 0)) {
                throw new SysException(ErrorCodeEnum.BILLS_HAVE_BEEN_RECEIVED_OR_PAID_NO_CANCEL);
            }
        }

        OutgoingStateDTO stateDTO = new OutgoingStateDTO();
        stateDTO.setOutgoingState(OutgoingStateEnum.CHECKING.key);
        Integer integer = request.getOrderNumber() < 60 ? 2 : request.getOrderNumber() / 50;
        stateDTO.setTime(integer);
        RedisTemplateX.hPut(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));

        // 异步处理账单明细
        CompletableFuture.runAsync(() ->
                deleteStatementOrder(request)
        );
    }

    @Override
    public PaginationSupportDTO<UpdatedStatementSupplyOrderDTO> queryUpdatedStatementOrderList(QueryUpdatedStatementOrderListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<UpdatedStatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryUpdatedStatementOrderList(request);
        PageInfo<UpdatedStatementSupplyOrderDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<UpdatedStatementSupplyOrderDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Transactional
    @Override
    public void updateStatementOrderList(StatementIdDTO request) {
        SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());
        // 1.更新账单明细
        supplierStatementOrderMapper.updateStatementOrderList(request);

        // 2.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        updateStatementAmount(statementIdDTO);

        //更新明细时为订单加锁
        List<Integer> orderIds = new ArrayList<>();
        QueryStatementSupplyOrderListDTO queryStatementSupplyOrderListDTO = new QueryStatementSupplyOrderListDTO();
        queryStatementSupplyOrderListDTO.setStatementId(request.getStatementId());
        List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderList(queryStatementSupplyOrderListDTO);
        for (StatementSupplyOrderDTO statementSupplyOrderDTO : list) {
            FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
            Example example = new Example(SupplierStatementOrderPO.class);
            example.createCriteria().andEqualTo("supplyOrderCode", statementSupplyOrderDTO.getSupplyOrderCode())
                    .andEqualTo("statementId", supplierStatementPO.getId());
            SupplierStatementOrderPO supplierStatementOrderPO = supplierStatementOrderMapper.selectOneByExample(example);
            financeLockSupplyOrderDTO.setSupplyOrderId(supplierStatementOrderPO.getSupplyOrderId());
            if (supplierStatementPO.getStatementStatus() != null && supplierStatementPO.getStatementStatus().equals(StatementStatusEnum.UN_CHECK.key)) {
                financeLockSupplyOrderDTO.setLockStatus(0);
            } else {
                financeLockSupplyOrderDTO.setLockStatus(1);
            }
            financeLockSupplyOrderDTO.setFinanceType(request.getStatementType());
            financeLockService.lockSupplyOrder(financeLockSupplyOrderDTO);
            orderIds.add(statementSupplyOrderDTO.getSupplyOrderId());
        }

        //记录日志
        SupplierStatementPO po = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());
        //账单金额改变才记录日志
        if (supplierStatementPO.getStatementAmt().compareTo(po.getStatementAmt()) != 0){
            StatementAmtLogDTO dto = new StatementAmtLogDTO();
            dto.setNewStatementAmt(po.getStatementAmt());
            dto.setOriginalStatementAmt(supplierStatementPO.getStatementAmt());
            dto.setType(request.getStatementType() + 1);
            dto.setContentType(1);
            dto.setOrderIdList(orderIds);
            dto.setCreatedBy(request.getOperator());
            dto.setCurrency(request.getCurrency());
            dto.setStatementId(request.getStatementId());
            statementAmtLogService.insertStatementAmtLog(dto);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatementSupplyOrderPayAmt(ModifySettlementOrderPayAmtDTO request) {
        SupplierStatementPO oldSupplierStatementOrderPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());
        if (oldSupplierStatementOrderPO.getStatementStatus().intValue() == StatementStatusEnum.CONFIRMED.key && oldSupplierStatementOrderPO.getSettlementStatus() == 1){
            throw new SysException(ErrorCodeEnum.BILL_IS_SETTLEMENT_NOT_UPDATE_PAY_AMOUNT);
        }

        SupplierStatementOrderPO supplierStatementOrderPO = supplierStatementOrderMapper.selectByPrimaryKey(request.getStatementSupplyOrderId());
        if (supplierStatementOrderPO == null){
            throw new SysException(ErrorCodeEnum.BILL_NOT_EXIST_SUPPLY);
        }
        //1、更新账单中的供货单要付金额
        supplierStatementOrderMapper.updateStatementSupplyOrderPayAmt(request);

        //2、更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        this.updateStatementAmount(statementIdDTO);

        //记录日志
        SupplierStatementPO newSupplierStatementOrderPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());
        if (oldSupplierStatementOrderPO.getStatementAmt().compareTo(newSupplierStatementOrderPO.getStatementAmt()) != 0){
            List<Integer> orderIdList = new ArrayList<>();
            orderIdList.add(supplierStatementOrderPO.getSupplyOrderId());


            StatementAmtLogDTO dto = new StatementAmtLogDTO();
            dto.setNewStatementAmt(newSupplierStatementOrderPO.getStatementAmt());
            dto.setOriginalStatementAmt(oldSupplierStatementOrderPO.getStatementAmt() == null ? BigDecimal.ZERO: oldSupplierStatementOrderPO.getStatementAmt());
            if (oldSupplierStatementOrderPO.getStatementType() == 0){
                dto.setType(1);
            }else if (oldSupplierStatementOrderPO.getStatementType() == 1){
                dto.setType(2);
            }else {
                dto.setType(3);
            }
            dto.setContentType(1);
            dto.setOrderIdList(orderIdList);
            dto.setCreatedBy(request.getOperator());
            dto.setCurrency(oldSupplierStatementOrderPO.getCurrency());
            dto.setStatementId(request.getStatementId());
            statementAmtLogService.insertStatementAmtLog(dto);
        }
    }

    @Transactional
    @Override
    public void notifyCollectionOfStatement(NotifyCollectionOfStatementDTO request) {
        // 1.判断账单应收金额是否发生变化
        SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());

        // 2.更新已通知金额
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        supplierStatementUpdate.setId(request.getStatementId());
        supplierStatementUpdate.setUnconfirmedReceivedAmt(supplierStatementPO.getUnconfirmedReceivedAmt().add(request.getAmt()));
        supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);

        // 3.创建工单
        NotifyCollectionDTO notifyCollectionDTO = new NotifyCollectionDTO();
        BeanUtils.copyProperties(request, notifyCollectionDTO);
        notifyCollectionDTO.setCollectionAmt(request.getAmt());
        notifyCollectionDTO.setPhotoList(request.getPhotoList());
        notifyCollectionDTO.setBusinessType(BusinessTypeEnum.SUPPLIERSTATEMENT.key);
        if (Objects.equals(request.getStatementType(), StatementTypeEnum.REWARD_AMT.key)) {
            notifyCollectionDTO.setBusinessType(BusinessTypeEnum.SUPPLIER_REWARD_STATEMENT.key);
        } else if (Objects.equals(request.getStatementType(), StatementTypeEnum.REBATE_AMT.key)) {
            notifyCollectionDTO.setBusinessType(BusinessTypeEnum.SUPPLIER_REBATE_STATEMENT.key);
        }
        notifyCollectionDTO.setNotifyItemDTOList(Collections.singletonList(new NotifyItemDTO(
                supplierStatementPO.getStatementCode(),
                request.getAmt()
        )));
        notifyCollectionDTO.setOrgCode(supplierStatementPO.getSupplierCode());
        notifyCollectionDTO.setOrgName(supplierStatementPO.getSupplierName());
        notifyCollectionDTO.setCompanyCode(supplierStatementPO.getCompanyCode());
        notifyCollectionDTO.setContent("账单款");
        notifyCollectionDTO.setCurrency(supplierStatementPO.getCurrency());
        notifyCollectionDTO.setPhotoList(request.getPhotoList());
        notifyCollectionDTO.setCreatedBy(request.getOperator());
        notifyCollectionDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        financeNofityService.notifyCollection(notifyCollectionDTO);
    }

    @Transactional
    @Override
    public Integer notifyPaymentOfStatement(NotifyPaymentOfStatementDTO request) {
        SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(request.getStatementId());

        // 2.更新已通知金额
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        supplierStatementUpdate.setId(request.getStatementId());
        supplierStatementUpdate.setUnconfirmedPaidAmt(supplierStatementPO.getUnconfirmedPaidAmt().add(request.getAmt()));
        supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);

        // 3.创建工单
        NotifyPaymentDTO notifyPaymentDTO = new NotifyPaymentDTO();
        BeanUtils.copyProperties(request, notifyPaymentDTO);
        notifyPaymentDTO.setBusinessType(BusinessTypeEnum.SUPPLIERSTATEMENT.key);
        if (Objects.equals(request.getStatementType(), StatementTypeEnum.REWARD_AMT.key)) {
            notifyPaymentDTO.setBusinessType(BusinessTypeEnum.SUPPLIER_REWARD_STATEMENT.key);
        } else if (Objects.equals(request.getStatementType(), StatementTypeEnum.REBATE_AMT.key)) {
            notifyPaymentDTO.setBusinessType(BusinessTypeEnum.SUPPLIER_REBATE_STATEMENT.key);
        }
        notifyPaymentDTO.setNotifyItemDTOList(Collections.singletonList(new NotifyItemDTO(
                supplierStatementPO.getStatementCode(),
                request.getAmt()
        )));
        notifyPaymentDTO.setPaymentAmt(request.getAmt());
        notifyPaymentDTO.setOrgCode(supplierStatementPO.getSupplierCode());
        notifyPaymentDTO.setOrgName(supplierStatementPO.getSupplierName());
        notifyPaymentDTO.setCompanyCode(supplierStatementPO.getCompanyCode());
        notifyPaymentDTO.setContent("账单款");
        notifyPaymentDTO.setCurrency(supplierStatementPO.getCurrency());
        notifyPaymentDTO.setCreatedBy(request.getOperator());
        notifyPaymentDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        notifyPaymentDTO.setChangeTime(request.getChangeTime());
        return financeNofityService.notifyPayment(notifyPaymentDTO);
    }

    @Transactional
    @Override
    public void modifyStatementName(ModifyStatementNameDTO request) {
        // 1.更新账单名称
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        supplierStatementUpdate.setId(request.getStatementId());
        supplierStatementUpdate.setStatementName(request.getStatementName());
        supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);
    }

    @Transactional
    @Override
    public void modifySettlementDate(ModifySettlementDateDTO request) {
        // 1.更新账单结算日期
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        supplierStatementUpdate.setId(request.getStatementId());
        supplierStatementUpdate.setSettlementDate(DateUtilX.stringToDate(request.getSettlementDate()));
        supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);
    }

    @Override
    public void createSettleWorkOrder(CreateSettleWorkOrderDTO request) {
        // 任务已存在，跳过
        SettleWorkOrderPO settleWorkOrderPO = settleWorkOrderService.querySettleWorkOrder(request.getSettleTaskCode());
        if (settleWorkOrderPO != null) {
            return;
        }

        //添加记账工单任务
        SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
        //任务编码生成
        String key = SystemCodeEnum.SUPPLIERSETTLEWORKORDERCODE.code;
        String code = RedisTemplateX.lRightPop(key);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", key);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(key);
        }
        settleWorkOrderDTO.setTaskCode(code);
        settleWorkOrderDTO.setSettleJson(JSON.toJSONString(request));
        settleWorkOrderDTO.setSettleTaskCode(request.getSettleTaskCode());
        settleWorkOrderDTO.setStatementCreateStatus(0);
        settleWorkOrderDTO.setStatementConfirmStatus(0);
        settleWorkOrderDTO.setWorkOrderStatus(0);
        settleWorkOrderDTO.setTaskStatus(TaskStatusEnum.BILL_CREATE.getCode());
        settleWorkOrderDTO.setCreatedDt(new Date());
        settleWorkOrderDTO.setSupplierCode(request.getSupplierCode());
        settleWorkOrderService.insertSettleWorkOrder(settleWorkOrderDTO);

        //未开始记账任务,放入缓存
        RedisTemplateX.hPut(RedisKey.SETTLE_WORK_ORDER_WAIT, settleWorkOrderDTO.getSettleTaskCode(), "1");

        //供货单强制锁
        FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
        financeLockSupplyOrderDTO.setSupplyOrderList(request.getSupplyOrderCodeList());
        financeLockSupplyOrderDTO.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        financeLockSupplyOrderDTO.setLockStatus(2);
        financeLockService.lockSupplyOrderList(financeLockSupplyOrderDTO);
    }

    @Override
    public void startSettleWorkOrder(CreateSettleWorkOrderDTO request) {
        if (RedisTemplateX.hExists(RedisKey.SETTLE_WORK_ORDER_WAIT, request.getSettleTaskCode())) {
            RedisTemplateX.hDelete(RedisKey.SETTLE_WORK_ORDER_WAIT, request.getSettleTaskCode());
            //开始任务，第一步创建账单
            List<String> list = new ArrayList<>();
            list.add(request.getSettleTaskCode());
            RedisTemplateX.lLeftPushAll(RedisKey.SETTLE_WORK_ORDER_CREATE_STATEMENT, list);
            //更新交易时间
            settleWorkOrderService.updateSettleWorkOrderChangeTime(request);
        }
    }

    @Override
    public void cancelSettleWorkOrder(CancelSettleWorkOrderDTO request) {
        // 清理缓存
        RedisTemplateX.hDelete(RedisKey.SETTLE_WORK_ORDER_WAIT, request.getSettleTaskCode());

        SettleWorkOrderPO workOrder = settleWorkOrderService.querySettleWorkOrder(request.getSettleTaskCode());
        CreateSettleWorkOrderDTO createSettleWorkOrderDTO = JSON.parseObject(workOrder.getSettleJson(), CreateSettleWorkOrderDTO.class);

        //删除数据库数据
        settleWorkOrderService.delCancelSettleWorkOrder(request.getSettleTaskCode());

        //供货单解锁
        FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
        financeLockSupplyOrderDTO.setSupplyOrderList(createSettleWorkOrderDTO.getSupplyOrderCodeList());
        financeLockSupplyOrderDTO.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        financeLockSupplyOrderDTO.setLockStatus(0);
        financeLockService.lockSupplyOrderList(financeLockSupplyOrderDTO);
    }

    @Override
    public List<SupplierOrderRefundDTO> querySupplierOrderRefundList(QuerySupplierOrderRefundDTO request) {
        return supplierStatementMapper.querySupplierOrderRefundList(request);
    }

    /**
     * 更新账单金额
     */
    private void updateStatementAmount(StatementIdDTO statementIdDTO) {
        QueryStatementTotalAmountDTO queryStatementTotalAmountDTO = supplierStatementOrderMapper.queryStatementAmount(statementIdDTO);
        if (queryStatementTotalAmountDTO == null) {
            queryStatementTotalAmountDTO = new QueryStatementTotalAmountDTO();
            queryStatementTotalAmountDTO.setAmount("0");
            queryStatementTotalAmountDTO.setStatementId(statementIdDTO.getStatementId());
        }
        supplierStatementOrderMapper.updateStatementAmount(queryStatementTotalAmountDTO);
    }

    /**
     * 异步创建插入账单
     * @Transactional
     */
    public void asyncCreatBill(CreateSupplierStatementDTO request) {
        // 异步处理
        asyncCreatBill.execute(new Runnable() {
            @Override
            public void run() {
                TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
                long millis = System.currentTimeMillis();
                SupplierStatementPO supplierStatementPO = new SupplierStatementPO();
                try {
                    try {
                        // 1.创建账单
                        BeanUtils.copyProperties(request, supplierStatementPO);
                        supplierStatementPO.setStatementStatus(StatementStatusEnum.UN_CHECK.key);
                        supplierStatementPO.setStatementAmt(BigDecimal.ZERO);
                        supplierStatementPO.setPaidAmt(BigDecimal.ZERO);
                        supplierStatementPO.setUnpaidAmt(BigDecimal.ZERO);
                        supplierStatementPO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
                        supplierStatementPO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
                        supplierStatementPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
                        supplierStatementPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
                        supplierStatementPO.setSettlementStatus(0);
                        supplierStatementPO.setCreatedBy(request.getOperator());
                        supplierStatementPO.setCreatedDt(new Date());
                        supplierStatementPO.setSettlementDate(DateUtilX.stringToDate(request.getSettlementDate()));

                        //获取编码
                        String key = SystemCodeEnum.SUPPLIERSTATEMENTCODE.code;
                        if (Objects.equals(request.getStatementType(), StatementTypeEnum.REWARD_AMT.key)) {
                            key = SystemCodeEnum.SUPPLIERREWARDSTATEMENTCODE.code;
                        } else if (Objects.equals(request.getStatementType(), StatementTypeEnum.REBATE_AMT.key)) {
                            key = SystemCodeEnum.SUPPLIERREBATESTATEMENTCODE.code;
                        }
                        String code = RedisTemplateX.lRightPop(key);
                        if (null == code) {
                            Map<String, String> requestMap = new HashMap<>();
                            requestMap.put("seqName", key);
                            sequenceRemote.createCode(requestMap);
                            code = RedisTemplateX.lRightPop(key);
                        }
                        supplierStatementPO.setStatementCode(code);

                        //获取供应商信息
//                        SupplierAddDTO supplierAdd = StrUtilX.parseObject(RedisTemplateX.hGetAll(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplierStatementPO.getSupplierCode()), SupplierAddDTO.class);
//                        BigDecimal rate = BigDecimal.ONE;
//                        if (supplierAdd != null) {
//                            rate = exchangeRateRemote.getRateToOrgCurrency(supplierAdd.getSettlementCurrency(), CompanyDTO.COMPANY_CODE);
//                        }
//                        supplierStatementPO.setRate(rate);
                        supplierStatementMapper.insert(supplierStatementPO);
                    } catch (Exception e) {
                        log.error("创建供应商账单表失败", e);
                        throw new RuntimeException("创建供应商账单表失败");
                    }
                    // 2.保存账单明细
                    try {
                        InsertStatementSupplyOrderDTO insertStatementSupplyOrderDTO = new InsertStatementSupplyOrderDTO();
                        BeanUtils.copyProperties(request, insertStatementSupplyOrderDTO);
                        insertStatementSupplyOrderDTO.setStatementId(supplierStatementPO.getId());
                        //supplierStatementOrderMapper.saveBatchStatementOrder(insertStatementSupplyOrderDTO);
                        // 查询账单明细订单ID，批量插入到账单
                        int pageSize = 500;
                        int pageNum = 1;
                        while (true) {
                            Page<StatementOrderPO> page = new Page<>(pageNum, pageSize);
                            List<StatementOrderPO> statementOrders = supplierStatementOrderMapper.getStatementOrder(insertStatementSupplyOrderDTO, page);
                            ++pageNum;
                            supplierStatementOrderMapper.saveBatchStatementOrder2(statementOrders);
                            if (statementOrders.size() < pageSize - 1) {
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("添加账供应商单明细失败", e);
                        throw new RuntimeException("添加供应商账单明细失败");
                    }
                    try {
                        // 3.更新账单金额
                        StatementIdDTO statementIdDTO = new StatementIdDTO();
                        statementIdDTO.setStatementId(supplierStatementPO.getId());
                        updateStatementAmount(statementIdDTO);
                    } catch (Exception e) {
                        log.error("更新供应商账单金额失败", e);
                        throw new RuntimeException("更新供应商账单金额失败");
                    }
                    try {
                        // 4.更新订单对账状态为出账中
                        UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
                        updateSupplyOrderFinanceDTO.setStatementId(supplierStatementPO.getId());
                        updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
                        updateSupplyOrderFinanceDTO.setFinanceLockStatus(1);
                        updateSupplyOrderFinanceDTO.setFinanceType(request.getStatementType());
                        supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
                    } catch (Exception e) {
                        log.error("更新订单对账状态失败", e);
                        throw new RuntimeException("更新订单对账状态失败");
                    }
                    try {
                        supplierStatementPO = supplierStatementMapper.selectByPrimaryKey(supplierStatementPO.getId());
                        //再创建账单时自动为账单里的订单加锁
                        QueryStatementSupplyOrderListPageDTO queryStatementSupplyOrderListPageDTO = new QueryStatementSupplyOrderListPageDTO();
                        queryStatementSupplyOrderListPageDTO.setStatementId(supplierStatementPO.getId());
                        queryStatementSupplyOrderListPageDTO.setPageSize(batchQueryCount);
                        Integer indexNum = 0;
                        while (true) {
                            queryStatementSupplyOrderListPageDTO.setCurrentPage(indexNum * batchQueryCount);
                            List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderListPage(queryStatementSupplyOrderListPageDTO);
                            if (CollUtilX.isEmpty(list)) {
                                break;
                            } else {
                                for (StatementSupplyOrderDTO statementSupplyOrderDTO : list) {
                                    FinanceLockSupplyOrderDTO financeLockSupplyOrderDTO = new FinanceLockSupplyOrderDTO();
                                    Example example = new Example(SupplierStatementOrderPO.class);
                                    example.createCriteria().andEqualTo("supplyOrderCode", statementSupplyOrderDTO.getSupplyOrderCode())
                                            .andEqualTo("statementId", supplierStatementPO.getId());
                                    SupplierStatementOrderPO supplierStatementOrderPO = supplierStatementOrderMapper.selectOneByExample(example);
                                    financeLockSupplyOrderDTO.setSupplyOrderId(supplierStatementOrderPO.getSupplyOrderId());
                                    financeLockSupplyOrderDTO.setLockStatus(0);
                                    financeLockSupplyOrderDTO.setFinanceType(request.getStatementType());
                                    financeLockService.lockSupplyOrder(financeLockSupplyOrderDTO);
                                }
                            }
                            indexNum++;
                        }
                        // 创建成功设置供应商redis的出账状态为未出账
                        OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                        stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
                        RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency(), JSONObject.toJSONString(stateDTO));
                    } catch (Exception e) {
                        log.error("再创建账单时自动为账单里的订单加锁失败", e);
                        throw new RuntimeException("再创建账单时自动为账单里的订单加锁失败");
                    }
                    log.error("创建供应商账单成功，耗时 {}", System.currentTimeMillis() - millis);
                    transactionManager.commit(transaction);
                } catch (Exception e) {
                    transactionManager.rollback(transaction);
                    log.error("创建供应商账单失败，原因： {} ,耗时 {}", e.getMessage(), System.currentTimeMillis() - millis);
                    // 创建失败设置供应商redis的出账状态为出账失败
                    OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                    stateDTO.setCheckInDate(request.getStartDate());
                    stateDTO.setCheckOutDate(request.getEndDate());
                    stateDTO.setOutgoingState(OutgoingStateEnum.CONFIRMED.key);
                    stateDTO.setFailureReason(e.getMessage());
                    RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency(), JSONObject.toJSONString(stateDTO));
                }
            }
        });
    }

    /**
     * 自助结算异步创建插入账单
     */
    public void creatSettleBill(CreateSupplierStatementDTO request) {
        TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
        long millis = System.currentTimeMillis();
        SupplierStatementPO supplierStatementPO = new SupplierStatementPO();
        try {
            try {
                // 1.创建账单
                BeanUtils.copyProperties(request, supplierStatementPO);
                supplierStatementPO.setStatementStatus(StatementStatusEnum.UN_CHECK.key);
                supplierStatementPO.setStatementAmt(BigDecimal.ZERO);
                supplierStatementPO.setPaidAmt(BigDecimal.ZERO);
                supplierStatementPO.setUnpaidAmt(BigDecimal.ZERO);
                supplierStatementPO.setUnconfirmedPaidAmt(BigDecimal.ZERO);
                supplierStatementPO.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
                supplierStatementPO.setSettlementStatus(0);
                supplierStatementPO.setCreatedBy(request.getOperator());
                supplierStatementPO.setCreatedDt(new Date());
                supplierStatementPO.setSettlementDate(DateUtilX.stringToDate(request.getSettlementDate()));
                supplierStatementPO.setStatementLabel(request.getStatementLabel());

                //获取编码
                String key = SystemCodeEnum.SUPPLIERSTATEMENTCODE.code;
                String code = RedisTemplateX.lRightPop(key);
                if (null == code) {
                    Map<String, String> requestMap = new HashMap<>();
                    requestMap.put("seqName", key);
                    sequenceRemote.createCode(requestMap);
                    code = RedisTemplateX.lRightPop(key);
                }
                supplierStatementPO.setStatementCode(code);
                supplierStatementMapper.insert(supplierStatementPO);
            } catch (Exception e) {
                log.error("创建供应商账单表失败", e);
                throw new RuntimeException("创建供应商账单表失败");
            }
            // 2.保存账单明细
            try {
                InsertStatementSupplyOrderDTO insertStatementSupplyOrderDTO = new InsertStatementSupplyOrderDTO();
                BeanUtils.copyProperties(request, insertStatementSupplyOrderDTO);
                insertStatementSupplyOrderDTO.setStatementId(supplierStatementPO.getId());
                // 查询账单明细供货单，批量插入到账单
                List<StatementOrderPO> statementOrders = supplierStatementOrderMapper.getStatementOrderByCodes(insertStatementSupplyOrderDTO);
                supplierStatementOrderMapper.saveBatchStatementOrder2(statementOrders);
            } catch (Exception e) {
                log.error("添加账供应商单明细失败", e);
                throw new RuntimeException("添加供应商账单明细失败");
            }
            try {
                // 3.更新账单金额
                StatementIdDTO statementIdDTO = new StatementIdDTO();
                statementIdDTO.setStatementId(supplierStatementPO.getId());
                updateStatementAmount(statementIdDTO);
            } catch (Exception e) {
                log.error("更新供应商账单金额失败", e);
                throw new RuntimeException("更新供应商账单金额失败");
            }
            try {
                // 4.更新订单对账状态为出账中
                UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
                updateSupplyOrderFinanceDTO.setStatementId(supplierStatementPO.getId());
                updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
                updateSupplyOrderFinanceDTO.setFinanceLockStatus(2);
                updateSupplyOrderFinanceDTO.setFinanceType(request.getStatementType());
                supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
            } catch (Exception e) {
                log.error("更新订单对账状态失败", e);
                throw new RuntimeException("更新订单对账状态失败");
            }
            try {
                // 创建成功设置供应商redis的出账状态为未出账
                OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
                RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency(), JSONObject.toJSONString(stateDTO));
            } catch (Exception e) {
                log.error("再创建账单时自动为账单里的订单加锁失败", e);
                throw new RuntimeException("再创建账单时自动为账单里的订单加锁失败");
            }
            log.error("创建供应商账单成功，耗时 {}", System.currentTimeMillis() - millis);
            //自助结算记账工单任务：进入确认账单
            if (StrUtilX.isNotEmpty(request.getSettleTaskCode())) {
                SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
                settleWorkOrderDTO.setSettleTaskCode(request.getSettleTaskCode());
                settleWorkOrderDTO.setTaskStatus(TaskStatusEnum.BILL_CONFIRM.getCode());
                settleWorkOrderDTO.setStatementId(supplierStatementPO.getId());
                settleWorkOrderDTO.setStatementCode(supplierStatementPO.getStatementCode());
                settleWorkOrderService.updateSettleWorkOrder(settleWorkOrderDTO);
            }
            transactionManager.commit(transaction);
        } catch (Exception e) {
            transactionManager.rollback(transaction);
            log.error("创建供应商账单失败，原因： {} ,耗时 {}", e.getMessage(), System.currentTimeMillis() - millis);
            // 创建失败设置供应商redis的出账状态为出账失败
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setCheckInDate(request.getStartDate());
            stateDTO.setCheckOutDate(request.getEndDate());
            stateDTO.setOutgoingState(OutgoingStateEnum.CONFIRMED.key);
            stateDTO.setFailureReason(e.getMessage());
            RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency(), JSONObject.toJSONString(stateDTO));
            if (StrUtilX.isNotEmpty(request.getSettleTaskCode())) {
                //创建失败，通知自助结算，并更新状态
                SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
                settleWorkOrderDTO.setSettleTaskCode(request.getSettleTaskCode());
                settleWorkOrderDTO.setFailReason(e.getMessage());
                settleWorkOrderDTO.setTaskStatus(TaskStatusEnum.BILL_CREATE.getCode());
                settleWorkOrderService.updateSettleWorkOrder(settleWorkOrderDTO);
            }
        }
    }


    /**
     * 取消订单，异步处理账单明细
     */
    public void deleteStatementOrder(ModifyStatementStatusDTO request) {
        TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        try {
            supplierStatementUpdate.setId(request.getStatementId());

            UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
            updateSupplyOrderFinanceDTO.setStatementId(request.getStatementId());
            updateSupplyOrderFinanceDTO.setFinanceType(request.getStatementType());

            if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
                supplierStatementUpdate.setUpdatedBy(request.getOperator());
                supplierStatementUpdate.setUpdatedDt(new Date());

                //查询该账单下面的所有订单
                QueryStatementSupplyOrderListDTO statementOrder = new QueryStatementSupplyOrderListDTO();
                statementOrder.setStatementId(request.getStatementId());

                // 查询账单明细订单ID，批量插入到账单
                int pageSize = 300;
                int pageNum = 1;
                while (true) {
                    PageHelper.startPage(pageNum, pageSize);
                    List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderList(statementOrder);
                    PageInfo<StatementSupplyOrderDTO> pageInfo = new PageInfo<>(list);
                    //++pageNum;
                    //相当于将所有的订单明细删除
                    if (CollUtilX.isNotEmpty(pageInfo.getList())) {
                        DeleteStatementOrderListDTO deleteStatementOrderListDTO = new DeleteStatementOrderListDTO();
                        deleteStatementOrderListDTO.setStatementId(request.getStatementId());
                        deleteStatementOrderListDTO.setStatementType(request.getStatementType());
                        List<Integer> orderList = new ArrayList<Integer>();
                        for (StatementSupplyOrderDTO statementSupplyOrderDTO : pageInfo.getList()) {
                            orderList.add(statementSupplyOrderDTO.getStatementOrderId());
                        }
                        deleteStatementOrderListDTO.setStatementOrderIdList(orderList);
                        deleteStatementOrderListDTO.setCurrency(request.getCurrency());
                        deleteStatementOrderList(deleteStatementOrderListDTO);
                    }
                    if (pageInfo.getSize() < pageSize - 1) {
                        break;
                    }
                }
                updateSupplyOrderFinanceDTO.setFinanceLockStatus(0);
                supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);
            }
            // 更新账单状态
            supplierStatementUpdate.setStatementStatus(request.getStatementStatus());
            supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);

            // 修改取消中状态
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            RedisTemplateX.hPut(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));
            transactionManager.commit(transaction);
        } catch (Exception e) {
            // 更新缓存状态
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            RedisTemplateX.hPut(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));
            log.error("删除供应商账单异常", e);
            transactionManager.rollback(transaction);
        }
    }
}
