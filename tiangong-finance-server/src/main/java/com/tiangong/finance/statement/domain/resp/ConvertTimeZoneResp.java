package com.tiangong.finance.statement.domain.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:28
 * @Description:
 */

@Data
public class ConvertTimeZoneResp implements Serializable {

    /**
     * 出账时间窗口: 系统开始时间 (HH:mm:ss)
     */
    private String systemStartTime;

    /**
     * 出账时间窗口: 系统结束时间 (HH:mm:ss)
     */
    private String systemEndTime;

    /**
     * 系统配置的时区
     */
    private String systemTimezone;

}
