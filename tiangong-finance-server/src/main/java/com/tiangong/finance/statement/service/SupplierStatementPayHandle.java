package com.tiangong.finance.statement.service;

import com.tiangong.finance.statement.dto.CancelStatementWorkOrderDTO;
import com.tiangong.finance.statement.dto.ConfirmStatementWorkOrderDTO;

public interface SupplierStatementPayHandle {

    /**
     * 确认工单
     */
    void confirmStatementWorkOrder(ConfirmStatementWorkOrderDTO request);

    /**
     * 取消工单
     */
    void cancelStatementWorkOrder(CancelStatementWorkOrderDTO request);
}
