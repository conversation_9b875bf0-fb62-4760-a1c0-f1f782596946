package com.tiangong.finance.statement.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.finance.dto.QueryAllSupplierSupplyOrderCurrencyDTO;
import com.tiangong.finance.dto.QueryAutoBillConfigsPageTaskDTO;
import com.tiangong.finance.statement.domain.NonVccAutoBillConfigPO;

import java.util.List;

import com.tiangong.finance.statement.domain.req.NonVccAutoBillConfigPageReq;
import com.tiangong.finance.statement.domain.resp.NonVccAutoBillConfigPageResp;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:39
 * @Description:
 */
public interface NonVccAutoBillConfigMapper extends MyMapper<NonVccAutoBillConfigPO> {

    List<NonVccAutoBillConfigPageResp> nonVccAutoBillConfigPage(NonVccAutoBillConfigPageReq nonVccAutoBillConfigPageReq);

    /**
     * 校验供应商编码是否存在
     * @param orgCode
     * @return
     */
    int checkSupplierCodeExists(@Param("orgCode") String orgCode);

    List<Integer> queryAllSupplierSupplyOrderCurrency(QueryAllSupplierSupplyOrderCurrencyDTO queryAllSupplierSupplyOrderCurrencyDTO);

    List<QueryAutoBillConfigsPageTaskDTO> queryAutoBillConfigsPage(@Param("billingRule") Integer billingRule, @Param("currentTime") String currentTime, @Param("offset") Integer offset, @Param("limit") Integer limit);

}
