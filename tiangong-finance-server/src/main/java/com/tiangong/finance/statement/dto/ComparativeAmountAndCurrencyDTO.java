package com.tiangong.finance.statement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/5/25 9:33
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComparativeAmountAndCurrencyDTO implements Serializable {

    private BigDecimal comparativeAmount;

    private String currency;

}
