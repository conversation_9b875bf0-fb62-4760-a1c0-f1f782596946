package com.tiangong.finance.statement.server;

import com.alibaba.fastjson.JSONObject;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.OutgoingStateEnum;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.*;
import com.tiangong.finance.statement.dto.OutgoingStateDTO;
import com.tiangong.finance.statement.service.AgentStatementService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

@RestController
@Slf4j
public class AgentStatementServer extends BaseController {

    @Autowired
    private AgentStatementService agentStatementService;

    /**
     * 已出账单查询
     */
    @PostMapping("/finance/agent/queryStatementList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<AgentStatementListResponseDTO>> queryStatementList(@RequestBody QueryAgentStatementListDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        return Response.success(agentStatementService.queryStatementList(request));
    }

    /**
     * 未出账查询
     */
    @PostMapping("/finance/agent/queryUncheckOutAgentList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<UncheckOutAgentDTO>> queryUncheckOutAgentList(@RequestBody QueryUncheckOutAgentListDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        return Response.success(agentStatementService.queryUncheckOutAgentList(request));
    }

    /**
     * 创建账单
     */
    @PostMapping("/finance/agent/createStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> createStatement(@RequestBody CreateAgentStatementDTO request) throws ParseException {
        request.setCompanyCode(super.getCompanyCode());
        request.setOperator(super.getUserName());
        agentStatementService.createStatement(request);
        return Response.success();
    }

    /**
     * 账单详情
     */
    @PostMapping("/finance/agent/queryStatementDetail")
    @AnonymousAccess
    public Response<AgentStatementDetailDTO> queryStatementDetail(@RequestBody StatementIdDTO request) {
        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(super.getCompanyCode());
        }
        return Response.success(agentStatementService.queryStatementDetail(request));
    }

    /**
     * 账单明细
     */
    @PostMapping("/finance/agent/queryStatementOrderList")
    @PreAuthorize("@syyo.check('common')")
    public Response<PaginationSupportDTO<StatementOrderDTO>> queryStatementOrderList(@RequestBody QueryStatementOrderListDTO request) {
        return Response.success(agentStatementService.queryStatementOrderList(request));
    }

    /**
     * 未出账订单查询
     */
    @PostMapping("/finance/agent/queryUnCheckOutOrder")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<UnCheckOutOrderDTO>> queryUnCheckOutOrder(@RequestBody QueryUnCheckOutOrderDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        return Response.success(agentStatementService.queryUnCheckOutOrder(request));
    }

    /**
     * 添加账单明细
     */
    @PostMapping("/finance/agent/addStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> addStatementOrderList(@RequestBody AddStatementOrderListDTO request) {
        request.setOperator(super.getUserName());
        request.setCompanyCode(super.getCompanyCode());
        agentStatementService.addStatementOrderList(request);
        return Response.success();
    }

    /**
     * 删除账单明细
     */
    @PostMapping("/finance/agent/deleteStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> deleteStatementOrderList(@RequestBody DeleteStatementOrderListDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_BEING_CANCELLED);
            }
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()), OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_IN_PROGRESS);
        }
        request.setOperator(super.getUserName());
        agentStatementService.deleteStatementOrderList(request);
        return Response.success();
    }

    /**
     * 修改账单状态
     */
    @PostMapping("/finance/agent/modifyStatementStatus")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> modifyStatementStatus(@RequestBody ModifyStatementStatusDTO request) {
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()), OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_IN_PROGRESS);
        }
        request.setOperator(super.getUserName());
        agentStatementService.modifyStatementStatus(request);
        return Response.success();
    }

    /**
     * 取消账单
     */
    @PostMapping("/finance/agent/canceStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> cancelStatement(@RequestBody ModifyStatementStatusDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_BEING_CANCELLED);
            }
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()), OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_IN_PROGRESS);
        }
        request.setOperator(super.getUserName());
        agentStatementService.cancelStatement(request);
        return Response.success();
    }

    /**
     * 账单明细变更查询
     */
    @PostMapping("/finance/agent/queryUpdatedStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<UpdatedStatementOrderDTO>> queryUpdatedStatementOrderList(@RequestBody QueryUpdatedStatementOrderListDTO request) {
        return Response.success(agentStatementService.queryUpdatedStatementOrderList(request));
    }

    /**
     * 更新账单明细
     */
    @PostMapping("/finance/agent/updateStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> updateStatementOrderList(@RequestBody StatementIdDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_BEING_CANCELLED);
            }
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()), OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_IN_PROGRESS);
        }
        request.setOperator(super.getUserName());
        agentStatementService.updateStatementOrderList(request);
        return Response.success();
    }

    /**
     * 修改账单中订单的要收金额
     */
    @PostMapping("/finance/agent/updateStatementOrderReceiveAmt")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> updateStatementOrderReceiveAmt(@RequestBody ModifySettlementOrderReceiveAmtDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_BEING_CANCELLED);
            }
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()), OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_IN_PROGRESS);
        }
        request.setOperator(super.getUserName());
        agentStatementService.updateStatementOrderReceiveAmt(request);
        return Response.success();
    }

    /**
     * 通知收款
     */
    @PostMapping("/finance/agent/notifyCollectionOfStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> notifyCollectionOfStatement(@RequestBody NotifyCollectionOfStatementDTO request) {
        request.setOperator(super.getUserName());
        agentStatementService.notifyCollectionOfStatement(request);
        return Response.success();
    }

    /**
     * 通知付款
     */
    @PostMapping("/finance/agent/notifyPaymentOfStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> notifyPaymentOfStatement(@RequestBody NotifyPaymentOfStatementDTO request) {
        request.setOperator(super.getUserName());
        agentStatementService.notifyPaymentOfStatement(request);
        return Response.success();
    }

    /**
     * 修改账单名称
     */
    @PostMapping("/finance/agent/modifyStatementName")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> modifyStatementName(@RequestBody ModifyStatementNameDTO request) {
        request.setOperator(super.getUserName());
        agentStatementService.modifyStatementName(request);
        return Response.success();
    }

    /**
     * 修改结算日期
     */
    @PostMapping("/finance/agent/modifySettlementDate")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> modifySettlementDate(@RequestBody ModifySettlementDateDTO request) {
        request.setOperator(super.getUserName());
        agentStatementService.modifySettlementDate(request);
        return Response.success();
    }

    /**
     * 删除客户账单失败信息
     */
    @PostMapping("/finance/agent/delOutgoing")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> delOutgoing(@RequestBody QueryUnCheckOutOrderDTO request) {
        OutgoingStateDTO stateDTO = new OutgoingStateDTO();
        stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
        RedisTemplateX.hPut(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode(), JSONObject.toJSONString(stateDTO));
        return Response.success();
    }

    /**
     * 查询客户账单信息
     */
    @PostMapping("/finance/agent/getOutgoing")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> getOutgoing(@RequestBody QueryUnCheckOutOrderDTO request) {
        return Response.success(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()));
    }


    /**
     * 查询客户账单取消状态
     */
    @PostMapping("/finance/agent/getCancelState")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> getCancelState(@RequestBody QueryUnCheckOutOrderDTO request) {
        Object object = RedisTemplateX.hashGet(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId());
        if (object != null) {
            return Response.success(object);
        } else {
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            RedisTemplateX.hPut(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));
        }
        return Response.success();
    }

    /**
     * 创建订单记账任务
     */
    @AnonymousAccess
    @PostMapping("/finance/agent/createOrderStatementTask")
    public Response<Object> createOrderStatementTask(@RequestBody CreateOrderStatementTaskDTO request) {
        agentStatementService.createOrderStatementTask(request);
        return Response.success();
    }

    /**
     * 订单附加项补汇差
     */
    @PostMapping("/finance/agent/repairOrderAdditionalCharges")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> repairOrderAdditionalCharges(@RequestBody ModifyStatementStatusDTO request) {
        request.setOperator(super.getUserName());
        agentStatementService.repairOrderAdditionalCharges(request);
        return Response.success();
    }

    /**
     * 校验客户结算成本异常
     */
    @PostMapping("/finance/agent/checkAgentSettlementCostError")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Integer> checkAgentSettlementCostError(@RequestBody ModifyStatementStatusDTO request) {
        return Response.success(agentStatementService.checkAgentSettlementCostError(request));
    }
}
