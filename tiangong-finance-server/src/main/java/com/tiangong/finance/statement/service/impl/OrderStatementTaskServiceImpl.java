package com.tiangong.finance.statement.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.OrderStatementTaskResponseDTO;
import com.tiangong.finance.remote.workorder.request.ConfirmWorkOrderDTO;
import com.tiangong.finance.enums.TaskStatusEnum;
import com.tiangong.finance.statement.domain.OrderStatementTaskPO;
import com.tiangong.finance.statement.dto.OrderStatementTaskDTO;
import com.tiangong.finance.statement.mapper.OrderStatementTaskMapper;
import com.tiangong.finance.statement.service.AgentStatementService;
import com.tiangong.finance.statement.service.OrderStatementTaskService;
import com.tiangong.finance.workorder.service.WorkOrderService;
import com.tiangong.keys.RedisKey;
import com.tiangong.keys.OrderConstantKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OrderStatementTaskServiceImpl implements OrderStatementTaskService {

    /**
     * 重试任务状态 - 账单创建
     */
    private static final String RETRY_BILL_CREATE = "0";

    /**
     * 重试任务状态 - 账单确认
     */
    private static final String RETRY_BILL_CONFIRM = "1";

    /**
     * 重试任务状态 - 工单处理
     */
    private static final String RETRY_WORK_ORDER_PROCESS = "2";

    @Autowired
    private OrderStatementTaskMapper orderStatementTaskMapper;

    @Autowired
    private AgentStatementService agentStatementService;

    @Autowired
    private WorkOrderService workOrderService;

    @Override
    public void insertOrderStatementTask(OrderStatementTaskDTO orderStatementTaskDTO) {
        OrderStatementTaskPO orderStatementTaskPO = new OrderStatementTaskPO();
        BeanUtils.copyProperties(orderStatementTaskDTO, orderStatementTaskPO);
        orderStatementTaskMapper.insert(orderStatementTaskPO);
    }

    @Override
    public PaginationSupportDTO<OrderStatementTaskResponseDTO> queryOrderStatementTaskPage(QueryOrderStatementTaskPageDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<OrderStatementTaskResponseDTO> settleWorkOrderList = orderStatementTaskMapper.queryOrderStatementTaskPage(request);
        PageInfo<OrderStatementTaskResponseDTO> page = new PageInfo<>(settleWorkOrderList);
        PaginationSupportDTO<OrderStatementTaskResponseDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(settleWorkOrderList);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public void retryOrderStatementTask(RetryOrderStatementTaskDTO request) {
        if (RedisTemplateX.hashGet(RedisKey.ORDER_STATEMENT_TASK_FAIL, request.getTaskCode()) == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        String info = RedisTemplateX.hashGet(RedisKey.ORDER_STATEMENT_TASK_FAIL, request.getTaskCode()).toString();
        RedisTemplateX.hDelete(RedisKey.ORDER_STATEMENT_TASK_FAIL, request.getTaskCode());
        List<String> list = new ArrayList<>();
        list.add(request.getTaskCode());
        OrderStatementTaskPO orderStatementTaskPO = new OrderStatementTaskPO();
        switch (info) {
            case RETRY_BILL_CREATE:
                orderStatementTaskPO.setStatementCreateStatus(0);
                orderStatementTaskPO.setStatementConfirmStatus(3);
                orderStatementTaskPO.setWorkOrderStatus(3);
                RedisTemplateX.lLeftPushAll(RedisKey.ORDER_STATEMENT_TASK_CREATE_STATEMENT, list);
                break;
            case RETRY_BILL_CONFIRM:
                orderStatementTaskPO.setStatementConfirmStatus(0);
                orderStatementTaskPO.setWorkOrderStatus(3);
                RedisTemplateX.lLeftPushAll(RedisKey.ORDER_STATEMENT_TASK_CONFIRM_STATEMENT, list);
                break;
            case RETRY_WORK_ORDER_PROCESS:
                orderStatementTaskPO.setWorkOrderStatus(0);
                RedisTemplateX.lLeftPushAll(RedisKey.ORDER_STATEMENT_TASK_PROCESS_WORK_ORDER, list);
                break;
            default:
                break;
        }
        orderStatementTaskPO.setTaskCode(request.getTaskCode());
        orderStatementTaskMapper.updateOrderStatementTask(orderStatementTaskPO);
    }

    @Override
    public OrderStatementTaskPO queryOrderStatementTask(String taskCode) {
        OrderStatementTaskPO orderStatementTaskPO = new OrderStatementTaskPO();
        orderStatementTaskPO.setTaskCode(taskCode);
        return orderStatementTaskMapper.selectOne(orderStatementTaskPO);
    }

    @Override
    public void updateOrderStatementTask(OrderStatementTaskDTO orderStatementTaskDTO) {
        String taskCode = orderStatementTaskDTO.getTaskCode();
        OrderStatementTaskPO workOrder = queryOrderStatementTask(taskCode);

        //成功
        if (StrUtilX.isEmpty(orderStatementTaskDTO.getFailReason())) {
            List<String> list = new ArrayList<>();
            list.add(taskCode);
            //执行成功，进入下一步
            if (TaskStatusEnum.BILL_CONFIRM.getCode().equals(orderStatementTaskDTO.getTaskStatus())) {
                workOrder.setStatementCreateStatus(1);
                workOrder.setStatementConfirmStatus(0);
                workOrder.setWorkOrderStatus(0);
                workOrder.setStatementId(orderStatementTaskDTO.getStatementId());
                workOrder.setStatementCode(orderStatementTaskDTO.getStatementCode());
                workOrder.setChangeTime(orderStatementTaskDTO.getChangeTime());
                RedisTemplateX.lLeftPushAll(RedisKey.ORDER_STATEMENT_TASK_CONFIRM_STATEMENT, list);
            } else if (TaskStatusEnum.WORK_ORDER_PROCESS.getCode().equals(orderStatementTaskDTO.getTaskStatus())) {
                workOrder.setStatementConfirmStatus(1);
                workOrder.setWorkOrderStatus(0);
                RedisTemplateX.lLeftPushAll(RedisKey.ORDER_STATEMENT_TASK_PROCESS_WORK_ORDER, list);
            } else if (TaskStatusEnum.COMPLETED.getCode().equals(orderStatementTaskDTO.getTaskStatus())) {
                workOrder.setWorkOrderStatus(1);
            }
            orderStatementTaskDTO.setUpdatedBy(OrderConstantKey.PERSON_PAY_OPERATOR);
            if (workOrder.getTaskSource() == 1){
                orderStatementTaskDTO.setUpdatedBy(OrderConstantKey.PERSON_REFUND_OPERATOR);
            }
            orderStatementTaskMapper.updateOrderStatementTask(workOrder);
            return;
        }

        //失败,更新状态
        workOrder.setFailReason(orderStatementTaskDTO.getFailReason());
        if (TaskStatusEnum.BILL_CREATE.getCode().equals(orderStatementTaskDTO.getTaskStatus())) {
            workOrder.setStatementCreateStatus(2);
            workOrder.setStatementConfirmStatus(3);
            workOrder.setWorkOrderStatus(3);
        } else if (TaskStatusEnum.BILL_CONFIRM.getCode().equals(orderStatementTaskDTO.getTaskStatus())) {
            workOrder.setStatementConfirmStatus(2);
            workOrder.setWorkOrderStatus(3);
        } else if (TaskStatusEnum.WORK_ORDER_PROCESS.getCode().equals(orderStatementTaskDTO.getTaskStatus())) {
            workOrder.setWorkOrderStatus(2);
        }
        orderStatementTaskDTO.setUpdatedBy(OrderConstantKey.PERSON_PAY_OPERATOR);
        if (workOrder.getTaskSource() == 1){
            orderStatementTaskDTO.setUpdatedBy(OrderConstantKey.PERSON_REFUND_OPERATOR);
        }
        orderStatementTaskMapper.updateOrderStatementTask(workOrder);

        //失败结果
        RedisTemplateX.hPut(RedisKey.ORDER_STATEMENT_TASK_FAIL, taskCode, String.valueOf(orderStatementTaskDTO.getTaskStatus()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void settleProcessWorkOrder(String taskCode) {
        OrderStatementTaskPO orderStatementTaskPO = queryOrderStatementTask(taskCode);
        CreateOrderStatementTaskDTO createOrderStatementTaskDTO = JSON.parseObject(orderStatementTaskPO.getTaskJson(), CreateOrderStatementTaskDTO.class);

        Integer id;
        try {
            // 第三方支付 - 通知收款
            if (createOrderStatementTaskDTO.getTaskSource() == 0){
                NotifyCollectionOfStatementDTO statementDTO = new NotifyCollectionOfStatementDTO();
                statementDTO.setStatementId(orderStatementTaskPO.getStatementId());
                statementDTO.setPaymentType(2);
                statementDTO.setAmt(createOrderStatementTaskDTO.getReceiveAmt());
                statementDTO.setPaySerialNo(createOrderStatementTaskDTO.getSerialNo());
                statementDTO.setPayer(createOrderStatementTaskDTO.getPayer());
                statementDTO.setChangeTime(createOrderStatementTaskDTO.getChangeTime());
                statementDTO.setOperator(OrderConstantKey.PERSON_PAY_OPERATOR);
                id = agentStatementService.notifyCollectionOfStatement(statementDTO);
            } else {
                // 第三方退款 - 通知付款
                NotifyPaymentOfStatementDTO statementDTO = new NotifyPaymentOfStatementDTO();
                statementDTO.setStatementId(orderStatementTaskPO.getStatementId());
                statementDTO.setPaymentType(2);
                statementDTO.setAmt(createOrderStatementTaskDTO.getReceiveAmt());
                statementDTO.setPaySerialNo(createOrderStatementTaskDTO.getSerialNo());
                statementDTO.setReceiver(createOrderStatementTaskDTO.getReceiver());
                statementDTO.setChangeTime(createOrderStatementTaskDTO.getChangeTime());
                statementDTO.setOperator(OrderConstantKey.PERSON_REFUND_OPERATOR);
                id = agentStatementService.notifyPaymentOfStatement(statementDTO);
            }

        } catch (SysException e) {
            // 处理记账失败，更新状态
            OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
            orderStatementTaskDTO.setTaskCode(taskCode);
            orderStatementTaskDTO.setFailReason(e.getMessage());
            orderStatementTaskDTO.setTaskStatus(TaskStatusEnum.WORK_ORDER_PROCESS.getCode());
            updateOrderStatementTask(orderStatementTaskDTO);
            return;
        } catch (Exception e) {
            log.error("处理记账异常", e);
            // 处理记账失败，更新状态
            OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
            orderStatementTaskDTO.setTaskCode(taskCode);
            orderStatementTaskDTO.setFailReason("系统异常");
            orderStatementTaskDTO.setTaskStatus(TaskStatusEnum.WORK_ORDER_PROCESS.getCode());
            updateOrderStatementTask(orderStatementTaskDTO);
            return;
        }

        // 2.确认记账
        try {
            ConfirmWorkOrderDTO confirmWorkOrderDTO = new ConfirmWorkOrderDTO();
            confirmWorkOrderDTO.setWorkOrderId(id);
            confirmWorkOrderDTO.setAmt(createOrderStatementTaskDTO.getReceiveAmt());
            confirmWorkOrderDTO.setPayer(createOrderStatementTaskDTO.getPayer());
            confirmWorkOrderDTO.setReceiver(createOrderStatementTaskDTO.getReceiver());
            confirmWorkOrderDTO.setOperator(OrderConstantKey.PERSON_PAY_OPERATOR);
            // 支付取账单创建时间，退款取下单时间
            confirmWorkOrderDTO.setChangeTime(DateUtilX.dateToString(orderStatementTaskPO.getChangeTime(), DateUtilX.hour_format));
            if (orderStatementTaskPO.getTaskSource() == 1){
                confirmWorkOrderDTO.setChangeTime(createOrderStatementTaskDTO.getChangeTime());
                confirmWorkOrderDTO.setOperator(OrderConstantKey.PERSON_REFUND_OPERATOR);
            }
            confirmWorkOrderDTO.setIsCollection(false);
            if (createOrderStatementTaskDTO.getTaskSource() == 0){
                confirmWorkOrderDTO.setIsCollection(true);
            }
            confirmWorkOrderDTO.setPaymentType(2);
            workOrderService.confirmWorkOrder(confirmWorkOrderDTO);
        } catch (Exception e) {
            log.error("确认记账异常", e);
            // 处理记账失败，更新状态
            OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
            orderStatementTaskDTO.setTaskCode(taskCode);
            orderStatementTaskDTO.setFailReason("系统异常");
            orderStatementTaskDTO.setTaskStatus(TaskStatusEnum.WORK_ORDER_PROCESS.getCode());
            updateOrderStatementTask(orderStatementTaskDTO);
            return;
        }

        // 3.处理记账成功，更新状态
        OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
        orderStatementTaskDTO.setTaskCode(taskCode);
        orderStatementTaskDTO.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
        orderStatementTaskDTO.setStatementCode(orderStatementTaskPO.getStatementCode());
        updateOrderStatementTask(orderStatementTaskDTO);

        // 当前任务成功之后开启此订单下一个账单任务
        if (RedisTemplateX.hasKey(RedisKey.ORDER_STATEMENT_TASK_WAIT + createOrderStatementTaskDTO.getOrderCode())) {
            String nextTaskCode = RedisTemplateX.sPop(RedisKey.ORDER_STATEMENT_TASK_WAIT + createOrderStatementTaskDTO.getOrderCode());
            //开始任务，第一步创建账单
            List<String> list = new ArrayList<>();
            list.add(nextTaskCode);
            RedisTemplateX.lLeftPushAll(RedisKey.ORDER_STATEMENT_TASK_CREATE_STATEMENT, list);
        } else {
            RedisTemplateX.sRemove(RedisKey.ORDER_STATEMENT_TASK_ING, createOrderStatementTaskDTO.getOrderCode());
        }
    }

    @Override
    public String selectOneByStatementCode(String statementCode) {
        return orderStatementTaskMapper.selectOneByStatementCode(statementCode);
    }

}
