package com.tiangong.finance.statement.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "f_agent_statement_order")
public class AgentStatementOrderPO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 账单id
     */
    @Column(name = "statement_id")
    private Integer statementId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Integer orderId;

    /**
     * 订单号
     */
    @Column(name = "order_code")
    private String orderCode;

    /**
     * 酒店名称
     */
    @Column(name = "hotel_name")
    private String hotelName;

    /**
     * 房型名称
     */
    @Column(name = "room_name")
    private String roomName;

    /**
     * 价格计划名称
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 入住人
     */
    private String guest;

    /**
     * 开始日期
     */
    @Column(name = "start_date")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 间数
     */
    @Column(name = "room_qty")
    private Integer roomQty;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 应收
     */
    @Column(name = "receivable_amt")
    private BigDecimal receivableAmt;

    /**
     * 已收
     */
    @Column(name = "received_amt")
    private BigDecimal receivedAmt;

    /**
     * 未收
     */
    @Column(name = "unreceived_amt")
    private BigDecimal unreceivedAmt;

    /**
     * 要收
     */
    @Column(name = "receive_amt")
    private BigDecimal receiveAmt;

    /**
     * 订单创建时间
     */
    @Column(name = "order_create_date")
    private Date orderCreateDate;

    /**
     * 确认状态：0待确认，1已确认，2已取消
     */
    @Column(name = "confirmation_status")
    private Integer confirmationStatus;

    /**
     * 刷卡金额
     */
    @Column(name = "swiping_card_amount")
    private BigDecimal swipingCardAmount;

    /**
     * 刷卡金额币种
     */
    @Column(name = "swiping_card_amount_currency")
    private Integer swipingCardAmountCurrency;

    /**
     * 刷卡汇率
     */
    @Column(name = "swiping_card_rate")
    private BigDecimal swipingCardRate;

    /**
     * 刷卡时间
     */
    @Column(name = "swiping_card_dt")
    private Date swipingCardDt;

    /**
     * 结算金额
     */
    @Column(name = "settle_amount")
    private BigDecimal settleAmount;

    /**
     * 结算金额币种
     */
    @Column(name = "settle_amount_currency")
    private Integer settleAmountCurrency;

    /**
     * 收款汇率(结算金额与成本收款金额的汇率)
     */
    @Column(name = "collection_rate")
    private BigDecimal collectionRate;

    /**
     * 成本收款金额
     */
    @Column(name = "collection_amount")
    private BigDecimal collectionAmount;

    /**
     * 汇差
     */
    @Column(name = "rate_difference_value")
    private BigDecimal rateDifferenceValue;

    /**
     * 结算成本错误描述
     */
    @Column(name = "settlement_cost_error_desc")
    private String settlementCostErrorDesc;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(name = "created_dt")
    private Date createdDt;

    /**
     * 修改人
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @Column(name = "updated_dt")
    private Date updatedDt;
}