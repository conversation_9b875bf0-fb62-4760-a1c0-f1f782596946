package com.tiangong.finance.statement.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.StatementTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.remote.statement.request.CreateSettleWorkOrderDTO;
import com.tiangong.finance.remote.statement.request.NotifyPaymentOfStatementDTO;
import com.tiangong.finance.remote.statement.request.QuerySettleWorkOrderPageDTO;
import com.tiangong.finance.remote.statement.request.RetrySettleWorkOrderDTO;
import com.tiangong.finance.remote.statement.response.SettleWorkOrderPageResponseDTO;
import com.tiangong.finance.remote.workorder.request.ConfirmWorkOrderDTO;
import com.tiangong.finance.enums.TaskStatusEnum;
import com.tiangong.finance.statement.domain.SettleWorkOrderPO;
import com.tiangong.finance.statement.dto.SettleWorkOrderDTO;
import com.tiangong.finance.statement.mapper.SettleWorkOrderMapper;
import com.tiangong.finance.statement.service.SettleWorkOrderService;
import com.tiangong.finance.statement.service.SupplierStatementService;
import com.tiangong.finance.workorder.service.WorkOrderService;
import com.tiangong.keys.RedisKey;
import com.tiangong.keys.SettleConstantKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.settle.remote.SettleServerRemote;
import com.tiangong.settle.req.TgWorkOrderReq;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class SettleWorkOrderServiceImpl implements SettleWorkOrderService {

    /**
     * 重试任务状态 - 账单创建
     */
    private static final String RETRY_BILL_CREATE = "0";

    /**
     * 重试任务状态 - 账单确认
     */
    private static final String RETRY_BILL_CONFIRM = "1";

    /**
     * 重试任务状态 - 工单处理
     */
    private static final String RETRY_WORK_ORDER_PROCESS = "2";

    @Autowired
    private SettleWorkOrderMapper settleWorkOrderMapper;

    @Autowired
    private SettleServerRemote settleServerRemote;

    @Autowired
    private SupplierStatementService supplierStatementService;

    @Autowired
    private WorkOrderService workOrderService;

    @Override
    public void insertSettleWorkOrder(SettleWorkOrderDTO settleWorkOrderDTO) {
        SettleWorkOrderPO settleWorkOrderPO = new SettleWorkOrderPO();
        BeanUtils.copyProperties(settleWorkOrderDTO, settleWorkOrderPO);
        settleWorkOrderMapper.insert(settleWorkOrderPO);
    }

    @Override
    public void delCancelSettleWorkOrder(String settleTaskCode) {
        SettleWorkOrderPO settleWorkOrderPO = new SettleWorkOrderPO();
        settleWorkOrderPO.setSettleTaskCode(settleTaskCode);
        settleWorkOrderMapper.delete(settleWorkOrderPO);
    }

    @Override
    public PaginationSupportDTO<SettleWorkOrderPageResponseDTO> querySettleWorkOrderPage(QuerySettleWorkOrderPageDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<SettleWorkOrderPageResponseDTO> settleWorkOrderList = settleWorkOrderMapper.querySettleWorkOrderPage(request);
        PageInfo<SettleWorkOrderPageResponseDTO> page = new PageInfo<>(settleWorkOrderList);
        PaginationSupportDTO<SettleWorkOrderPageResponseDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(settleWorkOrderList);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public void retrySettleWorkOrder(RetrySettleWorkOrderDTO request) {
        if (RedisTemplateX.hashGet(RedisKey.SETTLE_WORK_ORDER_FAIL, request.getSettleTaskCode()) == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        String info = RedisTemplateX.hashGet(RedisKey.SETTLE_WORK_ORDER_FAIL, request.getSettleTaskCode()).toString();
        RedisTemplateX.hDelete(RedisKey.SETTLE_WORK_ORDER_FAIL, request.getSettleTaskCode());
        List<String> list = new ArrayList<>();
        list.add(request.getSettleTaskCode());
        SettleWorkOrderPO settleWorkOrderPO = new SettleWorkOrderPO();
        switch (info) {
            case RETRY_BILL_CREATE:
                settleWorkOrderPO.setStatementCreateStatus(0);
                settleWorkOrderPO.setStatementConfirmStatus(3);
                settleWorkOrderPO.setWorkOrderStatus(3);
                RedisTemplateX.lLeftPushAll(RedisKey.SETTLE_WORK_ORDER_CREATE_STATEMENT, list);
                break;
            case RETRY_BILL_CONFIRM:
                settleWorkOrderPO.setStatementConfirmStatus(0);
                settleWorkOrderPO.setWorkOrderStatus(3);
                RedisTemplateX.lLeftPushAll(RedisKey.SETTLE_WORK_ORDER_CONFIRM_STATEMENT, list);
                break;
            case RETRY_WORK_ORDER_PROCESS:
                settleWorkOrderPO.setWorkOrderStatus(0);
                RedisTemplateX.lLeftPushAll(RedisKey.SETTLE_WORK_ORDER_PROCESS_WORK_ORDER, list);
                break;
            default:
                break;
        }
        settleWorkOrderPO.setSettleTaskCode(request.getSettleTaskCode());
        settleWorkOrderMapper.updateSettleWorkOrder(settleWorkOrderPO);
    }

    @Override
    public SettleWorkOrderPO querySettleWorkOrder(String settleTaskCode) {
        SettleWorkOrderPO settleWorkOrderPO = new SettleWorkOrderPO();
        settleWorkOrderPO.setSettleTaskCode(settleTaskCode);
        return settleWorkOrderMapper.selectOne(settleWorkOrderPO);
    }

    @Override
    public void updateSettleWorkOrder(SettleWorkOrderDTO settleWorkOrderDTO) {
        String settleTaskCode = settleWorkOrderDTO.getSettleTaskCode();
        SettleWorkOrderPO workOrder = querySettleWorkOrder(settleTaskCode);

        //成功
        if (StrUtilX.isEmpty(settleWorkOrderDTO.getFailReason())) {
            List<String> list = new ArrayList<>();
            list.add(settleTaskCode);
            //执行成功，进入下一步
            if (TaskStatusEnum.BILL_CONFIRM.getCode().equals(settleWorkOrderDTO.getTaskStatus())) {
                workOrder.setStatementCreateStatus(1);
                workOrder.setStatementConfirmStatus(0);
                workOrder.setWorkOrderStatus(0);
                workOrder.setStatementId(settleWorkOrderDTO.getStatementId());
                workOrder.setStatementCode(settleWorkOrderDTO.getStatementCode());
                RedisTemplateX.lLeftPushAll(RedisKey.SETTLE_WORK_ORDER_CONFIRM_STATEMENT, list);
            } else if (TaskStatusEnum.WORK_ORDER_PROCESS.getCode().equals(settleWorkOrderDTO.getTaskStatus())) {
                workOrder.setStatementConfirmStatus(1);
                workOrder.setWorkOrderStatus(0);
                RedisTemplateX.lLeftPushAll(RedisKey.SETTLE_WORK_ORDER_PROCESS_WORK_ORDER, list);
            } else if (TaskStatusEnum.COMPLETED.getCode().equals(settleWorkOrderDTO.getTaskStatus())) {
                workOrder.setWorkOrderStatus(1);
                //通知自助结算结果
                TgWorkOrderReq req = new TgWorkOrderReq();
                req.setFlag(true);
                req.setTaskCode(settleTaskCode);
                req.setBillCode(workOrder.getStatementCode());
                settleServerRemote.processTgJobOrderStatus(req);
            }
            settleWorkOrderDTO.setUpdatedBy(SettleConstantKey.SETTLE_OPERATOR);
            settleWorkOrderMapper.updateSettleWorkOrder(workOrder);
            return;
        }

        //失败,更新状态
        workOrder.setFailReason(settleWorkOrderDTO.getFailReason());
        if (TaskStatusEnum.BILL_CREATE.getCode().equals(settleWorkOrderDTO.getTaskStatus())) {
            workOrder.setStatementCreateStatus(2);
            workOrder.setStatementConfirmStatus(3);
            workOrder.setWorkOrderStatus(3);
        } else if (TaskStatusEnum.BILL_CONFIRM.getCode().equals(settleWorkOrderDTO.getTaskStatus())) {
            workOrder.setStatementConfirmStatus(2);
            workOrder.setWorkOrderStatus(3);
        } else if (TaskStatusEnum.WORK_ORDER_PROCESS.getCode().equals(settleWorkOrderDTO.getTaskStatus())) {
            workOrder.setWorkOrderStatus(2);
        }
        settleWorkOrderDTO.setUpdatedBy(SettleConstantKey.SETTLE_OPERATOR);
        settleWorkOrderMapper.updateSettleWorkOrder(workOrder);

        //同步结果到自助结算任务
        TgWorkOrderReq req = new TgWorkOrderReq();
        req.setFlag(false);
        req.setTaskCode(settleTaskCode);
        req.setMsg(settleWorkOrderDTO.getFailReason());
        settleServerRemote.processTgJobOrderStatus(req);

        //失败结果
        RedisTemplateX.hPut(RedisKey.SETTLE_WORK_ORDER_FAIL, settleTaskCode, String.valueOf(settleWorkOrderDTO.getTaskStatus()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void settleProcessWorkOrder(String settleTaskCode) {
        SettleWorkOrderPO settleWorkOrderPO = querySettleWorkOrder(settleTaskCode);
        CreateSettleWorkOrderDTO createSettleWorkOrderDTO = JSON.parseObject(settleWorkOrderPO.getSettleJson(), CreateSettleWorkOrderDTO.class);
        // 1.通知付款
        NotifyPaymentOfStatementDTO statementDTO = new NotifyPaymentOfStatementDTO();
        statementDTO.setStatementId(settleWorkOrderPO.getStatementId());
        statementDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        statementDTO.setPaymentType(1);
        statementDTO.setRemark(createSettleWorkOrderDTO.getRemark());
        statementDTO.setAmt(createSettleWorkOrderDTO.getSupplyTotalAmt());
        statementDTO.setReceiver(createSettleWorkOrderDTO.getReceiver());
        statementDTO.setPayerId(createSettleWorkOrderDTO.getPayerId());
        statementDTO.setPayer(createSettleWorkOrderDTO.getPayer());
        statementDTO.setChangeTime(DateUtilX.dateToString(createSettleWorkOrderDTO.getChangeTime(), DateUtilX.hour_format));
        statementDTO.setOperator(SettleConstantKey.SETTLE_OPERATOR);
        Integer id;
        try {
            id = supplierStatementService.notifyPaymentOfStatement(statementDTO);
        } catch (SysException e) {
            // 处理工单失败，通知自助结算，并更新状态
            SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
            settleWorkOrderDTO.setSettleTaskCode(settleTaskCode);
            settleWorkOrderDTO.setFailReason(e.getMessage());
            settleWorkOrderDTO.setTaskStatus(TaskStatusEnum.WORK_ORDER_PROCESS.getCode());
            updateSettleWorkOrder(settleWorkOrderDTO);
            return;
        } catch (Exception e) {
            log.error("处理工单异常", e);
            // 处理工单失败，通知自助结算，并更新状态
            SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
            settleWorkOrderDTO.setSettleTaskCode(settleTaskCode);
            settleWorkOrderDTO.setFailReason("系统异常");
            settleWorkOrderDTO.setTaskStatus(TaskStatusEnum.WORK_ORDER_PROCESS.getCode());
            updateSettleWorkOrder(settleWorkOrderDTO);
            return;
        }

        // 2.确认工单
        try {
            ConfirmWorkOrderDTO confirmWorkOrderDTO = new ConfirmWorkOrderDTO();
            confirmWorkOrderDTO.setWorkOrderId(id);
            confirmWorkOrderDTO.setAmt(createSettleWorkOrderDTO.getSupplyTotalAmt());
            confirmWorkOrderDTO.setReceiver(createSettleWorkOrderDTO.getReceiver());
            confirmWorkOrderDTO.setPayerId(createSettleWorkOrderDTO.getPayerId());
            confirmWorkOrderDTO.setChangeTime(DateUtilX.dateToString(createSettleWorkOrderDTO.getChangeTime(), DateUtilX.hour_format));
            confirmWorkOrderDTO.setOperator(SettleConstantKey.SETTLE_OPERATOR);
            confirmWorkOrderDTO.setIsCollection(false);
            confirmWorkOrderDTO.setPaymentType(1);
            workOrderService.confirmWorkOrder(confirmWorkOrderDTO);
        } catch (Exception e) {
            log.error("确认工单异常", e);
            // 处理工单失败，通知自助结算，并更新状态
            SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
            settleWorkOrderDTO.setSettleTaskCode(settleTaskCode);
            settleWorkOrderDTO.setFailReason("系统异常");
            settleWorkOrderDTO.setTaskStatus(TaskStatusEnum.WORK_ORDER_PROCESS.getCode());
            updateSettleWorkOrder(settleWorkOrderDTO);
            return;
        }

        // 3.处理工单成功，更新状态
        SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
        settleWorkOrderDTO.setSettleTaskCode(settleTaskCode);
        settleWorkOrderDTO.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
        settleWorkOrderDTO.setStatementCode(settleWorkOrderPO.getStatementCode());
        updateSettleWorkOrder(settleWorkOrderDTO);
    }

    @Override
    public void updateSettleWorkOrderChangeTime(CreateSettleWorkOrderDTO request) {
        SettleWorkOrderPO workOrder = querySettleWorkOrder(request.getSettleTaskCode());
        CreateSettleWorkOrderDTO createSettleWorkOrderDTO = JSON.parseObject(workOrder.getSettleJson(), CreateSettleWorkOrderDTO.class);
        createSettleWorkOrderDTO.setChangeTime(request.getChangeTime());
        createSettleWorkOrderDTO.setRemark(request.getRemark());
        workOrder.setSettleJson(JSON.toJSONString(createSettleWorkOrderDTO));
        settleWorkOrderMapper.updateByPrimaryKey(workOrder);
    }

    @Override
    public String selectOneByStatementCode(String statementCode) {
        return settleWorkOrderMapper.selectOneByStatementCode(statementCode);
    }
}
