package com.tiangong.finance.statement.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.dto.common.MyMapper;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.StatementOrderDTO;
import com.tiangong.finance.remote.statement.response.UnCheckOutOrderDTO;
import com.tiangong.finance.remote.statement.response.UpdatedStatementOrderDTO;
import com.tiangong.finance.statement.domain.AgentStatementOrderPO;
import com.tiangong.finance.statement.domain.StatementOrderPO;
import com.tiangong.finance.statement.dto.InsertStatementOrderDTO;
import com.tiangong.finance.statement.dto.UpdateOrderFinanceDTO;

import java.math.BigDecimal;
import java.util.List;

public interface AgentStatementOrderMapper extends MyMapper<AgentStatementOrderPO> {

    /**
     * 批量保存账单明细
     *
     * @param insertStatementOrderDTO 插入账单订单DTO，包含要保存的账单明细信息
     * @return 返回保存的记录数
     */
    Integer saveBatchStatementOrder(InsertStatementOrderDTO insertStatementOrderDTO);

    /**
     * 自动批量保存账单明细
     *
     * @param insertStatementOrderDTO 插入账单订单DTO，包含要自动保存的账单明细信息
     * @return 返回保存的记录数
     */
    Integer autoSaveBatchStatementOrder(InsertStatementOrderDTO insertStatementOrderDTO);

    /**
     * 获取账单明细
     *
     * @param list 插入账单订单DTO，包含查询条件
     * @param page 分页参数，包含分页信息
     * @return 返回账单订单持久化对象列表
     */
    List<StatementOrderPO> getStatementOrder(InsertStatementOrderDTO list, Page page);

    /**
     * 更新账单明细
     *
     * @param request 账单ID DTO，包含账单标识信息
     * @return 返回更新的记录数
     */
    Integer updateStatementOrderList(StatementIdDTO request);

    /**
     * 查询账单明细
     *
     * @param request 查询账单订单列表DTO，包含查询条件
     * @return 返回账单订单DTO列表
     */
    List<StatementOrderDTO> queryStatementOrderList(QueryStatementOrderListDTO request);

    /**
     * 更新账单金额
     *
     * @param request 查询账单总金额DTO，包含账单金额更新信息
     * @return 返回更新的记录数
     */
    Integer updateStatementAmount(QueryStatementTotalAmountDTO request);

    /**
     * 查询订单更新后的账单金额
     *
     * @param request 账单ID DTO，包含账单标识信息
     * @return 返回更新后的账单金额
     */
    BigDecimal queryNewStatementAmount(StatementIdDTO request);

    /**
     * 查询账单中更新订单数
     *
     * @param request 账单ID DTO，包含账单标识信息
     * @return 返回更新的订单数量
     */
    int queryStatementUpdateOrder(StatementIdDTO request);

    /**
     * 更新订单对账状态
     *
     * @param request 更新订单财务DTO，包含对账状态更新信息
     * @return 返回更新的记录数
     */
    Integer updateOrderFinance(UpdateOrderFinanceDTO request);

    /**
     * 未出账订单查询
     *
     * @param request 查询未出账订单DTO，包含查询条件
     * @return 返回未出账订单DTO列表
     */
    List<UnCheckOutOrderDTO> queryUnCheckOutOrder(QueryUnCheckOutOrderDTO request);

    /**
     * 账单明细变更查询
     *
     * @param request 查询更新账单订单列表DTO，包含查询条件
     * @return 返回更新账单订单DTO列表
     */
    List<UpdatedStatementOrderDTO> queryUpdatedStatementOrderList(QueryUpdatedStatementOrderListDTO request);

    /**
     * 查询当前订单下的金额总数
     *
     * @param request
     * @return
     */
    QueryStatementTotalAmountDTO queryStatementTotalAmount(StatementIdDTO request);

    /**
     * 批量删除
     *
     * @param list
     */
    void deleteAgentStatementOrderList(List<Integer> list);

    /**
     * 通过id查询订单id
     * @param list
     * @return
     */
    List<Integer> selectAgentStatementOrderListByOrderId(List<Integer> list);

    /**
     * 批量保存账单明细(不用select)
     *
     * @param statementOrders 账单订单持久化对象列表，包含要保存的账单明细信息
     */
    void saveBatchStatementOrder2(List<StatementOrderPO> statementOrders);

    /**
     * 更新订单对账状态(复制updateOrderFinance的，减少查询)
     *
     * @param request 更新订单财务DTO，包含对账状态更新信息
     * @return 返回更新的记录数
     */
    Integer updateOrderFinance2(UpdateOrderFinanceDTO request);

    /**
     * 根据订单id批量查询订单编码
     * @param list
     * @return
     */
    List<String> queryOrderCodeByOrderId(List<Integer> list);

    /**
     * 跟进供货单id批量查询供货单编码
     * @param list
     * @return
     */
    List<String> querySupplyOrderCodeByOrderId(List<Integer> list);

    /**
     * 根据账单订单id修改账单中订单的要收金额
     * @param request
     */
    void updateStatementOrderReceiveAmtById(ModifySettlementOrderReceiveAmtDTO request);

    /**
     * 根据订单编码查询账单订单
     * @param insertStatementOrderDTO
     * @return
     */
    List<StatementOrderPO> getStatementOrderByOrderCode(InsertStatementOrderDTO insertStatementOrderDTO);

    /**
     * 根据账单编码查询账单订单
     *
     * @param statementCodeDTO 账单编码DTO，包含账单编码查询条件
     * @return 返回代理账单订单持久化对象列表
     */
    List<AgentStatementOrderPO> selectStatementOrderByStatementCode(StatementCodeDTO statementCodeDTO);
}