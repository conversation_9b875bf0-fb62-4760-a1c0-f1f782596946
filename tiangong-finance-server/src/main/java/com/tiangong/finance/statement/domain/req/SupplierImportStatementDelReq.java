package com.tiangong.finance.statement.domain.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/31 17:31
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SupplierImportStatementDelReq implements Serializable {

    /**
     * 导入的供应商账单id
     */
    private Integer supplierImportStatementId;

}
