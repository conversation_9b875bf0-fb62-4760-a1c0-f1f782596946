package com.tiangong.finance.statement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.statement.domain.StatementAmtLogPO;
import com.tiangong.finance.statement.dto.StatementAmtLogDTO;
import com.tiangong.finance.statement.dto.StatementAmtLogListReq;

public interface StatementAmtLogService extends IService<StatementAmtLogPO> {

    /**
     * 新增账单金额日志
     */
    void insertStatementAmtLog(StatementAmtLogDTO dto);

    /**
     * 查询账单金额日志列表
     */
    PaginationSupportDTO<StatementAmtLogPO> statementAmtLogList(StatementAmtLogListReq req);
}
