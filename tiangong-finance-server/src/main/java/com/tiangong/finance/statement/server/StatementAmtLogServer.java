package com.tiangong.finance.statement.server;

import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.statement.domain.StatementAmtLogPO;
import com.tiangong.finance.statement.dto.StatementAmtLogListReq;
import com.tiangong.finance.statement.service.StatementAmtLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/finance")
public class StatementAmtLogServer {

    @Resource
    private StatementAmtLogService statementAmtLogService;

    /**
     * 查询账单金额日志列表
     */
    @PostMapping("/statementAmtLogList")
    public Response<PaginationSupportDTO<StatementAmtLogPO>> statementAmtLogList(@Validated @RequestBody StatementAmtLogListReq req){
        return Response.success(statementAmtLogService.statementAmtLogList(req));
    }
}
