package com.tiangong.finance.statement.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.tiangong.common.Response;
import com.tiangong.config.SettingsConstant;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.enums.DateQueryTypeEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.enums.StatementTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.convert.DTOConvert;
import com.tiangong.finance.dto.QueryAllSupplierSupplyOrderCurrencyDTO;
import com.tiangong.finance.dto.QueryAutoBillConfigsPageTaskDTO;
import com.tiangong.finance.enums.BillingRuleEnum;
import com.tiangong.finance.enums.OutgoingStateEnum;
import com.tiangong.finance.remote.statement.request.AddStatementSupplyOrderListDTO;
import com.tiangong.finance.remote.statement.request.CreateSupplierStatementDTO;
import com.tiangong.finance.remote.statement.request.QuerySupplierStatementListDTO;
import com.tiangong.finance.remote.statement.request.QueryUnCheckOutSupplyOrderDTO;
import com.tiangong.finance.remote.statement.response.UnCheckOutSupplyOrderDTO;
import com.tiangong.finance.statement.domain.NonVccAutoBillConfigPO;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.ConvertTimeZoneResp;
import com.tiangong.finance.statement.domain.resp.GetNonVccAutoBillConfigDetailResp;
import com.tiangong.finance.statement.domain.resp.NonVccAutoBillConfigPageResp;
import com.tiangong.finance.statement.dto.*;
import com.tiangong.finance.statement.mapper.NonVccAutoBillConfigMapper;
import com.tiangong.finance.statement.mapper.SupplierStatementOrderMapper;
import com.tiangong.finance.statement.server.SupplierStatementServer;
import com.tiangong.finance.statement.service.NonVccAutoBillConfigService;
import com.tiangong.finance.statement.service.SupplierStatementService;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import com.tiangong.common.StrPool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:32
 * @Description:
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class NonVccAutoBillConfigServiceImpl implements NonVccAutoBillConfigService {

    private final NonVccAutoBillConfigMapper nonVccAutoBillConfigMapper;

    private final HotelRemote hotelRemote;

    private final SettingsConstant settingsConstant;

    private final SupplierStatementService supplierStatementService;

    private final SupplierStatementServer supplierStatementServer;

    private final SupplierStatementOrderMapper supplierStatementOrderMapper;

    /**
     * 最大循环次数
     */
    private static final int MAX_LOOP_COUNT = 9999999;

    /**
     * 半月出账的16号
     */
    private static final Integer SEMI_MONTHLY_MIDDLE_DAY = 16;

    /**
     * 金额转换倍数
     */
    private static final Integer AMOUNT_CONVERSION_FACTOR = 100;

    /**
     * 保存或更新非VCC自动出账配置
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateNonVccAutoBillConfig(SaveOrUpdateNonVccAutoBillConfigReq request) {

        // 1. 校验request的必填参数
        if (request.getSupplierCode() == null || request.getHotelId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 如果付款链接发送邮箱不为空，则把中文逗号替换成英文逗号
        if (StrUtil.isNotBlank(request.getPaymentLinkEmails()) && request.getPaymentLinkEmails().contains(StrPool.CHINESE_COMMA)) {
            request.setPaymentLinkEmails(request.getPaymentLinkEmails().replace(StrPool.CHINESE_COMMA, StrPool.COMMA));
        }

        // 2. 校验供应商编码
        validateSupplierCode(request.getSupplierCode());

        // 3. 校验酒店ID
        validateHotelId(request.getHotelId(), request.getLanguage());

        // 4. 重复性校验
        checkNonVccAutoBillConfigExists(request);

        // 5. 计算系统时间
        // 解析时间,当hotelLocalStartTime和hotelLocalEndTime的时小于10时，需要补0，如8:00需要补08:00:00，否则LocalTime.parse()解析会报错
        request.setHotelLocalStartTime(splicingTime(request.getHotelLocalStartTime()));
        request.setHotelLocalEndTime(splicingTime(request.getHotelLocalEndTime()));
        LocalTime[] systemTimes = convertToSystemTime(
                request.getHotelLocalStartTime(),
                request.getHotelLocalEndTime(),
                request.getHotelLocalTimezone(),
                settingsConstant.getTimezone());

        // 6. 保存或更新数据
        NonVccAutoBillConfigPO configPO = DTOConvert.INSTANCE.nonVccAutoBillConfigPOConvert(request);
        configPO.setSystemStartTime(systemTimes[0]);
        configPO.setSystemEndTime(systemTimes[1]);

        if (request.getId() == null) {
            configPO.setCreatedBy(request.getCreatedBy());
            configPO.setCreatedDt(new Date());
            configPO.setUpdatedBy(request.getUpdatedBy());
            configPO.setUpdatedDt(new Date());
            nonVccAutoBillConfigMapper.insert(configPO);
        } else {
            configPO.setUpdatedBy(request.getUpdatedBy());
            configPO.setUpdatedDt(new Date());
            // 使用自定义更新方法，确保指定字段的空值也能覆盖老数据
            updateNonVccAutoBillConfigWithNullValues(configPO);
        }
    }

    /**
     * 若该供应商下面的这个酒店目前还有未完成的自动出账任务，无法删除自动出账酒店配置
     *
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteNonVccAutoBillConfig(DeleteNonVccAutoBillConfigReq request) {
        // 1.校验request的必填参数
        if (request == null || request.getId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 2.先根据id查询出该配置
        NonVccAutoBillConfigPO configPO = nonVccAutoBillConfigMapper.selectByPrimaryKey(request.getId());
        if (configPO == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 3.根据configPO的supplierCode和hotelId查询o_supply_order表对应的所有币种
        QueryAllSupplierSupplyOrderCurrencyDTO query = new QueryAllSupplierSupplyOrderCurrencyDTO();
        query.setSupplierCode(configPO.getSupplierCode());
        query.setHotelId(configPO.getHotelId());
        List<Integer> allCurrency = nonVccAutoBillConfigMapper.queryAllSupplierSupplyOrderCurrency(query);
        // 4.查询出账状态
        Object supplierAccountState = null;
        if (CollUtil.isNotEmpty(allCurrency)) {
            // 4.1 遍历所有币种，查询出账状态
            for (Integer currency : allCurrency) {
                supplierAccountState = RedisTemplateX.hashGet(
                        RedisKey.SUPPLY_ACCOUNT_STATE + StatementTypeEnum.SUPPLY_ORDER_AMT.key,
                        configPO.getSupplierCode() + "_" + currency);
                if (supplierAccountState != null) {
                    break;
                }
            }
        }
        // 4.2 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + StatementTypeEnum.SUPPLY_ORDER_AMT.key, configPO.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && outgoingStateDTO.getOutgoingState() != null
                && outgoingStateDTO.getOutgoingState().equals(OutgoingStateEnum.CHECKING.key)) {
            throw new SysException(ErrorCodeEnum.SUPPLIER_HAS_AN_OUTOFACCOUNT_STATUS);
        }
        // 5.该供应商下面的酒店目前没有未完成的自动出账任务，可以删除自动出账酒店配置
        nonVccAutoBillConfigMapper.deleteByPrimaryKey(request.getId());
    }

    /**
     * 查询非VCC自动出账配置详情
     *
     * @param request
     * @return
     */
    @Override
    public GetNonVccAutoBillConfigDetailResp getNonVccAutoBillConfigDetail(GetNonVccAutoBillConfigDetailReq request) {
        // 1.校验request的必填参数
        if (request == null || request.getId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 2.查询配置
        NonVccAutoBillConfigPO configPO = nonVccAutoBillConfigMapper.selectByPrimaryKey(request.getId());
        if (configPO == null) {
            return null;
        }
        return DTOConvert.INSTANCE.nonVccAutoBillConfigListDTOConvert(configPO);
    }

    /**
     * 分页查询非VCC自动出账配置
     *
     * @param request
     * @return
     */
    @Override
    public PaginationSupportDTO<NonVccAutoBillConfigPageResp> nonVccAutoBillConfigPage(NonVccAutoBillConfigPageReq request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<NonVccAutoBillConfigPageResp> list = nonVccAutoBillConfigMapper.nonVccAutoBillConfigPage(request);
        PageInfo<NonVccAutoBillConfigPageResp> page = new PageInfo<>(list);

        PaginationSupportDTO<NonVccAutoBillConfigPageResp> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());

        return paginationSupport;
    }

    /**
     * 时间转换
     *
     * @param request
     * @return
     */
    @Override
    public ConvertTimeZoneResp convertTimeZone(ConvertTimeZoneReq request) {
        request.setHotelLocalStartTime(splicingTime(request.getHotelLocalStartTime()));
        request.setHotelLocalEndTime(splicingTime(request.getHotelLocalEndTime()));
        LocalTime[] systemTimes = convertToSystemTime(
                request.getHotelLocalStartTime(),
                request.getHotelLocalEndTime(),
                request.getHotelLocalTimezone(),
                settingsConstant.getTimezone());

        ConvertTimeZoneResp response = new ConvertTimeZoneResp();
        response.setSystemStartTime(systemTimes[0].toString());
        response.setSystemEndTime(systemTimes[1].toString());
        response.setSystemTimezone(settingsConstant.getTimezone());

        return response;
    }

    /**
     * 校验供应商编码
     */
    private void validateSupplierCode(String supplierCode) {
        int count = nonVccAutoBillConfigMapper.checkSupplierCodeExists(supplierCode);
        if (count == 0) {
            throw new SysException(ErrorCodeEnum.SUPPLY_NOT_EXIST);
        }
    }

    /**
     * 校验酒店ID
     */
    private void validateHotelId(Long hotelId, String language) {
        Set<Long> hotelIdsSet = new HashSet<>();
        hotelIdsSet.add(hotelId.longValue());

        DestinationReq destinationReq = new DestinationReq();
        destinationReq.setLanguage(language);
        destinationReq.setDataSize(hotelIdsSet.size());
        destinationReq.setHotelIds(hotelIdsSet);

        Response<List<EsHotelDto>> esHotelDTOResponse = hotelRemote.searchDestinationHotel(destinationReq);
        if (esHotelDTOResponse.isError() || CollUtil.isEmpty(esHotelDTOResponse.getModel())) {
            throw new SysException(ErrorCodeEnum.NOT_EXIST_HOTEL);
        }
    }

    /**
     * 检查重复性
     */
    private void checkDuplicate(String supplierCode, Long hotelId, Long excludeId) {
        Example example = new Example(NonVccAutoBillConfigPO.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("supplierCode", supplierCode)
                .andEqualTo("hotelId", hotelId);

        if (excludeId != null) {
            criteria.andNotEqualTo("id", excludeId);
        }

        int count = nonVccAutoBillConfigMapper.selectCountByExample(example);
        if (count > 0) {
            // 此供应商下面已经存在此酒店的非VCC自动出账配置
            throw new SysException(ErrorCodeEnum.SUPPLIER_HOTEL_NON_VCC_AUTO_BILL_CONFIG_EXIST);
        }
    }

    /**
     * 更新非VCC自动出账配置，支持空值覆盖
     */
    private void updateNonVccAutoBillConfigWithNullValues(NonVccAutoBillConfigPO configPO) {
        // 先查询原有数据
        NonVccAutoBillConfigPO existingConfig = nonVccAutoBillConfigMapper.selectByPrimaryKey(configPO.getId());
        if (existingConfig == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 复制所有字段到现有配置
        existingConfig.setSupplierCode(configPO.getSupplierCode());
        existingConfig.setSupplierName(configPO.getSupplierName());
        existingConfig.setHotelId(configPO.getHotelId());
        existingConfig.setHotelName(configPO.getHotelName());
        existingConfig.setHotelLocalTimezone(configPO.getHotelLocalTimezone());
        existingConfig.setPaymentMethod(configPO.getPaymentMethod());
        existingConfig.setBillingRule(configPO.getBillingRule());
        existingConfig.setHotelLocalStartTime(configPO.getHotelLocalStartTime());
        existingConfig.setHotelLocalEndTime(configPO.getHotelLocalEndTime());
        existingConfig.setSystemStartTime(configPO.getSystemStartTime());
        existingConfig.setSystemEndTime(configPO.getSystemEndTime());
        existingConfig.setUpdatedBy(configPO.getUpdatedBy());
        existingConfig.setUpdatedDt(configPO.getUpdatedDt());

        // 对于需要支持空值覆盖的字段，直接设置（包括null值）
        existingConfig.setPaymentLinkEmails(configPO.getPaymentLinkEmails());
        existingConfig.setHotelPaymentContactPerson(configPO.getHotelPaymentContactPerson());
        existingConfig.setHotelCollectionBank(configPO.getHotelCollectionBank());
        existingConfig.setHotelCollectionAccountName(configPO.getHotelCollectionAccountName());
        existingConfig.setHotelCollectionAccountNumber(configPO.getHotelCollectionAccountNumber());
        existingConfig.setWorldFirstAccountNumber(configPO.getWorldFirstAccountNumber());
        existingConfig.setRemarks(configPO.getRemarks());
        existingConfig.setBillingDateType(configPO.getBillingDateType());
        existingConfig.setBillingDateDays(configPO.getBillingDateDays());

        // 使用updateByPrimaryKey确保所有字段都被更新
        nonVccAutoBillConfigMapper.updateByPrimaryKey(existingConfig);
    }

    /**
     * 转换酒店时间到系统时间
     */
    public static LocalTime[] convertToSystemTime(String hotelLocalStartTime, String hotelLocalEndTime,
                                                  String hotelLocalTimezone, String systemTimezone) {
        if (hotelLocalStartTime == null || hotelLocalEndTime == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        LocalTime startTime = LocalTime.parse(hotelLocalStartTime);
        LocalTime endTime = LocalTime.parse(hotelLocalEndTime);

        // 计算时差（系统时区 - 酒店时区）
        int hotelOffset = parseTimezoneOffset(hotelLocalTimezone);
        int systemOffset = parseTimezoneOffset(systemTimezone);
        int hourDiff = systemOffset - hotelOffset;

        // 转换时间
        LocalTime systemStartTime = startTime.plusHours(hourDiff);
        LocalTime systemEndTime = endTime.plusHours(hourDiff);

        return new LocalTime[]{systemStartTime, systemEndTime};
    }

    /**
     * 拼接时间
     *
     * @param time
     * @return
     */
    private static String splicingTime(String time) {
        final int minuteFormatLength=2;
        if (StrUtilX.isNotEmpty(time)) {
            String[] parts = time.split(":");
            if (parts[0].length() == 1) {
                parts[0] = "0" + parts[0];
            }
            time = String.join(":", parts);
            if (parts.length == minuteFormatLength) {
                time += ":00";
            }
            return time;
        }
        return null;
    }

    /**
     * 解析时区偏移量
     */
    private static int parseTimezoneOffset(String timezone) {
        // 示例：UTC+8 -> 8, UTC-5 -> -5
        String offset = timezone.replace("UTC", "").trim();
        return Integer.parseInt(offset);
    }

    /**
     * 校验非VCC自动出账配置是否存在
     */
    private void checkNonVccAutoBillConfigExists(SaveOrUpdateNonVccAutoBillConfigReq request) {
        if (request.getId() == null) {
            // 新增时校验唯一性
            checkDuplicate(request.getSupplierCode(), request.getHotelId(), null);
        } else {
            // 编辑时校验唯一性
            NonVccAutoBillConfigPO existConfig = nonVccAutoBillConfigMapper.selectByPrimaryKey(request.getId());
            if (existConfig == null) {
                throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
            }
            // 如果供应商编码或酒店ID发生变化，需要校验唯一性
            if (!existConfig.getSupplierCode().equals(request.getSupplierCode()) ||
                    !existConfig.getHotelId().equals(request.getHotelId())) {
                checkDuplicate(request.getSupplierCode(), request.getHotelId(), request.getId());
            }
        }
    }

    /**
     * 非VCC自动出账定时任务
     * 每小时执行一次
     * <p>
     * cron = "0 0 * * * ?"
     */
    @Override
    public void nonVccAutoBillTask(String param) {
        //如果param不为空，则证明指定了当前的日期时间，将param转换为LocalDateTime，格式:2025-06-01T10:20:50
        LocalDateTime currentDateTime = null;
        if (StrUtil.isNotBlank(param)) {
            try {
                currentDateTime = LocalDateTime.parse(param);
            } catch (Exception e) {
                log.error("非VCC自动出账转换时间异常", e);
            }
        }

        try {
            // 按出账规则从大到小依次处理
            // 按月
            processBillingRule(BillingRuleEnum.MONTHLY.getCode(), currentDateTime);
            // 按半月
            processBillingRule(BillingRuleEnum.SEMI_MONTHLY.getCode(), currentDateTime);
            // 按周
            processBillingRule(BillingRuleEnum.WEEKLY.getCode(), currentDateTime);
            // 按日
            processBillingRule(BillingRuleEnum.DAILY.getCode(), currentDateTime);
            // 按订单
            processBillingRule(BillingRuleEnum.BY_ORDER.getCode(), currentDateTime);

        } catch (Exception e) {
            log.error("非VCC自动出账异常", e);
        }
    }

    /**
     * 处理指定出账规则的配置
     *
     * @param billingRule
     * @param currentDateTime
     */
    private void processBillingRule(Integer billingRule, LocalDateTime currentDateTime) {
        LocalTime currentTime = LocalTime.now();
        if (currentDateTime != null && currentDateTime.toLocalTime() != null) {
            currentTime = currentDateTime.toLocalTime();
        }
        //将当前时间转换为时分秒格式的字符串
        String currentTimeStr = currentTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        int pageSize = 500;
        int offset = 0;

        //防止死循环
        for (int i = 0; i < MAX_LOOP_COUNT; i++) {
            // 分页查询符合条件的配置
            List<QueryAutoBillConfigsPageTaskDTO> configs = nonVccAutoBillConfigMapper.queryAutoBillConfigsPage(
                    billingRule, currentTimeStr, offset, pageSize
            );

            if (CollUtil.isEmpty(configs)) {
                break;
            }

            // 处理每个配置
            for (QueryAutoBillConfigsPageTaskDTO config : configs) {
                try {
                    processConfig(config, currentDateTime);
                } catch (Exception e) {
                    log.error("处理非VCC自动出账配置异常，configId={}", config.getId(), e);
                }
            }

            if (configs.size() < pageSize) {
                break;
            }

            offset += pageSize;
        }
    }


    /**
     * 处理单个配置
     *
     * @param config
     * @param currentDateTime
     */
    private void processConfig(QueryAutoBillConfigsPageTaskDTO config, LocalDateTime currentDateTime) {
        // 计算酒店当地时间
        LocalDateTime hotelLocalDateTime = calculateHotelLocalDateTime(
                config.getHotelLocalTimezone(),
                settingsConstant.getTimezone(),
                currentDateTime
        );
        // 查询并创建账单
        queryAndCreateAndAddSupplyOrders(config, hotelLocalDateTime);
    }

    /**
     * 计算酒店当地时间
     *
     * @param hotelTimezone
     * @param systemTimezone
     * @param currentDateTime
     * @return
     */
    private LocalDateTime calculateHotelLocalDateTime(String hotelTimezone, String systemTimezone, LocalDateTime currentDateTime) {
        LocalDateTime systemDateTime = LocalDateTime.now();
        if (currentDateTime != null) {
            systemDateTime = currentDateTime;
        }

        // 计算时差
        int hotelOffset = parseTimezoneOffset(hotelTimezone);
        int systemOffset = parseTimezoneOffset(systemTimezone);
        int hourDiff = hotelOffset - systemOffset;

        // 转换为酒店当地时间
        return systemDateTime.plusHours(hourDiff);
    }

    /**
     * 根据配置查询供货单然后出账
     *
     * @param config
     * @param hotelLocalDateTime
     */
    private void queryAndCreateAndAddSupplyOrders(QueryAutoBillConfigsPageTaskDTO config, LocalDateTime hotelLocalDateTime) {
        switch (config.getBillingRule()) {
            case 4:
                // 按月
                queryAndCreateAndAddMonthlyOrders(config, hotelLocalDateTime);
                break;
            case 3:
                // 按半月
                queryAndCreateAndAddSemiMonthlyOrders(config, hotelLocalDateTime);
                break;
            case 2:
                // 按周
                queryAndCreateAndAddWeeklyOrders(config, hotelLocalDateTime);
                break;
            case 1:
                // 按日
                queryAndCreateAndAddDailyOrders(config, hotelLocalDateTime);
                break;
            case 0:
                // 按订单
                queryAndCreateAndAddByOrderOrders(config, hotelLocalDateTime);
                break;
            default:
                break;
        }
    }

    /**
     * 查询按月出账的供货单然后出账
     */
    private void queryAndCreateAndAddMonthlyOrders(QueryAutoBillConfigsPageTaskDTO config, LocalDateTime hotelLocalDateTime) {
        // 1. 判断是否是每月1号
        if (hotelLocalDateTime.getDayOfMonth() != 1) {
            return;
        }

        // 2. 获取日期范围
        LocalDate lastMonthFirstDay = hotelLocalDateTime.toLocalDate().minusMonths(1).withDayOfMonth(1);
        LocalDate lastMonthLastDay = hotelLocalDateTime.toLocalDate().minusDays(1);

        // 3. 创建账单然后批量添加供货单
        createBatchOrderStatement(config, lastMonthFirstDay.toString(), lastMonthLastDay.toString());
    }

    /**
     * 查询按半月出账的供货单然后出账
     */
    private void queryAndCreateAndAddSemiMonthlyOrders(QueryAutoBillConfigsPageTaskDTO config, LocalDateTime hotelLocalDateTime) {
        int dayOfMonth = hotelLocalDateTime.getDayOfMonth();

        // 判断是否是1号或16号
        if (dayOfMonth == 1) {
            // 查询上个月16号到月底的供货单
            LocalDate startDate = hotelLocalDateTime.toLocalDate().minusMonths(1).withDayOfMonth(SEMI_MONTHLY_MIDDLE_DAY);
            LocalDate endDate = hotelLocalDateTime.toLocalDate().minusDays(1);

            createBatchOrderStatement(config, startDate.toString(), endDate.toString());
        } else if (dayOfMonth == SEMI_MONTHLY_MIDDLE_DAY) {
            // 查询本月1号到15号的供货单
            LocalDate startDate = hotelLocalDateTime.toLocalDate().withDayOfMonth(1);
            LocalDate endDate = hotelLocalDateTime.toLocalDate().withDayOfMonth(15);

            createBatchOrderStatement(config, startDate.toString(), endDate.toString());
        }
    }

    /**
     * 查询按周出账的供货单然后出账
     */
    private void queryAndCreateAndAddWeeklyOrders(QueryAutoBillConfigsPageTaskDTO config, LocalDateTime hotelLocalDateTime) {
        // 判断是否是周一
        if (hotelLocalDateTime.getDayOfWeek() != DayOfWeek.MONDAY) {
            return;
        }

        // 查询上周一到周日的供货单
        LocalDate lastMonday = hotelLocalDateTime.toLocalDate().minusWeeks(1);
        LocalDate lastSunday = lastMonday.plusDays(6);

        createBatchOrderStatement(config, lastMonday.toString(), lastSunday.toString());
    }

    /**
     * 查询按日出账的供货单然后出账
     */
    private void queryAndCreateAndAddDailyOrders(QueryAutoBillConfigsPageTaskDTO config, LocalDateTime hotelLocalDateTime) {
        // 查询昨天的供货单
        LocalDate yesterday = hotelLocalDateTime.toLocalDate().minusDays(1);

        createBatchOrderStatement(config, yesterday.toString(), yesterday.toString());
    }

    /**
     * 按订单维度查询供货单，然后每一个供货单对应创建账单
     */
    private void queryAndCreateAndAddByOrderOrders(QueryAutoBillConfigsPageTaskDTO config, LocalDateTime hotelLocalDateTime) {
        LocalDate targetDate = hotelLocalDateTime.toLocalDate();

        Integer dateQueryType = null;
        // 1. 根据出账日期类型计算目标日期
        switch (config.getBillingDateType()) {
            case 0:
                // 入住日前
                targetDate = targetDate.plusDays(config.getBillingDateDays());
                dateQueryType = DateQueryTypeEnum.DATE_QUERY_TYPE_1.getType();
                break;
            case 1:
                // 入住日当天
                dateQueryType = DateQueryTypeEnum.DATE_QUERY_TYPE_1.getType();
                break;
            case 2:
                // 离店日当天
                dateQueryType = DateQueryTypeEnum.DATE_QUERY_TYPE_2.getType();
                break;
            case 3:
                // 离店日后
                targetDate = targetDate.minusDays(config.getBillingDateDays());
                dateQueryType = DateQueryTypeEnum.DATE_QUERY_TYPE_2.getType();
                break;
            default:
                break;
        }


        int currentPage = 1;
        int totalPage = 1;
        int pageSize = 300;
        // 2.1 查询是否有符合条件的供货单
        QueryUnCheckOutSupplyOrderDTO queryUnCheckOutSupplyOrderDTO = new QueryUnCheckOutSupplyOrderDTO();
        queryUnCheckOutSupplyOrderDTO.setCurrentPage(currentPage);
        queryUnCheckOutSupplyOrderDTO.setPageSize(pageSize);
        queryUnCheckOutSupplyOrderDTO.setDateQueryType(dateQueryType);
        queryUnCheckOutSupplyOrderDTO.setStartDate(targetDate.toString());
        queryUnCheckOutSupplyOrderDTO.setEndDate(targetDate.toString());
        queryUnCheckOutSupplyOrderDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.getKey());
        queryUnCheckOutSupplyOrderDTO.setSupplierCode(config.getSupplierCode());
        queryUnCheckOutSupplyOrderDTO.setHotelId(Math.toIntExact(config.getHotelId()));
        queryUnCheckOutSupplyOrderDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        PaginationSupportDTO<UnCheckOutSupplyOrderDTO> paginationSupportDTOResponse = supplierStatementService.queryUnCheckOutSupplyOrder(queryUnCheckOutSupplyOrderDTO);
        if (paginationSupportDTOResponse != null && CollectionUtil.isNotEmpty(paginationSupportDTOResponse.getItemList())) {
            List<UnCheckOutSupplyOrderDTO> unCheckOutSupplyOrderDTOList = paginationSupportDTOResponse.getItemList();
            //每一个供货单对应创建账单
            for (UnCheckOutSupplyOrderDTO unCheckOutSupplyOrderDTO : unCheckOutSupplyOrderDTOList) {
                try {
                    createStatement(config, unCheckOutSupplyOrderDTO.getCurrency(), targetDate.toString(), targetDate.toString(), Collections.singletonList(unCheckOutSupplyOrderDTO), Boolean.FALSE);
                } catch (Exception e) {
                    log.error("创建非VCC账单失败，config={}，currency={}，startDate={}，endDate={}", config, unCheckOutSupplyOrderDTO.getCurrency(), targetDate, targetDate);
                }
            }

            totalPage = paginationSupportDTOResponse.getTotalPage();
            currentPage++;
            for (int i = currentPage; i <= totalPage; i++) {
                //每次出账之后，未出账的供货单会变少，如原先100个，出了10个，就剩下90个未出账的，所以每次查询的时候，都是查询第一页即可，数量会自动往前移动，页数无需加一
                paginationSupportDTOResponse = supplierStatementService.queryUnCheckOutSupplyOrder(queryUnCheckOutSupplyOrderDTO);
                if (paginationSupportDTOResponse != null && CollectionUtil.isNotEmpty(paginationSupportDTOResponse.getItemList())) {
                    unCheckOutSupplyOrderDTOList = paginationSupportDTOResponse.getItemList();
                    for (UnCheckOutSupplyOrderDTO unCheckOutSupplyOrderDTO : unCheckOutSupplyOrderDTOList) {
                        try {
                            createStatement(config, unCheckOutSupplyOrderDTO.getCurrency(), targetDate.toString(), targetDate.toString(), Collections.singletonList(unCheckOutSupplyOrderDTO), Boolean.FALSE);
                        } catch (Exception e) {
                            log.error("创建非VCC账单失败，config={}，currency={}，startDate={}，endDate={}", config, unCheckOutSupplyOrderDTO.getCurrency(), targetDate, targetDate);
                        }
                    }
                }
            }
        }
    }

    /**
     * 创建账单然后批量添加供货单
     */
    private void createBatchOrderStatement(QueryAutoBillConfigsPageTaskDTO config, String startDate, String endDate) {
        // 1.查询出所有的币种，同一个供应商下面的同一个酒店，可能存在多个币种，相同币种才放到同一个账单进行出账
        QueryAllSupplierSupplyOrderCurrencyDTO query = new QueryAllSupplierSupplyOrderCurrencyDTO();
        query.setSupplierCode(config.getSupplierCode());
        query.setHotelId(config.getHotelId());
        query.setDateQueryType(DateQueryTypeEnum.DATE_QUERY_TYPE_2.getType());
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        List<Integer> allCurrency = nonVccAutoBillConfigMapper.queryAllSupplierSupplyOrderCurrency(query);

        // 2.根据不同币种去查询供货单，如果能查询出对应的供货单，则进行出账
        int pageSize = 300;
        for (Integer currency : allCurrency) {
            int currentPage = 1;
            int totalPage = 1;
            // 2.1 查询是否有符合条件的供货单
            QueryUnCheckOutSupplyOrderDTO queryUnCheckOutSupplyOrderDTO = new QueryUnCheckOutSupplyOrderDTO();
            queryUnCheckOutSupplyOrderDTO.setCurrency(currency);
            queryUnCheckOutSupplyOrderDTO.setCurrentPage(currentPage);
            queryUnCheckOutSupplyOrderDTO.setPageSize(pageSize);
            queryUnCheckOutSupplyOrderDTO.setDateQueryType(DateQueryTypeEnum.DATE_QUERY_TYPE_2.getType());
            queryUnCheckOutSupplyOrderDTO.setStartDate(startDate);
            queryUnCheckOutSupplyOrderDTO.setEndDate(endDate);
            queryUnCheckOutSupplyOrderDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.getKey());
            queryUnCheckOutSupplyOrderDTO.setSupplierCode(config.getSupplierCode());
            queryUnCheckOutSupplyOrderDTO.setHotelId(Math.toIntExact(config.getHotelId()));
            queryUnCheckOutSupplyOrderDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
            PaginationSupportDTO<UnCheckOutSupplyOrderDTO> paginationSupportDTOResponse = supplierStatementService.queryUnCheckOutSupplyOrder(queryUnCheckOutSupplyOrderDTO);
            if (paginationSupportDTOResponse != null && CollectionUtil.isNotEmpty(paginationSupportDTOResponse.getItemList())) {
                List<UnCheckOutSupplyOrderDTO> unCheckOutSupplyOrderDTOList = paginationSupportDTOResponse.getItemList();
                // 2.2 存在需要创建账单的供货单，则将查到的供货单传过去一并创建账单(以当前分页第一页查到的供货单为准)，创建之后，还有需要加入的供货单，则接着查询添加，直到没有供货单为止
                Integer statementId = createStatement(config, currency, startDate, endDate, unCheckOutSupplyOrderDTOList, Boolean.TRUE);
                if (statementId == null) {
                    log.error("创建非VCC账单失败，config={}，currency={}，startDate={}，endDate={}", config, currency, startDate, endDate);
                    throw new SysException(ErrorCodeEnum.CREATE_STATEMENT_FAIL);
                }
                totalPage = paginationSupportDTOResponse.getTotalPage();
                currentPage++;
                for (int i = currentPage; i <= totalPage; i++) {
                    //每次出账之后，未出账的供货单会变少，如原先100个，出了10个，就剩下90个未出账的，所以每次查询的时候，都是查询第一页即可，数量会自动往前移动，页数无需加一
                    paginationSupportDTOResponse = supplierStatementService.queryUnCheckOutSupplyOrder(queryUnCheckOutSupplyOrderDTO);
                    if (paginationSupportDTOResponse != null && CollectionUtil.isNotEmpty(paginationSupportDTOResponse.getItemList())) {
                        unCheckOutSupplyOrderDTOList = paginationSupportDTOResponse.getItemList();
                        addOrdersToStatement(config, statementId, unCheckOutSupplyOrderDTOList, currency);
                    }
                }
            }
        }
    }

    /**
     * 创建账单
     */
    private Integer createStatement(QueryAutoBillConfigsPageTaskDTO config,
                                    Integer currency,
                                    String startDate,
                                    String endDate,
                                    List<UnCheckOutSupplyOrderDTO> unCheckOutSupplyOrderDTOList,
                                    Boolean isReturnStatementId) {
        CreateSupplierStatementDTO createStatementDTO = new CreateSupplierStatementDTO();
        createStatementDTO.setSupplierCode(config.getSupplierCode());
        createStatementDTO.setOrderNumber(unCheckOutSupplyOrderDTOList.size());
        createStatementDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        createStatementDTO.setStatementName(buildStatementName(config, currency, startDate, endDate));
        createStatementDTO.setSettlementDate(DateUtilX.dateToString(DateUtilX.addDate(new Date(), 1)));
        createStatementDTO.setSupplyOrderCodeList(unCheckOutSupplyOrderDTOList.stream().map(UnCheckOutSupplyOrderDTO::getSupplyOrderCode).collect(Collectors.toList()));
        createStatementDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        createStatementDTO.setSupplierName(config.getSupplierName());
        createStatementDTO.setOperator("system(非VCC自动创建账单)");
        createStatementDTO.setCurrency(currency);
        createStatementDTO.setIsSyncCreateStatement(1);
        createStatementDTO.setStatementLabel(config.getPaymentMethod());
        supplierStatementService.createStatement(createStatementDTO);

        if (isReturnStatementId) {
            //账单创建成功则根据供货单编号去查询对应的账单id
            QuerySupplierStatementListDTO querySupplierStatementListDTO = new QuerySupplierStatementListDTO();
            querySupplierStatementListDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            querySupplierStatementListDTO.setSupplyOrderCode(unCheckOutSupplyOrderDTOList.get(0).getSupplyOrderCode());
            List<Integer> statementIds = supplierStatementOrderMapper.queryStatementIds(querySupplierStatementListDTO);
            if (CollUtil.isEmpty(statementIds)) {
                return null;
            }
            return statementIds.get(0);
        }
        return null;
    }

    /**
     * 构建账单名称
     */
    private String buildStatementName(QueryAutoBillConfigsPageTaskDTO config,
                                      Integer currency,
                                      String startDate,
                                      String endDate) {
        StringBuilder name = new StringBuilder();

        name.append(config.getSupplierName()).append(" ");
        //酒店名称如果超过100个字符，则截取前100个字符
        if (StrUtil.isNotBlank(config.getHotelName()) && config.getHotelName().length() > AMOUNT_CONVERSION_FACTOR) {
            name.append(config.getHotelName().substring(0, AMOUNT_CONVERSION_FACTOR)).append(" ");
        } else {
            name.append(config.getHotelName()).append(" ");
        }

        switch (config.getBillingRule()) {
            case 0:
                // 按订单
                name.append(DateUtilX.dateToString(new Date(), "yyyy-MM-dd"));
                break;
            case 1:
                // 按日
                name.append("离店日期 ").append(endDate).append(" ").append(SettlementCurrencyEnum.getCodeByKey(String.valueOf(currency)));
                break;
            case 2:
            case 3:
                // 按周、按半月
            case 4:
                // 按月
                name.append("离店日期 ").append(startDate).append("-").append(endDate).append(" ").append(SettlementCurrencyEnum.getCodeByKey(String.valueOf(currency)));
                break;
            default:
                break;
        }
        return name.toString();
    }

    /**
     * 分批添加供货单到账单
     */
    private void addOrdersToStatement(QueryAutoBillConfigsPageTaskDTO config, Integer statementId, List<UnCheckOutSupplyOrderDTO> unCheckOutSupplyOrderDTOList, Integer currency) {
        // 分批添加，每批100个
        int batchSize = AMOUNT_CONVERSION_FACTOR;
        List<List<UnCheckOutSupplyOrderDTO>> batches = Lists.partition(unCheckOutSupplyOrderDTOList, batchSize);

        for (List<UnCheckOutSupplyOrderDTO> batch : batches) {
            addOrderBatch(config, statementId, batch, currency);
        }
    }

    /**
     * 添加一批供货单到账单
     */
    private void addOrderBatch(QueryAutoBillConfigsPageTaskDTO config, Integer statementId, List<UnCheckOutSupplyOrderDTO> batch, Integer currency) {
        AddStatementSupplyOrderListDTO addOrdersDTO = new AddStatementSupplyOrderListDTO();
        addOrdersDTO.setStatementId(statementId);
        addOrdersDTO.setSupplyOrderIdList(batch.stream()
                .map(UnCheckOutSupplyOrderDTO::getSupplyOrderId)
                .collect(Collectors.toList()));
        addOrdersDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        addOrdersDTO.setSupplierCode(config.getSupplierCode());
        addOrdersDTO.setCurrency(currency);
        addOrdersDTO.setOperator("system(非VCC自动创建账单)");
        addOrdersDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);

        //调用server层，因为有防止重复添加的逻辑
        supplierStatementServer.addStatementOrderList(addOrdersDTO);
    }

}
