package com.tiangong.finance.statement.domain.req;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:19
 * @Description:
 */

@Data
public class SaveOrUpdateNonVccAutoBillConfigReq extends BaseDTO {

    private Long id;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店当地时区 (例如: UTC+7)
     */
    private String hotelLocalTimezone;

    /**
     * 天宫付款方式 0-酒店给我方发送付款链接 1-银行转账 2-WorldFirst账户收款
     */
    private Integer paymentMethod;

    /**
     * 付款链接发送邮箱 (英文逗号进行分隔，最多支持3个邮箱)
     */
    private String paymentLinkEmails;

    /**
     * 酒店付款联系人
     */
    private String hotelPaymentContactPerson;

    /**
     * 酒店收款开户行
     */
    private String hotelCollectionBank;

    /**
     * 酒店收款账户名
     */
    private String hotelCollectionAccountName;

    /**
     * 酒店收款账号
     */
    private String hotelCollectionAccountNumber;

    /**
     * WorldFirst账户号
     */
    private String worldFirstAccountNumber;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 出账规则 0-按订单 1-按日 2-按周 3-按半月 4-按月
     */
    private Integer billingRule;

    /**
     * 出账日期类型 0-入住日前 1-入住日当天 2-离店日当天 3-离店日后
     */
    private Integer billingDateType;

    /**
     * 出账日期天数
     */
    private Integer billingDateDays;

    /**
     * 出账时间窗口: 酒店当地开始时间 (HH:mm:ss)
     */
    private String hotelLocalStartTime;

    /**
     * 出账时间窗口: 酒店当地结束时间 (HH:mm:ss)
     */
    private String hotelLocalEndTime;

    /**
     * 语言
     */
    private String language;

    /**
     * 运营商编码
     */
    private String companyCode;
}
