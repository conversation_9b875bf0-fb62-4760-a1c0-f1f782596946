package com.tiangong.finance.statement.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.AgentStatementDetailDTO;
import com.tiangong.finance.remote.statement.response.AgentStatementListResponseDTO;
import com.tiangong.finance.remote.statement.response.StatementOrderDTO;
import com.tiangong.finance.remote.statement.response.UnCheckOutOrderDTO;
import com.tiangong.finance.remote.statement.response.UncheckOutAgentDTO;
import com.tiangong.finance.remote.statement.response.UpdatedStatementOrderDTO;

import java.text.ParseException;

public interface AgentStatementService {

    /**
     * 已出账单查询
     *
     * @param request 查询代理账单列表DTO，包含查询条件
     * @return 返回分页的代理账单列表响应DTO数据
     */
    PaginationSupportDTO<AgentStatementListResponseDTO> queryStatementList(QueryAgentStatementListDTO request);

    /**
     * 未出账查询
     *
     * @param request 查询未出账代理列表DTO，包含查询条件
     * @return 返回分页的未出账代理DTO数据
     */
    PaginationSupportDTO<UncheckOutAgentDTO> queryUncheckOutAgentList(QueryUncheckOutAgentListDTO request);

    /**
     * 创建账单
     *
     * @param request 创建代理账单DTO，包含账单创建信息
     * @throws ParseException 日期解析异常，当日期格式不正确时抛出
     */
    void createStatement(CreateAgentStatementDTO request) throws ParseException;

    /**
     * 账单详情
     *
     * @param request 账单ID DTO，包含账单标识
     * @return 返回代理账单详情DTO
     */
    AgentStatementDetailDTO queryStatementDetail(StatementIdDTO request);

    /**
     * 账单明细
     *
     * @param request 查询账单订单列表DTO，包含查询条件
     * @return 返回分页的账单订单DTO数据
     */
    PaginationSupportDTO<StatementOrderDTO> queryStatementOrderList(QueryStatementOrderListDTO request);

    /**
     * 未出账订单查询
     *
     * @param request 查询未出账订单DTO，包含查询条件
     * @return 返回分页的未出账订单DTO数据
     */
    PaginationSupportDTO<UnCheckOutOrderDTO> queryUnCheckOutOrder(QueryUnCheckOutOrderDTO request);

    /**
     * 添加账单明细
     *
     * @param request 添加账单订单列表DTO，包含要添加的订单信息
     */
    void addStatementOrderList(AddStatementOrderListDTO request);

    /**
     * 删除账单明细
     *
     * @param request 删除账单订单列表DTO，包含要删除的订单信息
     */
    void deleteStatementOrderList(DeleteStatementOrderListDTO request);

    /**
     * 修改账单状态
     *
     * @param request 修改账单状态DTO，包含状态变更信息
     */
    void modifyStatementStatus(ModifyStatementStatusDTO request);

    /**
     * 取消账单
     *
     * @param request 修改账单状态DTO，包含取消账单信息
     */
    void cancelStatement(ModifyStatementStatusDTO request);

    /**
     * 账单明细变更查询
     *
     * @param request 查询更新账单订单列表DTO，包含查询条件
     * @return 返回分页的更新账单订单DTO数据
     */
    PaginationSupportDTO<UpdatedStatementOrderDTO> queryUpdatedStatementOrderList(QueryUpdatedStatementOrderListDTO request);

    /**
     * 更新账单明细
     *
     * @param request 账单ID DTO，包含账单标识信息
     */
    void updateStatementOrderList(StatementIdDTO request);

    /**
     * 通知收款
     *
     * @param request 通知账单收款DTO，包含收款通知信息
     * @return 返回通知操作结果状态码
     */
    Integer notifyCollectionOfStatement(NotifyCollectionOfStatementDTO request);

    /**
     * 通知付款
     *
     * @param request 通知账单付款DTO，包含付款通知信息
     * @return 返回通知操作结果状态码
     */
    Integer notifyPaymentOfStatement(NotifyPaymentOfStatementDTO request);

    /**
     * 修改账单名称
     *
     * @param request 修改账单名称DTO，包含新的账单名称
     */
    void modifyStatementName(ModifyStatementNameDTO request);

    /**
     * 修改结算日期
     *
     * @param request 修改结算日期DTO，包含新的结算日期
     */
    void modifySettlementDate(ModifySettlementDateDTO request);

    /**
     * 修改账单中订单的要收金额
     * @param request
     */
    void updateStatementOrderReceiveAmt(ModifySettlementOrderReceiveAmtDTO request);

    /**
     * 创建订单记账任务
     * @param request
     */
    void createOrderStatementTask(CreateOrderStatementTaskDTO request);

    /**
     * 订单附加项补汇差
     *
     * @param request 修改账单状态DTO，包含订单附加项补汇差信息
     */
    void repairOrderAdditionalCharges(ModifyStatementStatusDTO request);

    /**
     * 校验客户结算成本异常
     *
     * @param request 修改账单状态DTO，包含校验参数
     * @return 返回校验结果状态码
     */
    Integer checkAgentSettlementCostError(ModifyStatementStatusDTO request);
}
