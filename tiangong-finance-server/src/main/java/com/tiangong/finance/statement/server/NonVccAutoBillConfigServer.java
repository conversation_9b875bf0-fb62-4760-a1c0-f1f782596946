package com.tiangong.finance.statement.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.ConvertTimeZoneResp;
import com.tiangong.finance.statement.domain.resp.GetNonVccAutoBillConfigDetailResp;
import com.tiangong.finance.statement.domain.resp.NonVccAutoBillConfigPageResp;
import com.tiangong.finance.statement.service.NonVccAutoBillConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2025/6/5 18:11
 * @Description:
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/finance/nonVccAutoBillConfig")
public class NonVccAutoBillConfigServer extends BaseController {

    private final NonVccAutoBillConfigService nonVccAutoBillConfigService;

    /**
     * 保存或更新非VCC自动出账配置
     */
    @PostMapping("/saveOrUpdateNonVccAutoBillConfig")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> saveOrUpdateNonVccAutoBillConfig(@RequestBody SaveOrUpdateNonVccAutoBillConfigReq request) {
        request.setCreatedBy(super.getUserName());
        request.setUpdatedBy(super.getUserName());
        request.setCompanyCode(getCompanyCode());
        nonVccAutoBillConfigService.saveOrUpdateNonVccAutoBillConfig(request);
        return Response.success();
    }

    /**
     * 删除非VCC自动出账配置
     */
    @PostMapping("/deleteAutoBillConfig")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> deleteNonVccAutoBillConfig(@RequestBody DeleteNonVccAutoBillConfigReq request) {
        nonVccAutoBillConfigService.deleteNonVccAutoBillConfig(request);
        return Response.success();
    }

    /**
     * 查询非VCC自动出账配置详情
     */
    @PostMapping("/getAutoBillConfigDetail")
    @PreAuthorize("@syyo.check('finance')")
    public Response<GetNonVccAutoBillConfigDetailResp> getNonVccAutoBillConfigDetail(@RequestBody GetNonVccAutoBillConfigDetailReq request) {
        return Response.success(nonVccAutoBillConfigService.getNonVccAutoBillConfigDetail(request));
    }

    /**
     * 分页查询非VCC自动出账配置
     */
    @PostMapping("/nonVccAutoBillConfigPage")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<NonVccAutoBillConfigPageResp>> nonVccAutoBillConfigPage(@RequestBody NonVccAutoBillConfigPageReq request) {
        return Response.success(nonVccAutoBillConfigService.nonVccAutoBillConfigPage(request));
    }

    /**
     * 时间转换
     */
    @PostMapping("/convertTimeZone")
    @PreAuthorize("@syyo.check('finance')")
    public Response<ConvertTimeZoneResp> convertTimeZone(@RequestBody ConvertTimeZoneReq request) {
        return Response.success(nonVccAutoBillConfigService.convertTimeZone(request));
    }

    /**
     * 非VCC自动出账定时任务
     */
    @PostMapping("/nonVccAutoBillTask")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> nonVccAutoBillTask(@RequestParam("param") String param) {
        nonVccAutoBillConfigService.nonVccAutoBillTask(param);
        return Response.success();
    }
}
