package com.tiangong.finance.statement.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StatementOrderPO {
    /**
     *
     */
    private Integer statementId;
    /**
     *
     */
    private Integer supplyOrderId;
    /**
     *
     */
    private String supplyOrderCode;
    /**
     *
     */
    private String hotelName;
    /**
     *
     */
    private String roomName;
    /**
     *
     */
    private String productName;
    /**
     *
     */
    private String guest;
    /**
     *
     */
    private Date startDate;
    /**
     *
     */
    private Date endDate;
    /**
     *
     */
    private Integer roomQty;
    /**
     *
     */
    private BigDecimal baseCurrency;
    /**
     * 已付
     */
    private BigDecimal paidAmt;
    /**
     * 应付
     */
    private BigDecimal payableAmt;
    /**
     * 已收
     */
    private BigDecimal receivedAmt;
    /**
     * 应收
     */
    private BigDecimal receivableAmt;
    /**
     * 未付/未收
     */
    private BigDecimal amt;
    /**
     * 要收
     */
    private BigDecimal receiveAmt;
    /**
     *
     */
    private Date createdDt;
    /**
     *
     */
    private Integer confirmationStatus;
    /**
     *
     */
    private String operator;
    /**
     *
     */
    private Date sysDate;
    /**
     * 刷卡金额
     */
    private BigDecimal swipingCardAmount;
    /**
     * 刷卡金额币种
     */
    private Integer swipingCardAmountCurrency;
    /**
     * 刷卡汇率
     */
    private BigDecimal swipingCardRate;
    /**
     * 刷卡时间
     */
    private Date swipingCardDt;
    /**
     * 结算金额
     */
    private BigDecimal settleAmount;
    /**
     * 结算金额币种
     */
    private Integer settleAmountCurrency;
    /**
     * 收款汇率(结算金额与成本收款金额的汇率)
     */
    private BigDecimal collectionRate;
    /**
     * 成本收款金额
     */
    private BigDecimal collectionAmount;
    /**
     * 汇差
     */
    private BigDecimal rateDifferenceValue;
    /**
     * 结算成本错误描述
     */
    private String settlementCostErrorDesc;
}
