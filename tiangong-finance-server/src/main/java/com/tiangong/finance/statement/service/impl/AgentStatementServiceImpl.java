package com.tiangong.finance.statement.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.tiangong.common.Response;
import com.tiangong.common.remote.SequenceRemote;
import com.tiangong.config.SettingsConstant;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.DeleteEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.enums.SystemCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.finance.enums.*;
import com.tiangong.finance.lock.service.FinanceLockService;
import com.tiangong.finance.remote.lock.request.FinanceLockOrderDTO;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.*;
import com.tiangong.finance.remote.workorder.request.NotifyCollectionDTO;
import com.tiangong.finance.remote.workorder.request.NotifyItemDTO;
import com.tiangong.finance.remote.workorder.request.NotifyPaymentDTO;
import com.tiangong.finance.service.ExchangeRateService;
import com.tiangong.finance.statement.domain.*;
import com.tiangong.finance.statement.dto.*;
import com.tiangong.finance.statement.mapper.*;
import com.tiangong.finance.statement.service.AgentStatementService;
import com.tiangong.finance.statement.service.OrderStatementTaskService;
import com.tiangong.finance.statement.service.StatementAmtLogService;
import com.tiangong.finance.workorder.service.FinanceNofityService;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.OrderRemote;
import com.tiangong.order.remote.dto.AdditionalChargesDTO;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.ContactSupplierDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AgentStatementServiceImpl implements AgentStatementService {

    /**
     * 查询所有结算状态的标识值
     */
    private static final String ALL_SETTLEMENT_STATUS = "-1";

    /**
     * 查询所有逾期状态的标识值
     */
    private static final String ALL_OVERDUE_STATUS = "-1";

    /**
     * 查询所有账单状态的标识值
     */
    private static final String ALL_STATEMENT_STATUS = "-1";

    @Autowired
    private AgentStatementMapper agentStatementMapper;

    @Autowired
    private AgentStatementOrderMapper agentStatementOrderMapper;

    @Autowired
    private FinanceNofityService financeNofityService;

    @Autowired
    private FinanceLockService financeLockService;

    @Autowired
    private SequenceRemote sequenceRemote;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private Executor asyncCreatBill;

    @Autowired
    private OrderStatementTaskService orderStatementTaskService;

    @Autowired
    private ContactMapper contactMapper;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private StatementAmtLogService statementAmtLogService;

    @Autowired
    private ProtocolOrderSettlementCostMapper protocolOrderSettlementCostMapper;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private OrderRemote orderRemote;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private StatementAmtLogMapper statementAmtLogMapper;

    @Override
    public PaginationSupportDTO<AgentStatementListResponseDTO> queryStatementList(QueryAgentStatementListDTO request) {
        if (StrUtilX.isNotEmpty(request.getSettlementStatus()) && request.getSettlementStatus().equals(ALL_SETTLEMENT_STATUS)) {
            request.setSettlementStatus(null);
        }
        if (StrUtilX.isNotEmpty(request.getOverdueStatus()) && request.getOverdueStatus().equals(ALL_OVERDUE_STATUS)) {
            request.setOverdueStatus(null);
        }
        if (StrUtilX.isNotEmpty(request.getStatementStatus()) && request.getStatementStatus().equals(ALL_STATEMENT_STATUS)) {
            request.setStatementStatus(null);
        }
        // 如果订单编号不为空,则查询对应的账单编号
        List<AgentStatementOrderPO> matchedStatementOrders = new ArrayList<>();
        Set<Integer> statementIdSet = new HashSet<>();
        if (StrUtilX.isNotEmpty(request.getOrderCode())) {
            Example example = new Example(AgentStatementOrderPO.class);
            example.createCriteria().andLike("orderCode", "%" + request.getOrderCode() + "%");
            matchedStatementOrders = agentStatementOrderMapper.selectByExample(example);
            if (CollUtilX.isNotEmpty(matchedStatementOrders)) {
                statementIdSet = matchedStatementOrders.stream().map(AgentStatementOrderPO::getStatementId).collect(Collectors.toSet());
                request.setStatementIdList(new ArrayList<>(statementIdSet));
            }
            if (statementIdSet.size() == 0) {
                PaginationSupportDTO<AgentStatementListResponseDTO> paginationSupport = new PaginationSupportDTO<>();
                paginationSupport.setPageSize(25);
                paginationSupport.setTotalCount(0);
                paginationSupport.setTotalPage(0);
                paginationSupport.setCurrentPage(1);
                paginationSupport.setItemList(new ArrayList<>());
                return paginationSupport;
            }
        }

        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            request.setAgentName(null);
        }
        List<AgentStatementListResponseDTO> list = agentStatementMapper.queryStatementList(request);
        PageInfo<AgentStatementListResponseDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<AgentStatementListResponseDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public PaginationSupportDTO<UncheckOutAgentDTO> queryUncheckOutAgentList(QueryUncheckOutAgentListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            request.setAgentName(null);
        }
        List<UncheckOutAgentDTO> list = agentStatementMapper.queryUncheckOutAgentList(request);
        // 获取缓存的未出账状态，如果状态为失败的话加上失败原因
        for (UncheckOutAgentDTO uncheckOutAgentDTO : list) {
            Object object = RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, uncheckOutAgentDTO.getAgentCode());
            if (object != null) {
                OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(object, OutgoingStateDTO.class);
                if (outgoingStateDTO != null) {
                    BeanUtils.copyProperties(outgoingStateDTO, uncheckOutAgentDTO);
                }
            } else {
                OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
                RedisTemplateX.hPut(RedisKey.SATEMENT_ACCOUNT_STATE, uncheckOutAgentDTO.getAgentCode(), JSONObject.toJSONString(stateDTO));
                uncheckOutAgentDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            }
        }
        PageInfo<UncheckOutAgentDTO> page = new PageInfo<>(list);
        PaginationSupportDTO<UncheckOutAgentDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    /**
     * 创建账单，查询账单状态是否为出账中，如果是直接返回，否则把改供应商出账状态调整为出账中，避免重复提交
     */
    @Override
    @Transactional
    public void createStatement(CreateAgentStatementDTO request) {
        long millis = System.currentTimeMillis();
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()), OutgoingStateDTO.class);
        if (outgoingStateDTO == null || outgoingStateDTO.getOutgoingState().equals(OutgoingStateEnum.CHECKING.key)) {
            throw new SysException(ErrorCodeEnum.SUPPER_BILL_OUTGOING_STATUS);
        }
        Integer integer = request.getOrderNumber() < 60 ? 2 : request.getOrderNumber() / 20;
        // 设置供应商redis的出账状态statementOrder
        OutgoingStateDTO stateDTO = new OutgoingStateDTO();
        stateDTO.setCheckInDate(request.getStartDate());
        stateDTO.setCheckOutDate(request.getEndDate());
        stateDTO.setOutgoingState(OutgoingStateEnum.CHECKING.key);
        stateDTO.setTime(integer);
        stateDTO.setCreatTime(millis - 800);
        stateDTO.setDateQueryType(request.getDateQueryType());
        RedisTemplateX.hPut(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode(), JSONObject.toJSONString(stateDTO));

        // 个人支付自动创建账单
        if (StrUtilX.isNotEmpty(request.getTaskCode())) {
            creatPersonPayBill(request);
        } else {
            // 异步创建账单
            asyncCreatBill(request);
        }
    }

    private void creatPersonPayBill(CreateAgentStatementDTO request) {
        TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
        long millis = System.currentTimeMillis();
        AgentStatementPO newStatement = new AgentStatementPO();
        try {
            try {
                // 1.创建账单
                BeanUtils.copyProperties(request, newStatement);
                newStatement.setStatementStatus(StatementStatusEnum.UN_CHECK.key);
                newStatement.setStatementAmt(BigDecimal.ZERO);
                newStatement.setReceivedAmt(BigDecimal.ZERO);
                newStatement.setUnreceivedAmt(BigDecimal.ZERO);
                newStatement.setUnconfirmedPaidAmt(BigDecimal.ZERO);
                newStatement.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
                newStatement.setSettlementStatus(0);
                newStatement.setSettlementDate(DateUtilX.stringToDate(request.getSettlementDate()));
                newStatement.setCreatedBy(request.getOperator());
                newStatement.setCreatedDt(new Date());

                //获取分销商币种
                AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, newStatement.getAgentCode()), AgentAccountConfig.class);
                if (agentAccountConfig == null) {
                    throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
                }
                newStatement.setCurrency(agentAccountConfig.getSettlementCurrency());
                newStatement.setRate(getRateToOrgCurrency(newStatement.getCurrency()));
                //获取编码
                String code = RedisTemplateX.lRightPop(SystemCodeEnum.AGENTSTATEMENTCODE.code);
                if (null == code) {
                    Map<String, String> requestMap = new HashMap<>();
                    requestMap.put("seqName", SystemCodeEnum.AGENTSTATEMENTCODE.code);
                    sequenceRemote.createCode(requestMap);
                    code = RedisTemplateX.lRightPop(SystemCodeEnum.AGENTSTATEMENTCODE.code);
                }
                // 设置扣减额度账户
                if (StrUtilX.isNotEmpty(agentAccountConfig.getLineAccount())) {
                    newStatement.setSubtractLineAccount(agentAccountConfig.getLineAccount());
                } else {
                    newStatement.setSubtractLineAccount(agentAccountConfig.getAgentCode());
                }
                newStatement.setStatementCode(code);
                agentStatementMapper.insert(newStatement);
            } catch (Exception e) {
                log.error("创建客户账单表失败", e);
                throw new RuntimeException("创建客户账单表失败");
            }
            // 2.保存账单明细
            try {
                InsertStatementOrderDTO insertStatementOrderDTO = new InsertStatementOrderDTO();
                BeanUtils.copyProperties(request, insertStatementOrderDTO);
                insertStatementOrderDTO.setStatementId(newStatement.getId());
                List<StatementOrderPO> statementOrders = agentStatementOrderMapper.getStatementOrderByOrderCode(insertStatementOrderDTO);
                agentStatementOrderMapper.saveBatchStatementOrder2(statementOrders);
            } catch (Exception e) {
                log.error("添加客户账单明细失败", e);
                throw new RuntimeException("添加客户账单明细失败");
            }

            try {
                // 3.更新账单金额
                StatementIdDTO statementIdDTO = new StatementIdDTO();
                statementIdDTO.setStatementId(newStatement.getId());
                updateStatementAmount(statementIdDTO);
            } catch (Exception e) {
                log.error("更新客户账单金额失败", e);
                throw new RuntimeException("更新客户账单金额失败");
            }
            try {
                // 4.更新订单对账状态为出账中，并将订单加锁
                UpdateOrderFinanceDTO updateOrderFinanceDTO = new UpdateOrderFinanceDTO();
                updateOrderFinanceDTO.setStatementId(newStatement.getId());
                updateOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
                updateOrderFinanceDTO.setFinanceLockStatus(1);
                agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);
                newStatement = agentStatementMapper.selectByPrimaryKey(newStatement.getId());

                //在创建账单时自动为账单里的订单加锁
                QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
                queryStatementOrderListDTO.setStatementId(newStatement.getId());
                List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
                for (StatementOrderDTO statementOrderDTO : list) {
                    FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
                    Example example = new Example(AgentStatementOrderPO.class);
                    example.createCriteria().andEqualTo("orderCode", statementOrderDTO.getOrderCode())
                            .andEqualTo("statementId", newStatement.getId());
                    AgentStatementOrderPO statementOrder = agentStatementOrderMapper.selectOneByExample(example);
                    financeLockOrderDTO.setOrderId(statementOrder.getOrderId());
                    financeLockOrderDTO.setLockStatus(0);
                    financeLockService.lockOrder(financeLockOrderDTO);
                }
            } catch (Exception e) {
                log.error("客户账单更新订单对账状态失败", e);
                throw new RuntimeException("客户账单更新订单对账状态失败");
            }
            try {
                // 创建成功设置客户redis的出账状态为未出账
                OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
                RedisTemplateX.hPut(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode(), JSONObject.toJSONString(stateDTO));

            } catch (Exception e) {
                log.error("设置客户redis的出账状态为未出账失败", e);
                throw new RuntimeException("设置客户redis的出账状态为未出账失败");
            }

            // 查询账单中订单的账单id
            QueryStatementOrderListDTO queryStatementOrderList = new QueryStatementOrderListDTO();
            queryStatementOrderList.setStatementId(newStatement.getId());
            List<StatementOrderDTO> statementOrderList = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderList);

            // 更新要收金额
            ModifySettlementOrderReceiveAmtDTO modifyReceiveAmtDTO = new ModifySettlementOrderReceiveAmtDTO();
            modifyReceiveAmtDTO.setStatementId(newStatement.getId());
            modifyReceiveAmtDTO.setStatementOrderId(statementOrderList.get(0).getStatementOrderId());
            modifyReceiveAmtDTO.setReceiveAmt(request.getReceiveAmt());
            modifyReceiveAmtDTO.setAgentCode(request.getAgentCode());
            modifyReceiveAmtDTO.setOperator(request.getOperator());
            updateStatementOrderReceiveAmt(modifyReceiveAmtDTO);

            OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
            orderStatementTaskDTO.setTaskCode(request.getTaskCode());
            orderStatementTaskDTO.setTaskStatus(TaskStatusEnum.BILL_CONFIRM.getCode());
            orderStatementTaskDTO.setStatementId(newStatement.getId());
            orderStatementTaskDTO.setStatementCode(newStatement.getStatementCode());
            orderStatementTaskDTO.setChangeTime(newStatement.getCreatedDt());
            orderStatementTaskService.updateOrderStatementTask(orderStatementTaskDTO);

            transactionManager.commit(transaction);
        } catch (Exception e) {
            log.error("创建客户账单账单失败，原因： {} ,耗时 {}", e, System.currentTimeMillis() - millis);
            transactionManager.rollback(transaction);
            // 创建失败设置客户redis的出账状态为出账失败
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setCheckInDate(request.getStartDate());
            stateDTO.setCheckOutDate(request.getEndDate());
            stateDTO.setOutgoingState(OutgoingStateEnum.CONFIRMED.key);
            stateDTO.setFailureReason(e.getMessage());
            RedisTemplateX.hPut(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode(), JSONObject.toJSONString(stateDTO));
            if (StrUtilX.isNotEmpty(request.getTaskCode())) {
                //创建失败，通知自助结算，并更新状态
                OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
                orderStatementTaskDTO.setTaskCode(request.getTaskCode());
                orderStatementTaskDTO.setFailReason(e.getMessage());
                orderStatementTaskDTO.setTaskStatus(TaskStatusEnum.BILL_CREATE.getCode());
                orderStatementTaskService.updateOrderStatementTask(orderStatementTaskDTO);
            }
        }
    }

    @Override
    public AgentStatementDetailDTO queryStatementDetail(StatementIdDTO request) {
        AgentStatementDetailDTO agentStatementDetailDTO = new AgentStatementDetailDTO();
        AgentStatementPO queryStatement = new AgentStatementPO();
        if (request.getStatementId() != null) {
            queryStatement.setId(request.getStatementId());
        }
        if (StrUtilX.isNotEmpty(request.getStatementCode())) {
            queryStatement.setStatementCode(request.getStatementCode());
        }
        queryStatement = agentStatementMapper.selectOne(queryStatement);
        BeanUtils.copyProperties(queryStatement, agentStatementDetailDTO);
        agentStatementDetailDTO.setStatementId(queryStatement.getId());
        ContactSupplierDTO contactSupplierDTO = agentStatementMapper.selectContact(queryStatement.getAgentCode(), DeleteEnum.STATUS_EXIST.key);
        if (!StringUtils.isEmpty(contactSupplierDTO)) {
            agentStatementDetailDTO.setContactName(contactSupplierDTO.getContactName());
        }
        long days = DateUtilX.getDay(queryStatement.getSettlementDate(), (queryStatement.getRealSettlementDate() == null ? DateUtilX.stringToDate(DateUtilX.dateToString(new Date())) : queryStatement.getRealSettlementDate()));
        agentStatementDetailDTO.setOverdueDays(days > 0 ? (int) days : 0);

        //新增的订单明细数
        QueryUnCheckOutOrderDTO queryUnCheckOutOrderDTO = new QueryUnCheckOutOrderDTO();
        queryUnCheckOutOrderDTO.setAgentCode(agentStatementDetailDTO.getAgentCode());
        queryUnCheckOutOrderDTO.setCompanyCode(request.getCompanyCode());
        queryUnCheckOutOrderDTO.setDateQueryType(1);
        queryUnCheckOutOrderDTO.setPageSize(10000);
        agentStatementDetailDTO.setNewOrderQty(queryUnCheckOutOrder(queryUnCheckOutOrderDTO).getItemList().size());

        //更新的订单明细数
        QueryUpdatedStatementOrderListDTO queryUpdatedStatementOrderListDTO = new QueryUpdatedStatementOrderListDTO();
        queryUpdatedStatementOrderListDTO.setPageSize(10000);
        queryUpdatedStatementOrderListDTO.setStatementId(request.getStatementId());
        agentStatementDetailDTO.setUpdatedOrderQty(queryUpdatedStatementOrderList(queryUpdatedStatementOrderListDTO).getItemList().size());

        //查询是否自助结算单
        String statementCode = orderStatementTaskService.selectOneByStatementCode(queryStatement.getStatementCode());
        if (StrUtilX.isNotEmpty(statementCode)) {
            agentStatementDetailDTO.setIsSettle(1);
        } else {
            agentStatementDetailDTO.setIsSettle(0);
        }

        Example example = new Example(ContactPO.class);
        example.createCriteria().andEqualTo("orgCode", queryStatement.getAgentCode())
                .andEqualTo("active", DeleteEnum.STATUS_EXIST.key)
                .andEqualTo("contactType", 0)
                .andLike("contactRole", "%0%");
        List<ContactPO> contactList = contactMapper.selectByExample(example);
        if (CollUtilX.isNotEmpty(contactList) && contactList.get(0) != null) {
            agentStatementDetailDTO.setContactName(contactList.get(0).getContactName());
            agentStatementDetailDTO.setContactTel(contactList.get(0).getContactTel());
        }
        return agentStatementDetailDTO;
    }

    @Override
    public PaginationSupportDTO<StatementOrderDTO> queryStatementOrderList(QueryStatementOrderListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(request);
        for (StatementOrderDTO statementOrder : list) {
            BigDecimal orgToAgentRate = statementOrder.getOrgToAgentRate();
            if (orgToAgentRate == null) {
                orgToAgentRate = BigDecimal.ONE;
            }
            statementOrder.setReceivedOrgCurrencyAmt(statementOrder.getReceivedAmt().divide(orgToAgentRate, 2, RoundingMode.UP));
            statementOrder.setReceivableOrgCurrencyAmt(statementOrder.getReceivableAmt().divide(orgToAgentRate, 2, RoundingMode.UP));
            statementOrder.setUnreceivedOrgCurrencyAmt(statementOrder.getReceivableOrgCurrencyAmt().subtract(statementOrder.getReceivedOrgCurrencyAmt()).setScale(2, RoundingMode.UP));
            statementOrder.setReceiveOrgCurrencyAmt(statementOrder.getReceiveAmt().divide(orgToAgentRate, 2, RoundingMode.UP));
        }

        PageInfo<StatementOrderDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<StatementOrderDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public PaginationSupportDTO<UnCheckOutOrderDTO> queryUnCheckOutOrder(QueryUnCheckOutOrderDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<UnCheckOutOrderDTO> list = agentStatementOrderMapper.queryUnCheckOutOrder(request);
        for (UnCheckOutOrderDTO unCheckOutOrder : list) {
            BigDecimal orgToAgentRate = unCheckOutOrder.getOrgToAgentRate();
            if (orgToAgentRate == null) {
                orgToAgentRate = BigDecimal.ONE;
            }
            unCheckOutOrder.setReceivedOrgCurrencyAmt(unCheckOutOrder.getReceivedAmt().divide(orgToAgentRate, 2, RoundingMode.UP));
            unCheckOutOrder.setReceivableOrgCurrencyAmt(unCheckOutOrder.getReceivableAmt().divide(orgToAgentRate, 2, RoundingMode.UP));
            unCheckOutOrder.setUnreceivedOrgCurrencyAmt(unCheckOutOrder.getReceivableOrgCurrencyAmt().subtract(unCheckOutOrder.getReceivedOrgCurrencyAmt()).setScale(2, RoundingMode.UP));
        }
        PageInfo<UnCheckOutOrderDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<UnCheckOutOrderDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Transactional
    @Override
    public void addStatementOrderList(AddStatementOrderListDTO request) {
        long millis = System.currentTimeMillis();
        Object o = RedisTemplateX.hashGet(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_BEING_CANCELLED);
            }
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode()), OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HAS_A_BILL_IN_PROGRESS);
        }
        if (request.getStatementId() == null || (CollUtilX.isEmpty(request.getOrderIdList()) &&
                (null == request.getDateQueryType() || StrUtilX.isEmpty(request.getStartDate()) || StrUtilX.isEmpty(request.getEndDate())))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        AgentStatementPO existingStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        if (existingStatement.getStatementStatus().equals(StatementStatusEnum.CANCELED.key)) {
            throw new SysException(ErrorCodeEnum.BILL_HAS_BEEN_CANCELLED_NO_DETAILS_CAN_BE_ADDED);
        }

        //按条件批量增加，查询满足条件的订单
        if (null == request.getOrderIdList() || 0 == request.getOrderIdList().size()) {
            QueryUnCheckOutOrderDTO queryUnCheckOutOrderDTO = new QueryUnCheckOutOrderDTO();
            queryUnCheckOutOrderDTO.setAgentCode(existingStatement.getAgentCode());
            queryUnCheckOutOrderDTO.setCompanyCode(request.getCompanyCode());
            queryUnCheckOutOrderDTO.setDateQueryType(request.getDateQueryType());
            queryUnCheckOutOrderDTO.setStartDate(request.getStartDate());
            queryUnCheckOutOrderDTO.setEndDate(request.getEndDate());
            List<UnCheckOutOrderDTO> list = agentStatementOrderMapper.queryUnCheckOutOrder(queryUnCheckOutOrderDTO);
            if (CollUtilX.isNotEmpty(list)) {
                List<Integer> orderIdList = new ArrayList<>();
                for (UnCheckOutOrderDTO unCheckOutOrderDTO : list) {
                    orderIdList.add(unCheckOutOrderDTO.getOrderId());
                }
                request.setOrderIdList(orderIdList);
            } else {
                return;
            }
        }

        // 1.保存账单明细
        InsertStatementOrderDTO insertStatementOrderDTO = new InsertStatementOrderDTO();
        insertStatementOrderDTO.setStatementId(request.getStatementId());
        insertStatementOrderDTO.setOrderIdList(request.getOrderIdList());
        insertStatementOrderDTO.setCompanyCode(existingStatement.getCompanyCode());
        agentStatementOrderMapper.saveBatchStatementOrder(insertStatementOrderDTO);

        // 2.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        updateStatementAmount(statementIdDTO);

        //记录日志
        AgentStatementPO updatedStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        StatementAmtLogDTO dto = new StatementAmtLogDTO();
        dto.setNewStatementAmt(updatedStatement.getStatementAmt());
        dto.setOriginalStatementAmt(existingStatement.getStatementAmt());
        dto.setType(0);
        dto.setContentType(0);
        dto.setOrderIdList(request.getOrderIdList());
        dto.setCreatedBy(request.getOperator());
        dto.setCurrency(request.getCurrency());
        dto.setStatementId(request.getStatementId());
        statementAmtLogService.insertStatementAmtLog(dto);

        // 3.更新订单对账状态为出账中，并将订单加锁
        UpdateOrderFinanceDTO updateOrderFinanceDTO = new UpdateOrderFinanceDTO();
        updateOrderFinanceDTO.setStatementId(request.getStatementId());
        updateOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
        updateOrderFinanceDTO.setFinanceLockStatus(0);
        updateOrderFinanceDTO.setOrderIdList(request.getOrderIdList());
        agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);

        // 4.账单为对账中时，新加进来的订单明细加锁
        if (existingStatement.getStatementStatus().equals(StatementStatusEnum.CHECKING.key)) {
            List<Integer> orderIdList = request.getOrderIdList();
            for (Integer orderId : orderIdList) {
                FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
                financeLockOrderDTO.setOrderId(orderId);
                financeLockOrderDTO.setLockStatus(1);
                financeLockService.lockOrder(financeLockOrderDTO);
            }
        }
    }

    @Transactional
    @Override
    public void deleteStatementOrderList(DeleteStatementOrderListDTO request) {
        AgentStatementPO targetStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());

        //List<Integer> orderIds = new ArrayList<>();
        //按条件批量删除，查询满足条件的订单
        if (null == request.getStatementOrderIdList() || 0 == request.getStatementOrderIdList().size()) {
            QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
            queryStatementOrderListDTO.setStatementId(request.getStatementId());
            queryStatementOrderListDTO.setDateQueryType(request.getDateQueryType());
            queryStatementOrderListDTO.setStartDate(request.getStartDate());
            queryStatementOrderListDTO.setEndDate(request.getEndDate());
            List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
            if (CollUtilX.isNotEmpty(list)) {
                List<Integer> statementOrderIdList = new ArrayList<>();
                for (StatementOrderDTO statementOrderDTO : list) {
                    statementOrderIdList.add(statementOrderDTO.getStatementOrderId());
                }
                request.setStatementOrderIdList(statementOrderIdList);
            } else {
                return;
            }
        }

        // 1.更新结算状态为可出账
        UpdateOrderFinanceDTO updateOrderFinanceDTO = new UpdateOrderFinanceDTO();
        updateOrderFinanceDTO.setStatementId(request.getStatementId());
        updateOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
        // 移除明细时,订单解锁
        updateOrderFinanceDTO.setFinanceLockStatus(0);
        updateOrderFinanceDTO.setStatementOrderIdList(request.getStatementOrderIdList());
        agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);

        // 2.删除账单明细
        List<Integer> orderIds = agentStatementOrderMapper.selectAgentStatementOrderListByOrderId(request.getStatementOrderIdList());

        List<List<Integer>> lists = Lists.partition(request.getStatementOrderIdList(), 200);
        for (List<Integer> list : lists) {
            agentStatementOrderMapper.deleteAgentStatementOrderList(list);
        }

        // 3.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        updateStatementAmount(statementIdDTO);

        //记录日志
        AgentStatementPO updatedStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        StatementAmtLogDTO dto = new StatementAmtLogDTO();
        dto.setNewStatementAmt(updatedStatement.getStatementAmt());
        dto.setOriginalStatementAmt(targetStatement.getStatementAmt());
        dto.setType(0);
        dto.setContentType(2);
        dto.setOrderIdList(orderIds);
        dto.setCreatedBy(request.getOperator());
        dto.setCurrency(request.getCurrency());
        dto.setStatementId(request.getStatementId());
        statementAmtLogService.insertStatementAmtLog(dto);
    }

    @Transactional
    @Override
    public void modifyStatementStatus(ModifyStatementStatusDTO request) {
        // 1.确认账单前检查账单金额是否发生变化   2024-05-23：修改确认账单不校验金额是否发送变化
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key)) {
            StatementIdDTO statementIdDTO = new StatementIdDTO();
            statementIdDTO.setStatementId(request.getStatementId());
            int updateCount = agentStatementOrderMapper.queryStatementUpdateOrder(statementIdDTO);
            if (updateCount > 0 && (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CONFIRMED.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key))) {
                throw new SysException(ErrorCodeEnum.BILL_AMOUNT_HAS_CHANGED_PLEASE_UPDATE_THE_BILL_FIRST);
            }
            AgentStatementPO statementToCheck = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
            if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)
                    && (statementToCheck.getReceivedAmt() == null || statementToCheck.getReceivedAmt().compareTo(BigDecimal.ZERO) != 0
                    || statementToCheck.getUnconfirmedPaidAmt() == null || statementToCheck.getUnconfirmedPaidAmt().compareTo(BigDecimal.ZERO) != 0
                    || statementToCheck.getUnconfirmedReceivedAmt() == null || statementToCheck.getUnconfirmedReceivedAmt().compareTo(BigDecimal.ZERO) != 0)) {
                throw new SysException(ErrorCodeEnum.BILLS_HAVE_BEEN_RECEIVED_OR_PAID_NO_CANCEL);
            }
        }
        // 2.更新账单状态
        AgentStatementPO statementUpdate = new AgentStatementPO();
        statementUpdate.setId(request.getStatementId());

        UpdateOrderFinanceDTO updateOrderFinanceDTO = new UpdateOrderFinanceDTO();
        updateOrderFinanceDTO.setStatementId(request.getStatementId());
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key) || request.getStatementStatus().equals(StatementStatusEnum.CONFIRMED.key)) {
            QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
            queryStatementOrderListDTO.setStatementId(request.getStatementId());
            List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
            for (StatementOrderDTO statementOrderDTO : list) {
                FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
                Example example = new Example(AgentStatementOrderPO.class);
                example.createCriteria().andEqualTo("orderCode", statementOrderDTO.getOrderCode()).andEqualTo("statementId", request.getStatementId());
                AgentStatementOrderPO orderToLock = agentStatementOrderMapper.selectOneByExample(example);
                financeLockOrderDTO.setOrderId(orderToLock.getOrderId());
                financeLockOrderDTO.setLockStatus(1);
                financeLockService.lockOrder(financeLockOrderDTO);
            }
        } else if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
            QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
            queryStatementOrderListDTO.setStatementId(request.getStatementId());
            List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
            for (StatementOrderDTO statementOrderDTO : list) {
                FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
                Example example = new Example(AgentStatementOrderPO.class);
                example.createCriteria().andEqualTo("orderCode", statementOrderDTO.getOrderCode()).andEqualTo("statementId", request.getStatementId());
                AgentStatementOrderPO orderToUnlock = agentStatementOrderMapper.selectOneByExample(example);
                financeLockOrderDTO.setOrderId(orderToUnlock.getOrderId());
                financeLockOrderDTO.setLockStatus(0);
                financeLockService.lockOrder(financeLockOrderDTO);
            }
        }


        // 3.更新订单对账状态为已对账
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CONFIRMED.key)) {
            AgentStatementPO confirmedStatement = agentStatementMapper.selectOne(statementUpdate);
            statementUpdate.setUpdatedBy(request.getOperator());
            statementUpdate.setUpdatedDt(new Date());
            if (confirmedStatement.getReceivedAmt() != null && confirmedStatement.getStatementAmt().compareTo(confirmedStatement.getReceivedAmt()) == 0) {
                updateOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                statementUpdate.setSettlementStatus(1);
                statementUpdate.setRealSettlementDate(new Date());
                updateOrderFinanceDTO.setIsUpdateSettlementStatus(1);
                updateOrderFinanceDTO.setIsUpdateSettlementAmount(1);
            }
            // 已经对账就直接锁定
            updateOrderFinanceDTO.setFinanceLockStatus(1);
            agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);

        } else if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
            statementUpdate.setUpdatedBy(request.getOperator());
            statementUpdate.setUpdatedDt(new Date());
            //查询该订单下面的所有账单
            QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
            queryStatementOrderListDTO.setStatementId(request.getStatementId());
            List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);

            //相当于将所有的订单明细删除
            if (CollUtilX.isNotEmpty(list)) {
                DeleteStatementOrderListDTO deleteStatementOrderListDTO = new DeleteStatementOrderListDTO();
                deleteStatementOrderListDTO.setStatementId(request.getStatementId());
                List<Integer> orderList = new ArrayList<Integer>();
                for (StatementOrderDTO statementOrderDTO : list) {
                    orderList.add(statementOrderDTO.getStatementOrderId());
                }
                deleteStatementOrderListDTO.setStatementOrderIdList(orderList);
                deleteStatementOrderList(deleteStatementOrderListDTO);
            }
            updateOrderFinanceDTO.setFinanceLockStatus(0);
            agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);
        }
        statementUpdate.setStatementStatus(request.getStatementStatus());
        agentStatementMapper.updateByPrimaryKeySelective(statementUpdate);
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CHECKING.key) || Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
            QueryUpdatedStatementOrderListDTO queryUpdatedStatementOrderListDTO = new QueryUpdatedStatementOrderListDTO();
            queryUpdatedStatementOrderListDTO.setPageSize(10000);
            queryUpdatedStatementOrderListDTO.setStatementId(request.getStatementId());
            int size = queryUpdatedStatementOrderList(queryUpdatedStatementOrderListDTO).getItemList().size();
            if (size > 0) {
//                return new Response(ResultCodeEnum.FAILURE.code,null,"有尚未更新的账单明细，无法确认账单");
                StatementIdDTO updateStatementDto = new StatementIdDTO();
                updateStatementDto.setStatementId(request.getStatementId());
                updateStatementDto.setOperator(request.getOperator());
                updateStatementOrderList(updateStatementDto);
            }
        }
//        if (request.getStatementStatus() == StatementStatusEnum.CHECKING.key || request.getStatementStatus() == StatementStatusEnum.CONFIRMED.key){
//            QueryStatementOrderListDTO  queryStatementOrderListDTO  = new QueryStatementOrderListDTO();
//            queryStatementOrderListDTO.setStatementId(request.getStatementId());
//            List<StatementOrderDTO> list =agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
//            for (StatementOrderDTO statementOrderDTO: list) {
//                FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
//                Example example = new Example(AgentStatementOrderPO.class);
//                example.createCriteria().andEqualTo("orderCode",statementOrderDTO.getOrderCode());
//                AgentStatementOrderPO agentStatementOrderPO = agentStatementOrderMapper.selectOneByExample(example);
//                financeLockOrderDTO.setOrderId(agentStatementOrderPO.getOrderId());
//                financeLockOrderDTO.setLockStatus(1);
//                financeLockService.lockOrder(financeLockOrderDTO);
//            }
//        }else if (request.getStatementStatus() == StatementStatusEnum.CANCELED.key){
//            QueryStatementOrderListDTO  queryStatementOrderListDTO  = new QueryStatementOrderListDTO();
//            queryStatementOrderListDTO.setStatementId(request.getStatementId());
//            List<StatementOrderDTO> list =agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
//            for (StatementOrderDTO statementOrderDTO: list) {
//                FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
//                Example example = new Example(AgentStatementOrderPO.class);
//                example.createCriteria().andEqualTo("orderCode",statementOrderDTO.getOrderCode());
//                AgentStatementOrderPO agentStatementOrderPO = agentStatementOrderMapper.selectOneByExample(example);
//                financeLockOrderDTO.setOrderId(agentStatementOrderPO.getOrderId());
//                financeLockOrderDTO.setLockStatus(0);
//                financeLockService.lockOrder(financeLockOrderDTO);
//            }
//        }
    }

    @Transactional
    @Override
    public void cancelStatement(ModifyStatementStatusDTO request) {
        // 1.确认账单前检查账单金额是否发生变化   2024-05-23：修改确认账单不校验金额是否发送变化
        if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
            StatementIdDTO statementIdDTO = new StatementIdDTO();
            statementIdDTO.setStatementId(request.getStatementId());
            AgentStatementPO statementToCancel = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
            if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)
                    && (statementToCancel.getReceivedAmt() == null || statementToCancel.getReceivedAmt().compareTo(BigDecimal.ZERO) != 0
                    || statementToCancel.getUnconfirmedPaidAmt() == null || statementToCancel.getUnconfirmedPaidAmt().compareTo(BigDecimal.ZERO) != 0
                    || statementToCancel.getUnconfirmedReceivedAmt() == null || statementToCancel.getUnconfirmedReceivedAmt().compareTo(BigDecimal.ZERO) != 0)) {
                throw new SysException(ErrorCodeEnum.BILLS_HAVE_BEEN_RECEIVED_OR_PAID_NO_CANCEL);
            }
        }
        OutgoingStateDTO stateDTO = new OutgoingStateDTO();
        stateDTO.setOutgoingState(OutgoingStateEnum.CHECKING.key);
        Integer integer = request.getOrderNumber() < 60 ? 2 : request.getOrderNumber() / 50;
        stateDTO.setTime(integer);
        RedisTemplateX.hPut(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));
        // 异步处理账单明细
        CompletableFuture.runAsync(() ->
                deleteStatementOrder(request)
        );
    }

    @Override
    public PaginationSupportDTO<UpdatedStatementOrderDTO> queryUpdatedStatementOrderList(QueryUpdatedStatementOrderListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<UpdatedStatementOrderDTO> list = agentStatementOrderMapper.queryUpdatedStatementOrderList(request);
        PageInfo<UpdatedStatementOrderDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<UpdatedStatementOrderDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Transactional
    @Override
    public void updateStatementOrderList(StatementIdDTO request) {
        AgentStatementPO oldAgentStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        // 1.更新账单明细
        agentStatementOrderMapper.updateStatementOrderList(request);

        // 2.更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        updateStatementAmount(statementIdDTO);

        //更新明细时为订单加锁
        List<Integer> orderIds = new ArrayList<>();
        QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
        queryStatementOrderListDTO.setStatementId(request.getStatementId());
        List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
        AgentStatementPO currentStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        for (StatementOrderDTO statementOrderDTO : list) {
            FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
            Example example = new Example(AgentStatementOrderPO.class);
            example.createCriteria().andEqualTo("orderCode", statementOrderDTO.getOrderCode()).andEqualTo("statementId", request.getStatementId());
            AgentStatementOrderPO orderForLocking = agentStatementOrderMapper.selectOneByExample(example);
            financeLockOrderDTO.setOrderId(orderForLocking.getOrderId());
            if (currentStatement != null && currentStatement.getStatementStatus() != null && currentStatement.getStatementStatus().equals(StatementStatusEnum.UN_CHECK.key)) {
                financeLockOrderDTO.setLockStatus(0);
            } else {
                financeLockOrderDTO.setLockStatus(1);
            }
            financeLockService.lockOrder(financeLockOrderDTO);
            orderIds.add(statementOrderDTO.getOrderId());
        }

        //记录日志
        AgentStatementPO finalStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        StatementAmtLogDTO dto = new StatementAmtLogDTO();
        dto.setNewStatementAmt(finalStatement.getStatementAmt());
        dto.setOriginalStatementAmt(oldAgentStatement.getStatementAmt());
        dto.setType(0);
        dto.setContentType(1);
        dto.setOrderIdList(orderIds);
        dto.setCreatedBy(request.getOperator());
        dto.setCurrency(request.getCurrency());
        dto.setStatementId(request.getStatementId());
        statementAmtLogService.insertStatementAmtLog(dto);
    }

    @Transactional
    @Override
    public Integer notifyCollectionOfStatement(NotifyCollectionOfStatementDTO request) {
        // 1.判断账单应收金额是否发生变化
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        AgentStatementPO notifyStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());

        // 2.更新已通知金额
        AgentStatementPO collectionUpdate = new AgentStatementPO();
        collectionUpdate.setId(request.getStatementId());
        collectionUpdate.setUnconfirmedReceivedAmt(notifyStatement.getUnconfirmedReceivedAmt().add(request.getAmt()));
        agentStatementMapper.updateByPrimaryKeySelective(collectionUpdate);

        // 3.创建工单
        NotifyCollectionDTO notifyCollectionDTO = new NotifyCollectionDTO();
        BeanUtils.copyProperties(request, notifyCollectionDTO);
        notifyCollectionDTO.setBusinessType(BusinessTypeEnum.AGENTSTATEMENT.key);
        notifyCollectionDTO.setNotifyItemDTOList(Collections.singletonList(new NotifyItemDTO(
                notifyStatement.getStatementCode(),
                request.getAmt()
        )));
        notifyCollectionDTO.setCollectionAmt(request.getAmt());
        notifyCollectionDTO.setOrgCode(notifyStatement.getAgentCode());
        notifyCollectionDTO.setOrgName(notifyStatement.getAgentName());
        notifyCollectionDTO.setCompanyCode(notifyStatement.getCompanyCode());
        notifyCollectionDTO.setContent(RedisTemplateX.get(RedisKey.LOG_CODE + "O000025"));
        notifyCollectionDTO.setCurrency(notifyStatement.getCurrency());
        notifyCollectionDTO.setPhotoList(request.getPhotoList());
        notifyCollectionDTO.setCreatedBy(request.getOperator());
        notifyCollectionDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        return financeNofityService.notifyCollection(notifyCollectionDTO);
    }

    @Transactional
    @Override
    public Integer notifyPaymentOfStatement(NotifyPaymentOfStatementDTO request) {
        // 1.判断账单应收金额是否发生变化
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        AgentStatementPO paymentStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());

        // 2.更新已通知金额
        AgentStatementPO paymentUpdate = new AgentStatementPO();
        paymentUpdate.setId(request.getStatementId());
        paymentUpdate.setUnconfirmedPaidAmt(paymentStatement.getUnconfirmedPaidAmt().add(request.getAmt()));
        agentStatementMapper.updateByPrimaryKeySelective(paymentUpdate);

        // 3.创建工单
        NotifyPaymentDTO notifyPaymentDTO = new NotifyPaymentDTO();
        BeanUtils.copyProperties(request, notifyPaymentDTO);
        notifyPaymentDTO.setPhotoList(request.getPhotoList());
        notifyPaymentDTO.setBusinessType(BusinessTypeEnum.AGENTSTATEMENT.key);
        notifyPaymentDTO.setNotifyItemDTOList(Collections.singletonList(new NotifyItemDTO(
                paymentStatement.getStatementCode(),
                request.getAmt()
        )));
        notifyPaymentDTO.setPaymentAmt(request.getAmt());
        notifyPaymentDTO.setOrgCode(paymentStatement.getAgentCode());
        notifyPaymentDTO.setOrgName(paymentStatement.getAgentName());
        notifyPaymentDTO.setCompanyCode(paymentStatement.getCompanyCode());
        notifyPaymentDTO.setContent(RedisTemplateX.get(RedisKey.LOG_CODE + "O000025"));
        notifyPaymentDTO.setCurrency(paymentStatement.getCurrency());
        notifyPaymentDTO.setCreatedBy(request.getOperator());
        notifyPaymentDTO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        return financeNofityService.notifyPayment(notifyPaymentDTO);
    }

    @Transactional
    @Override
    public void modifyStatementName(ModifyStatementNameDTO request) {
        // 1.更新账单名称
        AgentStatementPO nameUpdate = new AgentStatementPO();
        nameUpdate.setId(request.getStatementId());
        nameUpdate.setStatementName(request.getStatementName());
        agentStatementMapper.updateByPrimaryKeySelective(nameUpdate);
    }

    @Transactional
    @Override
    public void modifySettlementDate(ModifySettlementDateDTO request) {
        // 1.更新账单结算日期
        AgentStatementPO dateUpdate = new AgentStatementPO();
        dateUpdate.setId(request.getStatementId());
        dateUpdate.setSettlementDate(DateUtilX.stringToDate(request.getSettlementDate()));
        agentStatementMapper.updateByPrimaryKeySelective(dateUpdate);
    }

    @Override
    public void updateStatementOrderReceiveAmt(ModifySettlementOrderReceiveAmtDTO request) {
        AgentStatementPO receiveAmtStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        if (receiveAmtStatement.getStatementStatus().equals(StatementStatusEnum.CANCELED.key)) {
            throw new SysException(ErrorCodeEnum.BILL_HAS_BEEN_CANCELLED_NO_DETAILS_CAN_BE_ADDED);
        }

        AgentStatementOrderPO receiveAmtOrder = agentStatementOrderMapper.selectByPrimaryKey(request.getStatementOrderId());
        if (receiveAmtOrder == null) {
            throw new SysException(ErrorCodeEnum.BILL_HAS_BEEN_CANCELLED_NO_DETAILS_CAN_BE_ADDED);
        }

        List<Integer> orderIdList = new ArrayList<>();
        orderIdList.add(receiveAmtOrder.getOrderId());

        // 1、更新账单中订单的要收金额
        agentStatementOrderMapper.updateStatementOrderReceiveAmtById(request);

        // 2、更新账单金额
        StatementIdDTO statementIdDTO = new StatementIdDTO();
        statementIdDTO.setStatementId(request.getStatementId());
        updateStatementAmount(statementIdDTO);

        //记录日志
        AgentStatementPO finalStatement = agentStatementMapper.selectByPrimaryKey(request.getStatementId());
        StatementAmtLogDTO dto = new StatementAmtLogDTO();
        dto.setNewStatementAmt(finalStatement.getStatementAmt());
        dto.setOriginalStatementAmt(receiveAmtStatement.getStatementAmt() == null ? BigDecimal.ZERO: receiveAmtStatement.getStatementAmt());
        dto.setType(0);
        dto.setContentType(1);
        dto.setOrderIdList(orderIdList);
        dto.setCreatedBy(request.getOperator());
        dto.setCurrency(finalStatement.getCurrency());
        dto.setStatementId(request.getStatementId());
        statementAmtLogService.insertStatementAmtLog(dto);
    }

    @Override
    public void createOrderStatementTask(CreateOrderStatementTaskDTO request) {
        OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
        //任务编码生成
        String key = SystemCodeEnum.SEQ_ORDER_STATEMENT_TASK_CODE.code;
        String code = RedisTemplateX.lRightPop(key);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", key);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(key);
        }
        orderStatementTaskDTO.setTaskCode(code);
        orderStatementTaskDTO.setTaskSource(request.getTaskSource());
        orderStatementTaskDTO.setOrderCode(request.getOrderCode());
        orderStatementTaskDTO.setStatementCreateStatus(StatementTaskStatusEnum.UNTREATED.key);
        orderStatementTaskDTO.setStatementConfirmStatus(StatementTaskStatusEnum.UNTREATED.key);
        orderStatementTaskDTO.setWorkOrderStatus(StatementTaskStatusEnum.UNTREATED.key);
        orderStatementTaskDTO.setTaskJson(JSON.toJSONString(request));
        orderStatementTaskDTO.setTaskStatus(TaskStatusEnum.BILL_CREATE.getCode());
        orderStatementTaskDTO.setAgentCode(request.getAgentCode());
        orderStatementTaskDTO.setCreatedDt(new Date());
        orderStatementTaskService.insertOrderStatementTask(orderStatementTaskDTO);

        // 查询此订单是否有任务进行中，有则放入等待队列
        if (RedisTemplateX.sIsMember(RedisKey.ORDER_STATEMENT_TASK_ING, request.getOrderCode())) {
            RedisTemplateX.sAdd(RedisKey.ORDER_STATEMENT_TASK_WAIT + request.getOrderCode(), code);
        } else {
            RedisTemplateX.sAdd(RedisKey.ORDER_STATEMENT_TASK_ING, request.getOrderCode());
            //开始任务，第一步创建账单
            List<String> list = new ArrayList<>();
            list.add(code);
            RedisTemplateX.lLeftPushAll(RedisKey.ORDER_STATEMENT_TASK_CREATE_STATEMENT, list);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void repairOrderAdditionalCharges(ModifyStatementStatusDTO request) {
        if (request.getStatementId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        if (request.getSettlementStrategy() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SETTLEMENTSTRATEGY);
        }
        List<AdditionalChargesDTO> dtoList = new ArrayList<>();
        QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
        queryStatementOrderListDTO.setStatementId(request.getStatementId());
        List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
        for (StatementOrderDTO statementOrderDTO : list) {
            // 协议账单 不等于0并且没有错误
            if (statementOrderDTO.getRateDifferenceValue() != null && BigDecimal.ZERO.compareTo(statementOrderDTO.getRateDifferenceValue()) != 0 && StrUtilX.isEmpty(statementOrderDTO.getSettlementCostErrorDesc())) {
                AdditionalChargesDTO additionalChargesDTO = new AdditionalChargesDTO();
                additionalChargesDTO.setOrderId(statementOrderDTO.getOrderId());
                additionalChargesDTO.setOrderCode(statementOrderDTO.getOrderCode());
                additionalChargesDTO.setAdditionalCharges(statementOrderDTO.getRateDifferenceValue());
                dtoList.add(additionalChargesDTO);
            }
        }

        // 当前时间
        Date currentDate = DateUtilX.getCurrentDate();

        // 协议结算成本需要修改附加费
        if (request.getSettlementStrategy() != null && request.getSettlementStrategy() == 1 && CollUtilX.isNotEmpty(dtoList)) {
            // 修改订单附加费
            Response<Object> response = orderRemote.modifyAdditionalCharges(dtoList);
            if (response.isError()) {
                // 异步执行
                CompletableFuture.runAsync(() -> {
                    try {
                        // 修改异常状态
                        AgentStatementPO repairStatement = new AgentStatementPO();
                        repairStatement.setId(request.getStatementId());
                        repairStatement.setRepairRateDifference(3);
                        repairStatement.setUpdatedDt(currentDate);
                        repairStatement.setUpdatedBy(request.getOperator());
                        agentStatementMapper.updateByPrimaryKeySelective(repairStatement);
                    } catch (Exception e) {
                        log.error("修改异常状态异常", e);
                    }
                });
                log.error("订单附加项补汇差，修改订单附加费失败，request={}，response={}", JSONUtil.toJsonStr(dtoList), JSONUtil.toJsonStr(response));
                throw new SysException("订单附加项补汇差，修改订单附加费失败");
            }

            // 注册事务同步器
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 事务提交后，加入到统计报表
                    CompletableFuture.runAsync(() -> {
                        try {
                            for (AdditionalChargesDTO dto : dtoList) {
                                // 加入到统计报表队列中
                                stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, dto.getOrderCode());
                            }
                        } catch (Exception e) {
                            log.error("【协议结算成本需要修改附加费】加入到统计报表队列异常，error:", e);
                        }
                    });
                }
            });
        }

        // 修改异常状态
        AgentStatementPO updatedStatement = new AgentStatementPO();
        updatedStatement.setId(request.getStatementId());
        updatedStatement.setRepairRateDifference(2);
        updatedStatement.setUpdatedDt(currentDate);
        updatedStatement.setUpdatedBy(request.getOperator());
        agentStatementMapper.updateByPrimaryKeySelective(updatedStatement);

        // 记录日志
        StatementAmtLogPO amtLog = new StatementAmtLogPO();
        amtLog.setType(0);
        amtLog.setContent("操作了“自动补汇差”按钮");
        amtLog.setCreatedBy(request.getOperator());
        amtLog.setCreatedDt(currentDate);
        amtLog.setStatementId(request.getStatementId());
        statementAmtLogMapper.insert(amtLog);
    }

    @Override
    public Integer checkAgentSettlementCostError(ModifyStatementStatusDTO request) {
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        int i = agentStatementMapper.checkAgentSettlementCostError(request);
        if (i > 0) {
            return 1;
        }
        return 0;
    }

    private void updateStatementAmount(StatementIdDTO request) {
        QueryStatementTotalAmountDTO queryStatementTotalAmountDTO = agentStatementOrderMapper.queryStatementTotalAmount(request);
        if (queryStatementTotalAmountDTO == null) {
            queryStatementTotalAmountDTO = new QueryStatementTotalAmountDTO();
            queryStatementTotalAmountDTO.setStatementId(request.getStatementId());
            queryStatementTotalAmountDTO.setAmount("0");
        }
        agentStatementOrderMapper.updateStatementAmount(queryStatementTotalAmountDTO);
    }


    /**
     * 异步创建插入账单
     */
    public void asyncCreatBill(CreateAgentStatementDTO request) {
        // 异步处理
        asyncCreatBill.execute(new Runnable() {
            @Override
            public void run() {
                TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
                long millis = System.currentTimeMillis();
                AgentStatementPO asyncStatement = new AgentStatementPO();
                try {
                    // 获取分销商币种
                    AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()), AgentAccountConfig.class);
                    if (agentAccountConfig == null) {
                        throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
                    }
                    // 获取商家币种
                    OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                    try {
                        // 1.创建账单
                        BeanUtils.copyProperties(request, asyncStatement);
                        asyncStatement.setStatementStatus(StatementStatusEnum.UN_CHECK.key);
                        asyncStatement.setStatementAmt(BigDecimal.ZERO);
                        asyncStatement.setReceivedAmt(BigDecimal.ZERO);
                        asyncStatement.setUnreceivedAmt(BigDecimal.ZERO);
                        asyncStatement.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
                        asyncStatement.setUnconfirmedPaidAmt(BigDecimal.ZERO);
                        asyncStatement.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
                        asyncStatement.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
                        asyncStatement.setSettlementStatus(0);
                        asyncStatement.setSettlementDate(DateUtilX.stringToDate(request.getSettlementDate()));
                        asyncStatement.setCreatedBy(request.getOperator());
                        asyncStatement.setCreatedDt(new Date());
                        asyncStatement.setCurrency(agentAccountConfig.getSettlementCurrency());
                        asyncStatement.setRate(getRateToOrgCurrency(asyncStatement.getCurrency()));
                        //获取编码
                        String code = RedisTemplateX.lRightPop(SystemCodeEnum.AGENTSTATEMENTCODE.code);
                        if (null == code) {
                            Map<String, String> requestMap = new HashMap<>();
                            requestMap.put("seqName", SystemCodeEnum.AGENTSTATEMENTCODE.code);
                            sequenceRemote.createCode(requestMap);
                            code = RedisTemplateX.lRightPop(SystemCodeEnum.AGENTSTATEMENTCODE.code);
                        }
                        asyncStatement.setStatementCode(code);
                        // 结算策略(0按下单 1按结算成本)
                        asyncStatement.setSettlementStrategy(agentAccountConfig.getSettlementStrategy());
                        // 设置扣减额度账户
                        if (StrUtilX.isNotEmpty(agentAccountConfig.getLineAccount())) {
                            asyncStatement.setSubtractLineAccount(agentAccountConfig.getLineAccount());
                        } else {
                            asyncStatement.setSubtractLineAccount(agentAccountConfig.getAgentCode());
                        }
                        agentStatementMapper.insert(asyncStatement);
                    } catch (Exception e) {
                        log.error("创建客户账单表失败", e);
                        throw new RuntimeException("创建客户账单表失败");
                    }
                    // 2.保存账单明细
                    try {
                        InsertStatementOrderDTO insertStatementOrderDTO = new InsertStatementOrderDTO();
                        BeanUtils.copyProperties(request, insertStatementOrderDTO);
                        insertStatementOrderDTO.setStatementId(asyncStatement.getId());
                        boolean settlementCostError = false;// 结算成本异常标识
                        boolean repairRateDifference = false;// 补汇差标识
                        // 查询账单明细订单ID，批量插入到账单
                        int pageSize = 500;
                        int pageNum = 1;
                        while (true) {
                            Page<StatementOrderPO> page = new Page<>(pageNum, pageSize);
                            List<StatementOrderPO> statementOrders = agentStatementOrderMapper.getStatementOrder(insertStatementOrderDTO, page);
                            ++pageNum;
                            // 按结算成本
                            if (agentAccountConfig.getSettlementStrategy() != null && agentAccountConfig.getSettlementStrategy() == 1) {
                                repairRateDifference = true;
                                Set<String> orderCodes = statementOrders.stream().map(StatementOrderPO::getSupplyOrderCode).collect(Collectors.toSet());
                                List<ProtocolOrderSettlementCostPO> poList = protocolOrderSettlementCostMapper.selectList(new LambdaQueryWrapper<ProtocolOrderSettlementCostPO>()
                                        .in(ProtocolOrderSettlementCostPO::getOrderCode, orderCodes).eq(ProtocolOrderSettlementCostPO::getDeleted, 0).eq(ProtocolOrderSettlementCostPO::getMatchingStatus, 1));
                                if (CollUtilX.isNotEmpty(poList)) {
                                    Map<String, ProtocolOrderSettlementCostPO> firstInfoMap = new HashMap<>();
                                    Map<String, BigDecimal> swipingCardAmountTotalMap = new HashMap<>();
                                    Map<String, BigDecimal> settleAmountTotalMap = new HashMap<>();
                                    for (ProtocolOrderSettlementCostPO settlementCost : poList) {
                                        if (!firstInfoMap.containsKey(settlementCost.getOrderCode())) {
                                            firstInfoMap.put(settlementCost.getOrderCode(), settlementCost);
                                        }
                                        if (swipingCardAmountTotalMap.containsKey(settlementCost.getOrderCode())) {
                                            if (settlementCost.getSwipingCardAmount() != null) {
                                                BigDecimal swipingCardAmount = swipingCardAmountTotalMap.get(settlementCost.getOrderCode()).add(settlementCost.getSwipingCardAmount());
                                                swipingCardAmountTotalMap.put(settlementCost.getOrderCode(), swipingCardAmount);
                                            }
                                        } else {
                                            if (settlementCost.getSwipingCardAmount() != null) {
                                                swipingCardAmountTotalMap.put(settlementCost.getOrderCode(), settlementCost.getSwipingCardAmount());
                                            }
                                        }
                                        if (settleAmountTotalMap.containsKey(settlementCost.getOrderCode())) {
                                            if (settlementCost.getSettleAmount() != null) {
                                                BigDecimal settleAmount = settleAmountTotalMap.get(settlementCost.getOrderCode()).add(settlementCost.getSettleAmount());
                                                settleAmountTotalMap.put(settlementCost.getOrderCode(), settleAmount);
                                            }
                                        } else {
                                            if (settlementCost.getSettleAmount() != null) {
                                                settleAmountTotalMap.put(settlementCost.getOrderCode(), settlementCost.getSettleAmount());
                                            }
                                        }
                                    }
                                    for (StatementOrderPO statementOrder : statementOrders) {
                                        ProtocolOrderSettlementCostPO protocolCost = firstInfoMap.get(statementOrder.getSupplyOrderCode());
                                        if (protocolCost == null) {
                                            continue;
                                        }
                                        statementOrder.setSwipingCardAmountCurrency(protocolCost.getSwipingCardAmountCurrency());// 刷卡金额币种 取第一条的数据
                                        statementOrder.setSettleAmountCurrency(protocolCost.getSettleAmountCurrency());// 结算金额币种 取第一条的数据
                                        statementOrder.setSwipingCardRate(protocolCost.getSwipingCardRate() == null ? null : protocolCost.getSwipingCardRate().setScale(8, RoundingMode.UP));// 刷卡汇率 取第一条的数据
                                        statementOrder.setSwipingCardDt(protocolCost.getSwipingCardDt());// 刷卡时间 取第一条的数据
                                        statementOrder.setSwipingCardAmount(swipingCardAmountTotalMap.get(statementOrder.getSupplyOrderCode()));// 刷卡金额(订单下刷卡金额的总和)
                                        statementOrder.setSettleAmount(settleAmountTotalMap.get(statementOrder.getSupplyOrderCode()));// 结算金额(订单下结算金额的总和)
                                        if (statementOrder.getBaseCurrency().compareTo(new BigDecimal(protocolCost.getSettleAmountCurrency())) == 0) {
                                            statementOrder.setCollectionRate(BigDecimal.ONE);// 收款汇率=(结算成本币种转商家币种汇率*商家币种转客户币种汇率)
                                        } else {
                                            // 查询汇率
                                            BigDecimal supplyToOrgRate = RedisUtil.getRateToOrgCurrency(protocolCost.getSettleAmountCurrency(), CompanyDTO.COMPANY_CODE);
                                            BigDecimal orgToAgentRate = RedisUtil.getRateToTargetCurrency(Integer.parseInt(orgDTO.getOrgCurrency()), CompanyDTO.COMPANY_CODE, statementOrder.getBaseCurrency().intValue());
                                            statementOrder.setCollectionRate(supplyToOrgRate.multiply(orgToAgentRate).setScale(8, RoundingMode.UP));// 收款汇率=(结算成本币种转商家币种汇率*商家币种转客户币种汇率)
                                        }
                                        statementOrder.setCollectionAmount(statementOrder.getSettleAmount().multiply(statementOrder.getCollectionRate()).setScale(2, RoundingMode.UP));// 成本收款金额=结算金额/收款汇率

                                        // 当：-(应收金额*5%)<=成本收款金额-应收金额<=应收金额*5% 汇差=成本收款金额-应收 本次要收=成本收款金额(客户币种)-已收
                                        // 当：-(应收金额*5%)>=成本收款金额-应收金额 应收金额*5%<=当成本收款金额-应收金额 不记录汇差信息，记录结算成本异常 本次要收=未收
                                        BigDecimal receivableAmt = statementOrder.getReceivableAmt().multiply(settingsConstant.getProtocolOrderSettlementCostPercent().divide(new BigDecimal(100), 2, RoundingMode.UP));
                                        BigDecimal tempAmt = statementOrder.getCollectionAmount().subtract(statementOrder.getReceivableAmt());
                                        if (tempAmt.abs().compareTo(receivableAmt.abs()) <= 0) {
                                            statementOrder.setRateDifferenceValue(statementOrder.getCollectionAmount().subtract(statementOrder.getReceivableAmt()));// 汇差=成本收款金额(客户币种)-应收
                                            statementOrder.setReceiveAmt(statementOrder.getCollectionAmount().subtract(statementOrder.getReceivedAmt()));// 本次要收=成本收款金额(客户币种)-已收
                                        } else {
                                            settlementCostError = true;
                                            if (statementOrder.getCollectionAmount().compareTo(statementOrder.getReceivableAmt()) < 0) {
                                                statementOrder.setSettlementCostErrorDesc("异常（成本收款金额小于应收超过" + settingsConstant.getProtocolOrderSettlementCostPercent() + "%）");// 结算成本错误描述
                                            } else {
                                                statementOrder.setSettlementCostErrorDesc("异常（成本收款金额大于应收超过" + settingsConstant.getProtocolOrderSettlementCostPercent() + "%）");// 结算成本错误描述
                                            }
                                        }
                                    }
                                }
                            }
                            // 标识修改
                            if (settlementCostError || repairRateDifference) {
                                AgentStatementPO errorUpdate = new AgentStatementPO();
                                errorUpdate.setId(asyncStatement.getId());
                                if (settlementCostError) {
                                    errorUpdate.setSettlementCostError(1);
                                }
                                errorUpdate.setRepairRateDifference(1);
                                agentStatementMapper.updateByPrimaryKeySelective(errorUpdate);
                            }
                            // 保存客户账单订单信息
                            agentStatementOrderMapper.saveBatchStatementOrder2(statementOrders);
                            if (statementOrders.size() < pageSize - 1) {
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("添加客户账单明细失败", e);
                        throw new RuntimeException("添加客户账单明细失败");
                    }

                    try {
                        // 3.更新账单金额
                        StatementIdDTO statementIdDTO = new StatementIdDTO();
                        statementIdDTO.setStatementId(asyncStatement.getId());
                        updateStatementAmount(statementIdDTO);
                    } catch (Exception e) {
                        log.error("更新客户账单金额失败", e);
                        throw new RuntimeException("更新客户账单金额失败");
                    }
                    try {
                        // 4.更新订单对账状态为出账中，并将订单加锁
                        UpdateOrderFinanceDTO updateOrderFinanceDTO = new UpdateOrderFinanceDTO();
                        updateOrderFinanceDTO.setStatementId(asyncStatement.getId());
                        updateOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CHECKING.key);
                        updateOrderFinanceDTO.setFinanceLockStatus(1);
                        agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);
                        asyncStatement = agentStatementMapper.selectByPrimaryKey(asyncStatement.getId());

                        //在创建账单时自动为账单里的订单加锁
                        QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
                        queryStatementOrderListDTO.setStatementId(asyncStatement.getId());
                        List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
                        for (StatementOrderDTO statementOrderDTO : list) {
                            FinanceLockOrderDTO financeLockOrderDTO = new FinanceLockOrderDTO();
                            Example example = new Example(AgentStatementOrderPO.class);
                            example.createCriteria().andEqualTo("orderCode", statementOrderDTO.getOrderCode())
                                    .andEqualTo("statementId", asyncStatement.getId());
                            AgentStatementOrderPO lockingOrder = agentStatementOrderMapper.selectOneByExample(example);
                            financeLockOrderDTO.setOrderId(lockingOrder.getOrderId());
                            financeLockOrderDTO.setLockStatus(0);
                            financeLockService.lockOrder(financeLockOrderDTO);
                        }
                    } catch (Exception e) {
                        log.error("客户账单更新订单对账状态失败", e);
                        throw new RuntimeException("客户账单更新订单对账状态失败");
                    }
                    transactionManager.commit(transaction);
                    // 创建成功设置供应商redis的出账状态为未出账
                    OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                    stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
                    RedisTemplateX.hPut(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode(), JSONObject.toJSONString(stateDTO));
                } catch (Exception e) {
                    transactionManager.rollback(transaction);
                    log.error("创建客户账单账单失败，原因： {} ,耗时 {}", e.getMessage(), System.currentTimeMillis() - millis);
                    // 创建失败设置供应商redis的出账状态为出账失败
                    OutgoingStateDTO stateDTO = new OutgoingStateDTO();
                    stateDTO.setCheckInDate(request.getStartDate());
                    stateDTO.setCheckOutDate(request.getEndDate());
                    stateDTO.setOutgoingState(OutgoingStateEnum.CONFIRMED.key);
                    stateDTO.setFailureReason(e.getMessage());
                    RedisTemplateX.hPut(RedisKey.SATEMENT_ACCOUNT_STATE, request.getAgentCode(), JSONObject.toJSONString(stateDTO));
                }
            }
        });
    }

    /**
     * 取消订单，异步处理账单明细
     * @param request 修改账单状态请求参数
     */
    public void deleteStatementOrder(ModifyStatementStatusDTO request) {
        TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            AgentStatementPO cancelUpdate = new AgentStatementPO();
            cancelUpdate.setId(request.getStatementId());

            UpdateOrderFinanceDTO updateOrderFinanceDTO = new UpdateOrderFinanceDTO();
            updateOrderFinanceDTO.setStatementId(request.getStatementId());

            if (Objects.equals(request.getStatementStatus(), StatementStatusEnum.CANCELED.key)) {
                cancelUpdate.setUpdatedBy(request.getOperator());
                cancelUpdate.setUpdatedDt(new Date());
                QueryStatementOrderListDTO queryStatementOrderListDTO = new QueryStatementOrderListDTO();
                queryStatementOrderListDTO.setStatementId(request.getStatementId());

                // 查询账单明细订单ID，批量插入到账单
                int pageSize = 300;
                int pageNum = 1;
                while (true) {
                    PageHelper.startPage(pageNum, pageSize);
                    // 查询该订单下面的所有账单
                    List<StatementOrderDTO> list = agentStatementOrderMapper.queryStatementOrderList(queryStatementOrderListDTO);
                    PageInfo<StatementOrderDTO> pageInfo = new PageInfo<>(list);
                    //++pageNum;
                    // 相当于将所有的订单明细删除
                    if (CollUtilX.isNotEmpty(pageInfo.getList())) {
                        DeleteStatementOrderListDTO deleteStatementOrderListDTO = new DeleteStatementOrderListDTO();
                        deleteStatementOrderListDTO.setStatementId(request.getStatementId());
                        List<Integer> orderList = new ArrayList<Integer>();
                        for (StatementOrderDTO statementOrderDTO : pageInfo.getList()) {
                            orderList.add(statementOrderDTO.getStatementOrderId());
                        }
                        deleteStatementOrderListDTO.setStatementOrderIdList(orderList);
                        deleteStatementOrderListDTO.setCurrency(request.getCurrency());
                        deleteStatementOrderList(deleteStatementOrderListDTO);
                    }
                    if (pageInfo.getSize() < pageSize - 1) {
                        break;
                    }
                }
                // 更新订单对账状态
                updateOrderFinanceDTO.setFinanceLockStatus(0);
                agentStatementOrderMapper.updateOrderFinance(updateOrderFinanceDTO);
            }
            // 更新账单状态
            cancelUpdate.setStatementStatus(request.getStatementStatus());
            agentStatementMapper.updateByPrimaryKeySelective(cancelUpdate);

            transactionManager.commit(transaction);
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            RedisTemplateX.hPut(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));
        } catch (Exception e) {
            transactionManager.rollback(transaction);
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            RedisTemplateX.hPut(RedisKey.AGENT_CANCEL_STATE, request.getAgentCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));
            log.error("取消客户账单失败", e);
        }
    }

    /**
     * 获取币种对CNY的汇率
     *
     * @param originalCurrency 币种
     * @return 汇率
     */
    private BigDecimal getRateToOrgCurrency(Integer originalCurrency) {
        return exchangeRateService.getRateToOrgCurrency(originalCurrency, CompanyDTO.COMPANY_CODE);
    }
}
