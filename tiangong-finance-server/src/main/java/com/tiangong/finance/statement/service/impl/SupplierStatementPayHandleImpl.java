package com.tiangong.finance.statement.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tiangong.enums.StatementTypeEnum;
import com.tiangong.finance.enums.CheckStatusEnum;
import com.tiangong.finance.enums.StatementStatusEnum;
import com.tiangong.finance.remote.statement.request.QueryStatementSupplyOrderListDTO;
import com.tiangong.finance.remote.statement.response.StatementSupplyOrderWithOrderCodeDTO;
import com.tiangong.finance.statement.domain.SupplierStatementPO;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementEntity;
import com.tiangong.finance.statement.dto.CancelStatementWorkOrderDTO;
import com.tiangong.finance.statement.dto.ConfirmStatementWorkOrderDTO;
import com.tiangong.finance.statement.dto.UpdateSupplyOrderFinanceDTO;
import com.tiangong.finance.statement.mapper.SupplierImportStatementMapper;
import com.tiangong.finance.statement.mapper.SupplierStatementMapper;
import com.tiangong.finance.statement.mapper.SupplierStatementOrderMapper;
import com.tiangong.finance.statement.service.SupplierStatementPayHandle;
import com.tiangong.keys.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class SupplierStatementPayHandleImpl implements SupplierStatementPayHandle {

    @Autowired
    private SupplierStatementMapper supplierStatementMapper;

    @Autowired
    private SupplierStatementOrderMapper supplierStatementOrderMapper;

    @Autowired
    private SupplierImportStatementMapper supplierImportStatementMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmStatementWorkOrder(ConfirmStatementWorkOrderDTO request) {
        // 1.更新账单结算金额
        boolean isFinishSettlement = false;
        SupplierStatementPO supplierStatementQuery = new SupplierStatementPO();
        supplierStatementQuery.setStatementCode(request.getStatementCode());
        SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectOne(supplierStatementQuery);
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        supplierStatementUpdate.setId(supplierStatementPO.getId());
        supplierStatementUpdate.setPaidAmt(supplierStatementPO.getPaidAmt().add(request.getConfirmAmt()));
        supplierStatementUpdate.setUnpaidAmt(supplierStatementPO.getUnpaidAmt().subtract(request.getConfirmAmt()));
        //奖励为收款
        if (StatementTypeEnum.REWARD_AMT.key.equals(supplierStatementPO.getStatementType())) {
            if (request.getNotifyAmt().compareTo(BigDecimal.ZERO) > 0) {
                supplierStatementUpdate.setUnconfirmedReceivedAmt(supplierStatementPO.getUnconfirmedReceivedAmt().subtract(request.getNotifyAmt()));
            } else {
                supplierStatementUpdate.setUnconfirmedPaidAmt(supplierStatementPO.getUnconfirmedPaidAmt().subtract(BigDecimal.ZERO.subtract(request.getNotifyAmt())));
            }
        } else {
            if (request.getNotifyAmt().compareTo(BigDecimal.ZERO) > 0) {
                supplierStatementUpdate.setUnconfirmedPaidAmt(supplierStatementPO.getUnconfirmedPaidAmt().subtract((request.getNotifyAmt())));
            } else {
                supplierStatementUpdate.setUnconfirmedReceivedAmt(supplierStatementPO.getUnconfirmedReceivedAmt().subtract(BigDecimal.ZERO.subtract(request.getNotifyAmt())));
            }
        }
        if (supplierStatementPO.getStatementAmt().compareTo(supplierStatementUpdate.getPaidAmt()) == 0) {
            isFinishSettlement = true;
        }

        if (isFinishSettlement && supplierStatementPO.getStatementStatus().equals(StatementStatusEnum.CONFIRMED.key)) {
            // 2.更新订单的结算状态和结算金额
            supplierStatementUpdate.setRealSettlementDate(new Date());
            supplierStatementUpdate.setSettlementStatus(1);
            //已结算则此账单对应的所有供应商账单的对比状态都变为已对比
            supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                    .eq(SupplierImportStatementEntity::getStatementId, supplierStatementUpdate.getId())
                    .eq(SupplierImportStatementEntity::getStatementStatus, 1)
                    .set(SupplierImportStatementEntity::getStatementStatus, 2));

            UpdateSupplyOrderFinanceDTO updateSupplyOrderFinanceDTO = new UpdateSupplyOrderFinanceDTO();
            updateSupplyOrderFinanceDTO.setStatementId(supplierStatementPO.getId());
            updateSupplyOrderFinanceDTO.setIsUpdateSettlementStatus(1);
            updateSupplyOrderFinanceDTO.setIsUpdateSettlementAmount(1);
            updateSupplyOrderFinanceDTO.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
            updateSupplyOrderFinanceDTO.setFinanceType(supplierStatementPO.getStatementType());
            updateSupplyOrderFinanceDTO.setFinanceLockStatus(1);
            supplierStatementOrderMapper.updateSupplyOrderFinance(updateSupplyOrderFinanceDTO);

            // 3.返还额度
            // TODO: 2019/7/5 调分销商接口返还额度
        }
        supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加入到统计报表
                // 2024年03月04日
                // 加入特殊流程
                // 确认工单的过程（供应商结算、奖励结算、返佣结算），查询对应的订单列表，触发订单报表统计
                CompletableFuture.runAsync(() -> {
                    try {
                        QueryStatementSupplyOrderListDTO query = new QueryStatementSupplyOrderListDTO();
                        query.setStatementId(supplierStatementPO.getId());
                        List<StatementSupplyOrderWithOrderCodeDTO> list = supplierStatementOrderMapper.queryStatementOrderWithOrderCodeList(query);

                        for (StatementSupplyOrderWithOrderCodeDTO supplyOrderWithOrderCodeDTO : list) {
                            // 加入到统计报表队列中
                            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, supplyOrderWithOrderCodeDTO.getOrderCode());
                        }
                    } catch (Exception e) {
                        log.error("【供货单工单处理】加入到统计报表队列异常,error:", e);
                    }
                });
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelStatementWorkOrder(CancelStatementWorkOrderDTO request) {
        // 更新通知金额
        SupplierStatementPO supplierStatementQuery = new SupplierStatementPO();
        supplierStatementQuery.setStatementCode(request.getStatementCode());
        SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectOne(supplierStatementQuery);
        SupplierStatementPO supplierStatementUpdate = new SupplierStatementPO();
        supplierStatementUpdate.setId(supplierStatementPO.getId());
        if (request.getNotifyAmt().compareTo(BigDecimal.ZERO) > 0) {
            supplierStatementUpdate.setUnconfirmedPaidAmt(supplierStatementPO.getUnconfirmedPaidAmt().subtract(request.getNotifyAmt()));
        } else {
            supplierStatementUpdate.setUnconfirmedReceivedAmt(supplierStatementPO.getUnconfirmedReceivedAmt().subtract(BigDecimal.ZERO.subtract(request.getNotifyAmt())));
        }
        supplierStatementMapper.updateByPrimaryKeySelective(supplierStatementUpdate);
    }
}
