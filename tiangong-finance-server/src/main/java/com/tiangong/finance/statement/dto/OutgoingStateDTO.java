package com.tiangong.finance.statement.dto;

import lombok.Data;

/**
 * 未出账
 */
@Data
public class OutgoingStateDTO {
    /**
     * 出账状态
     */
    private Integer outgoingState;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 入住时间
     */
    private String checkInDate;

    /**
     * 离店时间
     */
    private String checkOutDate;

    /**
     * 出账时间
     */
    private Integer time;

    /**
     * 创建时间
     */
    private Long creatTime;

    /**
     * 前端时间筛选类型
     */
    private Integer dateQueryType;
}
