package com.tiangong.finance.statement.domain.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:25
 * @Description:
 */

@Data
public class ConvertTimeZoneReq implements Serializable {

    /**
     * 酒店当地时区 (例如: UTC+7)
     */
    private String hotelLocalTimezone;

    /**
     * 出账时间窗口: 酒店当地开始时间 (HH:mm:ss)
     */
    private String hotelLocalStartTime;

    /**
     * 出账时间窗口: 酒店当地结束时间 (HH:mm:ss)
     */
    private String hotelLocalEndTime;

}
