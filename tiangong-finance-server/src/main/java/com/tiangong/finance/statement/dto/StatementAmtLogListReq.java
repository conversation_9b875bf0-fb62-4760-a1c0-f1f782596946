package com.tiangong.finance.statement.dto;

import com.tiangong.common.PageDto;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class StatementAmtLogListReq extends PageDto {

    /**
     * 0:客户账单管理 1：供应商账单管理 2：供应商奖励账单 3：供应商返佣账单
     */
    @NotNull(message = "TYPE_IS_NOT_EMPTY")
    public Integer type;

    /**
     * 账单id
     */
    private Integer statementId;
}
