package com.tiangong.finance.statement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementEntity;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.SupplierStatementInComparisonResp;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 供应商导入账单
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:36
 * @Description:
 */
public interface SupplierImportStatementService extends IService<SupplierImportStatementEntity> {

    /**
     * 导入供应商的账单
     */
    void supplierStatementImport(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq) throws IOException;

    /**
     * 供应商导入账单列表（分页）
     */
    PageVo supplierImportStatementPage(SupplierImportStatementPageReq req);

    /**
     * 供应商导入账单删除
     */
    void supplierImportStatementDel(SupplierImportStatementDelReq req);

    /**
     * 比对中供应商账单
     */
    List<SupplierStatementInComparisonResp> supplierStatementInComparison(SupplierStatementInComparisonReq req);

    /**
     * 生成供应商导入的账单模板
     */
    void supplierStatementTemplateGenerate(HttpServletResponse response);

    /**
     * 比对账单（分页）
     */
    Object contrastStatementPage(ContrastStatementPageReq req);

}


