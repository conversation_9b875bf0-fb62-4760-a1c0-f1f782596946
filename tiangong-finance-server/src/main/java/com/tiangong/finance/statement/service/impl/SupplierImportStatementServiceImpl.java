package com.tiangong.finance.statement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.constant.DynamicTableNameConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.config.page.MybatisPlusConfig;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.StrPool;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.statement.constant.SignsConstant;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementAnnexEntity;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementEntity;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.ContrastStatementPageResp;
import com.tiangong.finance.statement.domain.resp.SupplierImportStatementPageResp;
import com.tiangong.finance.statement.domain.resp.SupplierStatementInComparisonResp;
import com.tiangong.finance.statement.mapper.SupplierImportStatementAnnexMapper;
import com.tiangong.finance.statement.mapper.SupplierImportStatementMapper;
import com.tiangong.finance.statement.mapper.SupplyAutoReconciliationMapper;
import com.tiangong.finance.statement.service.SupplierImportStatementService;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 供应商导入账单
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:42
 * @Description:
 */
@Slf4j
@Service
public class SupplierImportStatementServiceImpl extends ServiceImpl<SupplierImportStatementMapper, SupplierImportStatementEntity> implements SupplierImportStatementService {

    /**
     * 批量插入的数量
     */
    private final Integer batchInsertCount = 2000;

    /**
     * Excel列数
     */
    private static final Integer EXCEL_COLUMN_COUNT = 3;

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(
            4,
            8,
            30,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100000),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Autowired
    private SupplierImportStatementMapper supplierImportStatementMapper;

    @Autowired
    private SupplierImportStatementAnnexMapper supplierImportStatementAnnexMapper;

    @Autowired
    private SupplyAutoReconciliationMapper supplyAutoReconciliationMapper;

    @Autowired
    HttpServletRequest request;

    /**
     * 导入供应商的账单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplierStatementImport(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq) throws IOException {
        LoginUser loginUser = TokenManager.getUser(request);
        importStatement(file, supplierImportStatementReq, "", loginUser);
    }

    /**
     * 供应商导入账单列表（分页）
     */
    @Override
    public PageVo supplierImportStatementPage(SupplierImportStatementPageReq req) {
        Page<SupplierImportStatementEntity> SupplierImportStatementPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        QueryWrapper<SupplierImportStatementEntity> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(SupplierImportStatementEntity::getDeleted, 0)
                .eq(StrUtilX.isNotEmpty(req.getSupplierCode()), SupplierImportStatementEntity::getSupplierCode, req.getSupplierCode())
                .between(req.getImportStartDate() != null && req.getImportEndDate() != null,
                        SupplierImportStatementEntity::getCreatedDt, req.getImportStartDate(), DateUtilX.addDate(req.getImportEndDate(), 1))
                .eq(req.getStatementStatus() != null, SupplierImportStatementEntity::getStatementStatus, req.getStatementStatus())
                .like(StrUtilX.isNotEmpty(req.getSupplierStatementCode()), SupplierImportStatementEntity::getSupplierStatementCode, req.getSupplierStatementCode())
                .like(StrUtilX.isNotEmpty(req.getSupplierStatementName()), SupplierImportStatementEntity::getSupplierStatementName, req.getSupplierStatementName())
                .like(StrUtilX.isNotEmpty(req.getStatementCode()), SupplierImportStatementEntity::getStatementCode, req.getStatementCode())
                .orderByDesc(SupplierImportStatementEntity::getCreatedDt);

        IPage<SupplierImportStatementEntity> ipage = supplierImportStatementMapper.selectPage(SupplierImportStatementPage, wrapper);

        List<SupplierImportStatementPageResp> collect = ipage.getRecords().stream().map((item) -> {
            SupplierImportStatementPageResp resp = new SupplierImportStatementPageResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

    /**
     * 供应商导入账单删除
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplierImportStatementDel(SupplierImportStatementDelReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        //只有待对比和导入失败才能删除
        int updateRows = supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                .eq(SupplierImportStatementEntity::getId, req.getSupplierImportStatementId())
                .in(SupplierImportStatementEntity::getStatementStatus, 0, 4)
                .eq(SupplierImportStatementEntity::getDeleted, 0)
                .set(SupplierImportStatementEntity::getDeleted, 1)
                .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));

        if (updateRows != 1) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "删除失败,非待比对和导入失败的不能删除!");
        }

        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getSupplierImportStatementId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
        supplierImportStatementAnnexMapper.update(null, new UpdateWrapper<SupplierImportStatementAnnexEntity>().lambda()
                .eq(SupplierImportStatementAnnexEntity::getSupplierImportStatementId, req.getSupplierImportStatementId())
                .set(SupplierImportStatementAnnexEntity::getDeleted, 1)
                .set(SupplierImportStatementAnnexEntity::getUpdatedBy, loginUser.getFullUserName())
                .set(SupplierImportStatementAnnexEntity::getUpdatedDt, new Date()));
    }

    /**
     * 比对中供应商账单
     */
    @Override
    public List<SupplierStatementInComparisonResp> supplierStatementInComparison(SupplierStatementInComparisonReq req) {

        List<SupplierImportStatementEntity> list = supplierImportStatementMapper.selectList(new QueryWrapper<SupplierImportStatementEntity>().lambda()
                .eq(SupplierImportStatementEntity::getStatementCode, req.getStatementCode())
                .eq(SupplierImportStatementEntity::getDeleted, 0)
                .in(SupplierImportStatementEntity::getStatementStatus, 1, 2));

        return list.stream().map(obj -> {
            SupplierStatementInComparisonResp resp = new SupplierStatementInComparisonResp();
            resp.setSupplierStatementId(obj.getId());
            resp.setSupplierStatementCode(obj.getSupplierStatementCode());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public void supplierStatementTemplateGenerate(HttpServletResponse response) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("供应商账单");
        XSSFRow rowTop = sheet.createRow(0);
        rowTop.createCell(0).setCellValue("供应商订单号");
        rowTop.createCell(1).setCellValue("我方供货单号");
        rowTop.createCell(2).setCellValue("要收金额");

        sheet.setColumnWidth(0, 25 * 256);
        sheet.setColumnWidth(1, 25 * 256);
        sheet.setColumnWidth(2, 25 * 256);

        OutputStream output = null;
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        ByteArrayInputStream byteArrayInputStream = null;

        try {
            ByteArrayOutputStream outputTmp = new ByteArrayOutputStream();
            workbook.write(outputTmp);
            outputTmp.flush();
            byte[] byteArray = outputTmp.toByteArray();
            IOUtils.closeQuietly(outputTmp);
            byteArrayInputStream = new ByteArrayInputStream(byteArray, 0, byteArray.length);

            //                告诉浏览器用什么软件可以打开此文件
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            // 下载文件的默认名称
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("供应商账单模板.xlsx", "UTF-8"));
            output = response.getOutputStream();
            bis = new BufferedInputStream(byteArrayInputStream);
            bos = new BufferedOutputStream(output);
            byte[] buff = new byte[1024];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }

        } catch (Exception e) {
            log.error("exportStatement has error", e);
        } finally {
            if (null != bos) {
                IOUtils.closeQuietly(bos);
            }
            if (null != bis) {
                IOUtils.closeQuietly(bis);
            }
            if (null != output) {
                IOUtils.closeQuietly(output);
            }
            if (null != byteArrayInputStream) {
                IOUtils.closeQuietly(byteArrayInputStream);
            }
        }
    }

    /**
     * 比对账单（分页）
     */
    @Override
    public Object contrastStatementPage(ContrastStatementPageReq req) {
        Page<SupplierImportStatementEntity> SupplierImportStatementPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        QueryWrapper<SupplierImportStatementEntity> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(SupplierImportStatementEntity::getDeleted, 0)
                .eq(StrUtilX.isNotEmpty(req.getSupplierCode()), SupplierImportStatementEntity::getSupplierCode, req.getSupplierCode())
                .notIn(SupplierImportStatementEntity::getStatementStatus, 2, 3, 4)
                .between(req.getImportStartDate() != null && req.getImportEndDate() != null,
                        SupplierImportStatementEntity::getCreatedDt, req.getImportStartDate(), DateUtilX.addDate(req.getImportEndDate(), 1))
                .orderByAsc(SupplierImportStatementEntity::getStatementStatus)
                .orderByDesc(SupplierImportStatementEntity::getCreatedDt);

        IPage<SupplierImportStatementEntity> ipage = supplierImportStatementMapper.selectPage(SupplierImportStatementPage, wrapper);

        List<ContrastStatementPageResp> collect = ipage.getRecords().stream().map((item) -> {
            ContrastStatementPageResp resp = new ContrastStatementPageResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

    /**
     * 导出账单
     */
    private void importStatement(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq, String statementCode, LoginUser loginUser) throws IOException {
        LocalDateTime startTime = LocalDateTime.now();
        SupplierImportStatementEntity entity = new SupplierImportStatementEntity();
        BeanUtils.copyProperties(supplierImportStatementReq, entity);
        if (StrUtilX.isNotEmpty(statementCode)) {
            entity.setStatementCode(statementCode);
        }
        entity.setStatementStatus(3);
        entity.setStartDate(DateUtilX.stringToDate(supplierImportStatementReq.getStartDate()));
        entity.setEndDate(DateUtilX.stringToDate(supplierImportStatementReq.getEndDate()));
        entity.setSettlementDate(DateUtilX.stringToDate(supplierImportStatementReq.getSettlementDate()));
        entity.setDeleted(0);
        entity.setCreatedDt(new Date());
        entity.setCreatedBy(loginUser.getFullUserName());
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy(loginUser.getFullUserName());
        //供应商账单编号规则：G+年月日+四位随机数
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd");
        String supplierStatementCode = "G" + dtf.format(startTime) + (System.currentTimeMillis() / 1000) % 10000 + "000000";
        supplierStatementCode = supplierStatementCode.substring(0, 13);
        //TODO 查询是否已存在对应的供应商账单编号

        entity.setSupplierStatementCode(supplierStatementCode);

        int insert = supplierImportStatementMapper.insert(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
        }

        String filename = file.getOriginalFilename();
        if (StrUtilX.isNotEmpty(filename)) {
            String suffix = filename.substring(filename.lastIndexOf(".") + 1);
            List<String> list = Arrays.asList(StrPool.EXCEL_XLS_EXTENSION, StrPool.EXCEL_EXTENSION);
            if (list.contains(suffix)) {
                BufferedInputStream inputStream = new BufferedInputStream(file.getInputStream());

                Workbook workbook = null;
                if (suffix.equals(StrPool.EXCEL_XLS_EXTENSION)) {
                    workbook = new HSSFWorkbook(inputStream);
                } else if (suffix.equals(StrPool.EXCEL_EXTENSION)) {
                    workbook = new XSSFWorkbook(inputStream);
                }

                //得到第一个shell
                Sheet sheet = workbook.getSheetAt(0);

                //Excel的行数  物理行数
                int totalRows = sheet.getLastRowNum() + 1;
                // 没有数据
                if (totalRows == 1) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new SysException(ErrorCodeEnum.FAIL.errorCode, "此excel没有数据!");
                }

                if (sheet.getRow(0).getPhysicalNumberOfCells() != EXCEL_COLUMN_COUNT) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new SysException(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                } else {
                    Row rowTop = sheet.getRow(0);
                    for (int i = 0; i < EXCEL_COLUMN_COUNT; i++) {
                        Cell cellTop = rowTop.getCell(i);
                        if (cellTop == null) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        }
                        cellTop.setCellType(Cell.CELL_TYPE_STRING);
                        String topTitle = cellTop.getStringCellValue();
                        if (i == 0 && !"供应商订单号".equals(topTitle)) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        } else if (i == 1 && !"我方供货单号".equals(topTitle)) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        } else if (i == 2 && !"要收金额".equals(topTitle)) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        }
                    }
                }

                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        BigDecimal supplierTotalReceivables = new BigDecimal(0);
                        String currencyStr = "CNY";

                        List<SupplierImportStatementAnnexEntity> annexList = new ArrayList<>();
                        SupplierImportStatementAnnexEntity supplierImportStatementAnnexEntity;
                        //循环Excel行数,从第2行开始。标题不入库
                        for (int r = 1; r < totalRows; r++) {
                            Row row = sheet.getRow(r);
                            if (row == null) {
                                continue;
                            }
                            supplierImportStatementAnnexEntity = new SupplierImportStatementAnnexEntity();
                            //循环Excel的列
                            for (int c = 0; c < EXCEL_COLUMN_COUNT; c++) {
                                Cell cell = row.getCell(c);
                                switch (c) {
                                    case 0:
                                        if (cell == null) {
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String supplierOrderCode = cell.getStringCellValue().replaceAll(" ", "");
                                        if (StrUtilX.isNotEmpty(supplierOrderCode)) {
                                            supplierImportStatementAnnexEntity.setSupplierOrderCode(supplierOrderCode);
                                        }
                                        break;
                                    case 1:
                                        if (cell == null) {
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String supplyOrderCode = cell.getStringCellValue().replaceAll(" ", "");
                                        if (StrUtilX.isNotEmpty(supplyOrderCode)) {
                                            supplierImportStatementAnnexEntity.setSupplyOrderCode(supplyOrderCode);
                                        }
                                        break;
                                    case 2:
                                        if (cell == null) {
                                            supplierImportStatementAnnexEntity.setReceivableAmt(new BigDecimal(0));
                                            supplierImportStatementAnnexEntity.setCurrency("CNY");
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String receivableAmountCurrency = cell.getStringCellValue();
                                        receivableAmountCurrency = receivableAmountCurrency.replaceAll(" ", "");

                                        String regex = "[0-9.]+";
                                        Pattern pattern = Pattern.compile(regex);
                                        Matcher matcher = pattern.matcher(receivableAmountCurrency);
                                        int flage = 0;
                                        StringBuilder sb = new StringBuilder();
                                        while (matcher.find()) {//将数字和小数点提取出来
                                            sb.append(matcher.group());
                                            flage++;
                                            if (flage == 100) {//防止出现死循环
                                                break;
                                            }
                                        }

                                        String strTmp = receivableAmountCurrency.replaceAll("[0-9.]", "");
                                        try {//转数字
                                            supplierTotalReceivables = supplierTotalReceivables.add(new BigDecimal(sb.toString()));
                                            supplierImportStatementAnnexEntity.setReceivableAmt(new BigDecimal(sb.toString()));
                                            if (StrUtilX.isNotEmpty(strTmp)) {
                                                supplierImportStatementAnnexEntity.setCurrency(strTmp);
                                            } else {
                                                supplierImportStatementAnnexEntity.setCurrency("CNY");
                                            }
                                        } catch (Exception e) {
                                            supplierImportStatementAnnexEntity.setReceivableAmt(SignsConstant.MONEY_SIGN);
                                            if (StrUtilX.isNotEmpty(receivableAmountCurrency)) {
                                                supplierImportStatementAnnexEntity.setCurrency(receivableAmountCurrency);
                                            } else {
                                                supplierImportStatementAnnexEntity.setCurrency("CNY");
                                            }
                                        }
                                        break;
                                    default:
                                        break;
                                }
                            }
                            if (StrUtilX.isNotEmpty(supplierImportStatementAnnexEntity.getSupplyOrderCode())) {
                                supplierImportStatementAnnexEntity.setSupplierImportStatementId(entity.getId());
                                supplierImportStatementAnnexEntity.setDeleted(0);
                                supplierImportStatementAnnexEntity.setCreatedBy(loginUser.getFullUserName());
                                supplierImportStatementAnnexEntity.setCreatedDt(new Date());
                                supplierImportStatementAnnexEntity.setUpdatedBy(loginUser.getFullUserName());
                                supplierImportStatementAnnexEntity.setUpdatedDt(new Date());
                                annexList.add(supplierImportStatementAnnexEntity);
                            }
                            //每batchInsertCount条就插入
                            if (annexList.size() >= batchInsertCount) {
                                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                                supplierImportStatementAnnexMapper.insertBatchSomeColumn(annexList);
                                annexList = new ArrayList<>();
                            }
                        }
                        if (annexList.size() > 0) {
                            MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                            supplierImportStatementAnnexMapper.insertBatchSomeColumn(annexList);
                        }

                        //更新总的要收金额
                        int updateRow = supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>()
                                .eq("id", entity.getId()).set("supplier_total_receivables", supplierTotalReceivables).set("currency", currencyStr).set("statement_status", 0)
                                .set("updated_by", loginUser.getFullUserName()).set("updated_dt", new Date()));
                        if (updateRow != 1) {
                            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
                        }
                    } catch (Exception e) {
                        //异常的话把对应的已插入的附件都删除掉
                        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                        supplierImportStatementAnnexMapper.delete(new QueryWrapper<SupplierImportStatementAnnexEntity>().lambda().eq(SupplierImportStatementAnnexEntity::getSupplierImportStatementId, entity.getId()));
                        //把导入记录改成导入失败
                        supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>()
                                .eq("id", entity.getId()).set("statement_status", 4)
                                .set("updated_by", loginUser.getFullUserName()).set("updated_dt", new Date()));
                    }
                }, EXECUTOR);
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new SysException(ErrorCodeEnum.FAIL.errorCode, "非法文件上传!");
            }
        } else {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "文件不能为空");
        }
    }
}