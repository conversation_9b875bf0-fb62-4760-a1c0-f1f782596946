package com.tiangong.finance.statement.domain.resp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/31 15:25
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SupplierImportStatementAnnexPageResp implements Serializable {

    /**
     * id号
     */
    private Integer id;
    /**
     * 供应商订单号
     */
    private String supplierOrderCode;
    /**
     * 供货单编码
     */
    private String supplyOrderCode;
    /**
     * 应收金额带单位
     */
    private String receivableAmtWithCurrency;

}
