package com.tiangong.finance.statement.domain.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/6 15:06
 * @Description:
 */

@Data
public class NonVccAutoBillConfigPageResp implements Serializable {

    private Long id;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 出账规则 0-按订单 1-按日 2-按周 3-按半月 4-按月
     */
    private Integer billingRule;

    /**
     * 出账日期类型 0-入住日前 1-入住日当天 2-离店日当天 3-离店日后
     */
    private Integer billingDateType;

    /**
     * 出账日期天数
     */
    private Integer billingDateDays;

    /**
     * 出账时间窗口: 酒店当地开始时间 (HH:mm:ss)
     */
    private LocalTime hotelLocalStartTime;

    /**
     * 出账时间窗口: 酒店当地结束时间 (HH:mm:ss)
     */
    private LocalTime hotelLocalEndTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

}
