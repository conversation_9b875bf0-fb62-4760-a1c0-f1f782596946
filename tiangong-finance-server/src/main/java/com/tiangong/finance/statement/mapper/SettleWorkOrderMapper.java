package com.tiangong.finance.statement.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.finance.remote.statement.request.QuerySettleWorkOrderPageDTO;
import com.tiangong.finance.remote.statement.response.SettleWorkOrderPageResponseDTO;
import com.tiangong.finance.statement.domain.SettleWorkOrderPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 自助结算工单
 *
 * <AUTHOR>
 * @Date 2024-01-02
 * @Description:
 */
@Mapper
public interface SettleWorkOrderMapper extends MyMapper<SettleWorkOrderPO> {

    /**
     * 查询记账工单任务列表
     * @param request
     * @return
     */
    List<SettleWorkOrderPageResponseDTO> querySettleWorkOrderPage(QuerySettleWorkOrderPageDTO request);

    /**
     * 更新记账工单任务
     * @param settleWorkOrderPO
     */
    void updateSettleWorkOrder(SettleWorkOrderPO settleWorkOrderPO);

    /**
     *  根据账单编码获取
     * @param statementCode
     * @return
     */
    String selectOneByStatementCode(String statementCode);
}
