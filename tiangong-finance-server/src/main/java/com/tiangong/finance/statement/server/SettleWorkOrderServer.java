package com.tiangong.finance.statement.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.SettleWorkOrderPageResponseDTO;
import com.tiangong.finance.statement.service.SettleWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class SettleWorkOrderServer extends BaseController {

    @Autowired
    private SettleWorkOrderService settleWorkOrderService;

    /**
     * 工单列表查询
     */
    @PostMapping("/finance/supplier/querySettleWorkOrderPage")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<SettleWorkOrderPageResponseDTO>> querySettleWorkOrderPage(@RequestBody QuerySettleWorkOrderPageDTO request) {
        return Response.success(settleWorkOrderService.querySettleWorkOrderPage(request));
    }

    /**
     * 重试
     */
    @PostMapping("/finance/supplier/retrySettleWorkOrder")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> retrySettleWorkOrder(@RequestBody RetrySettleWorkOrderDTO request) {
        settleWorkOrderService.retrySettleWorkOrder(request);
        return Response.success();
    }

}
