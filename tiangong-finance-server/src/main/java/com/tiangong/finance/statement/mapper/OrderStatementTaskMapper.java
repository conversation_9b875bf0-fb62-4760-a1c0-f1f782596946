package com.tiangong.finance.statement.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.finance.remote.statement.request.QueryOrderStatementTaskPageDTO;
import com.tiangong.finance.remote.statement.response.OrderStatementTaskResponseDTO;
import com.tiangong.finance.statement.domain.OrderStatementTaskPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单记账任务
 *
 * <AUTHOR>
 * @Description:
 */
@Mapper
public interface OrderStatementTaskMapper extends MyMapper<OrderStatementTaskPO> {

    /**
     * 查询记账工单任务列表
     * @param request
     * @return
     */
    List<OrderStatementTaskResponseDTO> queryOrderStatementTaskPage(QueryOrderStatementTaskPageDTO request);

    /**
     * 更新记账工单任务
     * @param settleWorkOrderPO
     */
    void updateOrderStatementTask(OrderStatementTaskPO settleWorkOrderPO);

    /**
     *  根据账单编码获取
     * @param statementCode
     * @return
     */
    String selectOneByStatementCode(String statementCode);

}
