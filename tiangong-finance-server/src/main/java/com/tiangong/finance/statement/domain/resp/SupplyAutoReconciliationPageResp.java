package com.tiangong.finance.statement.domain.resp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @Date 2023/6/1 14:51
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SupplyAutoReconciliationPageResp implements Serializable {

    /**
     * id号
     */
    private Integer id;
    /**
     * 供货单编码
     */
    private String supplyOrderCode;
    /**
     * 应付供应商金额带单位
     */
    private String supplyPaidAmtWithCurrency;
    /**
     * 供应商账单编号
     * 供应商账单编号规则：G+年月日+四位随机数
     */
    private String supplierStatementCode;
    /**
     * 供应商订单号
     */
    private String supplierOrderCode;
    /**
     * 供应商应收金额带单位
     */
    private String supplierReceivedAmtWithCurrency;

}
