package com.tiangong.finance.statement.service;

import com.tiangong.finance.statement.dto.CancelStatementWorkOrderDTO;
import com.tiangong.finance.statement.dto.ConfirmStatementWorkOrderDTO;

public interface AgentStatementPayHandle {

    /**
     * 确认账单支付工单
     * 确认客户账单的支付工单，更新账单状态为已确认，并处理相关的财务流程
     * @param request 确认工单请求对象，包含工单ID、账单信息等
     */
    void confirmStatementWorkOrder(ConfirmStatementWorkOrderDTO request);

    /**
     * 取消账单支付工单
     * 取消客户账单的支付工单，回滚相关的财务状态和额度变更
     * @param request 取消工单请求对象，包含工单ID、取消原因等
     */
    void cancelStatementWorkOrder(CancelStatementWorkOrderDTO request);
}
