package com.tiangong.finance.statement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.finance.statement.domain.StatementAmtLogPO;
import com.tiangong.finance.statement.dto.StatementAmtLogDTO;
import com.tiangong.finance.statement.dto.StatementAmtLogListReq;
import com.tiangong.finance.statement.mapper.AgentStatementOrderMapper;
import com.tiangong.finance.statement.mapper.StatementAmtLogMapper;
import com.tiangong.finance.statement.service.StatementAmtLogService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StatementAmtLogServerImpl extends ServiceImpl<StatementAmtLogMapper, StatementAmtLogPO> implements StatementAmtLogService {
    /**
     * 账单金额新增日志类型
     */
    private static final Integer STATEMENT_AMOUNT_ADD_TYPE = 0;
    /**
     * 账单金额调整日志类型
     */
    private static final Integer STATEMENT_AMOUNT_MODIFY_TYPE = 1;
    /**
     * 账单金额删除日志类型
     */
    private static final Integer STATEMENT_AMOUNT_DELETE_TYPE = 2;

    @Resource
    private AgentStatementOrderMapper agentStatementOrderMapper;

    @Override
    public void insertStatementAmtLog(StatementAmtLogDTO dto) {
        if (dto.getOrderIdList() == null || dto.getOrderIdList().size() == 0) {
            return;
        }
        List<String> strings = new ArrayList<>();
        String typeName = "";
        if (dto.getType() > 0) {
            typeName = RedisTemplateX.get(RedisKey.LOG_CODE+"O000021");
            strings = agentStatementOrderMapper.querySupplyOrderCodeByOrderId(dto.getOrderIdList());
        } else {
            typeName = RedisTemplateX.get(RedisKey.LOG_CODE+"O000022");
            strings = agentStatementOrderMapper.queryOrderCodeByOrderId(dto.getOrderIdList());
        }
        if (strings.size() == 0) {
            return;
        }
        String collect = strings.stream().collect(Collectors.joining(","));
        String content = "";
        if (STATEMENT_AMOUNT_ADD_TYPE.equals(dto.getContentType())) {
            content = RedisTemplateX.get(RedisKey.LOG_CODE+"O000018");
        } else if (STATEMENT_AMOUNT_MODIFY_TYPE.equals(dto.getContentType())) {
            content = RedisTemplateX.get(RedisKey.LOG_CODE+"O000019");
        } else if (STATEMENT_AMOUNT_DELETE_TYPE.equals(dto.getContentType())) {
            content = RedisTemplateX.get(RedisKey.LOG_CODE+"O000020");
        }
        // 获取币种
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(dto.getCurrency()));

        content = content + " " + typeName + " " + collect + ","+RedisTemplateX.get(RedisKey.LOG_CODE+"O000023") + " "
                + currency + dto.getOriginalStatementAmt().stripTrailingZeros().toPlainString()
                + " " +RedisTemplateX.get(RedisKey.LOG_CODE+"O000024")+" " + currency + " " + dto.getNewStatementAmt().stripTrailingZeros().toPlainString();
        StatementAmtLogPO po = new StatementAmtLogPO();
        po.setType(dto.getType());
        po.setContent(content);
        po.setCreatedBy(dto.getCreatedBy());
        po.setCreatedDt(new Date());
        po.setStatementId(dto.getStatementId());
        this.save(po);
    }

    @Override
    public PaginationSupportDTO<StatementAmtLogPO> statementAmtLogList(StatementAmtLogListReq req) {
        IPage<StatementAmtLogPO> iPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<StatementAmtLogPO> page = this.baseMapper.selectPage(iPage, new LambdaQueryWrapper<StatementAmtLogPO>()
                .eq(StatementAmtLogPO::getType, req.getType())
                .eq(StatementAmtLogPO::getStatementId, req.getStatementId())
                .orderByDesc(StatementAmtLogPO::getCreatedDt));
        PaginationSupportDTO<StatementAmtLogPO> dto = new PaginationSupportDTO<>();
        dto = dto.getPaginationSupportDTO(page);
        return dto;
    }
}
