package com.tiangong.finance.statement.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.finance.remote.statement.request.ModifyStatementStatusDTO;
import com.tiangong.finance.remote.statement.request.QueryAgentStatementListDTO;
import com.tiangong.finance.remote.statement.request.QueryUncheckOutAgentListDTO;
import com.tiangong.finance.remote.statement.response.AgentStatementListResponseDTO;
import com.tiangong.finance.remote.statement.response.UncheckOutAgentDTO;
import com.tiangong.finance.statement.domain.AgentStatementPO;
import com.tiangong.organization.remote.dto.ContactSupplierDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component(value = "agentStatementMapper")
public interface AgentStatementMapper extends MyMapper<AgentStatementPO> {

    /**
     * 查询已出账单列表
     * 根据查询条件查询已生成的客户账单信息，支持按客户、时间、状态等条件筛选
     * @param request 账单查询请求对象，包含客户编码、时间范围、账单状态等查询条件
     * @return 账单列表响应对象，包含账单基本信息、金额、状态等
     */
    List<AgentStatementListResponseDTO> queryStatementList(QueryAgentStatementListDTO request);

    /**
     * 查询未出账客户列表
     * 查询有未结算订单的客户列表，用于生成新的账单
     * @param request 未出账查询请求对象，包含查询时间范围、客户筛选条件等
     * @return 未出账客户列表，包含客户信息、应收金额、订单数量等
     */
    List<UncheckOutAgentDTO> queryUncheckOutAgentList(QueryUncheckOutAgentListDTO request);

    /**
     * 查询客户联系人信息
     * 根据客户编码查询指定角色的有效联系人信息
     * @param agentCode 客户编码
     * @param active 联系人状态（1-有效，0-无效）
     * @return 客户联系人信息，包含姓名、电话、邮箱等
     */
    ContactSupplierDTO selectContact(@Param("agentCode") String agentCode, @Param("active") Integer active);

    /**
     * 校验客户结算成本异常
     * 检查指定客户是否存在未处理的结算成本异常账单
     * @param request 校验请求对象，包含客户编码等信息
     * @return 异常账单数量，大于0表示存在异常
     */
    int checkAgentSettlementCostError(ModifyStatementStatusDTO request);
}