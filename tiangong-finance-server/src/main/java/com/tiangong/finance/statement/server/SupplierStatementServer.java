package com.tiangong.finance.statement.server;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.OutgoingStateEnum;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.*;
import com.tiangong.finance.statement.dto.OutgoingStateDTO;
import com.tiangong.finance.statement.service.SupplierStatementService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@Slf4j
public class SupplierStatementServer extends BaseController {

    @Autowired
    private SupplierStatementService supplierStatementService;

    /**
     * 已出账单查询
     */
    @PostMapping("/finance/supplier/queryStatementList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<SupplierStatementListResponseDTO>> queryStatementList(@RequestBody QuerySupplierStatementListDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        return Response.success(supplierStatementService.queryStatementList(request));
    }

    /**
     * 未出账查询
     */
    @PostMapping("/finance/supplier/queryUncheckOutSupplierList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<UncheckOutSupplierDTO>> queryUncheckOutSupplierList(@RequestBody QueryUncheckOutSupplierListDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        return Response.success(supplierStatementService.queryUncheckOutSupplierList(request));
    }

    /**
     * 创建账单
     */
    @PostMapping("/finance/supplier/createStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> createStatement(@RequestBody CreateSupplierStatementDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        request.setOperator(super.getUserName());
        supplierStatementService.createStatement(request);
        return Response.success();
    }

    /**
     * 账单详情
     */
    @PostMapping("/finance/supplier/queryStatementDetail")
    @PreAuthorize("@syyo.check('finance')")
    public Response<SupplierStatementDetailDTO> queryStatementDetail(@RequestBody StatementIdDTO request) {
        request.setCompanyCode(getCompanyCode());
        request.setOperator(super.getUserName());
        return Response.success(supplierStatementService.queryStatementDetail(request));
    }

    /**
     * 账单明细
     */
    @PostMapping("/finance/supplier/queryStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<StatementSupplyOrderDTO>> queryStatementOrderList(@RequestBody QueryStatementSupplyOrderListDTO request) {
        return Response.success(supplierStatementService.queryStatementOrderList(request));
    }

    /**
     * 账单明细带订单号
     */
    @PostMapping("/finance/supplier/queryStatementOrderWithOrderCodeList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<StatementSupplyOrderWithOrderCodeDTO>> queryStatementOrderWithOrderCodeList(@RequestBody QueryStatementSupplyOrderListDTO request) {
        return Response.success(supplierStatementService.queryStatementOrderWithOrderCodeList(request));
    }

    /**
     * 未出账订单查询
     */
    @PostMapping("/finance/supplier/queryUnCheckOutSupplyOrder")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<UnCheckOutSupplyOrderDTO>> queryUnCheckOutSupplyOrder(@RequestBody QueryUnCheckOutSupplyOrderDTO request) {
        request.setCompanyCode(getCompanyCode());
        return Response.success(supplierStatementService.queryUnCheckOutSupplyOrder(request));
    }

    /**
     * 添加账单明细
     */
    @PostMapping("/finance/supplier/addStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> addStatementOrderList(@RequestBody AddStatementSupplyOrderListDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.SUPPER_BILL_CANCEL);
            }
        }
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.SUPPER_BILL_CANCEL);
        }

        if (request.getStatementId() != null && (CollUtilX.isNotEmpty(request.getSupplyOrderIdList()) || null != request.getDateQueryType() && StrUtilX.isNotEmpty(request.getStartDate()) && StrUtilX.isNotEmpty(request.getEndDate()))) {
            request.setOperator(StrUtil.isNotBlank(request.getOperator()) ? request.getOperator() : super.getUserName());
            request.setCompanyCode(StrUtil.isNotBlank(request.getCompanyCode()) ? request.getCompanyCode() : super.getCompanyCode());
            supplierStatementService.addStatementOrderList(request);
        } else {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        return Response.success();
    }

    /**
     * 删除账单明细
     */
    @PostMapping("/finance/supplier/deleteStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> deleteStatementOrderList(@RequestBody DeleteStatementOrderListDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.SUPPER_BILL_CANCEL);
            }
        }
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.SUPPER_BILL_OUTGOING);
        }
        request.setOperator(super.getUserName());
        supplierStatementService.deleteStatementOrderList(request);
        return Response.success();
    }

    /**
     * 修改账单状态
     */
    @PostMapping("/finance/supplier/modifyStatementStatus")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> modifyStatementStatus(@RequestBody ModifyStatementStatusDTO request) {
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.SUPPER_BILL_OUTGOING);
        }
        request.setOperator(super.getUserName());
        supplierStatementService.modifyStatementStatus(request);
        return Response.success();
    }


    /**
     * 取消账单
     */
    @PostMapping("/finance/supplier/canceStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> canceStatement(@RequestBody ModifyStatementStatusDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.SUPPER_BILL_CANCEL);
            }
        }
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.SUPPER_BILL_OUTGOING);
        }
        request.setOperator(super.getUserName());
        supplierStatementService.cancelStatement(request);
        return Response.success();
    }


    /**
     * 账单明细变更查询
     */
    @PostMapping("/finance/supplier/queryUpdatedStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<UpdatedStatementSupplyOrderDTO>> queryUpdatedStatementOrderList(@RequestBody QueryUpdatedStatementOrderListDTO request) {
        return Response.success(supplierStatementService.queryUpdatedStatementOrderList(request));
    }

    /**
     * 更新账单明细
     */
    @PostMapping("/finance/supplier/updateStatementOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> updateStatementOrderList(@RequestBody StatementIdDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.SUPPER_BILL_CANCEL);
            }
        }
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.SUPPER_BILL_OUTGOING);
        }
        request.setOperator(super.getUserName());
        supplierStatementService.updateStatementOrderList(request);
        return Response.success();
    }

    /**
     * 修改账单中订单的要付金额
     */
    @PostMapping("/finance/supplier/updateStatementSupplyOrderPayAmt")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> updateStatementSupplyOrderPayAmt(@Valid @RequestBody ModifySettlementOrderPayAmtDTO request) {
        Object o = RedisTemplateX.hashGet(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId());
        if (o != null) {
            OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(o, OutgoingStateDTO.class);
            if (outgoingStateDTO == null || OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
                throw new SysException(ErrorCodeEnum.SUPPER_BILL_CANCEL);
            }
        }
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        OutgoingStateDTO outgoingStateDTO = StrUtilX.parseObject(supplierAccountState, OutgoingStateDTO.class);
        if (outgoingStateDTO != null && OutgoingStateEnum.CHECKING.key.equals(outgoingStateDTO.getOutgoingState())) {
            throw new SysException(ErrorCodeEnum.SUPPER_BILL_OUTGOING);
        }

        request.setOperator(super.getUserName());
        supplierStatementService.updateStatementSupplyOrderPayAmt(request);
        return Response.success();
    }

    /**
     * 通知收款
     */
    @PostMapping("/finance/supplier/notifyCollectionOfStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> notifyCollectionOfStatement(@RequestBody NotifyCollectionOfStatementDTO request) {
        request.setOperator(super.getUserName());
        supplierStatementService.notifyCollectionOfStatement(request);
        return Response.success();
    }

    /**
     * 通知付款
     */
    @PostMapping("/finance/supplier/notifyPaymentOfStatement")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Integer> notifyPaymentOfStatement(@RequestBody NotifyPaymentOfStatementDTO request) {
        request.setOperator(super.getUserName());
        return Response.success(supplierStatementService.notifyPaymentOfStatement(request));
    }

    /**
     * 修改账单名称
     */
    @PostMapping("/finance/supplier/modifyStatementName")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> modifyStatementName(@RequestBody ModifyStatementNameDTO request) {
        request.setOperator(super.getUserName());
        supplierStatementService.modifyStatementName(request);
        return Response.success();
    }

    /**
     * 修改结算日期
     */
    @PostMapping("/finance/supplier/modifySettlementDate")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> modifySettlementDate(@RequestBody ModifySettlementDateDTO request) {
        request.setOperator(super.getUserName());
        supplierStatementService.modifySettlementDate(request);
        return Response.success();
    }

    /**
     * 删除供应商账单失败信息
     */
    @PostMapping("/finance/supplier/delOutgoing")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> delOutgoing(@RequestBody QueryUncheckOutSupplierListDTO request) {
        OutgoingStateDTO stateDTO = new OutgoingStateDTO();
        stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
        RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency(), JSONObject.toJSONString(stateDTO));
        return Response.success();
    }

    /**
     * 查询供应商账单取消状态
     */
    @PostMapping("/finance/supplier/getCancelState")
    @PreAuthorize("@syyo.check('finance')")
    public Response<OutgoingStateDTO> getCancelState(@RequestBody QueryUncheckOutSupplierListDTO request) {
        Object object = RedisTemplateX.hashGet(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId());
        if (object != null) {
            return Response.success(StrUtilX.parseObject(object, OutgoingStateDTO.class));
        } else {
            OutgoingStateDTO stateDTO = new OutgoingStateDTO();
            stateDTO.setOutgoingState(OutgoingStateEnum.UN_CHECK.key);
            RedisTemplateX.hPut(RedisKey.SUPPLY_CANCEL_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getStatementId(), JSONObject.toJSONString(stateDTO));
        }
        return Response.success(null);
    }

    /**
     * 查询供应商账单信息
     */
    @PostMapping("/finance/supplier/getOutgoing")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> getOutgoing(@RequestBody QueryUncheckOutSupplierListDTO request) {
        Object supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode() + "_" + request.getCurrency());
        // 为空需要不按币种查询一次，防止旧数据存在导致判断错误，后续需要删除
        if (supplierAccountState == null) {
            supplierAccountState = RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_STATE + request.getStatementType(), request.getSupplierCode());
        }
        return Response.success(supplierAccountState);
    }

    /**
     * 创建记账工单任务【提供自助结算模块调用】
     */
    @AnonymousAccess
    @PostMapping("/finance/supplier/createSettleWorkOrder")
    public Response<Object> createSettleWorkOrder(@RequestBody CreateSettleWorkOrderDTO request) {
        supplierStatementService.createSettleWorkOrder(request);
        return Response.success();
    }

    /**
     * 取消记账工单任务【提供自助结算模块调用】
     */
    @AnonymousAccess
    @PostMapping("/finance/supplier/cancelSettleWorkOrder")
    public Response<Object> cancelSettleWorkOrder(@RequestBody CancelSettleWorkOrderDTO request) {
        supplierStatementService.cancelSettleWorkOrder(request);
        return Response.success();
    }

    /**
     * 开启记账工单任务【提供自助结算模块调用】
     */
    @AnonymousAccess
    @PostMapping("/finance/supplier/startSettleWorkOrder")
    public Response<Object> startSettleWorkOrder(@RequestBody CreateSettleWorkOrderDTO request) {
        supplierStatementService.startSettleWorkOrder(request);
        return Response.success();
    }

    /**
     * 查询供货单待退款明细接口【提供自助结算模块调用】
     */
    @AnonymousAccess
    @PostMapping("/finance/supplier/querySupplierOrderRefundList")
    public Response<List<SupplierOrderRefundDTO>> querySupplierOrderRefundList(@RequestBody QuerySupplierOrderRefundDTO request) {
        return Response.success(supplierStatementService.querySupplierOrderRefundList(request));
    }

}
