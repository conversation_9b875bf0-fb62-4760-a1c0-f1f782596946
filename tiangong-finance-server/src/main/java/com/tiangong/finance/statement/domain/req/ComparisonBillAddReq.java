package com.tiangong.finance.statement.domain.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 添加比对账单
 *
 * <AUTHOR>
 * @Date 2023/5/26 10:45
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ComparisonBillAddReq implements Serializable {

    /**
     * 供应商账单编号
     * 供应商账单编号规则：G+年月日+四位随机数
     */
    private List<Integer> supplierStatementIdList;

    /**
     * 账单id
     */
    private Integer statementId;

    /**
     * 账单编码
     */
    private String statementCode;

}
