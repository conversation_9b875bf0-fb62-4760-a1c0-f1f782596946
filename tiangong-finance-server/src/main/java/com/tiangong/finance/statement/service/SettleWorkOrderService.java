package com.tiangong.finance.statement.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.remote.statement.request.CreateSettleWorkOrderDTO;
import com.tiangong.finance.remote.statement.request.QuerySettleWorkOrderPageDTO;
import com.tiangong.finance.remote.statement.request.RetrySettleWorkOrderDTO;
import com.tiangong.finance.remote.statement.response.SettleWorkOrderPageResponseDTO;
import com.tiangong.finance.statement.domain.SettleWorkOrderPO;
import com.tiangong.finance.statement.dto.SettleWorkOrderDTO;

public interface SettleWorkOrderService {

    /**
     * 新增自助结算记账工单
     */
    void insertSettleWorkOrder(SettleWorkOrderDTO settleWorkOrderDTO);

    /**
     * 根据自助结算编码删除记账工单
     */
    void delCancelSettleWorkOrder(String settleTaskCode);

    /**
     * 查询记账工单处理列表
     */
    PaginationSupportDTO<SettleWorkOrderPageResponseDTO> querySettleWorkOrderPage(QuerySettleWorkOrderPageDTO request);

    /**
     * 记账工单任务重试
     */
    void retrySettleWorkOrder(RetrySettleWorkOrderDTO request);

    /**
     * 查询记账工单任务根据自助结算任务编码
     */
    SettleWorkOrderPO querySettleWorkOrder(String settleTaskCode);

    /**
     * 更新记账工单任务
     */
    void updateSettleWorkOrder(SettleWorkOrderDTO settleWorkOrderDTO);

    /**
     * 记账任务工单处理
     */
    void settleProcessWorkOrder(String settleTaskCode);

    /**
     * 更新交易时间
     */
    void updateSettleWorkOrderChangeTime(CreateSettleWorkOrderDTO request);

    /**
     * 根据账单编码获取
     */
    String selectOneByStatementCode(String statementCode);
}
