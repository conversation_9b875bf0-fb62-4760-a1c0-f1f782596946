package com.tiangong.finance.statement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.finance.statement.domain.entity.SupplyAutoReconciliationEntity;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.QueryStatementStatusResp;
import com.tiangong.finance.statement.domain.resp.ReconciliationResultTypeCountResp;

import javax.servlet.http.HttpServletResponse;

/**
 * 自动对账
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:37
 * @Description:
 */
public interface SupplyAutoReconciliationService extends IService<SupplyAutoReconciliationEntity> {

    /**
     * 添加/减少/重新比对账单
     */
    void comparisonBillAdd(ComparisonBillAddReq req);

    /**
     * 自动对账列表（分页）
     */
    PageVo supplyAutoReconciliationPage(SupplyAutoReconciliationPageReq req);

    /**
     * 自动对账结果各种类型数量的统计
     */
    ReconciliationResultTypeCountResp reconciliationResultTypeCount(ReconciliationResultTypeCountReq req);

    /**
     * 导出比对结果
     */
    void reconciliationResultExport(ReconciliationResultExportReq req, HttpServletResponse response);

    /**
     * 查询账单状态
     */
    QueryStatementStatusResp queryStatementStatus(QueryStatementStatusReq req);

}


