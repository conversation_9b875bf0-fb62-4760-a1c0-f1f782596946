package com.tiangong.finance.statement.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.*;

import java.util.List;

public interface SupplierStatementService {

    /**
     * 已出账单查询
     */
    PaginationSupportDTO<SupplierStatementListResponseDTO> queryStatementList(QuerySupplierStatementListDTO request);

    /**
     * 未出账查询
     */
    PaginationSupportDTO<UncheckOutSupplierDTO> queryUncheckOutSupplierList(QueryUncheckOutSupplierListDTO request);

    /**
     * 创建账单
     */
    void createStatement(CreateSupplierStatementDTO request);

    /**
     * 账单详情
     */
    SupplierStatementDetailDTO queryStatementDetail(StatementIdDTO request);

    /**
     * 账单明细
     */
    PaginationSupportDTO<StatementSupplyOrderDTO> queryStatementOrderList(QueryStatementSupplyOrderListDTO request);

    /**
     * 账单明细带订单号
     */
    PaginationSupportDTO<StatementSupplyOrderWithOrderCodeDTO> queryStatementOrderWithOrderCodeList(QueryStatementSupplyOrderListDTO request);

    /**
     * 未出账订单查询
     */
    PaginationSupportDTO<UnCheckOutSupplyOrderDTO> queryUnCheckOutSupplyOrder(QueryUnCheckOutSupplyOrderDTO request);

    /**
     * 添加账单明细
     */
    void addStatementOrderList(AddStatementSupplyOrderListDTO request);

    /**
     * 删除账单明细
     */
    void deleteStatementOrderList(DeleteStatementOrderListDTO request);

    /**
     * 修改账单状态
     */
    void modifyStatementStatus(ModifyStatementStatusDTO request);

    /**
     * 取消账单
     */
    void cancelStatement(ModifyStatementStatusDTO request);

    /**
     * 账单明细变更查询
     */
    PaginationSupportDTO<UpdatedStatementSupplyOrderDTO> queryUpdatedStatementOrderList(QueryUpdatedStatementOrderListDTO request);

    /**
     * 更新账单明细
     */
    void updateStatementOrderList(StatementIdDTO request);

    /**
     * 修改账单中订单的要付金额
     */
    void updateStatementSupplyOrderPayAmt(ModifySettlementOrderPayAmtDTO request);

    /**
     * 通知收款
     */
    void notifyCollectionOfStatement(NotifyCollectionOfStatementDTO request);

    /**
     * 通知付款
     */
    Integer notifyPaymentOfStatement(NotifyPaymentOfStatementDTO request);

    /**
     * 修改账单名称
     */
    void modifyStatementName(ModifyStatementNameDTO request);

    /**
     * 修改结算日期
     */
    void modifySettlementDate(ModifySettlementDateDTO request);

    /**
     * 创建自助结算记账工单任务
     */
    void createSettleWorkOrder(CreateSettleWorkOrderDTO request);

    /**
     * 开启记账工单任务
     */
    void startSettleWorkOrder(CreateSettleWorkOrderDTO request);

    /**
     * 取消自助结算记账工单任务
     */
    void cancelSettleWorkOrder(CancelSettleWorkOrderDTO request);

    /**
     * 查询供货单待退款明细接口
     */
    List<SupplierOrderRefundDTO> querySupplierOrderRefundList(QuerySupplierOrderRefundDTO request);
}
