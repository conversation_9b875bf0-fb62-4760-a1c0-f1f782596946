package com.tiangong.finance.statement.domain;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:07
 * @Description:
 */

@Data
@Table(name = "f_non_vcc_auto_bill_config")
public class NonVccAutoBillConfigPO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 供应商编码
     */
    @Column(name = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Column(name = "supplier_name")
    private String supplierName;

    /**
     * 酒店id
     */
    @Column(name = "hotel_id")
    private Long hotelId;

    /**
     * 酒店名称
     */
    @Column(name = "hotel_name")
    private String hotelName;

    /**
     * 酒店当地时区 (例如: UTC+7)
     */
    @Column(name = "hotel_local_timezone")
    private String hotelLocalTimezone;

    /**
     * 天宫付款方式 0-酒店给我方发送付款链接 1-银行转账 2-WorldFirst账户收款
     */
    @Column(name = "payment_method")
    private Integer paymentMethod;

    /**
     * 付款链接发送邮箱 (英文逗号进行分隔，最多支持3个邮箱)
     */
    @Column(name = "payment_link_emails")
    private String paymentLinkEmails;

    /**
     * 酒店付款联系人
     */
    @Column(name = "hotel_payment_contact_person")
    private String hotelPaymentContactPerson;

    /**
     * 酒店收款开户行
     */
    @Column(name = "hotel_collection_bank")
    private String hotelCollectionBank;

    /**
     * 酒店收款账户名
     */
    @Column(name = "hotel_collection_account_name")
    private String hotelCollectionAccountName;

    /**
     * 酒店收款账号
     */
    @Column(name = "hotel_collection_account_number")
    private String hotelCollectionAccountNumber;

    /**
     * WorldFirst账户号
     */
    @Column(name = "world_first_account_number")
    private String worldFirstAccountNumber;

    /**
     * 备注
     */
    @Column(name = "remarks")
    private String remarks;

    /**
     * 出账规则 0-按订单 1-按日 2-按周 3-按半月 4-按月
     */
    @Column(name = "billing_rule")
    private Integer billingRule;

    /**
     * 出账日期类型 0-入住日前 1-入住日当天 2-离店日当天 3-离店日后
     */
    @Column(name = "billing_date_type")
    private Integer billingDateType;

    /**
     * 出账日期天数
     */
    @Column(name = "billing_date_days")
    private Integer billingDateDays;

    /**
     * 出账时间窗口: 酒店当地开始时间 (HH:mm:ss)
     */
    @Column(name = "hotel_local_start_time")
    private LocalTime hotelLocalStartTime;

    /**
     * 出账时间窗口: 酒店当地结束时间 (HH:mm:ss)
     */
    @Column(name = "hotel_local_end_time")
    private LocalTime hotelLocalEndTime;

    /**
     * 出账时间窗口: 系统开始时间 (HH:mm:ss)
     */
    @Column(name = "system_start_time")
    private LocalTime systemStartTime;

    /**
     * 出账时间窗口: 系统结束时间 (HH:mm:ss)
     */
    @Column(name = "system_end_time")
    private LocalTime systemEndTime;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(name = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @Column(name = "updated_dt")
    private Date updatedDt;
}
