package com.tiangong.finance.statement.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.ConvertTimeZoneResp;
import com.tiangong.finance.statement.domain.resp.GetNonVccAutoBillConfigDetailResp;
import com.tiangong.finance.statement.domain.resp.NonVccAutoBillConfigPageResp;

/**
 * <AUTHOR>
 * @Date 2025/6/5 17:30
 * @Description:
 */

public interface NonVccAutoBillConfigService {
    /**
     * 保存或更新非VCC自动出账配置
     */
    void saveOrUpdateNonVccAutoBillConfig(SaveOrUpdateNonVccAutoBillConfigReq request);

    /**
     * 删除非VCC自动出账配置
     */
    void deleteNonVccAutoBillConfig(DeleteNonVccAutoBillConfigReq  request);

    /**
     * 查询非VCC自动出账配置详情
     */
    GetNonVccAutoBillConfigDetailResp getNonVccAutoBillConfigDetail(GetNonVccAutoBillConfigDetailReq  request);

    /**
     * 分页查询非VCC自动出账配置
     */
    PaginationSupportDTO<NonVccAutoBillConfigPageResp> nonVccAutoBillConfigPage(NonVccAutoBillConfigPageReq request);

    /**
     * 时间转换
     */
    ConvertTimeZoneResp convertTimeZone(ConvertTimeZoneReq request);

    /**
     * 非VCC自动出账定时任务
     */
    void nonVccAutoBillTask(String param);
}
