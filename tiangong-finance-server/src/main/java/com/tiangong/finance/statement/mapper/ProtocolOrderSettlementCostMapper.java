package com.tiangong.finance.statement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.finance.statement.domain.ProtocolOrderSettlementCostPO;
import com.tiangong.finance.statement.domain.req.ProtocolOrderSettlementCostReq;

import java.util.List;

/**
 * 协议订单结算成本
 */
public interface ProtocolOrderSettlementCostMapper extends BaseMapper<ProtocolOrderSettlementCostPO> {

    /**
     * 批量更新协议订单结算成本
     */
    void batchUpdateProtocolOrderSettlementCost(List<ProtocolOrderSettlementCostPO> poList);

    /**
     * 查询协议订单结算成本列表(分页)
     */
    IPage<ProtocolOrderSettlementCostPO> selectProtocolOrderSettlementCostPage(IPage<?> page, ProtocolOrderSettlementCostReq req);
}