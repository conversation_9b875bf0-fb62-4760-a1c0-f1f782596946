package com.tiangong.finance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExcelTitleEnum {
    /**
     * 供货单号
     */
    PAY_SERIAL_NO(0,"供货单号"),
    /**
     * VCC唯一标识ID
     */
    SWIPING_CARD_AMOUNT_CURRENCY(1,"VCC唯一标识ID"),
    /**
     * 刷卡币种
     */
    SWIPING_CARD_AMOUNT(2,"刷卡币种"),
    /**
     * 刷卡金额
     */
    SWIPING_CARD_RATE(3,"刷卡金额"),
    /**
     * 刷卡汇率
     */
    SWIPING_CARD_DT(4,"刷卡汇率"),
    /**
     * 结算币种
     */
    SETTLE_AMOUNT_CURRENCY(5,"结算币种"),
    /**
     * 结算金额
     */
    SETTLE_AMOUNT(6,"结算金额"),
    /**
     * 刷卡时间
     */
    SUPPLY_ORDER_CODE(7,"刷卡时间")
    ;

    public final int key;
    public final String value;

    public static int getKeyByValue(String value) {
        int key = 0;
        for(ExcelTitleEnum titleEnum : ExcelTitleEnum.values()) {
            if(titleEnum.value.equals(value.trim())) {
                key = titleEnum.key;
                break;
            }
        }
        return key;
    }

}
