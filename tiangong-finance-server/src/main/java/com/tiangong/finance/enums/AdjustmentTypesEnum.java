package com.tiangong.finance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 汇率调整类型枚举
 * 
 * <AUTHOR>
 * @date 2025/1/16
 */
@Getter
@AllArgsConstructor
public enum AdjustmentTypesEnum {

    /**
     * 数值加幅调整类型
     */
    DATA_ADD(0, "数值加幅调整"),

    /**
     * 数值减幅调整类型
     */
    DATA_SUBTRACT(1, "数值减幅调整"),

    /**
     * 百分比加幅调整类型
     */
    PERCENTAGE_ADD(2, "百分比加幅调整"),

    /**
     * 百分比减幅调整类型
     */
    PERCENTAGE_SUBTRACT(3, "百分比减幅调整"),

    /**
     * 不调整类型
     */
    NO_ADJUSTMENT(4, "不调整");

    /**
     * 调整类型编码
     */
    private final Integer code;

    /**
     * 调整类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static AdjustmentTypesEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AdjustmentTypesEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescriptionByCode(Integer code) {
        AdjustmentTypesEnum type = getByCode(code);
        return type != null ? type.getDescription() : null;
    }
}
