package com.tiangong.finance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 融资数据类型枚举
 * 
 * <AUTHOR>
 * @date 2025/1/9
 */
@Getter
@AllArgsConstructor
public enum FinancingDataTypeEnum {

    /**
     * 订单
     */
    ORDER(1, "订单"),

    /**
     * 账单
     */
    BILL(2, "账单");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static FinancingDataTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FinancingDataTypeEnum dataType : values()) {
            if (dataType.getCode().equals(code)) {
                return dataType;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescriptionByCode(Integer code) {
        FinancingDataTypeEnum dataType = getByCode(code);
        return dataType != null ? dataType.getDescription() : null;
    }
}
