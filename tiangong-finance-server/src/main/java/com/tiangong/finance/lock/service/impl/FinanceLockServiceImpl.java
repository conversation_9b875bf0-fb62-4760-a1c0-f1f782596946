package com.tiangong.finance.lock.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.finance.lock.mapper.FinanceLockMapper;
import com.tiangong.finance.lock.service.FinanceLockService;
import com.tiangong.finance.remote.lock.request.FinanceLockOrderDTO;
import com.tiangong.finance.remote.lock.request.FinanceLockSupplyOrderDTO;
import com.tiangong.finance.remote.lock.request.QueryOrderFinanceLockListDTO;
import com.tiangong.finance.remote.lock.request.QuerySupplyOrderFinanceLockListDTO;
import com.tiangong.finance.remote.lock.response.GuestDTO;
import com.tiangong.finance.remote.lock.response.OrderFinanceLockDTO;
import com.tiangong.finance.remote.lock.response.SupplyOrderFinanceLockDTO;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FinanceLockServiceImpl implements FinanceLockService {

    @Autowired
    private FinanceLockMapper financeLockMapper;

    @Override
    public PaginationSupportDTO<OrderFinanceLockDTO> queryOrderList(QueryOrderFinanceLockListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            request.setAgentName(null);
        }
        List<OrderFinanceLockDTO> list = financeLockMapper.queryOrderList(request);
        PageInfo<OrderFinanceLockDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<OrderFinanceLockDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public void lockOrder(FinanceLockOrderDTO request) {
        //多次出账，可解锁
        financeLockMapper.lockOrder(request);
    }

    @Override
    public PaginationSupportDTO<SupplyOrderFinanceLockDTO> querySupplyOrderList(QuerySupplyOrderFinanceLockListDTO request) {
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            request.setSupplierName(null);
        }
        IPage<HotelRecommendReq> page = new Page<>(request.getCurrentPage(), request.getPageSize());
        IPage<SupplyOrderFinanceLockDTO> iPage = financeLockMapper.selectSupplyOrderPage(page, request);
        List<SupplyOrderFinanceLockDTO> collect = iPage.getRecords().stream().peek(item -> {
            if (StrUtilX.isNotEmpty(item.getRoomNumbers()) && CollUtilX.isNotEmpty(item.getGuests())) {
                Map<Integer, String> guestMap = new HashMap<>();
                for (GuestDTO guest : item.getGuests()) {
                    if (guestMap.containsKey(guest.getRoomNumber())) {
                        String name = guestMap.get(guest.getRoomNumber()) + "," + guest.getName();
                        guestMap.put(guest.getRoomNumber(), name);
                    } else {
                        guestMap.put(guest.getRoomNumber(), guest.getName());
                    }
                }
                List<String> roomNumbers = StrUtilX.stringToList(item.getRoomNumbers(), ",");
                StringBuilder sb = new StringBuilder();
                for (String roomNumber : roomNumbers) {
                    if (StrUtilX.isNotEmpty(sb.toString())) {
                        sb.append(",");
                    }
                    sb.append(guestMap.get(Integer.parseInt(roomNumber)));
                }
                item.setGuest(sb.toString());
            }
        }).collect(Collectors.toList());

        PaginationSupportDTO<SupplyOrderFinanceLockDTO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(collect);
        paginationSupportDTO.setCurrentPage((int) iPage.getCurrent());
        paginationSupportDTO.setPageSize((int) iPage.getSize());
        paginationSupportDTO.setTotalCount(iPage.getTotal());
        paginationSupportDTO.setTotalPage((int) iPage.getPages());
        return paginationSupportDTO;
    }

    @Override
    public Response lockSupplyOrder(FinanceLockSupplyOrderDTO request) {
        Response response = new Response();
        Integer canLock = financeLockMapper.checkSupplyOrderCanLock(request.getSupplyOrderId());
        if (canLock != null) {
            return new Response(ResultCodeEnum.FAILURE.code, ErrorCodeEnum.BILLING_WORK_ORDER_PROCESSING_TASK_IS_IN_PROGRESS_AND_CANNOT_BE_UNLOCKED.errorCode, ErrorCodeEnum.BILLING_WORK_ORDER_PROCESSING_TASK_IS_IN_PROGRESS_AND_CANNOT_BE_UNLOCKED.errorDesc);
        }

        financeLockMapper.lockSupplyOrder(request);
        response.setResult(ResultCodeEnum.SUCCESS.code);
        return response;
    }


    @Override
    public void lockSupplyOrderList(FinanceLockSupplyOrderDTO request) {
        financeLockMapper.lockSupplyOrderList(request);
    }
}
