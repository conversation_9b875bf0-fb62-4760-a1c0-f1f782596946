package com.tiangong.finance.lock.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.lock.service.FinanceLockService;
import com.tiangong.finance.remote.lock.request.FinanceLockOrderDTO;
import com.tiangong.finance.remote.lock.request.FinanceLockSupplyOrderDTO;
import com.tiangong.finance.remote.lock.request.QueryOrderFinanceLockListDTO;
import com.tiangong.finance.remote.lock.request.QuerySupplyOrderFinanceLockListDTO;
import com.tiangong.finance.remote.lock.response.OrderFinanceLockDTO;
import com.tiangong.finance.remote.lock.response.SupplyOrderFinanceLockDTO;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class FinanceLockServer extends BaseController {

    /**
     * 查询所有锁定状态的标识值
     */
    private static final String ALL_LOCK_STATUS = "-1";

    @Autowired
    private FinanceLockService financeLockService;

    /**
     * 财务订单锁查询
     */
    @PostMapping("/finance/lock/queryOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<OrderFinanceLockDTO>> queryOrderList(@RequestBody QueryOrderFinanceLockListDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        if (StrUtilX.isNotEmpty(request.getLockStatus()) && request.getLockStatus().equals(ALL_LOCK_STATUS)) {
            request.setLockStatus(null);
        }
        return Response.success(financeLockService.queryOrderList(request));
    }

    /**
     * 财务订单加锁
     */
    @PostMapping("/finance/lock/lockOrder")
    public Response<Object> lockOrder(@RequestBody FinanceLockOrderDTO request) {
        request.setOperator(super.getUserName());
        financeLockService.lockOrder(request);
        return Response.success();
    }

    /**
     * 财务供货单锁查询
     */
    @PostMapping("/finance/lock/querySupplyOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<SupplyOrderFinanceLockDTO>> querySupplyOrderList(@RequestBody QuerySupplyOrderFinanceLockListDTO request) {
        request.setCompanyCode(super.getCompanyCode());
        if (StrUtilX.isNotEmpty(request.getLockStatus()) && request.getLockStatus().equals(ALL_LOCK_STATUS)) {
            request.setLockStatus(null);
        }
        return Response.success(financeLockService.querySupplyOrderList(request));
    }

    /**
     * 财务订单加锁
     */
    @PostMapping("/finance/lock/lockSupplyOrder")
    @PreAuthorize("@syyo.check('finance')")
    public Response lockSupplyOrder(@RequestBody FinanceLockSupplyOrderDTO request) {
        request.setOperator(super.getUserName());
        return financeLockService.lockSupplyOrder(request);
    }
}
