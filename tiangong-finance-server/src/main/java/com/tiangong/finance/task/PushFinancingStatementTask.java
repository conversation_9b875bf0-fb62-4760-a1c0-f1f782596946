package com.tiangong.finance.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.finance.financing.service.FinancingPushTaskService;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 推送融资账单任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PushFinancingStatementTask {

    @Autowired
    private FinancingPushTaskService financingPushTaskService;

    @XxlJob("pushFinancingStatementTask")
    public void pushFinancingStatementTask() {
        try {
            XxlJobHelper.log("执行推送融资账单任务开始");
            String param = XxlJobHelper.getJobParam();
            Map<String, String> paramMap = new HashMap<>();
            if (StrUtilX.isNotEmpty(param)) {
                paramMap = JSON.parseObject(param, new TypeReference<Map<String, String>>(){});
            }
            financingPushTaskService.pushFinancingStatementTask(paramMap);
            XxlJobHelper.log("执行推送融资账单任务结束");
        } catch (Exception e) {
            log.error("执行推送融资账单任务异常", e);
            XxlJobHelper.log("执行推送融资账单任务异常", e);
        }
    }
}
