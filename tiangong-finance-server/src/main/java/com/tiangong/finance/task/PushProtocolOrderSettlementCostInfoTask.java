package com.tiangong.finance.task;

import com.tiangong.finance.statement.service.ProtocolOrderSettlementCostService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 推送协议订单结算成本信息任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PushProtocolOrderSettlementCostInfoTask {

    @Autowired
    private ProtocolOrderSettlementCostService protocolOrderSettlementCostService;

    @XxlJob("pushProtocolOrderSettlementCostInfoTask")
    public void pushProtocolOrderSettlementCostInfoTask() {
        try {
            XxlJobHelper.log("执行推送协议订单结算成本信息任务开始");
            protocolOrderSettlementCostService.pushProtocolOrderSettlementCostInfoTask(null);
            XxlJobHelper.log("执行推送协议订单结算成本信息任务结束");
        } catch (Exception e) {
            log.error("执行推送协议订单结算成本信息任务异常", e);
            XxlJobHelper.log("执行推送协议订单结算成本信息任务异常", e);
        }
    }
}
