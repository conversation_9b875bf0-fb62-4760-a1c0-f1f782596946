package com.tiangong.finance.task;

import com.alibaba.fastjson.JSON;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.exception.SysException;
import com.tiangong.finance.remote.statement.request.CreateAgentStatementDTO;
import com.tiangong.finance.remote.statement.request.CreateOrderStatementTaskDTO;
import com.tiangong.finance.statement.domain.OrderStatementTaskPO;
import com.tiangong.finance.statement.dto.OrderStatementTaskDTO;
import com.tiangong.finance.statement.service.AgentStatementService;
import com.tiangong.finance.statement.service.OrderStatementTaskService;
import com.tiangong.keys.OrderConstantKey;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.DateUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 订单记账账单创建
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCreateStatementTask extends IJobHandler {

    /**
     * 最大任务批次大小
     */
    private static final Integer MAX_TASK_BATCH_SIZE = 5;

    @Autowired
    private AgentStatementService agentStatementService;

    @Autowired
    private OrderStatementTaskService orderStatementTaskService;

    @Override
    @XxlJob("orderCreateStatementTask")
    public void execute() {
        XxlJobHelper.log("订单记账创建账单定时任务开始。。。");
        for (int i = 0; i < MAX_TASK_BATCH_SIZE; i++) {
            if (!RedisTemplateX.hasKey(RedisKey.ORDER_STATEMENT_TASK_CREATE_STATEMENT)) {
                return;
            }
            String taskCode = RedisTemplateX.lRightPop(RedisKey.ORDER_STATEMENT_TASK_CREATE_STATEMENT);
            try {

                OrderStatementTaskPO orderStatementTaskPO = orderStatementTaskService.queryOrderStatementTask(taskCode);
                CreateOrderStatementTaskDTO createOrderStatementTaskDTO = JSON.parseObject(orderStatementTaskPO.getTaskJson(), CreateOrderStatementTaskDTO.class);
                //1.创建账单
                CreateAgentStatementDTO createAgentStatementDTO = new CreateAgentStatementDTO();
                createAgentStatementDTO.setAgentCode(createOrderStatementTaskDTO.getAgentCode());
                createAgentStatementDTO.setOrderNumber(1);
                //账单名称：xx客户xx年xx月xx日账单
                createAgentStatementDTO.setStatementName(createOrderStatementTaskDTO.getAgentName() + DateUtilX.dateToString(new Date(), DateUtilX.SPECIFIC_DATE) + "账单");
                //结算日期：当日+1
                createAgentStatementDTO.setSettlementDate(DateUtilX.dateToString(DateUtilX.addDate(new Date(), 1)));
                createAgentStatementDTO.setOrderCode(createOrderStatementTaskDTO.getOrderCode());
                createAgentStatementDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
                createAgentStatementDTO.setTaskCode(taskCode);
                createAgentStatementDTO.setReceiveAmt(createOrderStatementTaskDTO.getReceiveAmt());
                createAgentStatementDTO.setOperator(OrderConstantKey.PERSON_PAY_OPERATOR);
                if (orderStatementTaskPO.getTaskSource() == 1){
                    createAgentStatementDTO.setOperator(OrderConstantKey.PERSON_REFUND_OPERATOR);
                    createAgentStatementDTO.setReceiveAmt(createOrderStatementTaskDTO.getReceiveAmt().negate());
                }
                createAgentStatementDTO.setAgentName(createOrderStatementTaskDTO.getAgentName());
                agentStatementService.createStatement(createAgentStatementDTO);

            } catch (SysException e) {
                //创建失败，更新状态
                OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
                orderStatementTaskDTO.setTaskCode(taskCode);
                orderStatementTaskDTO.setFailReason(e.getMessage());
                orderStatementTaskDTO.setTaskStatus(0);
                orderStatementTaskService.updateOrderStatementTask(orderStatementTaskDTO);
            } catch (Exception e) {
                log.error("订单记账创建账单定时任务异常", e);
                //创建失败，更新状态
                OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
                orderStatementTaskDTO.setTaskCode(taskCode);
                orderStatementTaskDTO.setFailReason("创建账单异常");
                orderStatementTaskDTO.setTaskStatus(0);
                orderStatementTaskService.updateOrderStatementTask(orderStatementTaskDTO);
            }
        }
        XxlJobHelper.log("订单记账创建账单定时任务结束。。。");
    }
}
