package com.tiangong.finance.task;

import com.tiangong.finance.financing.service.FinancingPushTaskService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 推送融资离店订单任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PushFinancingCheckOutOrderTask {

    @Autowired
    private FinancingPushTaskService financingPushTaskService;

    @XxlJob("pushFinancingCheckOutOrderTask")
    public void pushFinancingCheckOutOrderTask() {
        try {
            XxlJobHelper.log("执行推送融资离店订单任务开始");
            String param = XxlJobHelper.getJobParam();
            if (StrUtilX.isNotEmpty(param)) {
                financingPushTaskService.pushFinancingCheckOutOrderTask(param, null);
            } else {
                String id = RedisTemplateX.setPop(RedisKey.FINANCING_PUSH_IDS_CHECK_OUT_ORDER);
                if (StrUtilX.isNotEmpty(id)) {
                    financingPushTaskService.pushFinancingCheckOutOrderTask(null, id);
                }
            }
            XxlJobHelper.log("执行推送融资离店订单任务结束");
        } catch (Exception e) {
            log.error("执行推送融资离店订单任务异常", e);
            XxlJobHelper.log("执行推送融资离店订单任务异常", e);
        }
    }
}
