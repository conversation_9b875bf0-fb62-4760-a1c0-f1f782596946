package com.tiangong.finance.task;

import com.tiangong.enums.StatementTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.StatementStatusEnum;
import com.tiangong.finance.remote.statement.request.ModifyStatementStatusDTO;
import com.tiangong.finance.statement.domain.SettleWorkOrderPO;
import com.tiangong.finance.statement.dto.SettleWorkOrderDTO;
import com.tiangong.finance.statement.service.SettleWorkOrderService;
import com.tiangong.finance.statement.service.SupplierStatementService;
import com.tiangong.keys.RedisKey;
import com.tiangong.keys.SettleConstantKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自助结算账单创建
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SettleConfirmStatementTask extends IJobHandler {

    /**
     * 最大任务批次大小
     */
    private static final Integer MAX_TASK_BATCH_SIZE = 5;

    @Autowired
    private SupplierStatementService supplierStatementService;

    @Autowired
    private SettleWorkOrderService settleWorkOrderService;

    @Override
    @XxlJob("settleConfirmStatementTask")
    public void execute() {
        XxlJobHelper.log("自助结算确认账单定时任务开始。。。");
        for (int i = 0; i < MAX_TASK_BATCH_SIZE; i++) {
            if (!RedisTemplateX.hasKey(RedisKey.SETTLE_WORK_ORDER_CONFIRM_STATEMENT)) {
                return;
            }
            String settleTaskCode = RedisTemplateX.lRightPop(RedisKey.SETTLE_WORK_ORDER_CONFIRM_STATEMENT);
            try {
                SettleWorkOrderPO settleWorkOrderPO = settleWorkOrderService.querySettleWorkOrder(settleTaskCode);
                //2.确认账单
                ModifyStatementStatusDTO modifyStatementStatusDTO = new ModifyStatementStatusDTO();
                modifyStatementStatusDTO.setStatementId(settleWorkOrderPO.getStatementId());
                modifyStatementStatusDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
                modifyStatementStatusDTO.setStatementStatus(StatementStatusEnum.CONFIRMED.key);
                modifyStatementStatusDTO.setSupplierCode(settleWorkOrderPO.getSupplierCode());
                modifyStatementStatusDTO.setOperator(SettleConstantKey.SETTLE_OPERATOR);
                supplierStatementService.modifyStatementStatus(modifyStatementStatusDTO);

                //确认成功，更新状态
                SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
                settleWorkOrderDTO.setSettleTaskCode(settleTaskCode);
                settleWorkOrderDTO.setTaskStatus(2);
                settleWorkOrderService.updateSettleWorkOrder(settleWorkOrderDTO);
            } catch (SysException e) {
                // 确认失败，通知自助结算，并更新状态
                SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
                settleWorkOrderDTO.setSettleTaskCode(settleTaskCode);
                settleWorkOrderDTO.setFailReason(e.getMessage());
                settleWorkOrderDTO.setTaskStatus(1);
                settleWorkOrderService.updateSettleWorkOrder(settleWorkOrderDTO);
            } catch (Exception e) {
                log.error("自助结算确认账单定时任务异常。。。", e);
                //创建失败，通知自助结算，并更新状态
                SettleWorkOrderDTO settleWorkOrderDTO = new SettleWorkOrderDTO();
                settleWorkOrderDTO.setSettleTaskCode(settleTaskCode);
                settleWorkOrderDTO.setFailReason("确认账单异常");
                settleWorkOrderDTO.setTaskStatus(1);
                settleWorkOrderService.updateSettleWorkOrder(settleWorkOrderDTO);
            }
        }
        XxlJobHelper.log("自助结算确认账单定时任务结束。。。");
    }
}
