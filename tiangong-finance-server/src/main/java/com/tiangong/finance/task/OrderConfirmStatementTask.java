package com.tiangong.finance.task;

import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.StatementStatusEnum;
import com.tiangong.finance.remote.statement.request.ModifyStatementStatusDTO;
import com.tiangong.finance.statement.domain.OrderStatementTaskPO;
import com.tiangong.finance.statement.dto.OrderStatementTaskDTO;
import com.tiangong.finance.statement.service.AgentStatementService;
import com.tiangong.finance.statement.service.OrderStatementTaskService;
import com.tiangong.keys.OrderConstantKey;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单记账确认账单
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderConfirmStatementTask extends IJobHandler {

    /**
     * 最大任务批次大小
     */
    private static final Integer MAX_TASK_BATCH_SIZE = 5;

    @Autowired
    private AgentStatementService agentStatementService;

    @Autowired
    private OrderStatementTaskService orderStatementTaskService;

    @Override
    @XxlJob("orderConfirmStatementTask")
    public void execute() {
        XxlJobHelper.log("订单记账确认账单定时任务开始。。。");
        for (int i = 0; i < MAX_TASK_BATCH_SIZE; i++) {
            if (!RedisTemplateX.hasKey(RedisKey.ORDER_STATEMENT_TASK_CONFIRM_STATEMENT)) {
                return;
            }
            String taskCode = RedisTemplateX.lRightPop(RedisKey.ORDER_STATEMENT_TASK_CONFIRM_STATEMENT);
            try {
                OrderStatementTaskPO orderStatementTaskPO = orderStatementTaskService.queryOrderStatementTask(taskCode);
                //2.确认账单
                ModifyStatementStatusDTO modifyStatementStatusDTO = new ModifyStatementStatusDTO();
                modifyStatementStatusDTO.setStatementId(orderStatementTaskPO.getStatementId());
                modifyStatementStatusDTO.setStatementStatus(StatementStatusEnum.CONFIRMED.key);
                modifyStatementStatusDTO.setAgentCode(orderStatementTaskPO.getAgentCode());
                modifyStatementStatusDTO.setOperator(OrderConstantKey.PERSON_PAY_OPERATOR);
                if (orderStatementTaskPO.getTaskSource() == 1){
                    modifyStatementStatusDTO.setOperator(OrderConstantKey.PERSON_REFUND_OPERATOR);
                }
                agentStatementService.modifyStatementStatus(modifyStatementStatusDTO);

                //确认成功，更新状态
                OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
                orderStatementTaskDTO.setTaskCode(taskCode);
                orderStatementTaskDTO.setTaskStatus(2);
                orderStatementTaskService.updateOrderStatementTask(orderStatementTaskDTO);
            } catch (SysException e) {
                // 确认失败，更新状态
                OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
                orderStatementTaskDTO.setTaskCode(taskCode);
                orderStatementTaskDTO.setFailReason(e.getMessage());
                orderStatementTaskDTO.setTaskStatus(1);
                orderStatementTaskService.updateOrderStatementTask(orderStatementTaskDTO);
            } catch (Exception e) {
                log.error("订单记账确认账单定时任务异常。。。", e);
                //创建失败，更新状态
                OrderStatementTaskDTO orderStatementTaskDTO = new OrderStatementTaskDTO();
                orderStatementTaskDTO.setTaskCode(taskCode);
                orderStatementTaskDTO.setFailReason("确认账单异常");
                orderStatementTaskDTO.setTaskStatus(1);
                orderStatementTaskService.updateOrderStatementTask(orderStatementTaskDTO);
            }
        }
        XxlJobHelper.log("订单记账确认账单定时任务结束。。。");
    }
}
