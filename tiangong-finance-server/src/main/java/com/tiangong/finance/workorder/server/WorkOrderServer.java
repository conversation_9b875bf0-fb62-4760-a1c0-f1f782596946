package com.tiangong.finance.workorder.server;

import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.remote.workorder.request.ConfirmWorkOrderDTO;
import com.tiangong.finance.remote.workorder.request.QueryWorkExportRequestDTO;
import com.tiangong.finance.remote.workorder.request.QueryWorkOrderListDTO;
import com.tiangong.finance.remote.workorder.request.WorkOrderIdDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderListResponseDTO;
import com.tiangong.finance.workorder.service.WorkOrderService;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@Slf4j
public class WorkOrderServer extends BaseController {

    /**
     * 查询所有财务类型的标识值
     */
    private static final String ALL_FINANCE_TYPE = "-1";

    /**
     * 查询所有工单状态的标识值
     */
    private static final String ALL_WORK_ORDER_STATUS = "-1";

    @Autowired
    private WorkOrderService workOrderService;

    @Autowired
    private HttpServletRequest httpServletRequest;

    /**
     * 财务工单查询
     */
    @PostMapping("/finance/workorder/queryWorkOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<WorkOrderListResponseDTO>> queryWorkOrderList(@RequestBody QueryWorkOrderListDTO request) {
        if (StrUtilX.isNotEmpty(request.getFinanceType()) && request.getFinanceType().equals(ALL_FINANCE_TYPE)) {
            request.setFinanceType(null);
        }

        if (StrUtilX.isNotEmpty(request.getWorkOrderStatus()) && request.getWorkOrderStatus().equals(ALL_WORK_ORDER_STATUS)) {
            request.setWorkOrderStatus(null);
        }
        request.setCompanyCode(super.getCompanyCode());
        PaginationSupportDTO<WorkOrderListResponseDTO> paginationSupportDTO = workOrderService.queryWorkOrderList(request);
        return Response.success(paginationSupportDTO);
    }

    /**
     * 导出财务工单
     */
    @PostMapping("/finance/workorder/exportWorkOrderList")
    @PreAuthorize("@syyo.check('finance')")
    public void exportWorkOrderList(@RequestBody QueryWorkExportRequestDTO request, HttpServletResponse response) {
        if (StrUtilX.isNotEmpty(request.getFinanceType()) && request.getFinanceType().equals(ALL_FINANCE_TYPE)) {
            request.setFinanceType(null);
        }

        if (StrUtilX.isNotEmpty(request.getWorkOrderStatus()) && request.getWorkOrderStatus().equals(ALL_WORK_ORDER_STATUS)) {
            request.setWorkOrderStatus(null);
        }
        String language = httpServletRequest.getHeader(HttpConstant.Language);
        request.setCompanyCode(super.getCompanyCode());
        workOrderService.exportWorkOrderList(language, request, response);
    }

    /**
     * 工单详情
     */
    @PostMapping("/finance/workorder/queryWorkOrderDetail")
    @PreAuthorize("@syyo.check('finance')")
    public Response<WorkOrderDTO> queryWorkOrderDetail(@RequestBody WorkOrderIdDTO request) {
        WorkOrderDTO workOrderDTO = workOrderService.queryWorkOrderDetail(request);
        return Response.success(workOrderDTO);
    }

    /**
     * 确认工单
     */
    @PostMapping("/finance/workorder/confirmWorkOrder")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> confirmWorkOrder(@RequestBody ConfirmWorkOrderDTO request) {
        request.setOperator(getUserName());
        workOrderService.confirmWorkOrder(request);
        return Response.success();
    }

    /**
     * 删除工单
     */
    @PostMapping("/finance/workorder/deleteWorkOrder")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> deleteWorkOrder(@RequestBody WorkOrderIdDTO request) {
        request.setOperator(getUserName());
        workOrderService.deleteWorkOrder(request);
        return Response.success();
    }

    /**
     * 修改工单备注
     */
    @PostMapping("/finance/workorder/editWorkOrderDetail")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> editWorkOrderDetail(@RequestBody WorkOrderDTO request) {
        request.setUpdatedBy(getUserName());
        workOrderService.editWorkOrderDetail(request);
        return Response.success();
    }
}
