package com.tiangong.finance.workorder.service.impl;

import com.tiangong.common.remote.SequenceRemote;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.enums.SystemCodeEnum;
import com.tiangong.exception.ParameterException;
import com.tiangong.file.remote.FileRemote;
import com.tiangong.file.req.FileReq;
import com.tiangong.finance.enums.WorkOrderStatusEnum;
import com.tiangong.finance.remote.ExchangeRateRemote;
import com.tiangong.finance.remote.workorder.request.*;
import com.tiangong.finance.remote.workorder.response.NotificationLogDTO;
import com.tiangong.finance.workorder.domain.WorkOrderItemPO;
import com.tiangong.finance.workorder.domain.WorkOrderPO;
import com.tiangong.finance.workorder.mapper.WorkOrderItemMapper;
import com.tiangong.finance.workorder.mapper.WorkOrderMapper;
import com.tiangong.finance.workorder.service.FinanceNofityService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class FinanceNofityServiceImpl implements FinanceNofityService {


    @Autowired
    private WorkOrderMapper workOrderMapper;

    @Autowired
    private WorkOrderItemMapper workOrderItemMapper;

    @Autowired
    private SequenceRemote sequenceRemote;

    @Autowired
    private ExchangeRateRemote exchangeRateRemote;

    @Autowired
    private FileRemote fileRemote;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer notifyCollection(NotifyCollectionDTO request) {
        // 1.创建工单
        WorkOrderPO workOrderInsert = new WorkOrderPO();
        BeanUtils.copyProperties(request, workOrderInsert);
        workOrderInsert.setCreatedDt(new Date());
        workOrderInsert.setCreatedBy(request.getCreatedBy());
        workOrderInsert.setAmount(request.getCollectionAmt());
        workOrderInsert.setWorkOrderStatus(WorkOrderStatusEnum.UN_SETTLE.key);

        if (request.getNotifyItemDTOList() == null || request.getNotifyItemDTOList().size() == 0) {
            throw new ParameterException("通知明细不能为空");
        } else if (request.getNotifyItemDTOList().size() == 1) {
            workOrderInsert.setBusinessCode(request.getNotifyItemDTOList().get(0).getBusinessCode());
        } else {
            StringBuilder sb = new StringBuilder();
            for (NotifyItemDTO notifyItemDTO : request.getNotifyItemDTOList()) {
                sb.append(notifyItemDTO.getBusinessCode()).append(",");
            }
            String businessCode = sb.deleteCharAt(sb.length() - 1).toString();
            workOrderInsert.setBusinessCode(businessCode);
        }

        //获取编码
        String code = RedisTemplateX.lRightPop(SystemCodeEnum.WORKORDERCODE.code);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", SystemCodeEnum.WORKORDERCODE.code);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(SystemCodeEnum.WORKORDERCODE.code);
        }
        workOrderInsert.setWorkOrderCode(code);
        //根据币种获取汇率
        BigDecimal rate = BigDecimal.ONE;
        if (workOrderInsert.getCurrency() != null) {
            rate = RedisUtil.getRateToOrgCurrency(workOrderInsert.getCurrency(), CompanyDTO.COMPANY_CODE);
        }
        workOrderInsert.setRate(rate);
        workOrderMapper.insert(workOrderInsert);
        // 2.保存工单明细
        if (request.getNotifyItemDTOList().size() > 1) {
            List<WorkOrderItemPO> workOrderItemInsertList = new ArrayList<>();
            for (NotifyItemDTO notifyItemDTO : request.getNotifyItemDTOList()) {
                WorkOrderItemPO workOrderItemPO = new WorkOrderItemPO();
                workOrderItemPO.setWorkOrderId(workOrderInsert.getId());
                workOrderItemPO.setBusinessCode(notifyItemDTO.getBusinessCode());
                workOrderItemPO.setAmount(notifyItemDTO.getAmount());
                workOrderItemInsertList.add(workOrderItemPO);
            }
            workOrderItemMapper.insertList(workOrderItemInsertList);
        }
        // 3.保存工单附件
        if (request.getPhotoList() != null && request.getPhotoList().size() > 0) {
            for (WorkOrderAttchDTO photo : request.getPhotoList()) {
                FileReq fileReq = new FileReq();
                fileReq.setFileId(photo.getFileId());
                fileReq.setObjId(workOrderInsert.getId().toString());
                fileReq.setTableName("f_work_order");
                fileRemote.update(fileReq);
            }
        }
        return workOrderInsert.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer notifyPayment(NotifyPaymentDTO request) {
        // 1.创建工单
        WorkOrderPO workOrderInsert = new WorkOrderPO();
        BeanUtils.copyProperties(request, workOrderInsert);
        workOrderInsert.setCreatedDt(new Date());
        workOrderInsert.setCreatedBy(request.getCreatedBy());
        workOrderInsert.setAmount(BigDecimal.ZERO.subtract(request.getPaymentAmt()));
        workOrderInsert.setWorkOrderStatus(WorkOrderStatusEnum.UN_SETTLE.key);
        if (request.getNotifyItemDTOList() == null || request.getNotifyItemDTOList().size() == 0) {
            throw new ParameterException("通知明细不能为空");
        } else if (request.getNotifyItemDTOList().size() == 1) {
            workOrderInsert.setBusinessCode(request.getNotifyItemDTOList().get(0).getBusinessCode());
        } else {
            StringBuilder sb = new StringBuilder();
            for (NotifyItemDTO notifyItemDTO : request.getNotifyItemDTOList()) {
                sb.append(notifyItemDTO.getBusinessCode()).append(",");
            }
            String businessCode = sb.deleteCharAt(sb.length() - 1).toString();
            workOrderInsert.setBusinessCode(businessCode);
        }

        //获取编码
        String code = RedisTemplateX.lRightPop(SystemCodeEnum.WORKORDERCODE.code);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", SystemCodeEnum.WORKORDERCODE.code);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(SystemCodeEnum.WORKORDERCODE.code);
        }
        workOrderInsert.setWorkOrderCode(code);
        //根据币种获取汇率
        BigDecimal rate = BigDecimal.ONE;
        if (workOrderInsert.getCurrency() != null) {
            rate = RedisUtil.getRateToOrgCurrency(workOrderInsert.getCurrency(), CompanyDTO.COMPANY_CODE);
        }
        workOrderInsert.setRate(rate);
        workOrderMapper.insert(workOrderInsert);
        // 2.保存工单明细
        if (request.getNotifyItemDTOList().size() > 1) {
            List<WorkOrderItemPO> workOrderItemInsertList = new ArrayList<>();
            for (NotifyItemDTO notifyItemDTO : request.getNotifyItemDTOList()) {
                WorkOrderItemPO workOrderItemPO = new WorkOrderItemPO();
                workOrderItemPO.setWorkOrderId(workOrderInsert.getId());
                workOrderItemPO.setBusinessCode(notifyItemDTO.getBusinessCode());
                workOrderItemPO.setAmount(notifyItemDTO.getAmount());
                workOrderItemInsertList.add(workOrderItemPO);
            }
            workOrderItemMapper.insertList(workOrderItemInsertList);
        }
        // 3.保存工单附件
        if (request.getPhotoList() != null && request.getPhotoList().size() > 0) {
            for (WorkOrderAttchDTO photo : request.getPhotoList()) {
                FileReq fileReq = new FileReq();
                fileReq.setFileId(photo.getFileId());
                fileReq.setObjId(workOrderInsert.getId().toString());
                fileReq.setTableName("f_work_order");
                fileRemote.update(fileReq);
            }
        }
        return workOrderInsert.getId();
    }

    @Override
    public List<NotificationLogDTO> financeNotificationLogList(BusinessCodeDTO request) {
        if (StrUtilX.isNotEmpty(request.getBusinessCode())) {
            return workOrderMapper.financeNotificationLogList(request);
        } else {
            return new ArrayList<>();
        }
    }
}
