package com.tiangong.finance.workorder.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.remote.workorder.request.ConfirmWorkOrderDTO;
import com.tiangong.finance.remote.workorder.request.QueryWorkExportRequestDTO;
import com.tiangong.finance.remote.workorder.request.QueryWorkOrderListDTO;
import com.tiangong.finance.remote.workorder.request.WorkOrderIdDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderListResponseDTO;

import javax.servlet.http.HttpServletResponse;

public interface WorkOrderService {

    /**
     * 财务工单查询
     */
    PaginationSupportDTO<WorkOrderListResponseDTO> queryWorkOrderList(QueryWorkOrderListDTO request);

    /**
     * 导出财务工单
     * @param request
     */
    void exportWorkOrderList(String language,QueryWorkExportRequestDTO request, HttpServletResponse response);

    /**
     * 工单详情
     */
    WorkOrderDTO queryWorkOrderDetail(WorkOrderIdDTO request);

    /**
     * 确认工单
     */
    void confirmWorkOrder(ConfirmWorkOrderDTO request);

    /**
     * 删除工单
     */
    void deleteWorkOrder(WorkOrderIdDTO request);

    /**
     * 编辑工单详情
     */
    void editWorkOrderDetail(WorkOrderDTO request);
}
