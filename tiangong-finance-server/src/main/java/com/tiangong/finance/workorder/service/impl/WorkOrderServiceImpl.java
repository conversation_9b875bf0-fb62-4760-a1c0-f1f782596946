package com.tiangong.finance.workorder.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.common.Response;
import com.tiangong.dto.common.BankBalanceChangeReq;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.file.remote.FileRemote;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import com.tiangong.finance.convert.DTOConvert;
import com.tiangong.finance.enums.BusinessTypeEnum;
import com.tiangong.finance.enums.WorkOrderStatusEnum;
import com.tiangong.finance.remote.workorder.request.*;
import com.tiangong.finance.remote.workorder.response.WorkOrderDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderExportResponseDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderListResponseDTO;
import com.tiangong.finance.statement.dto.CancelStatementWorkOrderDTO;
import com.tiangong.finance.statement.dto.ConfirmStatementWorkOrderDTO;
import com.tiangong.finance.statement.service.AgentStatementPayHandle;
import com.tiangong.finance.statement.service.SupplierStatementPayHandle;
import com.tiangong.finance.workorder.domain.WorkOrderAttchPO;
import com.tiangong.finance.workorder.domain.WorkOrderPO;
import com.tiangong.finance.workorder.mapper.WorkOrderAttchMapper;
import com.tiangong.finance.workorder.mapper.WorkOrderMapper;
import com.tiangong.finance.workorder.service.WorkOrderService;
import com.tiangong.order.remote.OrderFinanceHandleRemote;
import com.tiangong.order.remote.SupplyOrderFinanceHandleRemote;
import com.tiangong.order.remote.request.CancelOrderWorkOrderDTO;
import com.tiangong.order.remote.request.CancelSupplyOrderWorkOrderDTO;
import com.tiangong.order.remote.request.ConfirmOrderWorkOrderDTO;
import com.tiangong.order.remote.request.ConfirmSupplyOrderWorkOrderDTO;
import com.tiangong.organization.remote.BankRemote;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.ExcelExportUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class WorkOrderServiceImpl implements WorkOrderService {

    @Autowired
    private FileRemote fileRemote;

    @Autowired
    private WorkOrderMapper workOrderMapper;

    @Autowired
    private WorkOrderAttchMapper workOrderAttchMapper;

    @Autowired
    private AgentStatementPayHandle agentStatementPayHandle;

    @Autowired
    private SupplierStatementPayHandle supplierStatementPayHandle;

    @Autowired
    private OrderFinanceHandleRemote orderFinanceHandleRemote;

    @Autowired
    private SupplyOrderFinanceHandleRemote supplyOrderFinanceHandleRemote;

    @Autowired
    private BankRemote bankRemote;

    @Override
    public PaginationSupportDTO<WorkOrderListResponseDTO> queryWorkOrderList(QueryWorkOrderListDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        //查询应处理完时间,而应处理完时间 = 创建时间+ 1天,则把查询条件的开始日期减少一天即可
        if (StrUtilX.isNotEmpty(request.getStartDate())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(DateUtilX.stringToDate(request.getStartDate()));
            calendar.add(Calendar.DATE, -1);
            request.setStartDate(DateUtilX.dateToString(calendar.getTime(), "yyyy-MM-dd"));
        }
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            request.setAgentName(null);
        }
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            request.setSupplierName(null);
        }
        List<WorkOrderListResponseDTO> list = workOrderMapper.queryWorkOrderList(request);
        PageInfo<WorkOrderListResponseDTO> page = new PageInfo<>(list);

        PaginationSupportDTO<WorkOrderListResponseDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public void exportWorkOrderList(String language,QueryWorkExportRequestDTO request, HttpServletResponse response) {
        //查询应处理完时间,而应处理完时间 = 创建时间+ 1天,则把查询条件的开始日期减少一天即可
        if (StrUtilX.isNotEmpty(request.getStartDate())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(DateUtilX.stringToDate(request.getStartDate()));
            calendar.add(Calendar.DATE, -1);
            request.setStartDate(DateUtilX.dateToString(calendar.getTime(), "yyyy-MM-dd"));
        }
        // 编码存在使用编码查询
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            request.setAgentName(null);
        }
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            request.setSupplierName(null);
        }
        String fileName = "Settlement Report" + DateUtilX.dateToString(new Date(), "yyyy_MM_dd");
        if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.zh_CN.getValue())) {
            fileName = "已结算报表" + DateUtilX.dateToString(new Date(), "yyyy_MM_dd");
        }

        //2.查询数据
        List<WorkOrderExportResponseDTO> workOrderExportResponseDTOS = workOrderMapper.exportWorkOrderList(request);

        //3. 导出数据
        ExcelExportUtil.exportToZip(language,fileName,response, workOrderExportResponseDTOS);

    }

    @Override
    public WorkOrderDTO queryWorkOrderDetail(WorkOrderIdDTO request) {
        WorkOrderPO workOrderPO = workOrderMapper.selectByPrimaryKey(request.getWorkOrderId());
        WorkOrderDTO workOrderDTO = DTOConvert.INSTANCE.workOrderDTOConvert(workOrderPO);
        workOrderDTO.setWorkOrderId(workOrderPO.getId());
        workOrderDTO.setAmt(workOrderPO.getAmount());
        workOrderDTO.setSettledBy(workOrderPO.getUpdatedBy());
        workOrderDTO.setSettledDt(workOrderPO.getUpdatedDt());
        workOrderDTO.setCreatedDt(DateUtilX.dateToString(workOrderPO.getCreatedDt(), DateUtilX.hour_format));

        FileReq fileReq = new FileReq();
        fileReq.setObjId(request.getWorkOrderId().toString());
        fileReq.setTableName("f_work_order");
        Response<List<FileResp>> response = fileRemote.list(fileReq);
        if (response.getModel() != null && response.getModel().size() > 0) {
            List<WorkOrderAttchDTO> photoList = DTOConvert.INSTANCE.workOrderAttchDTOConvert(response.getModel());
            workOrderDTO.setPhotoList(photoList);
        }
        return workOrderDTO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmWorkOrder(ConfirmWorkOrderDTO request) {
        WorkOrderPO workOrderPO = workOrderMapper.selectByPrimaryKey(request.getWorkOrderId());

        //若是为付款，则要转变为负数
        if (!request.getIsCollection()) {
            request.setAmt(BigDecimal.ZERO.subtract(request.getAmt()));
        }

        // 1.保存工单状态
        WorkOrderPO workOrderUpdate = DTOConvert.INSTANCE.workOrderUpdateConvert(request);
        workOrderUpdate.setId(request.getWorkOrderId());
        workOrderUpdate.setAmount(request.getAmt());
        workOrderUpdate.setWorkOrderStatus(WorkOrderStatusEnum.SETTLED.key);
        workOrderUpdate.setUpdatedDt(new Date());
        workOrderUpdate.setUpdatedBy(request.getOperator());
        workOrderMapper.updateByPrimaryKeySelective(workOrderUpdate);

        // 2.保存工单附件
        WorkOrderAttchPO workOrderAttchDelete = new WorkOrderAttchPO();
        workOrderAttchDelete.setWorkOrderId(request.getWorkOrderId());
        workOrderAttchMapper.delete(workOrderAttchDelete);
        // 3.保存工单附件
        if (request.getPhotoList() != null && request.getPhotoList().size() > 0) {
            for (WorkOrderAttchDTO photo : request.getPhotoList()){
                FileReq fileReq = new FileReq();
                fileReq.setFileId(photo.getFileId());
                fileReq.setObjId(workOrderUpdate.getId().toString());
                fileReq.setTableName("f_work_order");
                fileRemote.update(fileReq);
            }
        }

        // 4.回调订单或账单更新状态
        if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.ORDER.key)) {
            ConfirmOrderWorkOrderDTO confirmOrderWorkOrderDTO = new ConfirmOrderWorkOrderDTO();
            confirmOrderWorkOrderDTO.setOrderCode(workOrderPO.getBusinessCode());
            confirmOrderWorkOrderDTO.setNotifyAmt(workOrderPO.getAmount());
            confirmOrderWorkOrderDTO.setConfirmAmt(request.getAmt());
            confirmOrderWorkOrderDTO.setOperator(request.getOperator());
            Response<Object> objectResponse = orderFinanceHandleRemote.confirmOrderWorkOrder(confirmOrderWorkOrderDTO);
            if(objectResponse.isError()){
                throw new RuntimeException("系统异常");
            }
        } else if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLYORDER.key)) {
            ConfirmSupplyOrderWorkOrderDTO confirmSupplyOrderWorkOrderDTO = new ConfirmSupplyOrderWorkOrderDTO();
            confirmSupplyOrderWorkOrderDTO.setSupplyOrderCode(workOrderPO.getBusinessCode());
            confirmSupplyOrderWorkOrderDTO.setNotifyAmt(BigDecimal.ZERO.subtract(workOrderPO.getAmount()));
            confirmSupplyOrderWorkOrderDTO.setConfirmAmt(BigDecimal.ZERO.subtract(request.getAmt()));
            confirmSupplyOrderWorkOrderDTO.setOperator(request.getOperator());
            Response<Object> objectResponse = supplyOrderFinanceHandleRemote.confirmSupplyOrderWorkOrder(confirmSupplyOrderWorkOrderDTO);
            if(objectResponse.isError()){
                throw new RuntimeException("系统异常");
            }
        } else if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.AGENTSTATEMENT.key)) {
            ConfirmStatementWorkOrderDTO confirmStatementWorkOrderDTO = new ConfirmStatementWorkOrderDTO();
            confirmStatementWorkOrderDTO.setStatementCode(workOrderPO.getBusinessCode());
            confirmStatementWorkOrderDTO.setNotifyAmt(workOrderPO.getAmount());
            confirmStatementWorkOrderDTO.setConfirmAmt(request.getAmt());
            confirmStatementWorkOrderDTO.setOperator(request.getOperator());
            agentStatementPayHandle.confirmStatementWorkOrder(confirmStatementWorkOrderDTO);
        } else if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLIERSTATEMENT.key) ||
                Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLIER_REWARD_STATEMENT.key) ||
                Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLIER_REBATE_STATEMENT.key)) {
            ConfirmStatementWorkOrderDTO confirmStatementWorkOrderDTO = new ConfirmStatementWorkOrderDTO();
            confirmStatementWorkOrderDTO.setStatementCode(workOrderPO.getBusinessCode());
            confirmStatementWorkOrderDTO.setNotifyAmt(BigDecimal.ZERO.subtract(workOrderPO.getAmount()));
            confirmStatementWorkOrderDTO.setConfirmAmt(BigDecimal.ZERO.subtract(request.getAmt()));
            if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLIER_REWARD_STATEMENT.key)){
                confirmStatementWorkOrderDTO.setNotifyAmt(workOrderPO.getAmount());
                confirmStatementWorkOrderDTO.setConfirmAmt(request.getAmt());
            }
            confirmStatementWorkOrderDTO.setOperator(request.getOperator());
            supplierStatementPayHandle.confirmStatementWorkOrder(confirmStatementWorkOrderDTO);
        }
        // 记录流水
        if (request.getReceiverId() != null && request.getIsCollection()){
            logBankBalanceChange(request, request.getReceiverId(), 1);
        } else if (request.getPayerId() != null && !request.getIsCollection()) {
            logBankBalanceChange(request, request.getPayerId(), 2);
        }
    }

    /**
     * 记录银行流水
     * @param bankId 银行ID
     * @param type 1-收 2-付
     */
    private void logBankBalanceChange(ConfirmWorkOrderDTO request, Integer bankId, int type) {
        BankBalanceChangeReq req = new BankBalanceChangeReq();
        req.setChangeType(3);
        req.setChangeContent("线下转账");
        req.setCreatedBy(request.getOperator());
        if (request.getPaymentType() == 1){
            req.setChangeType(2);
            req.setChangeContent("在线转账");
        }
        req.setChangeAmt(request.getAmt());
        req.setOrgCode(CompanyDTO.COMPANY_CODE);
        req.setChangeDate(request.getChangeTime());
        req.setBankId(bankId);
        req.setBankLogType(type);
        bankRemote.bankBalanceChange(req);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteWorkOrder(WorkOrderIdDTO request) {
        WorkOrderPO workOrderPO = workOrderMapper.selectByPrimaryKey(request.getWorkOrderId());

        // 1.保存工单状态
        WorkOrderPO workOrderUpdate = new WorkOrderPO();
        workOrderUpdate.setId(request.getWorkOrderId());
        workOrderUpdate.setWorkOrderStatus(WorkOrderStatusEnum.CANCELED.key);
        workOrderUpdate.setUpdatedBy(request.getOperator());
        workOrderUpdate.setUpdatedDt(new Date());
        workOrderMapper.updateByPrimaryKeySelective(workOrderUpdate);

        // 2.回调订单或账单更新状态
        if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.ORDER.key)) {
            CancelOrderWorkOrderDTO cancelOrderWorkOrderDTO = new CancelOrderWorkOrderDTO();
            cancelOrderWorkOrderDTO.setOrderCode(workOrderPO.getBusinessCode());
            cancelOrderWorkOrderDTO.setNotifyAmt(workOrderPO.getAmount());
            cancelOrderWorkOrderDTO.setOperator(request.getOperator());
            orderFinanceHandleRemote.cancelOrderWorkOrder(cancelOrderWorkOrderDTO);
        } else if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLYORDER.key)) {
            CancelSupplyOrderWorkOrderDTO cancelSupplyOrderWorkOrderDTO = new CancelSupplyOrderWorkOrderDTO();
            cancelSupplyOrderWorkOrderDTO.setSupplyOrderCode(workOrderPO.getBusinessCode());
            cancelSupplyOrderWorkOrderDTO.setNotifyAmt(BigDecimal.ZERO.subtract(workOrderPO.getAmount()));
            cancelSupplyOrderWorkOrderDTO.setOperator(request.getOperator());
            supplyOrderFinanceHandleRemote.cancelSupplyOrderWorkOrder(cancelSupplyOrderWorkOrderDTO);
        } else if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.AGENTSTATEMENT.key)) {
            CancelStatementWorkOrderDTO cancelStatementWorkOrderDTO = new CancelStatementWorkOrderDTO();
            cancelStatementWorkOrderDTO.setStatementCode(workOrderPO.getBusinessCode());
            cancelStatementWorkOrderDTO.setNotifyAmt(workOrderPO.getAmount());
            cancelStatementWorkOrderDTO.setOperator(request.getOperator());
            agentStatementPayHandle.cancelStatementWorkOrder(cancelStatementWorkOrderDTO);
        } else if (Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLIERSTATEMENT.key) ||
                Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLIER_REWARD_STATEMENT.key) ||
                Objects.equals(workOrderPO.getBusinessType(), BusinessTypeEnum.SUPPLIER_REBATE_STATEMENT.key)) {
            CancelStatementWorkOrderDTO cancelStatementWorkOrderDTO = new CancelStatementWorkOrderDTO();
            cancelStatementWorkOrderDTO.setStatementCode(workOrderPO.getBusinessCode());
            cancelStatementWorkOrderDTO.setNotifyAmt(BigDecimal.ZERO.subtract(workOrderPO.getAmount()));
            cancelStatementWorkOrderDTO.setOperator(request.getOperator());
            supplierStatementPayHandle.cancelStatementWorkOrder(cancelStatementWorkOrderDTO);
        }
    }

    @Override
    public void editWorkOrderDetail(WorkOrderDTO request) {
        WorkOrderPO workOrderPO = new WorkOrderPO();
        workOrderPO.setId(request.getWorkOrderId());
        workOrderPO.setRemark(request.getRemark());
        workOrderPO.setUpdatedBy(request.getUpdatedBy());
        workOrderPO.setUpdatedDt(new Date());
        workOrderMapper.updateByPrimaryKeySelective(workOrderPO);
    }
}
