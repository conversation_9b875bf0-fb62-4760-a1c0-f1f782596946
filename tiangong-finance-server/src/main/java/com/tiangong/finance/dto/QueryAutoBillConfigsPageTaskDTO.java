package com.tiangong.finance.dto;

import lombok.Data;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Date 2025/6/7 13:49
 * @Description:
 */

@Data
public class QueryAutoBillConfigsPageTaskDTO {

    private Long id;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店当地时区 (例如: UTC+7)
     */
    private String hotelLocalTimezone;

    /**
     * 天宫付款方式 0-酒店给我方发送付款链接 1-银行转账 2-WorldFirst账户收款
     */
    private Integer paymentMethod;

    /**
     * 出账规则 0-按订单 1-按日 2-按周 3-按半月 4-按月
     */
    private Integer billingRule;

    /**
     * 出账日期类型 0-入住日前 1-入住日当天 2-离店日当天 3-离店日后
     */
    private Integer billingDateType;

    /**
     * 出账日期天数
     */
    private Integer billingDateDays;

    /**
     * 出账时间窗口: 酒店当地开始时间 (HH:mm:ss)
     */
    private LocalTime hotelLocalStartTime;

    /**
     * 出账时间窗口: 酒店当地结束时间 (HH:mm:ss)
     */
    private LocalTime hotelLocalEndTime;

    /**
     * 出账时间窗口: 系统开始时间 (HH:mm:ss)
     */
    private LocalTime systemStartTime;

    /**
     * 出账时间窗口: 系统结束时间 (HH:mm:ss)
     */
    private LocalTime systemEndTime;

}
