package com.tiangong.finance.financing.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.util.Date;

/**
 * 融资信息推送任务表
 */
@Data
public class FinancingPushTaskReq extends BaseRequest {
    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 编码（订单编码/账单编码）
     */
    private String code;

    /**
     * 数据类型（1离店订单 2金额变动订单 3账单）
     */
    private Integer dataType;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 入住日期
     */
    private Date checkInDate;

    /**
     * 离店日期
     */
    private Date checkOutDate;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 晚数
     */
    private Integer nightQty;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 融资方类型id
     */
    private Long financingPartyTypeId;

    /**
     * 融资方名称
     */
    private String financingPartyName;

    /**
     * 推送状态(0待推送 1推送中 2已推送 3推送失败)
     */
    private Integer pushStatus;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;
}
