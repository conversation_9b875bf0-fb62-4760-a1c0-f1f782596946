package com.tiangong.finance.financing.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.financing.domain.req.FinancingConfigReq;
import com.tiangong.finance.financing.domain.resp.FinancingConfigResp;
import com.tiangong.finance.financing.domain.resp.FinancingPartyTypeResp;
import com.tiangong.finance.financing.service.FinancingConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 融资配置
 */
@RestController
@RequestMapping("/finance/financingConfig")
public class FinancingConfigServer extends BaseController {

    @Autowired
    private FinancingConfigService financingConfigService;

    /**
     * 新增融资配置
     */
    @PostMapping("/financingConfigAdd")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> hotelLabelConfigAdd(@RequestBody FinancingConfigReq req) {
        req.setCreatedBy(super.getUserName());
        return financingConfigService.financingConfigAdd(req);
    }

    /**
     * 编辑融资配置
     */
    @PostMapping("/financingConfigEdit")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> financingConfigEdit(@RequestBody FinancingConfigReq req) {
        req.setUpdatedBy(super.getUserName());
        return financingConfigService.financingConfigEdit(req);
    }

    /**
     * 查询融资配置列表（分页）
     */
    @PostMapping("/queryFinancingConfigPage")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<FinancingConfigResp>> queryFinancingConfigPage(@RequestBody FinancingConfigReq req) {
        return Response.success(financingConfigService.queryFinancingConfigPage(req));
    }

    /**
     * 查询融资方类型列表
     */
    @PostMapping("/queryFinancingPartyTypeList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<List<FinancingPartyTypeResp>> queryFinancingPartyTypeList(@RequestBody FinancingConfigReq req) {
        return Response.success(financingConfigService.queryFinancingPartyTypeList(req));
    }
}
