package com.tiangong.finance.financing.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 融资方类型表
 */
@Data
@TableName("f_financing_party_type")
public class FinancingPartyTypeEntity {
    /**
     * 主键id
     */
    @TableId(value = "financing_party_type_id", type = IdType.AUTO)
    private Long financingPartyTypeId;

    /**
     * 融资方名称
     */
    private String financingPartyName;

    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
