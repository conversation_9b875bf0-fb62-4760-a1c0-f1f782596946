package com.tiangong.finance.financing.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.FinancingPartyEnum;
import com.tiangong.finance.enums.FinancingPushTypeEnum;
import com.tiangong.finance.financing.domain.FinancingConfigEntity;
import com.tiangong.finance.financing.domain.FinancingPartyTypeEntity;
import com.tiangong.finance.financing.domain.req.FinancingConfigReq;
import com.tiangong.finance.financing.domain.resp.FinancingConfigResp;
import com.tiangong.finance.financing.domain.resp.FinancingPartyConfigExtendResp;
import com.tiangong.finance.financing.domain.resp.FinancingPartyTypeResp;
import com.tiangong.finance.financing.domain.resp.ZHILVConfig;
import com.tiangong.finance.financing.mapper.FinancingConfigMapper;
import com.tiangong.finance.financing.mapper.FinancingPartyTypeMapper;
import com.tiangong.finance.financing.service.FinancingConfigService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FinancingConfigServiceImpl extends ServiceImpl<FinancingConfigMapper, FinancingConfigEntity> implements FinancingConfigService {

    @Autowired
    private FinancingConfigMapper financingConfigMapper;

    @Autowired
    private FinancingPartyTypeMapper financingPartyTypeMapper;

    @Override
    public Response<Object> financingConfigAdd(FinancingConfigReq req) {
        // 校验参数
        checkFinancingConfigAddParam(req);

        // 校验供应商是否存在
        Map<String, String> checkMap = checkSupply(req.getExcludeSupplyCode());
        if (checkMap != null) {
            return Response.success(checkMap);
        }

        // 配置id
        Long financingConfigId = null;

        // 查询客户配置是否存在
        if (FinancingPushTypeEnum.CUSTOMER.getCode().equals(req.getPushType())) {
            FinancingConfigEntity entity = financingConfigMapper.selectOne(new LambdaQueryWrapper<FinancingConfigEntity>()
                    .eq(FinancingConfigEntity::getFinancingPartyTypeId, req.getFinancingPartyTypeId())
                    .eq(FinancingConfigEntity::getAgentCode, req.getAgentCode()));
            if (entity != null) {
                if (entity.getDeleted() == 0) {
                    throw new SysException(ErrorCodeEnum.THE_FINANCING_ALLOCATION_ALREADY_EXISTS);
                } else {
                    financingConfigId = entity.getFinancingConfigId();
                }
            }
        }
        // 查询供应商配置是否存在
        if (FinancingPushTypeEnum.SUPPLIER.getCode().equals(req.getPushType())) {
            FinancingConfigEntity entity = financingConfigMapper.selectOne(new LambdaQueryWrapper<FinancingConfigEntity>()
                    .eq(FinancingConfigEntity::getFinancingPartyTypeId, req.getFinancingPartyTypeId())
                    .eq(FinancingConfigEntity::getSupplyCode, req.getSupplyCode()));
            if (entity != null) {
                if (entity.getDeleted() == 0) {
                    throw new SysException(ErrorCodeEnum.THE_FINANCING_ALLOCATION_ALREADY_EXISTS);
                } else {
                    financingConfigId = entity.getFinancingConfigId();
                }
            }
        }

        // 设置对象
        FinancingConfigEntity entity = new FinancingConfigEntity();
        entity.setFinancingPartyTypeId(req.getFinancingPartyTypeId());
        entity.setPushType(req.getPushType());
        entity.setAgentCode(req.getAgentCode());
        entity.setExcludeSupplyCode(req.getExcludeSupplyCode());
        entity.setSupplyCode(req.getSupplyCode());
        entity.setChannelCode(req.getChannelCode());
        entity.setCompanyGroupType(req.getCompanyGroupType());
        entity.setCompanyName(req.getCompanyName());
        entity.setUnifyCode(req.getUnifyCode());
        entity.setOrderSourceCode(req.getOrderSourceCode());
        entity.setCreatedBy(req.getCreatedBy());
        entity.setCreatedDt(DateUtilX.getCurrentDate());

        // 判断是否更新
        if (financingConfigId == null) {
            // 保存数据
            this.save(entity);
        } else {
            // 修改为有效
            entity.setFinancingConfigId(financingConfigId);
            entity.setIsActive(1);
            entity.setDeleted(0);
            // 更新数据
            this.updateById(entity);
        }
        return Response.success();
    }

    /**
     * 校验新增融资配置参数
     */
    private void checkFinancingConfigAddParam(FinancingConfigReq req) {
        if (req.getFinancingPartyTypeId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_FINANCINGPARTY);
        }
        if (req.getPushType() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_PUSHTYPE);
        } else {
            // 公共校验
            checkCommon(req);
        }
        if (StrUtilX.isEmpty(req.getChannelCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHANNELCODE);
        }
        if (StrUtilX.isEmpty(req.getCompanyGroupType())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_COMPANYGROUPTYPE);
        }
        if (StrUtilX.isEmpty(req.getCompanyName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_COMPANYNAME);
        }
        if (StrUtilX.isEmpty(req.getUnifyCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_UNIFYCODE);
        }
        if (StrUtilX.isEmpty(req.getOrderSourceCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERSOURCECODE);
        }
    }

    /**
     * 公共校验
     */
    private void checkCommon(FinancingConfigReq req) {
        if (FinancingPushTypeEnum.CUSTOMER.getCode().equals(req.getPushType())) {
            if (StrUtilX.isEmpty(req.getAgentCode())) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
            }
        } else if (FinancingPushTypeEnum.SUPPLIER.getCode().equals(req.getPushType())) {
            if (StrUtilX.isEmpty(req.getSupplyCode())) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
            }
        } else {
            throw new SysException(ErrorCodeEnum.PUSH_TYPE_NOT_EXIST);
        }
    }

    /**
     * 校验供应商是否存在
     */
    private Map<String, String> checkSupply(String excludeSupplyCode) {
        // 校验供应商是否存在
        if (StrUtilX.isNotEmpty(excludeSupplyCode)) {
            List<String> supplyCodes = StrUtilX.stringToList(excludeSupplyCode, ",");
            Set<Object> supplyCodeList = RedisTemplateX.hKeys(RedisKey.SUPPLY_ACCOUNT_CONFIG);
            if (CollUtilX.isNotEmpty(supplyCodes)) {
                Set<String> codes = new HashSet<>();
                for (String supplyCode : supplyCodes) {
                    if (!supplyCodeList.contains(supplyCode)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("dataType", "2");
                        map.put("code", supplyCode);
                        return map;
                    }
                    if (codes.contains(supplyCode)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("dataType", "1");
                        map.put("code", supplyCode);
                        return map;
                    }
                    codes.add(supplyCode);
                }
            }
        }
        return null;
    }

    @Override
    public Response<Object> financingConfigEdit(FinancingConfigReq req) {
        // 校验参数
        checkFinancingConfigEditParam(req);

        // 校验供应商是否存在
        Map<String, String> checkMap = checkSupply(req.getExcludeSupplyCode());
        if (checkMap != null) {
            return Response.success(checkMap);
        }

        // 设置对象
        FinancingConfigEntity entity = new FinancingConfigEntity();
        entity.setFinancingConfigId(req.getFinancingConfigId());
        entity.setFinancingPartyTypeId(req.getFinancingPartyTypeId());
        entity.setPushType(req.getPushType());
        entity.setExcludeSupplyCode(req.getExcludeSupplyCode());
        entity.setChannelCode(req.getChannelCode());
        entity.setCompanyGroupType(req.getCompanyGroupType());
        entity.setCompanyName(req.getCompanyName());
        entity.setUnifyCode(req.getUnifyCode());
        entity.setOrderSourceCode(req.getOrderSourceCode());
        entity.setDeleted(req.getDeleted());
        entity.setIsActive(req.getIsActive());
        entity.setUpdatedBy(req.getUpdatedBy());
        entity.setUpdatedDt(DateUtilX.getCurrentDate());

        // 更新数据
        this.updateById(entity);
        return Response.success();
    }

    /**
     * 校验编辑融资配置参数
     */
    private void checkFinancingConfigEditParam(FinancingConfigReq req) {
        if (req.getFinancingConfigId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        if (req.getPushType() != null) {
            // 公共校验
            checkCommon(req);
        }
    }

    @Override
    public PaginationSupportDTO<FinancingConfigResp> queryFinancingConfigPage(FinancingConfigReq req) {
        IPage<FinancingConfigResp> ipage = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<FinancingConfigResp> iPage = financingConfigMapper.selectFinancingConfigPage(ipage, req);

        PaginationSupportDTO<FinancingConfigResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(iPage.getRecords());
        paginationSupportDTO.setCurrentPage((int) iPage.getCurrent());
        paginationSupportDTO.setPageSize((int) iPage.getSize());
        paginationSupportDTO.setTotalCount(iPage.getTotal());
        paginationSupportDTO.setTotalPage((int) iPage.getPages());
        return paginationSupportDTO;
    }

    @Override
    public List<FinancingConfigResp> queryFinancingConfigList(FinancingConfigReq req) {
        List<FinancingConfigResp> configList = financingConfigMapper.selectFinancingConfigList(req);
        // 设置扩展配置
        if (CollUtilX.isNotEmpty(configList)) {
            for (FinancingConfigResp resp : configList) {
                if (FinancingPartyEnum.ZHI_LV.getValue().equals(resp.getFinancingPartyName())) {
                    if (CollUtilX.isNotEmpty(resp.getConfigExtendList())) {
                        // 转换知旅配置扩展
                        convertZHILVConfigExtend(resp, resp.getConfigExtendList());
                    }
                }
            }
        }
        return configList;
    }

    @Override
    public List<FinancingPartyTypeResp> queryFinancingPartyTypeList(FinancingConfigReq req) {
        LambdaQueryWrapper<FinancingPartyTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtilX.isNotEmpty(req.getFinancingPartyName())) {
            queryWrapper.like(FinancingPartyTypeEntity::getFinancingPartyName, req.getFinancingPartyName());
        }
        List<FinancingPartyTypeEntity> entityList = financingPartyTypeMapper.selectList(queryWrapper);
        if (CollUtilX.isNotEmpty(entityList)) {
            return entityList.stream().map(item -> {
                FinancingPartyTypeResp resp = new FinancingPartyTypeResp();
                resp.setFinancingPartyTypeId(item.getFinancingPartyTypeId());
                resp.setFinancingPartyName(item.getFinancingPartyName());
                return resp;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<String> queryFinancingConfigIds() {
        return financingConfigMapper.selectFinancingConfigIds();
    }

    /**
     * 转换知旅配置扩展
     */
    private void convertZHILVConfigExtend(FinancingConfigResp resp, List<FinancingPartyConfigExtendResp> configExtendList) {
        Map<String, String> map = new HashMap<>();
        for (FinancingPartyConfigExtendResp configExtendResp : configExtendList) {
            map.put(configExtendResp.getFieldName(), configExtendResp.getFieldValue());
        }
        ZHILVConfig zhilvConfig = JSON.parseObject(JSONUtil.toJsonStr(map), new TypeReference<ZHILVConfig>() {
        });
        resp.setZhilvConfig(zhilvConfig);
    }
}