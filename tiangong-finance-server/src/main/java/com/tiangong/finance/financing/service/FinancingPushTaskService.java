package com.tiangong.finance.financing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.financing.domain.FinancingPushTaskEntity;
import com.tiangong.finance.financing.domain.req.FinancingPushDataReq;
import com.tiangong.finance.financing.domain.req.FinancingPushTaskReq;
import com.tiangong.finance.financing.domain.req.PushTaskReq;
import com.tiangong.finance.financing.domain.resp.FinancingAmountResp;
import com.tiangong.finance.financing.domain.resp.FinancingPushTaskResp;

import java.util.Map;

/**
 * 融资信息推送任务
 */
public interface FinancingPushTaskService extends IService<FinancingPushTaskEntity> {

    /**
     * 查询融资信息推送任务列表（分页）
     */
    PaginationSupportDTO<FinancingPushTaskResp> financingPushTaskPage(FinancingPushTaskReq req);

    /**
     * 同步融资离店订单到任务表
     */
    Map<Long, FinancingPushDataReq> syncFinancingCheckOutOrderToTable(String param, String id);

    /**
     * 推送融资离店订单任务
     */
    void pushFinancingCheckOutOrderTask(String param, String id);

    /**
     * 同步融资金额变动到任务表
     */
    Map<Long, FinancingPushDataReq> syncFinancingAmtChangeOrderToTable(String param, String id);

    /**
     * 推送融资金额变动订单任务
     */
    void pushFinancingAmtChangeOrderTask(String agentCode, String id);

    /**
     * 同步融资账单到任务表
     */
    Map<Long, FinancingPushDataReq> syncFinancingStatementToTable(Map<String, String> paramMap);

    /**
     * 推送融资账单任务
     */
    void pushFinancingStatementTask(Map<String, String> paramMap);

    /**
     * 推送融资信息
     */
    Map<String, String> pushFinancingInfo(PushTaskReq req);

    /**
     * 查询融资金额信息
     */
    FinancingAmountResp queryFinancingAmountInfo(FinancingPushTaskReq req);

    /**
     * 批量修改融资推送状态
     */
    Response<Object> batchUpdateFinancingPushStatus(PushTaskReq req);

    /**
     * 初始化融资配置id到缓存任务
     */
    void initFinancingConfigIdToRedisTask(String param);
}