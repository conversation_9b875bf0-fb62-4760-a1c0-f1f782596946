package com.tiangong.finance.financing.server;

import cn.hutool.json.JSONUtil;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.financing.domain.req.FinancingPushTaskReq;
import com.tiangong.finance.financing.domain.req.PushTaskReq;
import com.tiangong.finance.financing.domain.resp.FinancingAmountResp;
import com.tiangong.finance.financing.domain.resp.FinancingPushTaskResp;
import com.tiangong.finance.financing.service.FinancingPushTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 融资信息推送任务
 */
@Slf4j
@RestController
@RequestMapping("/finance/financingPushTask")
public class FinancingPushTaskServer extends BaseController {

    @Autowired
    private FinancingPushTaskService financingPushTaskService;

    /**
     * 查询融资信息推送任务列表（分页）
     */
    @PostMapping("/financingPushTaskPage")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<FinancingPushTaskResp>> financingPushTaskPage(@RequestBody FinancingPushTaskReq req) {
        return Response.success(financingPushTaskService.financingPushTaskPage(req));
    }

    /**
     * 查询融资金额信息
     */
    @PostMapping("/queryFinancingAmountInfo")
    @PreAuthorize("@syyo.check('finance')")
    public Response<FinancingAmountResp> queryFinancingAmountInfo(@RequestBody FinancingPushTaskReq req) {
        return Response.success(financingPushTaskService.queryFinancingAmountInfo(req));
    }

    /**
     * 推送融资信息
     */
    @PostMapping("/pushFinancingInfo")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> pushFinancingInfo(@RequestBody PushTaskReq req) {
        req.setUpdatedBy(getUserName());
        return Response.success(financingPushTaskService.pushFinancingInfo(req));
    }

    /**
     * 推送融资金额变动订单任务
     */
    @PostMapping("/pushFinancingAmtChangeOrderTask")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> pushFinancingAmtChangeOrderTask(@RequestBody Map<String, String> paramMap) {
        try {
            financingPushTaskService.pushFinancingAmtChangeOrderTask(paramMap.get("agentCode"), null);
        } catch (Exception e) {
            log.error("执行推送融资金额变动订单任务异常", e);
        }
        return Response.success();
    }

    /**
     * 推送融资离店订单任务
     */
    @PostMapping("/pushFinancingCheckOutOrderTask")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> pushFinancingCheckOutOrderTask(@RequestBody Map<String, String> paramMap) {
        try {
            financingPushTaskService.pushFinancingCheckOutOrderTask(JSONUtil.toJsonStr(paramMap), null);
        } catch (Exception e) {
            log.error("执行推送融资离店订单任务异常", e);
        }
        return Response.success();
    }

    /**
     * 推送融资账单任务
     */
    @PostMapping("/pushFinancingStatementTask")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> pushFinancingStatementTask(@RequestBody Map<String, String> paramMap) {
        try {
            financingPushTaskService.pushFinancingStatementTask(paramMap);
        } catch (Exception e) {
            log.error("执行推送融资账单任务异常", e);
        }
        return Response.success();
    }

    /**
     * 批量修改融资推送状态
     */
    @PostMapping("/batchUpdateFinancingPushStatus")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> batchUpdateFinancingPushStatus(@RequestBody PushTaskReq req) {
        return financingPushTaskService.batchUpdateFinancingPushStatus(req);
    }
}
