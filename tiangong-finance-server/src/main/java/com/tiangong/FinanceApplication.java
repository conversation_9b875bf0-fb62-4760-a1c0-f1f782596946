package com.tiangong;

import cn.hutool.core.thread.ThreadUtil;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import java.util.concurrent.TimeUnit;

/**
 * @Auther: Owen
 * @Date: 2019/4/23 19:09
 * @Description:
 */
@Slf4j
@SpringBootApplication
@EnableEncryptableProperties
@EnableFeignClients
@EnableDiscoveryClient
@MapperScan(basePackages = {"com.tiangong.*.mapper", "com.tiangong.*.*.mapper", "com.tiangong.*.*.*.mapper"})
@EntityScan(basePackages = {"com.tiangong.*.domain", "com.tiangong.*.*.domain"})
public class FinanceApplication {

    public static void main(String[] args) {
        SpringApplication.run(FinanceApplication.class, args);

        ThreadUtil.execute(() -> {

            ThreadUtil.sleep(1, TimeUnit.SECONDS); // 延迟 1 秒，保证输出到结尾
            log.info("\n----------------------------------------------------------\n\t" +
                    "项目启动成功！ \n\t" +
                    "Spring Boot Version: {}\n" +
                    ".__   __.   ______      .______    __    __    _______\n" +
                    "|  \\ |  |  /  __  \\     |   _  \\  |  |  |  |  /  _____|\n" +
                    "|   \\|  | |  |  |  |    |  |_)  | |  |  |  | |  |  __\n" +
                    "|  . `  | |  |  |  |    |   _  <  |  |  |  | |  | |_ |\n" +
                    "|  |\\   | |  `--'  |    |  |_)  | |  `--'  | |  |__| |\n" +
                    "|__| \\__|  \\______/     |______/   \\______/   \\______|\n" +
                    "\n" +
                    "███╗   ██╗ ██████╗     ██████╗ ██╗   ██╗ ██████╗\n" +
                    "████╗  ██║██╔═══██╗    ██╔══██╗██║   ██║██╔════╝\n" +
                    "██╔██╗ ██║██║   ██║    ██████╔╝██║   ██║██║  ███╗\n" +
                    "██║╚██╗██║██║   ██║    ██╔══██╗██║   ██║██║   ██║\n" +
                    "██║ ╚████║╚██████╔╝    ██████╔╝╚██████╔╝╚██████╔╝\n" +
                    "╚═╝  ╚═══╝ ╚═════╝     ╚═════╝  ╚═════╝  ╚═════╝ \n" +
                    "----------------------------------------------------------", SpringBootVersion.getVersion());
        });
    }
}
