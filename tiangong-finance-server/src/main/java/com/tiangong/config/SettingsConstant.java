package com.tiangong.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * @Description: 系统配置类
 * <p>
 * 全局参数配置
 * @RefreshScope 实时更新nacos配置
 */
@Data
@Configuration
@RefreshScope
public class SettingsConstant {

    @Value("${rate.url}")
    private String rateUrl;

    /**
     * eps验收汇率
     */
    @Value("${rate.url2}")
    private String rateUrl2;

    /**
     * 阿里云汇率地址
     */
    @Value("${rate.aliyunRateBaseUrl:https://jisuhuilv.market.alicloudapi.com}")
    private String aliyunRateBaseUrl;

    /**
     * 阿里云汇率接口AppCode
     */
    @Value("${rate.aliyunRateAppCode:f8f2d085842d4798b413c83e245aa2b4}")
    private String aliyunRateAppCode;

    /**
     * 是否强制更新汇率
     */
    @Value("${rate.isUpdateStatue}")
    private int isUpdateStatue;

    /**
     * 结算成本百分比
     */
    @Value("${protocolOrderSettlementCost.percent}")
    private BigDecimal protocolOrderSettlementCostPercent;

    /**
     * 融资订单每次批量数量
     */
    @Value("${financingOrder.batchSize}")
    private Integer financingOrderBatchSize;

    @Value("${timezone:UTC+8}")
    private String timezone;
}
