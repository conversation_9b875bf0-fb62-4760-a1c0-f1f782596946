# 本地启动环境
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 123 # 虚拟机的地址
        accessKeyL: 123
        secretKey: 123
        namespace: jiali_tiangong # 本地开发环境
        enabled: true
        group: DEFAULT_GROUP #默认 可以不用写
        register-enabled: true #是否把自己注册到注册中心的地址
      config:
        server-addr: 123 # 虚拟机的地址
        accessKeyL: 123
        secretKey: 123
        namespace: jiali_tiangong # 本地开发环境
        enabled: true #是否使用nacos的配置中心配置覆盖本地配置
        #context-path: /nacos
        file-extension: yml  #该配置的缺省值为properties，即默认是读取properties格式的配置文件
        extension-configs:
          - data-id: tiangong-db.yml
            group: DEFAULT_GROUP #默认 可以不用写
            refresh: true #默认不刷新
          - data-id: tiangong-redis.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: tiangong-core.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: tiangong-xxl-job.yml
            group: DEFAULT_GROUP
            refresh: true