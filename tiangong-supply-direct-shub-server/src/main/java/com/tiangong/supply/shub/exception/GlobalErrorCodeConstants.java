package com.tiangong.supply.shub.exception;

/**
 * 全局错误码枚举
 * 0-999 系统异常编码保留
 *
 *
 */
public interface GlobalErrorCodeConstants {

    ErrorCode SUCCESS = new ErrorCode("20000", "成功");
    ErrorCode RESULT_NULL = new ErrorCode("20001", "返回结果为空");
    ErrorCode DISPOSE_ORDER_START_ERROR = new ErrorCode("20200", "处理订单状态失败");
    ErrorCode ERROR = new ErrorCode("99999", "失败");
    ErrorCode UNKNOWN_ERROR = new ErrorCode("11111", "未知异常");
    ErrorCode SYSTEM_ERROR = new ErrorCode("50000", "系统异常");
    ErrorCode PARAM_NULL_ERROR = new ErrorCode("90000", "参数不能为空:");
}
