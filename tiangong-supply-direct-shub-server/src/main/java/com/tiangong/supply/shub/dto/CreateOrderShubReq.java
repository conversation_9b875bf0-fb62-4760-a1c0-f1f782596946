package com.tiangong.supply.shub.dto;

import com.tiangong.supply.direct.remote.request.RoomGuestNumber;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 创建订单请求参数
 * @author: xinpeng
 * @create: 2023-10-17 17:21
 */
@Data
public class CreateOrderShubReq extends ShubBaseDto {

    /**
     * 酒店id
     */
    private String hotelId;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 价格计划id
     */
    private String supplyRateId;

    /**
     * 币种
     */
    private Integer baseCurrency;

    /**
     * 到店另付币种
     */
    private Integer payInStoreCurrency;

    /**
     * 到店另付费用
     */
    private BigDecimal payInStorePrice;

    /**
     * 预订间数
     */
    private Integer roomNum;

    /**
     * 入住日期
     */
    private String checkInDate;

    /**
     * 离店日期
     */
    private String checkOutDate;

    /**
     * 订单总金额 每日售价之和乘以预订间数
     */
    private BigDecimal totalAmount;

    /**
     * 支付类型
     */
    private Integer payMethod;

    /**
     * 商户订单号
     */
    private String merchantOrderCode;

    /**
     * 订单联系人
     */
    private String linkMan;

    /**
     * 联系人电话
     */
    private String linkPhone;

    /**
     * 电话区号
     */
    private String linkCountryCode;

    /**
     * 联系人邮箱
     */
    private String linkEmail;

    /**
     * 到达时间
     */
    private String arriveTime;

    /**
     * 最晚到店时间
     */
    private String latestArriveTime;

    /**
     * 供应商编码
     */
    private String supplyCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 床型详细信息
     */
    private BedTypesDetails bedTypeDetails;

    /**
     * 入住人信息
     */
    private List<Guest> guests;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;

    /**
     * 价格明细
     */
    private List<CreateOrderPriceItem> createOrderDetails;

    /**
     * 供应商类型 1:国内 2:海外
     */
    private String supplyType;

    /**
     * 是否担保标志 ,1担保 0未担保
     */
    private Integer guaranteeFlag;

    /**
     * 是否VIP订单1-VIP订单0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 出行类型，1-因公，2-因私 非必填
     */
    private Integer travelType;

    /**
     * 国籍
     */
    private String nationality;

    /** 客户编码 */
    private String agentCode;
}