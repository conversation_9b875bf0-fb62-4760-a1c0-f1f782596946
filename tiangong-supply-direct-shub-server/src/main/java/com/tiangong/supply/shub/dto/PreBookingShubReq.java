package com.tiangong.supply.shub.dto;

import com.tiangong.supply.direct.remote.request.RoomGuestNumber;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 试预定请求参数
 * @author: xinpeng
 * @create: 2023-10-17 17:20
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PreBookingShubReq extends ShubBaseDto {

    /**
     * 酒店id
     */
    private String hotelId;
    /**
     * 房型id
     */
    private String roomId;
    /**
     * 价格计划id
     */
    private String supplyRateId;

    /**
     * 入住时间
     */
    private String checkInDate;
    /**
     * 离店时间
     */
    private String checkOutDate;
    /**
     * 房间数量
     */
    private Integer roomNum;
    /**
     * 供应商编码
     */
    private String supplyCode;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 国籍
     */
    private String nationality;

}