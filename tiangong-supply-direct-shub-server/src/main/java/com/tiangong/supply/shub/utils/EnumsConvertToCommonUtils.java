package com.tiangong.supply.shub.utils;

import com.tiangong.enums.OrderStatusEnum;
import com.tiangong.supply.shub.enues.ShubOrderStatusEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 供应商 常量数据转成内部常量
 */
@Slf4j
public class EnumsConvertToCommonUtils {

    /**
     * 转换订单状态
     * @param orderStatus 供应商订单状态
     * @return 加力天宫订单状态
     */
    public static Integer convertOrderStatus(Integer orderStatus) {
        if (orderStatus != null) {
            if (ShubOrderStatusEnum.CONFIRM.result == orderStatus){// 确认
                return OrderStatusEnum.CONFIRMED.no;
            }else if (ShubOrderStatusEnum.NO_CONFIRM.result == orderStatus ||
                    ShubOrderStatusEnum.CANCEL.result == orderStatus){// 不确认/已取消
                return OrderStatusEnum.CANCELED.no;
            }else if(ShubOrderStatusEnum.NEED_CONFIRM.result == orderStatus ||
                    ShubOrderStatusEnum.CANCEL_PENDING.result == orderStatus) {// 未确认/取消待审
                return OrderStatusEnum.CONFIRMING.no;
            }
        }
        return null;
    }
}
