package com.tiangong.supply.shub.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * @description: 查询订单请求参数
 * @author: xinpeng
 * @create: 2023-10-17 17:22
 */
@Data
public class QueryOrderShubReq extends ShubBaseDto {

    /**
     * 商户订单号
     */
    private String merchantOrderCode;

    /**
     * 供应商订单号
     */
    private String supplyOrderCode;

    /**
     * 查询缓存状态 默认查询缓存
     * true 当shub订单状态为最终状态时直接返回
     * false 直接查询供应商接口 状态不一致则更新本地
     */
    private Boolean queryCache;

}