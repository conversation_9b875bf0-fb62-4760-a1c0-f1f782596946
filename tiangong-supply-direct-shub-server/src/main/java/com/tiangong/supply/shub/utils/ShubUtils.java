package com.tiangong.supply.shub.utils;

import cn.hutool.core.map.MapUtil;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.supply.direct.remote.request.ChildrenInfo;
import com.tiangong.supply.direct.remote.request.RoomGuestNumber;
import com.tiangong.supply.shub.constant.ShubConstant;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnection;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 工具类
 */
@Slf4j
public class ShubUtils {

    /**
     * 拼接房间信息
     * @param roomGuestNumbers 房间信息
     * @return 拼接字符
     */
    public static String roomAppend(List<RoomGuestNumber> roomGuestNumbers) {
        StringBuilder roomStr = new StringBuilder();
        if (CollUtilX.isNotEmpty(roomGuestNumbers)) {
            for (int i = 0; i < roomGuestNumbers.size(); i++) {
                RoomGuestNumber roomGuestNumber = roomGuestNumbers.get(i);
                if (roomGuestNumber.getRoomIndex() != null) {
                    roomStr.append(roomGuestNumber.getRoomIndex());
                }
                if (roomGuestNumber.getAdultNum() != null) {
                    roomStr.append(roomGuestNumber.getAdultNum());
                }
                if (CollUtilX.isNotEmpty(roomGuestNumber.getChildrenInfos())) {
                    for (int j = 0; j < roomGuestNumber.getChildrenInfos().size(); j++) {
                        ChildrenInfo childrenInfo = roomGuestNumber.getChildrenInfos().get(j);
                        if (childrenInfo.getChildrenAge() != null) {
                            roomStr.append(childrenInfo.getChildrenAge());
                            if (j != roomGuestNumber.getChildrenInfos().size() - 1) {
                                roomStr.append(",");
                            }
                        }
                    }
                }
                if (i != roomGuestNumbers.size() - 1 && roomStr.length() != 0) {
                    roomStr.append(";");
                }
            }
        }
        return roomStr.toString();
    }

    /**
     * 创建产品缓存key
     */
    public static String buildProductCacheKey(String supplyCode,
                                       String hotelId,
                                       String supplyRateId,
                                       String checkInDate,
                                       String checkOutDate,
                                       String roomTypeID,
                                       List<RoomGuestNumber> roomGuestNumbers) {
        String roomStr = roomAppend(roomGuestNumbers);
        return ShubConstant.SHUB_PRODUCT_RESPONSE +
                supplyCode +
                StrUtilX.SPLIT_CODE +
                hotelId +
                StrUtilX.SPLIT_CODE +
                roomTypeID +
                StrUtilX.SPLIT_CODE +
                supplyRateId +
                StrUtilX.SPLIT_CODE +
                (roomStr.isEmpty() ? "" : roomStr + StrUtilX.SPLIT_CODE) +
                checkInDate +
                StrUtilX.SPLIT_CODE +
                checkOutDate;
    }

    /**
     * 创建试预订缓存key
     */
    public static String buildPrebookCacheKey(String supplyCode,
                                              String hotelId,
                                              String spProductId,
                                              List<RoomGuestNumber> roomGuestNumbers,
                                              String checkInDate,
                                              String checkOutDate) {
        String roomStr = roomAppend(roomGuestNumbers);
        return ShubConstant.SHUB_PREBOOK_RESPONSE +
                supplyCode +
                StrUtilX.SPLIT_CODE +
                hotelId +
                StrUtilX.SPLIT_CODE +
                spProductId +
                StrUtilX.SPLIT_CODE +
                (StrUtilX.isEmpty(roomStr) ? "" : roomStr + StrUtilX.SPLIT_CODE) +
                checkInDate +
                (StrUtilX.isEmpty(checkOutDate) ? "" : StrUtilX.SPLIT_CODE + checkOutDate);
    }

    /**
     * 批量保存到redis
     */
    public static void batchSaveToRedis(Map<String, String> redisCacheMap) {
        if (MapUtil.isEmpty(redisCacheMap)) {
            return;
        }
        try (RedisConnection conn = RedisTemplateX.getConnectionFactory()) {
            conn.openPipeline();
            for (Map.Entry<String, String> entry : redisCacheMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
                byte[] valueBytes = value.getBytes(StandardCharsets.UTF_8);
                // 使用SETEX命令设置键值并指定过期时间为3600秒（1小时）
                conn.setEx(keyBytes, 3600, valueBytes);
            }
            // 提交并执行管道中的所有命令
            conn.closePipeline();
        } catch (Exception e) {
            log.error("Redis批量写入失败", e);
        }
    }
}
