package com.tiangong.supply.shub.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 客人信息
 * @author: xinpeng
 * @create: 2023-10-18 11:37
 */
@Data
public class Guest {

    /**
     * 姓
     */
    @NotNull(message = "EMPTY_PARAM_LASTNAME")
    private String lastName;

    /**
     * 名字
     */
    @NotNull(message = "EMPTY_PARAM_FIRSTNAME")
    private String firstName;

    /**
     * 客人全称
     */
    private String guestName;
    /**
     * 会员卡号
     */
    private String membershipCardNumber;

    /**
     * 国籍 NationalityEnum
     */
    private Integer nationality;

    /**
     * 电话
     */
    private String phone;

    /**
     * 区号
     */
    private String countryCode;

    /**
     * 证件类型
     */
    private Integer idCardType;

    /**
     * 证件号码
     */
    private String idCardNo;

    /**
     * 房间序号，多间房多个入住人使用
     */
    private Integer roomIndex;

}