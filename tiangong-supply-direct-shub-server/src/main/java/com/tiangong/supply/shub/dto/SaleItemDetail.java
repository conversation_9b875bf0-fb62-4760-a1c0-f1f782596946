package com.tiangong.supply.shub.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tiangong.supply.direct.remote.dto.CancelPenalties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaleItemDetail {

    /**
     * 售卖日期
     * 格式 yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date saleDate;

    /**
     * 售价 含税价格 不包含到店另付
     */
    private BigDecimal basePrice;

    /**
     * 每日价格明细
     * 主要用于海外展示 售价包含那些东西
     */
    private TaxDetail taxDetails;

    /**
     * 配额数
     */
    private Integer quotaNum;

    /**
     * 配额状态 RoomStateEnum
     */
    private Integer roomState;

    /**
     * 是否即时确认
     */
    private Boolean canImmediatelyConfirm;

    /**
     * 是否可超
     */
    private Boolean overDraft;

    /**
     * 早餐类型
     */
    private String breakfastType;

    /**
     * 含早早餐数
     */
    private Integer breakfastNum;

    /**
     * 独立早餐价格
     */
    private BigDecimal breakfastPrice;




    /**
     * 取消条款类型 CancelRestrictEnum
     */
    private Integer cancelRestrictType;

    private Integer cancelRestrictDays;

    private String cancelRestrictTime;

    private String cancelRestrictRemark;

    /**
     * 取消罚金
     */
    private List<CancelPenalties> cancelPenalties;

    /**
     * 预定条款类型 BookRestrictEnum
     */
    private Integer bookRestrictType;

    private Integer bookRestrictDays;

    private String bookRestrictTime;

    /**
     * 入住条款类型 OccupancyRestrictEnum
     */
    private Integer occupancyRestrictType;

    private Integer occupancyRestrictDays;

    /**
     * 担保条款类型
     *  GuaranteeTypeEnum
     */
    private Integer guaranteeType;

    /**
     * 担保条件
     */
    private String guaranteeCondition;

    /**
     * 担保费用类型
     * GuaranteeFeeTypeEnum
     */
    private Integer guaranteeFeeType;
}
