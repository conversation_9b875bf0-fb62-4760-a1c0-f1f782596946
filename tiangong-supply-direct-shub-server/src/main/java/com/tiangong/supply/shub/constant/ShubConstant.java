package com.tiangong.supply.shub.constant;

import com.tiangong.keys.RedisKey;
import lombok.extern.slf4j.Slf4j;

/**
 * 供应商shub 常量
 */
@Slf4j
public class ShubConstant {

    public static final String SHUB = "SHUB";

    /**
     * 产品信息的shub取首日的 结果缓存 缓存1小时 用来补齐 试预定的取消条款和担保信息 信息 文件夹 key-value string->string 有效时间1小时
     * SUPPLY:SHUB_PRODUCT:
     */
    public static final String SHUB_PRODUCT_RESPONSE = RedisKey.supply_prefix + SHUB + "_PRODUCT:";

    /**
     * 试预定 结果缓存 缓存1小时 用来补齐创建订单的早餐信息 文件夹 key-value string->string 有效时间1小时
     * SUPPLY:SHUB_PREBOOK:
     */
    public static final String SHUB_PREBOOK_RESPONSE = RedisKey.supply_prefix + SHUB + "_PREBOOK:";

    /**
     * sls 日志来源
     */
    public static final String SLS_SOURCE = "tiangong-supply-direct-shub-server";

    /**
     * sls 查询产品日志名称
     */
    public static final String QUERY_PRODUCT_LIST = "queryProductList";

    /**
     * sls 试预订日志名称
     */
    public static final String PRO_BOOKING = "proBooking";

    /**
     * sls 创建订单日志名称
     */
    public static final String CREATE_SUPPLY_ORDER = "createSupplyOrder";

    /**
     * sls 查询订单日志名称
     */
    public static final String QUERY_SUPPLY_ORDER = "querySupplyOrder";

    /**
     * sls 取消订单日志名称
     */
    public static final String CANCEL_SUPPLY_ORDER = "cancelSupplyOrder";

    /**
     * sls 订单状态推送日志名称
     */
    public static final String ORDER_STATUS_PUSH = "orderStatusPush";

    /**
     * sls 自动同步入住明细日志名称
     */
    public static final String AUTO_SYNC_CHECK_INFO = "autoSyncCheckInfo";

    /**
     * sls 接收入住明细日志名称
     */
    public static final String ACCEPT_CHECK_INFO = "acceptCheckInfo";
}
