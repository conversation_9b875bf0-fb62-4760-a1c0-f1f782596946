package com.tiangong.supply.shub.xxljob;

import com.tiangong.supply.shub.service.impl.ShubOrderServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自动同步入住明细任务
 */
@Slf4j
@Component
public class AutoSyncCheckInfoTask {

    @Autowired
    private ShubOrderServiceImpl shubOrderService;

    @XxlJob("autoSyncCheckInfoTask")
    public void autoSyncCheckInfoTask() {
        try {
            XxlJobHelper.log("自动同步入住明细任务开始");
            String param = XxlJobHelper.getJobParam();
            shubOrderService.autoSyncCheckInfoTask(param);
            XxlJobHelper.log("自动同步入住明细任务结束");
        } catch (Exception e) {
            log.error("自动同步入住明细任务异常", e);
            XxlJobHelper.log("自动同步入住明细任务异常", e);
        }
    }
}
