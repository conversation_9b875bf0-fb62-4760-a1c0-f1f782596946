package com.tiangong.supply.shub.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 产品详情
 * @author: xinpeng
 * @create: 2023-10-19 18:07
 */
@Data
public class CommodityDetail {

    /**
     * 售价币种 CurrencyEnum
     */
    private Integer baseCurrency;


    /**
     * 供应商价格计划ID，产品数据不落地方案专用
     */
    private String supplyRateId;

    /**
     * 价格计划名称
     */
    private String pricePlanName;
    /**
     * GuestTypeEnum
     */
    private Integer guestType;

    /**
     * 总价
     */
    private BigDecimal totalAmount;

    /**
     * 均价=总售价/间夜数
     */
    private BigDecimal avgPrice;

    /**
     * 供应商奖励  供应商给我们的
     */
    private BigDecimal supplyReward;

    /**
     * 供应商返佣金 我们给供应商的
     */
    private BigDecimal supplyShouldRackBack;

    /**
     * 支付方式
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 供应商编码
     */
    private String supplyCode;

    /**
     * 供应商名称
     */
    private String supplyName;

    /**
     * 最小预定间数
     */
    private Integer minRoomCount;

    /**
     * 最大预定间数
     */
    private Integer maxRoomCount;

    /**
     * 最大入住人数
     */
    private Integer maxPerson;

    /**
     * 最小入住人数
     */
    private Integer minPerson;

    /**
     * 礼包信息
     */
//    private List<String> giftPacks;

    /**
     * 预定须知
     */
    private List<String> restrictTips;

    /**
     * 房间售卖列表
     * 多间房使用
     */
    private List<RoomItemDetail> roomDetails;

    /**
     * 国内价格计划
     * 每日明细
     */
    private List<SaleItemDetail> priceInfoDetails;

    /**
     * 床型详细信息
     */
    private List<BedTypesDetails> bedTypeDetails;

    /**
     * 产品标签
     * 0或空-无标签
     * 1-协议价格标签
     * 2-平台自签标签
     * 3-团房标签
     */
    private Integer productLabel;

    /**
     * 提示信息
     */
    private List<ShubTipsDTO> tips;

    /**
     * 是否立即确认标识
     */
    private int immediateConfirmFlag = 0;
}