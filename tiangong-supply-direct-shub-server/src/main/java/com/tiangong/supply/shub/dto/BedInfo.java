package com.tiangong.supply.shub.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BedInfo {

    /**
     * 床型名称 多个使用 和 拼接
     */
    private String bedTypeName;

    /**
     * 床型编码 多个使用 , 拼接
     * 可能会存在 一间房多个床型
     */
    private String bedTypeCode;

    /**
     * 床型数量
     */
    private Integer bedNum;

    /**
     * 床宽
     */
    private String bedWidth;

}
