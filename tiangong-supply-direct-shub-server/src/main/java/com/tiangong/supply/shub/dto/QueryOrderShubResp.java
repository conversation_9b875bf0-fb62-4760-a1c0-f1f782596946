package com.tiangong.supply.shub.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 查询订单响应参数
 * @author: xinpeng
 * @create: 2023-10-17 17:22
 */
@Data
public class QueryOrderShubResp {

    /**
     * 商户订单号
     */
    private String merchantOrderCode;

    /**
     * 供应商订单号
     */
    private String supplyOrderCode;

    /** 供应商确认号 */
    private String supplyConfirmNo;

    /** 订单状态 SHub 订单确认结果 */
    private Integer orderStatus;

    /** 入住时间 */
    private Date checkInDate;

    /** 离店时间 */
    private Date checkOutDate;

    /** 订单总金额 */
    private BigDecimal totalAmount;

    /** 币种 */
    private Integer baseCurrency;

    /** 到店另付币种 */
    private Integer payInStoreCurrency;
    /**
     * 到店另付费用
     */
    private BigDecimal payInStorePrice;

    /**
     * 房间数量
     */
    private Integer roomNum;

    /**
     * 酒店id
     */
    private String hotelId;

    /**
     * 房型id
     */
    private String roomId;

    /**
     * 成人数
     */
    private Integer adultNum;

    /**
     * 儿童数
     */
    private Integer childrenNum;

    /**
     * 儿童年龄
     */
    private String childrenAges;

    /**
     * 入住人姓名
     */
    private String guestNames;

    /**
     * 退订费
     */
    private BigDecimal refundPrice;

    /**
     * 退订费币种
     */
    private Integer refundCurrency;

    /**
     * 供应商返佣
     */
    private BigDecimal supplyShouldRackBack;

    /**
     * 供应奖励
     */
    private BigDecimal supplyReward;

    /**
     * 订单明细
     */
    private List<OrderItem> orderItems;

}